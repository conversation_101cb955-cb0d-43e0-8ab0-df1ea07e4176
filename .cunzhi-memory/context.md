# 项目上下文信息

- 完成了检查删除按钮权限功能实现：
1. 创建了CheckDeletePermissionDto类，包含tdsAlias、ledgerModuleId和复杂的权限配置结构（角色、机构、岗位、人员权限）
2. 在IAccountConfigService接口中添加了checkDeletePermission方法
3. 在AccountConfigServiceImpl中实现了权限检查逻辑，包括角色权限、机构权限、岗位权限和人员权限的验证
4. 修改了AccountConfigController中的checkDeletePermission方法，使用新的DTO和Service方法
5. 权限验证逻辑：只要用户满足任一权限类型（角色、机构、岗位、人员），即有删除权限

- 台账采集点相关的文件类，每次修改台账相关的下面的其中一个类，都要考虑修改其它相关的类文件。下面是类说明
1. TDSAccount.java (PC端数据源台账查询接口类文件)

2. DataSourceEditServiceImpl.java (PC端数据源台账保存接口类文件)，具体方法：com.yunhesoft.system.tds.service.impl.DataSourceEditServiceImpl.saveAccountData2MongoDB

3. AccountConfigServiceImpl.java (手动新增数据源台账表格时的接口类文件，具体方法：com.yunhesoft.accountTools.service.impl.AccountConfigServiceImpl.getTagValues)

4. AccountAppServiceImpl.java (移动端台账查询和保存修改相关的类文件)

修改逻辑统一性：所有涉及台账的关键位置都需要查看是否需要修改。
覆盖场景完整性：涉及数据源台账。
班组记事相关的类暂不修改。
