# 常用模式和最佳实践

台账采集点相关的文件类，每次修改台账相关的下面的其中一个类，都要考虑修改其它相关的类文件。下面是类说明
1. TDSAccount.java (PC端数据源台账查询接口类文件)

2. DataSourceEditServiceImpl.java (PC端数据源台账保存接口类文件)，具体方法：com.yunhesoft.system.tds.service.impl.DataSourceEditServiceImpl.saveAccountData2MongoDB

3. AccountConfigServiceImpl.java (手动新增数据源台账表格时的接口类文件，具体方法：com.yunhesoft.accountTools.service.impl.AccountConfigServiceImpl.getTagValues)

4. AccountAppServiceImpl.java (移动端台账查询和保存修改相关的类文件)

修改逻辑统一性：所有涉及台账的关键位置都需要查看是否需要修改。
覆盖场景完整性：涉及数据源台账。
班组记事相关的类暂不修改。
- 在AccountConfigServiceImpl中实现了已有数据的时间默认值更新逻辑：当默认值为2（当前时间）、4（当前日期）、5（当前时间不含日期）时，重新计算当前时间并更新JSON中的txt属性，确保手动新增时获取最新的时间值而不是复用之前保存的值。
- 优化了AccountConfigServiceImpl中的数据库查询逻辑：将原来的3次重复查询合并为1次，创建了统一的calculateDefaultValue方法处理所有默认值计算，扩展了已有数据处理范围从部分默认值到所有默认值，提高了性能并减少了代码重复。
