# 用户偏好设置

台账采集点相关的文件类，每次修改台账相关的下面的其中一个类，都要考虑修改其它相关的类文件。下面是类说明
1. TDSAccount.java (PC端数据源台账查询接口类文件)

2. DataSourceEditServiceImpl.java (PC端数据源台账保存接口类文件)，具体方法：com.yunhesoft.system.tds.service.impl.DataSourceEditServiceImpl.saveAccountData2MongoDB

3. AccountConfigServiceImpl.java (手动新增数据源台账表格时的接口类文件，具体方法：com.yunhesoft.accountTools.service.impl.AccountConfigServiceImpl.getTagValues)

4. AccountAppServiceImpl.java (移动端台账查询和保存修改相关的类文件)

修改逻辑统一性：所有涉及台账的关键位置都需要查看是否需要修改。
覆盖场景完整性：涉及数据源台账。
班组记事相关的类暂不修改。
- 用户偏好：需要生成总结性Markdown文档，不要生成测试脚本，不要编译和运行代码
- 用户偏好：需要生成总结性Markdown文档，不要生成测试脚本，不要编译和运行代码
- 用户偏好：不要生成总结性Markdown文档，不要生成测试脚本，不要编译和运行代码
- 用户希望利用数据源的方式实现功能，返回给前端的数据应该是JSON键值对格式，类似前端表格显示的结构
