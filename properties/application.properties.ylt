# 微服务名
spring.application.name=TM4-PAAS-JOBLIST
#项目信息配置
app.project=TM4-MAIN
app.application=TM4-MAIN
app.description=TM4开发平台
app.module=tm4-app
#tomcat
server.port=8887
server.servlet.context-path=/tm4main

#启用哪些配置文件
#** version,config必须激活
#** 根据项目情况启用 mysql or sqlserver or tm3
#spring.profiles.active=version,config,sqlserver
#spring.profiles.active=version,config,mysql



#** version,config必须激活
spring.profiles.active=version,config,kingbase
#kingbase 人大金仓
spring.datasource.url=***********************************************************************,sys_catalog
spring.datasource.username=ylthg
spring.datasource.password=YHbzhs@*6



#redis
spring.redis.host=127.0.0.1
spring.redis.port=8900
spring.redis.database=0
spring.redis.password=ENC@WUhiemhzISo2





#springboot 去掉自动mongodb驱动，解决启动报错问题，如启用mongodb请注释本条配置
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
#mongodb数据库连接
spring.data.mongodb.host=************
spring.data.mongodb.port=27017
spring.data.mongodb.database=ylt
spring.data.mongodb.auto-index-creation=true
spring.data.mongodb.username=ylt
spring.data.mongodb.password=YHbzhs!*6

#注册中心注册
eureka.client.enabled=true
eureka.instance.prefer-ip-address=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.client.service-url.defaultZone=http://************:8883/eureka

