# 微服务名
spring.application.name=TM4-PAAS-JOBLIST
#项目信息配置
app.project=TM4-MAIN
app.application=TM4-MAIN
app.description=TM4开发平台
app.module=tm4-app
app.outsys.clientSecret=http://************:9081/klmy
#tomcat
server.port=8887
server.servlet.context-path=/tm4main

#启用哪些配置文件
#** version,config必须激活
#** 根据项目情况启用 mysql or sqlserver or tm3
#spring.profiles.active=version,config,sqlserver
#spring.profiles.active=version,config,mysql



#** version,config必须激活
spring.profiles.active=version,config,kingbase
#kingbase 人大金仓
spring.datasource.url=***************************************************************,sys_catalog
spring.datasource.username=tm4dsz
spring.datasource.password=YHbzhs!*6


#mysql数据库连接
#spring.datasource.url=***************************************************************************************************************************************
#spring.datasource.username=root
#spring.datasource.password=bzhs!*6

#spring.datasource.url=***************************************************************************************************************************************
#spring.datasource.username=root
#spring.datasource.password=bzhs!*6
#sqlserver数据库
##马继，国鑫
#spring.datasource.url=********************************************************************************************
#spring.datasource.username=sa
#spring.datasource.password=ENC@YnpocyEqNg==
#
#spring.datasource.url=***********************************************************************************
##spring.datasource.username=sa
#spring.datasource.password=YH_zgb!*2020
#测试部
#spring.datasource.url=**************************************************************************************
#spring.datasource.username=sa
#spring.datasource.password=bzhs!*6

#spring.datasource.url=*******************************************************************************
#spring.datasource.username=sa
#spring.datasource.password=bzhs!*6

#李乔桐
#spring.datasource.url=***************************************************************************************
#spring.datasource.username=sa
#spring.datasource.password=ENC@YnpocyEqNg==

#达梦数据库连接
#spring.datasource.url=jdbc:dm://***************:5236
#spring.datasource.username=TM4WEBD
#spring.datasource.password=YHtm4!*2021


##oracle##
#spring.datasource.url=*****************************************
#spring.datasource.username=TM3_SHNXMYJT_SYS
#spring.datasource.password=ENC@YnpocyEqNg==


#redis
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.database=14
spring.redis.password=ENC@YmR5aA==

#tm3数据库连接
#** 根据项目情况是否启用，依赖配置文件tm3
#spring.datasource.tm3.url=*****************************************************************************************
#spring.datasource.tm3.username=sa
#spring.datasource.tm3.password=ENC@YnpocyEqNg==



#springboot 去掉自动mongodb驱动，解决启动报错问题，如启用mongodb请注释本条配置
#spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
#mongodb数据库连接
spring.data.mongodb.host=************
spring.data.mongodb.port=27017
spring.data.mongodb.database=tlmtm4
spring.data.mongodb.auto-index-creation=true
spring.data.mongodb.username=tlm
spring.data.mongodb.password=ENC@WUhiemhzISo2




#注册中心注册
eureka.client.enabled=true
eureka.instance.prefer-ip-address=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
#eureka.client.service-url.defaultZone=http://************:30552/eureka
eureka.client.service-url.defaultZone=http://************:30552/eureka

#服务降级是否开启
#feign.hystrix.enabled=true