
#开放健康检测监控端口
management.security.enabled=false
management.endpoints.web.exposure.include=*

#集中监控配置信息
spring.application.name=${app.project}
#监控服务端登录信息
spring.security.user.name=admin
spring.security.user.password=admin
#是否启用监控端
spring.boot.admin.client.enabled=false	
spring.boot.admin.client.url=http://localhost:9000/monitor
spring.boot.admin.client.instance.prefer-ip=true
spring.boot.admin.client.username=${spring.security.user.name}
spring.boot.admin.client.password=${spring.security.user.password}
spring.boot.admin.client.instance.metadata.user.name=${spring.security.user.name}
spring.boot.admin.client.instance.metadata.user.password=${spring.security.user.password}
#日志
logging.file.name=logs/log.log
logging.pattern.file=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx
logging.config=classpath:log4j2.properties
logging.level.root=INFO
#${LOG_LEVEL:INFO}