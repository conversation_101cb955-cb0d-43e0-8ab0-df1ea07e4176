# XXL-Job调度中心配置

#是否启用
xxl.job.enable=true
### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
#xxl.job.admin.addresses=http://127.0.0.1:8801/xxl-job-admin
xxl.job.admin.addresses=http://************:8801/xxl-job-admin/

### xxl-job, access token
xxl.job.accessToken=
### xxl-job executor appname
xxl.job.executor.appname=tm4-executor
### xxl-job executor registry-address: default use address to registry , otherwise use ip:port if address is null
xxl.job.executor.address=
### xxl-job executor server-info
xxl.job.executor.ip=
xxl.job.executor.port=9998
### xxl-job executor log-path
xxl.job.executor.logpath=/data/applogs/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=30