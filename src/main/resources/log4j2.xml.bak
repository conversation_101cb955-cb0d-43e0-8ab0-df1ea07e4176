<?xml version="1.0" encoding="UTF-8"?>
<configuration status="INFO" monitorInterval="5">  <!-- 配置日志文件输出目录，此配置将日志输出到tomcat根目录下的指定文件夹 -->
  
    <!--先定义所有的appender-->
    <appenders>    <!-- 优先级从高到低分别是 OFF、FATAL、ERROR、WARN、INFO、DEBUG、ALL -->
        
         <console name="Console" target="SYSTEM_OUT">      <!--输出日志的格式-->
            <PatternLayout pattern="[%d{HH:mm:ss:SSS}] - [%t] [%p] - %logger{1.} - %m%n"/>
            <!--<PatternLayout pattern="[%d{HH:mm:ss:SSS}] - (%F:%l) - %m%n"/>-->
            <!--<PatternLayout pattern="[%d{HH:mm:ss:SSS}] (%F:%L) %m%n" />-->
        </console>
        
       <JDBC name="databaseAppender" tableName="SYS_LOGINFO"  bufferSize="1">
          	<ConnectionFactory class="com.yunhesoft.syslog.utils.LogConnectionFactory" method="getConnection"/>
          	<Column name="ID" pattern="%d{yyyyMMddHHmmssSSS}"/>
            <Column name="LOGLEVEL" pattern="%level"/>
            <Column name="LOGGER" pattern="%logger"/>
            <Column name="LOGMESSAGE" pattern="%message"/>
            <Column name="LOGTIME" pattern="%d{yyyy-MM-dd HH:mm:ss}"/>
      </JDBC>
      
    </appenders>
    <loggers>
       <root level="info">
            <appender-ref ref="Console"/>
            <appender-ref ref="databaseAppender" /> 
        </root>
    </loggers>
</configuration>

<!-- 

<?xml version="1.0" encoding="UTF-8"?>
<configuration status="INFO" monitorInterval="5">
    <appenders>
      <JDBC name="databaseAppender" tableName="LOG_TABLE">
          <ConnectionFactory class="com.yunhesoft.syslog.utils.LogConnectionFactory" method="getConnection"/>
            <Column name="LOGLEVEL" pattern="%level"/>
            <Column name="LOGGER" pattern="%logger"/>
            <Column name="MESSAGE" pattern="%message"/>
            <Column name="LOGTIMESTAMP" pattern="%d{yyyy-MM-dd HH:mm:ss}"/>
      </JDBC>
  </appenders>
  <loggers>
      <logger name="com.xxxx" level="debug">
          <AppenderRef ref="databaseAppender" level="debug" />
      </logger>
  </loggers>
</configuration>

 -->