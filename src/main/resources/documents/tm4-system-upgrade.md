# tm4-system 升级说明
## 0.0.252-saas.397
- redis增加管道模式，用于快速批量插入数据
## 0.0.252-saas.367
- gofast 文件服务验证通过header 传递 token
- 通用dao增加方法，优化in查询方式效率

## 0.0.252-saas.339
- 目标传导公式调用改为微服务模式，增加公式树形加载参数

## 0.0.252-saas.319
- 机构下拉框增加指定显示层级功能，例如只显示到车间

## 0.0.252-saas.316
- word解析函数错误修正

## 0.0.252-saas.302
- 数据源解析速度优化

## 0.0.252-saas.284
- 为奖金提供多种人员查询接口

## 0.0.252-saas.283
- 权限资源树相关优化（独山子提出）
- 
## 0.0.252-saas.258 
-解决首页待办项可能出现重复的问题
## 0.0.252-saas.256 

- 目标传导公式增加微服务模式
- 自定义数据权限增加微服务模式

## 0.0.252-saas.255 

- 解决自定义页面相关bug

## 0.0.252-saas.252

- 解决oracle数据库在不更新任何字段时，更新人和更新时间中间缺少逗号，导致更新失败的问题

## 0.0.252-saas.251

 - 优化REST API方式请求外部微服务接口获取待办数

## 0.0.252-saas.246

 - 去除isModuleRegister注册模块判断，解决在微服务运行环境下的菜单、待办显示问题

## 0.0.252-saas.241

 - blob字段 历史数据转移语句

## 0.0.252-saas.239
- 目标传导通用公式解析增加指标名称、版本号参数 （注意 该版本不含238.test版本的代码（EntityMap的key不区分大小写））

## 0.0.252-saas.235
****
- 辽化system包合并

## 0.0.252-saas.232

- Oracle数据库、人大金昌数据分页问题修改

## 0.0.252-saas.231

- 适配人大金昌数据库

## 0.0.252-saas.230
- 主数据人员同步/机构同步接口增加返回最大版本号功能

## 0.0.252-saas.229
- 机构树形：按照指定机构代码显示本级及父级以上单枝树形

## 0.0.252-saas.227
- 台账lims多数据情况下，表格内容数据改为换行显示，去掉附加s时间信息

## 0.0.252-saas.226
- 台账获取接口数据优化，减少行重新获取时，二次提取次数

## 0.0.252-saas.223
- 台账获取接口数据，数值转化，科学计数法转对应数值

## 0.0.252-saas.221

- log4j升级2.17.1

## 0.0.252-saas.220
- 升级R3Db接口包，台账获取数据增加二次获取逻辑，首次获取数据未获取到或结果为0时，通过取时间点最后数据接口再次提取，提取到的数据将覆盖第一次提取结果，进行相关保存并显示；方案未应用数据不保存

## 0.0.252-saas.217

- 添加工位查询接口

## 0.0.252-saas.216

- tm4引用tm3待办功能

## 0.0.252-saas.213

- 机构工位功能优化调整，增加管理员台账取数及取数异常日志

## 0.0.252-saas.211

- 增加机构工位功能

## 0.0.252-saas.210

- 由于禁止使用clob字段类型，通用dao添加方法分割字符串进行存储

## 0.0.252-saas.206

- 解决数据源导出错误问题 JSON拼写不规范

## 0.0.252-saas.203

- 二级等保，后台接口权限按照管理员与用户区分控制

## 0.0.252-saas.202

- 修复 系统启动报错（机构相关Controller请求路径重复）

## 0.0.252-saas.201

- 修复 fastjson升级到2.0.49版本后SpringMVC使用fastjson消息转换器格式化时间字段失效的问题

## 0.0.252-saas.199

- 安全管理要求，fastjson 升级到 2.0.49

## 0.0.252-saas.198
- 升级目标传导扩展公式修改为异步加载

## 0.0.252-saas.194
- 台账数据源接口获取数据增加判断，过滤错误数据

## 0.0.252-saas.193
- 台账数据源按方案导出excel接口修正

## 0.0.252-saas.192
- 调整word解析压缩比，支持超过10M以上的word解析

## 0.0.252-saas.191
- 台账数据源上下限根据方案显示，表头显示第一个时间点方案，有变化，中间插入上下限新；无方案显示仪表上下限信息

## 0.0.252-saas.189
- 台账数据源获取数据优化调整时间节点，内部进行数据格式化，增加台账取数日志，修改仪表小数位同步更新

## 0.0.252-saas.186
- 台账数据源自动保存数据相关方法
## 0.0.252-saas.186
- 台账数据源自动保存数据相关方法
## 0.0.252-saas.185
- 台账数据源设置仪表问题修正，完善获取MongoDB数据方法

## 0.0.252-saas.184（待发布）

- 广东石化二级等保日志审计相关整改

## 0.0.252-saas.183
- 广东测试发布正式包，附加TM4引用TM3督办功能

## 0.0.252-saas.182.test04
- 台账数据源MongoDB存储结构及取存调整

## 0.0.252-saas.182

- 添加机构时如果redis中没有数据，先进行初始化

## 0.0.252-saas.181

- 解决快捷导航菜单显示不全的问题

## 0.0.252-saas.180

- 解决redis清除后的权限问题及机构列表不全的问题

## 0.0.252-saas.178
- 去除系统加载时的人员缓存加载，加快系统启动速度

## 0.0.252-saas.176
- 台账数据源获取数据，更新为新r3db函数

## 0.0.252-saas.175
- 解决按钮权限不好使的bug

## 0.0.252-saas.174
- 目标传导查表公式通用解析

## 0.0.252-saas.173
- 在线文档保存相关装置车间修正

## 0.0.252-saas.172

- 移动端认证token期限优化
## 0.0.252-saas.171

- 修正多租户模式下数据缓存错误


## 0.0.252-saas.169

- 台账数据源数据结构增加属性，用于记录生成图片标识



## 0.0.252-saas.168

- 登录并发优化，从内存数据库中获取数据
- SaaS模式启动优化，优先创建租户id字段


## 0.0.252-saas.167
- 数据源台账从移动端获取数据调整

## 0.0.252-saas.165
- 在线文档上传同步保存相关装置、车间代码，用于查询调用

## 0.0.252-saas.159
- 增加数据源公式解析controller，调整外部调用word解析相关函数

## 0.0.252-saas.158
- 数据源台账保存无用字段过滤（减少传输量），移动端修改同步台账仪表数据更新接口

## 0.0.252-saas.157

- 新增 用户协议数据查询接口
- 新增移动端token续签数据接口（账号与APPID使用RSA非对称加密）

### 0.0.252-saas.151

- 人员信息增加按说明信息查询人员功能

### 0.0.252-saas.150

- 目标传导主题菜单同步时，将全部菜单都同步到一个文件夹下

### 0.0.252-saas.148

- 数据源台账内部取数方式调整，使用同步仪表的位号，采集点添加时重新进行排序

### 0.0.252-saas.143

- 增加目标传导公式获取通用接口****
- 修改主题菜单同步模式，增加分组菜单
### 0.0.252-saas.138

- 增加机构查询范围接口
  包括
  本部门、本车间、本厂、本班组、本公司、本装置
### 0.0.252-saas.136

- 增加台账仪表版本随核算对象采集点修改进行同步更新功能函数

### 0.0.252-saas.135

- 优化 系统日志请求内容持久化超长截断（最大存储204800个字符=200KB），解决MongoDB排序内存达到上限报错问题

### 0.0.252-saas.134

- 台账数据源确认返回确认信息，前台不刷新做渲染

### 0.0.252-saas.133

- 台账数据源超过1000列时，获取汇总及原因信息时，改为分批获取

### 0.0.252-saas.130

- 台账数据源超限数据填写原因分析功能

### 0.0.252-saas.127

- 增加字段编辑权限设置功能后台

### 0.0.252-saas.126

- 优化 系统日志应答内容持久化超长截断（最大存储204800个字符=200KB），解决MongoDB排序内存达到上限报错问题
- 优化 系统日志白名单增加接口配置数据获取相关接口（不用记录此部分接口请求信息）

### 0.0.252-saas.124

- 台账数据源lims取数接口变更

### 0.0.252-saas.123
- 人员选择框增加机构过滤功能，显示指定机构及其子机构

### 0.0.252-saas.122

- 自定义台账仪表设置分页显示及设置后，隐藏其他表单核算对象对应仪表

### 0.0.252-saas.121

- TM3同步人员数据到TM4账号密码初始化

### 0.0.252-saas.120(不可用)

- TM3同步人员数据到TM4，如果没有角色默认普通角色

### 0.0.252-saas.119

- 台账增加分类层数获取和表头显示，无位号仪表为可编辑列，数据源动态列输出标识使用ID

### 0.0.252-saas.118

- 台账自定义配置功能修正

### 0.0.252-saas.117

- 台账自定义配置功能

### 0.0.252-saas.115

- 自定义报表新增行区列表头锁定功能

### 0.0.252-saas.113

- 优化 系统日志查询超长内容截断，防止前端页面卡死（超出4000字符截断）
- 新增 系统日志导出完整内容文件接口（可导出完整内容文件至本地查看）

### 0.0.252-saas.112

- 数据源台账增加自定义表单配置功能及实现

### 0.0.252-saas.111

- 优化启动时初始化按钮权限速度
- Tm3人员同步时同步党员的政治面貌（仅党员，非党员同步后为空白）（解决BUG）

### 0.0.252-saas.110

- Tm3人员同步时同步党员的政治面貌（仅党员，非党员同步后为空白）

### 0.0.252-saas.109.test1

- 辽兴台账表头增加核算对象配置与使用

### 0.0.252-saas.109

- 数据源列间公式保存支持存储数据库

### 0.0.252-saas.108

- TM3同步人员数据，TM4接收时如果角色为空，不更新人员角色为空，不处理。
- tm4核心包支持Tendis 内存数据库

### 0.0.252-saas.104

- 增加台账数据源确认可按间隔设置功能

### 0.0.252-saas.103

- 修正台账数据源多仪表数据合并显示顺序问题

### 0.0.252-saas.100

- 解决数据源调用Oracle存储过程错误

### 0.0.252-saas.99

- 数据源支持调用Oracle存储过程

### 0.0.252-saas.98

- 调整 数据源多表头列锁定，台账上下限显示位置、lims仪表数据显示

### 0.0.252-saas.97

- 调整 台账单元、设备表头同步、仪表数据获取问题调整

### 0.0.252-saas.96

- 调整 台账数据源多表头、确认显示问题调整

### 0.0.252-saas.95

- 调整 台账数据源动态仪表宽度显示逻辑

### 0.0.252-saas.94

- 解决 台账数据源保存报错问题

### 0.0.252-saas.92

- 解决 redis key值为空值报错问题

### 0.0.252-saas.90

- 解决redis部分函数报错问题

### 0.0.252-saas.87

 - 数据源台账功能调试及修改
 
### 0.0.252-saas.86

 - 添加提供外部待办接口
 
### 0.0.252-saas.85

 - 修复 MongoDB日志查询模糊检索报正则解析错误导致无法返回结果数据的问题
 - 优化 MongoDB日志存储增加应答数据
 - 优化 MongoDB日志存储可自定义功能名称（默认使用Controller接口名称）

### 0.0.252-saas.82

 -  添加数据源台账功能
 
### 0.0.252-saas.79

 -  添加外部数据源设置

### 0.0.252-saas.76

- https 证书加载顺序优化

### 0.0.252-saas.75

- MongoDB支持https
- tools包升级到13版本
-    注意此版本发布后,tools包需升级到13版本

### 0.0.252-saas.71

- redis支持https


### 0.0.252-saas.68

- 数据源公式解析支持自定义函数

### 0.0.252-saas.65
- 包升级，支持word嵌入word、excel附件 com.deepoove:poi-tl:1.9.1 -> 1.10.4

### 0.0.252-saas.64
- 在线文档发布发起行云流传递文档名称及发起人信息

### 0.0.252-saas.63（未发布）

- 系统菜单优化，支持三级目录及排序使用排序字段
- base实体对象添加机构、岗位信息， 修改通用dao ，insert 时插入机构、岗位信息

### 0.0.252-saas.61
- 在线文档审核中或审核后存档文件不允许删除，增加判断和控制

### 0.0.252-saas.58

- TM4框架支持Oracle数据库

### 0.0.252-saas.56
- 在线文档管理人改为多人模式，部分问题修改时

### 0.0.252-saas.55
- 解决 tm3session丢失问题


### 0.0.252-saas.53
- 岗位设置优化，添加岗位系数

### v0.0.252-saas.52
- 增加在线文档管理功能

### v0.0.252-saas.41
- 增加企业注册时，可以录入企业识别码

### v0.0.252-saas.30
- 增加多租户企业管理员注册时，微服务数据的初始化
- http://www.mytm3.com:8181/docs/tm4doc/tenant

### v0.0.252-saas.29
- 1.解决修改人员信息时，登录信息表的TENANT_ID置0的问题
- 2.解决租户管理员，修改个人快捷导航报错的问题

### v0.0.252-saas.28.test08
- 重载根据人员变动查询人员对象信息（增加处理专业信息逻辑，直接取professionalInfoId即可）
### v0.0.252-saas.25
- 修改注册时的默认角色id，默认机构id，默认岗位id
### v0.0.252-saas.24
- 人员信息，专业变多选
- 岗位信息，增加专业多选
### v0.0.252-saas.21
- 增加人员变动功能
### v0.0.252-saas.16
- 1.修改岗位设置，增加岗位负责人；
- 2.增加专业管理
- 3.人员信息增加专业管理下拉选择框
### v0.0.252-saas.11
- 修改了多租户注册以时-加载中小企业评测菜单

### v0.0.258
- 添加人员变动功能

### v0.0.255
- 项目报"xxx响应头缺失“漏洞处理

### v0.0.254
- 数据源导入导出修正

### v0.0.253
- 数据源脚本查询修正

### v0.0.251
- 输入参数同步修正
### v0.0.250
- 配置文件增加AES加密模式 （使用【TM4@】开头）

### v0.0.249
- 开放接口功能重构

### v0.0.247
- 数据源导出优化

### v0.0.246
- 修改验证码样式
### v0.0.241
- 增加白名单维护相关api
### v0.0.235
- 增加数据源是否对外开放
### v0.0.234
- 新增移动端人员接口
### v0.0.232
- 增加获取当前模块编码
### v0.0.231
- 机构类型多选时查询优化
### v0.0.229
- 增加日历功能优化

### v0.0.227
- 增加移动端微信发送卡片消息，调试完毕

### v0.0.224
- 增加日历功能优化

### v0.0.223
- 增加日历功能

### v0.0.222
- 增加移动端微信发送卡片消息

### v0.0.221
- 增加分析图功能优化

### v0.0.220
- 增加分析图功能优化

### v0.0.219（未发布）
- 增加微信定时发消息功能

### v0.0.218
- 通用Dao优化
- word导出合并列名输出修正

### v0.0.217
- 通用Dao支持事务
- word导出支持表单别名带入相关变量解析开发，多图片、文字换行接口优化
### v0.0.216
- 添加了新的获取实时数据的方法
### v0.0.215
- 修复自定义报表预览页面查询条件设置的公式计算失效的问题
### v0.0.214
- 上传文件大小支持在系统参数中设置
### v0.0.213
- 数据源导出优化
### v0.0.212
- 数据源导出优化
### v0.0.211（打包异常弃用）
- 数据源导出优化
### v0.0.210（打包异常弃用）
- 数据源导出优化
### v0.0.209（打包异常弃用）
- 数据源导出优化
### v0.0.208
- 自定义页面问题修复
### v0.0.207
- 数据源公式参数解析
### v0.0.206
- 数据源公式参数解析
### v0.0.205
- 自定义页面优化
### v0.0.204
- word导出问题修正
### v0.0.197
- 人脸库保存功能增加user信息判断

### v0.0.196
- word导出解析自定义数据源公式获取参数结果调整

### v0.0.195
- word导出支持数据源表头、内容合并；支持自定义数据源公式解析

### v0.0.192(未发布)
- 支持多租户模式

### v0.0.191
- 增加角色、权限、数据权限等控制功能
- 增加webservice 类型数据源
- 增加R3DB 数据读取接口
- 增加租户注册功能

### v0.0.190(版本作废-进入人员管理页面报错)

- 增加角色、权限、数据权限等控制功能
- 增加webservice 类型数据源
- 增加R3DB 数据读取接口
- 增加租户注册功能

### v0.0.189(版本作废)
- 版本作废-代码有问题

- 增加角色、权限、数据权限等控制功能
- 增加webservice 类型数据源
- 增加R3DB 数据读取接口
- 增加租户注册功能

### v0.0.188

- 解决PNG透明图片，因为图片压缩，背景变为黑色的问题，现已修改为PNG图片不进行压缩。

### v0.0.187

- 添加系统内置消息功能（依赖于MongoDB）

### v0.0.186
- 优化表单导出EXCEL解析数据源参数问题

### v0.0.182
- 公式解析获取变量优化
### v0.0.185
- 自定义页面 问题修正
### v0.0.181
- 移动端代办优化
### v0.0.180
- 移动端代办优化
### v0.0.178
- 移动端代办相关
### v0.0.176
- 签章功能外部系统同步关联表 字段优化

### v0.0.175
- 签章功能外部系统同步关联表 新增字段

### v0.0.174
- 添加签章功能外部系统同步关联表

### v0.0.173(未发布)
- 添加开发模式下自动修改系统名称及机构名称功能
- 修正登陆超过限定次数判断，改为 ip+登陆用户名做为key值判断

### v0.0.172
- 增加表单移动支持

### v0.0.171
- 增加人员岗位修改操作时清理人员待办缓存的功能

### v0.0.170
-  岗位验重
-  增加移动端菜单初始化方法

### v0.0.169

 - 菜单增加移动端参数
- 增加MongoDB相关操作类
- 强制 admin 账号修改为yhsoft
- 增加日志操作模块
 
### v0.0.168

 - 「自定义报表模块」自定义报表查询页面：解决建立独立系统菜单后无法访问的问题
 
### v0.0.167

 - 增加上传文件虚拟目录
 
### v0.0.166

 - 优化底层删除数据失效问题
 - 机构人员岗位选择框， 增加动态岗位选择，岗位列表（岗位库）选择

### v0.0.164 , v0.0.165 (删除数据底层有问题，不要使用此版本)

 - 增加根据岗位ID读取人员的列表

### v0.0.163

- 优化自定义页面首页展示相关支持

### v0.0.162

- 增加自定义页面首页展示相关支持
- 优化微信账号绑定读取redis业务逻辑

### v0.0.161

- 增加移动端绑定账号后台

### v0.0.160

- 流程表单导出Excel数据优化

### v0.0.159

- 增加表单模板数据导出文件地址

### v0.0.158

- 增加指定机构代码获取其全部管辖子机构的方法
- 添加虚拟目录用于容器化代理

### v0.0.157

- 添加表单设置导出Excel模板，添加流程节点导出表单Excel数据

### v0.0.156

- 优化自定义页面接口

### v0.0.155

- 增加机构查询参数 - 是否根据数据权限查询

### v0.0.154

- 修复通用dao bug


### v0.0.152

- 优化签章数据接口

### v0.0.151

- 人员信息批量生产账号功能优化

### v0.0.150

- 优化通用DAO批量处理数据速度（获取、更新）

### v0.0.149

- 增加签章数据接口

### v0.0.148

- 解决组织机构树形子节点无法显示的问题

### v0.0.147

- TM4批量添加数据说明（添加10000条数据测试）
- 盖章代码实现

### v0.0.146

- 更换消息实现类的JSON引入类

### v0.0.145

- 增加机构签章岗位设置

### v0.0.144

- 增加自定义页面相关支持

### v0.0.143

- 修复通过机构名称检索机构

### v0.0.142

- 添加通过机构名称检索机构

### v0.0.141

- 添加通过机构类型获取父机构列表

### v0.0.140

- 去掉人脸识别第三方人员信息表

### v0.0.137

- 数据源保存部分字段支持base64传输

### v0.0.134

- 基础功能后台优化

### v0.0.133

- 添加第三方人员人脸识别设置后台功能
	
### v0.0.132

- 数据源导出 报错修复

### v0.0.128

- 菜单权限支持按照菜单地址配置权限

### v0.0.127

- 菜单库初始化支持参数 serv.initMenuLib("角色管理", "/system/role/index1","a=1&b=2");

### v0.0.125

- 菜单增加：
	1、移动端、PC、全部选项
	2、菜单支持打开方式：全屏、框架内、等打开方式

### v0.0.124

+ 解决doc图片未打包进jar包的问题

### v0.0.123

+ 增加不拦截version.js文件

+ 增加doc文档的图片目录 /doc-img/**为不拦截
  
  + resources/documents下的.me文件，其中如果包含图片，请放置/resources/documents/doc-img中，且在.me文档中使用 相对路径进行引用。
  
  + 推荐大家使用markText文档编辑器，优点对图片，可复制 粘贴，并将文件自动保存到doc-img目录中。
  
          下载地址：https://github.com/marktext/marktext
  
  + markText文档编辑器设置
    
    ![](doc-img/2022-09-19-10-26-51-image.png)
    
    ![](doc-img/2022-09-19-10-22-23-image.png)

### v0.0.122

- 增加利用各种模式单点登录的功能：
    1.适用于tm4当中没有同步tm3中的帐号
  
      tm3
  
    2.适用tm4中已有登录用户信息
  
      sso-staffno 通过工号读取用户信息
      sso-phone 通过手机号读取用户信息 MOBILE
      sso-email 通过邮箱地址读取用户信息
      sso-qq 通过QQ号读取用户信息
      sso-wechat 通过微信号读取用户信息
      sso-loginname 通过登录名称读取用户信息
      sso-cardno 通过身份证号读取用户信息

- 数据权限优化：获取当前登录人的数据权限，如果不传数据权限编码，默认调用：组织机构数据权限

- 修复树形节点删除会修改兄弟节点删除字段问题

### v0.0.121

- word导出增加对新签名组件的支持 -wangcy
- 增加移动端微信单点登录的支持
- 调用示例：http://www.yunhesoft.net:9969/ex-auth.html?accessToken=wx&type=wx&params=a=1&page=system/config/index

### v0.0.120

- word导出增加对新组件无显示属性的修正
- word导出增加对数据源无结果或出错的判断

### v0.0.119

- 添加 自动发送微信消息功能
- word导出判断模板组件是否显示调整
- word模板生成支持生成前后缀

### v0.0.118

- 添加Redis管理功能
- 修正系统启动菜单库重复添加的问题

### v0.0.117

- 添加获取模块列表接口

### v0.0.116

- 添加在线用户功能

### v0.0.113

- 系统登录安全相关修改
- 1、90天未修改密码，提示修改
- 2、登陆验证密码传输进行加密传输
- 3、录入密码错误5次后锁定30分钟后才能登录

### v0.0.112

- 解决系统参数初始化不成功的问题
- 数据字段包移动位置
- 添加AviatorScript公式解析器 ，详见 http://www.mytm3.com:8181/docs/tm4doc/scripteval

### v0.0.111

- 修正了代办已读数功能objId为空的时候报错的问题

### v0.0.110

- 根据数据源是否有输出参数bdsx来确定表格以横向输出调整表格整体宽度
- 提供根据dataid获取模板id函数（刘涛提供）

### v0.0.109

- 代办已读功能增加删除已读数据

### v0.0.108

- 人员管理中添加兼岗功能

### v0.0.107

- 数据源导出Excel支持渲染函数
- 解决数据查询数据的一个bug

### v0.0.106

- 添加系统模块注册功能
- word生成查询相关表，判断图片保存用表并加入内部查询列表
- 数据源流程设置添加redis
- 数据源、待办模块只启用注册的模块

### v0.0.105

- 修正自定义表单导出word时，当表单没有数据导致的错误

### v0.0.104

- 自定义表单的word模板、数据导出支持图片

### v0.0.103

- 主框架不拦截地址配置，添加同步地址不拦截

### v0.0.102

- 数据源支持调用数据源数据

### v0.0.101

- 添加数据源输入参数联动设置功能

### v0.0.100

- 添加数据权限功能

### v0.0.99

- 自定义表单增加跟工作流相关的函数
- 数据源检索条件支持联动

### v0.0.98

- 数据源 getJsonData 函数优化，改为返回 JSONArray 对象

### v0.0.97

- 大屏调用数据源添加缓存redis功能

### v0.0.96

- 修正fastjson升级包 1.2.83

### v0.0.95

- 因fastJson工具安全漏洞原因，升级到 1.2.83
- 将自定义表单的菜单库生成移到系统框架内

### v0.0.94

- 数据源修改优化添加是否显示字符限制

### v0.0.93

- 数据源接口优化

### v0.0.91

- 解决SQLServer 数据库 ，数据源功能读取表格数据为空时报错问题
- 工具类ObjUtils添加 copy 函数

### v0.0.90

- 针对工作流引擎增加了读取岗位下人员的函数

### v0.0.88

- 增加自定义页面后台配置功能（优化）

### v0.0.87

- 增加自定义页面后台配置功能

### v0.0.86

- 自定义表单增加表单别名字段formAlis

### v0.0.85

- 2022.3.22 数据源公式解析优化，通用预编译脚本解析
- 增加自定义链接组件（目前工具栏使用）后台方法
- 增加自定义页面后台方法

### v0.0.84

- 升级功能明细见：v0.0.83 版本说明

### v0.0.83(此版本因引用包失败，无法正常运行，请升级下一个版本)

- TM4支持xxl-job调度中心
- 优化数据源生成输出参数
- 优化数据源初始化redis
- 优化entityService,增加执行带参的无返回值的函数
  例：
  delete b_zz where zzdm=? and used = ?;
  参数：   ["0010010101",1]

### v0.0.82

- 系统参数新增根据key修改值方法 public Res<?> updateByKey(@RequestBody SysConfig sysConfig)

### v0.0.81

- 优化人员检索条件

### v0.0.80

- 数据源增加前台设置页面，增加树形存储
- 数据源输入参数默认值解析优化，通用预编译脚本解析
- 脚本解析器优化，计算结果缓存redis
- 系统菜单优化，菜单删除由逻辑删除改为真删
- 同步菜单库优化，同步时判断分类和菜单是否重复（通过名称判断），如重复则不再重复添加

### v0.0.79

- 增加自定义页面配置功能

### v0.0.78

- 优化附件上传逻辑：按月份存储附件(解决相对路径，没有按月份存储的问题)

### v0.0.77

- 将富文本组件后台迁移到System包

### v0.0.76

- 数据源支持每次加载执行初始化语句

### v0.0.75

- 表单中，返回前台除了数据ID以外，增加索引表ID

### v0.0.74

- 修改登录认证时，针对弱密码 和 使用默认密码 给出不同提示语

### v0.0.73

- 修改登录认证时，针对默认密码也同等于弱密码，必须校验的功能优化

### v0.0.72

- 修改登录认证时，针对默认密码也同等于弱密码，必须校验的功能

### v0.0.71

- 修改登录认证时，进行弱密码校验的功能由后台检验
- 检验结果前端时，会带上弱密码校验的结果以及登录token

### v0.0.70

- 添加动态执行java函数功能，替换待办、数据源sysinfo函数解析
- 数据源部分功能回复原脚本解析器方法（new TJSEngine）
- 超级管理员密码初始化时初始化为TM4@bzhs!*6
- 添加系统参数，可以配置用户默认密码及是否登陆强制密码校验
- 配置文件中添加密码校验规则

### v0.0.69

- 解决 Where条件中的Column对象使用ifnull函数时在sqlserver数据库下，少括号的问题

### v0.0.68

- 待办功能、数据源功能脚本解析器优化，防止内存溢出
- 系统菜单同步优化

### v0.0.67

- 优化附件上传逻辑：按月份存储附件

### v0.0.66

- 优化根据人员id获取机构信息函数

### v0.0.65

- 添加根据人员id获取机构信息函数

### v0.0.64

- 自定义表单查询列表接口优化，支持按参数查询（按id过滤和控制表单只读）

### v0.0.63

- 增加控制验证码的参数：app.auth.captcha.enabled=false   如果设置为true或者不设置该参数，则启用验证码

### v0.0.62

- 通用Dao支持达梦及Oracle数据库的分页功能
- 通用表单优化-增加指定sheet页显示功能
- 通用Word 导出功能代码优化，支持表单不增加分类时，也可以导出
- 引入日志包，否者系统启动报错：org.springframework.boot:spring-boot-starter-log4j2:2.3.4.RELEASE

### v0.0.61

- 解决了tm3.5同步tm4时候清除redis中的对照关系

### v0.0.60

- 解决了tm3.5同步tm4时候查询岗位根节点问题

### v0.0.59

- 第一次运行系统，系统函数未初始化问题

### v0.0.58

- 支持tm3待办调用tm4待办接口

### v0.0.57

- 升级log4j2.17.0 版本

### v0.0.56

- 解决了TM3.5同步TM4时候角色创建失败问题

### v0.0.55

- 机构服务添加getParentOrgCode函数，获取全部父机构代码

### v0.0.53

- 解决系统中未设置岗位，岗位组件报错问题

### v0.0.52

- 解决tm3将组织机构，人员信息同步到tm4之后，tm3跳转到tm4框架时找不到登录人员的问题

### v0.0.51

- 将tm4-system包中必备的jar包打包到pom中，解决引用tm4-system包时，减少查找各种依赖包或依赖包版本不匹配的问题
- 注:tm4-system增加常包的时候，需要在build.gradle的最下方增加引用

### v0.0.50

- 日期格式化函数bug修改

### v0.0.49

- 待办数bug优化

### v0.0.48

- 优化同步TM4机构数据时机构类型匹配

### v0.0.47

- 权限详细查询接口优化

### v0.0.44

人员、机构选择组件支持分页及模糊检索

### v0.0.40

1、PO继承BaseEntity类时，自动创建createBy与updateBy字段内容从登录账号更改为用户ID（涉及到此处代码的大家一定要修改）

2、增加通用用户操作类，可判断用户是否为超级管理员功能，获取当前用户信息等（需不断完善）
//引入通用用户操作类
import com.yunhesoft.system.kernel.service.SysUserUtil;
//获取当前用户详细信息
SysUser sysUser = SysUserUtil.getCurrentUser();
//判断当前用户是否为[超级管理员]
boolean b = SysUserUtil.isAdmin();
//判断某用户ID是否为[超级管理员]
boolean b = SysUserUtil.isAdmin(String userId)

3、增加系统发消息通用接口，目前只支持调试模式（后台打日志）。未来支持：系统消息、微信、短信等
//引入通用消息操作类
import com.yunhesoft.system.msg.service.IMessageService;
//调用发消息接口，参数说明详见BEAN对象
MsgObjectRet retObj = msgSer.sendMsg(msgList);

### v0.0.39

修复解析日期或日期转换copy时，月份加1月的问题

### v0.0.38

恢复entityService的where的like为两者均like ,例: where.like("col","123") sql: col like "%123%"
为了能支持左右包含的情况，增加了 likeLeft %123  likeRight 123% 两个函数

### v0.0.37

修正 entityService的where.isNull()函数的bug
修正 entityService个别函数读取数据时，由于数据库字段与类字段类型不匹配时造成转换类型不匹配的问题

### v0.0.35

该版本修改了一下底层，导致上一次发布时，有bug，现已修复，但由于底层改动，还请大家尽快测试，有问题请第一时间联系我。
该版本：
1.增加： isNull,notNull,isEmpty,notEmpty,between等一系列常用的方法，并且均支持lambda方式。
2.增加：lb(),rb()代表着左小括号，右小括号，配合and()，or()函数，可以实现任意的where条件的组合
         如: (cd = 3 and (b =2 or c=3)) 使用 Where().eq(e::getCd,3).and().lb().eq(e::getB,2).or().eq(e:getC,3).rb()实现
3.增加: 针对字段的处理增加了两个函数 isNull(col,'default value') substr(col,3,4)
         写法：
        w.eq(Column.create("", SysLoginUser::getId, Column.COL_FUNC.SubStr, 2, 3), 'Isfadfw');
        w.eq(Column.create("", SysLoginUser::getId, Column.COL_FUNC.IfNull, ""));

### v0.0.30

- 人员选择框、岗位组件框后台接口

### v0.0.29

- Excel导入类异常处理修改

### v0.0.28

- ExcelExport.downLoadExcel 改为 public

### v0.0.26

- 添加获取系统自定义参数

### v0.0.22

- 添加系统启动执行初始化语句功能，通常用来修改数据表字段类型，详见说明文档 http://www.mytm3.com:8181/docs/tm4doc/tm4doc-1d0pftgplnrq4
- 添加系统待办后台，并支持启动初始化功能，详见 http://www.mytm3.com:8181/docs/tm4doc/tm4doc-1dbe1otk4urlk
- 程序启动时打印Tomcat版本信息

### v0.0.21

- com.github.xiaoymin:knife4j-spring-boot-starter的版本升级到2.0.9,解决baseUrl重复的问题
- 优化SysUserHolder.getCurrentUser()方法，优先从redis中获取user信息

### v0.0.20

- 降低com.github.xiaoymin:knife4j-spring-boot-starter的版本为2.0.6
- 由于版本降低，请将@EnableSwagger2注解修改为@EnableSwagger2WebMvc
- 增加项目文档在swagger上的展示功能，项目文档位于<b> resources </b>下的<b> documents </b>目录下，项目文档必须是*.md文件,文件名称有意义即可
- 项目文档第一级标题:  # 标题名称作为api文档的名称
- gradle的clientJar命令增加将*.md文件打包进jar包中的配置
- *.md文档当前，暂不支持链接图片，如果要链接图片，请确保图片地址可用。
- 如果实在想用图片，则将图片转为base64图片
- 修改部分模块controller提交方法，只支持post、get,删除put、delete方法，涉及模块：文件上传、系统参数、数据字典、用户注册前台应与之对应升级（2021-09-28）。
