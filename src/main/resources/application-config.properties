
##配置登录页是否使用验证码true:使用false:不使用
app.auth.captcha.enabled=false

#多少分钟无操作自动退出当前操作
app.auth.auto.logoutminute=10
#密码校验规则
app.password.rule=^(?=.*\\d)(?=.*[a-zA-Z])(?=.*[\\~\\!\\@\\#\\$\\%\\^\\&\\*\\(\\)\\[\\]\\{\\}\\<\\>\\?\\\\\\+])[a-zA-Z0-9\\~\\!\\@\\#\\$\\%\\^\\&\\*\\(\\)\\[\\]\\{\\}\\<\\>\\?\\\\\\+]{10,20}$
app.password.msg=密码长度为10~20位，必须包含字母、数字、特殊字符!

#是否强制定期修改密码
app.password.expire.force=false
#密码定期更换天数
app.password.expire.day=180
#允许密码输入错误次数
app.password.err.times=5
#密码输入错误锁定分钟
app.password.err.lockminute=30
#是否启用在线人数统计功能
app.useronline.enable=true

#JPA自动建表
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.datasource.druid.connection-properties=druid.stat.mergeSql=false;druid.stat.slowSqlMillis=500

#druid监控账号密码配置
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initialSize=100
spring.datasource.druid.minIdle=10
spring.datasource.druid.maxActive=300
spring.datasource.druid.maxWait=60000
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
spring.datasource.druid.minEvictableIdleTimeMillis=300000
spring.datasource.druid.maxEvictableIdleTimeMillis=900000
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
spring.datasource.druid.webStatFilter.enabled=true
spring.datasource.druid.statViewServlet.enabled=true
spring.datasource.druid.statViewServlet.allow=
spring.datasource.druid.statViewServlet.url-pattern=/druid/*
spring.datasource.druid.statViewServlet.login-username=lnyhadmin
spring.datasource.druid.statViewServlet.login-password=bzhs!*6
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=1000
spring.datasource.druid.filter.stat.merge-sql=true
spring.datasource.druid.filter.wall.config.multi-statement-allow=true

#api文档相关配置
## 开启增强配置 
knife4j.enable= true
## 开启Basic认证用户功能
knife4j.basic.enable=true
## Basic认证用户名
knife4j.basic.username=yhsoft
## Basic认证密码
knife4j.basic.password=ENC@YnpocyEqNg==
#系统信息配置
#server.port=8887
#spring.profiles.active=version,mysql
#server.servlet.context-path=/tm4main
server.servlet.session.timeout=PT1H
server.max-http-header-size=10240000
server.tomcat.max-http-form-post-size=10240000
#开启压缩传输功能
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain
#默认就是2048 byte
server.compression.min-response-size=2048
#排除登录验证的api地址
server.servlet.auth.excludes=/system/synchronous/**
#允许登录的域名，逗号分隔：例:http://127.0.0.1:8880,* 为全部允许,正式生产环境，请根据实际情况设置正确的域
server.servlet.cros.inludes=*

## 等待队列长度，默认100。
server.tomcat.accept-count=1000
## 最大工作线程数，默认200。（4核8g内存，线程数经验值800，操作系统做线程之间的切换调度是有系统开销的，所以不是越多越好。）
server.tomcat.max-threads=20
## 最小工作空闲线程数，默认10。（适当增大一些，以便应对突然增长的访问量）
server.tomcat.min-spare-threads=10
spring.devtools.restart.enabled=false
#-----tm3第三方代理配置，默认为空，如果为空，以数据库中的配置为准
tm3.server=
#------自定义配置信息.e------
#spring boot 内置 redis配置
#spring.redis.host=127.0.0.1
#spring.redis.port=6379
#spring.redis.database=2
#spring.redis.password=ENC@YmR5aA==
#超时一定要大于0
spring.redis.timeout=3600000
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=0
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-wait=-1


#session共享采用redis
#spring.session.store-type=redis
#spring.session.redis.namespace=Session
#spring.session.redis.flush-mode=immediate

spring.activiti.database-schema-update = true
spring.activiti.db-history-used = true
spring.activiti.check-process-definitions = false
spring.activiti.history-level = full

#spring.mvc.hiddenmethod.filter.enabled=true #支持delete、put
# 开启文件上传
spring.servlet.multipart.enabled=true
# 单个文件上传最大值
spring.servlet.multipart.max-file-size=20MB
# 单个请求最大限制
spring.servlet.multipart.max-request-size=20MB
#
##最大连接数
#http.maxTotal=100
##并发数
#http.defaultMaxPerRoute=20
##创建连接的最长时间
#http.connectTimeout=1000
##从连接池中获取到连接的最长时间
#http.connectionRequestTimeout=500
##数据传输的最长时间
#http.socketTimeout: 10000
##提交请求前测试连接是否可用
#http.staleConnectionCheckEnabled=true
##可用空闲连接过期时间,重用空闲连接时会先检查是否空闲时间超过这个时间，如果超过，释放socket重新建立
#http.validateAfterInactivity=3000000


#移动端 DCloud AppID（多个 ID 使用逗号分隔）
app.tm4.mobile.appid=__UNI__45FF58B


##百度AI人工智能-【人脸\￥��片\￨��音】鉴权服务地址配置
app.aip.baidu.url=https://aip.baidubce.com

##百度AI人工智能-【图片识别】详细参数
app.aip.baidu.ioc.ver=/rest/2.0/solution/v1/
app.aip.baidu.ioc.apikey=NmjaAD3KBpe0BEkpcUlvXjkj
app.aip.baidu.ioc.secretkey=KqA4DvqpEA2pyiMY4Rn5QjnXKpil6191
app.aip.baidu.ioc.token.needable=true
app.aip.baidu.ioc.token.ver=/oauth/2.0/
app.aip.baidu.ioc.token.grantType=client_credentials