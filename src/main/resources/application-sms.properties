#调试模式开关 1：开启（只打日志，不发消息） 0：关闭（系统正常发送消息）
tm4.sms.debug=0
#根据此表示判断使用什么短信发送接口(aliyun：阿里云 webapi：webapi接口（广东石化,需引入sms包）)
tm4.sms.provider=webapi
#短信相关
#短信发送签名 运和软件
tm4.sms.signName=ENC@6L+Q5ZKM6L2v5Lu2
#AccessKey（可自行申请）
tm4.sms.accessKeyId=LTAI5t5afyNmoTfdJZ17KLvZ
#AccessKeySecret（可自行申请）
tm4.sms.accessKeySecret=******************************
#短信API产品域名（接口地址）
tm4.sms.domain=https://56.gdsh.petrochina:9002/webapi/SendMessageByMobie
#tm4.sms.domain=dysmsapi.aliyuncs.com
#tm4.sms.domain=http://192.168.0.24:8887/tm4main/sms/webapi/webapiTest

#短信API产品名称（业务id）
tm4.sms.product=Dysmsapi
#服务器地址编号
tm4.sms.regionId=cn-beijing
#短信模板
#默认模板（该模板只有一个content变量待替换，webapi模式用${}包裹，第三方平台按照平台规则编写变量）
tm4.sms.smsTemplate.defaultTplCode=DEFAULT
#注册用户验证码模板
tm4.sms.smsTemplate.registerVerifyCode=SMS_215100409
#找回密码验证码模板
tm4.sms.smsTemplate.forgotpasswordVerifyCode=TM4@t36EVMBLMkLfKn9Ai6ks-N0XbgWKi2ZE5OUDeOqLwtXdDkgfpAd5gqyTwjbX9jSDgwtLssPtXKHhx5qiAeiiR33WH_XWDRoCTWtKJcuz25YI8a_SIGlOgUsGNyNwnZIXtgRh-5iL9xPQChl8jLpPYcG1j_Z-UdoyEuWyVzagQaQ
#人员信息修改验证码
tm4.sms.smsTemplate.personInformUpdateVerifyCode=SMS_215100407
#企业审核提示短信
tm4.sms.smsTemplate.enterpriseAuditMsg=SMS_218546159
#项目申报审核提示短信
tm4.sms.smsTemplate.projectAuditMsg=SMS_218566345


# webapi模式下自定义短信模板配置（对应tm4.sms.smsTemplate里的模板编码，变量用${}包裹）
tm4.sms.webapi.customTemplate.DEFAULT=${content}
#tm4.sms.webapi.customTemplate.SMS_215100409=您正在注册xxx,验证码为${code}，5分钟有效。

# webapi模式的请求方法（get或者post）
tm4.sms.webapi.method=get
# webapi模式的自定义参数
tm4.sms.webapi.customParams.sysId=ENC@MTA0NTk=
tm4.sms.webapi.customParams.sysPassword=ENC@QnpqamhzeHRAMjAyMw==
# webapi模式的请求参数
# (其中${}为系统内置参数，${phoneNum}代表手机号，${content}代表短信内容, ${signName}代表签名)
# (@{}变量代表tm4.sms.webapi.customParams属性下配置的自定义参数变量)
tm4.sms.webapi.params={"mobiles": "${phoneNum}", "content": "${content}", "sysId": "@{sysId}", "sysPassword": "@{sysPassword}", "signature": "${signName}", "remark": ""}


#缓存中验证码的key
tm4.sms.verifyCodeKey=VERIFYCODE
#缓存中验证码的过期时间（单位：秒）
tm4.sms.verifyCodeExpire=180