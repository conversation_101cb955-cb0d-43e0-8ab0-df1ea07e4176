#调试模式开关 1：开启（只打日志，不发消息） 0：关闭（系统正常发送消息）
tm4.sms.debug=1
#根据此表示判断使用什么短信发送接口
tm4.sms.provider=aliyun
#短信相关
#短信发送签名 运和软件
tm4.sms.signName=ENC@6L+Q5ZKM6L2v5Lu2
#AccessKey（可自行申请）
tm4.sms.accessKeyId=LTAI5t5afyNmoTfdJZ17KLvZ
#AccessKeySecret（可自行申请）
tm4.sms.accessKeySecret=******************************
#短信API产品域名（接口地址）
tm4.sms.domain=dysmsapi.aliyuncs.com
 #dysmsapi.aliyuncs.com
#短信API产品名称（业务id）
tm4.sms.product=Dysmsapi
#服务器地址编号
tm4.sms.regionId=cn-beijing
#短信模板
#注册用户验证码模板
tm4.sms.smsTemplate.registerVerifyCode=SMS_215100409
#找回密码验证码模板
tm4.sms.smsTemplate.forgotpasswordVerifyCode=TM4@t36EVMBLMkLfKn9Ai6ks-N0XbgWKi2ZE5OUDeOqLwtXdDkgfpAd5gqyTwjbX9jSDgwtLssPtXKHhx5qiAeiiR33WH_XWDRoCTWtKJcuz25YI8a_SIGlOgUsGNyNwnZIXtgRh-5iL9xPQChl8jLpPYcG1j_Z-UdoyEuWyVzagQaQ
#人员信息修改验证码
tm4.sms.smsTemplate.personInformUpdateVerifyCode=SMS_215100407
#企业审核提示短信
tm4.sms.smsTemplate.enterpriseAuditMsg=SMS_218546159
#项目申报审核提示短信
tm4.sms.smsTemplate.projectAuditMsg=SMS_218566345

#缓存中验证码的key
tm4.sms.verifyCodeKey=VERIFYCODE
#缓存中验证码的过期时间（单位：秒）
tm4.sms.verifyCodeExpire=180