#服务名
#spring.application.name=tm4-cloud-sys
#服务端口
#server.port=8887
#证书配置文件路径
#app.certificate.propfile=D:\\MyWorking\\tmca\\ca.properties
#app.certificate.propfile=classpath:ca.properties

#nacos用户名密码
#spring.cloud.nacos.username=yhnacos
#spring.cloud.nacos.password=ENC@eWhuYWNvcyo2

#注册中心
#spring.cloud.nacos.discovery.server-addr=https://192.168.0.28:31848
#spring.cloud.nacos.discovery.namespace=611f88d9-b11d-4c2b-8afd-4fab16e89ba2
# 配置中心
#spring.cloud.nacos.config.server-addr=https://192.168.0.28:31848
#spring.cloud.nacos.config.namespace=611f88d9-b11d-4c2b-8afd-4fab16e89ba2

#spring.cloud.nacos.config.file-extension=properties
#spring.cloud.nacos.config.group=DEFAULT_GROUP
#spring.cloud.nacos.config.prefix=application


#spring.cloud.nacos.config.shared-configs[0].data-id=application.properties
#spring.cloud.nacos.config.shared-configs[0].refresh=true

#spring.cloud.nacos.config.shared-configs[1].data-id=application-config.properties
#spring.cloud.nacos.config.shared-configs[1].refresh=true

#spring.cloud.nacos.config.shared-configs[2].data-id=application-sqlserver.properties
#spring.cloud.nacos.config.shared-configs[2].refresh=true


