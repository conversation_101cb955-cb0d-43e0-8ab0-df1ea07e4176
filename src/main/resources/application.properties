# 微服务名
spring.application.name=TM4-PAAS-JOBLIST

#项目信息配置
app.project=TM4-COST
app.application=TM4-COST
app.description=TM4精益核算
app.module=tm4-cost

#单点登录配置信息
app.outsys.authorizeUrl=https://qcznh.gdsh.petrochina:39337/oauth/authorize?client_id=xx&response_type=code&state=dlogin
app.outsys.tokenUrl=https://qcznh.gdsh.petrochina:39337/oauth/token
app.outsys.userUrl=https://qcznh.gdsh.petrochina:39337/users/userinfo
app.outsys.clientId=yhbzjxtst
app.outsys.clientSecret=jhKcd23UYsUWd2*jMsygkLKdfs


#系统环境运行模式 dev：开发；test：测试
app.mode=dev

#是否启用调度自动发送微信消息
app.auto.sendmsg=false

#班组核算url地址
app.bzhs.url=http://**************:81/#

#tomcat
server.port=8887
server.servlet.context-path=/tm4main

#启用哪些配置文件
#** version,config必须激活
#** 根据项目情况启用 mysql or sqlserver or tm3
spring.profiles.active=version,config,kingbase
#spring.profiles.active=version,config,kingbase

#mysql数据库连接
#spring.datasource.url=***************************************************************************************************************************************************************************
#spring.datasource.username=root
#spring.datasource.password=YHbzhs!*6


#人大金仓数据库 dev
#spring.datasource.url=************************************************************,sys_catalog
#spring.datasource.username=yunhe
#spring.datasource.password=bzhs!*6

#kingbase 人大金仓 TEST
spring.datasource.url=***************************************************************,sys_catalog
spring.datasource.username=tm4dsz
spring.datasource.password=YHbzhs!*6

#guangdong
#spring.datasource.url=*********************************************************************,sys_catalog
#spring.datasource.username=gd_bzjygl
#spring.datasource.password=YHbzhs!*6

#kingbase 人大金仓 辽化
#spring.datasource.url=*********************************************************************,sys_catalog
#spring.datasource.username=lh_bzjygl
#spring.datasource.password=YHbzhs!*6

#sqlserver数据库
#spring.datasource.url=*******************************************************************************
#spring.datasource.username=sa
#spring.datasource.password=bzhs!*6

##oracle##
#spring.datasource.url=****************************************
#spring.datasource.username=BZJJHS
#spring.datasource.password=ENC@WUhiemhzISo2

#kingbase 人大金仓
#spring.datasource.url=************************************************************,sys_catalog
#spring.datasource.username=yunhe
#spring.datasource.password=bzhs!*6

#redis
#spring.redis.host=127.0.0.1
#spring.redis.port=6379
#spring.redis.database=
#spring.redis.password=TM4@ttm5CBFMf8n3VvihZvFwdBV0uy55iA2xPsNXGqx3Dx9e6u0IgNEdrhD376PNkGDeJtl7befexPUB_EXXOiaKYj5D_FbeCPoMrrXa_0QpMaBfQZws6qqXpEnPNKrC08OT4XXvxZBj_yaFnWfPm1V0w-PmPBsNy0I8I-TGq9c2fWk
spring.redis.ssl=false
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.database=3
spring.redis.password=ENC@YmR5aA==

#springboot 去掉自动mongodb驱动，解决启动报错问题，如启用mongodb请注释本条配置
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
#mongodb数据库连接
#spring.data.mongodb.ssl=true
#spring.data.mongodb.host=************
#spring.data.mongodb.port=30019
#spring.data.mongodb.database=test
#spring.data.mongodb.username=tm4wrtest
#spring.data.mongodb.password=YHbzhs!*6
#spring.data.mongodb.auto-index-creation=true
spring.data.mongodb.host=127.0.0.1
spring.data.mongodb.port=27017
spring.data.mongodb.database=tm4
#spring.data.mongodb.database=test
#spring.data.mongodb.auto-index-creation=true
#spring.data.mongodb.username=
#spring.data.mongodb.password=

#默认local模式
#local：本地文件系统，minio：文件服务器（minio），其他待扩展
file.upload.mode=local
#公共文件夹名称
file.upload.publicDirName=public
#minio服务地址，key及密钥  桶名
#minio.url=http://************:9000
#minio.accessKey=vMV4PfKFDRQ4wMl0
#minio.secretKey=MCzw9KbrDbFDzPnWBvz80RAS9SrRv8Bk
#minio.bucketName=tm4-qsmzq

#注册中心注册
eureka.client.enabled=true
eureka.instance.instanceId=TM4-PAAS-JOBLIST-1
eureka.instance.prefer-ip-address=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
#eureka.client.service-url.defaultZone=http://************:30552/eureka
eureka.client.service-url.defaultZone=http://localhost:8761/eureka
spring.main.allow-bean-definition-overriding=true
# 表单导出excel格式（default 默认模式 gdsh 广东石化模式）
tmsf.form.excel.export.mode=gdsh


# actuator
management.endpoints.enable-by-default= false
management.endpoints.web.base-path= /act
management.endpoints.web.exposure.include=health,info,env,metrics,httptrace,gateway,loggers
management.endpoints.health.show-details= always
# admin
spring.boot.admin.client.url=http://localhost:9999
spring.boot.admin.client.instance.prefer-ip=true

#TM4访问地址
app.Tm4Url=http://127.0.0.1:8080

#消息中心客户端开关
gmsg.client.enabled=false
#消息中心地址
gmsg.server.url=ws://127.0.0.1:8080/websocket
gmsg.reconnect.interval=5000
gmsg.login.userId=job-list
gmsg.login.userName=测试-job-list

#操作卡导出模式(default 默认模式 gdsh 广东石化签章模式)
opercard.export.mode=

app.Tm3Url=
