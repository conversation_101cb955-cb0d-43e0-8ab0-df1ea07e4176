name=PropertiesConfig
# 定义变量。指定日志文件的位置和文件名称,以便记录多份日志时,直接引用
property.fileName=tm4
property.fileDir=./logs
property.filePath=${fileDir}/${fileName}.log
#------------------------------------------------------#
appenders=console, rolling
# rootLogger, 根记录器，所有记录器的父辈
# ALL < TRACE < DEBUG < INFO < WARN < ERROR < FATAL < OFF
# 指定根日志的级别
rootLogger.level=all
# 控制台输出
rootLogger.appenderRef.stdout.ref=Stdout
#控制台的个性化日志级别
rootLogger.appenderRef.stdout.level=info
# 文件输出
rootLogger.appenderRef.rolling.ref=InfoRollingFile
#文件输出的个性化日志级别
rootLogger.appenderRef.rolling.level=info
#------------------------------------------------------#
#精细调整每个包的日志及级别
logger.activiti.name=org.activiti.engine
logger.activiti.level=all
logger.springframework.name=org.springframework
logger.springframework.level=error
logger.springframework.boot.name=org.springframework.boot
logger.springframework.boot.level=error
logger.apache.name=org.apache
logger.apache.level=error
logger.hibernate.name=org.hibernate
logger.hibernate.level=error
logger.springfox.name=springfox
logger.springfox.level=error
logger.druid.name=com.alibaba.druid
logger.druid.level=error

logger.ibatis.name=org.apache.ibatis
logger.ibatis.level=debug
#------------------------------------------------------#

# console
# 指定输出源的类型与名称
appender.console.type=Console
appender.console.name=Stdout
appender.console.layout.type=PatternLayout
#开发时，打到IDE编辑器控制台上需要使用UTF8编码，否则汉字乱码
appender.console.layout.charset = UTF8
#部署后，打到控制台上的汉字需要使用GBK编码，否则汉字乱码
#appender.console.layout.charset = GBK
# 输出模板
appender.console.layout.pattern = %-d{yyyy-MM-dd HH:mm:ss.SSS} [%t] [%5p] [%l] %m%n
 
# rolling file
appender.rolling.type=RollingFile
appender.rolling.name=InfoRollingFile
appender.rolling.fileName=${filePath}
# 指定当发生Rolling时，文件的转移和重命名规则
appender.rolling.filePattern=${fileDir}/${fileName}_%d{yyyy-MM-dd}_%i.log
appender.rolling.layout.type=PatternLayout
# 输出模板
appender.rolling.layout.pattern = %-d{yyyy-MM-dd HH:mm:ss.SSS} [%t] [%5p] [%l] %m%n
# 指定记录文件的封存策略，该策略主要是完成周期性的日志文件封存工作
appender.rolling.policies.type=Policies
# 基于时间的触发策略
appender.rolling.policies.time.type=TimeBasedTriggeringPolicy
# 当前记录周期为每1h生成一个文件
appender.rolling.policies.time.interval=1
appender.rolling.policies.time.modulate=true
# 基于日志文件体积的触发策略
appender.rolling.policies.size.type=SizeBasedTriggeringPolicy
# 当日志文件体积大于size指定的值时，触发Rolling
appender.rolling.policies.size.size=10M
# 文件封存的覆盖策略
appender.rolling.strategy.type=DefaultRolloverStrategy
# 生成分割（封存）文件的个数
appender.rolling.strategy.max=100