package com.yunhesoft.accountTools.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.accountTools.entity.dto.AbnormalParam;
import com.yunhesoft.accountTools.entity.vo.AbnormalObj;
import com.yunhesoft.accountTools.service.IAccountSchedService;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 台账调度函数
 * 
 * <AUTHOR>
 *
 */
@Api(tags = "台账调度函数")
@RestController
@RequestMapping("/accountSched")
public class AccountSchedController extends BaseRestController {

	@Autowired
	private IAccountSchedService serv; // 服务类

	/**
	 * @category 自动保存台账仪表数据
	 * 
	 * @param param
	 * @return
	 */
	@ApiOperation(value = "自动保存台账仪表数据")
	@RequestMapping(value = "/autoSaveMeterData", method = { RequestMethod.POST })
	public Res<?> autoSaveAccountData(@RequestBody AbnormalParam param) {
		Res<AbnormalObj> res = new Res<AbnormalObj>();
		serv.autoSaveMeterData(param);
		return res.ok();
	}
	
	/**
	 * @category 自动保存台账待办数据
	 * 
	 * @param param
	 * @return
	 */
	@ApiOperation(value = "获取自动保存台账待办数据")
	@RequestMapping(value = "/getAccountTodoData", method = { RequestMethod.POST })
	public Res<?> accountTodoData(@RequestBody AbnormalParam param) {
		Res<AbnormalObj> res = new Res<AbnormalObj>();
//		serv.autoSaveMeterData(param);
		return res.ok();
	}
	
}
