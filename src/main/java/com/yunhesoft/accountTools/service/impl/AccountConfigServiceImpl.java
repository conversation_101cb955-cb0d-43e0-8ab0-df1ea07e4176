package com.yunhesoft.accountTools.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.accountTools.entity.dto.*;
import com.yunhesoft.accountTools.entity.po.DigitalLedger;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerExtendRow;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerModule;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerTime;
import com.yunhesoft.accountTools.entity.vo.ComboVo;
import com.yunhesoft.accountTools.entity.vo.LedgerTimeDataVo;
import com.yunhesoft.accountTools.entity.vo.PotVo;
import com.yunhesoft.accountTools.service.IAccountConfigService;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.paramDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampleclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostitemVo;
import com.yunhesoft.leanCosting.unitConf.service.*;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.tds.entity.dto.TdsQueryDto;
import com.yunhesoft.system.tds.entity.po.TdataSource;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.entity.po.TdsoutPara;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tds.service.IRtdbService;
import com.yunhesoft.system.tools.formulaParam.entity.dto.TdsFormulaQueryDto;
import com.yunhesoft.system.tools.formulaParam.entity.vo.TdsFormulaTreeVo;
import com.yunhesoft.system.tools.formulaParam.service.ITdsFormulaService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 台账设置相关服务接口
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Log4j2
@Service
public class AccountConfigServiceImpl implements IAccountConfigService {

    @Autowired
    private EntityService entityService;

    @Autowired
    private ICostService costService;

    @Autowired
    private IUnitMethodService unitMethodService;

    @Autowired
    private IUnitInterfaceService interfaceService;

    @Autowired
    private IImportExcelService importExcelService;

    @Autowired
    private UnitConfService unitConfService;

    @Autowired
    private IRtdbService rtdbService;

    @Autowired
    private IDataSourceService tdsServ; // 数据源相关服务

    @Autowired
    private ITdsFormulaService tdsFormulaServ; // 数据源公式相关服务

    @Autowired
    private ISysEmployeeInfoService employeeInfoService; // 人员信息服务

    @Autowired
    private ISysOrgService orgService; // 机构信息服务

    private static final Map<Integer, String> CONTROL_TYPE_MAP = new HashMap<>();
    static {
        CONTROL_TYPE_MAP.put(0, "textfield");
        CONTROL_TYPE_MAP.put(1, "numberfield");
        CONTROL_TYPE_MAP.put(2, "datetimefield");
        CONTROL_TYPE_MAP.put(3, "checkfield");
        CONTROL_TYPE_MAP.put(4, "combo");
        CONTROL_TYPE_MAP.put(8, "userfield");
        CONTROL_TYPE_MAP.put(9, "usersfield");
        CONTROL_TYPE_MAP.put(10, "datefield");
        CONTROL_TYPE_MAP.put(11, "timefield");
        CONTROL_TYPE_MAP.put(12, "uploadImg");
        CONTROL_TYPE_MAP.put(13, "orgfield");
        CONTROL_TYPE_MAP.put(14, "not_have");
        CONTROL_TYPE_MAP.put(15, "comboboxMulti");
    }

    private static final String accountULInfoCol = "_accountULInfoCol";
    // -------------------------------- 表单台账设置 ↓ ----------------------------------

    /**
     * 检查删除按钮权限
     * @param checkDeletePermissionDto
     * @return
     */
    @Override
    public boolean checkDeletePermission(CheckDeletePermissionDto checkDeletePermissionDto) {
        // 获取当前用户信息
        com.yunhesoft.core.common.model.SysUser currentUser = SysUserHolder.getCurrentUser();
        if (currentUser == null) {
            return false;
        }

        CheckDeletePermissionDto.Permission permission = null;

        // APP端：从台账模型表获取权限配置
        if("app".equals(checkDeletePermissionDto.getType())){
            Where where = Where.create();
            where.eq(DigitalLedger::getTmused, 1);
            where.eq(DigitalLedger::getFormId, checkDeletePermissionDto.getFormId());
            where.eq(DigitalLedger::getLedgerModuleId, checkDeletePermissionDto.getLedgerModuleId());
            Order order = Order.create().orderByAsc(DigitalLedger::getTmsort);
            List<DigitalLedger> digitalLedgers = entityService.queryData(DigitalLedger.class, where, order, null);

            // 获取台账模型的权限配置
            if (StringUtils.isNotEmpty(digitalLedgers)) {
                for (DigitalLedger digitalLedger : digitalLedgers) {
                    String permissionStr = digitalLedger.getPermission();
                    if (StringUtils.isEmpty(permissionStr)) {
                        // 如果权限配置为空，说明没有权限限制，返回true
                        return true;
                    } else {
                        // 解析权限配置JSON
                        permission = parsePermissionFromJson(permissionStr);
                        if (permission != null) {
                            break; // 找到第一个有权限配置的台账即可
                        }
                    }
                }

                // 如果所有台账都没有有效的权限配置，返回true
                if (permission == null) {
                    return true;
                }
            } else {
                // 如果没有找到台账记录，返回false
                return false;
            }
        } else {
            // PC端：直接从DTO获取权限配置
            permission = checkDeletePermissionDto.getPermission();
        }

        // 如果权限配置为空，PC端返回false，APP端在上面已经处理
        if (permission == null) {
            return false;
        }

        // 执行权限检查
        return checkUserPermission(currentUser, permission);
    }

    /**
     * 从JSON字符串解析权限配置
     * @param permissionStr JSON格式的权限配置字符串
     * @return 解析后的权限配置对象，解析失败返回null
     */
    private CheckDeletePermissionDto.Permission parsePermissionFromJson(String permissionStr) {
        try {
            if (StringUtils.isEmpty(permissionStr)) {
                return null;
            }
            JSONObject jsonObject = JSONObject.parseObject(permissionStr);
            return jsonObject.toJavaObject(CheckDeletePermissionDto.Permission.class);
        } catch (Exception e) {
            log.error("解析权限配置JSON失败: {}, error: {}", permissionStr, e.getMessage());
            return null;
        }
    }

    /**
     * 执行用户权限检查
     * @param currentUser 当前用户
     * @param permission 权限配置
     * @return 是否有权限
     */
    private boolean checkUserPermission(com.yunhesoft.core.common.model.SysUser currentUser, CheckDeletePermissionDto.Permission permission) {
        // 检查角色权限
        if (hasRolePermission(currentUser, permission.getRole())) {
            return true;
        }

        // 检查机构权限
        if (hasOrgPermission(currentUser, permission.getOrg())) {
            return true;
        }

        // 检查岗位权限
        if (hasPostPermission(currentUser, permission.getPost())) {
            return true;
        }

        // 检查人员权限
        if (hasStaffPermission(currentUser, permission.getStaff())) {
            return true;
        }

        return false;
    }

    /**
     * 检查角色权限
     */
    private boolean hasRolePermission(com.yunhesoft.core.common.model.SysUser currentUser, CheckDeletePermissionDto.PermissionGroup roleGroup) {
        if (roleGroup == null || roleGroup.getPermissonList() == null) {
            return false;
        }

        List<String> userRoles = currentUser.getRoles();
        if (StringUtils.isEmpty(userRoles)) {
            return false;
        }

        for (CheckDeletePermissionDto.PermissionItem item : roleGroup.getPermissonList()) {
            if ("role".equals(item.getPermType()) && userRoles.contains(item.getPermId())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查机构权限
     */
    private boolean hasOrgPermission(com.yunhesoft.core.common.model.SysUser currentUser, CheckDeletePermissionDto.PermissionGroup orgGroup) {
        if (orgGroup == null || orgGroup.getPermissonList() == null) {
            return false;
        }

        String userOrgId = currentUser.getOrgId();
        if (StringUtils.isEmpty(userOrgId)) {
            return false;
        }

        for (CheckDeletePermissionDto.PermissionItem item : orgGroup.getPermissonList()) {
            if ("org".equals(item.getPermType()) && userOrgId.equals(item.getPermId())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查岗位权限
     */
    private boolean hasPostPermission(com.yunhesoft.core.common.model.SysUser currentUser, CheckDeletePermissionDto.PermissionGroup postGroup) {
        if (postGroup == null || postGroup.getPermissonList() == null) {
            return false;
        }

        String userPostId = currentUser.getPostId();
        if (StringUtils.isEmpty(userPostId)) {
            return false;
        }

        for (CheckDeletePermissionDto.PermissionItem item : postGroup.getPermissonList()) {
            if ("post".equals(item.getPermType()) && userPostId.equals(item.getPermId())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查人员权限
     */
    private boolean hasStaffPermission(com.yunhesoft.core.common.model.SysUser currentUser, CheckDeletePermissionDto.PermissionGroup staffGroup) {
        if (staffGroup == null || staffGroup.getPermissonList() == null) {
            return false;
        }

        String userId = currentUser.getId();
        if (StringUtils.isEmpty(userId)) {
            return false;
        }

        for (CheckDeletePermissionDto.PermissionItem item : staffGroup.getPermissonList()) {
            if ("staff".equals(item.getPermType()) && userId.equals(item.getPermId())) {
                return true;
            }
        }
        return false;
    }

    private Boolean deleteBatchProcess(List<DigitalLedgerTime> deleteList) {
        List<DigitalLedgerTime> dellist = new ArrayList<>();
        for (DigitalLedgerTime del : deleteList) {
            del.setTmused(0);
            dellist.add(del);
        }
        int msg = 0;
        if (StringUtils.isNotEmpty(dellist)) {
            msg = entityService.updateByIdBatch(dellist);
        }
        return msg > 0;
    }

    public static Map<String, String> parseInParams(String inParams) {
        Map<String, String> paramsMap = new HashMap<>();

        if (StringUtils.isEmpty(inParams)) {
            return paramsMap;
        }

        try {
            // 使用正则表达式分割字符串
            String[] pairs = inParams.split("\\|");

            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();
                    paramsMap.put(key, value);
                }
            }

            return paramsMap;
        } catch (Exception e) {
            return paramsMap; // 发生异常时返回空Map
        }
    }

    /**
     * 获取台账时间数据
     *
     * @param param 查询参数
     * @return 时间数据列表
     */
//    @Override
//    public JSONArray queryLedgerTimeData(TdsQueryDto param) {
//        JSONArray resultArray = new JSONArray();
//        if (param == null || StringUtils.isEmpty(param.getInParaAlias())) {
//            log.warn("查询参数为空");
//            return resultArray;
//        }
//
//        Map<String, String> stringStringMap = parseInParams(param.getInParaAlias());
//
//        String ledgerModuleId = stringStringMap.get("ledgerModuleId");
//        String bcParam = stringStringMap.get("bc");
//        List<DigitalLedgerTime> digitalLedgerTime = new ArrayList<>();
//        // 获取上下班时间
//        String startTime = "";
//        String endTime = "";
//        if (StringUtils.isNotEmpty(bcParam)) {
//            try {
//                String[] bcs = bcParam.split(",");
//                if (bcs.length >= 2) {
//                    startTime = bcs[bcs.length - 2];
//                    endTime = bcs[bcs.length - 1];
//                }
//            } catch (Exception e) {
//                log.error("解析班次时间出错: {}", e.getMessage(), e);
//            }
//        }
//
//        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
//            log.warn("班次开始时间或结束时间为空");
//            return resultArray;
//        }
//        digitalLedgerTime = this.getInitTimeList(stringStringMap);
//        // 获取台账模型
//        DigitalLedgerModule obj = entityService.queryObjectById(DigitalLedgerModule.class, ledgerModuleId);
//        if (obj == null) {
//            log.warn("查询台账时间批次模式初始化信息失败：未找到台账模型, ledgerModuleId={}", ledgerModuleId);
//            return resultArray;
//        }
//
//        // 查询采集点信息，用于返回结构
//        List<Costunitsampledot> dotList = new ArrayList<>();
//        if (StringUtils.isNotNull(obj.getAccountObjId())) {
//            Where wheredot = Where.create();
//            wheredot.eq(Costunitsampledot::getTmused, 1);
//            wheredot.eq(Costunitsampledot::getBegintime, "2020-01-01");
//            wheredot.eq(Costunitsampledot::getUnitid, obj.getAccountObjId());
//            Order orderdot = Order.create();
//            orderdot.orderByAsc(Costunitsampledot::getTmsort);
//            dotList = entityService.queryList(Costunitsampledot.class, wheredot, orderdot);
//        }
//
//        Map<String, List<String>> tagNumberMap = new HashMap<>();
//        for (Costunitsampledot dot : dotList) {
//            List<String> dotIds = tagNumberMap.computeIfAbsent(dot.getTagnumber().toUpperCase(),
//                    k -> new ArrayList<>());
//            dotIds.add(dot.getId());
//        }
//
//        // 获取采集点仪表位号
//        List<String> tagNumbers = dotList.stream()
//                .map(Costunitsampledot::getTagnumber)
//                .distinct()
//                .collect(Collectors.toList());
//
//        // 查询rtdb数据 接口这个间隔timeInterval是秒
//        List<Tag> dataResult = rtdbService.queryRtdbTagData(tagNumbers, startTime, endTime,
//                0);
//
//        // 1. 组织rtdb数据为 Map<String tagCode, Map<String datetime, String value>>
//        Map<String, Map<String, String>> tagDataMap = new HashMap<>();
//        if (StringUtils.isNotEmpty(dataResult)) {
//            for (Tag tag : dataResult) {
//                if (tag == null)
//                    continue;
//                String tagCode = tag.getTagCode();
//                List<TagData> datas = tag.getDatas();
//                if (StringUtils.isEmpty(datas))
//                    continue;
//                Map<String, String> timeValueMap = tagDataMap.computeIfAbsent(tagCode.toUpperCase(),
//                        k -> new HashMap<>());
//                for (TagData each : datas) {
//                    String datetime = each.getDatetime();
//                    if (datetime != null && datetime.length() > 19) {
//                        datetime = datetime.substring(0, 19);
//                    }
//                    String value = each.getValue() == null ? null : each.getValue().toString();
//                    if (Coms.judgeDouble(value)) {
//                        value = new BigDecimal(value).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros()
//                                .toPlainString();
//                    }
//                    timeValueMap.put(datetime, value);
//                }
//            }
//        }
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        // 2. 遍历digitalLedgerTime，按StartTime查找每个采集点的实时值
//        for (DigitalLedgerTime timeBatch : digitalLedgerTime) {
//            JSONObject row = new JSONObject();
//            String timeKey = sdf.format(timeBatch.getStartTime());
//            row.put("timeMarkCol", timeKey.substring(0, 16));
//            // 遍历采集点
//            for (Costunitsampledot dot : dotList) {
//                String tagCode = dot.getTagnumber().toUpperCase();
//                String dotId = dot.getId();
//                String value = null;
//                // 精确匹配时间点
//                Map<String, String> timeValueMap = tagDataMap.get(tagCode);
//                if (timeValueMap != null) {
//                    value = timeValueMap.get(timeKey);
//                    // 若无精确值，可做最近时间点匹配（可选，需补充逻辑）
//                }
//                row.put(dotId, value);
//                row.put("ID", timeKey);
//                row.put("_editMark", "1");
//            }
//            resultArray.add(row);
//        }
//        return resultArray;
//    }

    @Override
    public List<DigitalLedgerTime> getInitTimeList(Map<String, String> stringStringMap) {
        if (StringUtils.isEmpty(stringStringMap)) {
            log.warn("解析参数为空");
            return Collections.emptyList();
        }
        String ledgerModuleId = stringStringMap.get("ledgerModuleId");
        String bcParam = stringStringMap.get("bc");
        if (StringUtils.isEmpty(ledgerModuleId) && StringUtils.isEmpty(bcParam)) {
            log.warn("台账模型ID和班次不能同时为空");
            return Collections.emptyList();
        }

        // 获取上下班时间
        String startTime = "";
        String endTime = "";
        if (StringUtils.isNotEmpty(bcParam)) {
            try {
                String[] bcs = bcParam.split(",");
                if (bcs.length >= 2) {
                    startTime = bcs[bcs.length - 2];
                    endTime = bcs[bcs.length - 1];
                }
            } catch (Exception e) {
                log.error("解析班次时间出错: {}", e.getMessage(), e);
            }
        }

        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            log.warn("班次开始时间或结束时间为空");
            return Collections.emptyList();
        }

        List<DigitalLedgerTime> digitalLedgerTime = new ArrayList<>();
        if (StringUtils.isNotEmpty(ledgerModuleId)) {
            digitalLedgerTime = this.getDigitalLedgerTimes(ledgerModuleId, "");
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (StringUtils.isEmpty(digitalLedgerTime)) {
            // 生成从startTime到endTime的时间批次，间隔按照startTime到endTime的时间间隔,默认1小时
            digitalLedgerTime = this.generateTimeBatches(ledgerModuleId, startTime, endTime);
        } else if (!digitalLedgerTime.isEmpty()) {
            // 当班时间类型
            if ("1".equals(digitalLedgerTime.get(0).getStartTimeType())) {
                // 生成从startTime到endTime的时间批次，间隔按照时间间隔设置
                Integer timeInterval = digitalLedgerTime.get(0).getTimeInterval();
                String timeIntervalType = digitalLedgerTime.get(0).getTimeIntervalType();

                if (timeInterval == null || timeInterval <= 0) {
                    timeInterval = 1;
                }

                if (StringUtils.isEmpty(timeIntervalType)) {
                    timeIntervalType = "hour";
                }

                digitalLedgerTime = this.generateTimeBatches(ledgerModuleId, startTime, endTime, timeInterval,
                        timeIntervalType);
            } else {
                // 自定义时间类型，取数据库时间
                try {
                    Date startDate = digitalLedgerTime.get(0).getStartTime();
                    Date endDate = digitalLedgerTime.get(0).getEndTime();

                    if (startDate != null && endDate != null) {
                        String startTimeStr = sdf.format(startDate);
                        String endTimeStr = sdf.format(endDate);

                        Integer timeInterval = digitalLedgerTime.get(0).getTimeInterval();
                        String timeIntervalType = digitalLedgerTime.get(0).getTimeIntervalType();

                        if (timeInterval == null || timeInterval <= 0) {
                            timeInterval = 1;
                        }

                        if (StringUtils.isEmpty(timeIntervalType)) {
                            timeIntervalType = "hour";
                        }

                        digitalLedgerTime = this.generateTimeBatches(ledgerModuleId, startTimeStr, endTimeStr,
                                timeInterval, timeIntervalType);
                    }
                } catch (Exception e) {
                    log.error("处理自定义时间类型出错: {}", e.getMessage(), e);
                    digitalLedgerTime = this.generateTimeBatches(ledgerModuleId, startTime, endTime);
                }
            }
        }
        return digitalLedgerTime;
    }

    public Map<String, Map<String, String>> getTagValues(RtdbDataDto param) {
        Map<String, Map<String, String>> tagDataMap = new HashMap<>();
        String startTime = param.getStartTime();
        List<String> tagNumberList = param.getTagIdList();
        if (param == null || StringUtils.isEmpty(tagNumberList) || StringUtils.isEmpty(startTime)) {
            log.warn("查询实时数据库参数为空param={}", param);
            return tagDataMap;
        }
        List<String> filteredList = tagNumberList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (StringUtils.isEmpty(filteredList)) {
            log.warn("查询实时数据库采集点ID参数为空 param={}", param);
            return tagDataMap;
        }

        String tdsInParaAlias = param.getTdsInParaAlias();
        Map<String, String> stringStringMap = parseInParams(tdsInParaAlias);
        String ledgerModuleId = stringStringMap.get("ledgerModuleId");
        String ledgerComponentId = stringStringMap.get("ledgerComponentId");
        String ledgerInitType = stringStringMap.get("ledgerInitType");

        // 根据初始化类型获取实时值或扩展行值
        // boolean hasRealTimeData = false;
        if (StringUtils.isNotEmpty(ledgerInitType) && "time".equals(ledgerInitType)
                && StringUtils.isNotEmpty(ledgerModuleId) && StringUtils.isNotEmpty(ledgerComponentId) && StringUtils.isNotEmpty(ledgerModuleId)) {
            try {
                String sql = "select START_TIME_TYPE,START_TIME,END_TIME_TYPE,END_TIME,TIME_INTERVAL,TIME_INTERVAL_TYPE from DIGITAL_LEDGER_TIME where TMUSED=1 "
                        + "and LEDGER_ID in (select LEDGER_MODULE_ID from DIGITAL_LEDGER where TMUSED=1 and INIT_TYPE='"
                        + ledgerInitType + "' and COMPONENT_ID='" + ledgerComponentId + "' and LEDGER_MODULE_ID='" + ledgerModuleId + "' )";
                List<Map<String, Object>> _list = entityService.queryListMap(sql);
                if (StringUtils.isNotEmpty(_list)) {
                    tagDataMap = this.initLedgerTimeDatas(filteredList, tagNumberList, tagDataMap, startTime);
                    // hasRealTimeData = true;
                }
            } catch (Exception e) {
                log.error("查询时间初始化设置信息失败: {}", e.getMessage(), e);
            }
        } else if (StringUtils.isNotEmpty(ledgerInitType) && "expand".equals(ledgerInitType)
                && StringUtils.isNotEmpty(ledgerModuleId) && StringUtils.isNotEmpty(ledgerComponentId)) {
            try {
                String sql = "select COLLECTION_POINT_ID,DATA_SOURCE_ALIAS,DATA_SOURCE_FIELD,DATA_SOURCE_PARA_ALIAS,DATA_SOURCE_PARA_NAME from DIGITAL_LEDGER_EXTEND_ROW where TMUSED=1 "
                        + "and LEDGER_ID in (select LEDGER_MODULE_ID from DIGITAL_LEDGER where TMUSED=1 and INIT_TYPE='"
                        + ledgerInitType + "' and COMPONENT_ID='" + ledgerComponentId + "' and LEDGER_MODULE_ID='" + ledgerModuleId + "' )";
                List<Map<String, Object>> _list = entityService.queryListMap(sql);
                if (StringUtils.isNotEmpty(_list)) {
                    tagDataMap = this.initLedgerExcendRowData(_list, tdsInParaAlias, filteredList, startTime);
                    // hasRealTimeData = true;
                }
            } catch (Exception e) {
                log.error("查询扩展行初始化设置信息失败: {}", e.getMessage(), e);
            }
        }

        // 优化：一次性获取所有采集点信息，避免后续重复查询
        Map<String, String> defaultValues = new HashMap<>();
        Map<String, String> dotControlTypeMap = new HashMap<>();
        Map<String, String> dotDisplayModeMap = new HashMap<>();
        Map<String, String> dotDefaultValsMap = new HashMap<>();
        Map<Object, LinkedHashMap<String, Object>> dotMap = new HashMap<>();

        try {
            String sql = "select id, defaultval, controltype, devicedefaultval, defaultvals, multiselectdisplaymode " +
                    "from COSTUNITSAMPLEDOT " +
                    "where tmused=1 and unitid= '" + ledgerModuleId + "'";
            List<LinkedHashMap<String, Object>> result = entityService.query(sql, null);
            dotMap = Optional.ofNullable(result).orElse(new ArrayList<>())
                    .stream().collect(Collectors.toMap(item -> item.get("id"), Function.identity()));

            // 同时构建控件类型等信息的Map，避免后续重复查询
            for (Object id : dotMap.keySet()) {
                LinkedHashMap<String, Object> dotInfo = dotMap.get(id);
                String idStr = String.valueOf(id);

                // 处理控件类型信息
                Object controlTypeObj = dotInfo.get("controltype");
                if (controlTypeObj != null) {
                    try {
                        int controlTypeInt = Integer.parseInt(String.valueOf(controlTypeObj));
                        dotControlTypeMap.put(idStr, getComboStr(controlTypeInt));
                    } catch (NumberFormatException e) {
                        log.warn("解析控件类型失败: {}", controlTypeObj);
                    }
                }

                // 处理显示模式和默认值数组信息
                Object displayModeObj = dotInfo.get("multiselectdisplaymode");
                if (displayModeObj != null) {
                    dotDisplayModeMap.put(idStr, displayModeObj.toString());
                }
                Object defaultValsObj = dotInfo.get("defaultvals");
                if (defaultValsObj != null) {
                    dotDefaultValsMap.put(idStr, defaultValsObj.toString());
                }
            }

            for (String id : filteredList) {
                if (dotMap.containsKey(id)) {
                    // 检查是否有默认值配置
                    Object defaultValObj = dotMap.get(id).get("defaultval");
                    if (defaultValObj == null || "0".equals(defaultValObj.toString()) || StringUtils.isEmpty(defaultValObj.toString())) {
                        continue; // 跳过没有默认值的采集点
                    }

                    String defaultVal = null;
                    switch (String.valueOf(defaultValObj)) {
                        case "1":
                            String controlType = "textfield"; // 默认值
                            if (dotMap.containsKey(id) && dotMap.get(id) != null
                                    && dotMap.get(id).get("controltype") != null) {
                                String controlTypeStr = String.valueOf(dotMap.get(id).get("controltype"));
                                controlType = getComboStr(Integer.parseInt(controlTypeStr));
                            }
                            if ("userfield".equals(controlType) || "usersfield".equals(controlType)) {
                                defaultVal = SysUserHolder.getCurrentUser().getId();
                            } else if ("orgfield".equals(controlType)) {
                                //外委人员
                                String userId = SysUserHolder.getCurrentUser().getId();
                                List<EmployeeVo> userPartTimePost = employeeInfoService.getUserPartTimePost(userId);
                                String orgId = SysUserHolder.getCurrentUser().getOrgId();
                                if (StringUtils.isNotEmpty(userPartTimePost)) {
                                    EmployeeVo partTimePost = userPartTimePost.stream().filter(item -> item.getOrgcode().equals(orgId)).findFirst().orElse(null);
                                    if (partTimePost != null) {
                                        defaultVal = partTimePost.getOrgcode();
                                    }else{
                                        defaultVal = orgId;
                                    }
                                }else{
                                    defaultVal = orgId;
                                }
                            } else {
                                defaultVal = SysUserHolder.getCurrentUser().getRealName();
                            }
                            break;
                        case "2":
                            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            defaultVal = sdf1.format(new Date());
                            break;
                        case "3":
                            Object deviceVal = dotMap.get(id).get("devicedefaultval");
                            defaultVal = deviceVal != null ? deviceVal.toString() : null;

                            // 对于控件类型15（多选下拉框），使用DEFAULTVALS字段
                            Object controlTypeObj = dotMap.get(id).get("controltype");
                            if (controlTypeObj != null && "15".equals(controlTypeObj.toString())) {
                                Object defaultValsObj = dotMap.get(id).get("defaultvals");
                                if (defaultValsObj != null && StringUtils.isNotEmpty(defaultValsObj.toString())) {
                                    defaultVal = defaultValsObj.toString();
                                }
                            }
                            break;
                        case "4":
                            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
                            defaultVal = sdf2.format(new Date());
                            break;
                        case "5":
                            SimpleDateFormat sdf3 = new SimpleDateFormat("HH:mm:ss");
                            defaultVal = sdf3.format(new Date());
                            break;
                        case "6":
                            //外委人员
                            String userId = SysUserHolder.getCurrentUser().getId();
                            List<EmployeeVo> userPartTimePost = employeeInfoService.getUserPartTimePost(userId);
                            String orgId = SysUserHolder.getCurrentUser().getOrgId();
                            if (StringUtils.isNotEmpty(userPartTimePost)) {
                                EmployeeVo partTimePost = userPartTimePost.stream().filter(item -> item.getOrgcode().equals(orgId)).findFirst().orElse(null);
                                if (partTimePost != null) {
                                    defaultVal = partTimePost.getOrgcode();
                                }else{
                                    defaultVal = orgId;
                                }
                            }else{
                                defaultVal = orgId;
                            }
                            break;
                        case "7":
                            SimpleDateFormat sdf4 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            defaultVal = sdf4.format(new Date());
                            break;
                        case "8":
                            defaultVal = SysUserHolder.getCurrentUser().getId();
                            break;
                        default:
                            break;
                    }
                    if (defaultVal != null) {
                        defaultValues.put(id, defaultVal);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取采集点默认值失败: {}", e.getMessage(), e);
        }

        // 对于每个采集点，如果没有实时值或扩展行值，但有默认值，则使用默认值
        if (!defaultValues.isEmpty()) {
            for (String dotId : filteredList) {
                // 获取该采集点的实时数据映射
                Map<String, String> timeValueMap = tagDataMap.computeIfAbsent(dotId, k -> new HashMap<>());

                // 如果该采集点在指定时间点没有数据，但有默认值，则添加默认值
                if ((timeValueMap.isEmpty() || timeValueMap.get(startTime) == null
                        || "".equals(timeValueMap.get(startTime)))
                        && defaultValues.containsKey(dotId)) {
                    timeValueMap.put(startTime, defaultValues.get(dotId));
                }
            }
        }

        TdsQueryDto tdsParam = new TdsQueryDto();
        // 设置查询参数
        tdsParam.setTdsAlias(param.getTdsAlias());
        tdsParam.setInParaAlias(tdsInParaAlias);

        String accountULInfoCol_value = "";
        // 查询TDS数据
        try {
            JSONArray tdsDatas = tdsServ.getTDSData(tdsParam);
            if (tdsDatas == null || tdsDatas.isEmpty()) {
                log.warn("tdsDatas数据为空: tdsParam={}", tdsParam);
            } else {
                Object dataObj = tdsDatas.getJSONObject(0).get("data");
                // 处理查询结果
                if (dataObj instanceof JSONArray) {
                    JSONArray dataArray = (JSONArray) dataObj;
                    if (StringUtils.isNotEmpty(dataArray)) {
                        if (dataArray.get(0) instanceof com.alibaba.fastjson.JSONObject) {
                            com.alibaba.fastjson.JSONObject dataItem = dataArray.getJSONObject(0);
                            if (dataItem.containsKey(accountULInfoCol)) {
                                accountULInfoCol_value = dataItem.getString(accountULInfoCol);
                            }
                        }
                    }
                } else if (dataObj instanceof com.alibaba.fastjson.JSONObject) {
                    com.alibaba.fastjson.JSONObject dataJson = (com.alibaba.fastjson.JSONObject) dataObj;
                    if (StringUtils.isNotEmpty(dataJson)) {
                        if (dataJson.containsKey(accountULInfoCol)) {
                            accountULInfoCol_value = dataJson.getString(accountULInfoCol);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("查询tds数据异常, tdsParam={}: {}", tdsParam, e.getMessage());
        }

        // 创建accountULInfoMap并将其添加到tagDataMap
        Map<String, String> accountULInfoMap = new HashMap<>();

        // 处理accountULInfoCol_value数据
        if (StringUtils.isEmpty(accountULInfoCol_value)) {
            try {
                // 创建外层JSON对象
                com.alibaba.fastjson.JSONObject rootJson = new com.alibaba.fastjson.JSONObject();
                // 创建内层data对象，用于存放所有采集点数据
                com.alibaba.fastjson.JSONObject dataJson = new com.alibaba.fastjson.JSONObject();

                // 优化：使用已查询的采集点信息，避免重复数据库查询

                // 为每个采集点创建对应的记录
                for (String dotId : filteredList) {
                    // 创建采集点信息对象
                    com.alibaba.fastjson.JSONObject dotInfo = new com.alibaba.fastjson.JSONObject();

                    // 设置默认值
                    String txtValue = "";
                    String comType = dotControlTypeMap.getOrDefault(dotId, "");

                    // 设置控件类型
                    dotInfo.put("com", comType);

                    // 对于控件类型15（多选下拉框），添加显示模式和默认值数组信息
                    if ("comboboxMulti".equals(comType)) {
                        String displayMode = dotDisplayModeMap.getOrDefault(dotId, "dropdown");
                        dotInfo.put("displayMode", displayMode);

                        String defaultValsStr = dotDefaultValsMap.get(dotId);
                        if (StringUtils.isNotEmpty(defaultValsStr)) {
                            // 将逗号分隔的字符串转换为数组
                            String[] defaultValsArray = defaultValsStr.split(",");
                            List<String> defaultValsList = new ArrayList<>();
                            for (String val : defaultValsArray) {
                                if (StringUtils.isNotEmpty(val.trim())) {
                                    defaultValsList.add(val.trim());
                                }
                            }
                            dotInfo.put("defaultVals", defaultValsList);
                        }
                    }

                    // 如果采集点有默认值，设置txt为默认值
                    if (defaultValues.containsKey(dotId)) {
                        txtValue = defaultValues.get(dotId);

                        // 根据控件类型处理txt值
                        if (StringUtils.isNotEmpty(comType) && StringUtils.isNotEmpty(txtValue)) {
                            if ("userfield".equals(comType) || "usersfield".equals(comType)) {
                                // 处理人员控件
                                List<String> userNames = new ArrayList<>();
                                txtValue = txtValue.replaceAll(" ", "");
                                String[] ids = txtValue.split(",");

                                for (String id : ids) {
                                    if (StringUtils.isNotEmpty(id)) {
                                        try {
                                            SysEmployeeInfo employee = employeeInfoService.findEmployeeById(id);
                                            if (employee != null) {
                                                userNames.add(employee.getEmpname());
                                            }
                                        } catch (Exception e) {
                                            log.error("获取人员信息失败: id={}, error={}", id, e.getMessage());
                                        }
                                    }
                                }

                                if (!userNames.isEmpty()) {
                                    txtValue = String.join(",", userNames);
                                }
                            } else if ("orgfield".equals(comType)) {
                                // 处理机构控件
                                List<String> orgNames = new ArrayList<>();
                                txtValue = txtValue.replaceAll(" ", "");
                                String[] ids = txtValue.split(",");

                                for (String id : ids) {
                                    if (StringUtils.isNotEmpty(id)) {
                                        try {
                                            SysOrg org = orgService.findOrgById(id);
                                            if (org != null) {
                                                orgNames.add(org.getOrgname());
                                            }
                                        } catch (Exception e) {
                                            log.error("获取机构信息失败: id={}, error={}", id, e.getMessage());
                                        }
                                    }
                                }

                                if (!orgNames.isEmpty()) {
                                    txtValue = String.join(",", orgNames);
                                }
                            }
                        }
                    }

                    dotInfo.put("txt", txtValue);

                    // 将采集点信息添加到dataJson
                    dataJson.put(dotId, dotInfo);
                }

                // 将data添加到根JSON对象
                rootJson.put("data", dataJson);

                // 更新accountULInfoCol_value为处理后的JSON字符串
                accountULInfoCol_value = rootJson.toJSONString();

            } catch (Exception e) {
                log.error("处理accountULInfoCol_value异常: {}", e.getMessage(), e);
            }
        } else {
            // 处理已有数据，所有默认值，重新计算并更新txt属性
            try {
                // 解析已有的JSON数据
                com.alibaba.fastjson.JSONObject rootJson = com.alibaba.fastjson.JSON.parseObject(accountULInfoCol_value);
                if (rootJson != null && rootJson.containsKey("data")) {
                    com.alibaba.fastjson.JSONObject dataJson = rootJson.getJSONObject("data");

                    // 优化：使用已查询的采集点信息，重新计算所有默认值
                    boolean hasUpdated = false;
                    for (Object dotIdObj : dotMap.keySet()) {
                        String dotId = String.valueOf(dotIdObj);
                        if (dataJson.containsKey(dotId)) {
                            LinkedHashMap<String, Object> dotConfigInfo = dotMap.get(dotIdObj);
                            Object defaultValObj = dotConfigInfo.get("defaultval");

                            // 只处理有默认值配置的采集点
                            if (defaultValObj != null && !"0".equals(defaultValObj.toString()) && StringUtils.isNotEmpty(defaultValObj.toString())) {
                                String defaultValStr = defaultValObj.toString();
                                String newTxtValue = calculateDefaultValue(defaultValStr, dotConfigInfo);

                                if (newTxtValue != null) {
                                    com.alibaba.fastjson.JSONObject dotInfo = dataJson.getJSONObject(dotId);

                                    // 处理特殊控件类型的txt值转换
                                    String comType = dotControlTypeMap.getOrDefault(dotId, "");
                                    if (StringUtils.isNotEmpty(comType) && StringUtils.isNotEmpty(newTxtValue)) {
                                        if ("userfield".equals(comType) || "usersfield".equals(comType)) {
                                            // 处理人员控件：将ID转换为姓名
                                            List<String> userNames = new ArrayList<>();
                                            newTxtValue = newTxtValue.replaceAll(" ", "");
                                            String[] ids = newTxtValue.split(",");

                                            for (String id : ids) {
                                                if (StringUtils.isNotEmpty(id)) {
                                                    try {
                                                        SysEmployeeInfo employee = employeeInfoService.findEmployeeById(id);
                                                        if (employee != null) {
                                                            userNames.add(employee.getEmpname());
                                                        }
                                                    } catch (Exception e) {
                                                        log.error("获取人员信息失败: id={}, error={}", id, e.getMessage());
                                                    }
                                                }
                                            }

                                            if (!userNames.isEmpty()) {
                                                newTxtValue = String.join(",", userNames);
                                            }
                                        } else if ("orgfield".equals(comType)) {
                                            // 处理机构控件：将ID转换为机构名称
                                            List<String> orgNames = new ArrayList<>();
                                            newTxtValue = newTxtValue.replaceAll(" ", "");
                                            String[] ids = newTxtValue.split(",");

                                            for (String id : ids) {
                                                if (StringUtils.isNotEmpty(id)) {
                                                    try {
                                                        SysOrg org = orgService.findOrgById(id);
                                                        if (org != null) {
                                                            orgNames.add(org.getOrgname());
                                                        }
                                                    } catch (Exception e) {
                                                        log.error("获取机构信息失败: id={}, error={}", id, e.getMessage());
                                                    }
                                                }
                                            }

                                            if (!orgNames.isEmpty()) {
                                                newTxtValue = String.join(",", orgNames);
                                            }
                                        }
                                    }

                                    dotInfo.put("txt", newTxtValue);
                                    hasUpdated = true;
                                    log.debug("更新采集点 {} 的txt值为: {}", dotId, newTxtValue);
                                }
                            }
                        }
                    }

                    // 如果有更新，重新序列化JSON数据
                    if (hasUpdated) {
                        accountULInfoCol_value = rootJson.toJSONString();
                        log.info("已重新计算并更新所有采集点的默认值");
                    }
                }
            } catch (Exception e) {
                log.error("处理已有数据的时间默认值更新异常: {}", e.getMessage(), e);
            }
        }

        accountULInfoMap.put(startTime, accountULInfoCol_value);
        tagDataMap.put(accountULInfoCol, accountULInfoMap);

        return tagDataMap;
    }

    // 获取组件类型
    private String getComboStr(Integer confType) {
        return CONTROL_TYPE_MAP.getOrDefault(confType, "textfield");
    }

    /**
     * 统一计算默认值
     * @param defaultVal 默认值类型
     * @param dotInfo 采集点信息
     * @return 计算后的默认值
     */
    private String calculateDefaultValue(String defaultVal, LinkedHashMap<String, Object> dotInfo) {
        if (StringUtils.isEmpty(defaultVal)) {
            return null;
        }

        String result = null;
        switch (defaultVal) {
            case "1": // 当前登录人
                result = SysUserHolder.getCurrentUser().getId();
                break;
            case "2": // 当前时间
                SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                result = sdf1.format(new Date());
                break;
            case "3": // 下拉框预设值
                Object deviceVal = dotInfo != null ? dotInfo.get("devicedefaultval") : null;
                result = deviceVal != null ? deviceVal.toString() : null;

                // 对于控件类型15（多选下拉框），使用DEFAULTVALS字段
                Object controlTypeObj = dotInfo != null ? dotInfo.get("controltype") : null;
                if (controlTypeObj != null && "15".equals(controlTypeObj.toString())) {
                    Object defaultValsObj = dotInfo.get("defaultvals");
                    if (defaultValsObj != null && StringUtils.isNotEmpty(defaultValsObj.toString())) {
                        result = defaultValsObj.toString();
                    }
                }
                break;
            case "4": // 当前日期
                SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
                result = sdf2.format(new Date());
                break;
            case "5": // 当前时间（不含日期）
                SimpleDateFormat sdf3 = new SimpleDateFormat("HH:mm:ss");
                result = sdf3.format(new Date());
                break;
            case "6": // 外委人员
                String userId = SysUserHolder.getCurrentUser().getId();
                List<EmployeeVo> userPartTimePost = employeeInfoService.getUserPartTimePost(userId);
                String orgId = SysUserHolder.getCurrentUser().getOrgId();
                if (StringUtils.isNotEmpty(userPartTimePost)) {
                    EmployeeVo partTimePost = userPartTimePost.stream().filter(item -> item.getOrgcode().equals(orgId)).findFirst().orElse(null);
                    if (partTimePost != null) {
                        result = partTimePost.getOrgcode();
                    } else {
                        result = orgId;
                    }
                } else {
                    result = orgId;
                }
                break;
            case "7": // 当前时间（不含日期）
                SimpleDateFormat sdf4 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                result = sdf4.format(new Date());
                break;
            case "8": // 当前登录人
                result = SysUserHolder.getCurrentUser().getId();
                break;
            default:
                break;
        }
        return result;
    }

    private Map<String, Map<String, String>> initLedgerTimeDatas(List<String> filteredList, List<String> tagNumberList,
            Map<String, Map<String, String>> tagDataMap, String startTime) {
        // 获取采集点仪表位号
        Where where = Where.create();
        where.in(Costunitsampledot::getId, filteredList.toArray());
        where.eq(Costunitsampledot::getTmused, 1);
        Order orderdot = Order.create();
        orderdot.orderByAsc(Costunitsampledot::getTmsort);
        // Where where = Where.create().in(DigitalLedgerModule::getId,
        // filteredList.toArray());
        List<Costunitsampledot> dotList = entityService.queryList(Costunitsampledot.class, where, orderdot);

        Map<String, List<String>> tagNumberMap = new HashMap<>();
        for (Costunitsampledot dot : dotList) {
            List<String> dotIds = tagNumberMap.computeIfAbsent(dot.getTagnumber().toUpperCase(),
                    k -> new ArrayList<>());
            dotIds.add(dot.getId());
        }

        if (StringUtils.isEmpty(dotList)) {
            log.warn("查询数据库采集点为空，参数{}", tagNumberList);
            return tagDataMap;
        }

        // 时间初始化 查实时数据
        // 获取采集点仪表位号
        List<String> tagNumbers = dotList.stream()
                .map(Costunitsampledot::getTagnumber)
                .distinct()
                .collect(Collectors.toList());
        long t1 = System.currentTimeMillis();
        // 查询rtdb数据 接口这个间隔timeInterval是秒
//        List<Tag> dataResult = rtdbService.queryRtdbTagData(tagNumbers, startTime, startTime, 60);
        // 改用新方法,会自动向前一分钟时间找值
        List<Tag> dataResult = rtdbService.queryLastTagDatas(tagNumbers, startTime);
        long t2 = System.currentTimeMillis();
        log.info("台账: 采集点数量: {}, 请求时间: {}， 获取实时数据接口执行耗时: {}ms", tagNumbers.size(), startTime , (t2 - t1));

        // 1. 组织rtdb数据为 Map<String tagCode, Map<String datetime, String value>>
        if (StringUtils.isNotEmpty(dataResult)) {
            for (Tag tag : dataResult) {
                if (tag == null)
                    continue;
                String tagCode = tag.getTagCode();
                List<TagData> datas = tag.getDatas();
                if (StringUtils.isEmpty(datas))
                    continue;

                // Map<String, String> timeValueMap =
                // tagDataMap.computeIfAbsent(tagCode.toUpperCase(),
                // k -> new HashMap<>());

                List<String> dotIds = tagNumberMap.get(tagCode.toUpperCase());
                if (StringUtils.isEmpty(dotIds)) {
                    continue;
                }
                Map<String, String> timeValueMap = new HashMap<>();
                for (String id : dotIds) {
                    timeValueMap = tagDataMap.computeIfAbsent(id,
                            k -> new HashMap<>());
                }

                for (TagData each : datas) {
                    String datetime = each.getDatetime();
                    if (datetime != null && datetime.length() > 19) {
                        datetime = datetime.substring(0, 19);
                    }
                    String value = each.getValue() == null ? null : each.getValue().toString();
                    if (Coms.judgeDouble(value)) {
                        value = new BigDecimal(value).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros()
                                .toPlainString();
                    }
//                    timeValueMap.put(datetime, value);
                    // 改用新方法，改用原始时间，实时接口返回的时间可能对不上
                    timeValueMap.put(startTime, value);
                    log.info("台账仪表接口返回数据：仪表：{}，实时值{}，请求时间：{}，实际时间：{}", tagCode, value, startTime, datetime);
                }
            }
        } else {
            // 默认返回默认 tagDataMap
            Map<String, String> timeValueMap = new HashMap<>();
            for (String id : filteredList) {
                timeValueMap = tagDataMap.computeIfAbsent(id,
                        k -> new HashMap<>());
                timeValueMap.put(startTime, "");
            }
        }
        return tagDataMap;
    }

    /**
     * 初始化台账扩展行数据
     *
     * @param _list          数据源关系列表，包含采集点ID、数据源别名和参数别名
     * @param tdsInParaAlias 数据源输入参数别名
     * @param filteredList   过滤后的采集点ID列表
     * @param startTime      开始时间
     * @return 采集点ID到时间-值映射的映射
     */
    private Map<String, Map<String, String>> initLedgerExcendRowData(
            List<Map<String, Object>> _list,
            String tdsInParaAlias,
            List<String> filteredList,
            String startTime) {

        // 1. 输入参数校验
        if (_list == null || _list.isEmpty()) {
            log.warn("初始化扩展行数据失败：数据源关系列表为空");
            return new HashMap<>();
        }

        Map<String, Map<String, String>> result = new HashMap<>(filteredList.size());

        try {
            // 2. 构建数据源映射关系
            Map<String, Map<String, String>> dataSourceParamMap = buildDataSourceParamMap(_list);

            // 3. 获取数据源别名列表
            List<String> dataAliasList = new ArrayList<>(dataSourceParamMap.keySet());
            if (dataAliasList.isEmpty()) {
                log.warn("未找到有效的数据源别名");
                return result;
            }

            // 4. 查询数据源数据
            Map<String, Map<String, Object>> dsDataMap = queryDataSourceData(dataAliasList, tdsInParaAlias);

            // 5. 生成时间记录
            List<String> dateList = Collections.singletonList(startTime);

            // 6. 为每个采集点生成数据记录
            for (String tagId : filteredList) {
                if (StringUtils.isEmpty(tagId)) {
                    continue; // 跳过无效的采集点ID
                }

                try {
                    // 处理单个采集点
                    Map<String, String> timeDataMap = processTagData(
                            tagId, dataAliasList, dataSourceParamMap, dsDataMap, dateList);

                    // 修复：只有当采集点有有效数据时才添加到结果中，避免空行
                    if (!timeDataMap.isEmpty() && timeDataMap.values().stream().anyMatch(StringUtils::isNotEmpty)) {
                        result.put(tagId, timeDataMap);
                        log.debug("采集点 {} 添加成功，数据点数量: {}", tagId, timeDataMap.size());
                    } else {
                        log.debug("采集点 {} 没有有效数据，跳过添加", tagId);
                    }
                } catch (Exception e) {
                    log.error("处理采集点数据异常, tagId={}: {}", tagId, e.getMessage(), e);
                    // 不再为出错的采集点添加空数据，避免空行
                }
            }

        } catch (Exception e) {
            log.error("初始化扩展行数据异常: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 构建数据源参数映射
     */
    private Map<String, Map<String, String>> buildDataSourceParamMap(List<Map<String, Object>> _list) {
        Map<String, Map<String, String>> dataSourceParamMap = new HashMap<>();

        for (Map<String, Object> map : _list) {
            String collectionPointId = getMapString(map, "COLLECTION_POINT_ID");
            String dataSourceParaAlias = getMapString(map, "DATA_SOURCE_PARA_ALIAS");
            String alias = getMapString(map, "DATA_SOURCE_ALIAS");

            if (StringUtils.isNotEmpty(alias)) {
                Map<String, String> paramMap = dataSourceParamMap.computeIfAbsent(
                        alias, k -> new HashMap<>());

                if (StringUtils.isNotEmpty(collectionPointId) && StringUtils.isNotEmpty(dataSourceParaAlias)) {
                    paramMap.put(collectionPointId, dataSourceParaAlias);
                }
            }
        }

        return dataSourceParamMap;
    }

    /**
     * 查询数据源数据
     */
    private Map<String, Map<String, Object>> queryDataSourceData(
            List<String> dataAliasList, String tdsInParaAlias) {

        Map<String, Map<String, Object>> dsDataMap = new HashMap<>();
        TdsQueryDto tdsParam = new TdsQueryDto();
        tdsParam.setInParaAlias(tdsInParaAlias);

        for (String tdsAlias : dataAliasList) {
            tdsParam.setTdsAlias(tdsAlias);

            try {
                JSONArray currentData = tdsServ.getTDSData(tdsParam);
                if (currentData != null && !currentData.isEmpty()) {
                    com.alibaba.fastjson.JSONObject firstItem = currentData.getJSONObject(0);
                    if (firstItem != null && firstItem.containsKey("data")) {
                        processDataSourceResult(dsDataMap, tdsAlias, firstItem.get("data"));
                    }
                }
            } catch (Exception e) {
                log.warn("查询数据源数据异常, tdsAlias={}: {}", tdsAlias, e.getMessage());
            }
        }

        return dsDataMap;
    }

    /**
     * 处理数据源查询结果
     */
    private void processDataSourceResult(
            Map<String, Map<String, Object>> dsDataMap,
            String tdsAlias,
            Object dataObj) {

        if (dataObj instanceof JSONArray) {
            JSONArray dataArray = (JSONArray) dataObj;
            for (int i = 0; i < dataArray.size(); i++) {
                if (dataArray.get(i) instanceof com.alibaba.fastjson.JSONObject) {
                    com.alibaba.fastjson.JSONObject dataItem = dataArray.getJSONObject(i);
                    dsDataMap.put(tdsAlias + "_" + i, dataItem);
                }
            }
        } else if (dataObj instanceof com.alibaba.fastjson.JSONObject) {
            com.alibaba.fastjson.JSONObject dataJson = (com.alibaba.fastjson.JSONObject) dataObj;
            dsDataMap.put(tdsAlias + "_0", dataJson);
        }
    }

    /**
     * 处理单个采集点的数据
     */
    private Map<String, String> processTagData(
            String tagId,
            List<String> dataAliasList,
            Map<String, Map<String, String>> dataSourceParamMap,
            Map<String, Map<String, Object>> dsDataMap,
            List<String> dateList) {

        Map<String, String> timeDataMap = new HashMap<>(dateList.size());

        // 遍历数据源找到对应的参数值
        for (String dsAlias : dataAliasList) {
            Map<String, String> paramMap = dataSourceParamMap.get(dsAlias);
            if (paramMap == null || !paramMap.containsKey(tagId)) {
                continue;
            }

            String paramAlias = paramMap.get(tagId);
            if (StringUtils.isEmpty(paramAlias)) {
                continue;
            }

            // 查找所有匹配的数据项
            for (Map.Entry<String, Map<String, Object>> entry : dsDataMap.entrySet()) {
                String key = entry.getKey();
                if (!key.startsWith(dsAlias + "_")) {
                    continue;
                }

                Map<String, Object> dataItem = entry.getValue();
                if (dataItem == null || !dataItem.containsKey(paramAlias)) {
                    continue;
                }

                Object paramValue = dataItem.get(paramAlias);
                String value = paramValue == null ? "" : paramValue.toString();

                // 解析索引并设置值
                try {
                    // 更可靠的索引解析方式
                    String[] keyParts = key.split("_");
                    if (keyParts.length > 0) {
                        int index = Integer.parseInt(keyParts[keyParts.length - 1]);
                        if (index < dateList.size()) {
                            // 注意：如果多个数据源有相同时间点的值，后面的会覆盖前面的
                            timeDataMap.put(dateList.get(index), value);
                        }
                    }
                } catch (NumberFormatException e) {
                    log.warn("解析索引失败: {}", key, e);
                }
            }
        }

        // 修复：不强制为所有时间点添加空值，避免生成空行
        // 只保留有实际数据的时间点
        log.debug("采集点 {} 处理完成，有效数据点数量: {}", tagId, timeDataMap.size());

        return timeDataMap;
    }

    /**
     * 根据时间间隔类型生成时间批次
     *
     * @param ledgerId         台账ID
     * @param startTimeStr     开始时间字符串
     * @param endTimeStr       结束时间字符串
     * @param timeInterval     时间间隔
     * @param timeIntervalType 时间间隔类型
     * @return 时间批次列表
     */
    private List<DigitalLedgerTime> generateTimeBatches(String ledgerId, String startTimeStr, String endTimeStr,
            Integer timeInterval, String timeIntervalType) {
        List<DigitalLedgerTime> digitalLedgerTimes = new ArrayList<>();

        try {
            // 解析开始和结束时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date startDate = sdf.parse(startTimeStr);
            Date endDate = sdf.parse(endTimeStr);

            // 设置默认值
            if (timeInterval == null || timeInterval <= 0) {
                timeInterval = 1;
            }

            if (StringUtils.isEmpty(timeIntervalType)) {
                timeIntervalType = "hour";
            }

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);

            int calendarField;
            int timeIntervalSeconds = 0;// 时间间隔秒
            switch (timeIntervalType) {
                case "minute":
                    calendarField = Calendar.MINUTE;
                    timeIntervalSeconds = timeInterval * 60;
                    break;
                case "hour":
                    calendarField = Calendar.HOUR_OF_DAY;
                    timeIntervalSeconds = timeInterval * 60 * 60;
                    break;
                case "day":
                    calendarField = Calendar.DAY_OF_MONTH;
                    timeIntervalSeconds = timeInterval * 24 * 60 * 60;
                    break;
                case "month":
                    calendarField = Calendar.MONTH;
                    timeIntervalSeconds = timeInterval * 30 * 24 * 60 * 60;
                    break;
                case "year":
                    calendarField = Calendar.YEAR;
                    timeIntervalSeconds = timeInterval * 365 * 24 * 60 * 60;
                    break;
                default:
                    calendarField = Calendar.HOUR_OF_DAY;
                    timeIntervalSeconds = timeInterval * 60 * 60;
                    break;
            }

            int batchCount = 0;
            Calendar tempCal = (Calendar) calendar.clone();
            while (!tempCal.getTime().after(endDate)) {
                batchCount++;
                tempCal.add(calendarField, timeInterval);
                // 避免无限循环
                if (batchCount > 9) {
                    break;
                }
            }

            // 如果没有批次，至少创建一个
            if (batchCount == 0) {
                batchCount = 1;
            }

            for (int i = 0; i < batchCount; i++) {
                DigitalLedgerTime time = new DigitalLedgerTime();
                time.setLedgerId(ledgerId);
                time.setTmused(1);

                // 设置开始时间
                Date currentStartTime = calendar.getTime();
                time.setStartTime(currentStartTime);
                time.setStartTimeType("1"); // 1表示动态时间

                // 计算结束时间
                Calendar endCal = (Calendar) calendar.clone();
                endCal.add(calendarField, timeInterval);

                // 确保最后一个批次的结束时间不超过指定的结束时间
                if (endCal.getTime().after(endDate)) {
                    endCal.setTime(endDate);
                }

                time.setEndTime(endCal.getTime());
                time.setEndTimeType("1");

                // 设置时间间隔
                time.setTimeInterval(timeInterval);
                time.setTimeIntervalType(timeIntervalType);

                digitalLedgerTimes.add(time);

                // 增加间隔时间，准备下一个时间点
                calendar.add(calendarField, timeInterval);

                // 如果下一个开始时间已经超过结束时间，则停止循环
                if (calendar.getTime().after(endDate)) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("生成时间批次出错: {}", e.getMessage(), e);
            // 解析时间出错时，返回默认的时间批次（使用原有方法）
            // Map<String, String> map = new HashMap<>();
            // map.put("ledgerModuleId", ledgerId);
            // return initTimeData(map, new ArrayList<>());
            return digitalLedgerTimes;
        }

        return digitalLedgerTimes;
    }

    private String getMapString(Map<String, Object> map, String key) {
        Object obj = map.get(key);
        if (obj == null) {
            return null;
        } else {
            return String.valueOf(obj);
        }
    }

    /**
     * 生成默认时间批次
     *
     * @param ledgerId     台账ID
     * @param startTimeStr 开始时间字符串
     * @param endTimeStr   结束时间字符串
     * @return 时间批次列表
     */
    private List<DigitalLedgerTime> generateTimeBatches(String ledgerId, String startTimeStr, String endTimeStr) {
        // 默认使用1小时间隔
        return generateTimeBatches(ledgerId, startTimeStr, endTimeStr, 1, "hour");
    }

    /**
     * 初始化时间批次 默认8点到17点 间隔1小时
     *
     * @param stringStringMap
     * @param digitalLedgerTime
     * @return
     */
    private List<DigitalLedgerTime> initTimeData(Map<String, String> stringStringMap,
            List<DigitalLedgerTime> digitalLedgerTime) {
        // 生成当天从8点到17点的时间批次，间隔1小时
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();

        // 设置基准日期为当天的8点
        calendar.set(Calendar.HOUR_OF_DAY, 8);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // 生成从8点到17点的时间批次，间隔1小时
        for (int i = 0; i < 10; i++) { // 修改为0-9，共10个小时点
            DigitalLedgerTime time = new DigitalLedgerTime();
            time.setLedgerId(stringStringMap.get("ledgerModuleId"));
            time.setTmused(0);

            // 设置开始时间（当前小时）
            Date currentTime = calendar.getTime();
            time.setStartTime(currentTime);
            time.setStartTimeType("1"); // 1表示当班时间

            // 计算结束时间（当前小时+1小时）
            Calendar endCal = (Calendar) calendar.clone();
            endCal.add(Calendar.HOUR_OF_DAY, 1);
            time.setEndTime(endCal.getTime());
            time.setEndTimeType("1");

            // 设置时间间隔（1小时）
            time.setTimeInterval(1);
            time.setTimeIntervalType("hour"); // 小时间隔

            digitalLedgerTime.add(time);

            // 增加1小时，准备下一个时间点
            calendar.add(Calendar.HOUR_OF_DAY, 1);
        }
        return digitalLedgerTime;
    }

    @Transactional
    @Override
    public Res<?> insertLedgerTime(List<DigitalLedgerTime> lists) {

        if (Objects.isNull(lists)) {
            return Res.FAIL("请求参数不能为空");
        }
        List<DigitalLedgerTime> digitalLedgerTime = this.getDigitalLedgerTimes(lists.get(0).getLedgerId(),
                lists.get(0).getAccountObjId());
        this.deleteBatchProcess(digitalLedgerTime);
        List<DigitalLedgerTime> addLists = new ArrayList<>();
        // List<DigitalLedgerTime> updateLists = new ArrayList<>();
        int max = 0;
        for (DigitalLedgerTime ledgerTime : lists) {
            if (ObjUtils.isEmpty(ledgerTime.getId())) {// 空新增
                max++;
                ledgerTime.setId(TMUID.getUID());
                ledgerTime.setTmSort(max);
                ledgerTime.setTmused(1);
                addLists.add(ledgerTime);
            }
            // else {
            // updateLists.add(ledgerTime);
            // }
        }
        // entityService.updateBatch(updateLists);
        entityService.insertBatch(addLists, 1000);
        return Res.OK();

    }

    @Override
    public LedgerTimeDataVo queryLedgerTime(String ledgerId) {
        LedgerTimeDataVo ledgerTimeDataVo = new LedgerTimeDataVo();

        // 获取台账模型
        DigitalLedgerModule obj = entityService.queryObjectById(DigitalLedgerModule.class, ledgerId);
        if (obj == null) {
            log.warn("查询台账时间批次模式初始化信息失败：未找到台账模型, ledgerId={}", ledgerId);
            return ledgerTimeDataVo;
        }
        // 查询采集点信息，用于返回结构
        List<Costunitsampledot> dotList = new ArrayList<>();
        if (StringUtils.isNotNull(obj.getAccountObjId())) {
            Where wheredot = Where.create();
            wheredot.eq(Costunitsampledot::getTmused, 1);
            wheredot.eq(Costunitsampledot::getBegintime, "2020-01-01");
            wheredot.eq(Costunitsampledot::getUnitid, obj.getAccountObjId());
            Order orderdot = Order.create();
            orderdot.orderByAsc(Costunitsampledot::getTmsort);
            dotList = entityService.queryList(Costunitsampledot.class, wheredot, orderdot);
        }
        ledgerTimeDataVo.setDotList(dotList);

        List<DigitalLedgerTime> digitalLedgerTime = this.getDigitalLedgerTimes(ledgerId, "");

        if (StringUtils.isNotEmpty(digitalLedgerTime)) {
            ledgerTimeDataVo.setResultList(digitalLedgerTime);
            return ledgerTimeDataVo;
        } else {
            // 获取当前日期
            Calendar calendar = Calendar.getInstance();

            // 设置基准日期为当天的8点
            calendar.set(Calendar.HOUR_OF_DAY, 8);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            // 设置开始时间（当前小时）
            Date currentTime = calendar.getTime();
            digitalLedgerTime = new ArrayList<>();
            DigitalLedgerTime digitalLedger = new DigitalLedgerTime();
            digitalLedger.setLedgerId(ledgerId);
            digitalLedger.setTmused(1);
            digitalLedger.setStartTime(currentTime);
            // 计算结束时间（当前小时+1小时）
            Calendar endCal = (Calendar) calendar.clone();
            endCal.add(Calendar.HOUR_OF_DAY, 9);
            digitalLedger.setEndTime(endCal.getTime());
            digitalLedger.setTimeInterval(1);
            digitalLedger.setTimeIntervalType("hour");
            digitalLedger.setStartTimeType("1");
            digitalLedger.setEndTimeType("1");
            digitalLedgerTime.add(digitalLedger);
            ledgerTimeDataVo.setResultList(digitalLedgerTime);
        }
        return ledgerTimeDataVo;
    }

    private List<DigitalLedgerTime> getDigitalLedgerTimes(String ledgerId, String accountObjId) {
        Where where = Where.create();
        where.eq(DigitalLedgerTime::getLedgerId, ledgerId);
        if (StringUtils.isNotEmpty(accountObjId)) {
            where.eq(DigitalLedgerTime::getAccountObjId, accountObjId);
        }
        Order order = Order.create();
        order.orderByAsc(DigitalLedgerTime::getTmSort);
        where.eq(DigitalLedgerTime::getTmused, 1);
        List<DigitalLedgerTime> digitalLedgerTime = entityService.queryList(DigitalLedgerTime.class, where, order);
        return digitalLedgerTime;
    }

    /**
     * 获取表单台账设置数据
     *
     * @param queryDto
     * @return
     */
    @Override
    public DigitalLedger getFormAccountConfigObj(AccountConfigQueryDto queryDto) {
        DigitalLedger result = null;
        if (queryDto != null) {
            String formid = queryDto.getFormid();
            String componentid = queryDto.getComponentid();
            if (StringUtils.isNotEmpty(formid) && StringUtils.isNotEmpty(componentid)) {
                List<DigitalLedger> queryList = this.getDataList(queryDto);
                if (StringUtils.isNotEmpty(queryList)) {
                    result = queryList.get(0);
                }
            }
        }
        return result;
    }

    /**
     * 获取表单台账设置数据
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<DigitalLedger> getDataList(AccountConfigQueryDto queryDto) {
        List<DigitalLedger> list = new ArrayList<DigitalLedger>();
        String formid = null; // 表单id
        String componentid = null; // 组件id
        String maxVersionNum = null; // 最大版本号
        List<String> formids = null; // 最大版本号
        if (StringUtils.isNotNull(queryDto)) {
            formid = queryDto.getFormid();
            componentid = queryDto.getComponentid();
            maxVersionNum = queryDto.getMaxVersionNum();
            formids = queryDto.getFormIds();
        }
        // 查询条件
        Where where = Where.create();
        where.eq(DigitalLedger::getTmused, 1);
        if (StringUtils.isNotEmpty(formid)) { // 表单id
            where.eq(DigitalLedger::getFormId, formid);
        }
        if (StringUtils.isNotEmpty(componentid)) { // 组件id
            where.eq(DigitalLedger::getComponentId, componentid);
        }
        if (StringUtils.isNotEmpty(maxVersionNum)) { // 版本号
            where.le(DigitalLedger::getVersionNum, maxVersionNum);
        }
        if (StringUtils.isNotEmpty(formids)) { // 版本号
            where.in(DigitalLedger::getFormId, formids.toArray());
        }
        // 排序
        Order order = Order.create();
        order.orderByAsc(DigitalLedger::getFormId);
        order.orderByAsc(DigitalLedger::getComponentId);
        order.orderByDesc(DigitalLedger::getTmsort);
        List<DigitalLedger> queryList = entityService.queryData(DigitalLedger.class, where, order, null);
        if (StringUtils.isNotEmpty(queryList)) {
            list = queryList;
        }
        return list;
    }

    /**
     * 保存表单台账设置数据
     *
     * @param saveDto
     * @return
     */
    @Override
    public String saveFormAccountConfigData(AccountConfigSaveDto saveDto) {
        String result = "";
        List<DigitalLedger> addList = new ArrayList<DigitalLedger>();
        List<DigitalLedger> updList = new ArrayList<DigitalLedger>();
        List<DigitalLedger> delList = new ArrayList<DigitalLedger>();
        if (saveDto != null) {
            String formid = saveDto.getFormid();
            List<DigitalLedger> saveList = saveDto.getFormAccountConfigList();
            if (StringUtils.isNotEmpty(formid) && StringUtils.isNotEmpty(saveList)) {
                // 获取已存在数据
                HashMap<String, DigitalLedger> dataMap = new HashMap<String, DigitalLedger>();
                AccountConfigQueryDto queryDto = new AccountConfigQueryDto();
                queryDto.setFormid(formid);
                List<DigitalLedger> dataList = this.getDataList(queryDto);
                if (StringUtils.isNotEmpty(dataList)) {
                    for (int i = 0; i < dataList.size(); i++) {
                        DigitalLedger dataObj = dataList.get(i);
                        String componentid = dataObj.getComponentId();
                        if (StringUtils.isNotEmpty(componentid)) {
                            if (!dataMap.containsKey(componentid)) {
                                dataMap.put(componentid, dataObj);
                            }
                        }
                    }
                }
                // 遍历保存数据
                List<String> hasComponentidList = new ArrayList<String>();
                for (int i = 0; i < saveList.size(); i++) {
                    DigitalLedger saveObj = saveList.get(i);
                    String componentid = saveObj.getComponentId();
                    if (StringUtils.isNotEmpty(componentid) && !hasComponentidList.contains(componentid)) {
                        hasComponentidList.add(componentid); // 判断执行一次
                        if (dataMap.containsKey(componentid)) { // 修改
                            DigitalLedger dataObj = dataMap.get(componentid);
                            String id = dataObj.getId();
                            Integer tmsort = dataObj.getTmsort();
                            BeanUtils.copyProperties(saveObj, dataObj); // 赋予返回对象
                            saveObj.setId(id);
                            saveObj.setTmsort(tmsort);
                            saveObj.setTmused(1);
                            saveObj.setFormId(formid);
                            saveObj.setComponentId(componentid);
                            updList.add(saveObj);
                        } else { // 新增
                            saveObj.setId(TMUID.getUID());
                            saveObj.setTmsort(1);
                            saveObj.setTmused(1);
                            saveObj.setFormId(formid);
                            saveObj.setComponentId(componentid);
                            addList.add(saveObj);
                        }
                        dataMap.remove(componentid);
                    }
                }
                if (StringUtils.isNotEmpty(dataMap)) {
                    Iterator<Map.Entry<String, DigitalLedger>> iterMap = dataMap.entrySet().iterator();
                    while (iterMap.hasNext()) {
                        Map.Entry<String, DigitalLedger> entryMap = iterMap.next();
                        DigitalLedger delObj = entryMap.getValue();
                        delList.add(delObj);
                    }
                }
            }
        }
        if (StringUtils.isNotEmpty(addList) || StringUtils.isNotEmpty(updList) || StringUtils.isNotEmpty(delList)) {
            result = this.saveData(addList, updList, delList);
        }
        return result;
    }

    /**
     * 保存表单台账设置
     *
     * @param addList
     * @param updList
     * @param delList
     * @return
     */
    private String saveData(List<DigitalLedger> addList, List<DigitalLedger> updList, List<DigitalLedger> delList) {
        String result = "";
        if ("".equals(result) && StringUtils.isNotEmpty(delList)) {
            if (entityService.deleteByIdBatch(delList, 500) == 0) {
                result = "删除失败（表单台账设置）！";
            }
        }
        if ("".equals(result) && StringUtils.isNotEmpty(updList)) {
            if (entityService.updateByIdBatch(updList, 500) == 0) {
                result = "修改失败（表单台账设置）！";
            }
        }
        if ("".equals(result) && StringUtils.isNotEmpty(addList)) {
            if (entityService.insertBatch(addList, 500) == 0) {
                result = "添加失败（表单台账设置）！";
            }
        }
        return result;
    }

    /**
     * 获取表单台账模型下拉框数据
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<ComboVo> getTaizModelCombList(AccountConfigQueryDto queryDto) {
        List<ComboVo> result = new ArrayList<ComboVo>();
        List<DigitalLedgerModule> list = this.getListByDigitalLedgerModule(queryDto);
        if (StringUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                DigitalLedgerModule obj = list.get(i);
                String id = obj.getId();
                String moduleName = obj.getModuleName();
                result.add(new ComboVo(id, moduleName));
            }
        }
        return result;
    }

    // -------------------------------- 台账模型设置 ↓ ----------------------------------

    /**
     * 根据ID获取台账模型数据
     *
     * @param id
     * @return
     */
    @Override
    public DigitalLedgerModule getDigitalLedgerModuleById(String id) {
        DigitalLedgerModule result = null;
        if (StringUtils.isNotEmpty(id)) {
            DigitalLedgerModule queryObj = entityService.queryObjectById(DigitalLedgerModule.class, id);
            if (StringUtils.isNotNull(queryObj)) {
                result = queryObj;
            }
        }
        return result;
    }

    /**
     * 获取台账模型数据
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<DigitalLedgerModule> getLedgerModuleList(AccountConfigQueryDto queryDto) {
        List<DigitalLedgerModule> result = new ArrayList<DigitalLedgerModule>();
        List<DigitalLedgerModule> queryList = this.getListByDigitalLedgerModule(queryDto);
        if (StringUtils.isNotEmpty(queryList)) {
            result = queryList;
        }
        return result;
    }

    /**
     * 获取台账模型设置
     *
     * @param queryDto
     * @return
     */
    private List<DigitalLedgerModule> getListByDigitalLedgerModule(AccountConfigQueryDto queryDto) {
        List<DigitalLedgerModule> list = new ArrayList<DigitalLedgerModule>();
        String moduleName = "";
        List<String> ids = null;
        Pagination<?> page = null; // 分页
        if (queryDto != null) {
            moduleName = queryDto.getModuleName();
            page = queryDto.getPage();
            ids = queryDto.getIds();
        }
        // 查询条件
        Where where = Where.create();
        where.eq(DigitalLedgerModule::getTmused, 1);
        if (StringUtils.isNotEmpty(moduleName)) {
            where.like(DigitalLedgerModule::getModuleName, moduleName.trim());
        }
        if (StringUtils.isNotEmpty(ids)) {
            where.in(DigitalLedgerModule::getId, ids.toArray());
        }
        // 排序
        Order order = Order.create();
        order.orderByAsc(DigitalLedgerModule::getTmsort);
        List<DigitalLedgerModule> queryList = entityService.queryData(DigitalLedgerModule.class, where, order, page);
        if (StringUtils.isNotEmpty(queryList)) {
            list = queryList;
        }
        return list;
    }

    /**
     * 保存台账模型数据
     *
     * @param saveDto
     * @return
     */
    @Override
    public String saveLedgerModuleData(AccountConfigSaveDto saveDto) {
        String result = "";
        List<DigitalLedgerModule> addList = new ArrayList<DigitalLedgerModule>();
        List<DigitalLedgerModule> updList = new ArrayList<DigitalLedgerModule>();
        HashMap<String, String> copyIdMap = new HashMap<String, String>(); // <新ID，复制ID> //需要复制采集点相关数据用的模型id

        if (saveDto != null) {
            String editType = saveDto.getEditType();
            List<DigitalLedgerModule> saveList = saveDto.getLedgerModuleList();
            if (StringUtils.isNotEmpty(editType) && StringUtils.isNotEmpty(saveList)) {
                // 保存记录的唯一ID
                List<String> saveIdList = new ArrayList<String>();
                if ("save".equals(editType)) {
                    for (int i = 0; i < saveList.size(); i++) {
                        DigitalLedgerModule saveObj = saveList.get(i);
                        String id_save = saveObj.getId();
                        if (StringUtils.isNotEmpty(id_save) && !saveIdList.contains(id_save)) {
                            saveIdList.add(id_save);
                        }
                    }
                }

                // 获取已存在记录
                int maxPx = 0;
                Map<String, DigitalLedgerModule> sameNameMap = new HashMap<String, DigitalLedgerModule>(); // 判断重复
                Map<String, DigitalLedgerModule> dataMap = new HashMap<String, DigitalLedgerModule>(); // 数据Map
                List<DigitalLedgerModule> dataList = this.getListByDigitalLedgerModule(null);
                if (StringUtils.isNotEmpty(dataList)) {
                    sameNameMap = dataList.stream().collect(Collectors.toMap(DigitalLedgerModule::getModuleName,
                            Function.identity(), (key1, key2) -> key1));
                    dataMap = dataList.stream()
                            .collect(Collectors.toMap(DigitalLedgerModule::getId, Function.identity()));
                    Integer tmsort = dataList.get(dataList.size() - 1).getTmsort();
                    if (tmsort != null) {
                        maxPx = tmsort;
                    }
                }

                // 遍历保存记录
                for (int i = 0; i < saveList.size(); i++) {
                    DigitalLedgerModule saveObj = saveList.get(i);
                    String id_save = saveObj.getId();
                    String name_save = saveObj.getModuleName() == null ? "" : saveObj.getModuleName().trim();
                    if ("save".equals(editType) || "copy".equals(editType)) { // 保存、另存为
                        if ("save".equals(editType) && StringUtils.isNotEmpty(dataMap)
                                && StringUtils.isNotEmpty(id_save) && dataMap.containsKey(id_save)) { // 修改
                            // 核算对象中记录
                            Costuint unitObj = unitMethodService.getCostuintById(id_save);
                            if (unitObj != null) {
                                int tmused = unitObj.getTmused() == null ? 0 : unitObj.getTmused();
                                if (tmused == 1) { // 有效记录
                                    String unitName = unitObj.getName();

                                    if (StringUtils.isNotEmpty(sameNameMap) && StringUtils.isNotEmpty(name_save)
                                            && sameNameMap.containsKey(name_save)) {
                                        DigitalLedgerModule sameObj = sameNameMap.get(name_save);
                                        String sameId = sameObj.getId();
                                        if (StringUtils.isNotEmpty(sameId) && !saveIdList.contains(sameId)) {
                                            result = "台账模型名称【" + name_save + "】已存在，不能重复设置！";
                                            return result;
                                        }
                                    }
                                    DigitalLedgerModule dataObj = dataMap.get(id_save);
                                    BeanUtils.copyProperties(saveObj, dataObj); // 赋予返回对象
                                    dataObj.setModuleName(name_save);
                                    updList.add(dataObj);

                                    if (!name_save.equals(unitName)) { // 模型名称修改，同步到核算对象中
                                        unitObj.setName(name_save);
                                        String costRet = costService.saveDataVo(unitObj, 0);
                                        if (StringUtils.isNotEmpty(costRet)) {
                                            return costRet;
                                        }
                                    }
                                }
                            }
                        } else { // 新增
                            if (StringUtils.isNotEmpty(sameNameMap) && StringUtils.isNotEmpty(name_save)
                                    && sameNameMap.containsKey(name_save)) {
                                result = "台账模型名称【" + name_save + "】已存在，不能重复设置！";
                                return result;
                            }
                            if ("copy".equals(editType)) { // 新增复制
                                if (StringUtils.isNotEmpty(id_save)) {
                                    String copyId = id_save;
                                    id_save = TMUID.getUID();
                                    copyIdMap.put(id_save, copyId);
                                } else {
                                    result = "未获取到复制记录的编码！";
                                    return result;
                                }
                            } else { // 新增保存
                                if (StringUtils.isEmpty(id_save)) {
                                    id_save = TMUID.getUID();
                                }
                            }
                            // 核算对象中记录
                            Costuint unitObj = new Costuint();
                            unitObj.setId(id_save);
                            unitObj.setName(name_save);
                            unitObj.setPid(null);
                            unitObj.setProductive(2); // 台账模型
                            unitObj.setTmused(1);
                            unitObj.setLedgerEntry(1);
                            String costRet = costService.saveDataVo(unitObj, 1);
                            if (StringUtils.isEmpty(costRet)) {
                                maxPx += 1;
                                DigitalLedgerModule dataObj = new DigitalLedgerModule();
                                BeanUtils.copyProperties(saveObj, dataObj); // 赋予返回对象
                                dataObj.setId(id_save);
                                dataObj.setModuleName(name_save);
                                dataObj.setAccountObjId(id_save);
                                dataObj.setTmsort(maxPx);
                                dataObj.setTmused(1);
                                addList.add(dataObj);
                            } else {
                                return costRet;
                            }
                        }
                    } else if ("del".equals(editType)) { // 删除
                        if (StringUtils.isNotEmpty(dataMap) && StringUtils.isNotEmpty(id_save)
                                && dataMap.containsKey(id_save)) {
                            DigitalLedgerModule dataObj = dataMap.get(id_save);
                            if (StringUtils.isNotNull(dataObj)) {
                                // 核算对象中记录
                                Costuint unitObj = unitMethodService.getCostuintById(id_save);
                                if (unitObj != null) {
                                    String costRet = costService.saveDataVo(unitObj, -1);
                                    if (StringUtils.isEmpty(costRet)) {
                                        dataObj.setTmused(0);
                                        updList.add(dataObj);
                                    } else {
                                        result = costRet;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (StringUtils.isEmpty(result) && (StringUtils.isNotEmpty(addList) || StringUtils.isNotEmpty(updList))) {
            result = this.saveDataByDigitalLedgerModule(addList, updList, null);
            if (StringUtils.isEmpty(result) && StringUtils.isNotEmpty(copyIdMap)) { // 复制采集点相关数据
                Iterator<Map.Entry<String, String>> iterMap = copyIdMap.entrySet().iterator();
                while (iterMap.hasNext()) {
                    Map.Entry<String, String> entryMap = iterMap.next();
                    String newId = entryMap.getKey();
                    String copyId = entryMap.getValue();
                    String version = "2020-01-01";
                    interfaceService.copySampledotByUnitId(newId, version, copyId, null, "1", null);
                }
            }
        }
        return result;
    }

    /**
     * 保存台账模型设置
     *
     * @param addList
     * @param updList
     * @param delList
     * @return
     */
    private String saveDataByDigitalLedgerModule(List<DigitalLedgerModule> addList, List<DigitalLedgerModule> updList,
            List<DigitalLedgerModule> delList) {
        String result = "";
        if ("".equals(result) && StringUtils.isNotEmpty(delList)) {
            if (entityService.deleteByIdBatch(delList, 500) == 0) {
                result = "删除失败（台账模型设置）！";
            }
        }
        if ("".equals(result) && StringUtils.isNotEmpty(updList)) {
            if (entityService.updateByIdBatch(updList, 500) == 0) {
                result = "修改失败（台账模型设置）！";
            }
        }
        if ("".equals(result) && StringUtils.isNotEmpty(addList)) {
            if (entityService.insertBatch(addList, 500) == 0) {
                result = "添加失败（台账模型设置）！";
            }
        }
        return result;
    }

    // -------------------------------- Excel解析自动生成采集点 ↓
    // ----------------------------------

    /**
     * Excel解析自动生成采集点_采集点分类树形
     *
     * @param param
     * @return
     */
    @Override
    public List<PotVo> getExcelSampleClassTreeList(ExcelParam param) {
        List<PotVo> result = new ArrayList<PotVo>();
        // 建立虚拟根节点
        PotVo rootNode = new PotVo();
        rootNode.setId("root");
        rootNode.setName("采集点分类");
        rootNode.setType("branch"); // branch-中间节点；leaf-叶子节点；detail-详细数据；
        result.add(rootNode);
        // 查询分类数据
        if (param != null) {
            String ledgerId = param.getModelId();
            boolean details = param.getDetails() == null ? false : param.getDetails(); // 是否返回采集点数据
            if (StringUtils.isNotEmpty(ledgerId)) {
                MethodQueryDto dto = new MethodQueryDto();
                dto.setUnitid(ledgerId);
                dto.setBegintime("2020-01-01");
                List<Costunitsampleclass> classList = unitMethodService.getCostunitsampleclassList(dto);
                if (StringUtils.isNotEmpty(classList)) {
                    Map<String, List<Costunitsampledot>> dotMap = new HashMap<String, List<Costunitsampledot>>();
                    Map<String, List<Costunitsampleclass>> classMap = classList.stream()
                            .collect(Collectors.groupingBy(Costunitsampleclass::getPid));
                    if (details) { // 查询采集点
                        List<Costunitsampledot> dotList = unitMethodService.getCostunitsampledotList(dto);
                        if (StringUtils.isNotEmpty(dotList)) {
                            dotMap = dotList.stream().collect(Collectors.groupingBy(Costunitsampledot::getPid));
                        }
                    }
                    this.setExcelSampleClassChildData(rootNode, classMap, dotMap);
                }
            }
        }
        return result;
    }

    /**
     * Excel解析自动生成采集点_递归设置采集点分类子节点
     *
     * @param pVo
     * @param classMap
     * @param dotMap
     */
    private void setExcelSampleClassChildData(PotVo pVo, Map<String, List<Costunitsampleclass>> classMap,
            Map<String, List<Costunitsampledot>> dotMap) {
        if (StringUtils.isNotNull(pVo)) {
            String pid = pVo.getId();
            List<PotVo> childClassList = new ArrayList<PotVo>();
            if (StringUtils.isNotEmpty(pid) && StringUtils.isNotEmpty(classMap) && classMap.containsKey(pid)) {
                List<Costunitsampleclass> list = classMap.get(pid);
                if (StringUtils.isNotEmpty(list)) {
                    for (int i = 0; i < list.size(); i++) {
                        Costunitsampleclass obj = list.get(i);
                        PotVo vo = new PotVo();
                        vo.setId(obj.getId());
                        vo.setName(obj.getName());
                        childClassList.add(vo);
                        this.setExcelSampleClassChildData(vo, classMap, dotMap);
                    }
                }
            }
            if (StringUtils.isNotEmpty(childClassList)) {
                pVo.setChildren(childClassList);
                pVo.setType("branch"); // branch-中间节点；leaf-叶子节点；detail-详细数据；
            } else {
                if (!"root".equals(pid)) {
                    pVo.setType("leaf");
                    if (StringUtils.isNotEmpty(dotMap) && dotMap.containsKey(pid)) {
                        List<Costunitsampledot> dotList = dotMap.get(pid);
                        if (StringUtils.isNotEmpty(dotList)) {
                            List<PotVo> childDotList = new ArrayList<PotVo>();
                            for (int i = 0; i < dotList.size(); i++) {
                                Costunitsampledot obj = dotList.get(i);
                                String mark = obj.getMark();
                                if (StringUtils.isNotEmpty(mark)) {
                                    PotVo vo = new PotVo();
                                    vo.setId(obj.getId());
                                    vo.setName(obj.getName());
                                    vo.setType("detail"); // branch-中间节点；leaf-叶子节点；detail-详细数据；
                                    vo.setMark(obj.getMark());
                                    childDotList.add(vo);
                                }
                            }
                            if (StringUtils.isNotEmpty(childDotList)) {
                                pVo.setChildren(childDotList);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Excel解析自动生成采集点_保存采集点数据
     *
     * @param param
     * @return
     */
    @Override
    public ExcelParam saveExcelSampleDot(ExcelParam param) {
        String defBegintime = "2020-01-01";
        String defClassName = "采集点"; // 默认的采集分类名称（根节点下）
        if (param != null) {
            String ledgerId = param.getModelId(); // 台账模型ID
            String ledgerName = param.getName() == null ? "" : param.getName().trim(); // 台账模型名称
            // String classId = param.getPid(); // 分类ID
            List<ExcelDot> dotSaveList = param.getResults(); // 采集点数据
            if (StringUtils.isNotEmpty(ledgerId) && StringUtils.isNotEmpty(ledgerName)
                    && StringUtils.isNotEmpty(dotSaveList)) {

                boolean isOK = true; // 判断每个执行过程是否成功

                // 1、整理台账模型数据
                List<DigitalLedgerModule> moduleSaveList = new ArrayList<DigitalLedgerModule>();
                DigitalLedgerModule moduleObj = this.getDigitalLedgerModuleById(ledgerId);
                if (moduleObj != null && moduleObj.getTmused() != null && moduleObj.getTmused() == 1) { // 已存在，更新模型数据
                    String moduleName_o = moduleObj.getModuleName() == null ? "" : moduleObj.getModuleName().trim();
                    if (!ledgerName.equals(moduleName_o)) {
                        moduleObj.setModuleName(ledgerName);
                        moduleSaveList.add(moduleObj);
                    }
                } else { // 不存在，生成新的模型数据
                    moduleObj = new DigitalLedgerModule();
                    moduleObj.setId(ledgerId);
                    moduleObj.setModuleName(ledgerName);
                    moduleSaveList.add(moduleObj);
                }
                // 保存台账模型数据
                if (StringUtils.isNotEmpty(moduleSaveList)) {
                    AccountConfigSaveDto moduleSaveDto = new AccountConfigSaveDto();
                    moduleSaveDto.setEditType("save");
                    moduleSaveDto.setLedgerModuleList(moduleSaveList);
                    String ret = this.saveLedgerModuleData(moduleSaveDto);
                    if (StringUtils.isNotEmpty(ret)) {
                        isOK = false;
                        param = null; // 执行失败，返回NULL
                    }
                }
                // //保存台账表单关系表
                // DigitalLedger digitalLedger = new DigitalLedger();
                // digitalLedger.setId(TMUID.getUID());
                // digitalLedger.setFormId(param.getBelongId());
                // digitalLedger.setLedgerName(ledgerName);
                // digitalLedger.setLedgerModuleId(ledgerId);
                // digitalLedger.setTmused(1);
                // entityService.insert(digitalLedger);
                // 台账模型执行成功
                if (isOK) {
                    String sampleClassId = this.generaterClass(param, defBegintime, defClassName);
                    // 采集点分类执行成功
                    if (isOK && StringUtils.isNotEmpty(sampleClassId)) {

                        // 3、整理采集点数据
                        Map<String, Costunitsampledot> dotMarkMap = new HashMap<String, Costunitsampledot>();
                        Map<String, List<Costunitsampledot>> dotPidMap = new HashMap<String, List<Costunitsampledot>>();
                        MethodQueryDto dotDto = new MethodQueryDto();
                        dotDto.setUnitid(ledgerId);
                        dotDto.setBegintime(defBegintime);
                        List<Costunitsampledot> dotList = unitMethodService.getCostunitsampledotList(dotDto);
                        if (StringUtils.isNotEmpty(dotList)) {
                            dotPidMap = dotList.stream().collect(Collectors.groupingBy(Costunitsampledot::getPid));
                            for (int i = 0; i < dotList.size(); i++) {
                                Costunitsampledot dotObj = dotList.get(i);
                                String mark = dotObj.getMark() == null ? "" : dotObj.getMark().trim();
                                if (StringUtils.isNotEmpty(mark) && !dotMarkMap.containsKey(mark)) {
                                    dotMarkMap.put(mark, dotObj);
                                }
                            }
                        }
                        // 遍历保存的采集点数据
                        List<Costunitsampledot> addDotList = new ArrayList<Costunitsampledot>();
                        List<Costunitsampledot> updDotList = new ArrayList<Costunitsampledot>();
                        HashMap<String, String> hasR3dbMap = new HashMap<String, String>();
                        List<Costunitsampledot> synR3dbList = new ArrayList<Costunitsampledot>(); // 同步R3DB数据
                        List<String> meterUnitList = new ArrayList<String>(); // 计量单位
                        for (int i = 0; i < dotSaveList.size(); i++) {
                            ExcelDot dotSaveObj = dotSaveList.get(i);
                            String mark_s = dotSaveObj.getMark() == null ? "" : dotSaveObj.getMark().trim(); // 唯一标识
                            if (StringUtils.isNotEmpty(mark_s)) {
                                String tagnumber_s = dotSaveObj.getTagnumber(); // 仪表位号
                                String name_s = dotSaveObj.getName(); // 仪表名称
                                String datasource_s = dotSaveObj.getDatasource(); // 数据来源 实时位号
                                String sdUnit_s = dotSaveObj.getSdUnit(); // 计量单位
                                String uplow_s = dotSaveObj.getUplow(); // 上下限
                                // 属性赋值
                                Costunitsampledot dotObj = new Costunitsampledot();
                                Costunitsampledot dotItem = new Costunitsampledot(); // 原来的采集点数据
                                int opResult = 1; // 操作结果：1-添加；2-更新；
                                boolean isPx = false; // 是否需要排序
                                if (StringUtils.isNotEmpty(dotMarkMap) && dotMarkMap.containsKey(mark_s)) { // 修改
                                    dotObj = dotMarkMap.get(mark_s);
                                    opResult = 2;
                                    BeanUtils.copyProperties(dotObj, dotItem); // 赋予返回对象
                                    // 判断是否需要重新排序
                                    String pid = dotObj.getPid() == null ? "" : dotObj.getPid();
                                    if (!pid.equals(sampleClassId)) {
                                        isPx = true;
                                    }
                                } else { // 新增
                                    isPx = true;
                                    // 赋值
                                    dotObj.setId(TMUID.getUID());
                                    dotObj.setBegintime(defBegintime);
                                    dotObj.setUnitid(ledgerId);
                                    dotObj.setCtype("2"); // 采集类型：1、成本仪表；2、控制指标；3、lims指标；0、无；
                                    dotObj.setIsUseToRecordEvent(0); // 是否用于记事：1、是；其他不是（默认）
                                    dotObj.setIsWriteBackInfluxdb(1); // 数据是否回写到influxdb：1、是（默认）；其他不是
                                    dotObj.setSourceype("1"); // 来源类型：1、influxdb；2、数据源；3、手工填写；
                                    dotObj.setTmused(1);
                                    dotObj.setUseTo(1); // 用途：控制指标是用于总平稳率计算，LIMS指标是用于总合格率计算，默认勾选
                                    dotObj.setMark(mark_s);
                                    dotMarkMap.put(mark_s, dotObj);
                                }
                                if (isPx) {
                                    int maxPx = 0;
                                    if (StringUtils.isNotEmpty(dotPidMap) && dotPidMap.containsKey(sampleClassId)) {
                                        List<Costunitsampledot> list = dotPidMap.get(sampleClassId);
                                        if (StringUtils.isNotEmpty(list)) {
                                            Integer tmsort = list.get(list.size() - 1).getTmsort();
                                            if (tmsort != null) {
                                                maxPx = tmsort;
                                            }
                                        }
                                    }
                                    maxPx += 1;
                                    dotObj.setTmsort(maxPx);
                                    // 将新排序数据放入Map
                                    if (dotPidMap.containsKey(sampleClassId)) {
                                        List<Costunitsampledot> list = dotPidMap.get(sampleClassId);
                                        list.add(dotObj);
                                    } else {
                                        List<Costunitsampledot> list = new ArrayList<Costunitsampledot>();
                                        list.add(dotObj);
                                        dotPidMap.put(sampleClassId, list);
                                    }
                                }
                                dotObj.setPid(sampleClassId);
                                if (tagnumber_s != null) { // 仪表位号
                                    dotObj.setTagnumber(tagnumber_s.trim());
                                }
                                if (name_s != null) { // 仪表名称
                                    dotObj.setName(name_s.trim());
                                }
                                if (datasource_s != null) { // 数据来源 实时位号
                                    dotObj.setDatasource(datasource_s.trim());
                                }
                                if (sdUnit_s != null) { // 计量单位
                                    sdUnit_s = sdUnit_s.trim();
                                    dotObj.setSdUnit(sdUnit_s);
                                    if (StringUtils.isNotEmpty(sdUnit_s) && !meterUnitList.contains(sdUnit_s)) {
                                        meterUnitList.add(sdUnit_s);
                                    }
                                }
                                if (uplow_s != null) { // 上下限
                                    this.setDotAttribute(dotObj, "uplow", uplow_s);
                                }
                                // 必填项判断赋值
                                if (StringUtils.isEmpty(dotObj.getTagnumber())) { // 仪表位号
                                    dotObj.setTagnumber(mark_s);
                                }
                                if (StringUtils.isEmpty(dotObj.getName())) { // 仪表名称
                                    dotObj.setName(mark_s);
                                }
                                if (opResult == 2) { // 修改
                                    updDotList.add(dotObj);
                                    // 获取同步r3db的采集点数据
                                    unitMethodService.getSynR3dbSampledotData(dotItem, dotObj, synR3dbList, hasR3dbMap);
                                } else { // 新增
                                    addDotList.add(dotObj);
                                    // 获取同步r3db的采集点数据
                                    unitMethodService.getSynR3dbSampledotData(dotObj, null, synR3dbList, hasR3dbMap);
                                }
                                dotSaveObj.setOpResult(opResult);
                            }
                        }
                        if (StringUtils.isNotEmpty(addDotList) || StringUtils.isNotEmpty(updDotList)) {
                            String ret = unitMethodService.saveSampledotDataByIsUpdNull(true, addDotList, updDotList,
                                    null);
                            if (StringUtils.isNotEmpty(ret)) {
                                isOK = false;
                                param = null; // 执行失败，返回NULL
                            } else {
                                unitMethodService.synOtherInterfaceBySampledotChange(addDotList, updDotList);
                                // 同步R3DB数据
                                if (StringUtils.isNotEmpty(synR3dbList)) {
                                    unitMethodService.synR3dbDataBySampledot(synR3dbList, null);
                                }
                                // 保存计量单位
                                if (StringUtils.isNotEmpty(meterUnitList)) {
                                    importExcelService.asyncMeterUnitData(meterUnitList);
                                }
                            }
                        }
                    }
                }
            }
        }
        return param;
    }

    /**
     * 生成采集点分类
     *
     * <AUTHOR>
     * @date 2025/4/23
     * @params getModelId 字段实际是台账模型id
     * @return
     *
     */
    @Override
    public String generaterClass(ExcelParam param, String defBegintime, String defClassName) {
        // boolean isOK = true; // 判断每个执行过程是否成功
        String ledgerId = param.getModelId(); // 台账模型ID
        // String ledgerName = param.getName() == null ? "" : param.getName().trim(); //
        // 台账模型名称
        String classId = param.getPid(); // 分类ID
        // 2、整理采集分类数据
        String sampleClassId = ""; // 采集分类ID
        List<Costunitsampleclass> addClassList = new ArrayList<Costunitsampleclass>();
        Map<String, Costunitsampleclass> classDataMap = new HashMap<String, Costunitsampleclass>();
        Map<String, List<Costunitsampleclass>> classPidMap = new HashMap<String, List<Costunitsampleclass>>();
        MethodQueryDto classDto = new MethodQueryDto();
        classDto.setUnitid(ledgerId);
        classDto.setBegintime(defBegintime);
        List<Costunitsampleclass> classList = unitMethodService.getCostunitsampleclassList(classDto);
        if (StringUtils.isNotEmpty(classList)) {
            classDataMap = classList.stream()
                    .collect(Collectors.toMap(Costunitsampleclass::getId, Function.identity()));
            classPidMap = classList.stream().collect(Collectors.groupingBy(Costunitsampleclass::getPid));
        }
        // 获取默认分类节点ID
        String defClassId = ""; // 默认采集分类ID
        int classMaxPx = 0;
        if (StringUtils.isNotEmpty(classPidMap) && classPidMap.containsKey("root")) {
            List<Costunitsampleclass> classFirstList = classPidMap.get("root");
            if (StringUtils.isNotEmpty(classFirstList)) {
                Integer tmsort = classFirstList.get(classFirstList.size() - 1).getTmsort();
                if (tmsort != null) {
                    classMaxPx = tmsort;
                }
                for (int i = 0; i < classFirstList.size(); i++) {
                    Costunitsampleclass classFirstObj = classFirstList.get(i);
                    String className = classFirstObj.getName();
                    if (StringUtils.isNotEmpty(className) && defClassName.equals(className.trim())) {
                        defClassId = classFirstObj.getId();
                        break;
                    }
                }
            }
        }
        boolean isNewDefClassNode = false; // 是否创建新的默认分类节点
        if (StringUtils.isEmpty(classId) || "root".equals(classId)) { // 未传入分类ID、传入的虚拟根节点ID
            // 使用默认分类ID
            if (StringUtils.isNotEmpty(defClassId)) {
                sampleClassId = defClassId;
            } else {
                isNewDefClassNode = true;
            }
        } else {
            if (StringUtils.isNotEmpty(classId) && StringUtils.isNotEmpty(classDataMap)
                    && classDataMap.containsKey(classId)
                    && (StringUtils.isEmpty(classPidMap) || !classPidMap.containsKey(classId))) { // 有分类ID，并且分类下没有子分类节点，才能生成采集点数据
                sampleClassId = classId;
            } else {
                // 使用默认分类ID
                if (StringUtils.isNotEmpty(defClassId)) {
                    sampleClassId = defClassId;
                } else {
                    isNewDefClassNode = true;
                }
            }
        }
        if (isNewDefClassNode) { // 生成新的分类节点
            sampleClassId = TMUID.getUID();
            Costunitsampleclass classObj = new Costunitsampleclass();
            classObj.setId(sampleClassId);
            classObj.setPid("root");
            classObj.setName(defClassName);
            classObj.setBegintime(defBegintime);
            classObj.setUnitid(ledgerId);
            classObj.setCtype(null);
            classObj.setTmused(1);
            classObj.setTmsort(++classMaxPx);
            addClassList.add(classObj);
            String ret = unitMethodService.saveSampleclassData(addClassList, null, null);
            if (StringUtils.isNotEmpty(ret)) {
                return null;
                // 执行失败，返回NULL
            } else { // 执行成功，将新分类数据放入Map
                classDataMap.put(sampleClassId, classObj);
            }
        }
        return sampleClassId;
    }

    /**
     * 设置采集点属性值
     *
     * @param dotObj
     * @param colmCode
     * @param dataVal
     */
    private void setDotAttribute(Costunitsampledot dotObj, String colmCode, String dataVal) {
        if (dotObj != null && StringUtils.isNotEmpty(colmCode)) {
            if (StringUtils.isNotEmpty(dataVal)) {
                dataVal = " " + dataVal.replaceAll("\r", "\n").replaceAll("\n", "") + " ";
            }
            if ("uplow".equals(colmCode)) { // 指标上下限
                Double indexRangeLower = null;
                Double indexRangeUpper = null;
                if (StringUtils.isEmpty(dataVal) || "/".equals(dataVal.trim())) {
                    // 空值
                } else if (dataVal.indexOf("＜") != -1 || dataVal.indexOf("<") != -1 || dataVal.indexOf("≤") != -1
                        || dataVal.indexOf("<=") != -1) {
                    String[] dataValArr = null;
                    dataVal = dataVal.replaceAll("＜", "<").replaceAll("<=", "<").replaceAll("≤", "<");
                    if (dataVal.indexOf("<") != -1) {
                        dataValArr = dataVal.split("<");
                    }
                    if (dataValArr != null && dataValArr.length == 2) {
                        String val = dataValArr[1];
                        if (StringUtils.isNotEmpty(val)) {
                            if (val.indexOf("±") != -1) { // 范围
                                try {
                                    double val_d = Double.valueOf(val.replaceAll("±", "").trim());
                                    indexRangeLower = 0 - val_d;
                                    indexRangeUpper = val_d;
                                } catch (Exception e) {
                                }
                            } else { // 上限
                                try {
                                    indexRangeUpper = Double.valueOf(val.trim());
                                } catch (Exception e) {
                                }
                            }
                        }
                    }
                } else if (dataVal.indexOf(">") != -1 || dataVal.indexOf("≥") != -1 || dataVal.indexOf(">=") != -1) {
                    String[] dataValArr = null;
                    dataVal = dataVal.replaceAll(">=", ">").replaceAll("≥", ">");
                    if (dataVal.indexOf(">") != -1) {
                        dataValArr = dataVal.split(">");
                    }
                    if (dataValArr != null && dataValArr.length == 2) {
                        String val = dataValArr[1];
                        if (StringUtils.isNotEmpty(val)) {
                            try {
                                indexRangeLower = Double.valueOf(val.trim());
                            } catch (Exception e) {
                            }
                        }
                    }
                } else if (dataVal.indexOf("～") != -1 || dataVal.indexOf("~") != -1 || dataVal.indexOf("——") != -1) {
                    dataVal = dataVal.replaceAll("——", "~").replaceAll("～", "~");
                    String[] dataValArr = dataVal.split("~");
                    if (dataValArr != null && dataValArr.length == 2) {
                        String valMin = dataValArr[0];
                        String valMax = dataValArr[1];
                        if (StringUtils.isNotEmpty(valMin)) {
                            try {
                                indexRangeLower = Double.valueOf(valMin.trim());
                            } catch (Exception e) {
                            }
                        }
                        if (StringUtils.isNotEmpty(valMax)) {
                            try {
                                indexRangeUpper = Double.valueOf(valMax.trim());
                            } catch (Exception e) {
                            }
                        }
                    }
                } else if ((dataVal.indexOf("—") != -1 || dataVal.indexOf("-") != -1)
                        && (dataVal.replaceAll("—", "-").split("-").length == 2)) {
                    String[] dataValArr = dataVal.replaceAll("—", "-").split("-");
                    String valMin = dataValArr[0];
                    String valMax = dataValArr[1];
                    if (StringUtils.isNotEmpty(valMin) && StringUtils.isNotEmpty(valMax)) {
                        try {
                            indexRangeLower = Double.valueOf(valMin.trim());
                            indexRangeUpper = Double.valueOf(valMax.trim());
                        } catch (Exception e) {
                        }
                    } else if (StringUtils.isNotEmpty(valMin) && StringUtils.isEmpty(valMax)) {
                        try {
                            indexRangeLower = Double.valueOf(valMin.trim());
                        } catch (Exception e) {
                        }
                    } else if (StringUtils.isEmpty(valMin) && StringUtils.isNotEmpty(valMax)) {
                        try {
                            indexRangeLower = 0 - Double.valueOf(valMax.trim());
                        } catch (Exception e) {
                        }
                        indexRangeUpper = indexRangeLower;
                    }
                } else {
                    if (StringUtils.isNotEmpty(dataVal)) {
                        try {
                            indexRangeLower = Double.valueOf(dataVal.trim());
                        } catch (Exception e) {
                        }
                        indexRangeUpper = indexRangeLower;
                    }
                }
                dotObj.setIndexRangeLower(indexRangeLower);
                dotObj.setIndexRangeUpper(indexRangeUpper);
            }
        }
    }

    // -------------------------------- 台账扩展行初始化 ↓
    // ----------------------------------

    /**
     * 查询采集点作为表头数据列表
     *
     * @param ledgerId
     * @return
     */
    @Override
    public List<CostitemVo> getDotTitleList(String ledgerId) {
        List<CostitemVo> result = new ArrayList<CostitemVo>();
        if (StringUtils.isNotEmpty(ledgerId)) {
            // 根据id获取台账模型数据
            DigitalLedgerModule queryObj = entityService.queryObjectById(DigitalLedgerModule.class, ledgerId);
            if (queryObj != null) {
                String accountObjId = queryObj.getAccountObjId();
                if (StringUtils.isNotEmpty(accountObjId)) {
                    paramDto dto = new paramDto();
                    dto.setUnitcode(accountObjId);
                    dto.setVersion("2020-01-01");
                    CostitemVo itemVo = unitConfService.loadPotTree(dto);
                    if (itemVo != null) {
                        this.getAllDotList(itemVo.getChildren(), result);
                    }
                }
            }
        }
        return result;
    }

    // 获取所有采集点数据
    private void getAllDotList(List<CostitemVo> list, List<CostitemVo> result) {
        if (result == null) {
            result = new ArrayList<CostitemVo>();
        }
        if (StringUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                CostitemVo obj = list.get(i);
                int nodeTypeMark = obj.getNodeTypeMark() == null ? 0 : obj.getNodeTypeMark();
                if (nodeTypeMark == 3) { // 采集点
                    result.add(obj);
                } else {
                    List<CostitemVo> childList = obj.getChildren();
                    if (StringUtils.isNotEmpty(childList)) {
                        this.getAllDotList(childList, result);
                    }
                }
            }
        }
    }

    /**
     * 获取数据源下拉框列表
     *
     * @return
     */
    @Override
    public List<TdataSource> getTdsCombList() {
        List<TdataSource> result = new ArrayList<TdataSource>();
        TdsFormulaQueryDto queryDto = new TdsFormulaQueryDto();
        queryDto.setShowTdsType("TDSSQL");
        List<TdsFormulaTreeVo> treeList = tdsFormulaServ.getTdsFormulaTreeList(queryDto);
        if (StringUtils.isNotEmpty(treeList)) {
            this.getAllLeafNode(treeList, result);
        }
        return result;
    }

    // 获取所有数据源数据
    private void getAllLeafNode(List<TdsFormulaTreeVo> treeList, List<TdataSource> result) {
        if (result == null) {
            result = new ArrayList<TdataSource>();
        }
        if (StringUtils.isNotEmpty(treeList)) {
            for (int i = 0; i < treeList.size(); i++) {
                TdsFormulaTreeVo treeObj = treeList.get(i);
                Integer isleaf = treeObj.getIsleaf() == null ? 0 : treeObj.getIsleaf();
                if (isleaf == 1) {
                    String tdsAlias = treeObj.getNodeId();
                    String tdsName = treeObj.getNodeName();
                    if (StringUtils.isNotEmpty(tdsAlias) && StringUtils.isNotEmpty(tdsName)) {
                        TdataSource tds = new TdataSource();
                        tds.setTdsalias(tdsAlias);
                        tds.setTdsname(tdsName);
                        result.add(tds);
                    }
                } else {
                    List<TdsFormulaTreeVo> childList = treeObj.getChildren();
                    this.getAllLeafNode(childList, result);
                }
            }
        }
    }

    /**
     * 获取数据源输入参数列表
     *
     * @param tdsAlias
     * @return
     */
    @Override
    public List<TdsinPara> getTdsInParamList(String tdsAlias) {
        List<TdsinPara> result = new ArrayList<TdsinPara>();
        if (StringUtils.isNotEmpty(tdsAlias)) {
            List<TdsinPara> list = tdsServ.getTDSInPara(tdsAlias);
            if (StringUtils.isNotEmpty(list)) {
                result = list;
            }
        }
        return result;
    }

    /**
     * 获取数据源输出参数列表
     *
     * @param tdsAlias
     * @return
     */
    @Override
    public List<TdsoutPara> getTdsOutParamList(String tdsAlias) {
        List<TdsoutPara> result = new ArrayList<TdsoutPara>();
        if (StringUtils.isNotEmpty(tdsAlias)) {
            List<TdsoutPara> list = tdsServ.getTDSOutPara(tdsAlias);
            if (StringUtils.isNotEmpty(list)) {
                result = list;
            }
        }
        return result;
    }

    /**
     * 获取台账扩展行初始化数据
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<DigitalLedgerExtendRow> getInitExtendRowList(AccountConfigQueryDto queryDto) {
        List<DigitalLedgerExtendRow> list = new ArrayList<DigitalLedgerExtendRow>();
        String ledgerId = ""; // 台账id
        if (StringUtils.isNotNull(queryDto)) {
            ledgerId = queryDto.getLedgerId();
        }
        // 查询条件
        Where where = Where.create();
        where.eq(DigitalLedgerExtendRow::getTmused, 1);
        if (StringUtils.isNotEmpty(ledgerId)) { // 台账id
            where.eq(DigitalLedgerExtendRow::getLedgerId, ledgerId);
        }
        // 排序
        Order order = Order.create();
        order.orderByAsc(DigitalLedgerExtendRow::getParaId);
        List<DigitalLedgerExtendRow> queryList = entityService.queryData(DigitalLedgerExtendRow.class, where, order,
                null);
        if (StringUtils.isNotEmpty(queryList)) {
            list = queryList;
        }
        return list;
    }

    /**
     * 保存台账扩展行初始化数据
     *
     * @param saveDto
     * @return
     */
    @Override
    public String saveInitExtendRowData(AccountConfigSaveDto saveDto) {
        String result = "";
        List<DigitalLedgerExtendRow> addList = new ArrayList<DigitalLedgerExtendRow>();
        List<DigitalLedgerExtendRow> delList = new ArrayList<DigitalLedgerExtendRow>();
        if (saveDto != null) {
            String ledgerId = saveDto.getLedgerId();
            List<DigitalLedgerExtendRow> saveList = saveDto.getLedgerExtendRowList();
            if (StringUtils.isNotEmpty(ledgerId)) {
                // 获取已存在数据
                AccountConfigQueryDto queryDto = new AccountConfigQueryDto();
                queryDto.setLedgerId(ledgerId);
                List<DigitalLedgerExtendRow> dataList = this.getInitExtendRowList(queryDto);
                if (StringUtils.isNotEmpty(dataList)) {
                    delList.addAll(dataList);
                }
                // 遍历保存数据
                if (StringUtils.isNotEmpty(saveList)) {
                    for (int i = 0; i < saveList.size(); i++) {
                        DigitalLedgerExtendRow saveObj = saveList.get(i);
                        saveObj.setId(TMUID.getUID());
                        addList.add(saveObj);
                    }
                }
            }
        }
        result = this.saveDataByInitExtendRow(addList, null, delList);
        return result;
    }

    /**
     * 保存台账扩展行初始化
     *
     * @param addList
     * @param updList
     * @param delList
     * @return
     */
    private String saveDataByInitExtendRow(List<DigitalLedgerExtendRow> addList, List<DigitalLedgerExtendRow> updList,
            List<DigitalLedgerExtendRow> delList) {
        String result = "";
        if ("".equals(result) && StringUtils.isNotEmpty(delList)) {
            if (entityService.deleteByIdBatch(delList, 500) == 0) {
                result = "删除失败（台账扩展行初始化）！";
            }
        }
        if ("".equals(result) && StringUtils.isNotEmpty(updList)) {
            if (entityService.updateByIdBatch(updList, 500) == 0) {
                result = "修改失败（台账扩展行初始化）！";
            }
        }
        if ("".equals(result) && StringUtils.isNotEmpty(addList)) {
            if (entityService.insertBatch(addList, 500) == 0) {
                result = "添加失败（台账扩展行初始化）！";
            }
        }
        return result;
    }

}
