package com.yunhesoft.accountTools.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.accountTools.entity.dto.AccountParam;
import com.yunhesoft.accountTools.entity.po.DigitalLedger;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerModule;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerTime;
import com.yunhesoft.accountTools.entity.vo.AccountAppVo;
import com.yunhesoft.accountTools.service.IAccountAppService;
import com.yunhesoft.accountTools.service.IAccountConfigService;
import com.yunhesoft.core.common.aviator.AviatorResult;
import com.yunhesoft.core.common.aviator.AviatorUtils;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.entity.dto.AccountSaveDto;
import com.yunhesoft.joblist.entity.po.JoblistPersonBind;
import com.yunhesoft.joblist.service.IJobGeneraterService;
import com.yunhesoft.leanCosting.calcLogic.PublicMethods;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.vo.SampleDotByClassVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostuintService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.mobile.entity.vo.AcctobjInputFlVo;
import com.yunhesoft.mobile.entity.vo.AcctobjInputVo;
import com.yunhesoft.mobile.entity.vo.AcctobjInputmxVo;
import com.yunhesoft.mobile.exception.MobileException;
import com.yunhesoft.mobile.service.IMobLeanCostingService;
import com.yunhesoft.qualityIndex.compute.QualityIndexComputeExector;
import com.yunhesoft.qualityIndex.entity.dto.QualityIndexComputeParamsDto;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.entity.dto.TdsQueryDto;
import com.yunhesoft.system.tds.entity.po.TdsAccountMarkinfo;
import com.yunhesoft.system.tds.entity.po.TdsAccountMeter;
import com.yunhesoft.system.tds.service.IDataSourceAccountService;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tds.service.impl.ControlTypeConverter;
import com.yunhesoft.system.tools.formulaParam.entity.vo.TdsFormulaTreeVo;
import com.yunhesoft.tmsf.form.service.IFormManageService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Log4j2
@Service
public class AccountAppServiceImpl implements IAccountAppService {

    private static final Pattern PARAM_PATTERN = Pattern.compile("\\{(.*?)\\}");
    @Autowired
    EntityService srv;
    @Autowired
    private IFormManageService formService;
    @Autowired
    private IDataSourceAccountService accountServ;
    @Autowired
    private IDataSourceService tdsServ;    //数据源
    @Autowired
    private ICostuintService iCostunitService;
    @Autowired
    private UnitItemInfoService unitItemInfoService;
    @Autowired
    private IAccountConfigService configService;
    @Autowired
    private IMobLeanCostingService mcostSrv;
    @Autowired
    private IJobGeneraterService jobGeneraterService;
    @Autowired
    private IUnitMethodService unitMethodService;

    @Autowired
    private QualityIndexComputeExector qualityIndexComputeExector;
    @Autowired
    private EntityService dao;

    @Autowired
    private ISysEmployeeInfoService sysEmployeeInfoService;

    /**
     * @category 获取活动相关表单及数据表数据
     */
    @Override
    public List<AccountAppVo> getAccountManageList(AccountParam dto) {
        List<AccountAppVo> result = new ArrayList<AccountAppVo>();
        String activeId = dto.getActiveId();
        // 1. 获取活动相关表单 - 增加参数校验
        if (StringUtils.isEmpty(activeId)) {
            log.warn("查询台账列表失败：活动ID为空");
            return Collections.emptyList();
        }
        // 2. 获取活动表单列表
        List<DigitalLedger> activeFormList = getActiveFormList(activeId, null);
        if (StringUtils.isEmpty(activeFormList)) {
            log.info("活动[{}]未关联任何表单", activeId);
            return result;
        }
        Map<String, DigitalLedger> formMap = activeFormList.stream()
                .collect(Collectors.toMap(DigitalLedger::getFormId, Function.identity()));
        // 3. 获取表单对应的台账模型信息
        List<DigitalLedger> ledgerModeList = getLedgerModeList(activeFormList);
        if (StringUtils.isEmpty(ledgerModeList)) {
            log.info("活动[{}]关联的表单未绑定台账模型", activeId);
            return result;
        }
        //按照表单分组
        Map<String, List<DigitalLedger>> ledgerFormGroup = ledgerModeList.stream()
                .collect(Collectors.groupingBy(DigitalLedger::getFormId));
        // 4. 获取台账模型ID列表
        List<String> ledgerModuleIds = ledgerModeList.stream()
                .map(DigitalLedger::getLedgerModuleId)
                .distinct()
                .collect(Collectors.toList());
        // 根据 台账模型ID 获取台账模型的详细信息
        List<DigitalLedgerModule> ledgerList = getLedgerList(ledgerModuleIds);
        // 创建模型ID到模型对象的映射表 (lmap)，便于后续快速查找
        Map<String, DigitalLedgerModule> lmap = new HashMap<String, DigitalLedgerModule>();
        for (DigitalLedgerModule obj : ledgerList) {
            String id = obj.getId();
            lmap.put(id, obj);
        }
        // 根据台账模型表获取相关模型配置，根据对应核算对象获取采集点/包括数据总量、已填写量、超限数量等统计指标
        Map<String, Map<String, Integer>> legCountMap = getLedgerData(ledgerList, dto);
        // 循环表单，获取数据表格，并封装返回数据
        Map<String, AccountAppVo> rmap = new LinkedHashMap<String, AccountAppVo>();
        // 循环处理每个台账模型，将其加入对应表单的数据结构中
        String sql = "select id,name from sf_form";
        List<LinkedHashMap<String, Object>> fromList = dao.query(sql, null);
        Map<String, String> formNameMap = Optional.ofNullable(fromList).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors
                        .toMap(item -> String.valueOf(item.get("id")),
                                item -> String.valueOf(item.get("name"))));
        for (Map.Entry<String, List<DigitalLedger>> formEntry : ledgerFormGroup.entrySet()) {
            //取得表单信息
            DigitalLedger form = formMap.get(formEntry.getKey());
            //对应台账模型列表
            List<DigitalLedger> ledgers = formEntry.getValue();
            if (StringUtils.isEmpty(ledgers)) {
                continue;
            }
            //封装表单信息
            JSONArray formAllComponents = formService.getFormComponentJsonArray(form.getFormId(), "tdsEditTable");
            if (formAllComponents == null || formAllComponents.isEmpty()) {
                log.warn("未找到表单组件信息！");
                return result;
            }
            Map<String, JSONObject> comMap = formAllComponents.stream()
                    .map(item -> (JSONObject) item).collect(Collectors.toMap(item -> item.getString("id"), item -> item));
            AccountAppVo obj = new AccountAppVo();
            obj.setActiveId(activeId);
            obj.setFormId(form.getFormId());
            obj.setFormName(formNameMap.containsKey(form.getFormId())?formNameMap.get(form.getFormId())
                    : form.getFormName());
            List<AccountAppVo> tlist = new ArrayList<AccountAppVo>();
            //封装台账模型信息
            ledgers.forEach(ledger -> {
                //获取详细的台账信息
                DigitalLedgerModule digitalLedgerModule = lmap.get(ledger.getLedgerModuleId());
                AccountAppVo accountAppVo = this.setLedgerObj(obj, digitalLedgerModule, ledger, legCountMap);
                //查询数据源信息
                JSONObject com = comMap.get(ledger.getComponentId());
                String optionStr = com.getString("options");
                JSONObject options = JSONObject.parseObject(optionStr);
                String tdsAlias = options.getString("tdsAlias");
                accountAppVo.setTdsAlias(tdsAlias);
                tlist.add(accountAppVo);
            });
            obj.setLedgerList(tlist);
            result.add(obj);
        }
        return result;
    }

    // 根据formId表单获取相关Ledger台账模型的配置
    private List<DigitalLedger> getLedgerModeList(List<DigitalLedger> activeFormList) {
        //根据表单获取台账模型信息
        if (StringUtils.isEmpty(activeFormList)) {
            return Collections.emptyList(); // 返回空集合而非null
        }
        List<String> formIdList = activeFormList.stream()
                .map(DigitalLedger::getFormId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (formIdList.isEmpty()) {
            return Collections.emptyList();
        }

        try {
            Where where = Where.create();
            where.eq(DigitalLedger::getTmused, 1);
            where.in(DigitalLedger::getFormId, formIdList.toArray());
            Order order = Order.create().orderByAsc(DigitalLedger::getTmsort);
            return srv.queryData(DigitalLedger.class, where, order, null);
        } catch (Exception e) {
            log.error("查询台账模型信息失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    // 封装台账模型对象到输入对象
    private AccountAppVo setLedgerObj(AccountAppVo obj, DigitalLedgerModule led, DigitalLedger ledger, Map<String, Map<String, Integer>> legCountMap) {
        AccountAppVo vo = new AccountAppVo();
        vo.setFormId(obj.getFormId());
        vo.setFormName(obj.getFormName());
        vo.setLedgerModuleId(led.getId());
        vo.setLedgerName(led.getModuleName());
        vo.setUnitId(led.getAccountObjId());
        vo.setTableId(ledger.getComponentId());
        vo.setLedgerInitType(ledger.getInitType());
        vo.setLedgerComponentId(ledger.getComponentId());

        Map<String, Integer> tmap = legCountMap.get(led.getId());
        if (tmap != null) {
            int rowNum = tmap.get("rowNum");
            int totalNum = tmap.get("total");
            vo.setDataRows(vo.getDataRows() == null ? rowNum : vo.getDataRows() + rowNum);
            vo.setDataTotal(vo.getDataTotal() == null ? totalNum : vo.getDataTotal() + totalNum);
        }
        return vo;
    }

    /**
     * @param ledgerList
     * @param dto
     * @return
     * @category 根据参数及模型列表，获取对应台账的数据统计信息
     * 该方法根据台账模型列表和参数对象，统计每个台账的数据信息，包括行数、总点数、已填写点数和超限点数。
     */
    private Map<String, Map<String, Integer>> getLedgerData(List<DigitalLedgerModule> ledgerList, AccountParam dto) {
        Map<String, Map<String, Integer>> rmap = new HashMap<>();
        String bc = dto.getShiftCode(); // 班次
        String sbsj = dto.getSbsj(); // 上班时间
        Date sbsjD = DateTimeUtils.parseDateTime(sbsj);
        if (ObjectUtils.isEmpty(ledgerList) || sbsjD == null) {
            return rmap;
        }
        // 循环台账模型，统计结果
        for (DigitalLedgerModule obj : ledgerList) {
            // 查数据，先获取核算对象，再获取采集点，核对数据
            String ucode = obj.getAccountObjId(); // 核算对象ID
            if (StringUtils.isEmpty(ucode)) {
                log.warn("台账模型ID：{}，核算对象ID为空", obj.getId());
                continue;
            }
            //获取全部的采集点
            List<Costunitsampledot> allSampledotListByUnitid = unitMethodService.getAllSampledotListByUnitid(ucode, null);
            // 核算对象ID集合
            List<String> nlist = new ArrayList<>();
            // 获取每个核算对象的设备ID列表
            List<Costuint> deviceIds = iCostunitService.getDeviceIds(ucode);
            if (ObjectUtils.isEmpty(deviceIds)) {
                nlist.add(ucode);
            } else {
                for (Costuint cost : deviceIds) {
                    nlist.add(cost.getId());
                }
            }
            if (nlist.isEmpty()) {
                log.warn("台账模型ID：{}，未找到设备ID", obj.getId());
                continue;
            }
            // 使用参数化查询构建WHERE条件，避免SQL注入
            StringBuffer whereUnit = new StringBuffer();
            whereUnit.append("in(");
            for (int i = 0; i < nlist.size(); i++) {
                if (i > 0) {
                    whereUnit.append(",");
                }
                whereUnit.append("'").append(nlist.get(i)).append("'");
            }
            whereUnit.append(")");
            List<Object> param = new ArrayList<>();
            String sql = "select" +
                    " INPUT_TIME,job_input_time "
                    +
                    "from ACCTOBJ_INPUTMX where TMUSED=1 and exists (select ID from ACCTOBJ_INPUT where ACCTOBJ_ID "
                    + whereUnit.toString()
                    + " and bcdm=? and sbsj=? and ID=ACCTOBJ_INPUTMX.IPT_ID) order by COLLECT_POINT_ID, INPUT_TIME";
            param.add(bc);
            param.add(sbsjD);
            List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
            Map<String, Integer> emptyMap = new HashMap<>();
            //需要改成job_input_time
            long rowNum =
                    Optional.ofNullable(list).orElse(new ArrayList<>())
                            .stream()
                            .filter(item-> item.get("job_input_time")!=null &&
                                    Objects.equals(String.valueOf(item.get("job_input_time")).substring(0,19),dto.getRq()))
                            .map(item -> String.valueOf(item.get("input_time"))
                                    )
                            .distinct()
                            .count();
            emptyMap.put("rowNum", (int) rowNum);
            emptyMap.put("total", allSampledotListByUnitid.size());
            rmap.put(obj.getId(), emptyMap);
        }
        return rmap;
    }

    // 结果处理
    private int judgeType(Object vobj, Costunitsampledot dotObj) {
        int rv = 0;
        if (vobj == null) {
        } else {
            String vstr = String.valueOf(vobj);
            if (StringUtils.isNotEmpty(vstr)) {
                if (Coms.judgeDouble(vstr)) {
                    double dv = Double.parseDouble(vstr);
                    rv = judgeDotUpDown(dv, dotObj);
                } else {// 非数值，不判断超限情况
                    rv = 2;
                }
            }
        }
        return rv;
    }

    // 判断采集点上下限
    private int judgeDotUpDown(double val, Costunitsampledot dotObj) {
        int rv = 0;
        Double low = dotObj.getIndexRangeLower();
        Double up = dotObj.getIndexRangeUpper();
        if (low == null && up == null) {
            // 04.18 修改
            rv = 3; // 有值 没有范围 按照 正常算
        } else if (low == null && up != null) {
            if (val > up) {
                rv = -1;
            } else {
                rv = 1;
            }
        } else if (low != null && up == null) {
            if (val < low) {
                rv = -1;
            } else {
                rv = 1;
            }
        } else {// 都有
            if (val < low || val > up) {
                rv = -1;
            } else {
                rv = 1;
            }
        }

        return rv;
    }

    private List<DigitalLedgerModule> getLedgerList(List<String> moduleIdList) {
        if (StringUtils.isEmpty(moduleIdList)) {
            return Collections.emptyList();
        }

        try {
            Where where = Where.create().in(DigitalLedgerModule::getId, moduleIdList.toArray());
            return srv.queryData(DigitalLedgerModule.class, where, null, null);
        } catch (Exception e) {
            log.error("查询台账模型详情失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取表格行数据
     *
     * @param params
     * @return
     */
    @Override
    public AccountAppVo getAccountRowList(JSONObject params) {
        AccountAppVo result = new AccountAppVo();
        if (params == null) {
            return result;
        }
        //外委人员
        String userId = SysUserHolder.getCurrentUser().getId();
        List<EmployeeVo> userPartTimePost = sysEmployeeInfoService.getUserPartTimePost(userId);
        String orgId = SysUserHolder.getCurrentUser().getOrgId();
        String orgName = SysUserHolder.getCurrentUser().getOrgName();
        String postId = SysUserHolder.getCurrentUser().getPostId();
        String postName = SysUserHolder.getCurrentUser().getPostName();
        if (StringUtils.isNotEmpty(userPartTimePost)) {
            EmployeeVo partTimePost = userPartTimePost.stream().filter(item -> item.getOrgcode().equals(orgId)).findFirst().orElse(null);
            if (partTimePost != null) {
                params.put("curorgcode", partTimePost.getOrgcode());
                params.put("curorgname",partTimePost.getOrgname());
                params.put("curpostId",partTimePost.getPostid());
                params.put("curpostName",partTimePost.getPostname());
            }else{
                params.put("curorgcode", orgId);
                params.put("curorgname", orgName);
                params.put("curpostId", postId);
                params.put("postName", postName);
            }
        }else{
            params.put("curorgcode", orgId);
            params.put("curorgname", orgName);
            params.put("curpostId", postId);
            params.put("postName", postName);
        }

        TdsQueryDto tdsParam = new TdsQueryDto();
        tdsParam.setIsInitData(false);
        tdsParam.setShowRenderValue(false);
        tdsParam.setErrInfo(false);
        // 设置查询参数
        tdsParam.setTdsAlias(params.getString("tdsAlias"));
        String initType = params.getString("ledgerInitType");
        //构建输入参数
        StringBuffer buffer = new StringBuffer();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            buffer.append(entry.getKey());
            buffer.append("=");
            buffer.append(entry.getValue());
            buffer.append("|");
        }
        tdsParam.setInParaAlias(buffer.toString());
        JSONArray tdsDatas = tdsServ.getTDSData(tdsParam);
        result.setTdsDatas(tdsDatas);
        if (tdsDatas == null || tdsDatas.isEmpty()) {
            result.setShowTime(false);
        } else {
            // 处理查询结果
            JSONObject firstItem = tdsDatas.getJSONObject(0);
            if (firstItem.containsKey("props")) {
                JSONObject props = firstItem.getJSONObject("props");
                if (props.containsKey("colprop")) {
                    JSONObject colProp = props.getJSONObject("colprop");
                    if (colProp.containsKey("timeMarkCol")) {
                        JSONObject timeMarkCol = colProp.getJSONObject("timeMarkCol");
                        if (timeMarkCol.isEmpty()) {
                            result.setShowTime(false);
                        } else {
                            result.setShowTime(true);
                        }

                    }
                }
            }
        }
        DigitalLedgerModule obj = srv.queryObjectById(DigitalLedgerModule.class, params.getString("ledgerModuleId"));
        if (obj == null) {
            return result;
        }
        // 查数据，先获取核算对象，再获取采集点，核对数据
        String ucode = obj.getAccountObjId(); // 核算对象ID
        // 获取设备ID列表
        List<String> nlist = new ArrayList<>();
        List<Costuint> deviceIds = iCostunitService.getDeviceIds(ucode);
        if (ObjectUtils.isEmpty(deviceIds)) {
            nlist.add(ucode);
        } else {
            for (Costuint cost : deviceIds) {
                if (cost != null && cost.getId() != null) {
                    nlist.add(cost.getId());
                }
            }
        }
        if (nlist.isEmpty()) {
            log.warn("获取表格行数据失败：未找到有效的设备ID, ucode={}", ucode);
            return result;
        }

        // 构建SQL查询条件，使用参数化查询避免SQL注入
        List<String> quotedIds = new ArrayList<>();
        for (String id : nlist) {
            quotedIds.add("'" + id + "'");
        }
        String whereUnit = "in(" + String.join(",", quotedIds) + ")";

        // 获取所有采集点
        List<String> tagIds = new ArrayList<>();
        List<Costunitsampledot> dotList = new ArrayList<>();
        for (String unitCode : nlist) {
            // 使用核算方法获取当前采集点
            List<SampleDotByClassVo> tlist = unitItemInfoService.getSampleDotByInputRightFromClass(unitCode,
                    params.getString("sbsj"));
            if (StringUtils.isNotEmpty(tlist)) {
                for (SampleDotByClassVo vo : tlist) {
                    List<Costunitsampledot> dlist = vo.getDotList();
                    if (StringUtils.isNotEmpty(dlist)) {
                        for (Costunitsampledot costunitsampledot : dlist) {
                            costunitsampledot.setPname(vo.getClassName());
                        }
                        dotList.addAll(dlist);
                    }
                }
            }

            // 因录入时使用版本方法，这里加入版本控制
            List<TdsAccountMeter> taglist = accountServ.getDefaultAccountUnitTagList(unitCode, params.getString("rq"));
            if (taglist != null) {
                List<String> tids = taglist.stream()
                        .map(TdsAccountMeter::getTagid)
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());
                tagIds.addAll(tids);
            }
        }

        if (dotList.isEmpty() || tagIds.isEmpty()) {
            log.warn("获取表格行数据失败：未找到采集点或标签ID, ucode={}", ucode);
            return result;
        }

        // 使用LinkedHashMap避免重复key的问题
        Map<String, Costunitsampledot> dotMap = new LinkedHashMap<>();
        for (Costunitsampledot dot : dotList) {
            if (dot != null && dot.getId() != null && tagIds.contains(dot.getId())) {
                dotMap.put(dot.getId(), dot);
            }
        }

        if (dotMap.isEmpty()) {
            log.warn("获取表格行数据失败：过滤后采集点为空, ucode={}", ucode);
            return result;
        }

        // 整理采集点信息进行封装，用于添加行处理
        List<AccountAppVo> tagInfoList = new ArrayList<>();
        for (Costunitsampledot dot : dotMap.values()) {
            AccountAppVo tag = new AccountAppVo();
            tag.setTagid(dot.getId());
            tag.setTagpid(dot.getPid());
            tag.setTagPname(dot.getPname());
            tag.setEditMark(dot.getIsWriteInput());
            tag.setTagname(dot.getName());
            tag.setTagnumber(dot.getTagnumber());
            tag.setSdunit(dot.getSdUnit());
            tag.setUpLimit(dot.getIndexRangeUpper());
            tag.setLowLimit(dot.getIndexRangeLower());
            tag.setCombinitkey(dot.getCombInitKey());
            tag.setCombinitval(dot.getCombInitVal());
            tag.setControltype(dot.getControlType());
            tag.setDatasource(dot.getDatasource());
            tag.setIsWriteBackInfluxdb(dot.getIsWriteBackInfluxdb());
            tag.setTmsort(dot.getTmsort());
            tagInfoList.add(tag);
        }
        result.setTagInfoList(tagInfoList);

        // 获取已有数据
        Date sbsjD = DateTimeUtils.parseDateTime(params.getString("sbsj"));

        if (sbsjD == null) {
            return result;
        }
        // 台账数据渲染其余配置
        Map<String,JSONObject> renderConfMap = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        if (tdsDatas != null && !tdsDatas.isEmpty()) {
            JSONObject firstItem = tdsDatas.getJSONObject(0);
            if (firstItem.containsKey("data")) {
                JSONArray data = firstItem.getJSONArray("data");
                // 遍历每个时间点的数据
                for (int i = 0; i < data.size(); i++) {
                    JSONObject timeData = data.getJSONObject(i);
                    String accountULInfoCol_str = timeData.getString("_accountULInfoCol");
                    JSONObject confData = JSONObject.parseObject(accountULInfoCol_str);
                    String id = timeData.getString("ID");
                    renderConfMap.put(id,confData);
                    if(!"expand".equals(initType)) {
                        String timeId = timeData.getString("_jobInputTime");
                        //临时是使用id列之后换成job_input_time
                        if (StringUtils.isEmpty(timeId)) {
                            continue;
                        }
                        if (!Objects.equals(timeId.substring(0, 19), params.getString("jobInputTime"))) {
                            continue;
                        }
                    }
                    // 处理每个时间点下的所有采集点数据
                    for (Map.Entry<String, Object> entry : timeData.entrySet()) {
                        String key = entry.getKey();
                        Object value = entry.getValue();

                        // 排除非采集点数据项
                        if (!key.equals("rowConfirm") &&
                                !key.equals("ID") &&
                                !key.equals("timeMarkCol") &&
                                !key.startsWith("_")) {
                            Map<String, Object> pointData = new LinkedHashMap<>();
                            pointData.put("INPUT_TIME", id);
                            pointData.put("COLLECT_POINT_ID", key);
                            pointData.put("COLLECT_POINT_VAL", value);
                            list.add(pointData);
                        }
                    }
                }
            }
        }
        // 按时间分组数据
        Map<String, Map<String, String>> timeMap = new LinkedHashMap<>();
        Map<String, String> inputTypeMap = new LinkedHashMap<>();
        for (Map<String, Object> map : list) {
            String sj = map.get("INPUT_TIME") == null ? "" : String.valueOf(map.get("INPUT_TIME"));
            if (StringUtils.isEmpty(sj)) {
                continue;
            }

            String dotid = map.get("COLLECT_POINT_ID") == null ? "" : String.valueOf(map.get("COLLECT_POINT_ID"));
            if (StringUtils.isEmpty(dotid)) {
                continue;
            }

            String val = map.get("COLLECT_POINT_VAL") == null ? null : String.valueOf(map.get("COLLECT_POINT_VAL"));

            if (!timeMap.containsKey(sj)) {
                timeMap.put(sj, new LinkedHashMap<>());
            }
            timeMap.get(sj).put(dotid, val);

            String inputType = map.get("INPUT_COMP_TYPE") == null ? "" : String.valueOf(map.get("INPUT_COMP_TYPE"));
            inputTypeMap.put(dotid, inputType);
        }
        //根据当前时间决定生成到哪个时间点
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("ledgerModuleId", params.getString("ledgerMoudleId"));
        paramMap.put("bc", params.getString("shiftId") + "," + params.getString("sbsj") + "," + params.getString("xbsj"));
        List<Date> timeList;
        List<DigitalLedgerTime> initTimeList = configService.getInitTimeList(paramMap);
        String result1 = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StringUtils.isNotEmpty(initTimeList)) {
            Date now = new Date();
            timeList = initTimeList
                    .stream()
                    .map(DigitalLedgerTime::getStartTime)
                    .sorted(Comparator.comparing(Date::getTime))
                    .collect(Collectors.toList());
            for (int i = 0; i < timeList.size(); i++) {
                Date date = timeList.get(i);
                if (date.getTime() > now.getTime()) {
                    if (i == 0) {
                        result1 = sdf.format(timeList.get(0));
                    } else {
                        result1 = sdf.format(timeList.get(i - 1));
                    }
                    break;
                } else if (Objects.equals(date.getTime(), now.getTime())) {
                    result1 = sdf.format(timeList.get(i));
                    break;
                }
            }
        }
        // 生成行数据
        int i = 1;
        List<AccountAppVo> rlist = new ArrayList<>();
        for (Map.Entry<String, Map<String, String>> entry : timeMap.entrySet()) {
            String sj = entry.getKey();
//            if (StringUtils.isNotEmpty(result1)) {
//                if (result1.compareTo(sj.substring(0, 19)) < 0) {
//                    continue;
//                }
//            }
            Map<String, String> tagmap = entry.getValue();

            AccountAppVo vo = new AccountAppVo();
            vo.setRowNo(i++);
            vo.setRowName("数据行");
            vo.setSjstr(sj);
            List<AccountAppVo> tagList = new ArrayList<>();
            vo.setTagList(tagList);

            for (Map.Entry<String, String> tagEntry : tagmap.entrySet()) {
                String tagId = tagEntry.getKey();
                String vobj = tagEntry.getValue();
                Costunitsampledot dot = dotMap.get(tagId);

                // 确保dot不为空，避免空指针异常
                if (dot == null) {
                    log.warn("采集点为空，跳过处理: tagId={}", tagId);
                    continue;
                }

                AccountAppVo tag = new AccountAppVo();
                tag.setTagid(dot.getId());
                tag.setTagpid(dot.getPid());
                tag.setTagPname(dot.getPname());
                tag.setEditMark(dot.getIsWriteInput());
                tag.setTagname(dot.getName());
                tag.setTagnumber(dot.getTagnumber());
                tag.setSdunit(dot.getSdUnit());
                tag.setUpLimit(dot.getIndexRangeUpper());
                tag.setLowLimit(dot.getIndexRangeLower());
                tag.setCombinitkey(dot.getCombInitKey());
                tag.setCombinitval(dot.getCombInitVal());
                tag.setControltype(dot.getControlType());
                tag.setDatasource(dot.getDatasource());
                tag.setIsWriteBackInfluxdb(dot.getIsWriteBackInfluxdb());
                tag.setTmsort(dot.getTmsort());
                JSONObject renderData = renderConfMap.get(sj);
                if(renderData!=null){
                    JSONObject data = renderData.getJSONObject("data");
                    if(data!=null){
                        JSONObject confData = data.getJSONObject(dot.getId());
                        if(confData!=null){
                            tag.setCollectPointText(confData.getString("txt"));
                        }
                    }
                }
                tag.setVal(vobj);
                tag.setInputOptions(inputTypeMap.get(tagId) == null ? "" : inputTypeMap.get(tagId));
                tagList.add(tag);
            }

            rlist.add(vo);
        }
        List<AccountAppVo> tagList = new ArrayList<>();
        if (StringUtils.isNotEmpty(rlist)) {
            for (AccountAppVo accountAppVo : rlist) {
                if (StringUtils.isNotEmpty(accountAppVo.getTagList())) {
                    tagList = accountAppVo.getTagList().stream().sorted(Comparator.comparing(AccountAppVo::getTmsort, Comparator.nullsFirst(Integer::compareTo)))
                            .collect(Collectors.toList());
                    accountAppVo.setTagList(tagList);
                }
            }
            result.setRowList(rlist);
        }
        return result;
    }

    /*
     * 获取活动表单列表
     */
    private List<DigitalLedger> getActiveFormList(String activeId, String ledgerId) {
        //根据活动获得表单列表
        List<DigitalLedger> list = new ArrayList<DigitalLedger>();
        if (StringUtils.isNotEmpty(ledgerId)) {
            DigitalLedgerModule ledger = srv.queryObjectById(DigitalLedgerModule.class, ledgerId);
            DigitalLedger obj = new DigitalLedger();
            obj.setFormName(ledger.getModuleName());

            list.add(obj);
        } else {
            Where where = Where.create();
            where.eq(JoblistPersonBind::getTmused, 1);
            where.eq(JoblistPersonBind::getPid, activeId);
            where.eq(JoblistPersonBind::getBindtype, 4);
            List<JoblistPersonBind> tlist = srv.queryData(JoblistPersonBind.class, where, null, null);
            if (StringUtils.isNotEmpty(tlist)) {
                for (JoblistPersonBind job : tlist) {
                    DigitalLedger obj = new DigitalLedger();
                    obj.setFormId(job.getBindid());
                    obj.setFormName(job.getBindname());
                    list.add(obj);
                }

            }
        }
        return list;
    }

    @Override
    public AccountParam saveAccountRowList(JSONObject saveData) {
        AccountParam dto = new AccountParam();
        if(saveData==null || saveData.isEmpty()){
            dto.setOpResult(false);
            return dto;
        }
        //解析台账数据参数
        JSONArray dataList = saveData.getJSONArray("dataList");
        if(dataList==null || dataList.isEmpty()){
            dto.setOpResult(false);
            return dto;
        }
        JSONObject param = saveData.getJSONObject("params");
        //班次代码
        String bc = param.getString("shiftId");
        String activeId = param.getString("activityId");
        //日期
        String rq = param.getString("rq");
        //上班时间
        String sbsj = param.getString("sbsj");
        //下班时间
        String xbsj = param.getString("xbsj");
        //台账模型ID
        String ledgerId = param.getString("ledgerModuleId");
        String taskId_new = param.getString("exampleId");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String activityDate = param.getString("activityDate");
        Date _jobInputTime = null;
        try {
            _jobInputTime = sdf.parse(activityDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        if (StringUtils.isEmpty(ledgerId) || StringUtils.isEmpty(sbsj) || StringUtils.isEmpty(xbsj)
                || StringUtils.isEmpty(bc)) {
            log.warn("保存台账数据失败：参数缺失, ledgerId={}, bc={}, sbsj={}, xbsj={}", ledgerId, bc, sbsj, xbsj);
            dto.setOpResult(false);
            return dto;
        }

        // 日期参数处理
        if (StringUtils.isEmpty(rq) && sbsj != null && sbsj.length() >= 10) {
            rq = sbsj.substring(0, 10);
            dto.setRq(rq);
        }

        // 获取台账模型信息
        DigitalLedgerModule mod = srv.queryObjectById(DigitalLedgerModule.class, ledgerId);
        if (mod == null) {
            log.warn("保存台账数据失败：未找到台账模型信息, ledgerId={}", ledgerId);
            dto.setOpResult(false);
            return dto;
        }

        // 台账模型的核算对象ID
        String unitCode = mod.getAccountObjId();
        if (StringUtils.isEmpty(unitCode)) {
            log.warn("保存台账数据失败：核算对象ID为空, ledgerId={}", ledgerId);
            dto.setOpResult(false);
            return dto;
        }

        List<String> unitIdList = new ArrayList<String>();
        List<Costuint> deviceIds = iCostunitService.getDeviceIds(unitCode);
        if (StringUtils.isEmpty(deviceIds)) {
            unitIdList.add(unitCode);
        } else {
            for (Costuint unit : deviceIds) {
                unitIdList.add(unit.getId());
            }
        }

        if (unitIdList.isEmpty()) {
            log.warn("保存台账数据失败：未找到有效的设备ID, unitCode={}", unitCode);
            dto.setOpResult(false);
            return dto;
        }

        // 查询核算对象名称和班次名称
        String unitName = getUnitName(unitCode);
        String shiftName = getShiftName(bc);

        // 获取活动和时间的id
        Map<Date, String> infoTimeIdMap = getActivityExampleTimeIdMap(bc, xbsj, sbsj, activeId);

        //解析行数据
        List<AccountAppVo> rowList = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            AccountAppVo javaObject = dataList.getJSONObject(i).toJavaObject(AccountAppVo.class);
            rowList.add(javaObject);
        }
        // 处理行数据
        if (ObjectUtils.isEmpty(rowList)) {
            log.warn("保存台账数据失败：行数据为空");
            dto.setOpResult(false);
            return dto;
        }
        //列表中的tagList是基本相同的 从中获取父仪表信息
        Map<String, String> p_tagIdNameMap = new HashMap<String, String>();
        List<AccountAppVo> ttagList = rowList.get(0).getTagList();
        if (StringUtils.isNotEmpty(ttagList)) {
            AccountAppVo tagClass = ttagList.get(0);
            if (StringUtils.isNotEmpty(tagClass.getTagpid())) {
                String id = tagClass.getTagpid();
                String name = tagClass.getTagPname();
                p_tagIdNameMap.put(id, name);
            }
        }
        List<AcctobjInputVo> unitInputList = new ArrayList<AcctobjInputVo>();//相当于行数据（时间点）
        SysUser sysUser = SysUserHolder.getCurrentUser();
        //遍历保存列表
        for (AccountAppVo obj : rowList) {
            //此保存列表应该属于一个活动所以直接使用活动的id
            Integer flag = obj.getRowFlag();
            //主键时间
            Date sjdate = StringUtils.isEmpty(obj.getSjstr()) ? null : DateTimeUtils.parseDateTime(obj.getSjstr());
            //本行记录的采集点数据
            List<AccountAppVo> tagList = obj.getTagList();
            //构建主数据
            AcctobjInputVo vo = new AcctobjInputVo();//行数据对象
            vo.setRowFlag(flag);
            vo.setJobInputTime(_jobInputTime);
            vo.setInputTime(sjdate);
            vo.setBaId(unitCode);
            vo.setBaName(unitName);
            vo.setBcdm(bc);
            vo.setBcmc(shiftName);
            vo.setSbsj(DateTimeUtils.parseDateTime(sbsj));
            vo.setXbsj(DateTimeUtils.parseDateTime(xbsj));
            vo.setTaskId(taskId_new);
            vo.setTmused(1);
            //外委人员
            String userId = sysUser.getId();
            List<EmployeeVo> userPartTimePost = sysEmployeeInfoService.getUserPartTimePost(userId);
            String orgId = sysUser.getOrgId();
            if (StringUtils.isNotEmpty(userPartTimePost)) {
                EmployeeVo partTimePost = userPartTimePost.stream().filter(item -> item.getOrgcode().equals(orgId)).findFirst().orElse(null);
                if (partTimePost != null) {
                    vo.setTeamId(partTimePost.getOrgcode());
                }else{
                    vo.setTeamId(orgId);
                }
            }else{
                vo.setTeamId(orgId);
            }
            List<AcctobjInputFlVo> flList = new ArrayList<>();//按分类汇总数据
            vo.setFlData(flList);

            Map<String, AcctobjInputVo> inputUnitMap = new LinkedHashMap<>();
            Map<String, AcctobjInputFlVo> flMap = new LinkedHashMap<>();
            //遍历采集点信息
            for (AccountAppVo tag : tagList) {
                String col = tag.getTagid();
                String tagpid = tag.getTagpid();

                String comType = null, txt = null, ops = null;
                int icom = -1;
                Integer tic = tag.getControltype();
                if (tic != null) {
                    icom = tic;
                }
                Object ct = ControlTypeConverter.convertToString(icom);
                String ks = tag.getCombinitkey();
                String vs = tag.getCombinitval();

                comType = ct == null ? null : String.valueOf(ct);
                if (StringUtils.isNotEmpty(ks) && StringUtils.isNotEmpty(vs)) {
                    //[{"text":"选项1","value":"选项1"},{"text":"选项2","value":"选项2"},{"text":"选项3","value":"选项3"}]
                    List<String> klist = Coms.StrToList(ks, ",");
                    List<String> vlist = Coms.StrToList(vs, ",");
                    StringBuffer sb = new StringBuffer();
                    for (int j = 0; j < klist.size(); j++) {
                        String key = klist.get(j).replace("\"", "\\\"");
                        String val = vlist.get(j).replace("\"", "\\\"");
                        sb.append(",{\"text\":\"");
                        sb.append(val);
                        sb.append("\",\"value\":\"");
                        sb.append(key);
                        sb.append("\"}");

                    }
                    if (sb.length() > 0) {
                        ops = "[" + sb.substring(1) + "]";
                    }
                }


                if (inputUnitMap.containsKey(unitCode)) {
                    if (flMap.containsKey(tagpid)) {
                        AcctobjInputFlVo flvo = flMap.get(tagpid);
                        AcctobjInputmxVo mx = new AcctobjInputmxVo();
                        mx.setCollectPoint(tag.getTagnumber());
                        mx.setCollectPointId(col);
                        // 04.09 修改
//							mx.setCollectPointVal(tag.getTagname());
                        mx.setCollectPointVal(tag.getVal());

                        mx.setTagNo(tag.getDatasource());
                        mx.setSn(tag.getTmsort());
                        mx.setInputCompType(comType);
                        mx.setCollectPointText(tag.getCollectPointText());
                        mx.setInputOptions(ops);
                        mx.setTmused(1);
                        mx.setIsWriteBackInfluxdb(tag.getIsWriteBackInfluxdb());
                        flvo.getMxData().add(mx);
                    } else {
                        AcctobjInputVo uobj = inputUnitMap.get(unitCode);

                        AcctobjInputFlVo flvo = ObjUtils.copyTo(uobj, AcctobjInputFlVo.class);
                        flvo.setFlId(tagpid);
                        flvo.setFlName(tag.getTagname());
                        List<AcctobjInputmxVo> mxData = new ArrayList<AcctobjInputmxVo>();
                        flvo.setMxData(mxData);

                        AcctobjInputmxVo mx = new AcctobjInputmxVo();
                        mx.setCollectPoint(tag.getTagnumber());
                        mx.setCollectPointId(col);
                        mx.setCollectPointVal(tag.getVal());
                        mx.setTagNo(tag.getDatasource());
                        mx.setSn(tag.getTmsort());
                        mx.setInputCompType(comType);
                        mx.setCollectPointText(txt);
                        mx.setInputOptions(ops);
                        mx.setTmused(1);
                        mx.setIsWriteBackInfluxdb(tag.getIsWriteBackInfluxdb());
                        mxData.add(mx);
                        flMap.put(tagpid, flvo);
                        uobj.getFlData().add(flvo);
                    }
                } else {
                    AcctobjInputVo inputUnitObj = ObjUtils.copyTo(vo, AcctobjInputVo.class);
                    String newAccobjId = unitCode;
                    for (String str : unitIdList) {
                        if (str.indexOf(unitCode) != -1) {
                            newAccobjId = str;
                            break;
                        }
                    }
                     inputUnitObj.setAcctobjId(newAccobjId);
                     inputUnitObj.setAcctobjName(tag.getUnitName());
                    //构建分类数据  使用父仪表信息
                    AcctobjInputFlVo flvo = ObjUtils.copyTo( inputUnitObj, AcctobjInputFlVo.class);
                    flvo.setFlId(tagpid);
                    flvo.setFlName(p_tagIdNameMap.get(tagpid));//未获取采集点父名称（可能是分类）
                    List<AcctobjInputmxVo> mxData = new ArrayList<AcctobjInputmxVo>();
                    flvo.setMxData(mxData);

                    AcctobjInputmxVo mx = new AcctobjInputmxVo();
                    mx.setCollectPoint(tag.getTagnumber());
                    mx.setCollectPointId(col);
                    mx.setCollectPointVal(tag.getVal());
                    mx.setTagNo(tag.getDatasource());
                    mx.setSn(tag.getTmsort());
                    mx.setInputCompType(comType);
                    mx.setCollectPointText(txt);
                    mx.setInputOptions(ops);
                    mx.setTmused(1);
                    mx.setIsWriteBackInfluxdb(tag.getIsWriteBackInfluxdb());
                    mxData.add(mx);

                    flMap.put(tagpid, flvo);

                    inputUnitObj.getFlData().add(flvo);
                    inputUnitMap.put(unitCode,  inputUnitObj);
                }
            }

            for (AcctobjInputVo ai : inputUnitMap.values()) {//多设备情况处理
                unitInputList.add(ai);
            }
        }

//             保存数据
        Boolean saveFlag = false;
        if (!unitInputList.isEmpty()) {
            saveFlag = syncDataToAccount(unitInputList);
            //计算质量指标
            QualityIndexComputeParamsDto jobDto = new QualityIndexComputeParamsDto();
            jobDto.setObjectId(taskId_new);
            jobDto.setRq(rq);
            jobDto.setOrgCode(param.getString("orgCode"));
            jobDto.setShiftCode(bc);
            jobDto.setSbsj(sbsj);
            jobDto.setXbsj(xbsj);
            jobDto.setIndexflag("activity");
            qualityIndexComputeExector.activityCompute(jobDto);
        } else {
            log.warn("保存台账数据失败：处理后的数据为空");
        }

        dto.setOpResult(saveFlag);
        return dto;
    }

//	/**
//	 * 保存台账表格行数据
//	 * @param dto
//	 * @return
//	 */
//	@Override
//	public AccountParam saveAccountRowList(AccountParam dto) {
//		try {
//			// 参数校验
//			String bc = dto.getShiftCode(); // 班次
//			String rq = dto.getRq(); // 日期
//			String sbsj = dto.getSbsj(); // 上班时间
//			String xbsj = dto.getXbsj(); // 下班时间
//			String ledgerId = dto.getLedgerId(); // 台账模型ID
//			String activeId = dto.getActiveId();
//
//			if (StringUtils.isEmpty(ledgerId) || StringUtils.isEmpty(sbsj) || StringUtils.isEmpty(xbsj)
//					|| StringUtils.isEmpty(bc)) {
//				log.warn("保存台账数据失败：参数缺失, ledgerId={}, bc={}, sbsj={}, xbsj={}", ledgerId, bc, sbsj, xbsj);
//				dto.setOpResult(false);
//				return dto;
//			}
//
//			// 日期参数处理
//			if (StringUtils.isEmpty(rq) && sbsj != null && sbsj.length() >= 10) {
//				rq = sbsj.substring(0, 10);
//				dto.setRq(rq);
//			}
//
//			// 获取台账模型信息
//			DigitalLedgerModule mod = srv.queryObjectById(DigitalLedgerModule.class, ledgerId);
//			if (mod == null) {
//				log.warn("保存台账数据失败：未找到台账模型信息, ledgerId={}", ledgerId);
//				dto.setOpResult(false);
//				return dto;
//			}
//
//			// 获取核算对象ID
//			String ucode = mod.getAccountObjId();
//			if (StringUtils.isEmpty(ucode)) {
//				log.warn("保存台账数据失败：核算对象ID为空, ledgerId={}", ledgerId);
//				dto.setOpResult(false);
//				return dto;
//			}
//
//			// 获取设备ID列表
//			List<String> nlist = new ArrayList<>();
//			List<Costuint> deviceIds = iCostunitService.getDeviceIds(ucode);
//			if (ObjectUtils.isEmpty(deviceIds)) {
//				nlist.add(ucode);
//			} else {
//				for (Costuint unit : deviceIds) {
//					if (unit != null && unit.getId() != null) {
//						nlist.add(unit.getId());
//					}
//				}
//			}
//
//			if (nlist.isEmpty()) {
//				log.warn("保存台账数据失败：未找到有效的设备ID, ucode={}", ucode);
//				dto.setOpResult(false);
//				return dto;
//			}
//
//			// 查询核算对象名称和班次名称
//			String sendUnitName = getUnitName(ucode);
//			String shiftName = getShiftName(bc);
//
//			// 获取对应实例ID
//			Map<Date, String> infoTimeIdMap = getActivityExampleTimeIdMap(bc, xbsj, sbsj, ucode);
//
//			// 处理行数据
//			List<AccountAppVo> rowList = dto.getRowList();
//			if (ObjectUtils.isEmpty(rowList)) {
//				log.warn("保存台账数据失败：行数据为空");
//				dto.setOpResult(false);
//				return dto;
//			}
//
//			// 获取采集点分类名称映射 04.16 修改
////			Map<String, String> pnameMap = getSampleClassNameMap(rowList);
//			Map<String, String> pnameMap = new HashMap<>();
//				List<AccountAppVo> tagList = rowList.get(0).getTagList();
//				AccountAppVo tag = tagList.get(0);
//				if (StringUtils.isNotEmpty(tag.getTagpid())) {
//					String id = tag.getTagpid();
//					String name = tag.getTagPname();
//					pnameMap.put(id, name);
//			}
//
//
//			// 构建数据保存对象
//			List<AcctobjInputVo> clist = buildInputDataList(rowList, nlist, infoTimeIdMap, ucode, sendUnitName,
//					shiftName, sbsj, xbsj, pnameMap);
//
//			// 保存数据
//			Boolean saveFlag = false;
//			if (!clist.isEmpty()) {
//				saveFlag = syncDataToAccount(clist);
//			} else {
//				log.warn("保存台账数据失败：处理后的数据为空");
//			}
//
//			dto.setOpResult(saveFlag);
//			return dto;
//		} catch (Exception e) {
//			log.error("保存台账数据发生异常: {}", e.getMessage(), e);
//			dto.setOpResult(false);
//			return dto;
//		}
//	}

    /**
     * 获取核算对象名称
     */
    private String getUnitName(String ucode) {
        try {
            String sql = "select name from costuint where id=?";
            List<Object> param = new ArrayList<>();
            param.add(ucode);
            List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
            if (StringUtils.isNotEmpty(list) && list.get(0) != null && list.get(0).get("name") != null) {
                return String.valueOf(list.get(0).get("name"));
            }
        } catch (Exception e) {
            log.warn("获取核算对象名称失败: {}, ucode={}", e.getMessage(), ucode);
        }
        return "";
    }

    /**
     * 获取班次名称
     */
    private String getShiftName(String bc) {
        try {
            String sql = "select SHIFTNAME from SHIFT_MODEL_CLASS where ID=?";
            List<Object> param = new ArrayList<>();
            param.add(bc);
            List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
            if (StringUtils.isNotEmpty(list) && list.get(0) != null && list.get(0).get("SHIFTNAME") != null) {
                return String.valueOf(list.get(0).get("SHIFTNAME"));
            }
        } catch (Exception e) {
            log.warn("获取班次名称失败: {}, bc={}", e.getMessage(), bc);
        }
        return "";
    }

    /**
     * 获取活动实例时间ID映射
     */
    private Map<Date, String> getActivityExampleTimeIdMap(String bc, String xbsj, String sbsj, String activeId) {
        Map<Date, String> infoTimeIdMap = new HashMap<>();
        try {
            // 查不到数据，ACTIVITY_ID 用的是 ucode 核算对象ID 04.15 修改
            String sql = "select ACTIVITY_DATE, FREQUENCY_TYPE, ID, BEGIN_DATE from JOBLIST_ACTIVITYEXAMPLE " +
                    "where SHIFT_CLASS_CODE=? and IS_PARENT=0 and XBSJ<=? and SBSJ>=? and ACTIVITY_ID=?";

            List<Object> param = new ArrayList<>();
            param.add(bc);
            param.add(xbsj);
            param.add(sbsj);
            param.add(activeId);

            List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
            if (StringUtils.isNotEmpty(list)) {
                for (Map<String, Object> map : list) {
                    Object sjobj = null;
                    Object typeobj = map.get("FREQUENCY_TYPE");

                    // 根据类型确定时间字段
                    if (typeobj == null || !"0".equals(String.valueOf(typeobj))) {
                        sjobj = map.get("BEGIN_DATE");
                    } else {
                        sjobj = map.get("ACTIVITY_DATE");
                    }
                    String sj = String.valueOf(sjobj);
                    String id = String.valueOf(map.get("ID"));
                    Date date = DateTimeUtils.parseDateTime(sj);

                    if (date != null) {
                        infoTimeIdMap.put(date, id);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取活动实例时间ID映射失败: {}", e.getMessage());
        }
        return infoTimeIdMap;
    }

    /**
     * 获取采集点分类名称映射
     */
    private Map<String, String> getSampleClassNameMap(List<AccountAppVo> rowList) {
        Map<String, String> pnameMap = new HashMap<>();
        try {
            if (!rowList.isEmpty() && rowList.get(0) != null) {
                List<AccountAppVo> tagList = rowList.get(0).getTagList();
                if (StringUtils.isNotEmpty(tagList)) {
                    // 提取所有的标签父ID
                    List<String> tagpidList = tagList.stream()
                            .filter(tag -> tag != null && StringUtils.isNotEmpty(tag.getTagpid()))
                            .map(AccountAppVo::getTagpid)
                            .distinct()
                            .collect(Collectors.toList());

                    if (!tagpidList.isEmpty()) {
                        // 使用参数化查询避免SQL注入
                        StringBuilder sqlBuilder = new StringBuilder();
                        sqlBuilder.append("select ID, NAME from costunitsampleclass where ID in (");
                        for (int i = 0; i < tagpidList.size(); i++) {
                            if (i > 0) {
                                sqlBuilder.append(",");
                            }
                            sqlBuilder.append("?");
                        }
                        sqlBuilder.append(")");

                        List<Map<String, Object>> list = srv.queryListMap(sqlBuilder.toString(), tagpidList.toArray());
                        if (StringUtils.isNotEmpty(list)) {
                            for (Map<String, Object> map : list) {
                                if (map.get("ID") != null && map.get("NAME") != null) {
                                    String id = String.valueOf(map.get("ID"));
                                    String name = String.valueOf(map.get("NAME"));
                                    pnameMap.put(id, name);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取采集点分类名称映射失败: {}", e.getMessage());
        }
        return pnameMap;
    }

    /**
     * 构建输入数据列表
     */
    private List<AcctobjInputVo> buildInputDataList(List<AccountAppVo> rowList, List<String> nlist,
                                                    Map<Date, String> infoTimeIdMap, String ucode, String sendUnitName, String shiftName,
                                                    String sbsj, String xbsj, Map<String, String> pnameMap) {

        List<AcctobjInputVo> clist = new ArrayList<>();

        for (AccountAppVo obj : rowList) {
            if (obj == null || ObjectUtils.isEmpty(obj.getTagList())) {
                continue;
            }

            Integer flag = obj.getRowFlag();
            Date sjdate = StringUtils.isEmpty(obj.getSjstr()) ? null : DateTimeUtils.parseDateTime(obj.getSjstr());
            String taskId = sjdate != null ? infoTimeIdMap.getOrDefault(sjdate, "") : "";

            List<AccountAppVo> tagList = obj.getTagList();

            // 创建行数据对象
            AcctobjInputVo vo = new AcctobjInputVo();
            vo.setRowFlag(flag);
            vo.setInputTime(sjdate);
            vo.setBaId(ucode);
            vo.setBaName(sendUnitName);
            vo.setBcdm(shiftName);
            vo.setBcmc(shiftName);
            vo.setSbsj(DateTimeUtils.parseDateTime(sbsj));
            vo.setXbsj(DateTimeUtils.parseDateTime(xbsj));
            vo.setTaskId(taskId);
            vo.setTmused(1);
            vo.setFlData(new ArrayList<>());

            // 临时存储映射，避免重复创建对象
            Map<String, AcctobjInputVo> umap = new LinkedHashMap<>();
            Map<String, AcctobjInputFlVo> tempmap = new LinkedHashMap<>();

            // 处理标签数据
            processTagList(tagList, ucode, umap, tempmap, vo, pnameMap, nlist);

            // 添加处理后的结果
            clist.addAll(umap.values());
        }

        return clist;
    }

    /**
     * 处理标签列表
     */
    private void processTagList(List<AccountAppVo> tagList, String ucode, Map<String, AcctobjInputVo> umap,
                                Map<String, AcctobjInputFlVo> tempmap, AcctobjInputVo vo, Map<String, String> pnameMap,
                                List<String> nlist) {

        for (AccountAppVo tag : tagList) {
            if (tag == null || StringUtils.isEmpty(tag.getTagid()) || StringUtils.isEmpty(tag.getTagpid())) {
                continue;
            }

            String col = tag.getTagid();
            String tagpid = tag.getTagpid();

            // 处理控件类型和选项
            String[] typeAndOptions = processControlTypeAndOptions(tag);
            String comType = typeAndOptions[0];
            String ops = typeAndOptions[1];

            if (umap.containsKey(ucode)) {
                // 已存在该核算对象的数据
                if (tempmap.containsKey(tagpid)) {
                    // 已存在该分类的数据，添加采集点明细
                    AcctobjInputFlVo flvo = tempmap.get(tagpid);
                    addCollectPointDetail(flvo, tag, col, comType, ops);
                } else {
                    // 不存在该分类的数据，创建新分类
                    AcctobjInputVo uobj = umap.get(ucode);
                    createNewCategory(uobj, tag, tagpid, col, comType, ops, tempmap);
                }
            } else {
                // 不存在该核算对象的数据，创建新核算对象
                createNewUnitObject(vo, ucode, tag, tagpid, col, comType, ops, umap, tempmap, pnameMap, nlist);
            }
        }
    }

    /**
     * 处理控件类型和选项
     */
    private String[] processControlTypeAndOptions(AccountAppVo tag) {
        String comType = null;
        String ops = null;

        // 处理控件类型
        int icom = -1;
        Integer tic = tag.getControltype();
        if (tic != null) {
            icom = tic;
        }
        Object ct = ControlTypeConverter.convertToString(icom);
        comType = ct == null ? null : String.valueOf(ct);

        // 处理下拉选项
        String ks = tag.getCombinitkey();
        String vs = tag.getCombinitval();
        if (StringUtils.isNotEmpty(ks) && StringUtils.isNotEmpty(vs)) {
            List<String> klist = Coms.StrToList(ks, ",");
            List<String> vlist = Coms.StrToList(vs, ",");

            if (!klist.isEmpty() && !vlist.isEmpty() && klist.size() == vlist.size()) {
                StringBuilder sb = new StringBuilder();
                for (int j = 0; j < klist.size(); j++) {
                    String key = klist.get(j).replace("\"", "\\\"");
                    String val = vlist.get(j).replace("\"", "\\\"");
                    sb.append(",{\"text\":\"");
                    sb.append(val);
                    sb.append("\",\"value\":\"");
                    sb.append(key);
                    sb.append("\"}");
                }

                if (sb.length() > 0) {
                    ops = "[" + sb.substring(1) + "]";
                }
            }
        }

        return new String[]{comType, ops};
    }

    /**
     * 添加采集点明细
     */
    private void addCollectPointDetail(AcctobjInputFlVo flvo, AccountAppVo tag, String col, String comType,
                                       String ops) {
        AcctobjInputmxVo mx = new AcctobjInputmxVo();
        mx.setCollectPoint(tag.getTagnumber());
        mx.setCollectPointId(col);
        mx.setCollectPointVal(tag.getVal());
        mx.setCollectPointText(tag.getVal());
        mx.setTagNo(tag.getDatasource());
        mx.setSn(tag.getTmsort());
        mx.setInputCompType(comType);
        mx.setInputOptions(ops);
        mx.setTmused(1);
        mx.setIsWriteBackInfluxdb(tag.getIsWriteBackInfluxdb());
        flvo.getMxData().add(mx);
    }

    /**
     * 创建新分类
     */
    private void createNewCategory(AcctobjInputVo uobj, AccountAppVo tag, String tagpid, String col,
                                   String comType, String ops, Map<String, AcctobjInputFlVo> tempmap) {

        AcctobjInputFlVo flvo = ObjUtils.copyTo(uobj, AcctobjInputFlVo.class);
        flvo.setFlId(tagpid);
        flvo.setFlName(tag.getTagPname());
        List<AcctobjInputmxVo> mxData = new ArrayList<>();
        flvo.setMxData(mxData);

        AcctobjInputmxVo mx = new AcctobjInputmxVo();
        mx.setCollectPoint(tag.getTagnumber());
        mx.setCollectPointId(col);
        mx.setCollectPointVal(tag.getVal());
        mx.setTagNo(tag.getDatasource());
        mx.setSn(tag.getTmsort());
        mx.setInputCompType(comType);
        mx.setCollectPointText(tag.getVal());
        mx.setInputOptions(ops);
        mx.setTmused(1);
        mx.setIsWriteBackInfluxdb(tag.getIsWriteBackInfluxdb());
        mxData.add(mx);

        tempmap.put(tagpid, flvo);
        uobj.getFlData().add(flvo);
    }

    /**
     * 创建新核算对象
     */
    private void createNewUnitObject(AcctobjInputVo vo, String ucode, AccountAppVo tag, String tagpid,
                                     String col, String comType, String ops, Map<String, AcctobjInputVo> umap,
                                     Map<String, AcctobjInputFlVo> tempmap, Map<String, String> pnameMap, List<String> nlist) {

        AcctobjInputVo uobj = ObjUtils.copyTo(vo, AcctobjInputVo.class);

        // 获取实际的核算对象ID
        String newAccobjId = ucode;
        for (String str : nlist) {
            if (str.contains(ucode)) {
                newAccobjId = str;
                break;
            }
        }
        uobj.setAcctobjId(newAccobjId);
        uobj.setAcctobjName(tag.getUnitName());

        // 创建分类对象
        AcctobjInputFlVo flvo = ObjUtils.copyTo(uobj, AcctobjInputFlVo.class);
        flvo.setFlId(tagpid);

        // 设置分类名称，优先使用pnameMap中的名称
        String flName = pnameMap.getOrDefault(tagpid, tag.getTagPname());
        flvo.setFlName(flName);

        List<AcctobjInputmxVo> mxData = new ArrayList<>();
        flvo.setMxData(mxData);

        // 创建采集点明细
        AcctobjInputmxVo mx = new AcctobjInputmxVo();
        mx.setCollectPoint(tag.getTagnumber());
        mx.setCollectPointId(col);
        mx.setCollectPointVal(tag.getVal());
        mx.setTagNo(tag.getDatasource());
        mx.setSn(tag.getTmsort());
        mx.setInputCompType(comType);
        mx.setCollectPointText(tag.getVal());
        mx.setInputOptions(ops);
        mx.setTmused(1);
        mx.setIsWriteBackInfluxdb(tag.getIsWriteBackInfluxdb());
        mxData.add(mx);

        tempmap.put(tagpid, flvo);
        uobj.getFlData().add(flvo);
        umap.put(ucode, uobj);
    }

    /**
     * 同步数据到台账
     */
    private Boolean syncDataToAccount(List<AcctobjInputVo> clist) {
        if (clist.isEmpty()) {
            return false;
        }

        try {
            // 获取父任务ID
            String p_activityId = "";
            String taskId = clist.get(0).getTaskId();

            if (StringUtils.isNotEmpty(taskId)) {
                String sql = "select PID from JOBLIST_ACTIVITYEXAMPLE where ID=?";
                List<Object> param = new ArrayList<>();
                param.add(taskId);
                List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());

                if (StringUtils.isNotEmpty(list) && list.get(0) != null && list.get(0).get("PID") != null) {
                    p_activityId = String.valueOf(list.get(0).get("PID"));
                }
            }

            // 准备保存对象
            AccountSaveDto obj = new AccountSaveDto();
            obj.setType("collectionPoint"); // 采集点类型
            obj.setCollectionPointInputData(clist);
            obj.setOperType("");
            obj.setFormDataId(null); // 移动端操作不涉及表单保存，所以无表单实例数据ID
            obj.setTaskId(p_activityId);
            // 同步数据
            return mcostSrv.syncDataWithAccount(obj);
        } catch (MobileException e) {
            log.error("移动端台账保存失败：{}，详细信息：{}", e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("台账数据同步异常：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<TdsFormulaTreeVo> getAccountVarList(AccountParam dto) {

        /*
         * 参数传入判断，设置是 活动 或 台账模型，提供的变量不同
         * 活动：提供 台账.变量（默认为第一个台账），查询对应活动关联表单中的台账模型列表，提供对应变量（台账模型列表根据数据进行动态显示）
         * 台账模型：仅提供台账变量，前缀为台账数据；变量为台账数据.变量
         */
        String activeId = dto.getActiveId();
        String ledgerId = dto.getLedgerId();
        Boolean isActive = true;//是否是活动
//		List ledgerList = new ArrayList();//台账模型信息
        List<DigitalLedger> activeFormList = null;
        if (StringUtils.isNotEmpty(ledgerId)) {
            isActive = false;
            activeFormList = getActiveFormList(activeId, ledgerId);
        } else {
            //根据活动获取所有台账模型信息
            activeFormList = getActiveFormList(activeId, null);
        }

        List<TdsFormulaTreeVo> rlist = new ArrayList<TdsFormulaTreeVo>();

        if (isActive) {//活动
            //增加活动变量，活动设置带质量分，默认为第一个表单
            TdsFormulaTreeVo vo = new TdsFormulaTreeVo();
            vo.setNodeId(TMUID.getUID());
            vo.setNodeName("表单");
            vo.setNodeType("param");
            vo.setPid("account");
            vo.setIsleaf(0);
            vo.setId(vo.getNodeId());
            List<TdsFormulaTreeVo> clist = encList(vo.getNodeId(), "from", "表单", true);
            vo.setChildren(clist);
            rlist.add(vo);
            //按具体活动中表单显示，获取对应变量
            if (StringUtils.isNotEmpty(activeFormList)) {
                for (int i = 0, il = activeFormList.size(); i < il; i++) {
                    DigitalLedger form = activeFormList.get(i);

                    TdsFormulaTreeVo fobj = new TdsFormulaTreeVo();
                    fobj.setNodeId(TMUID.getUID());
                    fobj.setNodeName(form.getFormName());
                    List<TdsFormulaTreeVo> _list = encList(vo.getNodeId(), form.getFormId(), form.getFormName(), true);
                    fobj.setChildren(_list);

                    rlist.add(fobj);
                }
            }
        } else {
            //表格模型变量
            TdsFormulaTreeVo vo = new TdsFormulaTreeVo();
            vo.setNodeId(TMUID.getUID());
            vo.setNodeName("模型");
            vo.setNodeType("param");
            vo.setPid("account");
            vo.setIsleaf(0);
            vo.setId(vo.getNodeId());
            List<TdsFormulaTreeVo> clist = encList(vo.getNodeId(), "data", "模型", false);
            vo.setChildren(clist);
            rlist.add(vo);
        }
        return rlist;
    }

    private List<TdsFormulaTreeVo> encList(String pid, String pcode, String pname, Boolean isActive) {
        List<TdsFormulaTreeVo> list = new ArrayList<TdsFormulaTreeVo>();
        if (isActive) {
            list.add(encNode(pid, "质量分", pcode + ".qualityScore", pname + ".质量分", 1));//
        }
//		list.add(encNode(pid, "总数量", pcode+".total", pname+".总数量", 1));//
//		list.add(encNode(pid, "已填写数量", pcode+".writeNum", pname+".已填写数量", 1));//
//		list.add(encNode(pid, "未填写数量", pcode+".unwriteNum", pname+".未填写数量", 1));
        list.add(encNode(pid, "填写率", pcode + ".writeRate", pname + ".填写率", 1));
        list.add(encNode(pid, "超限数量", pcode + ".overNum", pname + ".超限数量", 1));
        list.add(encNode(pid, "超限率", pcode + ".overRate", pname + ".超限率", 1));
        return list;
    }

    /*
     * 公式封装
     */
    private TdsFormulaTreeVo encNode(String pid, String nodeName, String paramCode, String paramName, Integer isLeaf) {
        TdsFormulaTreeVo formula = new TdsFormulaTreeVo();
        formula.setNodeId(TMUID.getUID());
        formula.setPid(pid);
        formula.setNodeName(nodeName);
        formula.setParamCode(paramCode);
        formula.setParamName(paramName);
        formula.setIsleaf(isLeaf);
        formula.setNodeType("param");
        formula.setId(formula.getNodeId());
        return formula;
    }
//	/*
//	 * 获取活动表单列表
//	 */
//	private List<DigitalLedgerForm> getActiveFormList(String activeId, String ledgerId) {
//		List<DigitalLedgerForm> list = new ArrayList<DigitalLedgerForm>();
//		if(StringUtils.isNotEmpty(ledgerId)) {
//			DigitalLedgerModule ledger = srv.queryObjectById(DigitalLedgerModule.class, ledgerId);
//			DigitalLedgerForm obj = new DigitalLedgerForm();
//			obj.setFormName(ledger.getModuleName());
//
//			list.add(obj);
//		}else {
////			Where where = Where.create();
////			where.eq(DigitalLedgerForm::getTmused, 1);
////			where.eq(DigitalLedgerForm::getActivityId, activeId);
////			Order order = Order.create();
////			order.order(DigitalLedgerForm::getTmsort);
////			list = srv.queryData(DigitalLedgerForm.class, where, order, null);
//			Where where = Where.create();
//			where.eq(JoblistPersonBind::getTmused, 1);
//			where.eq(JoblistPersonBind::getPid, activeId);
//			where.eq(JoblistPersonBind::getBindtype, 4);
//			List<JoblistPersonBind> tlist = srv.queryData(JoblistPersonBind.class, where, null, null);
//			if(StringUtils.isNotEmpty(tlist)) {
//				for (JoblistPersonBind job : tlist) {
//					DigitalLedgerForm obj = new DigitalLedgerForm();
//					obj.setFormId(job.getBindid());
//					obj.setFormName(job.getBindname());
//
//					list.add(obj);
//				}
//
//			}
//		}
//        return list;
//	}

    /**
     * @param dto
     * @return
     * @category 公式设置用台账变量列表
     */
    @Override
    public String parseFormula(AccountParam dto) {
        Double rd = null;
        String rv = null;
        String activeId = dto.getActiveId();//活动ID
        String lederId = dto.getLedgerId();//台账模型，据此获取核对对象再取相关采集点信息
        String formId = dto.getFormId();//表单
        String rq = dto.getRq();
        String orgCode = dto.getOrgCode();
        String orgName = dto.getOrgCode();
        String shiftCode = dto.getShiftCode();
        String sbsj = dto.getSbsj();
        String xbsj = dto.getXbsj();
        String yf = "";
        if (rq.length() >= 7) {
            yf = rq.substring(0, 7);
        }

        String formulaTxt = dto.getFormulaTxt();//公式信息
        String gs = formulaTxt + "";//新字段

        PublicMethods pm = new PublicMethods();
        if (pm.judgeDouble(gs)) {// 常数，直接返回
            rd = pm.convertDouble(formulaTxt, 0.0);
            return String.valueOf(rd);
        }

        SysUser user = SysUserHolder.getCurrentUser();
        ParseParamObj p = new ParseParamObj();
        p.setActiveId(activeId);
        p.setLedgerId(lederId);
        p.setFormId(formId);
        p.setYf(yf);// 发放月份
        p.setOrgCode(orgCode);
        p.setOrgName(orgName);
        p.setZyid(user.getId());
        p.setZyxm(user.getRealName());
        p.setGwid(user.getPostId());
        p.setGwmc(user.getPostName());
        p.setRq(rq);
        p.setSbsj(sbsj);
        p.setXbsj(xbsj);
        p.setShiftCode(shiftCode);
        p.setWs(2);//小数位数

		/*
		 * 1、获取公式变量，数据源变量
		 * 2、数据源变量解析
		 * 3、公式变量解析
		 * 3、根据结果整理替换变量
		 * 4、调用公式解析功能传入公式及对应值
		"tz\\.\\w*\\.\\w[1-10]"//台账变量
		 */

        //根据函数获取公式变量集合
        Map<String, List<String>> resMap = AviatorUtils.getAllParams(formulaTxt);
        // 公式变量
        List<String> varList = resMap.get("var");// 处理时，需先将数据源字符串屏蔽掉
        // 数据源列表（数据源别名，含$）
        List<String> tdsList = resMap.get("tds");
        // 数据源公式列表
        List<String> tdsFormulaList = resMap.get("tdsFormula");

//		//变量处理， 如果公式存在数据源参数，先替换掉数据源
//		if (tdsFormulaList != null && tdsFormulaList.size() > 0) {
//			List<String> list = new ArrayList<String>();
//			int count = tdsFormulaList.size();
//			for (int i = 0; count > i; i++) {
//				list.add("0.0");
//			}
//			formulaTxt = Coms.replaceBl(formulaTxt, _dsFormula, list);// 将数据源公式内容替换为计算结果
//		}

        CalcTools cct = new CalcTools();

        //---------------变量解析结果
        Map<String, Object> valueMap = new HashMap<>();
//		String dd = "0";
        List<String> vlist = new ArrayList<String>();//所有相关公式一起解析，如果有相同模块或相近公式，可汇总分组，解析一次处理，减少处理次数
        for (String var : varList) { // 获取变量值并赋值
            if ("$".equals(var.substring(0, 1)) || "if".equals(var) || "round".equals(var) || pm.judgeDouble(var) || "tdsNoVar".equals(var)) {
                // 数据源不解析、函数和常数不解析
                continue;
            }
            vlist.add(var);
        }
        if (StringUtils.isNotEmpty(vlist)) {
            getVarValue(vlist, p, valueMap, pm);
        }


        //---------------数据源解析结果
        Map<String, Object> tdsValueMap = new HashMap<>();
        if (tdsList != null && tdsList.size() > 0) {

            cct.TDSRetrieve(tdsList, p);
            for (String param : tdsFormulaList) {
                String tdsValue = "0.0";
                try {
                    tdsValue = cct.replaceDsFormula(param, p);
                } catch (Exception e) {
                }
                if (pm.judgeDouble(tdsValue)) {
                    tdsValueMap.put(param, Double.valueOf(tdsValue));
                } else if (StringUtils.isNotEmpty(tdsValue) && tdsValue.startsWith("[") && tdsValue.endsWith("]")) {//数组判断内容并取第一个
                    List<String> _list = Coms.StrToList(tdsValue.substring(1, tdsValue.length() - 1), ",");
                    if (_list.size() > 0 && StringUtils.isNotEmpty(_list.get(0)) && pm.judgeDouble(_list.get(0))) {
                        tdsValueMap.put(param, Double.valueOf(_list.get(0)));
                    } else {
                        tdsValueMap.put(param, tdsValue);
                    }
                } else {
                    tdsValueMap.put(param, tdsValue);
                }
            }
        }

        //---------------------------------整体公式解析
        try {
            Map<String,Object> valueMap1 = new HashMap<>();
            valueMap1.put("tongjizhi", 3333.9);
            AviatorResult ar = AviatorUtils.execute(gs, valueMap, tdsValueMap);
            Object oval = ar.getResult();
            if (StringUtils.isNotNull(oval)) {
                String sval = String.valueOf(oval);
                if ("nan".equalsIgnoreCase(sval) || "Infinity".equals(sval) || "-Infinity".equals(sval)) {
                    rv = pm.formatResult("0", p.getWs());
                } else {
                    rv = pm.formatResult(sval, p.getWs());
                }
            } else {
                rv = pm.formatResult("0", p.getWs());
            }
        } catch (Exception e) {

        }
        return rv;
    }

    /*
     * 获取变量值
     */
    private void getVarValue(List<String> vlist, ParseParamObj p, Map<String, Object> valueMap, PublicMethods pm) {
        Map<String, Map<String, String>> funDataMap = new HashMap<String, Map<String, String>>();//模块结果表，用于保存某个模块对应的获取结果，避免多次调用同一模块解析
//		Integer xs = p.getWs();

        for (String gs : vlist) {

            if (pm.judgeDouble(gs)) {//直接是数值不处理
//				valueMap.put(gs, gs);
            } else {
                List<String> slist = Coms.StrToList(gs, "\\.");
                if (slist.size() == 1) {//单个变量，暂不考虑 TODO
                    valueMap.put(gs, "0");
                } else if (slist.size() == 2) { //一个分割符变量 a.b
                    parseTzData(funDataMap, slist, p, pm);//解析台账变量
                    if ("form".equals(slist.get(0))) {//表单解析(默认为第一个)
                        Map<String, String> vmap = funDataMap.get("form");
                        if (vmap != null) {
                            Object v = vmap.get(slist.get(1));
                            if (v != null) {
                                valueMap.put(gs, toObj(v));
                            } else {
                                valueMap.put(gs, 0);
                            }
                        }
                    } else if ("data".equals(slist.get(0))) {//台账模型解析
                        Map<String, String> vmap = funDataMap.get("data");
                        if (vmap != null) {
                            Object v = vmap.get(slist.get(1));
                            if (v != null) {
                                valueMap.put(gs, toObj(v));
                            } else {
                                valueMap.put(gs, 0);
                            }
                        }
                    } else {//表单解析(指定表单id)
                        Map<String, String> vmap = funDataMap.get(slist.get(0));
                        if (vmap != null) {
                            Object v = vmap.get(slist.get(1));
                            if (v != null) {
                                valueMap.put(gs, toObj(v));
                            } else {
                                valueMap.put(gs, 0);
                            }
                        }
                    }
                } else if (slist.size() == 3) {//两个分隔符变量 a.b.c
                    // 调用外部考核：模块名称.主题.参数名称
                    if ("tz".equals(slist.get(0))) {
                        parseTzData(funDataMap, slist, p, pm);//解析台账变量
                        if ("form".equals(slist.get(1))) {//表单解析(默认为第一个)
                            Map<String, String> vmap = funDataMap.get("tz.form");
                            if (vmap != null) {
                                Object v = vmap.get(slist.get(2));
                                if (v != null) {
                                    valueMap.put(gs, toObj(v));
                                } else {
                                    valueMap.put(gs, 0);
                                }
                            }
                        } else if ("data".equals(slist.get(2))) {//台账模型解析
                            Map<String, String> vmap = funDataMap.get("tz.data");
                            if (vmap != null) {
                                Object v = vmap.get(slist.get(2));
                                if (v != null) {
                                    valueMap.put(gs, toObj(v));
                                } else {
                                    valueMap.put(gs, 0);
                                }
                            }
                        } else {//表单解析(指定表单id)
                            Map<String, String> vmap = funDataMap.get("tz." + slist.get(1));
                            if (vmap != null) {
                                Object v = vmap.get(slist.get(2));
                                if (v != null) {
                                    valueMap.put(gs, toObj(v));
                                } else {
                                    valueMap.put(gs, 0);
                                }
                            }
                        }
                    }
                }
            }
        }

    }

    private Object toObj(Object v) {
        if (v == null) {
            return 0;
        } else {
            String sv = String.valueOf(v);
            if (Coms.judgeLong(sv)) {
                return Integer.parseInt(sv);
            } else if (Coms.judgeDouble(sv)) {
                return Double.parseDouble(sv);
            } else {
                return v;
            }
        }
    }

    //根据变量分段拆分解析
    private void parseTzData(Map<String, Map<String, String>> funDataMap, List<String> vlist, ParseParamObj p, PublicMethods pm) {
        if (vlist.size() == 2) {//两个分隔符变量 a.b
            if ("form".equals(vlist.get(0))) {//表单解析(默认为第一个)
                if (!funDataMap.containsKey("form")) {
                    Map<String, String> dataMap = getDataMap(p, "form", pm);
                    funDataMap.put("form", dataMap);
                }
            } else if ("data".equals(vlist.get(0))) {//表单解析(默认为第一个)
                if (!funDataMap.containsKey("data")) {
                    Map<String, String> dataMap = getDataMap(p, "data", pm);
                    funDataMap.put("data", dataMap);
                }
            } else {
                if (!funDataMap.containsKey(vlist.get(0))) {
                    Map<String, String> dataMap = getDataMap(p, vlist.get(1), pm);
                    funDataMap.put(vlist.get(1), dataMap);
                }
            }
        } else if (vlist.size() == 3) {//两个分隔符变量 a.b.c
            if ("tz".equals(vlist.get(0))) {
                if ("form".equals(vlist.get(1))) {//表单解析(默认为第一个)
                    if (!funDataMap.containsKey("tz.form")) {
                        Map<String, String> dataMap = getDataMap(p, "form", pm);
                        funDataMap.put("tz.form", dataMap);
                    }
                } else if ("data".equals(vlist.get(1))) {//表单解析(默认为第一个)
                    if (!funDataMap.containsKey("tz.data")) {
                        Map<String, String> dataMap = getDataMap(p, "data", pm);
                        funDataMap.put("tz.data", dataMap);
                    }
                } else {
                    if (!funDataMap.containsKey("tz." + vlist.get(1))) {
                        Map<String, String> dataMap = getDataMap(p, vlist.get(2), pm);
                        funDataMap.put(vlist.get(1), dataMap);
                    }
                }
            }
        }
    }


    //获取台账具体数据结果方法
    private Map<String, String> getDataMap(ParseParamObj p, String mark, PublicMethods pm) {
        Map<String, String> rmap = null;
        if ("form".equals(mark)) {//取表单第一个台账模型
            //获取活动相关表单
            List<DigitalLedger> activeFormList = getActiveFormList(p.getActiveId(), null);
            //获取表单对应的台账模型信息
            List<DigitalLedger> ledgerList = getLedgerModeList(activeFormList);
            if (StringUtils.isNotEmpty(ledgerList)) {
                DigitalLedger ledger = ledgerList.get(0);//取第一个
                List<String> llist = new ArrayList<String>();
                llist.add(ledger.getLedgerModuleId());
                List<DigitalLedgerModule> ledgerModeList = getLedgerList(llist);
                //根据台账模型表获取相关模型配置，根据对应核算对象获取采集点
                AccountParam dto = ObjUtils.copyTo(p, AccountParam.class);
                Map<String, Map<String, Integer>> legCountMap = getLedgerData(ledgerModeList, dto);
                Map<String, Integer> dmap = legCountMap.get(ledger.getLedgerModuleId());

                //质量分需要根据模型查表独立查询
                String qualityScore = null;
                String sql = "select score from QUALITY_RESULT where QUALITY_ID=? and sourceId=?";//表单上取模型质量分，取模型id
                List<Object> param = new ArrayList<Object>();
                param.add(p.getQualityId());
                param.add(ledger.getLedgerModuleId());
                List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
                if (StringUtils.isNotEmpty(list)) {
                    Object jg = list.get(0).get("score");
                    if (jg != null) {
                        qualityScore = String.valueOf(jg);
                    }
                }

                if (dmap != null && dmap.size() > 0) {
                    Integer total = dmap.get("total");
                    Integer writeNum = dmap.get("writeNum");
                    Integer overNum = dmap.get("overNum");
                    Double writeRate = 0.0d;
                    if (total > 0) {
                        writeRate = pm.convertDouble(String.valueOf(writeNum * 1.0 / total), 0.0);
                    }
                    Double overRate = 0.0d;
                    if (total > 0) {
                        overRate = pm.convertDouble(String.valueOf(overNum * 1.0 / total), 0.0);
                    }

                    rmap = new HashMap<String, String>();
                    rmap.put("qualityScore", qualityScore);//质量分
                    rmap.put("total", String.valueOf(total));
                    rmap.put("writeNum", String.valueOf(writeNum));
                    rmap.put("unwriteNum", String.valueOf(total - writeNum));
                    rmap.put("writeRate", String.valueOf(writeRate));
                    rmap.put("overNum", String.valueOf(overNum));
                    rmap.put("overRate", String.valueOf(overRate));
                }
            }
        } else if ("data".equals(mark)) {//获取某个台账模型数据
            List<DigitalLedgerModule> ledgerModeList = new ArrayList<DigitalLedgerModule>();
            DigitalLedgerModule led = srv.queryObjectById(DigitalLedgerModule.class, p.getLedgerId());
            ledgerModeList.add(led);
            //根据台账模型表获取相关模型配置，根据对应核算对象获取采集点
            AccountParam dto = ObjUtils.copyTo(p, AccountParam.class);
            Map<String, Map<String, Integer>> legCountMap = getLedgerData(ledgerModeList, dto);
            Map<String, Integer> dmap = legCountMap.get(p.getLedgerId());
            if (dmap != null && dmap.size() > 0) {
                Integer total = dmap.get("total");
                Integer writeNum = dmap.get("writeNum");
                Integer overNum = dmap.get("overNum");
                Double writeRate = 0.0d;
                if (total > 0) {
                    writeRate = pm.convertDouble(String.valueOf(writeNum * 1.0 / total), 0.0);
                }
                Double overRate = 0.0d;
                if (total > 0) {
                    overRate = pm.convertDouble(String.valueOf(overNum * 1.0 / overRate), 0.0);
                }

                rmap = new HashMap<String, String>();
                rmap.put("total", String.valueOf(total));
                rmap.put("writeNum", String.valueOf(writeNum));
                rmap.put("unwriteNum", String.valueOf(total - writeNum));
                rmap.put("writeRate", String.valueOf(writeRate));
                rmap.put("overNum", String.valueOf(overNum));
                rmap.put("overRate", String.valueOf(overRate));
            }

        } else {//mark为台账模型id
            List<String> llist = new ArrayList<String>();
            llist.add(mark);
            List<DigitalLedgerModule> ledgerModeList = getLedgerList(llist);
            if (StringUtils.isEmpty(ledgerModeList)) {
                return rmap;
            }
            //根据台账模型表获取相关模型配置，根据对应核算对象获取采集点
            AccountParam dto = ObjUtils.copyTo(p, AccountParam.class);
            Map<String, Map<String, Integer>> legCountMap = getLedgerData(ledgerModeList, dto);
            Map<String, Integer> dmap = legCountMap.get(mark);
            if (dmap != null && dmap.size() > 0) {
                Integer total = dmap.get("total");
                Integer writeNum = dmap.get("writeNum");
                Integer overNum = dmap.get("overNum");
                Double writeRate = 0.0d;
                if (total > 0) {
                    writeRate = pm.convertDouble(String.valueOf(writeNum * 1.0 / total), 0.0);
                }
                Double overRate = 0.0d;
                if (total > 0) {
                    overRate = pm.convertDouble(String.valueOf(overNum * 1.0 / overRate), 0.0);
                }

                //质量分需要根据模型查表独立查询
                String qualityScore = null;
                String sql = "select score from QUALITY_RESULT where QUALITY_ID=? and sourceId=?";//表单上取模型质量分，取模型id
                List<Object> param = new ArrayList<Object>();
                param.add(p.getQualityId());
                param.add(mark);
                List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
                if (StringUtils.isNotEmpty(list)) {
                    Object jg = list.get(0).get("score");
                    if (jg != null) {
                        qualityScore = String.valueOf(jg);
                    }
                }

                rmap = new HashMap<String, String>();
                rmap.put("qualityScore", qualityScore);//质量分
                rmap.put("total", String.valueOf(total));
                rmap.put("writeNum", String.valueOf(writeNum));
                rmap.put("unwriteNum", String.valueOf(total - writeNum));
                rmap.put("writeRate", String.valueOf(writeRate));
                rmap.put("overNum", String.valueOf(overNum));
                rmap.put("overRate", String.valueOf(overRate));
            }
        }
        return rmap;
    }

    private String escapeExprSpecialword(String keyword) {
        if (StringUtils.isNotEmpty(keyword)) {
            String[] fbsArr = {"\\", "$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|", "·"};
            for (String key : fbsArr) {
                if (keyword.contains(key)) {
                    keyword = keyword.replace(key, "\\" + key);
                }
            }
        }
        return keyword;
    }

    // 取参数值
    private String getVarValue(String var, ParseParamObj p, CalcTools cct, PublicMethods pm) {
        String Var;
        String rtn = var;
        Integer xs = p.getWs();
        Var = var.toLowerCase();
        if (pm.judgeDouble(Var)) {
            rtn = Var;// 常数，直接返回
        } else {
            String[] s = Var.split("\\.");
            List<String> vlist = Coms.StrToList(Var, "\\.");
            if (vlist.size() == 1) {//单个变量，暂不考虑
                rtn = "0";
            } else if (vlist.size() == 2) { //一个分割符变量 a.b
                String keyf, keyo;

                rtn = "0";// 没有匹配要素返回0
//				if (var.indexOf("[") >= 0) {//调试时确认公式格式，是否有[]
                // 从注册模块来的方法：模块名称.参数名称
                // 拆解参数，得到注册模块和具体参数
                //如果需解析其他模块变量数据，需查询注册列表，如果未注册，返回默认值
//					if (!nocalcobj.containsKey(qkid)) {
                keyf = vlist.get(0).replaceAll("[\\{\\}]", "");
                keyo = vlist.get(1).replaceAll("[\\{\\}]", "");
//				ConcurrentHashMap<String, String> ym = getRegistModuleData(var, keyf, keyo, p, cct);
//				if (ym != null) {
//					// 取数
//					String zh = (new StringBuffer(orgid).append(",").append(zyid)).toString();
//					if (ym.containsKey(zh)) {
//						rtn = ym.get(zh);
//					}
//				}
//					} else {
//						rtn = "0";
//					}
//				}
            } else if (vlist.size() == 3) {//两个分隔符变量 a.b.c
                // 调用外部考核：模块名称.主题.参数名称
                rtn = "0";// 没有匹配要素返回0
                // 拆解参数，得到注册模块和具体参数
                String keyf = s[0].replaceAll("[\\{\\}]", "");
                String zt = s[1].replaceAll("[\\{\\}]", "");
                String cs = s[2].replaceAll("[\\{\\}]", "");
                String keyo = (new StringBuffer(zt).append(".").append(cs)).toString();
//				ConcurrentHashMap<String, String> ym = this.getRegistModuleData(var, keyf, keyo, info, cpb);
//				if (ym != null) {
//					// 取数
//					String zh = (new StringBuffer(orgid).append(",").append(zyid)).toString();
//					if (ym.containsKey(zh)) {
//						rtn = ym.get(zh);
//					}
//				}
            }
        }
        return rtn;
    }

    /**
     * 获得匹配的变量
     *
     * @param str
     * @param rex
     * @return
     */
    private List<String> getBl(String str, String rex) {
        List<String> list = new ArrayList<String>();
        if (rex == null ? true : rex.trim().length() == 0)
            rex = "([^\\/^\\+^\\-^\\*^ ^\\(^\\)^\\,^\\>,^\\=,^\\<]+\\.)+([A-Za-z]+)";
        Pattern pattern = Pattern.compile(rex);
        Matcher m = pattern.matcher(str);
        while (m.find()) {
            list.add(m.group());
        }
        return list;
    }

//	/*
//	 * 计算台账转化后数据结果
//	 */
//	public String calLedgerResult(String data, List<?> conditionList) {
//		String rv = "0";
//
//
//		return rv;
//	}

    /**
     * @category 判断并解析结果
     * 目前解析只支持以下公式设置方式
     * {"formula":"[统计值] > 90", "v": "10"},
     * {"formula":"90 >= [统计值] > 80", "v": "9"},
     * {"formula":"80 >= [统计值] >= 60", "v": "6"},
     * {"formula":"[统计值] < 60", "v": "3"}
     */
    @Override
    public String judgeParse(AccountParam dto) {
        String rv = null;
        String val = dto.getVal();//[统计值]的结果
        List<Map<String, String>> flist = dto.getFormulaMap();
        String gs = flist.get(0).get("formula");
        gs = gs.replaceAll("\\[统计值\\]", "TONG_JI_ZHI");
        Map<String, Object> valueMap = new HashMap<>();
        valueMap.put("TONG_JI_ZHI", Double.valueOf(val));
        AviatorResult arr = AviatorUtils.execute(gs, valueMap, null);
        if (StringUtils.isNotEmpty(flist)) {
            for (Map<String, String> map : flist) {
                String v = String.valueOf(map.get("v"));
                String result = String.valueOf(arr.getResult());
                if ("true".equals(result)) {
                    rv = v;
                    break;
                }
            }
        }


        return rv;
    }

    //公式判断
    private Boolean judgeGs(String gs, String val) {
        if (gs.indexOf("[统计值]") != -1) {
            String ngs = gs.replace("[统计值]", ",,,").replace(" ", "");
            List<String> gslist = Coms.StrToList(ngs, ",,,");
            for (int i = 0, il = gslist.size(); i < il; i++) {
                String item = gslist.get(i);
                if (i == 0) {
                    if (item.length() > 0) {
                        Boolean jg = judgeStr(item, val, 1);
                        if (jg == false) {
                            return jg;
                        }
                    }
                } else {
                    Boolean jg = judgeStr(item, val, 0);
                    if (jg == false) {
                        return jg;
                    }
                }
            }
            return true;
        }
        return false;
    }

    private Boolean judgeStr(String item, String val, int pos) {
        Boolean flag = false;
        int bj = -1;
        String cv = item;

        List<String> tlist = new ArrayList<String>();
        tlist.add(">=");
        tlist.add("<=");
        tlist.add(">");
        tlist.add("<");
        tlist.add("==");

        for (int i = 0, il = tlist.size(); i < il; i++) {
            String s = tlist.get(i);
            if (item.indexOf(s) != -1) {
                bj = i;
                cv = item.replace(s, "");
                break;
            }
        }
        if (Coms.judgeDouble(cv) && Coms.judgeDouble(val)) {
            Double v1 = pos == 0 ? Double.parseDouble(val) : Double.parseDouble(cv);
            Double v2 = pos == 0 ? Double.parseDouble(cv) : Double.parseDouble(val);
            if (bj == 0 && v1 >= v2) {
                flag = true;
            } else if (bj == 1 && v1 <= v2) {
                flag = true;
            } else if (bj == 2 && v1 > v2) {
                flag = true;
            } else if (bj == 3 && v1 < v2) {
                flag = true;
            } else if (bj == 4 && v1.doubleValue() == v2.doubleValue()) {
                flag = true;
            }
        }
        return flag;
    }

    @Autowired
    private IFormManageService formSrv;

    @Override
    public boolean isInsertData(AccountParam dto) {
        //通过表单id 和 表格id 查询是否允许新增
        String formId = dto.getFormId();
        String tableId = dto.getTableId();
        JSONArray formAllComponents = formSrv.getFormComponentJsonArray(formId, "tdsEditTable");
        if (formAllComponents == null) {
            return false;
        }
        for (int i = 0; i < formAllComponents.size(); i++) {
            JSONObject component = formAllComponents.getJSONObject(i);
            if (component.getString("id").equals(tableId)) {
                JSONObject options = component.getJSONObject("options");
                return options.getBoolean("queryModel");
            }
        }
        return false;
    }
    /**
     * 获取原因分析
     * <AUTHOR>
     * @date 2025/6/26
     * @params
     * @return
     *
    */
    @Override
    public TdsAccountMarkinfo getReasonAnalysis(JSONObject param) {
        Where where = Where.create();
        where.eq(TdsAccountMarkinfo::getUnitcode, param.getString("shiftId"));
        where.eq(TdsAccountMarkinfo::getColalias, param.getString("colAlias"));
        where.eq(TdsAccountMarkinfo::getTimepoInteger, param.getString("id"));
        where.eq(TdsAccountMarkinfo::getSbsjstr, param.getString("sbsj"));
        where.eq(TdsAccountMarkinfo::getRq, param.getString("rq"));
        where.eq(TdsAccountMarkinfo::getXbsjstr, param.getString("xbsj"));
        where.eq(TdsAccountMarkinfo::getTdsalias, param.getString("tdsAlias"));
        where.eq(TdsAccountMarkinfo::getTmused, 1);
        TdsAccountMarkinfo tdsAccountMarkinfo = srv.rawQueryObjectByWhere(TdsAccountMarkinfo.class, where);
        if(tdsAccountMarkinfo==null){
            tdsAccountMarkinfo = new TdsAccountMarkinfo();
            tdsAccountMarkinfo.setUnitcode(param.getString("shiftId"));
            tdsAccountMarkinfo.setColalias(param.getString("colAlias"));
            tdsAccountMarkinfo.setMarkKey(param.getString("colAlias")+"_"+param.getString("id"));
            tdsAccountMarkinfo.setTimepoInteger(param.getString("id"));
            tdsAccountMarkinfo.setRq(param.getString("rq"));
            tdsAccountMarkinfo.setSbsjstr(param.getString("sbsj"));
            tdsAccountMarkinfo.setXbsjstr(param.getString("xbsj"));
            tdsAccountMarkinfo.setTdsalias(param.getString("tdsAlias"));
            tdsAccountMarkinfo.setTmused(1);
        }
        return tdsAccountMarkinfo;
    }

    @Override
    public Boolean saveReasonAnalysis(TdsAccountMarkinfo param) {
        if(param==null){
            return false;
        }
        if(StringUtils.isEmpty(param.getId())){
            //添加原因分析
            param.setId(TMUID.getUID());
            param.setTmused(1);
            return srv.insert(param)>0;
        }else {
            return srv.rawUpdateByIdIncludeNull(param)>0;
        }
    }
}
