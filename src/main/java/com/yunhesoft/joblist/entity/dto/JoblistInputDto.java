package com.yunhesoft.joblist.entity.dto;

import com.yunhesoft.joblist.entity.po.JoblistGeneralFeedback;
import com.yunhesoft.task.apply.entity.vo.TmTaskInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("岗位责任清单录入查询参数对象")
@Data
public class JoblistInputDto {
    //——————————————————————————————————
    // 通用查询条件
    //——————————————————————————————————
    @ApiModelProperty("工作日期")
    private String WorkDate;
    @ApiModelProperty("班次 ID")
    private String shiftId;
    @ApiModelProperty("上班时间")
    private String sbsj;
    @ApiModelProperty("下班时间")
    private String xbsj;
    @ApiModelProperty("班次所属日期")
    private String tbrq;
    @ApiModelProperty("查询用户 ID")
    private String userId;
    @ApiModelProperty("查询岗位 ID")
    private String postId;
    @ApiModelProperty("机构 ID")
    private String orgCode;

    @ApiModelProperty("管理机构 ID")
    private String p_orgCode;
    @ApiModelProperty("日期字符串")
    private String dateStr;
    @ApiModelProperty("工作类型，取工作列表配置中的alias字段")
    private String routineWorkType;

    @ApiModelProperty("任务 ID")
    private String jobId;
    private String queryMethod;
    @ApiModelProperty("模糊查询关键字")
    private String fuzzyKeyword;

    @ApiModelProperty("每页最大记录数")
    private Integer pageSize;

    @ApiModelProperty("当前页码")
    private Integer pageNum;

    @ApiModelProperty("是否为父机构")
    private Boolean isParentOrg;

    //——————————————————————————————————
    // 例外任务（临时任务：任务督办模块数据）
    //——————————————————————————————————
    @ApiModelProperty("保存任务信息")
    private TmTaskInfoVo tmTaskInfo;

    @ApiModelProperty("查询模式：all=全部工作，todo=待办工作")
    private String managerQueryMode;

    @ApiModelProperty("查询类型")
    private String queryType;

    @ApiModelProperty("类别")
    private String category;

    @ApiModelProperty("专业")
    private String special;
    //——————————————————————————————————
    // 例行任务
    //——————————————————————————————————
    @ApiModelProperty("分配工作数据")
    private JobAllocationDto jobAllocationData;

    //——————————————————————————————————
    // 交接班日志（班组记事模块数据）
    //——————————————————————————————————


    //——————————————————————————————————
    // 常规活动反馈数据
    //——————————————————————————————————
    @ApiModelProperty("常规活动反馈数据")
    private JoblistGeneralFeedback generalFeedbackData;

    //——————————————————————————————————
    // 储运日报
    //——————————————————————————————————
    @ApiModelProperty("机构代码")
    private String orgId;
}
