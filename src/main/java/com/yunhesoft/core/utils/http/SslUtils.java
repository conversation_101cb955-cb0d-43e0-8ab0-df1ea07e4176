package com.yunhesoft.core.utils.http;

import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Arrays;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;

import lombok.extern.log4j.Log4j2;
import okhttp3.OkHttpClient;

@Log4j2
public class SslUtils {

    /**
     * 获取这个SSLSocketFactory
     */
    public static SSLSocketFactory getSSLSocketFactory() {

        try {
            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, getTrustManager(), new SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取TrustManager
     */
    private static TrustManager[] getTrustManager() {
        return new TrustManager[]{new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }
        }};
    }

    /**
     * 获取HostnameVerifier
     */
    public static HostnameVerifier getHostnameVerifier() {
        return (s, sslSession) -> true;
    }

    public static X509TrustManager getX509TrustManager() {
        X509TrustManager trustManager = null;
        try {
            TrustManagerFactory trustManagerFactory = TrustManagerFactory
                    .getInstance(TrustManagerFactory.getDefaultAlgorithm());
            trustManagerFactory.init((KeyStore) null);
            TrustManager[] trustManagers = trustManagerFactory.getTrustManagers();
            if (trustManagers.length != 1 || !(trustManagers[0] instanceof X509TrustManager)) {
                throw new IllegalStateException("Unexpected default trust managers:" + Arrays.toString(trustManagers));
            }
            trustManager = (X509TrustManager) trustManagers[0];
        } catch (Exception e) {
            log.error("", e);
        }

        return trustManager;
    }

    public static OkHttpClient getUnsafeOkHttpClent() throws KeyManagementException {
        try {
//			final TrustManager[] trustAllCerts = new TrustManager[] { new X509TrustManager() {
//				@Override
//				public void checkClientTrusted(X509Certificate[] x509Certificates, String s)
//						throws CertificateException {
//
//				}
//
//				@Override
//				public void checkServerTrusted(X509Certificate[] x509Certificates, String s)
//						throws CertificateException {
//
//				}
//
//				@Override
//				public X509Certificate[] getAcceptedIssuers() {
//					return new X509Certificate[] {};
//				}
//			} };
//			X509TrustManager x509TrustManager = (X509TrustManager) trustAllCerts[0];
//			final SSLContext sslContext = SSLContext.getInstance("SSL");
//			sslContext.init(null, trustAllCerts, new SecureRandom());
//			final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            X509TrustManager x509TrustManager = getX509TrustManager();
            SSLSocketFactory sslSocketFactory = getSSLSocketFactory();
            builder.sslSocketFactory(sslSocketFactory, x509TrustManager);

            builder.hostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String s, SSLSession sslSession) {
                    return true;
                }
            });
            return builder.build();

        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }
}
