package com.yunhesoft.core.utils.http;

import java.util.concurrent.TimeUnit;

import lombok.extern.log4j.Log4j2;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import com.alibaba.fastjson.JSONObject;


/**
 * http 访问工具类
 *
 * <AUTHOR>
 */
@Log4j2
public class HttpsUtils {


    /**
     * 接口获取数据(POST)
     *
     * @param url    接口地址
     * @param type   body or form
     * @param header header 需传导的参数
     * @param params 其他参数
     * @return
     */
    public static String doPost(String url, String type, JSONObject header, JSONObject params) {
        String jsonData = null;
        try {
            OkHttpClient client = getOkHttpClient(url);;
            RequestBody requestBody = null;
            if ("body".equals(type)) {
                String json = "{}";
                if (params != null) {
                    json = params.toJSONString();
                }
                // 2. 构建请求体 (body-JSON格式)
                requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
            } else {
                // 2. 构建请求体 (表单格式)
                requestBody = createFormBody(params);//new FormBody.Builder().add("pid", dto.getPid()).add("time", dto.getTime()).build();
            }
            // 3. 创建请求对象
            Request request = createPostRequest(url, header, requestBody);//new Request.Builder().url(url).build();
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    jsonData = response.body().string().trim();
                    String s = "[" + url + "],POST请求成功!";
                    printLog(s, type, header, params);
                    log.info(jsonData);//打印返回值
                } else {
                    String s = "[" + url + "],请求失败，状态码: " + response.code();
                    printLog(s, type, header, params);
                }
            } catch (Exception e) {
                String s = "[" + url + "]请求失败," + e.getMessage();
                printLog(s, type, header, params);
            }
        } catch (Exception ex) {
            String s = "[" + url + "]请求失败," + ex.getMessage();
            printLog(s, type, header, params);
        }
        return jsonData;
    }



    /**
     * 接口获取数据(POST)
     *
     * @param url    接口地址
     * @param header header 需传导的参数
     * @param params 其他参数
     * @return
     */
    public static String doGet(String url, JSONObject header, JSONObject params) {
        String jsonData = null;
        try {
            OkHttpClient client = getOkHttpClient(url);
            // 3. 创建请求对象
            Request request = createGetRequest(url, header, params);
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    jsonData = response.body().string().trim();
                    String s = "[" + url + "],GET请求成功!";
                    printLog(s, null, header, params);
                    log.info(jsonData);//打印返回值
                } else {
                    String s = "[" + url + "],请求失败，状态码: " + response.code();
                    printLog(s, null, header, params);
                }
            } catch (Exception e) {
                String s = "[" + url + "]请求失败," + e.getMessage();
                printLog(s, null, header, params);
            }
        } catch (Exception ex) {
            String s = "[" + url + "]请求失败," + ex.getMessage();
            printLog(s, null, header, params);
        }
        return jsonData;
    }

    private static OkHttpClient getOkHttpClient(String url) throws Exception {
        OkHttpClient client = null;
        if (url.toLowerCase().startsWith("https")) {//忽略证书
            client = SslUtils.getUnsafeOkHttpClent().newBuilder()
                    .connectTimeout(10, TimeUnit.SECONDS)  // 连接超时
                    .readTimeout(60, TimeUnit.SECONDS)     // 读取超时
                    .writeTimeout(60, TimeUnit.SECONDS)    // 写入超时
                    .build();
        } else {
            client = new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)  // 连接超时
                    .readTimeout(60, TimeUnit.SECONDS)     // 读取超时
                    .writeTimeout(60, TimeUnit.SECONDS)    // 写入超时
                    .build();
        }
        return client;
    }

    /**
     * 日志打印
     *
     * @param msg
     * @param type
     * @param header
     * @param params
     */
    private static void printLog(String msg, String type, JSONObject header, JSONObject params) {
        log.info(msg);
        if (type != null) {
            log.info("type:" + type);
        }
        if (header != null) {
            log.info("header:" + header.toJSONString());
        }
        if (params != null) {
            log.info("params:" + params.toJSONString());
        }

    }

    /**
     * 构建请求体 (表单格式)
     *
     * @param params
     * @return
     */
    private static RequestBody createFormBody(JSONObject params) {
        FormBody.Builder formBuilder = new FormBody.Builder();
        if (params != null && params.size() > 0) {
            for (String k : params.keySet()) {// 动态添加参数
                formBuilder.add(k, params.getString(k));
            }
        }
        return formBuilder.build();
    }

    /**
     * 创建request对象
     *
     * @param url
     * @param header
     * @param body
     * @return
     */
    private static Request createPostRequest(String url, JSONObject header, RequestBody body) {
//            Request request = new Request.Builder()
//            .url(url)
//            .post(jsonBody) // 设置为 POST 请求
//            .addHeader("Tenant-Id", "1")
//            .build();
        Request.Builder requestBuilder = new Request.Builder();
        requestBuilder.url(url);
        addHeader(requestBuilder,header);
        requestBuilder.post(body);
        return requestBuilder.build();
    }


    private static void addHeader(Request.Builder requestBuilder,JSONObject header){
        if(header!=null && header.size()>0){
            for(String k: header.keySet()){
                requestBuilder.addHeader(k, header.getString(k));
            }
        }
    }

    private static Request createGetRequest(String url, JSONObject header, JSONObject params) {
        Request.Builder requestBuilder = new Request.Builder();
        requestBuilder.url(getUrl(url, params));
        addHeader(requestBuilder,header);
        requestBuilder.get();
        return requestBuilder.build();
    }

    private static String getUrl(String url,JSONObject params){
        if(params!=null && params.size()>0){
            StringBuffer sb = new StringBuffer();
            for(String k:params.keySet()){
                String value = params.getString(k);
                if(value==null){
                    value = "";
                }
                sb.append(k);
                sb.append("=");
                sb.append(value);
                sb.append("&");
            }
            String s = sb.toString();
            s = s.substring(0,s.length()-1);
            if(url.indexOf("?")>0){
                url += "&" + s;
            }else{
                url += "?" + s;
            }
            return url;
        }else{
            return url;
        }
    }

    public static void main(String[] args) throws Exception {

			JSONObject header = new JSONObject();
			header.put("Tenant-Id", 1);
			JSONObject params = new JSONObject();
			params.put("action", "getDsData");
			params.put("ds", "dxlsjy");
			params.put("type", "json");
			String json = HttpsUtils.doGet("https://yh.mytm3.com/TM2Lyyh/tds/getTdsData.jsp", header, params);
			log.info(json);

    }


}
