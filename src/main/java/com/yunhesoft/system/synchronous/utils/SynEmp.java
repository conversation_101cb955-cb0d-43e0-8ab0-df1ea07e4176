package com.yunhesoft.system.synchronous.utils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.employee.entity.dto.EmpInitLoginDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeDto;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.IEmployeeBasicOperationService;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.post.entity.vo.PostVo;
import com.yunhesoft.system.post.service.IPostBasicOperationService;
import com.yunhesoft.system.role.entity.po.SysRole;
import com.yunhesoft.system.role.service.ISysRoleService;
import com.yunhesoft.system.synchronous.entity.po.SysComparison;
import com.yunhesoft.system.synchronous.service.ISynchronousService;

import lombok.extern.log4j.Log4j2;
@Log4j2
@Service
public class SynEmp extends SynModel {
	
	@Autowired
	private IPostBasicOperationService postOperSrv;
	@Autowired
	private IEmployeeBasicOperationService empOperSrv;	
	@Autowired
	private ISysRoleService roleOperSrv;
	@Autowired
	private ISysOrgService orgServer;
	@Autowired
	private ISynchronousService synServer;
	
	
	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList,HashMap<String,String> synParam) {
		// TODO Auto-generated method stub

		String defaultRoleId = null;//默认角色ID	
		Map<String, SysOrg> orgMap = null;//机构路径map
		
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>();//失败返回的数据		
		String synMode = synParam.get("synMode");//同步模式 0正常模式 1上级模式（这个模式是由于人员不在班组上，只能手动调班组。人员同步时，会判断调动的机构是否没有真正变动（人手动调到班组，如果同步是还在这个班组的上级机构，那就是没改变机构，不能把人从班组拉到上级班组））
		if(StringUtils.isEmpty(synMode)) {
			synMode="0";//标准模式
		}
		if("1".equals(synMode)) {//上级模式的时候需要获取机构路径
			orgMap = getOrgMap();
		}
		String defaultRoleName = synParam.get("defaultRoleName");//默认创建账号的角名称	
		if(StringUtils.isEmpty(defaultRoleName)) {
			defaultRoleName= "普通用户";
		}
		String synDelFlagValue = synParam.get("synDelFlagValue");//外部数据同步删除标识值
		if(StringUtils.isEmpty(synDelFlagValue)) {
			synDelFlagValue="1";//默认标识1为删除
		}
		String sexValue = synParam.get("sexValue");//男女标识值
		if(StringUtils.isEmpty(sexValue)) {
			sexValue="{'男':'1','女':'0'}";
		}
		HashMap<String,String> sexMap = new HashMap<String,String>();
		sexMap.put("男", "1");//TM4男为1
		sexMap.put("女", "0");//TM4女为0
		try {
			JSONObject jobj = JSON.parseObject(sexValue);
			if(jobj.containsKey("男")) {
				String mVal = jobj.getString("男");
				if(mVal!=null) {
					sexMap.put(mVal, "1");//TM4男为1
				}
				String wVal = jobj.getString("女");
				if(wVal!=null) {
					sexMap.put(wVal, "0");//TM4女为0
				}
			}
		}catch(Exception e) {
			log.error(e);
		}
		boolean synTM3 = false;//是否要同步到TM3对照表
		String synTM3Str = synParam.get("synTM3");//是否要同步到TM3对照表
		if("true".equals(synTM3Str)) {
			synTM3 = true;
		}
		List<LinkedHashMap<String,HashMap<String, Object>>> synDataMapList = new ArrayList<LinkedHashMap<String,HashMap<String, Object>>>();//同步数据map list
		LinkedHashMap<String,HashMap<String, Object>> subMap= new LinkedHashMap<String,HashMap<String, Object>>();	
		List<List<EmployeeDto>> synUserList = new ArrayList<List<EmployeeDto>>();//同步人员list
		List<EmployeeDto> subUser = new ArrayList<EmployeeDto>();
		List<List<String>> synUserIdList = new ArrayList<List<String>>();//同步人员id list
		List<String> subUserId = new ArrayList<String>();
		LinkedHashMap<String,PostVo> synPostIdMap = new LinkedHashMap<String,PostVo>();//岗位mapLinkedHashMap
		LinkedHashMap<String,PostVo> synPostNameMap = new LinkedHashMap<String,PostVo>();//岗位map
		for(HashMap<String, Object> temp:dataList) {
			EmployeeDto synUser = ObjUtils.convertToObject(EmployeeDto.class, temp);//由map生成人员类
			if(synUser!=null) {//数据读取成功，有id
				if(StringUtils.isNotEmpty(synUser.getPostname())) {//有完整的岗位信息（岗位名称即可）  StringUtils.isNotEmpty(synUser.getPostid()) && 
					PostVo post = new PostVo();
					post.setId(synUser.getPostid());
					post.setName(synUser.getPostname());
					if(StringUtils.isNotEmpty(synUser.getPostid())) {
						synPostIdMap.put(synUser.getPostid(), post);//添加到岗位map，统一处理
					}else {
						synPostNameMap.put(synUser.getPostname(), post);//添加到岗位map，统一处理
					}
				}
				if(StringUtils.isNotEmpty(synUser.getEmpTmuid())) {
//					if("男".equals(synUser.getSex())) {
//						synUser.setSex("1");
//					}else if("女".equals(synUser.getSex())) {
//						synUser.setSex("0");
//					}
					synUser.setSex(sexMap.get(synUser.getSex()));//获取男女标识
					synUser.setOrgStatus(1);//主机构人员
					synUser.setPostStatus(1);//主岗位
					subMap.put(synUser.getEmpTmuid(), temp);//存放进map，用于后续确定是否同步成功
					subUser.add(synUser);
					subUserId.add(synUser.getEmpTmuid());
					if(subUser.size()>=100) {//100条处理一次
						synDataMapList.add(subMap);
						synUserList.add(subUser);
						synUserIdList.add(subUserId);
						subMap= new LinkedHashMap<String,HashMap<String, Object>>();
						subUser = new ArrayList<EmployeeDto>();
						subUserId = new ArrayList<String>();
					}
				}
			}else {
				result.add(temp);//记录到失败数据中
			}
		}
		if(subUser.size()>0) {//还有剩余的数据，存放一条新记录
			synDataMapList.add(subMap);
			synUserList.add(subUser);
			synUserIdList.add(subUserId);
		}
		HashMap<String,PostVo> postMap = this.synPost(synPostIdMap,synPostNameMap);//同步岗位
		
		if(synUserList.size()>0) {
			List<EmployeeDto> synComparisonList = new ArrayList<EmployeeDto>();//对照列表
			List<SysRole> roleList = roleOperSrv.getRoleByName(defaultRoleName);//获取默认角色
			if(StringUtils.isNotEmpty(roleList)) {//有岗位信息
				defaultRoleId=roleList.get(0).getId();//默认角色ID			
			}
			Date maxDt = null;
			for(int i=0,j=synUserList.size();i<j;i++) {//同步人员,100人一批
				List<EmployeeDto> userList = synUserList.get(i);//批量处理的数据列表，处理后，失败的数据会移除，只保留成功的数据
				List<HashMap<String, Object>> failureList =this.synUser(synDataMapList.get(i), userList, synUserIdList.get(i),synDelFlagValue,defaultRoleId,orgMap,postMap,synComparisonList);	
				if(StringUtils.isNotEmpty(failureList)) {
					result.addAll(failureList);
				}
				if(userList.size()>0){//有成功的数据
					for(EmployeeDto temp:userList) {
						Date dt = temp.getChangeDt();//最后变动日期
						if(dt!=null) {//有日期
							if(maxDt==null) {
								maxDt=dt;
							}else {
								if(DateTimeUtils.bjDate(dt, maxDt)>0) {//新的日期比当前最大的大
									maxDt=dt;
								}
							}
						}
					}
				}
			}
			if(synTM3) {//需要同步TM3
				this.synSysComparison(synComparisonList);
			}
			if(maxDt!=null) {
				this.version = DateTimeUtils.formatDateTime(maxDt);//记录同步成功的数据的最大版本
			}
		}
		return result;
	}
	/**
	 * 获取机构树形
	 * @category 
	 * <AUTHOR> 
	 * @return
	 */
	private Map<String, SysOrg> getOrgMap(){
		Map<String, SysOrg> result = null;
		List<SysOrg> orgList =orgServer.getOrgList("root");//获取系统内的机构(不从缓存走，走数据库)
		if(StringUtils.isNotEmpty(orgList)) {//没有任何节点
			result = orgList.stream().collect(Collectors.toMap(SysOrg::getId,
					SysOrg -> SysOrg, (key1, key2) -> key1));//同步数据map
		}
		return result;
	}
	/**
	 * 判断是否需要更新机构
	 * @category 
	 * <AUTHOR> 
	 * @param emp 要更新的人员信息
	 * @param userVo 数据库内的人员信息
	 * @return
	 */
	private boolean isChange(EmployeeDto emp,EmployeeVo userVo,Map<String, SysOrg> orgMap) {
		boolean result = true;
		if(StringUtils.isNotEmpty(emp.getOrgcode())){//有机构代码
			if(orgMap!=null) {//需要判断机构
				SysOrg oldOrg = orgMap.get(userVo.getOrgcode());//获取原来的机构
				if(oldOrg!=null && StringUtils.isNotEmpty(oldOrg.getOrgpath())) {
					if(oldOrg.getOrgpath().indexOf("/"+emp.getOrgcode())>=0) {//如果要变更的机构是现有所在机构的上级机构
						result = false;//不向上变动
					}
				}
			}
		}else {
			result = false;
		}
		return result;	
	}
	/**
	 * 同步岗位信息
	 * @category 同步岗位信息
	 * <AUTHOR> 
	 * @param synPostMap
	 */
	private HashMap<String,PostVo> synPost(LinkedHashMap<String,PostVo> synPostIdMap,LinkedHashMap<String,PostVo> synPostNameMap) {
		HashMap<String,PostVo> result = new HashMap<String,PostVo>();
		boolean hasIdMap = StringUtils.isNotEmpty(synPostIdMap);
		boolean hasNameMap = StringUtils.isNotEmpty(synPostNameMap);
		if(hasIdMap || hasNameMap) {//有岗位信息
			List<PostVo> dataList = new ArrayList<PostVo>();
			if(hasIdMap) {
				dataList.addAll(synPostIdMap.values());
			}
			if(hasNameMap) {
				dataList.addAll(synPostNameMap.values());
			}
			List<PostVo> saveList = postOperSrv.importPostByIdOrName(dataList);
			if(StringUtils.isNotEmpty(saveList)) {
				for(PostVo temp:saveList) {
					result.put(temp.getName(), temp);//按名称生成map
				}
			}
		}
		return result;
	}

	/**
	 * 同步人员信息
	 * @category 同步人员信息
	 * <AUTHOR> 
	 * @param dataMap 数据map
	 * @param dataList 
	 * @param userList
	 * @param userIdList
	 */
	private List<HashMap<String, Object>> synUser(LinkedHashMap<String,HashMap<String, Object>> dataMap,List<EmployeeDto> userList,List<String> userIdList,
			String synDelFlagValue,String defaultRoleId,Map<String, SysOrg> orgMap,HashMap<String,PostVo> postMap,List<EmployeeDto> synComparisonList) {
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>();
		List<EmployeeVo> queryList = empOperSrv.getEmployee(userIdList);
		Map<String, EmployeeVo> queryMap = null;
		if(StringUtils.isNotEmpty(queryList)) {
			queryMap = queryList.stream().collect(Collectors.toMap(EmployeeVo::getEmpTmuid,
					EmployeeVo -> EmployeeVo, (key1, key2) -> key1));//数据map
		}
		if(queryMap==null) {
			queryMap=new HashMap<String, EmployeeVo>();
		}
		List<EmployeeDto> addList = new ArrayList<EmployeeDto>();
		List<EmployeeDto> updateList = new ArrayList<EmployeeDto>();
		List<EmployeeDto> oldUpdateList = new ArrayList<EmployeeDto>();
		List<EmployeeDto> deleteList = new ArrayList<EmployeeDto>();
		List<EmployeeDto> successDataList = new ArrayList<EmployeeDto>();//成功数据
		List<String> addLoginList = new ArrayList<String>();
		for(EmployeeDto temp:userList) {
			if(synDelFlagValue.equals(temp.getSynDelFlag())){//是删除
				//不用处理了
				deleteList.add(temp);//等于是成功了，不需要进数据库
			}else {
				if(StringUtils.isEmpty(temp.getPostid()) && StringUtils.isNotEmpty(temp.getPostname())) {//有岗位名称，但是没有岗位id
					PostVo post = postMap.get(temp.getPostname());
					if(post!=null) {
						temp.setPostid(post.getId());//将岗位id赋值给人员
					}
				}
				EmployeeVo userVo = queryMap.get(temp.getEmpTmuid());
				if(userVo!=null) {//有人更新
					if(StringUtils.isEmpty(temp.getPostid())) {//没传岗位则不更新岗位信息
						temp.setPostid(null);
					}
					if(!isChange(temp,userVo,orgMap)) {//如果不需要修改机构
						temp.setOrgcode(null);
					}
					EmployeeDto bean = ObjUtils.copyTo(userVo, EmployeeDto.class);
					ObjUtils.copyNonNullProperties(bean, temp);//更新人员信息
					updateList.add(bean);
					oldUpdateList.add(temp);//这个是用来移除成功记录的
				}else {//无人新建	
					if(StringUtils.isNotEmpty(temp.getEmpname()) && StringUtils.isNotEmpty(temp.getOrgcode())/* && StringUtils.isNotEmpty(temp.getStaffNo())*/) {//有人员名称和机构才新建(这里有没有工号的人)
//						temp.setRoleid(this.defaultRoleId);//默认角色
						addList.add(temp);
						addLoginList.add(temp.getEmpTmuid());
					}
				}
			}
		}
		if(addList.size()>0) {
			String saveResult = empOperSrv.addEmployee(addList);
			if(StringUtils.isNotEmpty(saveResult)) {//有错误信息
				this.errorInfo += saveResult+";";
			}else {
				EmpInitLoginDto dto = new EmpInitLoginDto();
				dto.setEmpidList(addLoginList);
				dto.setLoginType("1");
				dto.setRoleId(defaultRoleId);
				empOperSrv.initLoginInfoAll(dto);//创建账号
				successDataList.addAll(addList);
				synComparisonList.addAll(addList);//添加到对照列表
			}
		}
		if(updateList.size()>0) {
			String saveResult = empOperSrv.updEmployee(updateList);
			if(StringUtils.isNotEmpty(saveResult)) {//有错误信息
				this.errorInfo += saveResult+";";
			}else {
				successDataList.addAll(oldUpdateList);
			}
		}
		if(deleteList.size()>0) {//删除
			//不用处理了
			successDataList.addAll(deleteList);//等于是成功了，不需要进数据库
		}
		userList.removeAll(successDataList);//将成功的数据移除
		if(userList.size()>0) {//有失败的数据
			for(EmployeeDto temp:userList) {
				HashMap<String, Object> fData = dataMap.get(temp.getEmpTmuid());//获取失败的原始数据
				if(fData!=null) {
					result.add(fData);//添加到失败记录
				}
			}				
		}	
		userList.clear();//清空数据列表
		userList.addAll(successDataList);//将成功的数据设置回记录列表
		return result;
	}

	@Override
	protected List<Object> getPullData(String whereSql,HashMap<String,String> synParam) {
		// TODO Auto-generated method stub
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}
	/**
	 * 同步TM3对照关系
	 * @category 同步TM3对照关系
	 * <AUTHOR> 
	 * @param addList
	 */
	private void synSysComparison(List<EmployeeDto> addList) {
		if(StringUtils.isNotEmpty(addList)) {
			List<SysComparison> clist = new ArrayList<SysComparison>();
			for(EmployeeDto temp:addList) {
				SysComparison sbean = new SysComparison();
				sbean.setId(TMUID.getUID());
				sbean.setCode(null);
				sbean.setCodetm4(temp.getEmpTmuid());
				sbean.setCommodule("emp");
				sbean.setComtype("tm3");
				sbean.setUsed(1);
				clist.add(sbean);
			}
			synServer.saveSysComparison(clist,false);
		}
	}
}
