package com.yunhesoft.system.synchronous.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.synchronous.entity.dto.SynDataDto;
import com.yunhesoft.system.synchronous.entity.model.SynColumnCfg;
import com.yunhesoft.system.synchronous.entity.model.SynResult;

/**
 * 数据同步抽象类
 * <AUTHOR>
 */
public abstract class SynModel{
	
	protected String errorInfo="";//错误信息
	protected SynDataDto cfg;//接口采集配置信息
	protected SynClassType synType;//接口数据模型的类型
	protected String version = null;//版本信息
	protected String token = null; //token
	protected String tenantId = null; //租户id
	protected HashMap<String,String> synParam = new HashMap<String,String>();//接口采集参数列表
	public String getErrorInfo() {
		return errorInfo;
	}
	public void setErrorInfo(String errorInfo) {
		this.errorInfo = errorInfo;
	}
	public SynDataDto getCfg() {
		return cfg;
	}
	public void setCfg(SynDataDto cfg) {
		this.cfg = cfg;
	}
	public SynClassType getSynType() {
		return synType;
	}
	public void setSynType(SynClassType synType) {
		this.synType = synType;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public HashMap<String, String> getSynParam() {
		return synParam;
	}
	public void setSynParam(HashMap<String, String> synParam) {
		this.synParam = synParam;
	}
	/**
	 * 同步数据
	 * @category 同步数据
	 * <AUTHOR> 
	 * @param cfg 同步参数,具体内容见 SynDataDto 类注释
	 * @return String false开头代表执行失败
	 */
	public SynResult synData(SynDataDto cfg) {
		SynResult result = new SynResult();
		if(cfg!=null) {
			this.errorInfo="";
			this.cfg=cfg;
			checkCfg();//校验配置信息
			if (StringUtils.isNotEmpty(cfg.getColumnCfg())) {// 参数不为空
				if (StringUtils.isNotEmpty(cfg.getSynDataJson())) {
					List<HashMap<String,Object>> synDataList = new ArrayList<HashMap<String,Object>>();
					for(int i=0,j=cfg.getSynDataJson().size();i<j;i++) {
						List<HashMap<String,Object>> dataList = this.synDataAnalysis(cfg.getSynDataJson().get(i));//将外部接口数据根据转换配置转换成内部数据
						if (StringUtils.isNotEmpty(this.errorInfo)){//有错误信息
							result.setErrInfo(result.getErrInfo()+" page"+(i+1)+":"+this.errorInfo);
							this.errorInfo="";//清空
						}
						if (StringUtils.isNotEmpty(dataList)) {//有接口数据
							synDataList.addAll(dataList);
						}else {
							result.setErrInfo(result.getErrInfo()+" page"+(i+1)+"未解析到数据");
						}
					}
					if (StringUtils.isNotEmpty(synDataList)) {
						result.setTotal(synDataList.size());//总数
						List<HashMap<String,Object>> failureList = saveSynData(synDataList,this.synParam);
						if (StringUtils.isNotEmpty(failureList)) {
							result.setFailure(failureList.size());
							result.setSuccess(result.getTotal()-result.getFailure());//计算成功数量
							if(result.getSuccess()>0) {//有成功的数据
								result.setResult(true);
								result.setVersion(getVersion(cfg.getSynDataJson().get(cfg.getSynDataJson().size()-1)));//取最后一页数据的版本信息
							}else {//没有成功的数据
								result.setResult(false);
							}
						}else {
							result.setResult(true);
							result.setSuccess(synDataList.size());
							result.setFailure(0);
							result.setVersion(getVersion(cfg.getSynDataJson().get(cfg.getSynDataJson().size()-1)));//取最后一页数据的版本信息
						}
						if (StringUtils.isNotEmpty(this.errorInfo)) {
							result.setErrInfo(this.errorInfo);
						}
					}else {
						result.setResult(false);
						result.setErrInfo("未获取到接口数据。jsonData:"+JSON.toJSONString(cfg.getSynDataJson()));
					}
				}else {
					result.setResult(false);
					result.setErrInfo("接口未传入数据。jsonData:"+JSON.toJSONString(cfg.getSynDataJson()));
				}
			}else {
				result.setResult(false);
				result.setErrInfo("未传入字段对照信息。cfg:"+JSON.toJSONString(cfg));
			}
		}else {
			result.setResult(false);
			result.setErrInfo("未传入接口配置信息。");
		}
		return result;
	}
	/**
	 * 拉取数据
	 * @category 拉取数据
	 * <AUTHOR> 
	 * @param cfg 拉取参数,具体内容见 SynCollectionCfg 类注释
	 * @return String 返回 SynCollectionCfg.synTemplate中指定的内容（会替换其中的变量为实际数据） false开头代表执行失败
	 */
	public SynResult pullData(SynDataDto cfg) {
		SynResult result = new SynResult();
		if(cfg!=null) {
			this.errorInfo="";
			this.cfg=cfg;
			checkCfg();//校验配置信息
			if (StringUtils.isNotEmpty(cfg.getColumnCfg())) {// /传入了字段对照信息
				List<Object> dataList = getPullData(cfg.getWhereSql(),this.synParam);//根据时间段和其他参数获取要拉取的数据
				if (StringUtils.isNotEmpty(dataList)){//拉取到了数据
					if (StringUtils.isNotEmpty(this.errorInfo)) {
						result.setErrInfo(this.errorInfo);
					}
					this.pullDataAnalysis(dataList,result);
				}else {
					result.setResult(false);
					result.setErrInfo("未拉取到数据。cfg:"+JSON.toJSONString(cfg)+"。"+(this.errorInfo==null?"":this.errorInfo));
				}
			}else {
				result.setResult(false);
				result.setErrInfo("未传入字段对照信息。cfg:"+JSON.toJSONString(cfg));
			}
		}else {
			result.setResult(false);
			result.setErrInfo("未传入接口配置信息。");
		}
		return result;
	}
	
	/**
	 * 同步对照数据转换
	 * @category 同步对照数据转换
	 * <AUTHOR> 
	 * @param jsonData 接口数据
	 * @return List<HashMap<String,Object>>
	 */
	private List<HashMap<String,Object>> synDataAnalysis(String jsonData){
		//传过来一个数组
		//resok传递
		//返回version
		//定义返回对象
		//采集条件手写，不使用startDt
		List<HashMap<String,Object>> result = new ArrayList<HashMap<String,Object>>();
		if (StringUtils.isNotEmpty(jsonData)) {// /传入了字段对照信息
			try {
				String path = this.cfg.getSynDataPath();//获取数据路径
				if(path==null || path.length()==0) {
					path="/";//默认路径
				}else {
					path=path.trim();
				}
				String dataJson = null;//数据json
				Object jobj = JSON.parse(jsonData);
				String[] pathArr = path.split("/");//解析路径
//				List<String> pathList = new ArrayList<String>();
//				for(String temp:pathArr) {
//					if(temp!=null && temp.length()!=0) {//有路径
//						pathList.add(temp);
//					}
//				}
//				for(int i=0,j=pathList.size();i<j;i++){
//					jobj = getPathObj(jobj,pathList.get(i),(i+1==j?1:2));	
//				}
				for(String temp:pathArr) {
					if(temp!=null && temp.length()!=0) {//有路径
						jobj = getPathObj(jobj,temp,2);
					}
				}
				if(jobj!=null) {
					if(jobj instanceof JSONArray) {
						dataJson = ((JSONArray) jobj).toJSONString();
					} else if(jobj instanceof JSONObject) {
						dataJson="["+((JSONObject) jobj).toJSONString()+"]";
					} else{
						dataJson=jobj.toString();
					}
				}
				if(dataJson!=null) {
					JSONArray jarr = JSON.parseArray(dataJson);
					if(jarr!=null && jarr.size()>0) {
						for(int i=0,j=jarr.size();i<j;i++) {
							JSONObject obj =jarr.getJSONObject(i);
							HashMap<String,Object> dataMap = new HashMap<String,Object>();
							for(SynColumnCfg temp:this.cfg.getColumnCfg()) {
								dataMap.put(temp.getInsideColumn(), obj.get(temp.getOutsideColumn()));//根据对照字段生成生成返回数据
							}		
							result.add(dataMap);
						}			
					}
				}else {
					this.errorInfo = "路径解析错误。json:"+jsonData+" path:"+path;
				}
			}catch(Exception e) {
				this.errorInfo = e.getMessage();
			}
		}
		return result;
	}
	/**
	 * 获取同步数据版本
	 * @category 获取同步数据版本
	 * <AUTHOR> 
	 * @param jsonData 同步数据的版本
	 * @return
	 */
	private String getVersion(String jsonData) {
		String result = this.version;
		if (StringUtils.isEmpty(this.version)) {//如果没有在之前的数据处理过程中生成version，那么就通过version路径获取version
			if (StringUtils.isNotEmpty(this.cfg.getSynVersionPath())) {//有version路径，则解析路径
				if (StringUtils.isNotEmpty(jsonData)){//json有效
					try {
						String path = this.cfg.getSynVersionPath();//获取version路径
						if (StringUtils.isNotEmpty(path)) {
							Object jobj = JSON.parse(jsonData);
							String[] pathArr = path.split("/");//解析路径
							for(String temp:pathArr) {
								if(temp!=null && temp.length()!=0) {//有路径
									jobj = getPathObj(jobj,temp,2);
								}
							}
							if(jobj!=null) {
								result=jobj.toString();
							}
						}
					}catch(Exception e) {
						this.errorInfo = e.getMessage();
					}				
				}
			}
		}
		return result;
	}
	/**
	 * 获取路径对象
	 * @category 
	 * <AUTHOR> 
	 * @param jobj
	 * @param path
	 * @param type 1路径取到JSONArray时，取JSONArray中的第一个记录。2路径取到JSONArray时，取JSONArray中的第一个记录的属性值
	 * @return
	 */
	private Object getPathObj(Object jobj,String path,int type) {
		Object result = null;
		if(jobj!=null) {
			if(jobj instanceof JSONArray) {
				JSONArray objArr = (JSONArray) jobj;
				if(objArr.size()>0) {
					if(type==1) {//取数组中的第一个记录
						result = objArr.get(0);
					}else {
						result = getPathObj(objArr.get(0),path,type);//取数组中第一个记录的path属性值
					}
				}
			} else if(jobj instanceof JSONObject) {
				result = ((JSONObject) jobj).get(path);
			} else {
				result = jobj;
			}
		}
		return result;
	}

	/**
	 * 拉取数据转换成推送json
	 * @category 
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	private void pullDataAnalysis(List<Object> dataList,SynResult result) {
		
		if(dataList!=null && dataList.size()>0) {//有数据
			try {
				List<HashMap<String,Object>> returnDataList = new ArrayList<HashMap<String,Object>>();			
				String json = JSON.toJSONString(dataList);
				JSONArray jarr = JSON.parseArray(json);
				if(jarr!=null && jarr.size()>0) {
					for(int i=0,j=jarr.size();i<j;i++) {
						JSONObject obj =jarr.getJSONObject(i);
						HashMap<String,Object> dataMap = new HashMap<String,Object>();
						for(SynColumnCfg temp:this.cfg.getColumnCfg()) {
							dataMap.put(temp.getOutsideColumn(), obj.get(temp.getInsideColumn()));//根据对照字段生成生成返回数据
						}				
						returnDataList.add(dataMap);
					}
					if (StringUtils.isNotEmpty(this.cfg.getPullTemplate())){//设置了返回模板
						String pullData = this.cfg.getPullTemplate();
						pullData = pullData.replace("[@nowDt]", DateTimeUtils.getNowDateTimeStr());//替换数据位置	
						pullData = pullData.replace("[@data]", JSON.toJSONString(returnDataList));//替换数据位置	
						result.setPullData(pullData);
					}else{//未设置，直接返回数据json数组		
						result.setPullData(JSON.toJSONString(returnDataList));
					}
					result.setTotal(returnDataList.size());
					result.setResult(true);					
				}
			}catch(Exception e) {
				result.setResult(false);	
				result.setErrInfo(e.getMessage()+(this.errorInfo==null?"":this.errorInfo));
			}
		}
	}
	/**
	 * 对配置信息进行校验
	 * @category 
	 * <AUTHOR>
	 */
	private void checkCfg() {
		//整理对照字段信息
		if (StringUtils.isNotEmpty(cfg.getColumnCfg())) {//传入了字段对照信息
			List<SynColumnCfg> removeList = new ArrayList<SynColumnCfg>();
			for(SynColumnCfg temp:this.cfg.getColumnCfg()) {
				int sign = 0;
				if (StringUtils.isEmpty(temp.getInsideColumn())) {//为空
					sign+=1;
				}
				if (StringUtils.isEmpty(temp.getOutsideColumn())) {//为空
					sign+=10;
				}
				if(sign==11) {//2个对照都没有
					removeList.add(temp);
				}else if(sign==1) {//没有内部对照字段
					temp.setInsideColumn(temp.getOutsideColumn());
				}else if(sign==10) {//没有外部对照字段
					temp.setOutsideColumn(temp.getInsideColumn());
				}
			}
			if(removeList.size()>0) {
				this.cfg.getColumnCfg().removeAll(removeList);
			}
		}
		//解析同步参数
		if (StringUtils.isNotEmpty(cfg.getSynParam())) {//参数不为空，进行参数解析
			try {
				Object jobj = JSON.parse(this.cfg.getSynParam());//参数只支持JSONObject模式
				if(jobj!=null) {
					if(jobj instanceof JSONObject) {
						JSONObject obj = (JSONObject) jobj;
						for(Entry<String,Object> temp:obj.entrySet()) {
							if(StringUtils.isNotEmpty(temp.getKey()) && temp.getValue()!=null) {//参数有效
								this.synParam.put(temp.getKey(), String.valueOf(temp.getValue()));
							}
						}
					}
				}
			}catch(Exception e) {
				this.errorInfo = e.getMessage();
			}		
		}
	}

	/**
	 * 保存接口采集的数据
	 * @category 保存接口采集的数据
	 * <AUTHOR> 
	 * @param dataList
	 * @param synParam 其他参数
	 * @return List<HashMap<String,Object>> 需要将dataList中处理失败的记录返回，用于后续接口错误判断，错误恢复等功能
	 */
	protected abstract List<HashMap<String,Object>> saveSynData(List<HashMap<String,Object>> dataList,HashMap<String,String> synParam);
	
	/**
	 * 获取接口拉取的数据
	 * @category 获取接口拉取的数据
	 * <AUTHOR> 
	 * @param whereSql 拉取sql
	 * @param synParam 其他参数
	 * @return
	 */
	protected abstract List<Object> getPullData(String whereSql,HashMap<String,String> synParam);
	
}
