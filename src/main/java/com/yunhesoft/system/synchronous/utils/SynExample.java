package com.yunhesoft.system.synchronous.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Random;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yunhesoft.core.utils.StringUtils;

@Service
public class SynExample extends SynModel {

	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList,HashMap<String,String> synParam) {
		// TODO Auto-generated method stub
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>();
		if(StringUtils.isNotEmpty(dataList)) {//非空
			System.out.println("同步开始：数据数量"+dataList.size()+" -----------------------------------------------");
			for(HashMap<String, Object> temp:dataList) {
				int i = new Random().nextInt(5);
				if(i>3) {//同步成功
					//保存数据
					System.out.println("同步成功："+JSON.toJSONString(temp));
				}else {//同步失败
					//返回失败的数据
					result.add(temp);
					System.out.println("同步失败："+JSON.toJSONString(temp));
				}
			}
			System.out.println("同步结束：成功"+(dataList.size()-result.size())+" 失败"+result.size()+" -----------------------------------------------");
		}else {
			System.out.println("未解析到同步数据"+" -----------------------------------------------");
		}
		return result;
		/* 下面是接口调用json示例 synCode=SynModelFactory中注册过的类型 不区分大小写
		{
		    "columnCfg": [
		      {
		        "insideColumn": "X",
		        "outsideColumn": "M",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "Y",
		        "outsideColumn": "N",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      }
		    ],
		  "pullTemplate": "",
		  "synCode": "Example",
		  "synDataJson": ["{'a':'version1002','b':{c:2,d:[{'M':1,'N':'是'},{'M':2,'N':'否'}]}}","{'a':'version1001','b':{c:2,d:[{'M':3,'N':'上'},{'M':4,'N':'下'}]}}"],
		  "synDataPath": "/b/d",
		  "synDesc": "",
		  "synParam": "",
		  "synVersionPath": "/a",
		  "tableName": "",
		  "whereSql": ""
		}
		*/
		
	}

	@Override
	protected List<Object> getPullData(String whereSql,HashMap<String,String> synParam) {
		// TODO Auto-generated method stub
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}

}
