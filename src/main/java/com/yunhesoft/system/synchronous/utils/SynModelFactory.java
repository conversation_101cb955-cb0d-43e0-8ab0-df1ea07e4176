package com.yunhesoft.system.synchronous.utils;

import java.util.Hashtable;

import com.yunhesoft.core.utils.spring.SpringUtils;

import lombok.extern.log4j.Log4j2;
/**
 * 数据模型生产工厂
 * <AUTHOR>
 */
@Log4j2
public class SynModelFactory {
	
	private static Hashtable<SynClassType,Class<?>> dataClass = new Hashtable<SynClassType,Class<?>>();//接口实体类名
	/**
	 * 获取数据模型导入导出实体类列表
	 * @category 获取数据模型导入导出实体类列表
	 * @return Hashtable<SynClassType,String>  SynClassType:模型数据类型 String:模型数据类文件路径
	 */
	private static Hashtable<SynClassType,Class<?>>  getDataModel(){
		if(dataClass.isEmpty()){
			//	dataClass.put(SynClassType.Emp, "com.yunhesoft.system.synchronous.utils.SynSqlServerData");//同步SqlServer数据
			dataClass.put(SynClassType.Example, SynExample.class);//同步样例	
			dataClass.put(SynClassType.Org, SynOrg.class);//同步机构
			dataClass.put(SynClassType.Emp, SynEmp.class);//同步人员
			dataClass.put(SynClassType.Sql, SynSqlData.class);//通用同步接口
		}
		return dataClass;
	}
	
	/**
	 * 数据模型类实例化
	 * @param className  (String) 类对象
	 * @return DataModel 数据模型
	 */
	public static SynModel getInstance(String className){
		SynModel result = null;
		if(className!=null && className.length()!=0){
			if(className.indexOf(".")>=0) {//class路径模式			
				try{			
					Class<?> c = Class.forName(className);
					if(c!=null) {
						result = (SynModel) SpringUtils.getBean(c);
					}
//					result = (SynModel)c.newInstance();//实例化具体接口类
				}catch(Exception e){
					log.error("", e);
				}
			}else {
				SynClassType synType =SynClassType.get(className);
				if(synType!=null){//是程序已经定义了的导入导出类
					try{			
						Class<?> c = getDataModel().get(synType);
						result = (SynModel) SpringUtils.getBean(c);
	//					String ClassName = getDataModel().get(synType);//获得接口类
	//					Class<?> c = Class.forName(ClassName);
	//					result = (SynModel)c.newInstance();//实例化具体接口类
						result.setSynType(synType);//设置数据类型
					}catch(Exception e){
						log.error("", e);
					}
				}
			}
		}
		return result;
	}
}
