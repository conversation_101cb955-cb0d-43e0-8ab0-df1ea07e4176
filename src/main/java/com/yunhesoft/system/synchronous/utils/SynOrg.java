package com.yunhesoft.system.synchronous.utils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.common.utils.Tree;
import com.yunhesoft.core.common.utils.TreeNode;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.org.entity.dto.SysOrgAdd;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.synchronous.entity.po.SysComparison;
import com.yunhesoft.system.synchronous.service.ISynchronousService;

@Service
public class SynOrg extends SynModel {
	
	@Autowired
	private ISysOrgService orgServer;
	@Autowired
	private ISynchronousService synServer;
	
	private String rootId = synParam.get("rootId");//外部系统机构根节点ID
	private String synDelFlagValue = synParam.get("synDelFlagValue");//外部数据同步删除标识值
	private boolean rootSyn = true;//是否要同步外部系统的根节点

	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList,HashMap<String,String> synParam) {
		// TODO Auto-generated method stub
		//System.out.println(JSON.toJSONString(dataList));
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>();//失败返回的数据		
		this.rootId = synParam.get("rootId");//外部系统机构根节点ID
		this.synDelFlagValue = synParam.get("synDelFlagValue");//外部数据同步删除标识值
		if(StringUtils.isEmpty(this.synDelFlagValue)) {
			this.synDelFlagValue="1";//默认标识1为删除
		}
		String rootSynStr = synParam.get("rootSyn");//是否要同步外部系统的根节点
		if("false".equals(rootSynStr)) {
			this.rootSyn = false;
		}
		boolean synTM3 = false;//是否要同步到TM3对照表
		String synTM3Str = synParam.get("synTM3");//是否要同步到TM3对照表
		if("true".equals(synTM3Str)) {
			synTM3 = true;
		}
		if(StringUtils.isNotEmpty(rootId)) {
			LinkedHashMap<String,HashMap<String, Object>> dataMap= new LinkedHashMap<String,HashMap<String, Object>>();
			List<SysOrgAdd> synDataList = new ArrayList<SysOrgAdd>();//执行同步的机构
			List<SysOrgAdd> successDataList = new ArrayList<SysOrgAdd>();//执行同步的机构
			for(HashMap<String, Object> temp:dataList) {
				SysOrgAdd synOrg = ObjUtils.convertToObject(SysOrgAdd.class, temp);//由map生成机构类
				if(synOrg!=null && StringUtils.isNotEmpty(synOrg.getId())) {//数据读取成功，有机构id
					dataMap.put(synOrg.getId(), temp);//存放进map，用于后续确定是否同步成功
					synDataList.add(synOrg);		
				}else {
					result.add(temp);//记录到失败数据中
				}
			}
			if(synDataList.size()>0) {
				HashMap<String,TreeNode<SysOrg>> treeNodeMap = new HashMap<String,TreeNode<SysOrg>>();
				TreeNode<SysOrg> rootNode = null;
				List<TreeNode<SysOrg>> treeNodeList = new ArrayList<TreeNode<SysOrg>>();
				List<SysOrg> orgList =orgServer.getOrgList("root");//获取系统内的机构(不从缓存走，走数据库)
				if(StringUtils.isEmpty(orgList)) {//没有任何节点
					SysOrg root = orgServer.initOrg("机构");
					rootNode = new TreeNode<SysOrg>(root.getOrgpath(),"root",root);
					treeNodeList.add(rootNode);
					treeNodeMap.put(root.getId(), rootNode);
				}else{
					for(SysOrg temp:orgList){
						if (StringUtils.isNotEmpty(temp.getOrgpath())) {//有路径
							if(temp.getOrglevel()!=null && temp.getOrglevel().intValue()==0) {//这个是根节点
								rootNode = new TreeNode<SysOrg>(temp.getOrgpath(),"root",temp);
								treeNodeList.add(rootNode);
								treeNodeMap.put(temp.getId(), rootNode);
							}else {
								String pid = temp.getOrgpath().replaceAll("/"+temp.getId()+"$", "");//替换末尾的
								TreeNode<SysOrg> node = new TreeNode<SysOrg>(temp.getOrgpath(),pid,temp);
								treeNodeList.add(node);
								treeNodeMap.put(temp.getId(), node);
							}
						}
					}
				}
				Tree<SysOrg> orgTree= new Tree<SysOrg>(rootNode);
				orgTree.buildTree(treeNodeList);//构建树形(这里会构建树节点的关系，构建后，可以直接在treeNodeMap直接获取节点的下级节点数量。为了容错，不能使用tree自带的map，tree自带map去掉了冗余节点)
				if(rootNode!=null) {

					Map<String, SysOrgAdd> synOrgMap = synDataList.stream().collect(Collectors.toMap(SysOrgAdd::getId,
							SysOrgAdd -> SysOrgAdd, (key1, key2) -> key1));//同步数据map
					List<TreeNode<SysOrgAdd>> synTreeNodeList = new ArrayList<TreeNode<SysOrgAdd>>();
					TreeNode<SysOrgAdd> synRootNode = new TreeNode<SysOrgAdd>("root",null,null);
					synTreeNodeList.add(synRootNode);
					for(SysOrgAdd temp:synDataList) {
						if(rootId.equals(temp.getId())) {//外部数据根节点
							if(rootSyn){//同步根节点
								temp.setPorgcode(rootNode.bean.getId());//父节点是根节点
							}else {
								successDataList.add(temp);//不同步代表该节点就同步成功了，不报错
								continue;//不同步根节点就跳过
							}
						}else {					
							if(!rootSyn){//不同步根节点
								if(rootId.equals(temp.getPorgcode())) {//不同步根节点，需要把根节点下层的数据接到系统跟节点上
									temp.setPorgcode(rootNode.bean.getId());//父节点是根节点
								}
							}
						}
						String pid=temp.getPorgcode();//父节点
						SysOrgAdd porg = synOrgMap.get(temp.getPorgcode());
						if(porg==null){//在同步数据中没有上级节点
							pid="root";//直接接到root节点上
						}
						TreeNode<SysOrgAdd> node = new TreeNode<SysOrgAdd>(temp.getId(),pid,temp);
						synTreeNodeList.add(node);
					}
					List<SysOrgAdd> synComparisonList = new ArrayList<SysOrgAdd>();//对照列表
					Tree<SysOrgAdd> synTree= new Tree<SysOrgAdd>(synRootNode);
					synTree.buildTree(synTreeNodeList);//构建树形
					if(StringUtils.isNotEmpty(synRootNode.childNodes)) {//有子节点
						LinkedHashMap<String,TreeNode<SysOrgAdd>> groupMap = new LinkedHashMap<String,TreeNode<SysOrgAdd>>();
						for(TreeNode<SysOrgAdd> temp:synRootNode.childNodes) {
							TreeNode<SysOrgAdd> groupParent = groupMap.get(temp.bean.getPorgcode());//获取共有的父节点		
							if(groupParent==null){
								TreeNode<SysOrg> pOrg = treeNodeMap.get(temp.bean.getPorgcode());
								if(pOrg!=null) {//找到了机构的父节点
									SysOrgAdd orgAdd= ObjUtils.copyTo(pOrg.bean, SysOrgAdd.class);
									groupParent = new TreeNode<SysOrgAdd>(orgAdd.getId(),"",orgAdd);//构建节点类
									groupMap.put(temp.bean.getPorgcode(), groupParent);
								}
							}	
							if(groupParent!=null) {//找到了父节点
								groupParent.appendChild(temp);//添加到父节点
							}
						}
						for(TreeNode<SysOrgAdd> node : groupMap.values()) {
							synOrg(node,treeNodeMap,successDataList,synComparisonList);//同步机构数据
						}
					}
					if(synTM3) {//需要同步TM3
						this.synSysComparison(synComparisonList);
					}
					Date maxDt = null;
					for(SysOrgAdd temp:successDataList) {
						Date dt= parseDateTime(temp.getVersionDate());
						if(dt!=null) {//有日期
							if(maxDt==null) {
								maxDt=dt;
							}else {
								if(DateTimeUtils.bjDate(dt, maxDt)>0) {//新的日期比当前最大的大
									maxDt=dt;
								}
							}
						}
					}
					if(maxDt!=null) {
						this.version = DateTimeUtils.formatDateTime(maxDt);//记录同步成功的数据的最大版本
					}
					synDataList.removeAll(successDataList);//将成功的数据移除
					if(synDataList.size()>0) {//有失败的数据
						for(SysOrgAdd temp:synDataList) {
							HashMap<String, Object> fData = dataMap.get(temp.getId());//获取失败的原始数据
							if(fData!=null) {
								result.add(fData);//添加到失败记录
							}
						}
					}			
				}else {//没查到根节点，无法同步数据
					result.clear();
					result.addAll(dataList);//所有数据都失败了
					this.errorInfo="未找到系统机构根节点，无法同步机构数据";
				}
			}
		}else {
			this.errorInfo="未指定外部数据根节点ID，无法同步机构数据";
		}
		return result;
	}
	/**
	 * 递归处理数据
	 * @category 
	 * <AUTHOR>
	 */
	private void synOrg(TreeNode<SysOrgAdd> node,HashMap<String,TreeNode<SysOrg>> treeNodeMap,List<SysOrgAdd> successDataList,List<SysOrgAdd> synComparisonList){
		if(StringUtils.isNotEmpty(node.childNodes)) {//没有任何节点
			int sort = 0;//排序
			int orgLevel = 0;//机构级别
			TreeNode<SysOrg> org = treeNodeMap.get(node.bean.getId());
			if(org!=null && StringUtils.isNotEmpty(org.childNodes)) {//有子节点
				for(TreeNode<SysOrg> temp:org.childNodes) {
					if(temp.bean.getTmSort()!=null && temp.bean.getTmSort().intValue()>sort) {
						sort=temp.bean.getTmSort().intValue();//查找最大排序值
					}
				}
			}
			if(node.bean.getOrglevel()!=null) {
				orgLevel=node.bean.getOrglevel().intValue();
			}
			//下级节点，机构级别加1
			orgLevel++;
			List<SysOrgAdd> insertList = new ArrayList<SysOrgAdd>();
			List<TreeNode<SysOrgAdd>> insertTreeList = new ArrayList<TreeNode<SysOrgAdd>>();
			List<SysOrgAdd> updateList = new ArrayList<SysOrgAdd>();	
			List<TreeNode<SysOrgAdd>> updateTreeList = new ArrayList<TreeNode<SysOrgAdd>>();
			List<TreeNode<SysOrgAdd>> deleteTreeList = new ArrayList<TreeNode<SysOrgAdd>>();	
			for(TreeNode<SysOrgAdd> temp:node.childNodes) {
				temp.bean.setOrgpath(node.bean.getOrgpath()+"/"+temp.bean.getId());//生成路径
				temp.bean.setOrglevel(orgLevel);
				TreeNode<SysOrg> synOrg = treeNodeMap.get(temp.bean.getOrgcode());
				if(synOrg==null) {//没有数据
					if(synDelFlagValue.equals(temp.bean.getSynDelFlag())){//是删除
						//不用处理了
						successDataList.add(temp.bean);//等于是成功了，不需要进数据库
					}else {
						if(temp.bean.getTmSort()==null || temp.bean.getTmSort().intValue()<=1) {//没同步排序，需要自己计算
							temp.bean.setTmSort(++sort);
						}
						insertList.add(temp.bean);
						insertTreeList.add(temp);
					}			
				}else {//有数据
					if(synDelFlagValue.equals(temp.bean.getSynDelFlag())){//是删除
						deleteTreeList.add(temp);
					}else {
						if(temp.bean.getTmSort()!=null && temp.bean.getTmSort().intValue()>1) {//同步了排序，需要更新排序
							temp.bean.setUpdateSort(true);//需要更新排序
						}
						updateList.add(temp.bean);
						updateTreeList.add(temp);					
					}
				}
			}
			List<TreeNode<SysOrgAdd>> subList = new ArrayList<TreeNode<SysOrgAdd>>();
			if(insertList.size()>0) {//新建
				boolean result = orgServer.insertData(insertList);//插入机构
				if(result) {//成功
					synComparisonList.addAll(insertList);//增加到对照列表
					successDataList.addAll(insertList);//成功
					subList.addAll(insertTreeList);//成功就处理子节点
				}
			}
			if(updateList.size()>0) {//更新
				boolean result = orgServer.updateData(updateList);//更新机构
				if(result) {//成功
					successDataList.addAll(updateList);//成功
				}
				subList.addAll(updateTreeList);//更新无论是否成功，都处理子节点
			}
			if(deleteTreeList.size()>0) {//删除
				for(TreeNode<SysOrgAdd> temp:deleteTreeList) {
					//SysOrgDel del = ObjUtils.copyTo(temp.bean, SysOrgDel.class);
					//if(orgServer.deleteData(del)){
						successDataList.add(temp.bean);//成功
					//}
				}	
				subList.addAll(deleteTreeList);//删除无论是否成功，都处理子节点
			}			
			if(subList.size()>0) {//递归处理下级节点
				for(TreeNode<SysOrgAdd> temp:subList) {
					synOrg(temp,treeNodeMap,successDataList,synComparisonList);
				}
			}
		}
	}
	
	@Override
	protected List<Object> getPullData(String whereSql,HashMap<String,String> synParam) {
		// TODO Auto-generated method stub
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}

	/**
	 * 解析输出日期类型数据
	 * 
	 * @param dtStr 日期字符串 格式：yyyy-MM-dd HH-mm-ss
	 * @return DATE
	 */
	private Date parseDateTime(String dtStr) {
		Date result = null;
		if(StringUtils.isNotEmpty(dtStr)) {//有子节点
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			try {		
				result = sdf.parse(dtStr);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return result;
	}
	/**
	 * 同步TM3对照关系
	 * @category 同步TM3对照关系
	 * <AUTHOR> 
	 * @param addList
	 */
	private void synSysComparison(List<SysOrgAdd> addList) {
		if(StringUtils.isNotEmpty(addList)) {
			List<SysComparison> clist = new ArrayList<SysComparison>();
			for(SysOrgAdd temp:addList) {
				SysComparison sbean = new SysComparison();
				sbean.setId(TMUID.getUID());
				sbean.setCode(null);
				sbean.setCodetm4(temp.getId());
				sbean.setCommodule("org");
				sbean.setComtype("tm3");
				sbean.setUsed(1);
				clist.add(sbean);
			}
			synServer.saveSysComparison(clist,false);
		}
	}
}
