package com.yunhesoft.system.synchronous.utils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.synchronous.entity.model.SynColumnCfg;

import lombok.extern.log4j.Log4j2;
@Log4j2
@Service
public class SynSqlData extends SynModel {
	@Autowired
	private EntityService entityService;
	private int batchSize=1000;//批量执行语句的条数
	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList,HashMap<String,String> synParam) {
		String  batchSizeStr=synParam.get("batchSize");//处理批量页数参数
		if(Coms.judgeInt(batchSizeStr)) {
			batchSize=Integer.parseInt(batchSizeStr);
			if(batchSize<=0) {
				batchSize=1000;
			}
		}
		// TODO Auto-generated method stub
		//System.out.println(JSON.toJSONString(dataList));
		//insert into xxx(a,b,c) values (?,?,?);
		log.info("接口"+this.cfg.getSynCode()+"开始执行");
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>();//失败返回的数据	
		if(StringUtils.isNotEmpty(this.cfg.getTableName())) {//是否传入表名
			try {
				if(StringUtils.isNotEmpty(this.cfg.getWhereSql())) {//有删除条件
					boolean deleteAll =false;
					List<Object> delBatchArgs = new ArrayList<Object>();
					StringBuffer delSql = new StringBuffer();
					delSql.append("delete from ").append(this.cfg.getTableName());
					String whereSql = this.cfg.getWhereSql();
					if(StringUtils.isNotEmpty(whereSql)) {
						whereSql=whereSql.trim();
						Pattern pall = Pattern.compile("@all\\(([-\\d\\s]*)\\)", Pattern.CASE_INSENSITIVE);
						Matcher mall = pall.matcher(whereSql);
						if (mall.find()) {
							//需要删除全部数据
							deleteAll=true;
						}else{
							Pattern p = Pattern.compile("^(where)", Pattern.CASE_INSENSITIVE);
							Matcher m = p.matcher(whereSql);
							if (!m.find()) {
								whereSql = " where " + whereSql;
							}
							Pattern pm = Pattern.compile("@month\\(([-\\d\\s]*)\\)", Pattern.CASE_INSENSITIVE);
							Matcher mm = pm.matcher(whereSql);
							while(mm.find()){  
								int monthAdd=0;
								String add = mm.group(1).replaceAll("\\s", "");
								if(Coms.judgeInt(add)) {
									monthAdd=Integer.parseInt(add);
								}
								Date nowDt = DateTimeUtils.getNowDate();//当前日期					
								if(monthAdd!=0) {
									nowDt=DateTimeUtils.doMonth(nowDt, monthAdd);
								}
								//mm.appendReplacement(delSql,"'"+DateTimeUtils.format(nowDt,DateTimeUtils.DateFormat_YM)+"'");//替换已经查找到的数据为具体月份
								mm.appendReplacement(delSql,"?");//替换已经查找到的数据为具体月份
								delBatchArgs.add(DateTimeUtils.format(nowDt,DateTimeUtils.DateFormat_YM));
							}
							mm.appendTail(delSql);//将剩下未匹配的尾部字符添加进StringBuffer
							
							Pattern pd = Pattern.compile("@day\\(([-\\d\\s]*)\\)", Pattern.CASE_INSENSITIVE);
							Matcher md = pd.matcher(delSql.toString());
							delSql = new StringBuffer();
							while(md.find()){
								int dayAdd=0;
								String add = md.group(1).replaceAll("\\s", "");
								if(Coms.judgeInt(add)) {
									dayAdd=Integer.parseInt(add);
								}
								Date nowDt = DateTimeUtils.getNowDate();//当前日期					
								if(dayAdd!=0) {
									nowDt=DateTimeUtils.doDate(nowDt, dayAdd);
								}
//								md.appendReplacement(delSql,"'"+DateTimeUtils.format(nowDt,DateTimeUtils.DateFormat_YMD)+"'");//替换已经查找到的数据为具体月份
								md.appendReplacement(delSql,"?");//替换已经查找到的数据为具体月份
								delBatchArgs.add(DateTimeUtils.format(nowDt,DateTimeUtils.DateFormat_YMD));
							}
							md.appendTail(delSql);//将剩下未匹配的尾部字符添加进StringBuffer					
						}
			
						if(deleteAll || delBatchArgs.size()>0) {//必须有时间参数，防止注入
							List<Object[]> delArgs = new ArrayList<Object[]>();
							delArgs.add(delBatchArgs.toArray());
							entityService.deleteBatch(delSql.toString(), delArgs);
							log.info("删除历史数据完毕。相关语句："+delSql.toString()+",参数："+JSON.toJSONString(delArgs));
						}
						
//						if(deleteAll || delBatchArgs.size()>0) {//必须有时间参数，防止注入
//							if(res<=0) {//这个不行，删除不到数据也回0
//								this.errorInfo += "历史数据删除失败,请手动处理,同步停止;";
//							//	result.addAll(dataList);//全失败
//							//	return result;
//							}else {
//								log.info("删除历史数据成功。相关语句："+delSql.toString()+",参数："+JSON.toJSONString(delArgs));
//							}
//						}
					}
				}
			}catch(Exception e) {
				this.errorInfo += e.getMessage();
				result.addAll(dataList);//全失败
				return result;
			}
			Timestamp nowDt = new Timestamp(DateTimeUtils.getNowDate().getTime());//当前采集数据时间
			List<SynColumnCfg> colunmList  = new ArrayList<SynColumnCfg>();
			StringBuffer sql = new StringBuffer();
			sql.append("insert into ").append(this.cfg.getTableName()).append("(ID,CREATE_TIME");
			boolean hasID=false;
			SynColumnCfg idCfg = null;//id的配置信息
			boolean hasCt=false;
			SynColumnCfg ctCfg = null;//创建时间的配置信息
			StringBuffer paramSql = new StringBuffer("?,?");
			for(SynColumnCfg temp:this.cfg.getColumnCfg()) {
				if("id".equalsIgnoreCase(temp.getInsideColumn())){
					//id不处理
					hasID=true;
					idCfg=temp;
				}else if("create_time".equalsIgnoreCase(temp.getInsideColumn())){
					//create_time不处理
					hasCt=true;
					ctCfg=temp;
				}else {//只处理非ID和CREATE_TIME列
					sql.append(","+temp.getInsideColumn());
					paramSql.append(",?");
					colunmList.add(temp);
				}
			}
			if(hasCt) {
				colunmList.add(0,ctCfg);//把create_time放在第二位(这个顺序重要！！必须先插入create_time，再插入id)
			}else {
				ctCfg = new SynColumnCfg();
				ctCfg.setInsideColumn("CREATE_TIME");
				ctCfg.setSynDataType(SynColumnCfg.synDataType_date);
				colunmList.add(0,ctCfg);
			}
			if(hasID) {
				colunmList.add(0,idCfg);//把ID放在第一位
			}else {
				idCfg = new SynColumnCfg();
				idCfg.setInsideColumn("ID");
				colunmList.add(0,idCfg);
			}
			sql.append(") values (").append(paramSql).append(")");
			List<List<HashMap<String, Object>>> batchInputList = new ArrayList<List<HashMap<String, Object>>>();
			List<List<Object[]>> batchList = new ArrayList<List<Object[]>>();
			List<Object[]> batchArgs = new ArrayList<Object[]>();
			List<HashMap<String, Object>> inpuitDatas = new ArrayList<HashMap<String, Object>>();
			log.info("开始同步数据，共"+dataList.size()+"条");
			for(HashMap<String, Object> temp:dataList) {
				List<Object> args = new ArrayList<Object>();
//				if(!hasID) {//未同步ID,自动添加ID
//					args.add(TMUID.getUID());
//				}
//				if(!hasCt) {//未同步create_time,自动添加create_time
//					args.add(nowDt);
//				}
				for(SynColumnCfg column:colunmList) {
					Object valObj = temp.get(column.getInsideColumn());
					if(StringUtils.isNotEmpty(column.getSynDataType())) {//非空
						if(SynColumnCfg.synDataType_date.equalsIgnoreCase(column.getSynDataType())) {//日期
							Timestamp ts = null;
							if(valObj!=null) {
								String dtStr = DateTimeUtils.formNonStandardDateTime(String.valueOf(valObj));
								Date dt = DateTimeUtils.parseDate(dtStr);
								if (dt != null) {
									ts = new Timestamp(dt.getTime());
								} 
							}else {
								if("create_time".equalsIgnoreCase(column.getInsideColumn())){//创建时间
									ts = nowDt;
								}
							}	
							args.add(ts);
						}else if(SynColumnCfg.synDataType_number.equalsIgnoreCase(column.getSynDataType())) {//数值型
							Double val = null;
							if(Coms.judgeDouble(valObj)) {
								val = Double.parseDouble(String.valueOf(valObj));
							}
							args.add(val);
						}else {//其他都当字符型
							String val = null;
							if(valObj!=null) {
								val =String.valueOf(temp.get(column.getInsideColumn()));
							}else {
								if("id".equalsIgnoreCase(column.getInsideColumn())){
									val = TMUID.getUID();
								}
							}	
							args.add(val);
						}
					}else {
						if(valObj==null && "id".equalsIgnoreCase(column.getInsideColumn())) {//ID列空值时赋值
							args.add(TMUID.getUID());
						}else {
							args.add(valObj);//使用数据原始类型
						}	
					}
				}
				batchArgs.add(args.toArray());
				inpuitDatas.add(temp);
				if(batchArgs.size()>=batchSize) {//1000条处理一次
					batchList.add(batchArgs);
					batchArgs = new ArrayList<Object[]>(); 
					batchInputList.add(inpuitDatas);
					inpuitDatas = new ArrayList<HashMap<String, Object>>();
				}
			}
			if(batchArgs.size()>0) {
				batchList.add(batchArgs);//剩余数据都添加到处理list中
				batchInputList.add(inpuitDatas);
			}
			
			for(int i=0,j=batchList.size();i<j;i++) {			
				List<Object[]> addArr = batchList.get(i);
				String info = "第"+(i+1)+"组数据("+(i*batchSize+1)+"~"+(i*batchSize+addArr.size())+")插入";			
				try {
					int[] saveResult = entityService.rawInsertBatch(sql.toString(), addArr);
					if(saveResult!=null && saveResult.length>0 && saveResult[0]>0) {//成功
						//成功
						info+="成功;";
					}else {
						info+="失败;";
						this.errorInfo += info ;
						result.addAll(batchInputList.get(i));//全失败了
					}
				}catch(Exception e) {
					info+="失败;";
					this.errorInfo += e.getMessage();
					result.addAll(batchInputList.get(i));//全失败了
				}
				log.info(info);					
			}
			
			log.info("同步数据结束");
		}else{
			this.errorInfo += "未指定数据同步表名;";
		}
		return result;
	}

	@Override
	protected List<Object> getPullData(String whereSql,HashMap<String,String> synParam) {
		// TODO Auto-generated method stub
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}
//例子：
//	{
//	    "columnCfg": [
//	    {
//	        "insideColumn": "empTmuid",
//	        "outsideColumn": "userId",
//	        "synDataType": "",
//	        "synDesc": "",
//	        "synParam": ""
//	      },{
//	        "insideColumn": "changeDt",
//	        "outsideColumn": "modifyTime",
//	        "synDataType": "date",
//	        "synDesc": "",
//	        "synParam": ""
//	      },{
//	        "insideColumn": "postid",
//	        "outsideColumn": "postId",
//	        "synDataType": "number",
//	        "synDesc": "",
//	        "synParam": ""
//	      },{
//	        "insideColumn": "postname",
//	        "outsideColumn": "postName",
//	        "synDataType": "",
//	        "synDesc": "",
//	        "synParam": ""
//	      }
//	    ],
//	  "pullTemplate": "",
//	  "synCode": "sql",
//	  "synDataJson": ["
//{
//\"code\": \"200\",
//\"msg\": \"查询成功\",
//\"version\": \"1.0.0\",
//\"startPage\": \"2\",
//\"hasMore\": \"1\",
//\"total\": \"2148\",
//\"rows\": [
//    {
//        \"modifyTime\": \"2023-05-12 08:32:15\",
//        \"userRealName\": \"徐丽\",
//        \"postName\": \"硫磺回收内操\",
//        \"postId\": \"252\",
//        \"userId\": \"233\"
//    },
//    {
//        \"modifyTime\": \"2023-06-12 08:32:15\",
//        \"userRealName\": \"宋晓昱\",
//        \"postName\": \"硫磺回收内操\",
//        \"postId\": \"252\",
//        \"userId\": \"231\"
//    },
//    {
//        \"modifyTime\": \"2023-07-12 08:32:15\",
//        \"userRealName\": \"宋晓峰\",
//        \"postName\": \"工艺工程师\",
//        \"postId\": \"259\",
//        \"userId\": \"2090\"
//    },
//    {
//        \"modifyTime\": \"2023-08-12 08:32:15\",
//        \"userRealName\": \"张思阳\",
//        \"postName\": \"法律管理\",
//        \"postId\": \"39\",
//        \"userId\": \"264\"
//    }
//]
//}
//                               "],
//	  "synDataPath": "/rows",
//	  "synDesc": "",
//	  "synParam": "",
//	  "synVersionPath": "/version",
//	  "tableName": "synTable1",
//	  "whereSql": "convert(varchar(7),changeDt,120)>@month(-5)"
//	}

}
