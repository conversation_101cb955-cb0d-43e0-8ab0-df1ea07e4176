package com.yunhesoft.system.synchronous.utils;

public enum SynClassType {

	Example,Org,Emp,Post,Sql;
	/**
	 * 根据输入字符串获得数据类型
	 * @param type (String) 类型字符串
	 * @return SynClassType 数据类型
	 */
	public static SynClassType get(String type) {
		
		
		
		if (Example.toString().equalsIgnoreCase(type)) {//样例
			return Example;
		} else if (Org.toString().equalsIgnoreCase(type)) {//机构同步
			return Org;
		} else if (Emp.toString().equalsIgnoreCase(type)) {//人员同步
			return Emp;
		} else if (Post.toString().equalsIgnoreCase(type)) {//岗位同步
			return Post;
		} else if (Sql.toString().equalsIgnoreCase(type)) {//执行sqlserver语句
			return Sql;
		} else {
			return null;
		}
	}
}
