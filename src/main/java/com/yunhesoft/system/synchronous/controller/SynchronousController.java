package com.yunhesoft.system.synchronous.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.synchronous.entity.dto.SynDataDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueEmpDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueOrgDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueuePostDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueRightDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueRoleDto;
import com.yunhesoft.system.synchronous.entity.model.SynResult;
import com.yunhesoft.system.synchronous.entity.vo.SysComparisonVo;
import com.yunhesoft.system.synchronous.entity.vo.SysSyncMessage;
import com.yunhesoft.system.synchronous.service.ISynchronousService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
/**
 * <AUTHOR>
 */
@Api(tags = "人员管理接口")
@RestController
@RequestMapping("/system/synchronous")
public class SynchronousController {

	@Autowired
	private ISynchronousService ish;
	
	/**
	 * 同步机构信息
	 * 
	 * @category 同步机构信息
	 * @param synchronousOrg
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/synchronousOrg", method = { RequestMethod.POST })
	@ApiOperation(value = "同步机构信息")
	@ApiImplicitParam(name = "synchronousOrg", value = "同步机构信息", required = true, paramType = "body", dataType = "List<SysQueueOrgDto>")
	public Res<?> synchronousOrg(@RequestBody List<SysQueueOrgDto> listOrg) {
		SysSyncMessage mes=ish.SaveSysQueueOrg(listOrg);
		if(mes.getMessage()==null||mes.getMessage().equals("")) {
			return Res.OK(true);
		}else {
			return Res.OK(false);
		}
	}
	
	/**
	 * 同步人员信息
	 * 
	 * @category 同步人员构信息
	 * @param synchronousEmp
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/synchronousEmp", method = { RequestMethod.POST })
	@ApiOperation(value = "同步人员信息")
	@ApiImplicitParam(name = "synchronousEmp", value = "同步人员信息", required = true, paramType = "body", dataType = "List<SysQueueEmpDto>")
	public Res<?> synchronousEmp(@RequestBody List<SysQueueEmpDto> listEmp) {
		SysSyncMessage mes=ish.SaveSysQueueEmp(listEmp);
		if(mes.getMessage()==null||mes.getMessage().equals("")) {
			return Res.OK(true);
		}else {
			return Res.OK(false);
		}
	}
	/**
	 * 同步岗位信息
	 * 
	 * @category 同步岗位构信息
	 * @param synchronousPost
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/synchronousPost", method = { RequestMethod.POST })
	@ApiOperation(value = "同步岗位信息")
	@ApiImplicitParam(name = "synchronousPost", value = "同步岗位信息", required = true, paramType = "body", dataType = "List<SysQueuePostDto>")
	public Res<?> synchronousPost(@RequestBody List<SysQueuePostDto> listPost) {
		SysSyncMessage mes=ish.SaveSysQueuePost(listPost);
		if(mes.getMessage()==null||mes.getMessage().equals("")) {
			return Res.OK(true);
		}else {
			return Res.OK(false);
		}
	}
	
	/**
	 * 同步角色信息
	 * 
	 * @category 同步角色构信息
	 * @param synchronousPost
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/synchronousRole", method = { RequestMethod.POST })
	@ApiOperation(value = "同步角色信息")
	@ApiImplicitParam(name = "synchronousRole", value = "同步角色信息", required = true, paramType = "body", dataType = "List<SysQueueRoleDto>")
	public Res<?> synchronousRole(@RequestBody List<SysQueueRoleDto> listRole) {
		SysSyncMessage mes=ish.SaveSysQueueRole(listRole);
		if(mes.getMessage()==null||mes.getMessage().equals("")) {
			return Res.OK(true);
		}else {
			return Res.OK(false);
		}
	}
	
	/**
	 * 同步权限 信息
	 * 
	 * @category 同步权限角色构信息
	 * @param synchronousPost
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/synchronousRight", method = { RequestMethod.POST })
	@ApiOperation(value = "同步权限信息")
	@ApiImplicitParam(name = "synchronousRight", value = "同步权限信息", required = true, paramType = "body", dataType = "List<SysQueueRightDto>")
	public Res<?> synchronousRight(@RequestBody List<SysQueueRightDto> listright) {
		SysSyncMessage mes=ish.SaveSysQueueRight(listright);
		if(mes.getMessage()==null||mes.getMessage().equals("")) {
			return Res.OK(true);
		}else {
			return Res.OK(false);
		}
	}
	
	
	/**
	 * 外部数据通用同步接口
	 * @category 外部数据通用同步接口
	 * <AUTHOR>
	 * @param parm
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/synData", method = { RequestMethod.POST })
	@ApiOperation(value = "外部数据通用同步接口", notes = "外部数据通用同步接口")
	@ApiImplicitParam(name = "param", value = "接口访问参数", required = true, paramType = "body", dataType = "SynDataDto")
	public Res<?> synData(@RequestBody SynDataDto param) {
		Res<SynResult> r = new Res<SynResult>();
		r.setCode(200);
		SynResult result = ish.synData(param);
		if(result!=null) {//成功
			r.setSuccess(result.isResult());
			r.setMessage(result.getErrInfo());
			r.setTotal(result.getTotal());
			r.setResult(result);	
		}else {
			r.setSuccess(false);//失败了	
			r.setMessage("数据同步失败");
			if(result==null) {
				result = new SynResult();
				result.setResult(false);
			}
			r.setResult(result);
		}
		return r;
	}
	
	/**
	 * 内部数据通用拉取接口
	 * @category 内部数据通用拉取接口
	 * <AUTHOR>
	 * @param parm
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/pullData", method = { RequestMethod.POST })
	@ApiOperation(value = "内部数据通用拉取接口", notes = "内部数据通用拉取接口")
	@ApiImplicitParam(name = "param", value = "接口访问参数", required = true, paramType = "body", dataType = "SynDataDto")
	public Res<?> pullData(@RequestBody SynDataDto param) {		
		Res<SynResult> r = new Res<SynResult>();
		r.setCode(200);
		SynResult result = ish.pullData(param);
		if(result!=null) {//成功
			r.setSuccess(result.isResult());
			r.setMessage(result.getErrInfo());
			r.setTotal(result.getTotal());
			r.setResult(result);	
		}else {
			r.setSuccess(false);//失败了	
			r.setMessage("数据拉取失败");
			if(result==null) {
				result = new SynResult();
				result.setResult(false);
			}
			r.setResult(result);
		}
		return r;
	}
	
	
	/**
	 * 获取TM3和TM4的对照数据
	 * 
	 * @category 获取TM3和TM4的对照数据
	 * @param dataList
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getSynchronousData", method = { RequestMethod.POST })
	@ApiOperation(value = "获取TM3和TM4的对照数据")
	@ApiImplicitParam(name = "dataList", value = "获取TM3和TM4的对照数据", required = true, paramType = "body", dataType = "List<SysComparisonVo>")
	public Res<?> getSynchronousData(@RequestBody List<SysComparisonVo> dataList) {
		return Res.OK(ish.getSynchronousData(dataList));
	}
	
	/**
	 * 获取TM3和TM4需要关联对照的数据列表
	 * @category 获取TM3和TM4需要关联对照的数据列表
	 * @param dataList
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getSynchronousComparisonVo", method = { RequestMethod.POST })
	@ApiOperation(value = "获取TM3和TM4需要关联对照的数据列表")
	public Res<?> getSynchronousComparisonVo() {
		return Res.OK(ish.getSynchronousComparisonVo());
	}
	/**
	 * 更新TM3和TM4的对照关系
	 * @category 更新TM3和TM4的对照关系
	 * @param dataList
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/updateTM3Comparison", method = { RequestMethod.POST })
	@ApiOperation(value = "更新TM3和TM4的对照数据")
	public Res<?> updateTM3Comparison(@RequestBody List<SysComparisonVo> dataList) {
		return Res.OK(ish.updateTM3Comparison(dataList));
	}

}
