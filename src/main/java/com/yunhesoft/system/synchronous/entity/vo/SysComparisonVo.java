package com.yunhesoft.system.synchronous.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 对照关系
  * @Description:
  * <AUTHOR>
  * @date 2024年9月4日
 */
@Data
@ApiModel(value="对照关系VO类",description="对照关系VO类")
public class SysComparisonVo {
	@ApiModelProperty(value = "TM3编码")
	private String codeTM3;
	@ApiModelProperty(value = "TM4编码")
	private String codeTM4;
	@ApiModelProperty(value = "对照类型 org emp post")
	private String codeType;
}