package com.yunhesoft.system.synchronous.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;
/**
 * TM4同步对照表
 * 
 * @category 同步对照表
 * <AUTHOR>
 * @date 2021/11/12
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_COMPARISON")
public class SysComparison  extends BaseEntity {
	/** */
	private static final long serialVersionUID = 1L;
	
	/** tm4编码 */
	@Column(name = "CODETM4", length = 100)
	private String codetm4;

	/** 其他系统编码 */
	@Column(name = "CODE", length = 500)
	private String code;
	
	/** 对照模块  */
	@Column(name = "COMMODULE", length = 100)
	private String commodule;
	
	/** 对照类型 */
	@Column(name = "COMTYPE", length = 100)
	private String comtype;

	/** 备用字段1 */
	@Column(name = "PARAM1", length = 500)
	private String param1;
	
	/** 备用字段2 */
	@Column(name = "PARAM2", length = 500)
	private String param2;
	
	/** 备用字段3 */
	@Column(name = "PARAM3", length = 500)
	private String param3;
	
	/** 备用字段4 */
	@Column(name = "PARAM4", length = 500)
	private String param4;
	
	/** 备用字段5 */
	@Column(name = "PARAM5", length = 500)
	private String param5;

	/** 是否可用 */
	@Column(name = "USED")
	private Integer used;
	
}
