package com.yunhesoft.system.synchronous.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * TM4权限同步队表
 * 
 * @category 权限同步队列表
 * <AUTHOR>
 * @date 2021/11/12
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_QUEUE_RIGHT")
public class SysQueueRight  extends BaseEntity {

	/** */
	private static final long serialVersionUID = 1L;
	
	/** 权限id */
	@Column(name = "RIGHTID", length = 100)
	private String rightid;
	/** 角色名称 */
	@Column(name = "ROLEID", length = 500)
	private String roleid;
	/** 权限名 */
	@Column(name = "RIGHTNAME", length = 100)
	private String rightName;
	/** TM4菜单路径 */
	@Column(name = "TM4PATH", length = 200)
	private String tm4path;
	/** TM4按钮名 */
	@Column(name = "TM4PERMS")
	private String tm4perms;
	
	/** 同步类型 1添加，2修改，3删除*/
	@Column(name = "SYNC_TYPE")
	private Integer syncType;
	/** 同步状态 1成功，-1失败*/
	@Column(name = "SYNC_STATUS")
	private Integer syncStatus;
	/** 同步时间 */
	@Column(name = "SYNC_TIME")
	private Date syncTime;
	/** 同步处理时间 */
	@Column(name = "SYNC_EXEC_TIME")
	private Date syncExecTime;
	/** 同步备注 */
	@Column(name = "SYNC_MEMO", length = 1000)
	private String syncMemo;
	/** 对照类型--TM3、TM4 */
	@Column(name = "COMTYPE", length = 100)
	private String comtype;
	
	
	
}
