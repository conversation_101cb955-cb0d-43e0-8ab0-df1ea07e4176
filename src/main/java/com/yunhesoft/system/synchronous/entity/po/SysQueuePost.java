package com.yunhesoft.system.synchronous.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * TM4岗位同步队表
 * 
 * @category 岗位同步队列表
 * <AUTHOR>
 * @date 2021/11/12
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_QUEUE_POST")
public class SysQueuePost extends BaseEntity {
	/** */
	private static final long serialVersionUID = 1L;
	
	/** 岗位id */
	@Column(name = "POSTID", length = 100)
	private String postid;
	/** 岗位名称 */
	@Column(name = "POSTNAME", length = 500)
	private String postName;
	/** 机构代码 */
	@Column(name = "ORGDM", length = 100)
	private String orgdm;
	/** 机构名称 */
	@Column(name = "ORGMC", length = 200)
	private String orgmc;
	/** 岗位级别 */
	@Column(name = "POSTLEVEL")
	private Integer postlevel;
	
	/** 同步类型 1添加，2修改，3删除*/
	@Column(name = "SYNC_TYPE")
	private Integer syncType;
	/** 同步状态 1成功，-1失败*/
	@Column(name = "SYNC_STATUS")
	private Integer syncStatus;
	/** 同步时间 */
	@Column(name = "SYNC_TIME")
	private Date syncTime;
	/** 同步处理时间 */
	@Column(name = "SYNC_EXEC_TIME")
	private Date syncExecTime;
	/** 同步备注 */
	@Column(name = "SYNC_MEMO", length = 1000)
	private String syncMemo;
	/** 对照类型--TM3、TM4 */
	@Column(name = "COMTYPE", length = 100)
	private String comtype;
}