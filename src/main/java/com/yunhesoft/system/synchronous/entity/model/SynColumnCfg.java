package com.yunhesoft.system.synchronous.entity.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class SynColumnCfg {
	public static final String synDataType_date = "date";//日期型
	public static final String synDataType_number = "number";//数值型
	public static final String synDataType_varchar = "varchar";//字符行
	
	
	@ApiModelProperty(value = "字段说明信息，注释项，无实际用途，可不填")
	private String synDesc;
	
	@ApiModelProperty(value = "外部数据字段别名")
	private String outsideColumn;
	
	@ApiModelProperty(value = "要同步的系统内部表的字段别名")
	private String insideColumn;
	
	@ApiModelProperty(value = "要同步的字段的类型，可不填")
	private String synDataType;
	
	@ApiModelProperty(value = "字段同步时用到的参数（自定义格式，自定义解析函数）")
	private String synParam ;
	
}
