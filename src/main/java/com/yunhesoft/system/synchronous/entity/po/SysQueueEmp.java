package com.yunhesoft.system.synchronous.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;
/**
 * TM4人员同步表
 * 
 * @category 人员同步队列表
 * <AUTHOR>
 * @date 2021/11/12
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_QUEUE_EMP")
public class SysQueueEmp   extends BaseEntity {

	/** */
	private static final long serialVersionUID = 1L;
	
	/** 人员id */
	@Column(name = "EMPID", length = 100)
	private String empid;
	/** 人员姓名 */
	@Column(name = "EMPNAME", length = 500)
	private String empName;
	/** 机构代码 */
	@Column(name = "ORGDM", length = 100)
	private String orgdm;
	/** 岗位id */
	@Column(name = "POSTID", length = 100)
	private String postid;
	/** 角色id */
	@Column(name = "ROLEID", length = 100)
	private String roleid;
	/** 用户名  */
	@Column(name = "USER_NAME", length = 100)
	private String userName;
	/** 用户密码  */
	@Column(name = "PASSWORD", length = 100)
	private String password;
	/** 工号  */
	@Column(name = "STAFF_NO", length = 100)
	private String staffNo;
	/** 性别  */
	@Column(name = "SEX")
	private Integer sex;
	/** mail  */
	@Column(name = "MAIL", length = 50)
	private String mail;
	/** 移动电话mail  */
	@Column(name = "MOBILE", length = 50)
	private String mobile;
	/** 入职时间  */
	@Column(name = "ENTRY_DATE")
	private Date entryDate;
	/** 同步类型 1添加，2修改，3删除*/
	@Column(name = "SYNC_TYPE")
	private Integer syncType;
	/** 同步状态 1成功，-1失败*/
	@Column(name = "SYNC_STATUS")
	private Integer syncStatus;
	/** 同步时间 */
	@Column(name = "SYNC_TIME")
	private Date syncTime;
	/** 同步处理时间 */
	@Column(name = "SYNC_EXEC_TIME")
	private Date syncExecTime;
	/** 同步备注 */
	@Column(name = "SYNC_MEMO", length = 1000)
	private String syncMemo;
	/** 对照类型--TM3、TM4 */
	@Column(name = "COMTYPE", length = 100)
	private String comtype;
	/** 政治面貌 */
	@Column(name = "POLITICAL", length = 50)
	private String political;
}
