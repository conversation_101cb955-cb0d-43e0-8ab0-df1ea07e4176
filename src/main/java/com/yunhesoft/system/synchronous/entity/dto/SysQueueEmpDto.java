package com.yunhesoft.system.synchronous.entity.dto;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class SysQueueEmpDto {
	/** 人员id */
	private String empid;
	/** 人员姓名 */
	private String empName;
	/** 机构代码 */
	private String orgdm;
	/** 岗位id */
	private String postid;
	/** 角色id */
	private String roleid;
	/** 同步类型 1添加，2修改，3删除*/
	private Integer syncType;
	/** 对照类型--TM3、TM4 */
	private String comtype;
	
	/** 用户名  */
	private String userName;
	/** 用户密码  */
	private String password;
	/** 工号  */
	private String staffNo;
	/** 性别  */
	private Integer sex;
	/** mail  */
	private String mail;
	/** 移动电话mail  */
	private String mobile;
	/** 入职时间  */
	private Date entryDate;
	/** 政治面貌 */
	private String political;
}
