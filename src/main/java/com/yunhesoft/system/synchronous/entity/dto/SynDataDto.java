package com.yunhesoft.system.synchronous.entity.dto;

import java.util.List;

import com.yunhesoft.system.synchronous.entity.model.SynColumnCfg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class SynDataDto {

	/**公用变量*/
	@ApiModelProperty(value = "接口同步编码 (例如机构为org,人员为emp)，必填项。忽略大小写。")
	private String synCode;
	
	@ApiModelProperty(value = "接口说明信息，注释项，无实际用途，可不填")
	private String synDesc;
	
	@ApiModelProperty(value = "接口同步、拉取参数（自定义格式，自定义解析函数）")
	private String synParam;

	@ApiModelProperty(value = "数据同步（同步时用于删除已采集数据，重新采集）和拉取（拉取时用于查询拉取数据）用where条件，支持变量参数@month(+-i) @day(+-i) @all()（只要该where条件中含有@all()参数，则表示删除原有全部数据）。填写该项时必须带变量参数，不带参数不执行同步时的删除")
	private String whereSql;
	
	@ApiModelProperty(value = "数据同步和拉取的表名")
	private String tableName;
	
	@ApiModelProperty(value = "接口数据字段对照配置信息")
	private List<SynColumnCfg> columnCfg ;
	
	/**数据同步用变量*/
	@ApiModelProperty(value = "接口同步数据在json中的路径，如果json直接为数据数组则为/。例:如果json {k1:'1',k2:'2',k3:{k31:'3',k32:[{a:1,b:1},{a:2,b:3}]}} 则采集路径为 /k3/k32  ;如果json [{a:1,b:1},{a:2,b:3}] 则采集路径为/")
	private String synDataPath="/";
	
	@ApiModelProperty(value = "接口同步数据版本信息在json中的路径，如果json直接为数据数组则为/。例:如果json {k1:'1',k2:'2',k3:{k31:'version',k32:[{a:1,b:1},{a:2,b:3}]}} 则采集路径为 /k3/k31  ;如果json [{a:1,b:version},{a:2,b:3}] 则采集路径为/b（指定数据为数组，取数组第一条数据中的属性）")
	private String synVersionPath="/";
	
	@ApiModelProperty(value = "接口同步数据Json数组，可以将一组相同模式的接口数据传入，程序会将一组数据根据相同的规则解析出数据来，合并一同处理（解决外部数据接口有分页数据的情况，无如分页，则数组中只给一条数据即可）")
	private List<String> synDataJson;

	/**数据拉取用变量*/
	@ApiModelProperty(value = "拉取数据用的接口模板，数据会替换模板中的[@data][@nowDt]变量。例:模板为{k1:'1',k2:'2',k3:{k31:'3',k32:[@data]}}，拉取数据为[{a:1,b:1},{a:2,b:3}]， 替换数据后为 {k1:'1',k2:'2',k3:{k31:'3',k32:[{a:1,b:1},{a:2,b:3}]}}")
	private String pullTemplate;
}
