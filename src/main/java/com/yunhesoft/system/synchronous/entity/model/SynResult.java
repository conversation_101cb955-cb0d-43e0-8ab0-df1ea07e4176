package com.yunhesoft.system.synchronous.entity.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class SynResult {
	@ApiModelProperty(value = "接口采集结果，成功至少1条就会返回true，1条都未成功返回false")
	private boolean result = false;
	
	@ApiModelProperty(value = "数据同步总数/数据拉取总数")
	private int total = 0;
	
	@ApiModelProperty(value = "同步成功数据数量")
	private int success=0;
	
	@ApiModelProperty(value = "同步失败数据数量")
	private int failure=0;
	
	@ApiModelProperty(value = "同步版本信息")
	private String version ;
	
	@ApiModelProperty(value = "接口错误信息")	
	private String errInfo = "";
	
	@ApiModelProperty(value = "拉取到的数据.数据会替换SynDataDto.pullTemplate模板中的[@data][@nowDt]变量。例:模板为{k1:'1',k2:'2',k3:{k31:'3',k32:[@data]}}，拉取数据为[{a:1,b:1},{a:2,b:3}]， 替换数据后为 {k1:'1',k2:'2',k3:{k31:'3',k32:[{a:1,b:1},{a:2,b:3}]}}")	
	private String pullData;
}
