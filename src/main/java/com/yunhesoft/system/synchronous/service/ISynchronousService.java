package com.yunhesoft.system.synchronous.service;

import java.util.List;
import java.util.Map;

import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.synchronous.entity.dto.SynDataDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueEmpDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueOrgDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueuePostDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueRightDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueRoleDto;
import com.yunhesoft.system.synchronous.entity.model.SynResult;
import com.yunhesoft.system.synchronous.entity.po.SysComparison;
import com.yunhesoft.system.synchronous.entity.vo.SysComparisonVo;
import com.yunhesoft.system.synchronous.entity.vo.SysSyncMessage;

/**
 * TM4同步数据
 * 
 * @category 同步数据类
 * <AUTHOR>
 *
 */
public interface ISynchronousService {

	/**
	 * 批量同步机构信息
	 * 
	 * @param list
	 * @param listOrg
	 * @return
	 */
	public SysSyncMessage SaveSysQueueOrg(List<SysQueueOrgDto> listOrg);

	/**
	 * 批量同步人员信息
	 * 
	 * @param list
	 * @param listOrg
	 * @return
	 */
	public SysSyncMessage SaveSysQueueEmp(List<SysQueueEmpDto> listEmp);

	/**
	 * 批量同步岗位信息
	 * 
	 * @param list
	 * @param listOrg
	 * @return
	 */
	public SysSyncMessage SaveSysQueuePost(List<SysQueuePostDto> listPost);

	/**
	 * 批量同步权限信息
	 * 
	 * @param list
	 * @param listOrg
	 * @return
	 */
	public SysSyncMessage SaveSysQueueRight(List<SysQueueRightDto> listright);

	/**
	 * 批量同步角色信息
	 * 
	 * @param list
	 * @param listOrg
	 * @return
	 */
	public SysSyncMessage SaveSysQueueRole(List<SysQueueRoleDto> listRole);

	/**
	 * 同步数据接口
	 * 
	 * @category <AUTHOR>
	 * @param jsonData 接口采集到的数据
	 * @param cfg      同步参数,具体内容见 SynCollectionCfg 类注释
	 * @return String false开头代表执行失败
	 */
	public SynResult synData(SynDataDto cfg);

	/**
	 * 拉取数据接口
	 * 
	 * @category <AUTHOR>
	 * @param cfg 拉取参数,具体内容见 SynCollectionCfg 类注释
	 * @return String 返回 SynDataDto.pullTemplate中指定的内容（会替换其中的变量为实际数据）
	 */
	public SynResult pullData(SynDataDto cfg);

	/**
	 * 根据类型和模块获取数据Map
	 * 
	 * @param type
	 * @param module
	 * @return
	 */
	public Map<String, SysComparison> getSysComparisonMap(String type, String module);

	/**
	 * 获取人员信息数据
	 * 
	 * @return
	 */
	public List<SysEmployeeInfo> getSysEmployeeInfoList();
	
	/**
	 * 获取TM3和TM4对照信息
	 * @category 获取TM3和TM4对照信息
	 * <AUTHOR> 
	 * @param dataList 对照列表
	 * @return List<SysComparisonVo>
	 */
	public List<SysComparisonVo> getSynchronousData(List<SysComparisonVo> dataList);
	/**
	 * 保存对照关系
	 * @category 
	 * <AUTHOR> 
	 * @param dataList
	 * @param isUpdate 是否为更新
	 * @return
	 */
	public boolean saveSysComparison(List<SysComparison> dataList,boolean isUpdate);
	/**
	 * 获取需要关联对照的数据列表
	 * @category 
	 * <AUTHOR>
	 * @return List<SysComparisonVo>
	 */
	public List<SysComparison> getSynchronousComparison();
	/**
	 * 获取需要关联对照的数据列表
	 * @category 
	 * <AUTHOR>
	 * @return List<SysComparisonVo>
	 */
	public List<SysComparisonVo> getSynchronousComparisonVo();
	/**
	 * 更新TM3对照关系（人员）
	 * @category 
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	public boolean updateTM3Comparison(List<SysComparisonVo> dataList);
}
