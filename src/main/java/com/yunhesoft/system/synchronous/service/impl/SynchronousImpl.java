package com.yunhesoft.system.synchronous.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.auth.entity.po.SysLoginUser;
import com.yunhesoft.system.employee.entity.dto.EmployeeDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.employee.service.IEmployeeBasicOperationService;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.impl.EntityServiceImpl;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.menu.entity.po.SysMenu;
import com.yunhesoft.system.org.entity.dto.SysOrgAdd;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.entity.po.SysOrgRelation;
import com.yunhesoft.system.org.service.ISysOrgRelationService;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.post.entity.dto.PostDto;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.post.entity.po.SysPostRelati;
import com.yunhesoft.system.post.service.IPostBasicOperationService;
import com.yunhesoft.system.post.service.ISysDiyPost;
import com.yunhesoft.system.post.service.ISysPostService;
import com.yunhesoft.system.role.entity.po.SysRole;
import com.yunhesoft.system.role.entity.po.SysRolePerm;
import com.yunhesoft.system.role.entity.po.SysUserRole;
import com.yunhesoft.system.synchronous.entity.dto.SynDataDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueEmpDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueOrgDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueuePostDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueRightDto;
import com.yunhesoft.system.synchronous.entity.dto.SysQueueRoleDto;
import com.yunhesoft.system.synchronous.entity.model.SynResult;
import com.yunhesoft.system.synchronous.entity.po.SysComparison;
import com.yunhesoft.system.synchronous.entity.po.SysQueueEmp;
import com.yunhesoft.system.synchronous.entity.po.SysQueueOrg;
import com.yunhesoft.system.synchronous.entity.po.SysQueuePost;
import com.yunhesoft.system.synchronous.entity.po.SysQueueRight;
import com.yunhesoft.system.synchronous.entity.po.SysQueueRole;
import com.yunhesoft.system.synchronous.entity.vo.SysComparisonVo;
import com.yunhesoft.system.synchronous.entity.vo.SysSyncMessage;
import com.yunhesoft.system.synchronous.service.ISynchronousService;
import com.yunhesoft.system.synchronous.utils.SynModel;
import com.yunhesoft.system.synchronous.utils.SynModelFactory;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class SynchronousImpl implements ISynchronousService {
	@Autowired
	private RedisUtil redis; // redis实例
	@Autowired
	private EntityService dao;
	@Autowired
	private IPostBasicOperationService ipbo;
	@Autowired
	private IEmployeeBasicOperationService iemp;
	@Autowired
	private EntityServiceImpl entservice;
	@Autowired
	private ISysOrgService ios;
	@Autowired
	private IEmployeeBasicOperationService iel;
	@Autowired
	private ISysPostService iSysPostServ; // 岗位信息
	@Autowired
	private ISysEmployeeInfoService iSysEmpServ; // 人员信息
	@Autowired
	private ISysDiyPost diyPost;

	@Autowired
	private ISysOrgRelationService iSysOrgRelationServ;
	
	private String redisKye = "Synchronous";

	/**
	 * 同步机构信息到队列表
	 * 
	 * @param List<SysQueueOrg>
	 * @param listOrg
	 * @return
	 */
	@Override
	public SysSyncMessage SaveSysQueueOrg(List<SysQueueOrgDto> listOrg) {
		// 获取时间
		Date date = new Date();
		List<SysQueueOrg> listAdd = new ArrayList<SysQueueOrg>();
		// 创建返回信息
		SysSyncMessage mage = new SysSyncMessage();
		// 判断参数
		if (listOrg != null && listOrg.size() > 0) {

			int m = listOrg.size();
			for (int i = 0; i < m; i++) {
				// 遍历传参数
				SysQueueOrgDto bean = listOrg.get(i);
				/** 机构id */
				String orgid = bean.getOrgid();
				/** 机构名称 */
				String orgName = bean.getOrgName();
				/** 父机构id */
				String porgid = bean.getPorgid();
				/** 机构级别 */
				Integer orglevel = bean.getOrglevel();
				/** 同步类型 1添加，2修改，3删除 */
				Integer syncType = bean.getSyncType();
				/** 对照类型--TM3、TM4 */
				String comtype = bean.getComtype();

				// 机构队列表
				SysQueueOrg e = new SysQueueOrg();
				e.setId(TMUID.getUID());
				e.setOrgid(orgid);
				e.setOrgName(orgName);
				e.setPorgid(porgid);
				e.setOrglevel(orglevel);
				/** 同步类型 1添加，2修改，3删除 */
				e.setSyncType(syncType);
				/** 同步状态 0待处理,1成功，-1失败 */
				e.setSyncStatus(0);
				/** 同步时间 */
				e.setSyncTime(date);
				/** 同步处理时间 */
				e.setSyncExecTime(null);
				/** 同步备注 */
				e.setSyncMemo(null);
				/** 对照类型--TM3、TM4 */
				e.setComtype(comtype);
				listAdd.add(e);
			}
			// 保存数据到队列表
			if (listAdd != null && listAdd.size() > 0) {
				int num = dao.insertBatch(listAdd);
				if (num <= 0) {
					mage.setCode("-1");
					mage.setMessage("保存机构数据到队列表失败!");
				} else {
					// 调用调度执行队列数据
					mage = ExecSysQueue();
					redis.set(redisKye, "close", 180);
					delRedis();// 清楚reids中的菜单数据
					ios.initRedis();// 机构初始化到redis
				}
			}

		}
		return mage;
	}

	/**
	 * 同步人员信息到队列表
	 * 
	 * @param List<SysQueueEmpDto>
	 * @param listEmp
	 * @return
	 */
	@Override
	public SysSyncMessage SaveSysQueueEmp(List<SysQueueEmpDto> listEmp) {
		// 获取时间
		Date date = new Date();
		List<SysQueueEmp> listAdd = new ArrayList<SysQueueEmp>();
		// 创建返回信息
		SysSyncMessage mage = new SysSyncMessage();
		// 判断参数
		if (listEmp != null && listEmp.size() > 0) {
			int m = listEmp.size();
			for (int i = 0; i < m; i++) {
				// 遍历传参数
				SysQueueEmpDto bean = listEmp.get(i);
				/** 人员id */
				String empid = bean.getEmpid();
				/** 人员姓名 */
				String empName = bean.getEmpName();
				/** 机构代码 */
				String orgdm = bean.getOrgdm();
				/** 岗位id */
				String postid = bean.getPostid();
				/** 角色id */
				String roleid = bean.getRoleid();
				/** 同步类型 1添加，2修改，3删除 */
				Integer syncType = bean.getSyncType();
				/** 对照类型--TM3、TM4 */
				String comtype = bean.getComtype();

				/** 用户名 */
				String userName = bean.getUserName();
				/** 用户密码 */
				String password = bean.getPassword();
				/** 工号 */
				String staffNo = bean.getStaffNo();
				/** 性别 */
				Integer sex = bean.getSex();
				/** mail */
				String mail = bean.getMail();
				/** 移动电话mail */
				String mobile = bean.getMobile();
				/** 入职时间 */
				Date entryDate = bean.getEntryDate();
				/** 政治面貌 */
				String political = bean.getPolitical();
				// 人员队列表
				SysQueueEmp e = new SysQueueEmp();
				e.setId(TMUID.getUID());
				/** 人员id */
				e.setEmpid(empid);
				/** 人员姓名 */
				e.setEmpName(empName);
				/** 机构代码 */
				e.setOrgdm(orgdm);
				/** 岗位id */
				e.setPostid(postid);
				/** 角色id */
				e.setRoleid(roleid);

				/** 用户名 */
				e.setUserName(userName);
				/** 用户密码 */
				e.setPassword(password);
				/** 工号 */
				e.setStaffNo(staffNo);
				/** 性别 */
				e.setSex(sex);
				/** mail */
				e.setMail(mail);
				/** 移动电话mail */
				e.setMobile(mobile);
				/** 入职时间 */
				e.setEntryDate(entryDate);

				/** 同步类型 1添加，2修改，3删除 */
				e.setSyncType(syncType);
				/** 同步状态 0待处理,1成功，-1失败 */
				e.setSyncStatus(0);
				/** 同步时间 */
				e.setSyncTime(date);
				/** 同步处理时间 */
				e.setSyncExecTime(null);
				/** 同步备注 */
				e.setSyncMemo(null);
				/** 对照类型--TM3、TM4 */
				e.setComtype(comtype);
				/** 政治面貌 */
				String politicalstatus="";//默认无政治面貌
				if(political!=null && political.length()!=0) {//有同步政治面貌
					if(political.indexOf("党员")>=0 && political.indexOf("预备")<0) {//政治面貌中含有党员并且非预备党员
						politicalstatus="01";//党员
					}
				}
				e.setPolitical(politicalstatus);
				listAdd.add(e);
			}
			// 保存数据到队列表
			if (listAdd != null && listAdd.size() > 0) {
				int num = dao.insertBatch(listAdd);
				if (num <= 0) {
					mage.setCode("-1");
					mage.setMessage("保存机构数据到队列表失败!");
				} else {
					// 调用调度执行队列数据
					mage = ExecSysQueue();
					redis.set(redisKye, "close", 180);
					delRedis();// 清楚reids中的菜单数据
					iSysEmpServ.initRedis(); // 人员信息初始化到redis
				}
			}
		}
		return mage;
	}

	/**
	 * 同步岗位信息到队列表
	 * 
	 * @param List<SysQueuePostDto>
	 * @param listPost
	 * @return
	 */
	@Override
	public SysSyncMessage SaveSysQueuePost(List<SysQueuePostDto> listPost) {
		// 获取时间
		Date date = new Date();
		List<SysQueuePost> listAdd = new ArrayList<SysQueuePost>();
		// 创建返回信息
		SysSyncMessage mage = new SysSyncMessage();
		// 判断参数
		if (listPost != null && listPost.size() > 0) {
			int m = listPost.size();
			for (int i = 0; i < m; i++) {
				// 遍历传参数
				SysQueuePostDto bean = listPost.get(i);
				/** 岗位id */
				String postid = bean.getPostid();
				/** 岗位名称 */
				String postName = bean.getPostName();
				/** 机构代码 */
				String orgdm = bean.getOrgdm();
				/** 机构名称 */
				String orgmc = bean.getOrgmc();
				/** 岗位级别 */
				Integer postlevel = bean.getPostlevel();
				/** 同步类型 1添加，2修改，3删除 */
				Integer syncType = bean.getSyncType();
				/** 对照类型--TM3、TM4 */
				String comtype = bean.getComtype();

				// 岗位队列表
				SysQueuePost e = new SysQueuePost();
				e.setId(TMUID.getUID());
				e.setPostid(postid);
				e.setPostName(postName);
				e.setPostlevel(postlevel);
				e.setOrgdm(orgdm);
				e.setOrgmc(orgmc);
				/** 同步类型 1添加，2修改，3删除 */
				e.setSyncType(syncType);
				/** 同步状态 0待处理,1成功，-1失败 */
				e.setSyncStatus(0);
				/** 同步时间 */
				e.setSyncTime(date);
				/** 同步处理时间 */
				e.setSyncExecTime(null);
				/** 同步备注 */
				e.setSyncMemo(null);
				/** 对照类型--TM3、TM4 */
				e.setComtype(comtype);
				listAdd.add(e);
			}
			// 保存数据到队列表
			if (listAdd != null && listAdd.size() > 0) {
				int num = dao.insertBatch(listAdd);
				if (num <= 0) {
					mage.setCode("-1");
					mage.setMessage("保存岗位数据到队列表失败!");
				} else {
					// 调用调度执行队列数据
					mage = ExecSysQueue();
					redis.set(redisKye, "close", 180);
					delRedis();// 清楚reids中的菜单数据
					iSysPostServ.initRedis();// 岗位初始化到redis
				}
			}
		}
		return mage;
	}

	/**
	 * 同步角色信息到队列表
	 * 
	 * @param List<SysQueueRoleDto>
	 * @param listRole
	 * @return
	 */
	@Override
	public SysSyncMessage SaveSysQueueRole(List<SysQueueRoleDto> listRole) {
		// 获取时间
		Date date = new Date();
		List<SysQueueRole> listAdd = new ArrayList<SysQueueRole>();
		// 创建返回信息
		SysSyncMessage mage = new SysSyncMessage();
		// 判断参数
		if (listRole != null && listRole.size() > 0) {
			int m = listRole.size();
			for (int i = 0; i < m; i++) {
				// 遍历传参数
				SysQueueRoleDto bean = listRole.get(i);
				/** 角色id */
				String roleid = bean.getRoleid();
				/** 岗位名称 */
				String roleName = bean.getRoleName();
				/** 权限类别 */
				Integer qxlb = bean.getQxlb();
				/** 同步类型 1添加，2修改，3删除 */
				Integer syncType = bean.getSyncType();
				/** 对照类型--TM3、TM4 */
				String comtype = bean.getComtype();

				// 角色队列表
				SysQueueRole e = new SysQueueRole();
				e.setId(TMUID.getUID());
				e.setRoleid(roleid);
				e.setRoleName(roleName);
				e.setQxlb(qxlb);
				/** 同步类型 1添加，2修改，3删除 */
				e.setSyncType(syncType);
				/** 同步状态 0待处理,1成功，-1失败 */
				e.setSyncStatus(0);
				/** 同步时间 */
				e.setSyncTime(date);
				/** 同步处理时间 */
				e.setSyncExecTime(null);
				/** 同步备注 */
				e.setSyncMemo(null);
				/** 对照类型--TM3、TM4 */
				e.setComtype(comtype);
				listAdd.add(e);
			}
			// 保存数据到队列表
			if (listAdd != null && listAdd.size() > 0) {
				int num = dao.insertBatch(listAdd);
				if (num <= 0) {
					mage.setCode("-1");
					mage.setMessage("保存角色数据到队列表失败!");
				} else {
					// 调用调度执行队列数据
					mage = ExecSysQueue();
					delRedis();// 清楚reids中的菜单数据
					redis.set(redisKye, "close", 180);
				}
			}
		}
		return mage;
	}

	/**
	 * 同步权限信息到队列表
	 * 
	 * @param List<SysQueueRightDto>
	 * @param listRole
	 * @return
	 */
	@Override
	public SysSyncMessage SaveSysQueueRight(List<SysQueueRightDto> listright) {
		// 获取时间
		Date date = new Date();
		List<SysQueueRight> listAdd = new ArrayList<SysQueueRight>();
		// 创建返回信息
		SysSyncMessage mage = new SysSyncMessage();
		// 判断参数
		if (listright != null && listright.size() > 0) {
			int m = listright.size();
			for (int i = 0; i < m; i++) {
				// 遍历传参数
				SysQueueRightDto bean = listright.get(i);
				/** 权限id */
				String rightid = bean.getRightid();
				/** 角色id */
				String roleid = bean.getRoleid();
				/** 角色名称 */
				String rightName = bean.getRightName();
				/** TM4菜单路径 */
				String tm4path = bean.getTm4path();
				/** TM4按钮名 */
				String tm4perms = bean.getTm4perms();
				/** 同步类型 1添加，2修改，3删除 */
				Integer syncType = bean.getSyncType();
				/** 对照类型--TM3、TM4 */
				String comtype = bean.getComtype();

				// 机构队列表
				SysQueueRight e = new SysQueueRight();
				e.setId(TMUID.getUID());
				/** 权限id */
				e.setRightid(rightid);
				/** 角色名称 */
				e.setRoleid(roleid);
				/** 权限名 */
				e.setRightName(rightName);
				/** TM4菜单路径 */
				e.setTm4path(tm4path);
				/** TM4按钮名 */
				e.setTm4perms(tm4perms);

				/** 同步类型 1添加，2修改，3删除 */
				e.setSyncType(syncType);
				/** 同步状态 0待处理,1成功，-1失败 */
				e.setSyncStatus(0);
				/** 同步时间 */
				e.setSyncTime(date);
				/** 同步处理时间 */
				e.setSyncExecTime(null);
				/** 同步备注 */
				e.setSyncMemo(null);
				/** 对照类型--TM3、TM4 */
				e.setComtype(comtype);
				listAdd.add(e);
			}
			// 保存数据到队列表
			if (listAdd != null && listAdd.size() > 0) {
				int num = dao.insertBatch(listAdd);
				if (num <= 0) {
					mage.setCode("-1");
					mage.setMessage("保存权限数据到队列表失败!");
				} else {
					// 调用调度执行队列数据
					mage = ExecSysQueue();
					delRedis();// 清楚reids中的菜单数据
					redis.set(redisKye, "close", 180);
				}
			}
		}
		return mage;
	}

	/**
	 * 调度执行所有队列表数据
	 * 
	 * @return
	 */
	public SysSyncMessage ExecSysQueue() {
		// 创建返回信息
		SysSyncMessage mage = new SysSyncMessage();
		mage.setCode("1");// 成功
		// 提示信息
		String str = "";
		Where where = Where.create();
		Order order = Order.create();
		// 处理机构队列数据
		// 用redis控制开关

		try {
			// 开关
			String iskg = "";

			if (redis.getString(redisKye) != null) {
				iskg = redis.getString(redisKye);
			}

			if (iskg != null && iskg.equals("open")) {
				return mage;
			} else {
				// 开启开关
				redis.set(redisKye, "open", 180);
			}

			boolean boolOrg = true;
			// 处理机构队列
			where.eq(SysQueueOrg::getSyncStatus, 0);
			order.orderByAsc(SysQueueOrg::getOrglevel);
			order.orderByAsc(SysQueueOrg::getOrgid);
			List<SysQueueOrg> listOrgQueue = dao.rawQueryListByWhere(SysQueueOrg.class, where, order);
			if (listOrgQueue != null && listOrgQueue.size() > 0) {
				str = EsecQueueOrg(listOrgQueue);
			} else {
				boolOrg = false;
			}

			boolean boolPost = true;
			where = Where.create();
			order = Order.create();
			// 处理岗位队列数据
			where.eq(SysQueuePost::getSyncStatus, 0);
			order.orderByAsc(SysQueuePost::getId);
			List<SysQueuePost> listPostQueue = dao.rawQueryListByWhere(SysQueuePost.class, where, order);
			if (listPostQueue != null && listPostQueue.size() > 0) {
				str = str + EsecQueuePost(listPostQueue);
			} else {
				boolPost = false;
			}

			boolean boolRole = true;
			where = Where.create();
			order = Order.create();
			// 处理角色队列数据
			where.eq(SysQueueRole::getSyncStatus, 0);
			order.orderByAsc(SysQueueRole::getId);
			List<SysQueueRole> listRoleQueue = dao.rawQueryListByWhere(SysQueueRole.class, where, order);
			if (listRoleQueue != null && listRoleQueue.size() > 0) {
				str = str + EsecQueueRole(listRoleQueue);
			} else {
				boolRole = false;
			}

			boolean boolEmp = true;
			where = Where.create();
			order = Order.create();
			// 处理人员队列数据
			where.eq(SysQueueEmp::getSyncStatus, 0);
			order.orderByAsc(SysQueueEmp::getId);
			List<SysQueueEmp> listEmpQueue = dao.rawQueryListByWhere(SysQueueEmp.class, where, order);
			if (listEmpQueue != null && listEmpQueue.size() > 0) {
				str = str + EsecQueueEmp(listEmpQueue);
			} else {
				boolEmp = false;
			}

			boolean boolRight = true;
			where = Where.create();
			order = Order.create();
			// 处理权限队列数据
			where.eq(SysQueueRight::getSyncStatus, 0);
			order.orderByAsc(SysQueueRight::getId);
			List<SysQueueRight> listRightQueue = dao.rawQueryListByWhere(SysQueueRight.class, where, order);
			if (listRightQueue != null && listRightQueue.size() > 0) {
				str = str + EsecQueueRight(listRightQueue);
			} else {
				boolRight = false;
			}
			redis.set(redisKye, "close", 180);
			if (boolOrg || boolPost || boolRole || boolRight || boolEmp) {
				// 重复调用本方法
				mage = ExecSysQueue();
			}
		} catch (Exception ex) {
			log.error("", ex);
			redis.set(redisKye, "close", 180);
			str = "处理队列数据出错";
		}
		if (str != null && !str.equals("")) {
			mage.setCode("-1");
			mage.setMessage(str);
		}
		return mage;
	}

	/**
	 * 执行机构队列数据
	 * 
	 * @param listOrgQueue
	 * @return
	 */
	public String EsecQueueOrg(List<SysQueueOrg> listOrgQueue) {

		Date date = new Date();
		// 创建返回信息
		String mage = "";
		// 创建返回信息

		// --------查询对照表，用于判断父节点是否存在--------------
		Map<String, SysComparison> mapCom = getSysComparisonMap("", "org");
		// --------查询TM4机构表,用于修改机构数据---------------------
		Map<String, SysOrg> mapSysOrg = getSysOrgList();
		// --------查询TM4机构关系表，用于修改数据-------------------
		Map<String, SysOrgRelation> mapSysOrgRelation = getSysOrgRelationList();

		if (listOrgQueue != null && listOrgQueue.size() > 0) {
			Where where = new Where();
			// 获取机构根节点
			where.eq(SysOrg::getUsed, 1).eq(SysOrg::getOrglevel, 0);
			List<SysOrg> listSysOrg = dao.rawQueryListByWhere(SysOrg.class, where);
			if (listSysOrg != null && listSysOrg.size() > 0) {
				SysOrg e = listSysOrg.get(0);
				// 根节点id
				mapSysOrg.put("root", e);
			}

			int m = listOrgQueue.size();
			// 遍历机构队列
			for (int i = 0; i < m; i++) {
				// 更新队列表
				List<SysQueueOrg> listQueueUp = new ArrayList<SysQueueOrg>();
				List<SysOrg> listorgUp = new ArrayList<SysOrg>();
				List<SysOrg> listorgAdd = new ArrayList<SysOrg>();
				List<SysOrgRelation> listorgReUp = new ArrayList<SysOrgRelation>();
				List<SysOrgRelation> listorgReAdd = new ArrayList<SysOrgRelation>();
//				List<SysOrgAdd> listOrgAdd=new ArrayList<SysOrgAdd>();

				List<SysComparison> listSysComUp = new ArrayList<SysComparison>();
				List<SysComparison> listSysComAdd = new ArrayList<SysComparison>();

				SysQueueOrg bean = listOrgQueue.get(i);
				/** 机构id */
				String orgid = bean.getOrgid();
				/** 机构名称 */
				String orgName = bean.getOrgName();
				/** 父机构id */
				String porgid = bean.getPorgid();

				/** 机构级别 */
				Integer orglevel = bean.getOrglevel();
				/** 同步类型 1添加，2修改，3删除 */
				Integer syncType = bean.getSyncType();
				/** 同步状态 1成功，-1失败 */
//				Integer syncStatus=bean.getSyncStatus();
//				/** 同步时间 */
//				Date syncTime=bean.getSyncTime();
//				/** 同步处理时间 */
//				Date syncExecTime=bean.getSyncExecTime();
//				/** 同步备注 */
//				String syncMemo=bean.getSyncMemo();
//				/** 对照类型--TM3、TM4 */
//				String comtype=bean.getComtype();

				bean.setSyncStatus(1);
				bean.setSyncExecTime(date);
				bean.setSyncMemo("同步成功！");

				if (syncType == null) {
					bean.setSyncStatus(-1);
					bean.setSyncExecTime(date);
					bean.setSyncMemo("执行类型不明确【syncType】为空，同步类型 1添加，2修改，3删除！");
					mage = mage + ";(" + orgid + ")" + bean.getSyncMemo();
				}
				// 得按机构级别，把高级别的先处理了，在处理低级别的，避免找不到id

				String tm4id = "";
				String tm4pid = "";
				String tm4Porgpath = "";// 父节点的路径
//				Integer tm4Porglevel1=0;
				// 从对照表获取TM4的机构id
				if (mapCom != null && mapCom.containsKey(orgid)) {
					tm4id = mapCom.get(orgid).getCodetm4();
					if (tm4id == null) {
						tm4id = "";
					}
				}
				// 从对照表获取TM4的父机构id
				if (mapCom != null && mapCom.containsKey(porgid)) {
					tm4pid = mapCom.get(porgid).getCodetm4();
					if (tm4pid == null) {
						tm4pid = "";
					}
				}
				if (tm4pid == null || "".equals(tm4pid)) {
					tm4pid = mapSysOrg.get("root").getId();
				}
				// 获取父节点的路径
				if (mapSysOrg != null && mapSysOrg.containsKey(tm4pid)) {
					tm4Porgpath = mapSysOrg.get(tm4pid).getOrgpath();
//					tm4Porglevel=mapSysOrg.get(porgid).getOrglevel();
				}

				if (syncType == 3) {
					// 如果找到了TM4的id就可以删除
					if (!"".equals(tm4id)) {
						// 判断机构表中是否存在
						if (mapSysOrg != null && mapSysOrg.containsKey(tm4id)) {
							// 删除这个机构
							SysOrg e = mapSysOrg.get(tm4id);
							e.setUsed(0);
							listorgUp.add(e);
							// 删除map中这个机构
							mapSysOrg.remove(tm4id);
							// 删除关系表
							if (mapSysOrgRelation != null && mapSysOrgRelation.containsKey(tm4id)) {
								SysOrgRelation er = mapSysOrgRelation.get(tm4id);
								er.setUsed(0);
								listorgReUp.add(er);
								// 删除map中这个机构
								mapSysOrgRelation.remove(tm4id);
							}

						} else {
							bean.setSyncStatus(-1);
							bean.setSyncExecTime(date);
							bean.setSyncMemo("同步删除失败，因为TM4机构表中没有这个机构！");
							mage = mage + ";(" + orgid + ")" + bean.getSyncMemo();
						}
					} else {
						bean.setSyncStatus(-1);
						bean.setSyncExecTime(date);
						bean.setSyncMemo("同步删除失败，因为TM4机构对照表中没有这个机构！");
						mage = mage + ";(" + orgid + ")" + bean.getSyncMemo();
					}
				} else {
					// 看对照表是否存在，存在就修改
					if (!"".equals(tm4id)) {
						// 判断机构表是否存在，存在就修改
						if (mapSysOrg != null && mapSysOrg.containsKey(tm4id)) {
							SysOrg e = mapSysOrg.get(tm4id);
							e.setOrgNumber(orgid);
							e.setOrgname(orgName);
							e.setOrglevel(orglevel);
							e.setOrgNamePath(tm4Porgpath + "/" + e.getOrgcode());
							listorgUp.add(e);
							mapSysOrg.put(tm4id, e);
							// 修改关系变的父id
							if (mapSysOrgRelation != null && mapSysOrgRelation.containsKey(tm4id)) {
								SysOrgRelation er = mapSysOrgRelation.get(tm4id);
								er.setPorgcode(tm4pid);
								listorgReUp.add(er);
								// 删除map中这个机构
								mapSysOrgRelation.put(tm4id, er);
							}
						} else {
							// 机构表不存在就添加
							SysOrgAdd orgadd = new SysOrgAdd();
							// 建立实体
							orgadd = createSysOrgAdd(orgid, orgName, tm4pid, orglevel, tm4Porgpath);
							// 调用机构接口方式生成机构表实体
							SysOrg sorg = ios.insertSysOrgData(orgadd);
							tm4id = sorg.getId();
							listorgAdd.add(sorg);
							// 生成的机构数据放入map
							mapSysOrg.put(tm4id, sorg);

							// 生成关系表
							SysOrgRelation sorl = ios.insertSysOrgRelationData(tm4id, tm4pid);
							listorgReAdd.add(sorl);
							// 封装机构关系表的map
							mapSysOrgRelation.put(tm4id, sorl);

							// 添加完，要添加新对照
							SysComparison sc = mapCom.get(orgid);
							sc.setCodetm4(tm4id);
							listSysComUp.add(sc);
							mapCom.put(orgid, sc);
						}

					} else {
						// 对照表不存在就添加
						// 机构表添加
						SysOrgAdd orgadd = new SysOrgAdd();
						// 建立实体
						orgadd = createSysOrgAdd(orgid, orgName, tm4pid, orglevel, tm4Porgpath);
						// 调用机构接口方式生成机构表实体
						SysOrg sorg = ios.insertSysOrgData(orgadd);
						tm4id = sorg.getId();
						listorgAdd.add(sorg);
						// 生成的机构数据放入map
						mapSysOrg.put(tm4id, sorg);

						// 生成关系表
						SysOrgRelation sorl = ios.insertSysOrgRelationData(tm4id, tm4pid);
						listorgReAdd.add(sorl);
						// 封装机构关系表的map
						mapSysOrgRelation.put(tm4id, sorl);

						// 添加完，要添加新对照
						SysComparison sc = new SysComparison();
						String code = TMUID.getUID();
						sc.setId(code);
						sc.setCode(orgid);// tm3
						sc.setCodetm4(tm4id);
						sc.setCommodule("org");
						sc.setComtype("tm3");
						sc.setUsed(1);
						listSysComAdd.add(sc);
						mapCom.put(orgid, sc);

					}
				}

				// ----------------单条队列数据执行事务执行--------
				try {
					entservice.begin();// 事务开始
					if (listorgAdd != null && listorgAdd.size() > 0) {
						entservice.insertBatch(listorgAdd);// 添加机构
					}
					if (listorgUp != null && listorgUp.size() > 0) {
						entservice.updateByIdBatch(listorgUp);// 更新机构
					}
					if (listorgReAdd != null && listorgReAdd.size() > 0) {
						entservice.insertBatch(listorgReAdd);// 添加机构关系
						iSysOrgRelationServ.initOrgRelationToReids();
					}
					if (listorgReUp != null && listorgReUp.size() > 0) {
						entservice.updateByIdBatch(listorgReUp);// 更新机构关系
					}
					if (listSysComAdd != null && listSysComAdd.size() > 0) {
						entservice.insertBatch(listSysComAdd);// 添加对照表
					}
					if (listSysComUp != null && listSysComUp.size() > 0) {
						entservice.updateByIdBatch(listSysComUp);// 更新对照表
					}
					entservice.commit();// 事务提交
					bean.setSyncStatus(1);
					bean.setSyncExecTime(date);
					bean.setSyncMemo("事务同时更新【对照表】【机构表】【机构关系表】成功！");
				} catch (Exception e) {
					entservice.rollback();// 事务回滚
					bean.setSyncStatus(-1);
					bean.setSyncExecTime(date);
					bean.setSyncMemo("事务同时更新【对照表】【机构表】【机构关系表】失败！");
					mage = mage + ";(" + orgid + ")" + bean.getSyncMemo();
				}
				// -------------------更新队列表的，状态，描述等-----------------------
				listQueueUp.add(bean);
				dao.saveBatch(listQueueUp);// 更新岗位队列表信息
			}
		}
		return mage;
	}

	/**
	 * 获取机构实体
	 * 
	 * @param orgdm
	 * @param orgmc
	 * @param TM4POrgid
	 * @param TM4Porglevel
	 * @param TM4Porgpath
	 * @return
	 */
	public SysOrgAdd createSysOrgAdd(String orgdm, String orgmc, String TM4POrgid, Integer TM4Porglevel,
			String TM4Porgpath) {
		String tmuid = TMUID.getUID();
		SysOrgAdd e = new SysOrgAdd();
		// 机构名称
		e.setOrgname(orgmc);
		// 机构代码
		e.setOrgNumber(orgdm);
		// 部门 department
		e.setOrgType("department");
		// 部门 department 公司 company 厂 factory 车间 workshop 装置 equipment 班组 shiftteam
		if (TM4Porglevel != null && TM4Porglevel == 1) {
			// 公司 company
			e.setOrgType("company");
		} else if (TM4Porglevel != null && TM4Porglevel == 2) {
			// 厂 factory
			e.setOrgType("factory");
		} else if (TM4Porglevel != null && TM4Porglevel == 3) {
			// 车间 workshop
			e.setOrgType("workshop");
		} else if (TM4Porglevel != null && TM4Porglevel == 4) {
			// 装置 equipment
			//e.setOrgType("equipment");
			e.setOrgType("");//装置不能打标识，防止目标传导、日考核等功能层级出错
		} else if (TM4Porglevel != null && TM4Porglevel == 5) {
			// 班组 shiftteam
			e.setOrgType("shiftteam");
		}
		// --暂时不用
		e.setPorgpath("");
		// 父机构id
		e.setPorgcode(TM4POrgid);
		// 父机构级别
		e.setOrglevel(TM4Porglevel);
		// 父路径（不包含自己）
		e.setOrgpath(TM4Porgpath + "/" + tmuid);
		// 部门负责人--暂时不用
		e.setOrgHead("");
		// 联系方式
		e.setPhone("");
		// 标识 0:添加；1：修改
		e.setRowFlag(0);
		// 机构编码
		e.setOrgcode(tmuid);
		// 排序
		e.setTmSort(null);
		// 机构id
		e.setId(tmuid);
		// 是否需要重新计算全路径和机构级别
		e.setCalfullpath(null);

		return e;
	}
	/**
	 * 执行岗位队列数据
	 * 
	 * @param listPostQueue
	 * @return
	 */
	public String EsecQueuePost(List<SysQueuePost> listPostQueue) {
		Date date = new Date();
		// 创建返回信息
		String mage = "";
		// 判断岗位队列是否有数据
		Where where = Where.create();
		if (listPostQueue != null && listPostQueue.size() > 0) {
			Map<String, SysPost> mapPost = getSysPostList();
			Map<String, SysPost> namePost = new HashMap<String,SysPost>();//按名字归类
			if(mapPost!=null && mapPost.size()>0){
				for(SysPost temp:mapPost.values()) {
					namePost.put(getPostKey(temp.getName(),temp.getOrgCode()), temp);
				}
			}
			// 读取岗位信息表，封装MAP，用于判断
			Map<String, SysComparison> mapCom = getSysComparisonMap("", "post");
			Map<String, SysComparison> mapOrg = new HashMap<String, SysComparison>();

			boolean isOrgPost = false;//是否同步为机构岗位模式（false同步到岗位库，ture同步到各个车间、部门）
			String useOrgDiyPost = diyPost.isUseOrgDiyPost();//是否启用机构岗位模式
			if(useOrgDiyPost!=null && useOrgDiyPost.length()!=0) {//有设置就启用了
				isOrgPost = true;
			}
			if(isOrgPost) {
				// 读取机构信息表，封装MAP，用于判断
				mapOrg=getSysComparisonMap("", "org");
			}
			// 读取岗位信息表关系表
//			Map<String,SysPostRelati> mapPostRelati=getSysPostRelatiList();
			// 读取对照表数据
			

			// 获取岗位根节点
//			String rootId = "";
//			where.eq(SysPost::getUsed, 1);
//			where.eq(SysPost::getPostlevel, 0);
//			where.isEmpty(SysPost::getOrgCode);
//			List<SysPost> listSysPost = dao.rawQueryListByWhere(SysPost.class, where);
//			if (listSysPost != null && listSysPost.size() > 0) {
//				SysPost e = listSysPost.get(0);
//				// 根节点id
//				rootId = e.getId();
//			} else {
//				rootId = "";// 这个必须有
//			}
//			where = Where.create();
//			where.eq(SysPost::getUsed, 1);
//			where.gt(SysPost::getPostlevel, 0);
//			// 排序字段
//			Integer sort = dao.findMaxValue(SysPost.class, SysPost::getTmSort, Integer.class, where);
//			if (sort == null) {
//				sort = 0;
//			}
			HashMap<String,Integer> sortMap = new HashMap<String,Integer>();//排序map
			HashMap<String,SysPost> rootMap = new HashMap<String,SysPost>();//根节点map
			int m = listPostQueue.size();
			// 遍历岗位队列表
			for (int i = 0; i < m; i++) {
				// 更新队列表
				List<SysQueuePost> listQueueUp = new ArrayList<SysQueuePost>();
//				List<SysPost> listpostAdd=new ArrayList<SysPost>();
//				List<SysPost> listpostUp = new ArrayList<SysPost>();
//				List<SysPostRelati> listpostRAdd=new ArrayList<SysPostRelati>();
//				List<SysPostRelati> listpostRUp=new ArrayList<SysPostRelati>();
				List<SysComparison> listSysComAdd = new ArrayList<SysComparison>();
				List<SysComparison> listSysComUp = new ArrayList<SysComparison>();

				List<PostDto> listPostDto = new ArrayList<PostDto>();

				SysQueuePost bean = listPostQueue.get(i);
				/** 岗位id */
				String postid = bean.getPostid();
				/** 岗位名称 */
				String postName = bean.getPostName();
				/** 机构代码 */
				String orgdm=null;
				if(isOrgPost) {//按机构同步模式，需要机构代码
					if(bean.getOrgdm()!=null && bean.getOrgdm().length()>=8) {
						SysComparison orgsc = mapOrg.get(bean.getOrgdm().substring(0,8)+"00");//获取车间		
						if(orgsc!=null) {
							orgdm=orgsc.getCodetm4();//获取车间对应的tm4机构代码
						}		
					}
				}
				/** 机构名称 */
//				String orgmc=bean.getOrgmc();
				/** 岗位级别 */
//				Integer postlevel=bean.getPostlevel();
				/** 同步类型 1添加，2修改，3删除 */
				Integer syncType = bean.getSyncType();
				/** 同步状态 1成功 -1失败 /事务更新回滚 */
//				Integer syncStatus=bean.getSyncStatus();
				/** 同步时间 */
//				Date syncTime=bean.getSyncTime();
				/** 同步处理时间 */
//				Date syncExecTime=bean.getSyncExecTime();
				/** 同步备注 */
//				String syncMemo=bean.getSyncMemo();
				/** 对照类型--TM3、TM4 */
//				String comtype=bean.getComtype();
				bean.setSyncStatus(1);
				bean.setSyncExecTime(date);
				bean.setSyncMemo("同步成功！");
				if (syncType == null) {
					bean.setSyncStatus(-1);
					bean.setSyncExecTime(date);
					bean.setSyncMemo("执行类型不明确【syncType】为空，同步类型 1添加，2修改，3删除！");
					mage = mage + ";(" + postid + ")" + bean.getSyncMemo();
				}
				if(postName!=null && postName.length()!=0) {	
					if (syncType != null && syncType == 3) {// 删除岗位。不能删除，TM4岗位为岗位库模式，不能进行删除
						// 查对照表和岗位表是否存在
	//					if (mapCom != null && mapCom.containsKey(postid)) {
	//						// 获取TMM4岗位id
	//						SysComparison sysc = mapCom.get(postid);
	//						String gwid = sysc.getCodetm4();
	//						// 判断tm4岗位表中是否存在这个岗位
	//						if (mapPost != null && mapPost.containsKey(gwid)) {
	//							SysPost epost = mapPost.get(gwid);
	//							// 1.删除mapPost岗位表中的这个岗位信息
	//							mapPost.remove(gwid);
	//							// 2.删除mapCom对照表中的这个岗位信息
	//							mapCom.remove(postid);
	//							// 3.删除对照表中的这个岗位信息
	//							sysc.setUsed(0);
	//							listSysComUp.add(sysc);
	//							// 4.删除TM4岗位表中这个岗位
	//							epost.setUsed(0);
	//							listpostUp.add(epost);
	//							// 5.删除TM4岗位关系表中这个岗位
	//							// 由于TM4岗位关系表中没有used字段，暂时不删除，以免不可恢复
	//
	//						} else {
	//							// 如果TM4中没有这个岗位，同步状态失败
	//							bean.setSyncStatus(-1);
	//							bean.setSyncExecTime(date);
	//							bean.setSyncMemo("同步删除失败，因为TM4岗位对照表中没有这个岗位！");
	//							mage = mage + ";(" + postid + ")" + bean.getSyncMemo();
	//						}
	//					}
					} else {
						// 添加或者修改，判断，如果表中没有就添加，有就更新
						boolean addPost = false;//是否要添加岗位
						SysComparison sysc  = null;//已存在的对照关系
						if (mapCom != null && mapCom.containsKey(postid)) {// 判断岗位是否在对照表存在,有对照信息
							// 判断TM4岗位表是否存在，存在得更新
							// 1.从mapPost中取出岗位数据
							sysc = mapCom.get(postid);
							String gwid = sysc.getCodetm4();
							SysPost ep = mapPost.get(gwid);
							if(ep!=null) {//找到了对应岗位
								if(postName.equals(ep.getName())) {//名称没有改变，则不需要进行操作
									
								}else {//名称不同，则根据情况新建或者切换到同名岗位上
									addPost = true;
								}
							}else {
								addPost = true;
							}
						}else {
							addPost = true;
						}
						if(addPost) {//需要添加岗位
							SysPost np = namePost.get(getPostKey(postName,orgdm));//查找同名岗位
							String postId = null;//tm4的岗位ID
							if(np!=null) {//找到了其他的已有岗位
								postId = np.getId();	
							}else {//没有对照到已有的岗位，则新建岗位
								// 1.生成岗位表数据
								int sort = 0;//不用计算排序，岗位创建时自动计算   getPostSort(orgdm,sortMap);
								String rootId= getPostRootId(orgdm,rootMap);
								PostDto postdto = getAssignmentPostDto(postName, sort,rootId ,orgdm);
								listPostDto.add(postdto);
								postId = postdto.getId();
								//生成临时岗位对照信息，防止重复插入同名岗位
								SysPost tempPost = new SysPost();
								tempPost.setId(postdto.getId());
								tempPost.setName(postdto.getName());
								tempPost.setOrgCode(postdto.getOrgCode());
								mapPost.put(tempPost.getId(), tempPost);
								namePost.put(getPostKey(tempPost.getName(),tempPost.getOrgCode()), tempPost);
							}		
							if(sysc!=null) {//有对照信息，直接更新即可
								sysc.setCodetm4(postId);//更新为新的id
								listSysComUp.add(sysc);
							}else {//无对照信息，生成对照信息
								// 3.生成对照表数据
								SysComparison sc = new SysComparison();
								String code = TMUID.getUID();
								sc.setId(code);
								sc.setCode(postid);// tm3
								sc.setCodetm4(postId);
								sc.setCommodule("post");
								sc.setComtype("tm3");
								sc.setUsed(1);
								listSysComAdd.add(sc);
								// 4.mapCom对照表map中放入这个新的岗位
								mapCom.put(postid, sc);
							}	
						}						
					}
					// ----------------单条队列数据执行事务执行--------
					try {
						entservice.begin();// 事务开始
						if (listSysComAdd != null && listSysComAdd.size() > 0) {
							entservice.insertBatch(listSysComAdd);// 添加对照表
						}
//						if (listpostUp != null && listpostUp.size() > 0) {
//							entservice.updateByIdBatch(listpostUp);// 更新岗位表
//						}
						if (listSysComUp != null && listSysComUp.size() > 0) {
							entservice.updateByIdBatch(listSysComUp);// 修改对照表
						}
						if (listPostDto != null && listPostDto.size() > 0) {
							ipbo.addPost(listPostDto);// 调用外部接口,创建岗位信息
						}
						entservice.commit();// 事务提交
						bean.setSyncStatus(1);
						bean.setSyncExecTime(date);
						bean.setSyncMemo("事务同时更新【对照表】【岗位表】【岗位关系表】成功！");
					} catch (Exception e) {
						entservice.rollback();// 事务回滚
						bean.setSyncStatus(-1);
						bean.setSyncExecTime(date);
						bean.setSyncMemo("事务同时更新【对照表】【岗位表】【岗位关系表】失败！");
						mage = mage + ";(" + postid + ")" + bean.getSyncMemo();
					}
					// -------------------更新队列表的，状态，描述等-----------------------
				}else {
					bean.setSyncMemo("同步成功(无岗位名称，不进行同步)！");
				}
				listQueueUp.add(bean);
				dao.saveBatch(listQueueUp);// 更新岗位队列表信息
			}
		}
		return mage;
	}
	/**
	 * 生成岗位key
	 * @category 
	 * <AUTHOR> 
	 * @param postName
	 * @param orgCode
	 * @return
	 */
    private String getPostKey(String postName,String orgCode) {
    	String key = postName+"_";
    	if(orgCode!=null && orgCode.length()!=0) {
    		key+=orgCode;
    	}
    	return key;
    }
//    /**
//     * 获取岗位排序
//     * @category 获取岗位排序
//     * <AUTHOR> 
//     * @param orgCode
//     * @param sortMap
//     */
//    private int getPostSort(String orgCode,HashMap<String,Integer> sortMap) {
//    	int result = 0;
//    	if(orgCode!=null && orgCode.length()!=0) {
//    		Integer sort = sortMap.get(orgCode);
//    		if(sort==null) {
//    			Where where = Where.create();
//    			where.eq(SysPost::getUsed, 1);
//    			where.gt(SysPost::getPostlevel, 0);
//    			where.eq(SysPost::getOrgCode, orgCode);
//    			sort = dao.findMaxValue(SysPost.class, SysPost::getTmSort, Integer.class, where);
//    			if (sort == null) {
//    				sort = 0;
//    			}
//    		}
//    		sort++;
//    		sortMap.put(orgCode, sort);
//    		result = sort;
//    	}else{
//    		Integer sort = sortMap.get("");
//    		if(sort==null) {
//	    		Where where = Where.create();
//				where.eq(SysPost::getUsed, 1);
//				where.gt(SysPost::getPostlevel, 0);
//				where.isEmpty(SysPost::getOrgCode);
//				sort = dao.findMaxValue(SysPost.class, SysPost::getTmSort, Integer.class, where);
//				if (sort == null) {
//					sort = 0;
//				}
//    		}	
//    		sort++;
//    		sortMap.put("", sort);
//    		result = sort;
//    	}
//    	return result;
//    }
    /**
     * 获取岗位根节点ID
     * @category 获取岗位根节点ID
     * <AUTHOR> 
     * @param orgCode
     * @param sortMap
     * @return
     */
    private String getPostRootId(String orgCode,HashMap<String,SysPost> postMap) {
    	String result = "";
    	if(orgCode!=null && orgCode.length()!=0) {
    		SysPost post = postMap.get(orgCode);
    		if(post==null) {
    			post=diyPost.getDiyPostRoot(orgCode, null);
    			postMap.put(orgCode, post);
    		}
    		if(post!=null) {
    			result=post.getId();
    		}
    	}else{
    		SysPost post = postMap.get("");
    		if(post==null) {
	    		Where where = Where.create();
	    		where.eq(SysPost::getUsed, 1);
	    		where.eq(SysPost::getPostlevel, 0);
	    		where.isEmpty(SysPost::getOrgCode);
	    		List<SysPost> listSysPost = dao.rawQueryListByWhere(SysPost.class, where);//查询岗位库根节点
				if(listSysPost!=null && listSysPost.size()>0) {
					post= listSysPost.get(0);
					postMap.put("", post);
				}
    		}	
    		if(post!=null) {
    			result=post.getId();
    		}
    	}
    	return result;
    }
    
    
    
//	/**
//	 * 执行岗位队列数据
//	 * 
//	 * @param listPostQueue
//	 * @return
//	 */
//	public String EsecQueuePost(List<SysQueuePost> listPostQueue) {
//		Date date = new Date();
//		// 创建返回信息
//		String mage = "";
//		// 判断岗位队列是否有数据
//		Where where = Where.create();
//		if (listPostQueue != null && listPostQueue.size() > 0) {
//			// 读取岗位信息表，封装MAP，用于判断
//			Map<String, SysPost> mapPost = getSysPostList();
//			// 读取岗位信息表关系表
////			Map<String,SysPostRelati> mapPostRelati=getSysPostRelatiList();
//			// 读取对照表数据
//			Map<String, SysComparison> mapCom = getSysComparisonMap("", "post");
//
//			// 获取岗位根节点
//			String rootId = "";
//			where.eq(SysPost::getUsed, 1).eq(SysPost::getPostlevel, 0);
//			List<SysPost> listSysPost = dao.rawQueryListByWhere(SysPost.class, where);
//			if (listSysPost != null && listSysPost.size() > 0) {
//				SysPost e = listSysPost.get(0);
//				// 根节点id
//				rootId = e.getId();
//			} else {
//				rootId = "";// 这个必须有
//			}
//			where = Where.create();
//			where.eq(SysPost::getUsed, 1);
//			where.gt(SysPost::getPostlevel, 0);
//			// 排序字段
//			Integer sort = dao.findMaxValue(SysPost.class, SysPost::getTmSort, Integer.class, where);
//			if (sort == null) {
//				sort = 0;
//			}
//			int m = listPostQueue.size();
//			// 遍历岗位队列表
//			for (int i = 0; i < m; i++) {
//				// 更新队列表
//				List<SysQueuePost> listQueueUp = new ArrayList<SysQueuePost>();
////				List<SysPost> listpostAdd=new ArrayList<SysPost>();
//				List<SysPost> listpostUp = new ArrayList<SysPost>();
////				List<SysPostRelati> listpostRAdd=new ArrayList<SysPostRelati>();
////				List<SysPostRelati> listpostRUp=new ArrayList<SysPostRelati>();
//				List<SysComparison> listSysComAdd = new ArrayList<SysComparison>();
//				List<SysComparison> listSysComUp = new ArrayList<SysComparison>();
//
//				List<PostDto> listPostDto = new ArrayList<PostDto>();
//
//				SysQueuePost bean = listPostQueue.get(i);
//				/** 岗位id */
//				String postid = bean.getPostid();
//				/** 岗位名称 */
//				String postName = bean.getPostName();
//				/** 机构代码 */
////				String orgdm=bean.getOrgdm();
//				/** 机构名称 */
////				String orgmc=bean.getOrgmc();
//				/** 岗位级别 */
////				Integer postlevel=bean.getPostlevel();
//				/** 同步类型 1添加，2修改，3删除 */
//				Integer syncType = bean.getSyncType();
//				/** 同步状态 1成功 -1失败 /事务更新回滚 */
////				Integer syncStatus=bean.getSyncStatus();
//				/** 同步时间 */
////				Date syncTime=bean.getSyncTime();
//				/** 同步处理时间 */
////				Date syncExecTime=bean.getSyncExecTime();
//				/** 同步备注 */
////				String syncMemo=bean.getSyncMemo();
//				/** 对照类型--TM3、TM4 */
////				String comtype=bean.getComtype();
//				bean.setSyncStatus(1);
//				bean.setSyncExecTime(date);
//				bean.setSyncMemo("同步成功！");
//				if (syncType == null) {
//					bean.setSyncStatus(-1);
//					bean.setSyncExecTime(date);
//					bean.setSyncMemo("执行类型不明确【syncType】为空，同步类型 1添加，2修改，3删除！");
//					mage = mage + ";(" + postid + ")" + bean.getSyncMemo();
//				}
//				// 删除岗位
//				if (syncType != null && syncType == 3) {
//					// 查对照表和岗位表是否存在
//					if (mapCom != null && mapCom.containsKey(postid)) {
//						// 获取TMM4岗位id
//						SysComparison sysc = mapCom.get(postid);
//						String gwid = sysc.getCodetm4();
//						// 判断tm4岗位表中是否存在这个岗位
//						if (mapPost != null && mapPost.containsKey(gwid)) {
//							SysPost epost = mapPost.get(gwid);
//							// 1.删除mapPost岗位表中的这个岗位信息
//							mapPost.remove(gwid);
//							// 2.删除mapCom对照表中的这个岗位信息
//							mapCom.remove(postid);
//							// 3.删除对照表中的这个岗位信息
//							sysc.setUsed(0);
//							listSysComUp.add(sysc);
//							// 4.删除TM4岗位表中这个岗位
//							epost.setUsed(0);
//							listpostUp.add(epost);
//							// 5.删除TM4岗位关系表中这个岗位
//							// 由于TM4岗位关系表中没有used字段，暂时不删除，以免不可恢复
//
//						} else {
//							// 如果TM4中没有这个岗位，同步状态失败
//							bean.setSyncStatus(-1);
//							bean.setSyncExecTime(date);
//							bean.setSyncMemo("同步删除失败，因为TM4岗位对照表中没有这个岗位！");
//							mage = mage + ";(" + postid + ")" + bean.getSyncMemo();
//						}
//					}
//				} else {
//					// 添加或者修改，判断，如果表中没有就添加，有就更新
//					// 判断岗位是否在对照表存在
//					if (mapCom != null && mapCom.containsKey(postid)) {
//						// 判断TM4岗位表是否存在，存在得更新
//						// 1.从mapPost中取出岗位数据
//						SysComparison sysc = mapCom.get(postid);
//						String gwid = sysc.getCodetm4();
//						if (mapPost != null && mapPost.containsKey(gwid)) {
//							// 能取出数据，更新
//							SysPost ep = mapPost.get(gwid);
//							// 更新岗位名称
//							ep.setName(postName);
//							listpostUp.add(ep);
//						} else {
//							// 没有取出岗位数据，那就新增加岗位数据，并且修改对照表数据
//							// 1.新增岗位表数据,2.新增岗位关系表数据
//							sort = sort + 1;
//							PostDto postdto = getAssignmentPostDto(postName, sort, rootId);
//							listPostDto.add(postdto);
//							// 3.修改对照表的TM4岗位id
//							SysComparison sysc1 = mapCom.get(postid);
//							sysc1.setCodetm4(postdto.getId());
//							listSysComUp.add(sysc1);
//						}
//					} else {
//						// 如果没有对照信息,就找不到TM4的岗位信息
//
//						// 1.生成岗位表数据，2.生成岗位关系表数据
//						sort = sort + 1;
//						PostDto postdto = getAssignmentPostDto(postName, sort, rootId);
//						listPostDto.add(postdto);
//						String id = postdto.getId();
//						// 3.生成对照表数据
//						SysComparison sc = new SysComparison();
//						String code = TMUID.getUID();
//						sc.setId(code);
//						sc.setCode(postid);// tm3
//						sc.setCodetm4(id);
//						sc.setCommodule("post");
//						sc.setComtype("tm3");
//						sc.setUsed(1);
//						listSysComAdd.add(sc);
//						// 4.mapCom对照表map中放入这个新的岗位
//						mapCom.put(postid, sc);
//					}
//				}
//				// ----------------单条队列数据执行事务执行--------
//				try {
//					entservice.begin();// 事务开始
//					if (listSysComAdd != null && listSysComAdd.size() > 0) {
//						entservice.insertBatch(listSysComAdd);// 添加对照表
//					}
//					if (listpostUp != null && listpostUp.size() > 0) {
//						entservice.updateByIdBatch(listpostUp);// 更新岗位表
//					}
//					if (listSysComUp != null && listSysComUp.size() > 0) {
//						entservice.updateByIdBatch(listSysComUp);// 修改对照表
//					}
//					if (listPostDto != null && listPostDto.size() > 0) {
//						ipbo.addPost(listPostDto);// 调用外部接口,创建岗位信息
//					}
//					entservice.commit();// 事务提交
//					bean.setSyncStatus(1);
//					bean.setSyncExecTime(date);
//					bean.setSyncMemo("事务同时更新【对照表】【岗位表】【岗位关系表】成功！");
//				} catch (Exception e) {
//					entservice.rollback();// 事务回滚
//					bean.setSyncStatus(-1);
//					bean.setSyncExecTime(date);
//					bean.setSyncMemo("事务同时更新【对照表】【岗位表】【岗位关系表】失败！");
//					mage = mage + ";(" + postid + ")" + bean.getSyncMemo();
//				}
//				// -------------------更新队列表的，状态，描述等-----------------------
//				listQueueUp.add(bean);
//				dao.saveBatch(listQueueUp);// 更新岗位队列表信息
//			}
//		}
//		return mage;
//	}

	/**
	 * 赋值岗位实体用于生成岗位表数据
	 * 
	 * @param gwmc
	 * @param sort
	 * @param pgwid
	 * @return
	 */
	public PostDto getAssignmentPostDto(String postName, Integer sort, String pgwid,String orgCode) {
		PostDto e = new PostDto();
		/** 当前节点id */
		e.setId(TMUID.getUID());
		/** 树形全路径 */
		e.setAllpath("");
		/** 岗位名称 */
		e.setName(postName);
		/** 岗位分类ID */
		e.setClassid("");
		/** 岗位级别ID */
		e.setLevelid("");
		/** 岗位系数 */
		e.setCoefficient(0.0);
		/** 注释 */
		e.setMemo("");
		/** 排序 */
		e.setTmSort(sort);
		/** 是否使用 */
		e.setUsed(1);
		/** 层级 */
		e.setPostlevel(0);
		/** 角色记录ID */
		e.setRoleTmuid(null);
		/** 角色ID（外键,关联:sys_role.id） */
		e.setRoleid(null);
		/** 角色名称 */
		e.setRolename("");
		/** 父节点编码 */
		e.setPcode(pgwid);
		/** 新父节点编码（用于移动节点接口，存储移动后的父节点id） */
		e.setPcodeNew("");
		/** 是否为系统内置人员（1：是，0：否） */
		e.setSys(null);
		/**岗位系数**/
		e.setCoefficient(1d);
		/**所属机构**/
		e.setOrgCode(orgCode);
		return e;
	}

	/**
	 * 赋值岗位实体用于生成岗位表数据
	 * 
	 * @param gwmc
	 * @param sort
	 * @param pgwid
	 * @return
	 */
	public PostDto getAssignmentRoleDto(String postName, Integer sort, String pgwid) {
		PostDto e = new PostDto();
		/** 当前节点id */
		e.setId("");
		/** 树形全路径 */
		e.setAllpath("");
		/** 岗位名称 */
		e.setName(postName);
		/** 岗位分类ID */
		e.setClassid("");
		/** 岗位级别ID */
		e.setLevelid("");
		/** 岗位系数 */
		e.setCoefficient(0.0);
		/** 注释 */
		e.setMemo("");
		/** 排序 */
		e.setTmSort(sort);
		/** 是否使用 */
		e.setUsed(1);
		/** 层级 */
		e.setPostlevel(0);
		/** 角色记录ID */
		e.setRoleTmuid(null);
		/** 角色ID（外键,关联:sys_role.id） */
		e.setRoleid(null);
		/** 角色名称 */
		e.setRolename("");
		/** 父节点编码 */
		e.setPcode(pgwid);
		/** 新父节点编码（用于移动节点接口，存储移动后的父节点id） */
		e.setPcodeNew("");
		/** 是否为系统内置人员（1：是，0：否） */
		e.setSys(null);

		return e;
	}

	/**
	 * 执行角色队列数据
	 * 
	 * @param listOrgQueue
	 * @return
	 */
	public String EsecQueueRole(List<SysQueueRole> listRoleQueue) {
		Date date = new Date();
		// 创建返回信息
		String mage = "";
		// 判断角色队列是否有数据
		if (listRoleQueue != null && listRoleQueue.size() > 0) {
			// 获取角色表数据
			Map<String, SysRole> mapRole = getSysRoleList();
			// 读取对照表数据
			Map<String, SysComparison> mapCom = getSysComparisonMap("", "role");

			Integer sort = 99;// 排序字段
			Where where = Where.create();
			where.eq(SysRole::getUsed, 1);
			where.eq(SysRole::getLevel, 1);
			sort = entservice.findMaxValue(SysRole.class, SysRole::getTmSort, Integer.class, where);
			if (sort == null) {
				sort = 99;
			}
			// 遍历角色队列表
			int num = listRoleQueue.size();
			for (int i = 0; i < num; i++) {
				sort = sort + i;
				// 更新队列表
				List<SysQueueRole> listQueueUp = new ArrayList<SysQueueRole>();
				List<SysRole> listRoleUp = new ArrayList<SysRole>();
				List<SysRole> listRoleadd = new ArrayList<SysRole>();
				List<SysComparison> listSysComAdd = new ArrayList<SysComparison>();
				List<SysComparison> listSysComUp = new ArrayList<SysComparison>();

				SysQueueRole bean = listRoleQueue.get(i);
				/** 角色id */
				String roleid = bean.getRoleid();
				/** 岗位名称 */
				String roleName = bean.getRoleName();
				/** 权限类别 */
//				Integer qxlb=bean.getQxlb();

				/** 同步类型 1添加，2修改，3删除 */
				Integer syncType = bean.getSyncType();
				/** 同步状态 1成功 -1失败 /事务更新回滚 */
//				Integer syncStatus=bean.getSyncStatus();
				/** 同步时间 */
//				Date syncTime=bean.getSyncTime();
				/** 同步处理时间 */
//				Date syncExecTime=bean.getSyncExecTime();
				/** 同步备注 */
//				String syncMemo=bean.getSyncMemo();
				/** 对照类型--TM3、TM4 */
//				String comtype=bean.getComtype();
				bean.setSyncStatus(1);
				bean.setSyncExecTime(date);
				bean.setSyncMemo("同步成功！");
				if (syncType == null) {
					bean.setSyncStatus(-1);
					bean.setSyncExecTime(date);
					bean.setSyncMemo("执行类型不明确【syncType】为空，同步类型 1添加，2修改，3删除！");
					mage = mage + ";(" + roleid + ")" + bean.getSyncMemo();
				} else {
					// 删除角色
					if (syncType != null && syncType == 3) {
						// 查对照表和岗位表是否存在
						if (mapCom != null && mapCom.containsKey(roleid)) {
							SysComparison sysc = mapCom.get(roleid);
							String id = sysc.getCodetm4();
							SysRole eRole = mapRole.get(id);
							// 1.删除mapRole角色表中的这个角色信息
							mapRole.remove(id);
							// 2.删除mapCom对照表中的这个角色信息
							mapCom.remove(roleid);
							// 3.删除对照表中的这个岗位信息
							sysc.setUsed(0);
							listSysComUp.add(sysc);
							// 4.删除TM4岗位表中这个岗位
							eRole.setUsed(0);
							listRoleUp.add(eRole);
						} else {
							// 没有这个角色，那就不用删除，也找不到对应TM4中的id
							bean.setSyncStatus(-1);
							bean.setSyncExecTime(date);
							bean.setSyncMemo("同步删除失败，因为TM4角色对照表中没有这个角色！");
							mage = mage + ";(" + roleid + ")" + bean.getSyncMemo();
						}

					} else {
						// 添加或者修改角色,判断，如果表中没有就添加，有就更新
						// 判断角色是否在对照表存在
						if (mapCom != null && mapCom.containsKey(roleid)) {
							// 判断TM4角色表是否存在，存在得更新
							// 1.从mapRole中取出角色数据
							SysComparison sysc = mapCom.get(roleid);
							String id = sysc.getCodetm4();
							if (mapRole != null && mapRole.containsKey(id)) {
								// 能取出数据，更新
								SysRole eRole = mapRole.get(id);
								// 更新角色名称
								eRole.setName(roleName);
								listRoleUp.add(eRole);
							} else {
								// 没有取出角色数据，那就新增加角色数据，并且修改对照表数据
								// 1.新增角色表数据
								SysRole sysrole = getAssignmentRoleDto(roleName, sort);
								listRoleadd.add(sysrole);
								// 3.修改对照表的TM4角色id
								SysComparison sysc1 = mapCom.get(roleid);
								sysc1.setCodetm4(sysrole.getId());
								listSysComUp.add(sysc1);
							}
						} else {
							// 如果没有对照信息,就找不到TM4的角色信息

							// 1.生成角色表数据
							SysRole sysrole = getAssignmentRoleDto(roleName, sort);
							listRoleadd.add(sysrole);
							String id = sysrole.getId();
							// 3.生成对照表数据
							SysComparison sc = new SysComparison();
							String code = TMUID.getUID();
							sc.setId(code);
							sc.setCode(roleid);// tm3-角色id
							sc.setCodetm4(id);
							sc.setCommodule("role");
							sc.setComtype("tm3");
							sc.setUsed(1);
							listSysComAdd.add(sc);
							// 4.mapCom对照表map中放入这个新的角色
							mapCom.put(roleid, sc);
						}
					}
				}

				// ----------------单条队列数据执行事务执行--------
//				try {
//					entservice.begin();//事务开始
//					if(listSysComAdd!=null&&listSysComAdd.size()>0) {
//						entservice.insertBatch(listSysComAdd);//添加对照表
//					}
//					if(listRoleUp!=null&&listRoleUp.size()>0) {
//						entservice.updateByIdBatch(listRoleUp);//更新角色表
//					}
//					if(listSysComUp!=null&&listSysComUp.size()>0) {
//						entservice.updateByIdBatch(listSysComUp);//修改对照表
//					}
//					if(addroledto!=null&&addroledto.getId()!=null) {
//						irs.addRole(addroledto);//调用外部接口,创建角色信息 
//					}
//					entservice.commit();//事务提交
//					bean.setSyncStatus(1);
//					bean.setSyncExecTime(date);
//					bean.setSyncMemo("事务同时更新【对照表】【角色表】成功！");
//				}catch(Exception e) {
//					entservice.rollback();//事务回滚
//					bean.setSyncStatus(-1);
//					bean.setSyncExecTime(date);
//					bean.setSyncMemo("事务同时更新【对照表】【角色表】失败！");
//					mage=mage+";("+roleid+")"+bean.getSyncMemo();
//				}

				String meges = "";
				try {
					if (listSysComAdd != null && listSysComAdd.size() > 0) {
						int mq = entservice.insertBatch(listSysComAdd);// 添加对照表
						if (mq < 1) {
							meges = "【添加对照表--失败】";
						}
					}
				} catch (Exception e) {
					log.error("添加对照表====失败", e);
				}

				try {
					if (listRoleUp != null && listRoleUp.size() > 0) {
						int mq = entservice.updateByIdBatch(listRoleUp);// 更新角色表
						if (mq < 1) {
							meges = meges + "--【更新角色表--失败】";
						}
					}
				} catch (Exception e) {
					log.error("更新角色表====报错", e);
				}

				try {
					if (listSysComUp != null && listSysComUp.size() > 0) {
						int mq = entservice.updateByIdBatch(listSysComUp);// 修改对照表
						if (mq < 1) {
							meges = meges + "--【修改对照表--失败】";
						}
					}
				} catch (Exception e) {
					log.error("修改对照表====报错", e);
				}

				try {
					if (listRoleadd != null && listRoleadd.size() > 0) {
						int mq = entservice.insertBatch(listRoleadd);// 创建角色表
						if (mq < 1) {
							meges = meges + "--【创建角色表--失败】";
						}
					}
				} catch (Exception e) {
					log.error("创建角色信息====报错", e);
				}

				if (!meges.equals("")) {
					bean.setSyncStatus(-1);
					bean.setSyncExecTime(date);
					bean.setSyncMemo(meges);
					mage = mage + ";(" + roleid + ")" + bean.getSyncMemo();
				}
				// -------------------更新队列表的，状态，描述等-----------------------
				listQueueUp.add(bean);
				dao.saveBatch(listQueueUp);// 更新岗位队列表信息
			}
		}
		return mage;
	}

	/**
	 * 赋值角色实体用于生成角色表数据
	 * 
	 * @param gwmc
	 * @param sort
	 * @param pgwid
	 * @return
	 */
	public SysRole getAssignmentRoleDto(String roleName, Integer tmSort) {
		SysRole sysRole = new SysRole();
		sysRole.setId(TMUID.getUID());
		sysRole.setPid("0");
		sysRole.setName(roleName);
		sysRole.setLevel(1);
		sysRole.setUsed(1);
		sysRole.setIsUse(1);
		sysRole.setDescription("同步角色");
		sysRole.setCreateTime(new Date());
		sysRole.setIsAdmin(0);
		sysRole.setTmSort(tmSort);
		return sysRole;
	}

	/**
	 * 执行权限队列数据
	 * 
	 * @param listOrgQueue
	 * @return
	 */
	public String EsecQueueRight(List<SysQueueRight> listRightQueue) {
		// 获取当前时间
		Date date = new Date();
		// 创建返回信息
		String mage = "";

		if (listRightQueue != null && listRightQueue.size() > 0) {
			// 读取对照表数据
			Map<String, SysComparison> mapCom = getSysComparisonMap("", "role");
			// 封装角色绑定权限的map[角色id+权限id，记录>]----最终在下面的删除时候用
//			Map<String,SysRolePerm> mapSrp=new HashMap<String,SysRolePerm>();
			Map<String, List<SysRolePerm>> mapSrpDel = new HashMap<String, List<SysRolePerm>>();
			List<SysRolePerm> listsrp = dao.rawQueryListByWhere(SysRolePerm.class, null);
			if (listsrp != null && listsrp.size() > 0) {
				int num = listsrp.size();
				for (int i = 0; i < num; i++) {
					SysRolePerm bean = listsrp.get(i);
					// 按角色id为KEY封装
					String id = bean.getRoleid();
//					String qid=bean.getPermid();
//					mapSrp.put(id+"_"+qid,bean);
					if (mapSrpDel != null && mapSrpDel.containsKey(id)) {
						List<SysRolePerm> listr = mapSrpDel.get(id);
						listr.add(bean);
						mapSrpDel.put(id, listr);
					} else {
						List<SysRolePerm> listr = new ArrayList<SysRolePerm>();
						listr.add(bean);
						mapSrpDel.put(id, listr);
					}

				}
			}
			// 封装菜单，对应的<菜单路径，菜单id>
			Map<String, String> mapPath = new HashMap<String, String>();
			// 封装菜单下，对应的<按钮名，按钮id>
			Map<String, Map<String, String>> mapPerms = new HashMap<String, Map<String, String>>();
			List<SysMenu> list = dao.rawQueryListByWhere(SysMenu.class, null);
			if (list != null && list.size() > 0) {
				int n = list.size();
				for (int i = 0; i < n; i++) {
					SysMenu bean = list.get(i);
					String id = bean.getId();
					String pid = bean.getPid();
					// 菜单路径
					String path = bean.getPath();
					// 按钮名
					String perms = bean.getPerms();

					// 菜单
					if (path != null && !path.equals("")) {
						// <菜单路径，菜单id>
						mapPath.put(bean.getPath(), bean.getId());
					}
					// 按钮
					if (perms != null && !perms.equals("")) {
						// <菜单路径 <按钮名，按钮id>>
						if (mapPerms != null && mapPerms.containsKey(pid)) {
							// 存在，就取出放入map
							Map<String, String> mapPer = mapPerms.get(pid);
							mapPer.put(perms, id);
							mapPerms.put(pid, mapPer);
						} else {
							// 不存在，就新建map
							Map<String, String> mapPer = new HashMap<String, String>();
							mapPer.put(perms, id);
							mapPerms.put(pid, mapPer);
						}
					}
				}
			}

			int num = listRightQueue.size();
			// 删除所有觉得的权限 ，删除后，下面重新添加
			List<SysRolePerm> listDel = new ArrayList<SysRolePerm>();
			for (int i = 0; i < num; i++) {
				SysQueueRight bean = listRightQueue.get(i);
				/** 权限id */
//				String rightid=bean.getRightid();
				// 角色id
				String roleid = bean.getRoleid();
				/** TM4菜单路径 */
//				String tm4path=bean.getTm4path();
				/** TM4按钮名 */
//				String tm4perms=bean.getTm4perms();

				if (mapCom != null && mapCom.size() > 0) {
					// 获取tm4中角色id
					SysComparison sc = mapCom.get(roleid);
					// tm4中对应的角色id
					String tm4_js_id = "";
					if (sc != null && sc.getCodetm4() != null) {
						tm4_js_id = sc.getCodetm4();
						// 判断角色和权限都有
						if (!"".equals(tm4_js_id)) {
							// 角色id+权限id
							if (mapSrpDel != null && mapSrpDel.containsKey(tm4_js_id)) {
								// 获取角色下的所有关联权限
								List<SysRolePerm> listr = mapSrpDel.get(tm4_js_id);
								listDel.addAll(listr);
							}
						}
					}
				}
			}

			for (int i = 0; i < num; i++) {
				// 更新队列表
				List<SysQueueRight> listQueueUp = new ArrayList<SysQueueRight>();
				// 维护角色权限绑定表
				List<SysRolePerm> listAdd = new ArrayList<SysRolePerm>();

				SysQueueRight bean = listRightQueue.get(i);
				/** 权限id */
				String rightid = bean.getRightid();
				/** 角色名称 */
				String roleid = bean.getRoleid();
				/** 权限名 */
//				String rightName=bean.getRightName();
				/** TM4菜单路径 */
				String tm4path = bean.getTm4path();
				/** TM4按钮名 */
				String tm4perms = bean.getTm4perms();

				/** 同步类型 1添加，2修改，3删除 */
//				Integer syncType=bean.getSyncType();
				/** 同步状态 1成功 -1失败 /事务更新回滚 */
//				Integer syncStatus=bean.getSyncStatus();
				/** 同步时间 */
//				Date syncTime=bean.getSyncTime();
				/** 同步处理时间 */
//				Date syncExecTime=bean.getSyncExecTime();
				/** 同步备注 */
//				String syncMemo=bean.getSyncMemo();
				/** 对照类型--TM3、TM4 */
//				String comtype=bean.getComtype();

				bean.setSyncStatus(1);
				bean.setSyncExecTime(date);
				bean.setSyncMemo("同步成功！");

				// 判断对照表是否有这个角色,通过对照表把TM4的角色信息取出来
				if (mapCom != null && mapCom.size() > 0) {
					// 获取tm4中角色id
					SysComparison sc = mapCom.get(roleid);
					// tm4中对应的角色id
					String tm4_js_id = "";
					if (sc != null) {
						tm4_js_id = sc.getCodetm4();
					}

					// 判斷TM4这边菜单表是否有值，如果没有值，那下面程序就不用走了，什么也对不上
					if (mapPath == null || mapPath.size() <= 0) {
						bean.setSyncStatus(-1);
						bean.setSyncExecTime(date);
						bean.setSyncMemo("同步权限失败，TM4的权限菜单表没有值！");
						mage = mage + ";(" + rightid + ")" + bean.getSyncMemo();
					} else {
						// 判断TM3传值--菜单字段是否有值
						if (tm4path != null && !"".equals(tm4path)) {
							// 菜单或者按钮的tmuid
							String tm4_qx_id = "";
//							if(tm4_qx_id.equals("")) {
//								System.out.println("");
//							}
							// 判断perms有没有值，如果有值证明这个队列信息是 tm3传过来的按钮权限
							if (tm4perms != null && !"".equals(tm4perms)) {
								if (mapPath.containsKey(tm4path)) {
									// 获取路径对应的 tm4的id
									String id = mapPath.get(tm4path);
									if (mapPerms.containsKey(id)) {
										// 通过路径的id 获取菜单路径下 所以的按钮权限
										Map<String, String> mapPers = mapPerms.get(id);
										if (mapPers.containsKey(tm4perms)) {
											// 通过参数中的按钮权限名取出tm4的按钮id
											tm4_qx_id = mapPers.get(tm4perms);
										}
									}
								}
							} else {
								// 如果perms没有值，那这个是菜单权限
								// 通过路径获取菜单表中得【菜单路径】--id
								if (mapPath.containsKey(tm4path)) {
									tm4_qx_id = mapPath.get(tm4path);
								}
							}

							// 判断是否找到了 权限的id
							if (tm4_qx_id == null || tm4_qx_id.equals("")) {
								// 如果没有找到，权限id
								bean.setSyncStatus(-1);
								bean.setSyncExecTime(date);
								bean.setSyncMemo("同步权限失败，未匹配到TM4中的权限！");
								mage = mage + ";(" + rightid + ")" + bean.getSyncMemo();
							} else {
								// 添加
								SysRolePerm ep = new SysRolePerm();
								ep.setId(TMUID.getUID());
								ep.setRoleid(tm4_js_id);
								ep.setPermid(tm4_qx_id);
								listAdd.add(ep);

							}
						} else {
							// TM3传值--菜单字段菜单路径没有，那就什么都匹配不上了
							bean.setSyncStatus(-1);
							bean.setSyncExecTime(date);
							bean.setSyncMemo("同步权限失败，权限表中缺少TM4相关菜单路径【tm4path】值！");
							mage = mage + ";(" + rightid + ")" + bean.getSyncMemo();
						}
					}
				} else {
					// 如果对照表没有这个角色，在TM4中就找不到这个角色的id了，同步不了
					bean.setSyncStatus(-1);
					bean.setSyncExecTime(date);
					bean.setSyncMemo("同步权限失败，TM4中未找到这个角色信息");
					mage = mage + ";(" + rightid + ")" + bean.getSyncMemo();
				}

				boolean bool = true;
				try {
					// --------执行删除/更新------------
					if (listDel != null && listDel.size() > 0) {
						int x = dao.deleteByIdBatch(listDel);
						if (x <= 0) {
							bool = false;
						}
					}

					if (listAdd != null && listAdd.size() > 0) {
						int x = dao.insertBatch(listAdd);
						if (x <= 0) {
							bool = false;
						}
					}
					if (!bool) {
						bean.setSyncStatus(-1);
						bean.setSyncExecTime(date);
						bean.setSyncMemo("同步权限失败，同步执行删除或添加[sys_role_perm]时失败");
						mage = mage + ";(" + rightid + ")" + bean.getSyncMemo();
					}
				} catch (Exception ex) {
					bean.setSyncStatus(-1);
					bean.setSyncExecTime(date);
					bean.setSyncMemo("同步权限失败，同步执行删除或添加[sys_role_perm]时，程序报错！");
					mage = mage + ";(" + rightid + ")" + bean.getSyncMemo();
				}

				// ----------更新权限队列信息------------
				listQueueUp.add(bean);
				dao.saveBatch(listQueueUp);// 更新岗位队列表信息
			}
		}
		return mage;
	}

	/**
	 * 执行人员队列数据
	 * 
	 * @param listOrgQueue
	 * @return
	 */
	@SuppressWarnings("unused")
	public String EsecQueueEmp(List<SysQueueEmp> listEmpQueue) {
		// 获取当前时间
		Date date = new Date();
		// 创建返回信息
		String mage = "";

		// 人员表
		Map<String, SysEmployeeInfo> mapEmp = getSysEmployeeInfoMap();
		// 本次同步新建的人员
		Map<String, EmployeeDto> newEmp = new HashMap<String, EmployeeDto>();
		// 读取人员对照表数据
		Map<String, SysComparison> mapComEmp = getSysComparisonMap("", "emp");
		// 机构表
		Map<String, SysEmployeeOrg> mapOrg = getSysOrgMap();
		// 读取机构对照表数据
		Map<String, SysComparison> mapComOrg = getSysComparisonMap("", "org");
		// 岗位表
		Map<String, SysEmployeeOrgPost> mapPost = getSysPostMap();
		// 读取岗位对照表数据
		Map<String, SysComparison> mapComPost = getSysComparisonMap("", "post");
		// 角色表
		Map<String, SysUserRole> mapRole = getSysRoleMap();
		// 读取角色对照表数据
		Map<String, SysComparison> mapComRole = getSysComparisonMap("", "role");
		// 登录信息表
		Map<String, SysLoginUser> mapLogin = getSysLoginUserList();

		if (listEmpQueue != null && listEmpQueue.size() > 0) {
			int num = listEmpQueue.size();
			for (int i = 0; i < num; i++) {
				// 修改队列
				List<SysQueueEmp> listQueueUp = new ArrayList<SysQueueEmp>();
				// 修改人员信息
				List<SysEmployeeInfo> listEmpinfoUp = new ArrayList<SysEmployeeInfo>();

				// 添加，修改人员对应岗位，机构，角色信息
				List<SysEmployeeOrg> listEmpOrgUp = new ArrayList<SysEmployeeOrg>();
				List<SysEmployeeOrg> listEmpOrgAdd = new ArrayList<SysEmployeeOrg>();
				List<SysEmployeeOrgPost> listEmpOrgPostUp = new ArrayList<SysEmployeeOrgPost>();
				List<SysEmployeeOrgPost> listEmpOrgPostAdd = new ArrayList<SysEmployeeOrgPost>();
				List<SysUserRole> listEmpUserRoleDel = new ArrayList<SysUserRole>();
				List<SysUserRole> listEmpUserRoleUp = new ArrayList<SysUserRole>();
				List<SysUserRole> listEmpUserRoleAdd = new ArrayList<SysUserRole>();
				List<SysLoginUser> listEmpLoginUserDel = new ArrayList<SysLoginUser>();
				List<SysLoginUser> listEmpLoginUserAdd = new ArrayList<SysLoginUser>();

				// 调用外部方法，保存人员
				List<EmployeeDto> listEmpAdd = new ArrayList<EmployeeDto>();
				// 添加对照信息
				List<SysComparison> listSysComAdd = new ArrayList<SysComparison>();
				List<SysComparison> listSysComAup = new ArrayList<SysComparison>();
				// 登录信息
				SysLoginUser slu = new SysLoginUser();
				SysQueueEmp bean = listEmpQueue.get(i);
				/** 人员id */
				String zyid = bean.getEmpid();
				/** 机构代码 */
				String orgdm = bean.getOrgdm();
				/** 岗位id */
				String gwid = bean.getPostid();
				/** 角色id */
				String rid = bean.getRoleid();
				// 用户名
				String userName = bean.getUserName();
//				//密码
				String passWord = bean.getPassword();
//				//工号
//				String staffNo=bean.getStaffNo();

				/** 同步类型 1添加，2修改，3删除 */
				Integer syncType = bean.getSyncType();
				/** 同步状态 1成功 -1失败 /事务更新回滚 */
//				Integer syncStatus=bean.getSyncStatus();
				/** 同步时间 */
//				Date syncTime=bean.getSyncTime();
				/** 同步处理时间 */
//				Date syncExecTime=bean.getSyncExecTime();
				/** 同步备注 */
//				String syncMemo=bean.getSyncMemo();
				/** 对照类型--TM3、TM4 */
//				String comtype=bean.getComtype();

				// TM4机构id
				String orgid = "";
				if (orgdm != null && mapComOrg != null && mapComOrg.containsKey(orgdm)) {
					orgid = mapComOrg.get(orgdm).getCodetm4();
				}
				// TM4岗位id
				String postid = "";
				if (gwid != null && mapComPost != null && mapComPost.containsKey(gwid)) {
					postid = mapComPost.get(gwid).getCodetm4();
				}
				if(postid==null || postid.length()==0) {
					System.out.println("人员缺少岗位信息："+bean.getEmpName()+":"+bean.getStaffNo()+":"+gwid);
				}
				
				// TM4角色id
				String roleid = "";
				if (rid != null && mapComRole != null && mapComRole.containsKey(rid)) {
					roleid = mapComRole.get(rid).getCodetm4();
				}

				bean.setSyncStatus(1);
				bean.setSyncExecTime(date);
				bean.setSyncMemo("同步成功！");
				// 执行类型
				if (syncType == null) {
					bean.setSyncStatus(-1);
					bean.setSyncExecTime(date);
					bean.setSyncMemo("执行类型不明确【syncType】为空，同步类型 1添加，2修改，3删除！");
					mage = mage + ";(" + zyid + ")" + bean.getSyncMemo();
				} else {
					String tm4Empid = "";
					// 获取 TM4中 人员的ID
					// 删除
					if (syncType == 3) {
						if (mapComEmp != null && mapComEmp.containsKey(zyid)) {
							SysComparison c = mapComEmp.get(zyid);
							tm4Empid = c.getCodetm4();

							// 删除人员，判断人员是否存在
							if (mapEmp != null && mapEmp.containsKey(tm4Empid)) {
								// 1.删除人员表数据
								SysEmployeeInfo e = mapEmp.get(tm4Empid);
								e.setUsed(0);
								listEmpinfoUp.add(e);
								// 移除这个人
								mapEmp.remove(tm4Empid);
								// 2.删除人员机构信息
								if (mapOrg != null && mapOrg.containsKey(tm4Empid)) {
									SysEmployeeOrg e1 = mapOrg.get(tm4Empid);
									e1.setUsed(0);
									listEmpOrgUp.add(e1);
								}
								// 3.删除人员岗位信息
								if (mapPost != null && mapPost.containsKey(tm4Empid)) {
									SysEmployeeOrgPost e2 = mapPost.get(tm4Empid);
									e2.setUsed(0);
									listEmpOrgPostUp.add(e2);
								}

								// 4.删除人员角色信息---由于表中没有used，直接删除
								if (mapRole != null && mapRole.containsKey(tm4Empid)) {
									SysUserRole e3 = mapRole.get(tm4Empid);
									listEmpUserRoleDel.add(e3);
								}
								// 5.删除人员登录信息---由于表中没有used，暂时不删除
								if (mapLogin != null && mapLogin.containsKey(tm4Empid)) {
									SysLoginUser e4 = mapLogin.get(tm4Empid);
									listEmpLoginUserDel.add(e4);
								}
								// 6.删除对照表人员数据
								c.setUsed(0);
								// 移除对照表map中的这个人
								mapComEmp.remove(zyid);
							} else {
								bean.setSyncStatus(-1);
								bean.setSyncExecTime(date);
								bean.setSyncMemo("删除失败，未能在人员表中找到该人员【" + tm4Empid + "】！");
								mage = mage + ";(" + zyid + ")" + bean.getSyncMemo();
							}
						} else {
							bean.setSyncStatus(-1);
							bean.setSyncExecTime(date);
							bean.setSyncMemo("在对照表中未能找到TM4的人员ID信息！");
							mage = mage + ";(" + zyid + ")" + bean.getSyncMemo();
						}
					} else {
	
						// 添加--修改
						// 判断对照表中是否有人
						if (mapComEmp != null && mapComEmp.containsKey(zyid)) {
							// 有对照关系
							SysComparison c = mapComEmp.get(zyid);
							tm4Empid = c.getCodetm4();
							// 判断人员表有没有这个人
							if (mapEmp != null && mapEmp.containsKey(tm4Empid)) {
								String old_orgid = "";
								String old_postid = "";
								String old_roleid = "";
								// 从人员机构关系表中获取，旧的机构id
								if (mapOrg != null && mapOrg.containsKey(tm4Empid)) {
									if (mapOrg.get(tm4Empid).getOrgcode() != null) {
										old_orgid = mapOrg.get(tm4Empid).getOrgcode();
									}
								}
								// 从人员岗位关系表中获取，旧的机构id
								if (mapPost != null && mapPost.containsKey(tm4Empid)) {
									if (mapPost.get(tm4Empid).getPostid() != null) {
										old_postid = mapPost.get(tm4Empid).getPostid();
									}
								}
								// 从人员角色关系表中获取，旧的机构id
								if (mapRole != null && mapRole.containsKey(tm4Empid)) {
									if (mapRole.get(tm4Empid).getRoleid() != null) {
										old_roleid = mapRole.get(tm4Empid).getRoleid();
									}
								}
								// 人员存在，获取人员信息的实体
								SysEmployeeInfo sei = mapEmp.get(tm4Empid);
								// 修改人员姓名等信息
								sei = getSysEmployeeInfoUp(sei, bean);
								listEmpinfoUp.add(sei);
								// 重新替换map中的人员信息
								mapEmp.put(tm4Empid, sei);
								// 判断机构是否和原来机构一样，如果不一样，证明该人员换机构了
//								if(!orgid.equals(old_orgid)) {
								SysEmployeeOrg empOrgPo = mapOrg.get(tm4Empid);
								if (empOrgPo != null) {// 修改
									// 修改人员对应机构的id
									empOrgPo.setOrgcode(orgid);
									listEmpOrgUp.add(empOrgPo);
								} else {
									empOrgPo = createSysEmployeeOrg(tm4Empid, orgid);
									listEmpOrgAdd.add(empOrgPo);
								}
								mapOrg.put(tm4Empid, empOrgPo);
//								}
								// 判断岗位是否和原来一样，如果不一样，证明该人员换岗位了
//								if(!postid.equals(old_postid)) {
								SysEmployeeOrgPost empOrgPostPo = mapPost.get(tm4Empid);
								if (empOrgPostPo != null) {
									empOrgPostPo.setPostid(postid);
									empOrgPostPo.setOrgcode(orgid);
									listEmpOrgPostUp.add(empOrgPostPo);
								} else {
									empOrgPostPo = createSysEmployeeOrgPost(tm4Empid, postid, orgid);
									listEmpOrgPostAdd.add(empOrgPostPo);
								}

								mapPost.put(tm4Empid, empOrgPostPo);
//								}
								// 判断角色是否和原来一样，如果不一样，证明该人员换角色了
//								
								if (roleid != null && !"".equals(roleid)) {
									SysUserRole empUserRole = mapRole.get(tm4Empid);
									if (empUserRole != null) {
										empUserRole.setRoleid(roleid);
										listEmpUserRoleUp.add(empUserRole);
									} else {
										empUserRole = new SysUserRole();
										empUserRole.setId(TMUID.getUID());
										empUserRole.setRoleid(roleid);
										empUserRole.setUserid(tm4Empid);
										listEmpUserRoleAdd.add(empUserRole);
									}
									mapRole.put(tm4Empid, empUserRole);
								}else {
									//人员没有TM3角色，人员也没有TM4角色
									if(old_roleid==null||"".equals(old_roleid)) {
										SysUserRole empUserRole = new SysUserRole();
										empUserRole.setId(TMUID.getUID());
										empUserRole.setRoleid("guest");
										empUserRole.setUserid(tm4Empid);
										listEmpUserRoleAdd.add(empUserRole);
										mapRole.put(tm4Empid, empUserRole);
									}
								}
//								}
								// 修改登录信息---判断帐号密码是否修改
								slu = iel.saveLoginInfo(tm4Empid, userName, passWord);
							} else {
								if(!newEmp.containsKey(tm4Empid)) {//没有新建过，防止人员重复
									// 人员不存在
									// 1.新建人员信息
									EmployeeDto bean1 = getEmployeeDto(bean, "", "", "");
									String empid = bean1.getEmpTmuid();
									listEmpAdd.add(bean1);
									// 2.修改人员对照表的中的TM4id
									c.setCodetm4(empid);
									listSysComAup.add(c);
									// 对照表map中放入这个新的角色
									mapComEmp.put(zyid, c);
									newEmp.put(empid, bean1);
									// 4.创建人员机构关系表中的 TM4人员id
									if (orgid != null && !"".equals(orgid)) {
										SysEmployeeOrg empOrgPo = createSysEmployeeOrg(empid, orgid);
										listEmpOrgAdd.add(empOrgPo);
										mapOrg.put(empid, empOrgPo);
									}
									// 5.创建人员岗位机构关系表中的 TM4人员id
									if (postid != null && !"".equals(postid)) {
										SysEmployeeOrgPost empOrgPostPo = createSysEmployeeOrgPost(empid, postid, orgid);
										listEmpOrgPostAdd.add(empOrgPostPo);
										mapPost.put(empid, empOrgPostPo);
									}
									// 6.创建人员角色关系表中的 TM4人员id
									if (roleid != null && !"".equals(roleid)) {
										SysUserRole empUserRole = new SysUserRole();
										empUserRole.setId(TMUID.getUID());
										empUserRole.setRoleid(roleid);
										empUserRole.setUserid(empid);
										listEmpUserRoleAdd.add(empUserRole);
										mapRole.put(empid, empUserRole);
									}else {
										//人员没有TM3角色，人员也没有TM4角色---给个默认的普通用户
										SysUserRole empUserRole = new SysUserRole();
										empUserRole.setId(TMUID.getUID());
										empUserRole.setRoleid("guest");
										empUserRole.setUserid(empid);
										listEmpUserRoleAdd.add(empUserRole);
										mapRole.put(empid, empUserRole);
									}
									// 9.创建登录信息---调用外部方法
									slu = iel.saveLoginInfo(empid, userName, passWord);
								}
							}
						} else {
							// 没有对照关系，就新建该人员
							// 1.新建人员信息
							EmployeeDto bean1 = getEmployeeDto(bean, orgid, postid, roleid);
							listEmpAdd.add(bean1);
							String empid = bean1.getEmpTmuid();
							// 2.创建人员对照表的
							SysComparison sc1 = createSysComparison(zyid, empid, "emp");
							listSysComAdd.add(sc1);
							mapComEmp.put(zyid, sc1);// 对照表map中放入这个新的角色
							newEmp.put(empid, bean1);
							// 4.创建人员机构关系表中的 TM4人员id
							if (orgid != null && !"".equals(orgid)) {
								SysEmployeeOrg empOrgPo = createSysEmployeeOrg(empid, orgid);
								listEmpOrgAdd.add(empOrgPo);
								mapOrg.put(empid, empOrgPo);
							}
							// 5.创建人员岗位机构关系表中的 TM4人员id
							if (postid != null && !"".equals(postid)) {
								SysEmployeeOrgPost empOrgPostPo = createSysEmployeeOrgPost(empid, postid, orgid);
								listEmpOrgPostAdd.add(empOrgPostPo);
								mapPost.put(empid, empOrgPostPo);
							}
							// 6.创建人员角色关系表中的 TM4人员id
							if (roleid != null && !"".equals(roleid)) {
								SysUserRole empUserRole = new SysUserRole();
								empUserRole.setId(TMUID.getUID());
								empUserRole.setRoleid(roleid);
								empUserRole.setUserid(empid);
								listEmpUserRoleAdd.add(empUserRole);
								mapRole.put(empid, empUserRole);
							}else {
								//人员没有TM3角色，人员也没有TM4角色---给个默认的普通用户
								SysUserRole empUserRole = new SysUserRole();
								empUserRole.setId(TMUID.getUID());
								empUserRole.setRoleid("guest");
								empUserRole.setUserid(empid);
								listEmpUserRoleAdd.add(empUserRole);
								mapRole.put(empid, empUserRole);
							}
							// 9.创建登录信息---调用外部方法
							slu = iel.saveLoginInfo(empid, userName, passWord);
						}

					}
				}

				// -------------------------执行事务----------------------------
				try {
					entservice.begin();// 事务开始
					// 修改人员信息
					if (listEmpinfoUp != null && listEmpinfoUp.size() > 0) {
						entservice.updateByIdBatch(listEmpinfoUp);
					}
					// 人员机构修改
					if (listEmpOrgUp != null && listEmpOrgUp.size() > 0) {
						entservice.updateByIdBatch(listEmpOrgUp);
					}
					// 人员机构添加
					if (listEmpOrgAdd != null && listEmpOrgAdd.size() > 0) {
						entservice.insertBatch(listEmpOrgAdd);
					}
					// 人员岗位修改
					if (listEmpOrgPostUp != null && listEmpOrgPostUp.size() > 0) {
						entservice.updateByIdBatch(listEmpOrgPostUp);
					}
					// 人员岗位添加
					if (listEmpOrgPostAdd != null && listEmpOrgPostAdd.size() > 0) {
						entservice.insertBatch(listEmpOrgPostAdd);
					}
					// 人员角色删除
					if (listEmpUserRoleDel != null && listEmpUserRoleDel.size() > 0) {
						entservice.deleteByIdBatch(listEmpUserRoleDel);
					}
					// 人员角色修改
					if (listEmpUserRoleUp != null && listEmpUserRoleUp.size() > 0) {
						entservice.updateByIdBatch(listEmpUserRoleUp);
					}
					// 人员角色添加
					if (listEmpUserRoleAdd != null && listEmpUserRoleAdd.size() > 0) {
						entservice.insertBatch(listEmpUserRoleAdd);
					}
					// 登录删除
					if (listEmpLoginUserDel != null && listEmpLoginUserDel.size() > 0) {
						entservice.deleteByIdBatch(listEmpLoginUserDel);
					}
					// 登录添加
					if (listEmpLoginUserAdd != null && listEmpLoginUserAdd.size() > 0) {
						entservice.insertBatch(listEmpLoginUserAdd);
					}
					// 添加对照信息
					if (listSysComAdd != null && listSysComAdd.size() > 0) {
						entservice.insertBatch(listSysComAdd);
					}
					if (listSysComAup != null && listSysComAup.size() > 0) {
						entservice.updateByIdBatch(listSysComAup);
					}
					// 保存登录
					if (slu != null && slu.getId() != null) {
						entservice.save(slu);
					}
					// 调用外部方法，添加人员信息
					if (listEmpAdd != null && listEmpAdd.size() > 0) {
						iemp.addEmployee(listEmpAdd, true);
					}
					entservice.commit();// 事务提交
					bean.setSyncStatus(1);
					bean.setSyncExecTime(date);
					bean.setSyncMemo("事务同时更新【对照表】【人员表】【人员机构】【人员岗位】【人员角色】【人员权限】【人员登录】成功！");
				} catch (Exception e) {
					entservice.rollback();// 事务回滚
					bean.setSyncStatus(-1);
					bean.setSyncExecTime(date);
					bean.setSyncMemo("事务同时更新【对照表】【人员表】【人员机构】【人员岗位】【人员角色】【人员权限】【人员登录】失败，报错！");
					mage = mage + ";(" + zyid + ")" + bean.getSyncMemo();
				}

				// ----------更新权限队列信息------------
				listQueueUp.add(bean);
				dao.saveBatch(listQueueUp);// 更新岗位队列表信息

			}
		}

		return mage;
	}

	/**
	 * 更新人员信息
	 * 
	 * @param e
	 * @param bean
	 * @return
	 */
	public SysEmployeeInfo getSysEmployeeInfoUp(SysEmployeeInfo e, SysQueueEmp bean) {
		/** 姓名 */
		e.setEmpname(bean.getEmpName());
		/** 性别 */
		e.setSex(bean.getSex() == null ? "1" : String.valueOf(bean.getSex()));
		/** mail */
		e.setMail(bean.getMail());
		/** 工号 */
		e.setStaffNo(bean.getStaffNo());
		/** 移动电话mail */
		e.setMobile(bean.getMobile());
		/** 入职时间 */
		e.setEntryDate(bean.getEntryDate());
		/** 政治面貌 */
		e.setPolitical(bean.getPolitical());
		return e;
	}

	/**
	 * 创建机构人员关系实体
	 * 
	 * @param empid
	 * @param orgid
	 * @return
	 */
	public SysEmployeeOrgPost createSysEmployeeOrgPost(String empid, String postid, String orgid) {
		SysEmployeeOrgPost empOrgPostPo = new SysEmployeeOrgPost();
		empOrgPostPo.setId(TMUID.getUID());
		empOrgPostPo.setEmpid(empid);
		empOrgPostPo.setPostid(postid);
		empOrgPostPo.setOrgcode(orgid);
		empOrgPostPo.setStatus(1);
		empOrgPostPo.setUsed(1);
		return empOrgPostPo;
	}

	/**
	 * 创建机构人员关系实体
	 * 
	 * @param empid
	 * @param orgid
	 * @return
	 */
	public SysEmployeeOrg createSysEmployeeOrg(String empid, String orgid) {
		SysEmployeeOrg empOrgPo = new SysEmployeeOrg();
		empOrgPo.setId(TMUID.getUID());
		empOrgPo.setEmpid(empid);
		empOrgPo.setOrgcode(orgid);
		empOrgPo.setStatus(1);
		empOrgPo.setUsed(1);
		return empOrgPo;
	}

	/**
	 * 创建对照关系实体类
	 * 
	 * @param tm3Id
	 * @param tm4Id
	 * @param type
	 * @return
	 */
	public SysComparison createSysComparison(String tm3Id, String tm4Id, String type) {
		SysComparison sc = new SysComparison();
		String code = TMUID.getUID();
		sc.setId(code);
		sc.setCode(tm3Id);// tm3-角色id
		sc.setCodetm4(tm4Id);
		sc.setCommodule(type);
		sc.setComtype("tm3");
		sc.setUsed(1);
		return sc;
	}

	/**
	 * 创建人员的实体类
	 * 
	 * @param bean
	 * @return
	 */
	public EmployeeDto getEmployeeDto(SysQueueEmp bean, String orgid, String postid, String roleid) {
		EmployeeDto e = new EmployeeDto();
		/** 用户记录ID */
		e.setEmpTmuid(TMUID.getUID());

		/** 机构记录ID */
		e.setOrgTmuid("");

		/** 岗位记录ID */
		e.setPostTmuid("");

		/** 角色记录ID */
		e.setRoleTmuid("");

		/** 用户姓名 */
		e.setEmpname(bean.getEmpName());

		/** 入职日期 */
		e.setEntryDate(bean.getEntryDate());

		/** 性别 */
		e.setSex(bean.getSex() == null ? "1" : String.valueOf(bean.getSex()));

		/** 工号 */
		e.setStaffNo(bean.getStaffNo());

		/** 手机号 */
		e.setMobile(bean.getMobile());

		/** 证件类型编码（1居民身份证，2军人证，3中国护照，4外国护照，5台湾居民来往大陆通行证，6港澳居民来往内地通行证，7其它） */
		e.setCardTypeCode("");

		/** 证件类型名称 */
		e.setCardTypeName("");

		/** 证件号 */
		e.setCardno("");

		/** 职务ID */
		e.setDutyid("");

		/** 职级ID */
		e.setPositionlevelid("");

		/** 政治面貌（1党员、2预备党员、3团员、4群众、5其它） */
		e.setPoliticsStatus(null);
		e.setPolitical(bean.getPolitical());
		/** 邮箱 */
		e.setMail(bean.getMail());

		/** QQ */
		e.setQq("");

		/** 微信 */
		e.setWechat("");

		/** 民族 */
		e.setNationality("");

		/** 籍贯 */
		e.setNativePlace("");

		/** 最高学历（1博士、2硕士、3本科、4大专、5高中、6初中、7小学、8其它） */
		e.setEducation(null);

		/** 婚姻状况（1是0否） */
		e.setMarital(null);

		/** 生日类型（1公立、2农历） */
		e.setBirthdayType(null);

		/** 出生日期 */
		e.setBirthday(null);

		/** 历史工龄 */
		e.setOldWorkNum(null);

		/** 员工类型（1全职、2兼职、3实习、4外派、5其它） */
		e.setStaffType(1);

		/** 工作地点 */
		e.setWorkplace("");

		/** 户口类型(1城镇、2非城镇) */
		e.setAccountType(null);

		/** 居住地址 */
		e.setLiveAddress("");

		/** 描述 */
		e.setMemo("");

		/** 人员排序 */
		e.setTmSort(null);

		/** 离退日期 */
		e.setDimissionDate(null);

		/** 人员状态 - 在职(1)、离职(0)、退休(-1) */
		e.setStatus(1);

		/** 机构编码 */
		e.setOrgcode(orgid);

		/** 是否主机构，1主机构 2兼机构 */
		e.setOrgStatus(1);

		/** 机构排序 */
		e.setOrgSort(null);

		/** 岗位ID */
		e.setPostid(postid);

		/** 是否主岗位，1主岗位2兼岗位3借调岗位 */
		e.setPostStatus(1);

		/** 岗位排序 */
		e.setPostSort(null);

		/** 角色ID */
		e.setRoleid(roleid);

		/** 是否使用 */
		e.setUsed(1);

		/** 登录名称 */
		e.setLoginUserName("");

		/** 登录账号状态 */
		e.setLoginStatus(null);

		/** 是否为系统内置人员（1：是，0：否） */
		e.setSys(null);
		return e;
	}

	/**
	 * 查询同步对照表数据
	 * 
	 * @param type
	 * @param module
	 * @return
	 */
	public Map<String, SysComparison> getSysComparisonMap(String type, String module) {
		Map<String, SysComparison> mapOrg = new HashMap<String, SysComparison>();
		Where where = Where.create();
		if (type != null && !type.equals("")) {
			where.eq(SysComparison::getComtype, type);
		}
		if (module != null && !module.equals("")) {
			where.eq(SysComparison::getCommodule, module);
		}
		where.eq(SysComparison::getUsed, 1);
		List<SysComparison> listcom = dao.rawQueryListByWhere(SysComparison.class, where);
		if (listcom != null && listcom.size() > 0) {
			int n = listcom.size();
			for (int i = 0; i < n; i++) {
				SysComparison bean = listcom.get(i);
				mapOrg.put(bean.getCode(), bean);
			}
		}
		return mapOrg;
	}

	/**
	 * 查询机构表数据
	 * 
	 * @return
	 */
	public Map<String, SysOrg> getSysOrgList() {
		Map<String, SysOrg> mapOrg = new HashMap<String, SysOrg>();
		Where where = Where.create();
		where.eq(SysOrg::getUsed, 1);
		List<SysOrg> listcom = dao.rawQueryListByWhere(SysOrg.class, where);
		if (listcom != null && listcom.size() > 0) {
			int n = listcom.size();
			for (int i = 0; i < n; i++) {
				SysOrg bean = listcom.get(i);
				mapOrg.put(bean.getId(), bean);
			}
		}
		return mapOrg;
	}

	/**
	 * 查询机构表逻辑父子关系数据
	 * 
	 * @return
	 */
	public Map<String, SysOrgRelation> getSysOrgRelationList() {
		return iSysOrgRelationServ.getOrgRelation();
//		Map<String, SysOrgRelation> mapOrg = new HashMap<String, SysOrgRelation>();
//		Where where = Where.create();
//		where.eq(SysOrg::getUsed, 1);
//		List<SysOrgRelation> listcom = dao.rawQueryListByWhere(SysOrgRelation.class, where);
//		if (listcom != null && listcom.size() > 0) {
//			int n = listcom.size();
//			for (int i = 0; i < n; i++) {
//				SysOrgRelation bean = listcom.get(i);
//				mapOrg.put(bean.getId(), bean);
//			}
//		}
//		return mapOrg;
	}

	/**
	 * 查询岗位表数据
	 * 
	 * @return
	 */
	public Map<String, SysPost> getSysPostList() {
		Map<String, SysPost> map = new HashMap<String, SysPost>();
		Where where = Where.create();
		where.eq(SysOrg::getUsed, 1);
		List<SysPost> list = dao.rawQueryListByWhere(SysPost.class, where);
		if (list != null && list.size() > 0) {
			int n = list.size();
			for (int i = 0; i < n; i++) {
				SysPost bean = list.get(i);
				map.put(bean.getId(), bean);
			}
		}
		return map;
	}

	/**
	 * 查询岗位关系表数据
	 * 
	 * @return
	 */
	public Map<String, SysPostRelati> getSysPostRelatiList() {
		Map<String, SysPostRelati> map = new HashMap<String, SysPostRelati>();
		Where where = Where.create();
		where.eq(SysOrg::getUsed, 1);
		List<SysPostRelati> list = dao.rawQueryListByWhere(SysPostRelati.class, where);
		if (list != null && list.size() > 0) {
			int n = list.size();
			for (int i = 0; i < n; i++) {
				SysPostRelati bean = list.get(i);
				map.put(bean.getCode(), bean);
			}
		}
		return map;
	}

	/**
	 * 查询角色表数据
	 * 
	 * @return
	 */
	public Map<String, SysRole> getSysRoleList() {
		Map<String, SysRole> map = new HashMap<String, SysRole>();
		Where where = Where.create();
		where.eq(SysOrg::getUsed, 1);
		List<SysRole> list = dao.rawQueryListByWhere(SysRole.class, where);
		if (list != null && list.size() > 0) {
			int n = list.size();
			for (int i = 0; i < n; i++) {
				SysRole bean = list.get(i);
				map.put(bean.getId(), bean);
			}
		}
		return map;
	}

	/**
	 * 查询角色权限关系表数据
	 * 
	 * @return
	 */
	public Map<String, SysRolePerm> getSysRolePermList() {
		Map<String, SysRolePerm> map = new HashMap<String, SysRolePerm>();
		Where where = Where.create();
		where.eq(SysOrg::getUsed, 1);
		List<SysRolePerm> list = dao.rawQueryListByWhere(SysRolePerm.class, where);
		if (list != null && list.size() > 0) {
			int n = list.size();
			for (int i = 0; i < n; i++) {
				SysRolePerm bean = list.get(i);
				map.put(bean.getRoleid(), bean);
			}
		}
		return map;
	}

	/**
	 * 查询登录表数据
	 * 
	 * @return
	 */
	public Map<String, SysLoginUser> getSysLoginUserList() {
		Map<String, SysLoginUser> map = new HashMap<String, SysLoginUser>();
		Where where = Where.create();
		List<SysLoginUser> list = dao.rawQueryListByWhere(SysLoginUser.class, where);
		if (list != null && list.size() > 0) {
			int n = list.size();
			for (int i = 0; i < n; i++) {
				SysLoginUser bean = list.get(i);
				map.put(bean.getId(), bean);
			}
		}
		return map;
	}

	/**
	 * 查询菜单表数据
	 * 
	 * @return
	 */
	public List<Object> getSysMenuPathList() {
		List<Object> listObj = new ArrayList<Object>();
		// 封装菜单，对应的<菜单路径，菜单id>
		Map<String, String> mapPath = new HashMap<String, String>();
		// 封装菜单下，对应的<按钮名，按钮id>
		Map<String, Map<String, String>> mapPerms = new HashMap<String, Map<String, String>>();
		List<SysMenu> list = dao.rawQueryListByWhere(SysMenu.class, null);
		if (list != null && list.size() > 0) {
			int n = list.size();
			for (int i = 0; i < n; i++) {
				SysMenu bean = list.get(i);
				String id = bean.getId();
				String pid = bean.getPid();
				// 菜单路径
				String path = bean.getPath();
				// 按钮名
				String perms = bean.getPerms();

				// 菜单
				if (path != null && !path.equals("")) {
					// <菜单路径，菜单id>
					mapPath.put(bean.getPath(), bean.getId());
				} else {
					// 按钮
					if (perms != null && !perms.equals("")) {
						// <菜单路径 <按钮名，按钮id>>
						if (mapPerms != null && mapPerms.containsKey(pid)) {
							// 存在，就取出放入map
							Map<String, String> mapPer = mapPerms.get(pid);
							mapPer.put(perms, id);
							mapPerms.put(pid, mapPer);
						} else {
							// 不存在，就新建map
							Map<String, String> mapPer = new HashMap<String, String>();
							mapPer.put(perms, id);
							mapPerms.put(pid, mapPer);
						}
					}
				}
			}
		}
		listObj.add(mapPath);
		listObj.add(mapPerms);
		return listObj;
	}

	/**
	 * 查询人员表数据
	 * 
	 * @return
	 */
	public Map<String, SysEmployeeInfo> getSysEmployeeInfoMap() {
		Map<String, SysEmployeeInfo> map = new HashMap<String, SysEmployeeInfo>();
		Where where = Where.create();
		where.eq(SysOrg::getUsed, 1);
		List<SysEmployeeInfo> list = dao.rawQueryListByWhere(SysEmployeeInfo.class, where);
		if (list != null && list.size() > 0) {
			int n = list.size();
			for (int i = 0; i < n; i++) {
				SysEmployeeInfo bean = list.get(i);
				map.put(bean.getId(), bean);
			}
		}
		return map;
	}

	/**
	 * 查询机构表数据
	 * 
	 * @return
	 */
	public Map<String, SysEmployeeOrg> getSysOrgMap() {
		Map<String, SysEmployeeOrg> map = new HashMap<String, SysEmployeeOrg>();
		Where where = Where.create();
		where.eq(SysOrg::getUsed, 1);
		List<SysEmployeeOrg> list = dao.rawQueryListByWhere(SysEmployeeOrg.class, where);
		if (list != null && list.size() > 0) {
			int n = list.size();
			for (int i = 0; i < n; i++) {
				SysEmployeeOrg bean = list.get(i);
				map.put(bean.getEmpid(), bean);
			}
		}
		return map;
	}

	/**
	 * 查询角色 表数据
	 * 
	 * @return
	 */
	public Map<String, SysUserRole> getSysRoleMap() {
		Map<String, SysUserRole> map = new HashMap<String, SysUserRole>();
		Where where = Where.create();
		List<SysUserRole> list = dao.rawQueryListByWhere(SysUserRole.class, where);
		if (list != null && list.size() > 0) {
			int n = list.size();
			for (int i = 0; i < n; i++) {
				SysUserRole bean = list.get(i);
				map.put(bean.getUserid(), bean);
			}
		}
		return map;
	}

	/**
	 * 查询人员岗位机构关系表数据
	 * 
	 * @return
	 */
	public Map<String, SysEmployeeOrgPost> getSysPostMap() {
		Map<String, SysEmployeeOrgPost> map = new HashMap<String, SysEmployeeOrgPost>();
		Where where = Where.create();
		where.eq(SysOrg::getUsed, 1);
		List<SysEmployeeOrgPost> list = dao.rawQueryListByWhere(SysEmployeeOrgPost.class, where);
		if (list != null && list.size() > 0) {
			int n = list.size();
			for (int i = 0; i < n; i++) {
				SysEmployeeOrgPost bean = list.get(i);
				map.put(bean.getEmpid(), bean);
			}
		}
		return map;
	}

	/**
	 * 清楚redis 中菜单缓存
	 * 
	 * <AUTHOR>
	 */

	public void delRedis() {
		log.info("正在清除待办缓存。。。");
		Collection<String> keys1 = redis.keys("SYSTEM:USER:INFO:*");
		if (keys1 != null) {
			redis.delete(keys1);
		}

		Collection<String> keys2 = redis.keys("SYSTEM:MENU:*");
		if (keys2 != null) {
			redis.delete(keys2);
		}

		Collection<String> keys3 = redis.keys("user_reference:tm3_tm4:*");
		if (keys3 != null) {
			redis.delete(keys3);
		}
		log.info("清除待办缓存完毕！！！");
	}
	/**
	 * 同步数据接口
	 * @category 
	 * <AUTHOR> 
	 * @param jsonData 接口采集到的数据
	 * @param cfg 同步参数,具体内容见 SynCollectionCfg 类注释
	 * @return String false开头代表执行失败
	 */
	@Override
	public SynResult synData(SynDataDto cfg) {
		// TODO Auto-generated method stub
		SynResult result = new SynResult();
		if(cfg!=null) {
			SynModel model = SynModelFactory.getInstance(cfg.getSynCode());
			if(model!=null) {
				result = model.synData(cfg);
			}else {
				result.setResult(false);
				result.setErrInfo("接口编码未注册："+cfg.getSynCode());
			}		
		}else {
			result.setResult(false);
			result.setErrInfo("未传入接口配置信息");
		}
		
		return result;
	}
	
	/**
	 * 拉取数据接口
	 * @category 
	 * <AUTHOR> 
	 * @param cfg 拉取参数,具体内容见 SynCollectionCfg 类注释
	 * @return String 返回 SynCollectionCfg.synTemplate中指定的内容（会替换其中的变量为实际数据） false开头代表执行失败
	 */
	@Override
	public SynResult pullData(SynDataDto cfg) {
		// TODO Auto-generated method stub
		SynResult result = new SynResult();
		if(cfg!=null) {
			SynModel model = SynModelFactory.getInstance(cfg.getSynCode());
			if(model!=null) {
				result = model.pullData(cfg);
			}else {
				result.setResult(false);
				result.setErrInfo("接口编码未注册："+cfg.getSynCode());
			}
		}else {
			result.setResult(false);
			result.setErrInfo("未传入接口配置信息");
		}
		
		return result;
	}

	
	/**
	 *	获取人员信息数据
	 * @return
	 */
	@Override
	public List<SysEmployeeInfo> getSysEmployeeInfoList(){
		List<SysEmployeeInfo> list = new ArrayList<SysEmployeeInfo>();
		Where where = Where.create();
		where.eq(SysOrg::getUsed, 1);
		List<SysEmployeeInfo> queryList = dao.rawQueryListByWhere(SysEmployeeInfo.class, where);
		if(queryList!=null&&queryList.size()>0) {
			list = queryList;
		}
		return list;
	}
	/**
	 * 获取TM3和TM4对照信息
	 * @category 获取TM3和TM4对照信息
	 * <AUTHOR> 
	 * @param dataList 对照列表
	 * @return List<SysComparisonVo>
	 */
	@Override
	public List<SysComparisonVo> getSynchronousData(List<SysComparisonVo> dataList) {
		// TODO Auto-generated method stub
		List<SysComparisonVo> result = new ArrayList<SysComparisonVo>();
		if (StringUtils.isNotEmpty(dataList)) {// 参数不为空
			List<String> idList = new ArrayList<String>();
			for(SysComparisonVo temp:dataList) {
				if(StringUtils.isNotEmpty(temp.getCodeTM3())) {
					idList.add(temp.getCodeTM3());
				}
			}
			Where where = Where.create();
			where.eq(SysComparison::getUsed, 1);
			where.in(SysComparison::getCode, idList.toArray());
			List<SysComparison> queryList = dao.queryList(SysComparison.class, where);
			if (StringUtils.isNotEmpty(queryList)) {// 不为空
				for(SysComparison temp:queryList) {
					SysComparisonVo vo =new SysComparisonVo();
					vo.setCodeTM3(temp.getCode());
					vo.setCodeTM4(temp.getCodetm4());
					vo.setCodeType(temp.getCommodule());
					result.add(vo);
				}
			}
		}

		return result;
	}
	/**
	 * 保存对照关系
	 * @category 
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	@Override
	public boolean saveSysComparison(List<SysComparison> dataList,boolean isUpdate) {
		// TODO Auto-generated method stub
		boolean result = false;
		if (StringUtils.isNotEmpty(dataList)) {// 不为空
			int res = 0;
			if(isUpdate) {
				res = dao.updateBatch(dataList, 1000);
			}else {
				res = dao.insertBatch(dataList, 1000);
			}
			if(res>0) {
				result = true;
			}
		}
		return result;
	}
	/**
	 * 获取需要关联对照的数据列表(仅返回人员，机构会自动对照)
	 * @category 
	 * <AUTHOR>
	 * @return List<SysComparisonVo>
	 */
	@Override
	public List<SysComparison> getSynchronousComparison() {
		// TODO Auto-generated method stub
		List<SysComparison> reuslt = new ArrayList<SysComparison>();
		Where where = Where.create();
		where.eq(SysComparison::getUsed, 1);
		where.isNull(SysComparison::getCode);//查没有对照关系的
		List<SysComparison> queryList = dao.queryList(SysComparison.class, where);
		if (StringUtils.isNotEmpty(queryList)) {// 不为空
			List<SysComparison> orgList = new ArrayList<SysComparison>();
			for(SysComparison temp:queryList) {
				if("org".equals(temp.getCommodule())) {
					SysOrg org =ios.findOrgById(temp.getCodetm4());
					if(org!=null && StringUtils.isNotEmpty(org.getOrgNumber())) {//有机构绑定的编码
						temp.setCode(org.getOrgNumber());//直接绑定对照关系
						orgList.add(temp);
					}
				}else if("emp".equals(temp.getCommodule())) {
					SysEmployeeInfo emp =iSysEmpServ.findEmployeeById(temp.getCodetm4());
					if(emp!=null && StringUtils.isNotEmpty(emp.getStaffNo())) {//有工号	
						temp.setCode(emp.getStaffNo());//将工号带回带对照信息
						reuslt.add(temp);
					}
				}
			}
			if(StringUtils.isNotEmpty(orgList)) {
				dao.updateBatch(orgList, 1000);//直接更新机构对照关系
			}
		}
		return reuslt;
	}
	/**
	 * 获取需要关联对照的数据列表(仅返回人员，机构会自动对照)
	 * @category 
	 * <AUTHOR>
	 * @return List<SysComparisonVo>
	 */
	@Override
	public List<SysComparisonVo> getSynchronousComparisonVo() {
		// TODO Auto-generated method stub
		List<SysComparisonVo> reuslt = new ArrayList<SysComparisonVo>();
		List<SysComparison> queryList = getSynchronousComparison();
		if (StringUtils.isNotEmpty(queryList)) {// 不为空
			for(SysComparison temp:queryList) {
				SysComparisonVo vo = new SysComparisonVo();
				vo.setCodeTM3(temp.getCode());
				vo.setCodeTM4(temp.getCodetm4());
				vo.setCodeType(temp.getCommodule());
				reuslt.add(vo);
			}
		}
		return reuslt;
	}
	
	/**
	 * 更新TM3对照关系（人员）
	 * @category 
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	@Override
	public boolean updateTM3Comparison(List<SysComparisonVo> dataList) {
		// TODO Auto-generated method stub
		boolean result = false;
		if (StringUtils.isNotEmpty(dataList)) {// 不为空
			List<SysComparison> queryList = getSynchronousComparison();
			if (StringUtils.isNotEmpty(queryList)) {// 不为空
				HashMap<String,SysComparison> dataMap = new HashMap<String,SysComparison>();
				for(SysComparison temp:queryList){
					dataMap.put(temp.getCommodule()+"_"+temp.getCodetm4(), temp);
				}
				List<SysComparison> saveList = new ArrayList<SysComparison>();
				for(SysComparisonVo temp:dataList) {
					if(StringUtils.isNotEmpty(temp.getCodeTM3()) && StringUtils.isNotEmpty(temp.getCodeTM4())) {//对照关系已回传
						SysComparison bean = dataMap.get(temp.getCodeType()+"_"+temp.getCodeTM4());
						if(bean!=null) {
							bean.setCode(temp.getCodeTM3());
							saveList.add(bean);
						}
					}
				}
				if (StringUtils.isNotEmpty(saveList)) {// 不为空
					result = this.saveSysComparison(saveList, true);
				}
			}
		}
		return result;
	}


}
