package com.yunhesoft.system.employee.utils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间工具 
 * @category 时间工具 
 * <AUTHOR>
 */
public class DateUtil {
	public static final String DAY = "day";
	public static final String HOUR = "hour";
	public static final String MINITE = "minute";
	public static final String SECOND = "second";
	/** 北京时间 */
	static ZoneId ZONEID_BJ = ZoneId.of("GMT+08:00");

	/**
	 * 将字符串转换为日期
	 * @category 将字符串转换为日期
	 * @param dt
	 */
	public static Date parseDate(String text) {
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("YYYY-MM-dd");
		LocalDateTime lcdt = LocalDateTime.parse(text, dtf);
		Date dt = Date.from(lcdt.atZone(ZONEID_BJ).toInstant());
		return dt;
	}

	/**
	 * 将字符串转换为日期
	 * @category 将字符串转换为日期
	 * @param dt
	 */
	public static LocalDate parseLocalDate(String text) {
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("YYYY-MM-dd");
		LocalDate lcdt = LocalDate.parse(text, dtf);
		return lcdt;
	}

	/**
	 * 将日期转换为字符串
	 * @category 将日期转换为字符串
	 * @param dt
	 */
	public static String formatDate(Date dt) {
		LocalDate lcdt = ZonedDateTime.ofInstant((dt).toInstant(), ZONEID_BJ).toLocalDate();
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("YYYY-MM-dd");
		return lcdt.format(dtf);
	}

	/**
	 * 将日期转换为字符串
	 * @category 将日期转换为字符串
	 * @param dt
	 */
	public static String formatDate(LocalDate dt) {
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("YYYY-MM-dd");
		return dt.format(dtf);
	}

	/**
	 * 将时间转换为字符串
	 * @category 将时间转换为字符串
	 * @param dt
	 */
	public static String formatDateTime(Date dt) {
		LocalDate lcdt = ZonedDateTime.ofInstant((dt).toInstant(), ZONEID_BJ).toLocalDate();
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("YYYY-MM-dd HH:mm:ss");
		return lcdt.format(dtf);
	}

	/**
	 * 将时间转换为字符串
	 * @category 将时间转换为字符串
	 * @param dt
	 */
	public static String formatDateTime(LocalDate dt) {
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("YYYY-MM-dd HH:mm:ss");
		return dt.format(dtf);
	}

	/**
	 * 获取当前时间字符串
	 * @category 获取当前时间字符串
	 * @return
	 */
	public static String getNowStr() {
		LocalDate now = LocalDate.now();
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("YYYY-MM-dd HH:mm:ss");
		return now.format(dtf);
	}

	/**
	 * 比较时间大小（当d1>d2，返回true；其它返回false；如计算错误返回null）
	 * @category 比较时间大小
	 * @param d1 支持Date、LocalDate
	 * @param d2 支持Date、LocalDate
	 * @return
	 */
	public static Boolean isAfter(Object d1, Object d2) {
		try {
			LocalDate lD1 = null;
			LocalDate lD2 = null;
			if (d1 != null) {
				if (d1 instanceof LocalDate) {
					lD1 = (LocalDate) d1;
				} else if (d1 instanceof Date) {
					lD1 = ZonedDateTime.ofInstant(((Date) d1).toInstant(), ZONEID_BJ).toLocalDate();
				} else {
					return null;
				}
			}

			if (d2 != null) {
				if (d2 instanceof LocalDate) {
					lD2 = (LocalDate) d2;
				} else if (d2 instanceof Date) {
					lD2 = ZonedDateTime.ofInstant(((Date) d2).toInstant(), ZONEID_BJ).toLocalDate();
				} else {
					return null;
				}
			}

			return lD1.isAfter(lD2);
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 比较时间大小（当d1<d2，返回true；其它返回false；如计算错误返回null）
	 * @category 比较时间大小
	 * @param d1 支持Date、LocalDate
	 * @param d2 支持Date、LocalDate
	 * @return
	 */
	public static Boolean isBefore(Object d1, Object d2) {
		try {
			LocalDate lD1 = null;
			LocalDate lD2 = null;
			if (d1 != null) {
				if (d1 instanceof LocalDate) {
					lD1 = (LocalDate) d1;
				} else if (d1 instanceof Date) {
					lD1 = ZonedDateTime.ofInstant(((Date) d1).toInstant(), ZONEID_BJ).toLocalDate();
				} else {
					return null;
				}
			}

			if (d2 != null) {
				if (d2 instanceof LocalDate) {
					lD2 = (LocalDate) d2;
				} else if (d2 instanceof Date) {
					lD2 = ZonedDateTime.ofInstant(((Date) d2).toInstant(), ZONEID_BJ).toLocalDate();
				} else {
					return null;
				}
			}

			return lD1.isBefore(lD2);
		} catch (Exception e) {
			return null;
		}
	}/**
	 * 对秒进行加减
	 */
	public static Date doSecond(Date dt, int second) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.SECOND, second);

		return cal.getTime();
	}

	/**
	 * 对分钟进行加减
	 */
	public static Date doMinute(Date dt, int minute) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.MINUTE, minute);

		return cal.getTime();
	}

	/**
	 * 对日期进行加减天数
	 */
	public static Date doDate(Date dt, int day) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.DAY_OF_MONTH, day);

		return cal.getTime();
	}

	/**
	 * 对月份进行加减
	 */
	public static Date doMonth(Date dt, int yue) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.MONTH, yue);

		return cal.getTime();
	}

	/**
	 * 对月份进行加减
	 * @param
	 * 		month 月份字符串 yyyy-MM
	 * @param
	 * 		yue 月 -100~100之间，-1代表上个月，1代表下个月，0代表当前月
	 * @return yyyy-MM(出错，返回传入月份)
	 */
	public static String doMonth(String month, String yue) {
		String retValue = month;
		if(yue!=null && !"".equals(yue.trim())){
			try{
				int iyue = Integer.parseInt(yue);
				String tempMonth = month+"-01";
				retValue = DateUtil.format(DateUtil.doMonth(DateUtil.parseDate(tempMonth), iyue), "yyyy-MM");
			}catch(Exception ex){
				retValue = month;
				ex.printStackTrace();
			}
		}
		return retValue;
	}

	/**
	 * 对小时进行加减
	 */
	public static Date doHour(Date dt, int hour) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.HOUR, hour);

		return cal.getTime();
	}

	/**
	 * 对年进行加减
	 */
	public static Date doYear(Date dt, int nian) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.YEAR, nian);

		return cal.getTime();
	}

	/**
	 * 日期相差天数 dataSort: day hour minute second
	 *
	 * @param d1
	 * @param d2
	 * @return
	 */
	public static long diffDate(Date d1, Date d2, String dataSort) {
		if (d1 == null || d2 == null)
			return 0;
		long l1 = d1.getTime();
		long l2 = d2.getTime();
		if (DAY.equals(dataSort)) {
			return (l1 - l2) / (1000 * 60 * 60 * 24);
		} else if (HOUR.equals(dataSort)) {
			return (l1 - l2) / (1000 * 60 * 60);
		} else if (MINITE.equals(dataSort)) {
			return (l1 - l2) / (1000 * 60);
		} else if (SECOND.equals(dataSort)) {
			return (l1 - l2) / 1000;
		}
		return 0;
	}

	/**
	 * 格式化输出日期
	 *
	 * @param dt
	 *            日期 格式：自定义
	 * @return 字符串
	 */
	public static String format(Date dt, String s) {
		if (dt == null) {
			return "";
		} else {
			try {
				return new SimpleDateFormat(s).format(dt);
			} catch (Exception e) {
				return "";
			}
		}
	}
	/**
	 * 日期相差秒数
	 * @d
	 * @param d1
	 * @param d2
	 * @return long
	 */

	public static long diffSecond(Date d1, Date d2) {
		if (d1 == null || d2 == null)
			return 0;
		long l1 = d1.getTime();
		long l2 = d2.getTime();
		long l = Math.abs(((l1 - l2) / 1000));
		return l;
	}
}
