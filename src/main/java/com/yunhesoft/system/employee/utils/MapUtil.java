package com.yunhesoft.system.employee.utils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.yunhesoft.system.auth.entity.po.SysLoginUser;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.post.entity.vo.PostVo;
import com.yunhesoft.system.role.entity.po.SysRole;
import com.yunhesoft.system.role.entity.po.SysUserRole;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
public class MapUtil {
	/**
	 * 获取人员机构Map，key：人员信息记录id
	 * 
	 * @category 获取人员机构Map
	 * @param list
	 * @return
	 */
	public Map<String, SysEmployeeOrg> getEmpidOrgMap(List<SysEmployeeOrg> list) {
		Map<String, SysEmployeeOrg> map = new LinkedHashMap<String, SysEmployeeOrg>();

		try {
			if (list != null && list.size() > 0) {
				for (SysEmployeeOrg b : list) {
					map.put(b.getEmpid(), b);
				}
			}
		} catch (Exception e) {
			log.error("",e);
		}

		return map;
	}

	/**
	 * 获取人员机构岗位Map，key：人员信息记录id
	 * 
	 * @category 获取人员机构岗位Map
	 * @param list
	 * @return
	 */
	public Map<String, SysEmployeeOrgPost> getEmpidOrgPostMap(List<SysEmployeeOrgPost> list) {
		Map<String, SysEmployeeOrgPost> map = new LinkedHashMap<String, SysEmployeeOrgPost>();

		try {
			if (list != null && list.size() > 0) {
				for (SysEmployeeOrgPost b : list) {
					map.put(b.getEmpid(), b);
				}
			}
		} catch (Exception e) {
			log.error("",e);
		}

		return map;
	}

	/**
	 * 获取人员角色Map，key：人员信息记录id
	 * 
	 * @category 获取人员角色Map
	 * @param list
	 * @return
	 */
	public Map<String, List<SysUserRole>> getEmpidRoleMap(List<SysUserRole> list) {
		Map<String, List<SysUserRole>> map = new LinkedHashMap<String, List<SysUserRole>>();
		try {
			if (list != null && list.size() > 0) {
				for (SysUserRole b : list) {
					List<SysUserRole> listmap = map.get(b.getUserid());
					if (listmap == null) {
						listmap = new ArrayList<SysUserRole>();
						map.put(b.getUserid(), listmap);
					}
					if (!listmap.contains(b)) {
						listmap.add(b);
					}
				}
			}
		} catch (Exception e) {
			log.error("",e);
		}
		return map;
	}

	/**
	 * 获取岗位信息Map，key：岗位id，value：岗位信息（岗位模块数据）
	 * 
	 * @category 获取岗位信息Map
	 * @param list
	 * @return
	 */
	public Map<String, PostVo> getPostIdMap(List<PostVo> list) {
		Map<String, PostVo> map = new LinkedHashMap<String, PostVo>();

		try {
			if (list != null && list.size() > 0) {
				for (PostVo postVo : list) {
					String postid = postVo.getId();
					map.put(postid, postVo);
				}
			}
		} catch (Exception e) {
			log.error("",e);
		}

		return map;
	}

	/**
	 * 获取机构信息Map，key：机构编码，value：机构信息（机构模块数据）
	 * 
	 * @category 获取机构信息Map
	 * @param list
	 * @return
	 */
	public Map<String, SysOrg> getOrgcodeMap(List<SysOrg> list) {
		Map<String, SysOrg> extOrgMap = new LinkedHashMap<String, SysOrg>();

		try {
			if (list != null && list.size() > 0) {
				for (SysOrg org : list) {
					String orgcode = org.getOrgcode();
					extOrgMap.put(orgcode, org);
				}
			}
		} catch (Exception e) {
			log.error("",e);
		}

		return extOrgMap;
	}

	/**
	 * 获取角色信息Map，key：角色编码，value：角色信息（角色模块数据）
	 * 
	 * @category 获取角色信息Map
	 * @param list
	 * @return
	 */
	public Map<String, SysRole> getRoleIdMap(List<SysRole> list) {
		Map<String, SysRole> extRoleMap = new LinkedHashMap<String, SysRole>();

		try {
			if (list != null && list.size() > 0) {
				for (SysRole role : list) {
					String roleid = role.getId();
					extRoleMap.put(roleid, role);
				}
			}
		} catch (Exception e) {
			log.error("",e);
		}

		return extRoleMap;
	}
	
	/**
	 * 获取登录信息Map，key：id，value：登录信息
	 * 
	 * @category 获取登录信息Map
	 * @param list
	 * @return
	 */
	public Map<String, SysLoginUser> getLoginIdMap(List<SysLoginUser> list) {
		Map<String, SysLoginUser> extLoginMap = new LinkedHashMap<String, SysLoginUser>();

		try {
			if (list != null && list.size() > 0) {
				for (SysLoginUser login : list) {
					String id = login.getId();
					extLoginMap.put(id, login);
				}
			}
		} catch (Exception e) {
			log.error("",e);
		}

		return extLoginMap;
	}
	/**
	 * 获取人员兼岗Map，key：人员信息记录id
	 * 
	 * @category 获取人员兼岗Map
	 * @param list
	 * @return
	 */
	public Map<String, List<SysEmployeeOrgPost>> getEmpidPartOrgPostMap(List<SysEmployeeOrgPost> list) {
		Map<String, List<SysEmployeeOrgPost>> map = new LinkedHashMap<String, List<SysEmployeeOrgPost>>();
		try {
			if (list != null && list.size() > 0) {
				for (SysEmployeeOrgPost b : list) {
					List<SysEmployeeOrgPost> listmap = map.get(b.getEmpid());
					if (listmap == null) {
						listmap = new ArrayList<SysEmployeeOrgPost>();
						map.put(b.getEmpid(), listmap);
					}
					if (!listmap.contains(b)) {
						listmap.add(b);
					}
				}
			}
		} catch (Exception e) {
			log.error("",e);
		}
		return map;
	}
}
