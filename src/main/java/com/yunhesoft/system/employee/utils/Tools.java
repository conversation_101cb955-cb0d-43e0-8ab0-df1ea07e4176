package com.yunhesoft.system.employee.utils;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.employee.entity.dto.EmpParamDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeDto;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.impl.EmployeeBasicOperationImpl;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.org.entity.dto.SysOrgAdd;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.entity.po.SysOrgRelation;
import com.yunhesoft.system.org.service.ISysOrgRelationService;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.post.entity.dto.PostParamDto;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.post.service.ISysPostService;
import com.yunhesoft.system.role.service.ISysRoleService;

import lombok.extern.log4j.Log4j2;

/**
 * 系统信息类
 * 
 * <AUTHOR>
 *
 */
@Log4j2
@Service
public class Tools {
	@Autowired
	private AuthService authService;
	@Autowired
	private ISysRoleService roleServ; // 角色操作类
	@Autowired
	private EmployeeBasicOperationImpl empServ;// 人员操作类
	@Autowired
	private ISysOrgService orgServ;// 机构操作类
	@Autowired
	private ISysOrgRelationService orgRelServ;// 组织机构关系表
	@Autowired
	private ISysPostService postServ; // 岗位

	private int USER_CITY = 2; // 市科技局管理员
	private int USER_DISTRICT = 3; // 区科技局管理员
	private int USER_ENTERPRISE = 4; // 企业用户
	private int USER_GUEST = -1;// 未注册用户

	/**
	 * 获得企业用户默认岗位ID
	 * 
	 * @return
	 */
	public String getEnterprisePostId() {
		return "user_enterprise";
	}

	/**
	 * 获得企业用户默认角色ID
	 * 
	 * @return
	 */
	public String getEnterpriseRoleId() {
		return "user_enterprise";
	}

	/**
	 * 判断用户是否为企业用户
	 * 
	 * 1:超级管理员；2：市科技局管理员；3：区科技局管理员；4：企业用户
	 * 
	 * @param userId
	 * @return
	 */
	public boolean isEnterpriseUser(String userId) {
		Boolean bln = roleServ.hasAdminType(userId, USER_GUEST);// 未注册用户判断
		if (bln == null || bln == true) {// 如果为空或者是TRUE，说明是未注册用户
			return true;
		}
		bln = roleServ.hasAdminType(userId, USER_ENTERPRISE);// 企业用户判断
		return bln;
	}

	/**
	 * 判断当前登录用户用户是否为企业用户
	 * 
	 * @return
	 */
	public boolean isEnterpriseUser() {
		SysUser user = SysUserHolder.getCurrentUser();
		if (user != null) {
			return isEnterpriseUser(user.getId());
		} else {
			return false;
		}
	}

	/**
	 * 判断用户是否为区科技局管理员
	 * 
	 * 1:超级管理员；2：市科技局管理员；3：区科技局管理员；4：企业用户
	 * 
	 * @param userId
	 * @return
	 */
	public boolean isDistrictUser(String userId) {
		return roleServ.hasAdminType(userId, USER_DISTRICT);
	}

	/**
	 * 判断当前登录用户用户是否为区科技局管理员
	 * 
	 * @return
	 */
	public boolean isDistrictUser() {
		SysUser user = SysUserHolder.getCurrentUser();
		if (user != null) {
			return isDistrictUser(user.getId());
		} else {
			return false;
		}
	}

	/**
	 * 判断用户是否为区科技局管理员
	 * 
	 * 1:超级管理员；2：市科技局管理员；3：区科技局管理员；4：企业用户
	 * 
	 * @param userId
	 * @return
	 */
	public boolean isCityUser(String userId) {
		return roleServ.hasAdminType(userId, USER_CITY);
	}

	/**
	 * 判断当前登录用户用户是否为区科技局管理员
	 * 
	 * @return
	 */
	public boolean isCityUser() {
		SysUser user = SysUserHolder.getCurrentUser();
		if (user != null) {
			return isCityUser(user.getId());
		} else {
			return false;
		}
	}

	/**
	 * 企业审核通过后自动添加组织机构和人员信息
	 * 
	 * @param org    [orgNuber]:企业唯一识别码 ;[orgName] :企业名称 ; [porgcode]:所属区域编码]
	 * @param employ [empname]
	 *               :姓名;[empTmuid]:员工ID（和用户登录表一致），[mobile]:电话；[candno]:身份证号
	 * @return
	 */
	public boolean initOrgAndEmp(SysOrgAdd org, EmployeeDto employ) {
		boolean bln = true;
		if (org != null && org.getOrgNumber() != null) {
			/*
			 * SysOrg sysOrg = null; List<SysOrg> listOrg =
			 * orgServ.getOrgByOrgNumber(org.getOrgNumber()); if
			 * (StringUtils.isEmpty(listOrg)) {// 不存在，需新建 // 1、查询所在区机构代码 SysOrg disOrg =
			 * getDisOrg(org.getPorgcode()); if (disOrg == null) { // 未找到父级机构（区）
			 * log.info("同步企业用户人员（" + org.getOrgNumber() + "，" + employ.getEmpname() +
			 * "）失败，详细信息未检索到所在区（" + org.getPorgcode() + "）机构!"); return false; }
			 * org.setPorgcode(disOrg.getId()); org.setOrgType("enterprise");
			 * org.setOrgpath(disOrg.getOrgpath()); org.setOrglevel(disOrg.getOrglevel());
			 * 
			 * List<SysOrgAdd> listAdd = new ArrayList<SysOrgAdd>(); listAdd.add(org);
			 * List<SysOrgTreeData> listtree = orgServ.insertData(listAdd); // String error
			 * = orgServ.insertData(listAdd);// 添加机构 if (StringUtils.isEmpty(listtree)) {
			 * log.info("同步企业用户机构" + org.getOrgNumber() + "失败!"); return false; } else {
			 * sysOrg = ObjUtils.copyTo(listtree.get(0), SysOrg.class); }
			 * 
			 * listOrg = orgServ.getOrgByOrgNumber(org.getOrgNumber()); if
			 * (StringUtils.isNotEmpty(listOrg)) {// 不存在，需新建 sysOrg = listOrg.get(0); } else
			 * { log.info("同步企业用户机构" + org.getOrgNumber() + "失败!"); }
			 * 
			 * } else { sysOrg = listOrg.get(0); } // 企业添加完毕，开始添加人员==========
			 * employ.setSex("1"); employ.setStaffNo("E" + employ.getMobile());// 工号
			 * employ.setOrgStatus(1); employ.setOrgcode(sysOrg.getId()); // 机构信息
			 * employ.setStatus(1); employ.setPostStatus(1);
			 * employ.setPostid(getEnterprisePostId());// 岗位
			 * employ.setRoleid(getEnterpriseRoleId());// 角色 List<EmployeeDto> add = new
			 * ArrayList<EmployeeDto>(); add.add(employ); String s =
			 * empServ.addEmployee(add); if (StringUtils.isNotEmpty(s)) {
			 * log.info("同步企业用户人员（" + org.getOrgNumber() + "，" + employ.getEmpname() +
			 * "）失败，详细信息：" + s); return false; }
			 */}
		return bln;

	}

	/**
	 * 获得企业用户默认岗位
	 * 
	 * @return
	 */
	public SysPost getPost(String postId) {
		PostParamDto param = new PostParamDto();
		param.setId(postId);
		param.setUsed(1);
		List<SysPost> listPost = postServ.getPost(param);
		if (StringUtils.isNotEmpty(listPost)) {
			return listPost.get(0);
		} else {
			return null;
		}
	}

	/**
	 * 获取区所在机构
	 * 
	 * @param areacode
	 * @return
	 */
	public SysOrg getDisOrg(String areacode) {
		SysOrg org = null;
		List<SysOrg> listOrg = orgServ.getOrgByType("district");
		if (StringUtils.isNotEmpty(listOrg)) {
			for (SysOrg sysOrg : listOrg) {
				if (areacode.equals(sysOrg.getOrgNumber())) {
					return sysOrg;
				}
			}
		}
		return org;
	}

	/**
	 * 根据用户ID获取用户企业所在区编码
	 * 
	 * @param userId
	 * @return
	 */
	public String getAreaCodeByUserId(String userId) {
		SysUser user = authService.getUser(userId);
		if (user != null) {
			List<SysOrgRelation> list = orgRelServ.listData(user.getOrgId());
			if (StringUtils.isNotEmpty(list)) {
				return getAreaCodeByOrgId(list.get(0).getPorgcode());
				/*
				 * List<SysOrg> listOrg = orgServ.listData(list.get(0).getPorgcode()); if
				 * (StringUtils.isNotEmpty(listOrg)) { return listOrg.get(0).getOrgNumber(); }
				 */
			}
		}
		return null;
	}

	/**
	 * 根据用户ID获取用户科技局所在区编码
	 * 
	 * @param userId
	 * @return
	 */
	public String getAreaCodeByUserId_Government(String userId) {
		SysUser user = authService.getUser(userId);
		if (user != null) {
			List<SysOrgRelation> list = orgRelServ.listData(user.getOrgId());
			if (StringUtils.isNotEmpty(list)) {
				return getAreaCodeByOrgId(list.get(0).getOrgcode());
			}
		}
		return null;
	}

	/**
	 * 通过机构代码获得企业所在区代码
	 * 
	 * @param orgId
	 * @return
	 */
	public String getAreaCodeByOrgId(String orgId) {
		List<SysOrg> listOrg = orgServ.listData(orgId);
		if (StringUtils.isNotEmpty(listOrg)) {
			return listOrg.get(0).getOrgNumber();
		}
		return null;
	}

	/**
	 * 获得我管辖的机构ID列表
	 * 
	 * @param userId
	 * @return
	 */
	public List<String> getGxOrgId(String userId) {
		List<String> list = new ArrayList<String>();
		if (isEnterpriseUser(userId)) {// 企业用户
			SysUser user = authService.getUser(userId);
			if (user != null) {
				list.add(user.getOrgId());// 返回自己的orgID
			}
		} else if (isCityUser(userId)) {// 市科技局用户
			return null;
		} else if (isDistrictUser(userId)) {// 区科技局用户
			// 返回自己所在区的企业ID
			String areacode = getAreaCodeByUserId_Government(userId);
			if (StringUtils.isNotEmpty(areacode)) {
				// 查询本区的所有企业
				SysOrg disOrg = getDisOrg(areacode);
				if (disOrg != null) {
					List<SysOrgRelation> listOrg = orgRelServ.listDataPids(disOrg.getId());
					if (StringUtils.isNotEmpty(listOrg)) {
						for (SysOrgRelation sysOrgRelation : listOrg) {
							list.add(sysOrgRelation.getOrgcode());
						}
					}
				}
			}

		}
		return list;
	}

	/**
	 * 根据人员id获取机构的编码（企业用户为社会唯一编码）
	 * 
	 * @param userId
	 * @return
	 */
	public String getOrgNumber(String userId) {
		String orgNumber = null;
		List<EmployeeVo> list = empServ.getEmployee(userId);
		if (StringUtils.isNotEmpty(list)) {
			orgNumber = list.get(0).getOrgNumber();
		}
		return orgNumber;
	}

	/**
	 * 根据员工id获得企业下所有人的人员信息
	 * 
	 * @param userId 员工id
	 * @return
	 */
	public List<EmployeeVo> getAllUser(String userId) {
		List<EmployeeVo> list = null;
		SysUser user = authService.getUser(userId);
		if (user != null) {
			String orgcode = user.getOrgId();
			EmpParamDto param = new EmpParamDto();
			param.setOrgcode(orgcode);
			list = empServ.getEmployee(param);
		}
		return list;
	}

}
