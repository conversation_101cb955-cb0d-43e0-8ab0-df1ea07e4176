package com.yunhesoft.system.employee.entity.dto;

import java.util.List;

import lombok.Data;

@Data
public class EmployeeQueryDto {
	/**
	 * 变动日期
	 */
	private String changeDt;
	/**
	 * 机构代码
	 */
	private String orgCode;
	/**
	 * 人员id列表
	 */
	private List<String> empIds;
	/**
	 * 人员id列表
	 */
	private List<String> empNames;
	/**
	 * 人员工号列表
	 */
	private List<String> staffNos;
	/**
	 * 是否包含岗位变动信息，true时带岗位变动信息，false或null不带岗位变动信息，不需要岗位信息时不要给true，可以加快查询速度
	 */
	private Boolean hasPostChange;
	
	/**
	 * 是否包含指定机构的全部子机构 false为不含子机构，true或null 含全部子机构
	 */
	private Boolean readAllSubOrg;
	
}
