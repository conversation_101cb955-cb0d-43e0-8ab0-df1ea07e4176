package com.yunhesoft.system.employee.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 人员机构表（索引{关系}、兼机构）
 * 
 * <AUTHOR>
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_EMPLOYEE_ORG")
public class SysEmployeeOrg extends BaseEntity {
	/** */
	private static final long serialVersionUID = 1L;

	/** 记录总数，用于分页计算 */
//	@TableField(exist = false)
	@Transient
	private Long recordCount;

	/** 用户ID（关联:sys_employee_info.id） */
	@Column(name = "empid", length = 50)
	private String empid;

	/** 机构编码（关联:sys_org.orgcode） */
	@Column(name = "orgcode", length = 50)
	private String orgcode;

	/** 是否主机构（1主机构2兼机构3借调机构） */
	@Column(name = "status")
	private Integer status;

	/** 排序 */
	@Column(name = "tm_sort")
	private Integer tmSort;

	/** 是否使用 */
	@Column(name = "used")
	private Integer used;

	/** 变动日期 */
	@Column(name = "change_date")
	private Date changeDate;

	/** 截止日期 */
	@Column(name = "end_date")
	private Date endDate;

	/** 变动类型（1新增2修改3机构调出4机构调入5岗位调出6岗位调入7离职8退休9删除10机构变动11岗位变动） */
	@Column(name = "change_type")
	private Integer changeType;
	
	/** 租户ID */
//	@Column(name = "TENANT_ID", columnDefinition = "varchar(50) default '0'", length = 50)
//	private String tenant_id = "0";
}
