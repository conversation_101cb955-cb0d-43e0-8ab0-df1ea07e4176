package com.yunhesoft.system.employee.entity.dto;

import lombok.Data;

@Data
public class EmployeePermDto {
	/**
	 * 变动日期
	 */
	private String changeDt;//无用，不再查询变动，太慢了
	/**
	 * 机构代码(指定后，只返回对应机构的人员)
	 */
	private String orgCode;
	/**
	 * 是否包含指定机构的全部子机构 false为不含子机构，true或null 含全部子机构
	 */
	private Boolean readAllSubOrg;
	/**
	 * 是否取完整信息（0和null 只取人员当前信息（含机构信息） 1取人员变动的机构信息 2取人员变动的机构和岗位信息  1和2需要给changeDt）
	 */
	private Integer queryType;//无用只取人员当前机构
	/**
	 * 权限菜单地址 （例：prize:professionalAward:index?isld=1&iscompany=1）支持传入多个，逗号分隔
	 */
	private String permission;
	/**
	 * 人员姓名
	 */
	private String empname;
	/**
	 * 人员工号
	 */
	private String staffNo;
	/**
	 * 手机号
	 */
	private String mobile;
}
