package com.yunhesoft.system.employee.entity.dto;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ApiModel(description = "人员信息")
public class EmployeeDto {
	/** 用户记录ID */
	private String empTmuid;

	/** 机构记录ID */
	private String orgTmuid;

	/** 岗位记录ID */
	private String postTmuid;

	/** 角色记录ID */
	private String roleTmuid;

	/** 用户姓名 */
	private String empname;

	/** 入职日期 */
	private Date entryDate;

	/** 性别 */
	private String sex;

	/** 工号 */
	private String staffNo;

	/** 手机号 */
	private String mobile;

	/** 证件类型编码（1居民身份证，2军人证，3中国护照，4外国护照，5台湾居民来往大陆通行证，6港澳居民来往内地通行证，7其它） */
	private String cardTypeCode;

	/** 证件类型名称 */
	private String cardTypeName;

	/** 证件号 */
	private String cardno;

	/** 职务ID */
	private String dutyid;

	/** 职级ID */
	private String positionlevelid;

	/** 政治面貌（1党员、2预备党员、3团员、4群众、5其它） */
	private Integer politicsStatus;

	/** 邮箱 */
	private String mail;

	/** QQ */
	private String qq;

	/** 微信 */
	private String wechat;

	/** 民族 */
	private String nationality;

	/** 籍贯 */
	private String nativePlace;

	/** 最高学历（1博士、2硕士、3本科、4大专、5高中、6初中、7小学、8其它） */
	private Integer education;

	/** 婚姻状况（1是0否） */
	private Integer marital;

	/** 生日类型（1公立、2农历） */
	private Integer birthdayType;

	/** 出生日期 */
	private Date birthday;

	/** 历史工龄 */
	private Double oldWorkNum;

	/** 员工类型（1全职、2兼职、3实习、4外派、5其它） */
	private Integer staffType;

	/** 工作地点 */
	private String workplace;

	/** 户口类型(1城镇、2非城镇) */
	private Integer accountType;

	/** 居住地址 */
	private String liveAddress;

	/** 描述 */
	private String memo;

	/** 人员排序 */
	private Integer tmSort;

	/** 离退日期 */
	private Date dimissionDate;

	/** 人员状态 - 在职(1)、离职(0)、退休(-1) */
	private Integer status;

	/** 机构编码 */
	private String orgcode;

	/** 是否主机构，1主机构 2兼机构 */
	private Integer orgStatus;

	/** 机构排序 */
	private Integer orgSort;

	/** 岗位ID */
	private String postid;

	/** 岗位名称 */
	private String postname;
	
	/** 是否主岗位，1主岗位2兼岗位3借调岗位 */
	private Integer postStatus;

	/** 岗位排序 */
	private Integer postSort;

	/** 兼岗位ID */
	private String partTimePostId;

	/** 角色ID */
	private String roleid;

	/** 是否使用 */
	private Integer used;

	/** 登录名称 */
	private String loginUserName;

	/** 登录账号状态 */
	private Integer loginStatus;

	/** 是否为系统内置人员（1：是，0：否） */
	private Integer sys;

	/** 政治面貌 */
	private String political;
	/** 租户id */
	private String tenantId;

	/** 专业设置 */
	private String professionalInfoId;

	/** 变动日期 */
	private Date changeDt;
	
	/** 同步删除标识 */
	private String synDelFlag;
	
	/** 工位ID */
	private String stationCode;
	
	/** 工位名称 */
	private String stationName;
}
