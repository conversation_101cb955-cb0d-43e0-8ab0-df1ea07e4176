package com.yunhesoft.system.employee.entity.vo;

import java.util.Date;

import javax.validation.constraints.NotNull;

import com.yunhesoft.system.kernel.utils.excel.ExcelExt;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ApiModel(description = "人员信息")
public class EmployeeVo {
	private Date updateTime;
	/** 记录总数，用于分页计算 */
	private Long recordCount;

	/** 用户记录ID */

	@Excel(name = "ID", width = 22, orderNum = "1")
	@ExcelExt(align = "left")
	private String empTmuid;

	/** 用户姓名 */
	@Excel(name = "*姓名", width = 10, orderNum = "2")
	@ExcelExt(align = "left")
	@NotNull
	private String empname;

	/** 性别 */
	@Excel(name = "性别", width = 10, orderNum = "3", replace = { "男_1", "女_0" })
	private String sex;

	/** 工号 */
	@Excel(name = "*工号", width = 10, orderNum = "4")
	@ExcelExt(align = "left")
	@NotNull
	private String staffNo;

	/** 岗位记录ID */
	@Excel(name = "岗位", width = 25, orderNum = "5")
	@ExcelExt(align = "left")
	private String postid;

	/** 角色记录ID */
	private String roleTmuid;
	/** 角色ID */
	private String roleid;
	/** 角色名称 */
	@Excel(name = "角色", width = 30, orderNum = "6")
	@ExcelExt(align = "left")
	private String rolename;

	/** 手机号 */
	@Excel(name = "手机", width = 15, orderNum = "7")
	private String mobile;

	/** 邮箱 */
	@Excel(name = "邮箱", width = 20, orderNum = "8")
	private String mail;

	/** 入职日期 */
	@Excel(name = "入职日期", width = 10, orderNum = "9", format = "yyyy-MM-dd")
	private Date entryDate;

	/** 员工类型（1全职、2兼职、3实习、4外派、5其它） */
	@Excel(name = "员工类型", width = 10, orderNum = "10", replace = { "_null", "全职_1", "兼职_2", "实习_3", "外派_4", "其它_5" })
	private Integer staffType;
	
	/**所属专业 */
	@Excel(name = "所属专业", width = 10, orderNum = "10", replace = { "_null","[]_null", "全职_1", "兼职_2", "实习_3", "外派_4", "其它_5" })
	private String professionalInfoId;

	/** 证件类型编码（1居民身份证，2军人证，3中国护照，4外国护照，5台湾居民来往大陆通行证，6港澳居民来往内地通行证，7其它） */
	private String cardTypeCode;

	/** 证件类型名称 */
	private String cardTypeName;

	/** 证件号 */
	/** 手机号 */
	// @Excel(name = "身份证号", width = 20, orderNum = "6")
	private String cardno;

	/** 职务ID */
	private String dutyid;

	/** 职级ID */
	private String positionlevelid;

	/** 政治面貌（1党员、2预备党员、3团员、4群众、5其它） */
	private Integer politicsStatus;

	/** QQ */
	private String qq;

	/** 微信 */
	private String wechat;

	/** 民族 */
	private String nationality;

	/** 籍贯 */
	private String nativePlace;

	/** 最高学历（1博士、2硕士、3本科、4大专、5高中、6初中、7小学、8其它） */
	private Integer education;

	/** 婚姻状况（1是0否） */
	private Integer marital;

	/** 生日类型（1公立、2农历） */
	private Integer birthdayType;

	/** 出生日期 */
	private Date birthday;

	/** 历史工龄 */
	private Double oldWorkNum;

	/** 工作地点 */
	private String workplace;

	/** 户口类型(1城镇、2非城镇) */
	private Integer accountType;

	/** 居住地址 */
	private String liveAddress;

	/** 描述 */
	private String memo;

	/** 人员排序 */
	private Integer tmSort;

	/** 离退日期 */
	private Date dimissionDate;

	/** 人员状态 - 在职(1)、离职(0)、退休(-1) */
	private Integer status;

	/** 机构记录ID */
	private String orgTmuid;

	/** 机构编码 */
	private String orgcode;

	/** 机构名称 */
	private String orgname;
	
	/** 单位名称 */
	private String unitName;
	
	/** 单位编码 */
	private String unitCode;

	private String orgNumber;

	/** 是否主机构，1主机构 2兼机构 */
	private Integer orgStatus;

	/** 机构排序 */
	private Integer orgSort;

	private String postTmuid;

	/** 岗位名称 */
	private String postname;

	/** 是否主岗位，1主岗位2兼岗位3借调岗位 */
	private Integer postStatus;

	/** 岗位排序 */
	private Integer postSort;
	
	/** 岗位专业ID */
	private String postProfessionalInfoId;

	/** 兼岗位名称 */
	private String partTimePostName;

	/** 兼岗位ID */
	private String partTimePostId;

	/** 是否使用 */
	private Integer used;

	/** 登录名称 */
	private String loginUserName;

	/** 登录账号状态 */
	private Integer loginStatus;

	/** 是否系统内置人员 */
	private Integer sys;

	/** 政治面貌 */
	private String political;
	
	/** 变动日期 */
	@Excel(name = "变动日期",format="yyyy-MM-dd")
	private Date changeDt;
	

	/**
	 * 是否岗位负责人
	 */
	private String isHead;
	
	/**
	 * 是否从责任矩阵中直接取出
	 * 				0或者null是推到
	 * 				1直接取出
	 */
	private int isMtmMatrix;
	
	/** 工位ID */
	private String stationCode;
	
	/** 工位名称 */
	private String stationName;
	
	/** 人员大排序用字符串 */
	private String sortStr;
}
