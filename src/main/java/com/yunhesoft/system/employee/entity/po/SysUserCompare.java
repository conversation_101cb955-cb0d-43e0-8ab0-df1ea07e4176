package com.yunhesoft.system.employee.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 人员对照表
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value = "系统人员对照表")
@Entity
@Setter
@Getter
@Table(name = "SYS_USER_COMPARE")
public class SysUserCompare extends BaseEntity {

	/** */
	private static final long serialVersionUID = 1L;

	/**
	 * TM4组员ID 一个TM4人员对应多个TM3人员
	 */
	@ApiModelProperty(value = "TM4组员ID 一个TM4人员对应多个TM3人员")
	@Column(name = "targetOrgUserId", length = 200)
	private String targetOrgUserId;

	/**
	 * TM3组员ID
	 */
	@ApiModelProperty(value = "TM3组员ID")
	@Column(name = "sourceOrgUserId", length = 200)
	private String sourceOrgUserId;

	/**
	 * TM3组员名称
	 */
	@ApiModelProperty(value = "TM3组员名称")
	@Column(name = "sourceOrgUserName", length = 200)
	private String sourceOrgUserName;

	
	@ApiModelProperty(value = "TM3组员工号")
	@Column(name = "sourceOrgUserJobNumber", length = 200)
	private String sourceOrgUserJobNumber;
	
	/**
	 * 连带系数
	 * 
	 * TM3人员连带系数
	 */
	@ApiModelProperty(value = "TM3人员连带系数")
	@Column(name = "associatedCoefficient")
	private Double associatedCoefficient;
	
	
	/**
	 * 添加方式
	 * 添加0  修改1
	 */
	@Transient
	private Integer rowflag;

}
