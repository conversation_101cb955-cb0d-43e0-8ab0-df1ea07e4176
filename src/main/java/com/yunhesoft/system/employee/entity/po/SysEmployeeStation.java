package com.yunhesoft.system.employee.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 员工工位信息表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_EMPLOYEE_STATION")
public class SysEmployeeStation extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 人员编码 */
    @Column(name="EMPID", length=50)
    private String empid;
    
    /** 工位代码 */
    @Column(name="STATION_CODE", length=50)
    private String stationCode;
    
    /** 工位名称 */
    @Column(name="STATION_NAME", length=50)
    private String stationName;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}

