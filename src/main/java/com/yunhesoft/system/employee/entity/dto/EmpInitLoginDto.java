package com.yunhesoft.system.employee.entity.dto;

import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 账号批量初始化
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ApiModel(description = "人员调动信息")
public class EmpInitLoginDto {
	/** 人员id */
	private List<String> empidList;
	
	private String roleId;

	/** 初始化登陆账号方式 */
	private String loginType; // 1:工号；2：姓名全拼；3：手机号

}
