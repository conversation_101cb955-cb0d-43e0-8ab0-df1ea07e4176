package com.yunhesoft.system.employee.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 人员岗位变动信息表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_EMPLOYEE_CHANGE_POST", indexes = {
	    @Index(name = "sys_change_post_idxo", columnList = "employeeid,startdt,enddt"),
	    @Index(name = "sys_change_post_idxp", columnList = "orgcode,startdt,enddt")
	})
public class SysEmployeeChangePost extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 人员ID */
    @Column(name="EMPLOYEEID", length=50)
    private String employeeId;
    
    /** 机构代码 */
    @Column(name="ORGCODE", length=50)
    private String orgCode;
    
    /** 岗位代码 */
    @Column(name="POSTID", length=50)
    private String postId;
    
    /** 开始日期 */
    @Column(name="STARTDT")
    private Date startDt;
    
    /** 截止日期 */
    @Column(name="ENDDT")
    private Date endDt;
    
    /** 离职标识 0在职 1离职*/ 
    @Column(name="DEPARTSTATUS")
    private Integer departStatus;

    /** 人员当前状态 1启用 0删除-1离职-2退休*/
    @Column(name="USERSTATUS")
    private Integer userStatus;
}
