package com.yunhesoft.system.employee.entity.dto;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 人员机构岗位信息（用于员工调动、兼岗、调岗）
 * @category 人员机构岗位信息
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "人员机构岗位信息")
public class EmployeeOrgPostDto {
	/** 人员机构记录id */
	private String empOrgTmuid;

	/** 人员岗位记录id */
	private String empPostTmuid;

	/** 人员机构记录id（预变动） */
	private String empOrgTmuidTodo;

	/** 人员岗位记录id（预变动） */
	private String empPostTmuidTodo;

	/** 人员机构记录id（历史变动） */
	private String empOrgTmuidHis;

	/** 人员岗位记录id（历史变动） */
	private String empPostTmuidHis;

	/** 人员id */
	private String empid;

	/** 人员姓名 */
	private String empname;

	/** 机构代码（原） */
	private String oldOrgcode;

	/** 机构名称（原） */
	private String oldOrgname;

	/** 岗位ID（原） */
	private String oldPostid;

	/** 岗位名称（原） */
	private String oldPostname;

	/** 机构代码（新） */
	private String newOrgcode;

	/** 机构名称（新） */
	private String newOrgname;

	/** 岗位ID（新） */
	private String newPostid;

	/** 岗位名称（新） */
	private String newPostname;

	/** 机构岗位状态（1主机构岗位2兼机构岗位3借调机构岗位） */
	private Integer status;

	/** 变动日期 */
	private Date changeDate;

	/** 截止日期 */
	private Date endDate;

	/** 变动类型（1新增2修改3机构调出4机构调入5岗位调出6岗位调入7离职8退休9删除10机构变动11岗位变动） */
	private Integer changeType;

	/** 清除数据标识（1：清除数据，0：不清除）：调动到历史日期，系统将从调动日期开始到当天的调动信息全部清除 */
	private Integer doCover;

	/** 时间状态（-1 历史，0 当前，1 未来）*/
	private Integer timeStatus;

}
