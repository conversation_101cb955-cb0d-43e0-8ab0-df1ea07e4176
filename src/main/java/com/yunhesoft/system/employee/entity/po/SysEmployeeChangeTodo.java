package com.yunhesoft.system.employee.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 人员变动待办表（预变动-调度）
 * 
 * @category 人员变动待办表（预变动-调度）
 * <AUTHOR>
 * @date 2020/03/30
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_EMPLOYEE_CHANGE_TODO")
public class SysEmployeeChangeTodo extends BaseEntity {
	/** */
	private static final long serialVersionUID = 1L;

	/** 用户ID（外键,关联:sys_employee_info.id） */
	@Column(name = "empid", length = 50)
	private String empid;

	/** 用户 */
	@Column(name = "empname", length = 100)
	private String empname;

	/** 原code */
	@Column(name = "old_code", length = 50)
	private String oldCode;

	/** 原名称 */
	@Column(name = "old_name", length = 100)
	private String oldName;

	/** 新code */
	@Column(name = "new_code", length = 50)
	private String newCode;

	/** 新名称 */
	@Column(name = "new_name", length = 100)
	private String newName;

	/** 数据类型（1机构2岗位） */
	@Column(name = "data_type", columnDefinition = "int default 0 ")
	private Integer dataType;

	/** 是否主机构主岗位（1主机构岗位2兼机构岗位3借调机构岗位） */
	@Column(name = "status", columnDefinition = "int default 0 ")
	private Integer status;

	/** 变动日期 */
	@Column(name = "change_date")
	private Date changeDate;

	/** 截止日期 */
	@Column(name = "end_date")
	private Date endDate;

	/** 变动类型（1新增2修改3机构调出4机构调入5岗位调出6岗位调入7离职8退休9删除10机构变动11岗位变动） */
	@Column(name = "change_type", columnDefinition = "int default 0 ")
	private Integer changeType;

	/** 运行状态（1已运行0未运行） */
	@Column(name = "run_status", columnDefinition = "int default 0 ")
	private Integer runStatus;

	/** 描述 */
	@Column(name = "memo", length = 1000)
	private String memo;

	/** 是否使用 */
	@Column(name = "used", columnDefinition = "int default 0 ")
	private Integer used;

	/** 分组id */
	@Column(name = "groupid", length = 50)
	private String groupid;
}
