package com.yunhesoft.system.employee.entity.dto;

import java.util.List;

import com.yunhesoft.system.employee.entity.po.SysEmployeeGldy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "员绑定管理党员", description = "员绑定管理党员")
public class SysEmployeeGldyDto {

	/** 人员 编码 */
	@ApiModelProperty(value = "人员编码")
	private String empId;
	
	// 需要保存的数据
	@ApiModelProperty(value = "需要保存的数据(json数组)")
	private List<SysEmployeeGldy> data;
}
