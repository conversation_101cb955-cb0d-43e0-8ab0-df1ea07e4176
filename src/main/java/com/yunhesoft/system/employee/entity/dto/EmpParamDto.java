package com.yunhesoft.system.employee.entity.dto;

import com.yunhesoft.system.kernel.service.model.Order;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 人员信息查询参数
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "人员信息")
public class EmpParamDto {
	/** 机构代码，支持多个，逗号分隔 */
	private String orgcode;

	/** 只显示失效记录标识（1：失效，0：有效） */
	private Integer invalid;

	/** 数据有效标识（1：有效，0：无效） */
	private Integer used;

	/** 人员信息id，支持多个，逗号分隔 */
	private String empid;

	/** 是否主机构，1主机构 2兼机构 */
	private Integer orgStatus;

	/** 分页当前页数 */
	private Integer current;

	/** 分页每页记录数 */
	private Integer size;

	/** 姓名 */
	private String empname;

	/** 工号 */
	private String staffNo;

	/** 手机号 */
	private String mobile;

	/** 登录名称 */
	private String loginUserName;
	// 是否进行管辖查询
	private Boolean isJurisdiction;
	// 是否查询单位
	private Boolean showUnit;
	/**
	 * Excel导入类型 1：模板（用于添加数据） 2：数据（用于修改数据）
	 */
	private String exportType;

	/**
	 * 排序字段（empname）
	 */
	private Order order;

	/**
	 * 说明信息
	 */
	private String memo;

	private String premissions;

	// 租户id
	private String tenantId;

	// 岗位id
	private String postIds;

}
