package com.yunhesoft.system.employee.entity.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.yunhesoft.system.kernel.utils.excel.ExcelExt;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class EmployeeExcelVo {

    @Excel(name = "*姓名", width = 10, orderNum = "2")
    @ExcelExt(align = "left")
    private String empname;

    @Excel(name = "性别", width = 10, orderNum = "3", replace = { "男_1", "女_0" })
    private String sex;

    @Excel(name = "工号", width = 10, orderNum = "4")
    @ExcelExt(align = "left")
    private String staffNo;

    @Excel(name = "单位名称", width = 10, orderNum = "5")
    @ExcelExt(align = "left")
    private String unitName;
    @Excel(name = "部门", width = 10, orderNum = "6")
    @ExcelExt(align = "left")
    private String orgname;

    @Excel(name = "岗位", width = 25, orderNum = "5")
    @ExcelExt(align = "left")
    private String postname;

    @Excel(name = "角色", width = 25, orderNum = "5")
    @ExcelExt(align = "left")
    private String rolename;

    @Excel(name = "登录名称", width = 25, orderNum = "5")
    @ExcelExt(align = "left")
    private String loginUserName;
}
