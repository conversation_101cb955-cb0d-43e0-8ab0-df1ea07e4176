package com.yunhesoft.system.employee.entity.dto;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "获取人员变动信息")
public class EmpChangeDto {
	
	@ApiModelProperty(value = "人员id列表")
	private List<String> empIds = new ArrayList<String>();

	@ApiModelProperty(value = "机构代码")
	private String orgCode;

	@ApiModelProperty(value = "变动日期")
	private String changeDt;
	
	@ApiModelProperty(value = "是否读取orgCode的全部子机构的变动信息（包含orgCode） true读取子机构，false不读取子机构，只读取给定的机构")
	private Boolean readAllSubOrg;
	
	@ApiModelProperty(value = "是否同时读取岗位变动信息 true:是")
	private Boolean readPostChange;
	
	
}
