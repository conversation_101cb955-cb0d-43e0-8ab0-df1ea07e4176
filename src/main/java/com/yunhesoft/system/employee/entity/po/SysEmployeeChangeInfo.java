package com.yunhesoft.system.employee.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 人员变动流水表
 * 
 * @category 人员变动流水表
 * <AUTHOR>
 * @date 2020/03/30
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_EMPLOYEE_CHANGE_INFO")
public class SysEmployeeChangeInfo extends BaseEntity {
	/** */
	private static final long serialVersionUID = 1L;

	/** 用户ID */
	@Column(name = "empid", length = 50)
	private String empid;

	/** 用户 */
	@Column(name = "empname", length = 100)
	private String empname;

	/** 原编码 */
	@Column(name = "old_code", length = 50)
	private String oldCode;

	/** 原名称 */
	@Column(name = "old_name", length = 100)
	private String oldName;

	/** 新编码 */
	@Column(name = "new_code", length = 50)
	private String newCode;

	/** 新名称 */
	@Column(name = "new_name", length = 100)
	private String newName;

	/** 数据类型 */
	@Column(name = "data_type", columnDefinition = "int default 0 ")
	private Integer dataType;

	/** 是否主机构主岗位 */
	@Column(name = "status", columnDefinition = "int default 0 ")
	private Integer status;

	/** 变动日期 */
	@Column(name = "change_date")
	private Date changeDate;

	/** 变动类型 */
	@Column(name = "change_type", columnDefinition = "int default 0 ")
	private Integer changeType;

	/** 描述 */
	@Column(name = "memo", length = 1500)
	private String memo;

	/** 是否使用 */
	@Column(name = "used", columnDefinition = "int default 0 ")
	private Integer used;

	/** 分组id */
	@Column(name = "groupid", length = 50)
	private String groupid;
}
