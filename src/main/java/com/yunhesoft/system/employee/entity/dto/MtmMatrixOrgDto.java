package com.yunhesoft.system.employee.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "主题责任矩阵数据录入对象", description = "主题责任矩阵数据录入对象")
public class MtmMatrixOrgDto {
	
	
	@ApiModelProperty(value = "人员Id")
	private String userId;
	
	@ApiModelProperty(value = "旧机构代码Id")
	private String oldOrgId;
	
	@ApiModelProperty(value = "新机构代码Id")
	private String newOrgId;

}
