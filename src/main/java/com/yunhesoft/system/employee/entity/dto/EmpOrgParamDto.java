package com.yunhesoft.system.employee.entity.dto;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 人员机构信息查询参数
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "人员机构信息查询参数")
public class EmpOrgParamDto {
	/** 人员信息id，支持多个，逗号分隔*/
	String empid;
	
	/** 机构代码，支持多个，逗号分隔 */
	private String orgcode;
	
	/** 数据有效标识（1：有效，0：无效）*/
	private Integer used;
	
	/** 分页当前页数*/
	private Integer current;
	
	/** 分页每页记录数*/
	private Integer size;
}
