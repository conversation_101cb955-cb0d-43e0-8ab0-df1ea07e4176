package com.yunhesoft.system.employee.entity.vo;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 人员机构岗位信息（用于员工调动、兼岗、调岗）
 * @category 人员机构岗位信息
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "人员机构岗位信息")
public class EmployeeOrgPostVo {
	/** 记录总数，用于分页计算 */
	private Long recordCount;

	/** 人员机构记录id */
	private String empOrgTmuid;

	/** 人员岗位记录id */
	private String empPostTmuid;

	/** 人员机构记录id（预变动） */
	private String empOrgTmuidTodo;

	/** 人员岗位记录id（预变动） */
	private String empPostTmuidTodo;

	/** 人员信息id */
	String empid;

	/** 机构代码（原） */
	private String oldOrgcode;

	/** 机构名称（原） */
	private String oldOrgname;

	/** 岗位ID（原） */
	private String oldPostid;

	/** 岗位名称（原） */
	private String oldPostname;

	/** 机构代码（新） */
	private String newOrgcode;

	/** 机构名称（新） */
	private String newOrgname;

	/** 岗位ID（新） */
	private String newPostid;

	/** 岗位名称（新） */
	private String newPostname;

	/** 机构岗位状态（1主机构岗位2兼机构岗位3借调机构岗位） */
	private Integer status;

	/** 变动日期 */
	private Date changeDate;

	/** 截止日期 */
	private Date endDate;

	/** 时间状态（-1 历史，0 当前，1 未来）*/
	private Integer timeStatus;
	
	/**
	 * 是否岗位负责人
	 */
	private String isHead;
	
	/**
	 * 是否从责任矩阵中直接取出
	 * 				0或者null是推到
	 * 				1直接取出
	 */
	private int isMtmMatrix;
}
