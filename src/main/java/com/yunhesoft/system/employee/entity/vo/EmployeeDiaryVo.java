package com.yunhesoft.system.employee.entity.vo;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 人员机构岗位流水信息（用于员工调动、兼岗、借调）
 * @category 人员机构岗位流水信息
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "人员机构岗位流水信息")
public class EmployeeDiaryVo {
	/** 记录总数，用于分页计算 */
	private Long recordCount;

	/** 用户ID */
	private String empid;

	/** 用户 */
	private String empname;

	/** 原编码 */
	private String oldCode;

	/** 原名称 */
	private String oldName;

	/** 新编码 */
	private String newCode;

	/** 新名称 */
	private String newName;

	/** 数据类型 */
	private Integer dataType;

	/** 是否主机构主岗位 */
	private Integer status;

	/** 变动日期 */
	private Date changeDate;

	/** 变动类型 */
	private Integer changeType;

	/** 描述 */
	private String memo;

	/** 是否使用 */
	private Integer used;
}
