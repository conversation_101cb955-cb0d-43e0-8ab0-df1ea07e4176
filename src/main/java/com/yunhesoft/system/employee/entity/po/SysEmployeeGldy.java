package com.yunhesoft.system.employee.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 人员绑定管理党员
 * 
 * @category 人员绑定管理党员
 * <AUTHOR>
 * @date 2022-09-20
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_EMPLOYEE_GLDY")
public class SysEmployeeGldy extends BaseEntity {
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "人员ID")
	@Column(name = "TARGETORGUSERID", length = 200)
	private String targetOrgUserId;

	/**
	 * TM3组员ID
	 */
	@ApiModelProperty(value = "绑定组员ID")
	@Column(name = "BINDUSERID", length = 200)
	private String bindUserId;

	/**
	 * TM3组员名称
	 */
	@ApiModelProperty(value = "绑定组员名称")
	@Column(name = "BINDUSERNAME", length = 200)
	private String bindUserName;

	
	@ApiModelProperty(value = "工号")
	@Column(name = "JOBNUMBER", length = 200)
	private String jobNumber;
	
	/**
	 * 添加方式
	 * 添加0  修改1,-1删除
	 */
	@Transient
	private Integer rowflag;

}
