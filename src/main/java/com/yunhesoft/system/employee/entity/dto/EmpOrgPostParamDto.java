package com.yunhesoft.system.employee.entity.dto;

import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 人员机构岗位信息查询参数
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "人员机构岗位信息查询参数")
public class EmpOrgPostParamDto {
	/** 人员信息id */
	String empid;

	/** 机构代码 */
	String orgcode;

	/** 岗位id */
	String postid;

	/** 是否主岗位，1主岗位2兼岗位3借调岗位 */
	private Integer postStatus;

	/** 数据有效标识（1：有效，0：无效） */
	private Integer used;

	/** 分页当前页数 */
	private Integer current;

	/** 分页每页记录数 */
	private Integer size;

	/** 人员信息id List */
	private List<String> empidList;
}
