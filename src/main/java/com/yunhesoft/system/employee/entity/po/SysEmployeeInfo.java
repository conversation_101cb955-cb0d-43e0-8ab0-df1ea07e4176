package com.yunhesoft.system.employee.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 人员信息表
 * 
 * @category 人员信息表
 * <AUTHOR>
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_EMPLOYEE_INFO")
public class SysEmployeeInfo extends BaseEntity {

	private static final long serialVersionUID = 1L;

//	@TableField(exist = false)
	@ApiModelProperty(value = "记录总数，用于分页计算", hidden = true)
	@Transient
	private Long recordCount;

	@ApiModelProperty(value = "用户姓名")
	@Column(name = "EMPNAME", length = 100)
	private String empname;

	@ApiModelProperty(value = "入职日期")
	@Column(name = "ENTRY_DATE")
	private Date entryDate;

	@ApiModelProperty(value = "性别")
	@Column(name = "SEX", length = 50)
	private String sex;

	@ApiModelProperty(value = "工号")
	@Column(name = "STAFF_NO", length = 100)
	private String staffNo;

	@ApiModelProperty(value = "手机号")
	@Column(name = "MOBILE", length = 50)
	private String mobile;

	@ApiModelProperty(value = "证件类型编码（1居民身份证，2军人证，3中国护照，4外国护照，5台湾居民来往大陆通行证，6港澳居民来往内地通行证，7其它）")
	@Column(name = "CARD_TYPE_CODE", length = 50)
	private String cardTypeCode;

	@ApiModelProperty(value = "证件类型名称")
	@Column(name = "CARD_TYPE_NAME", length = 100)
	private String cardTypeName;

	@ApiModelProperty(value = "证件号")
	@Column(name = "CARDNO", length = 100)
	private String cardno;

	@ApiModelProperty(value = "职务ID")
	@Column(name = "DUTYID", length = 50)
	private String dutyid;

	@ApiModelProperty(value = "职级ID")
	@Column(name = "POSITIONLEVELID", length = 50)
	private String positionlevelid;

	 @ApiModelProperty(value = "政治面貌（1党员、2预备党员、3团员、4群众、5其它）")
	 @Column(name = "POLITICS_STATUS")
	 private Integer politicsStatus;

	@ApiModelProperty(value = "邮箱")
	@Column(name = "MAIL", length = 100)
	private String mail;

	@ApiModelProperty(value = "QQ")
	@Column(name = "QQ", length = 50)
	private String qq;

	@ApiModelProperty(value = "微信")
	@Column(name = "WECHAT", length = 50)
	private String wechat;

	@ApiModelProperty(value = "民族")
	@Column(name = "NATIONALITY", length = 50)
	private String nationality;

	@ApiModelProperty(value = "籍贯")
	@Column(name = "NATIVE_PLACE", length = 50)
	private String nativePlace;

	@ApiModelProperty(value = "最高学历（1博士、2硕士、3本科、4大专、5高中、6初中、7小学、8其它）")
	@Column(name = "EDUCATION")
	private Integer education;

	@ApiModelProperty(value = "婚姻状况（1是0否）")
	@Column(name = "MARITAL")
	private Integer marital;

	@ApiModelProperty(value = "生日类型（1公立、2农历）")
	@Column(name = "BIRTHDAY_TYPE")
	private Integer birthdayType;

	@ApiModelProperty(value = "出生日期")
	@Column(name = "BIRTHDAY")
	private Date birthday;

	@ApiModelProperty(value = "历史工龄")
	@Column(name = "OLD_WORK_NUM")
	private Double oldWorkNum;

	@ApiModelProperty(value = "员工类型（1全职、2兼职、3实习、4外派、5其它）")
	@Column(name = "STAFF_TYPE")
	private Integer staffType;

	@ApiModelProperty(value = "工作地点")
	@Column(name = "WORKPLACE", length = 500)
	private String workplace;

	@ApiModelProperty(value = "户口类型(1城镇、2非城镇)")
	@Column(name = "ACCOUNT_TYPE")
	private Integer accountType;

	@ApiModelProperty(value = "居住地址")
	@Column(name = "LIVE_ADDRESS", length = 500)
	private String liveAddress;

	@ApiModelProperty(value = "描述")
	@Column(name = "MEMO", length = 2000)
	private String memo;

	@ApiModelProperty(value = "排序")
	@Column(name = "TM_SORT")
	private Integer tmSort;

	@ApiModelProperty(value = "是否使用")
	@Column(name = "USED")
	private Integer used;

	@ApiModelProperty(value = "离退日期")
	@Column(name = "DIMISSION_DATE")
	private Date dimissionDate;

	@ApiModelProperty(value = "人员状态 - 在职(1)、离职(0)、退休(-1)")
	@Column(name = "STATUS")
	private Integer status;

	@ApiModelProperty(value = "失效记录标识（1：失效，0：有效）")
	@Column(name = "INVALID")
	private Integer invalid;

	@ApiModelProperty(value = "政治面貌")
	@Column(name = "POLITICAL", length = 5)
	private String political;

	@ApiModelProperty(value = "是否为系统内置人员（1：是，0：否）")
	@Column(name = "SYS")
	private Integer sys;
	
	//SysProfessionalInfo.id
	@ApiModelProperty(value = "专业设置")
	@Column(name = "PROFESSIONALINFOID")
	private String professionalInfoId;
	
	/** 租户ID */
//	@Column(name = "TENANT_ID", columnDefinition = "varchar(50) default '0'", length = 50)
//	private String tenant_id = "0";
	
	@ApiModelProperty(value = "用工类型")
	@Column(name = "EMPLOYMENTTYPE",length = 50)
	private String employmentType;
}
