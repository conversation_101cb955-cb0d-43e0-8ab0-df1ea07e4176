package com.yunhesoft.system.employee.entity.vo;


import java.util.Date;

import lombok.Data;

/**
 *	TM3党员
 * <AUTHOR>
 *
 */
@Data
public class SysTm3PartyMember {
	
	/** 人员id */
	private String empid;
	
	/** 人员姓名 */
	private String empName;
	
	/** 机构代码 */
	private String orgdm;
	
	/** 工号  */
	private String staffNo;
	
	/** 性别  */
	private String sex;
	
	/** 移动电话  */
	private String mobile;
	
	/** 政治面貌（1党员、2预备党员、3团员、4群众、5其它）  */
	private Integer politicsStatus;
	
	/** 民族  */
	private String nationality;
	
	/** 最高学历（1博士、2硕士、3本科、4大专、5高中、6初中、7小学、8其它）  */
	private Integer education;
	
	/** 生日类型（1公立、2农历）  */
	private Integer birthdayType;
	
	/** 出生日期  */
	private Date birthday;
	
	/** 登入名称  */
	private String dlmc;
	
	/** 登入密码  */
	private String yhkl;
	
	
	

}
