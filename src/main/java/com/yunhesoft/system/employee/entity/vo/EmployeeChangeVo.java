package com.yunhesoft.system.employee.entity.vo;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 战略主题表头显示数据
 * 
 * @Description
 * <AUTHOR>
 * @date 2021年11月18日
 */
@Data
@ApiModel(value="人员变动数据VO类",description="人员变动数据VO类")
public class EmployeeChangeVo {

    /** 人员ID */
	@ApiModelProperty(value = "人员ID")
    private String employeeId;
    
    /** 机构代码 */
	@ApiModelProperty(value = "机构代码")
    private String orgCode;
	
	/** 岗位ID */
	@ApiModelProperty(value = "岗位ID")
    private String postId;
    
    /** 开始日期 */
	@ApiModelProperty(value = "开始日期")
    private Date startDt;
    
    /** 截止日期 */
	@ApiModelProperty(value = "截止日期")
    private Date endDt;
    
    /** 离职标识 0在职 1离职*/ 
	@ApiModelProperty(value = "离职标识 0在职 1离职")
    private Integer departStatus;

}