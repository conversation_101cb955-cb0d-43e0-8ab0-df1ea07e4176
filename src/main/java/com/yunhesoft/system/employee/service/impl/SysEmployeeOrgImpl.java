package com.yunhesoft.system.employee.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.employee.entity.dto.EmpOrgParamDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Select;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrgRelation;
import com.yunhesoft.system.org.service.ISysOrgRelationService;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
public class SysEmployeeOrgImpl implements ISysEmployeeOrgService {

	@Autowired
	private EntityService entityService;

	@Autowired
	private ISysOrgRelationService orgRelationServ; // 机构关系

	/**
	 * 批量添加人员机构信息
	 * 
	 * @category 批量添加人员机构信息
	 * @param list
	 * @return
	 */
	@Override
	public boolean addBatch(List<SysEmployeeOrg> listPo) {
		boolean flag = true;
		try {
			if (listPo != null && listPo.size() > 0) {
				int b = entityService.insertBatch(listPo);
				flag = (b > 0 ? true : false);
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return flag;
	}

	/**
	 * 通过记录ID批量删除人员
	 * 
	 * @category 通过记录ID批量删除人员
	 * @param list
	 * @return
	 */
	@Override
	public boolean deleteBatchById(List<SysEmployeeOrg> listPo) {
		boolean flag = true;
		try {
			if (listPo != null && listPo.size() > 0) {
				int b = entityService.deleteByIdBatch(listPo);
				flag = (b > 0 ? true : false);
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return flag;
	}

	/**
	 * 批量更新人员机构信息
	 * 
	 * @category 批量更新人员机构信息
	 * @param list
	 * @return
	 */
	@Override
	public boolean updateBatchByIds(List<SysEmployeeOrg> listPo) {
		boolean flag = true;
		try {
			if (listPo != null && listPo.size() > 0) {
				int b = entityService.updateByIdBatch(listPo);
				flag = (b > 0 ? true : false);
			}
		} catch (Exception e) {
			log.error("", e);
		}
		return flag;
	}

	/**
	 * 获取人员机构信息
	 * 
	 * @category 获取人员机构信息
	 * @return
	 */
	@Override
	public List<SysEmployeeOrg> getEmployeeOrg(EmpOrgParamDto paramDto) {
		List<SysEmployeeOrg> list = new ArrayList<SysEmployeeOrg>();
		try {
			// 参数
			String empid = paramDto.getEmpid();
			String orgcode = paramDto.getOrgcode();
			Integer used = paramDto.getUsed();
			Integer current = paramDto.getCurrent();
			Integer size = paramDto.getSize();
			Where query = Where.create();
			Order order = Order.create();
			// 数据有效标识（1：有效，0：无效）
			if (used != null) { // used : -1 离职；-2 退休
				if(used<=-1){
					// used <= - 1 , 查离职和退休的人员
					query.le(SysEmployeeOrg::getUsed, used.intValue());
				}else{
					query.eq(SysEmployeeOrg::getUsed, used.intValue());
				}
			}
			// 过滤人员信息id，支持多个，逗号分隔
			if (empid != null && !"".equals(empid)) {
				List<String> empidList = Arrays.asList(empid.split(","));
				if (empidList.size() > 1) {
					query.in(SysEmployeeOrg::getEmpid, empidList.toArray());
				} else {
					query.eq(SysEmployeeOrg::getEmpid, empid);
				}
			}
			// 过滤机构代码
			if (orgcode != null && !"".equals(orgcode)) {
				List<String> orgcodeList = Arrays.asList(orgcode.split(","));
				if (orgcodeList.size() > 1) {
					query.in(SysEmployeeOrg::getOrgcode, orgcodeList.toArray());
				} else {
					query.eq(SysEmployeeOrg::getOrgcode, orgcode);
				}
			}
			// 查询主机构
			query.eq(SysEmployeeOrg::getStatus, 1);
			// 排序
			order.orderByAsc(SysEmployeeOrg::getTmSort);
			if (current != null && size != null) { // 分页
				// 创建分页对象
				Pagination<?> page = Pagination.create(current, size);
				// 读取总记录数量
				page.setTotal(entityService.queryCount(SysEmployeeOrg.class, query).intValue());
				// 读取记录结果
				list = entityService.queryList(SysEmployeeOrg.class, query, page, order);
				if (list != null && list.size() > 0) {
					list.get(0).setRecordCount(page.getTotal());
				}
			} else { // 不分页
				list = entityService.queryList(SysEmployeeOrg.class, query, order);
				if (list != null && list.size() > 0) {
					list.get(0).setRecordCount(new Long(list.size()));
				}
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return list;
	}

	/**
	 * 根据人员id获取机构信息
	 * 
	 * @param empId 人员id
	 * @param type  类型 equal:平级；parent:上级
	 * 
	 * @return
	 */
	@Override
	public String getOrgIdByEmpId(String empId, String type) {
		String orgId = null;
		EmpOrgParamDto paramDto = new EmpOrgParamDto();
		paramDto.setEmpid(empId);
		paramDto.setUsed(1);
		List<SysEmployeeOrg> list = getEmployeeOrg(paramDto);
		if (StringUtils.isNotEmpty(list)) {
			orgId = list.get(0).getOrgcode(); // 所属
		}
		if ("equal".equals(type)) {// 平级机构
			return orgId;
		} else if ("super".equals(type)) {// 上级机构
			if (StringUtils.isNotEmpty(orgId)) {
				List<SysOrgRelation> listOrg = orgRelationServ.listData(orgId);
				if (StringUtils.isNotEmpty(listOrg)) {
					return listOrg.get(0).getPorgcode();
				}
			}
		}
		return null;
	}

	/**
	 * @category 根据员工名称模糊查询员工列表
	 * @param name
	 * @param fieldList
	 * @param page
	 * @param pageSize
	 * @return
	 */
	@Override
	public List<SysEmployeeInfo> searchNameLike(String name, List<String> fieldList, int limit) {
		Select select = Select.create();
		if (limit == 0) {
			limit = 50;
		}
		if (fieldList != null && fieldList.size() > 0) {
			for (String field : fieldList) {
				select.select(field);
			}
		} else {
			select.select(SysEmployeeInfo::getId).select(SysEmployeeInfo::getEmpname);
		}
		List<SysEmployeeInfo> emps = entityService.queryList(SysEmployeeInfo.class, select,
				Where.create().like(SysEmployeeInfo::getEmpname, name).eq(SysEmployeeInfo::getUsed, 1));
		return emps;
	}
}