package com.yunhesoft.system.employee.service.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.HttpUtils;
import com.yunhesoft.system.employee.entity.vo.SysTm3UserTree;
import com.yunhesoft.system.employee.entity.vo.Tm3UserBeanVo;
import com.yunhesoft.system.employee.service.ISysTm3UserDetailService;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;

@Service
public class SysTm3UserDetailServiceImpl implements ISysTm3UserDetailService {
	
	@Autowired
	private ISysConfigService sysConfigService;
	
	@Override
	public List<SysTm3UserTree> listTm3UserData(String dwbm) {
		String url = sysConfigService.getSysConfig("tm3_url");
		boolean DEBUG = true;
		url = url+"/getTm3User";
		Map<String, String> header = new LinkedHashMap<String, String>();
		// 如果是根节点带入0
		if ("root".equals(dwbm)) {
			dwbm = "0";
		}
		url = url + "?dwbm=" + dwbm;
		String json = "";
		String str = HttpUtils.doPost(DEBUG, url, header, json);
		return getList(str);
	}

	@Override
	public List<SysTm3UserTree> listTm3LikeUserNameData(String userName) {
		String url = sysConfigService.getSysConfig("tm3_url");
		boolean DEBUG = true;
		url = url+"/getTm3LikeUserName";
		Map<String, String> header = new LinkedHashMap<String, String>();
		// 如果是根节点带入0
		if (userName!=null&&userName.length()>0) {
			url = url + "?userName=" + userName;
		}
		String json = "";
		String str = HttpUtils.doPost(DEBUG, url, header, json);
		return getList(str);
	}
	
	
	@Override
	public List<SysTm3UserTree> listTm3LikeUserNameData(Tm3UserBeanVo userBean) {
		String userName = userBean.getUserName();
		String dwbm = userBean.getUserDwbm();
		String url = sysConfigService.getSysConfig("tm3_url");
		boolean DEBUG = true;
		url = url+"/getTm3LikeUserName";
		Map<String, String> header = new LinkedHashMap<String, String>();
		// 如果是根节点带入0
		if (userName!=null&&userName.length()>0) {
			url = url + "?userName=" + userName + "&dwbm="+dwbm;
		}
		String json = "";
		String str = HttpUtils.doPost(DEBUG, url, header, json);
		return getList(str);
	}
	
	
	/**
	 * 返回人员对照接口同一处理结构
	 * @param jsonData
	 * @return
	 */
	private List<SysTm3UserTree> getList(String jsonData){
		JSONObject jsonObject = JSONObject.parseObject(jsonData);

		List<SysTm3UserTree> list = new ArrayList<SysTm3UserTree>();
		if (jsonObject != null && jsonObject.size() > 0) {
			String result = jsonObject.getString("result");
			JSONArray jsonArray = JSONArray.parseArray(result);
			for (int i = 0; i < jsonArray.size(); i++) {
				JSONObject jsonObject2 = jsonArray.getJSONObject(i);
				String userId = jsonObject2.getString("userId");
				String userName = jsonObject2.getString("userName");
				String userJobNumber = jsonObject2.getString("userJobNumber");
				SysTm3UserTree orgTree = new SysTm3UserTree();
				orgTree.setUserId(userId);
				orgTree.setUserName(userName);
				orgTree.setUserJobNumber(userJobNumber);
				list.add(orgTree);
			}
		}
		return list;
	}

	@Override
	public List<SysTm3UserTree> getUserIds(String ids) {
		String url = sysConfigService.getSysConfig("tm3_url");
		boolean DEBUG = true;
		url = url+"/getTm3LikeUserName";
		Map<String, String> header = new LinkedHashMap<String, String>();
		// 如果是根节点带入0
		if (ids!=null&&ids.length()>0) {
			url = url + "?ids=" + ids;
		}else {
			return null;
		}
		String json = "";
		String str = HttpUtils.doPost(DEBUG, url, header, json);
		return getList(str);
	}

	
	
	
}
