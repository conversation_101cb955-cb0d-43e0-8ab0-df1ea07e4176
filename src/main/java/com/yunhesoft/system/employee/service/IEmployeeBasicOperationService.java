package com.yunhesoft.system.employee.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.yunhesoft.system.auth.entity.dto.LoginUserDto;
import com.yunhesoft.system.auth.entity.po.SysLoginUser;
import com.yunhesoft.system.employee.entity.dto.EmpInitLoginDto;
import com.yunhesoft.system.employee.entity.dto.EmpParamDto;
import com.yunhesoft.system.employee.entity.dto.EmpPartimePostParamDto;
import com.yunhesoft.system.employee.entity.dto.EmpTransferParamDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeOrgPostDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.employee.entity.vo.EmployeeDiaryVo;
import com.yunhesoft.system.employee.entity.vo.EmployeeOrgPostVo;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.kernel.service.model.Pagination;

/**
 * <AUTHOR>
 */
public interface IEmployeeBasicOperationService {
    /**
     * 批量添加人员信息
     *
     * @param listDto
     * @return
     * @category 批量添加人员信息
     */
    String addEmployee(List<EmployeeDto> listDto);

    /**
     * 批量添加人员信息
     *
     * @param listDto
     * @param sync    true:同步时调用，false：人员页面调用
     * @return
     * @category 批量添加人员信息
     */
    String addEmployee(List<EmployeeDto> listDto, boolean sync);

    /**
     * 批量删除人员信息
     *
     * @param listDto
     * @return
     * @category 批量删除人员信息
     */
    String delEmployee(List<EmployeeDto> listDto);

    /**
     * 批量修改人员信息
     *
     * @param listDto
     * @return
     * @category 批量修改人员信息
     */
    String updEmployee(List<EmployeeDto> listDto);

    /**
     * 批量增删改人员信息
     *
     * @param addList
     * @param delList
     * @param updList
     * @return
     * @category 批量增删改人员信息
     */
    String saveEmployee(List<EmployeeDto> addList, List<EmployeeDto> delList, List<EmployeeDto> updList);

    /**
     * 获取人员、人员机构、人员岗位信息
     *
     * @param param
     * @return
     */
    List<EmployeeVo> getEmployee(EmpParamDto param);

    /**
     * 获取人员、人员机构、人员岗位信息
     *
     * @param empId 人员id
     * @return
     */
    List<EmployeeVo> getEmployee(String empId);

    /**
     * 获取人员、人员机构、人员岗位信息
     *
     * @param empIds 人员id列表
     * @return
     */
    List<EmployeeVo> getEmployee(List<String> empIds);

    /**
     * 批量添加人员调动信息
     *
     * @param listDto
     * @return
     * @category 批量添加人员调动信息
     */
    String addTransfer(List<EmployeeOrgPostDto> listDto);

    /**
     * 批量删除人员调动信息
     *
     * @param listDto
     * @return
     * @category 批量删除人员调动信息
     */
    String deleteTransfer(List<EmployeeOrgPostDto> listDto);

    /**
     * 批量修改人员调动信息
     *
     * @param listDto
     * @return
     * @category 批量修改人员调动信息
     */
    String updateTransfer(List<EmployeeOrgPostDto> listDto);

    /**
     * 获取人员调动实时信息（历史+当前+未来）
     *
     * @param paramDto
     * @return
     * @throws Exception
     * @category 获取人员调动实时信息
     */
    List<EmployeeOrgPostVo> getTransfer(EmpTransferParamDto paramDto) throws Exception;

    /**
     * 获取人员调动流水信息
     *
     * @param paramDto
     * @return
     * @category 获取人员调动流水信息
     */
    List<EmployeeDiaryVo> getTransferDiary(EmpTransferParamDto paramDto) throws Exception;

    /**
     * 批量添加人员兼岗信息
     *
     * @param listDto
     * @return
     * @category 批量添加人员兼岗信息
     */
    String addPartimePost(List<EmployeeOrgPostDto> listDto);

    /**
     * 批量删除人员兼岗信息
     *
     * @param listDto
     * @return
     * @category 批量删除人员兼岗信息
     */
    String deletePartimePost(List<EmployeeOrgPostDto> listDto);

    /**
     * 批量修改人员兼岗信息
     *
     * @param listDto
     * @return
     * @category 批量修改人员兼岗信息
     */
    String updatePartimePost(List<EmployeeOrgPostDto> listDto);

    /**
     * 获取人员兼岗信息（历史+当前+未来）
     *
     * @param paramDto
     * @return
     * @category 获取人员兼岗信息
     */
    List<EmployeeOrgPostVo> getPartimePost(EmpPartimePostParamDto paramDto) throws Exception;

    /**
     * 获取人员兼岗流水信息
     *
     * @param paramDto
     * @return
     * @category 获取人员兼岗流水信息
     */
    List<EmployeeDiaryVo> getPartimePostDiary(EmpPartimePostParamDto paramDto) throws Exception;

    /**
     * 批量添加人员借调信息
     *
     * @param listDto
     * @return
     * @category 批量添加人员借调信息
     */
    String addOnloan(List<EmployeeOrgPostDto> listDto);

    /**
     * 批量删除人员借调信息
     *
     * @param listDto
     * @return
     * @category 批量删除人员借调信息
     */
    String deleteOnloan(List<EmployeeOrgPostDto> listDto);

    /**
     * 批量修改人员借调信息
     *
     * @param listDto
     * @return
     * @category 批量修改人员借调信息
     */
    String updateOnloan(List<EmployeeOrgPostDto> listDto);

    /**
     * 获取登录信息
     *
     * @return
     */
    LoginUserDto getLoginInfo(String id);

    /**
     * 保存登录信息
     *
     * @param dto
     * @return
     */
    String saveLoginInfo(EmployeeDto dto);

    /**
     * 初始化登录密码
     *
     * @param id
     * @return
     */
    String initLoginPassword(List<String> idList);

    /**
     * 数据导入
     *
     * @param dataList 导入的数据
     * @param params   附加参数
     * @return
     */
    String importData(List<EmployeeVo> dataList, String params);

    /**
     * 获得人员信息
     *
     * @param paramDto
     * @return
     */
    List<EmployeeVo> getAllEmployee(EmpParamDto paramDto);

    /**
     * 获得岗位map
     */
    void getPostMap(Map<String, String> mapPost, List<String> listPost, EmpParamDto paramDto);

    /**
     * 获得角色map
     */
    void getRoleMap(Map<String, String> mapRole, List<String> listRole);

    /**
     * 保存登录信息
     */
    SysLoginUser saveLoginInfo(String empid, String username, String password);

    /**
     * @param orgId
     * @param postId
     * @return
     * @category 读取机构-岗位下的人员ID列表
     */
    Set<String> getEmployeeIdsByOrgIdPostId(String orgId, String postId);

    /**
     * @param orgId
     * @param postId
     * @return
     * @category 读取机构-岗位下的人员相互关系
     */
    List<SysEmployeeOrgPost> getSysEmployeeOrgPostsByOrgIdPostId(String orgId, String postId);

    /**
     * @param orgId
     * @param postId
     * @return
     * @category 读取机构-岗位下的人员ID列表
     */
    Set<String> getEmployeeIdsByOrgIdPostIdDisableTenant(String orgId, String postId);

    /**
     * @param orgId
     * @param postId
     * @return
     * @category 读取机构-岗位下的人员相互关系
     */
    List<SysEmployeeOrgPost> getSysEmployeeOrgPostsByOrgIdPostIdDisableTenant(String orgId, String postId);

    /**
     * 初始化全部人员的密码
     *
     * @param
     * @return
     * <AUTHOR>
     */
    Boolean initPasswordForAllUser();

    /**
     * @param postId
     * @return
     * @category 读取岗位下的人员列表
     */
    List<SysEmployeeOrgPost> getSysEmployeeOrgPostsByPostId(String postId);

    /**
     * 获取推荐的用户名
     *
     * @param userName 传入的用户名
     * @param type     2：拼音全拼
     * @return
     */
    String getLoginName(String userName, Integer type);

    /**
     * 初始化全部人员的登录信息
     *
     * @param 类型 1 工号 2 拼写 3 手机号
     * @return
     */
    String initLoginInfoAll(EmpInitLoginDto dto);

    /**
     * 获取人员的基本信息，不关联表，只查询未删除的
     *
     * @return
     */
    List<SysEmployeeInfo> getEmpAllList();

    /**
     * 从redis中获取人员信息
     *
     * @param empId 人员id
     * @return
     */
    EmployeeVo getEmployeeFromReids(String empId);

    /**
     * 人员信息存储到redis
     *
     * @param empId
     * @return
     */
    EmployeeVo setEmployeeToReids(String tenantId, String empId);

    /**
     * 初始化人员信息到redis
     *
     * @param list
     */
    void initEmployeeData(List<SysLoginUser> list);

    /**
     * 清除人员信息redis
     *
     * @param empid
     */
    void clearEmployeeData(String empid);

    /**
     * 根据组织编码获取人员信息
     *
     * @param orgcode
     * @return
     */
    List<EmployeeVo> getEmployeeByOrgcode(String orgcode);

    /**
     * 根据组织编码获取人员信息
     *
     * @param orgcode 组织编码
     * @param page    页码
     * @param size    每页大小
     * @return
     */
    List<EmployeeVo> getEmployeeByOrgcode(EmpParamDto paramDto, Pagination<?> page);
}
