package com.yunhesoft.system.employee.service;

import java.util.List;
import java.util.Map;

import com.yunhesoft.system.employee.entity.dto.EmpParamDto;
import com.yunhesoft.system.employee.entity.dto.EmployeePermDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeQueryDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;

/**
 * 人员信息
 *
 * <AUTHOR>
 */
public interface ISysEmployeeInfoService {
	/**
	 * 公司
    */
   public static final int staffType_4 = 4; //1全职 2兼职 3实习 4外委 5其他
	
    /**
     * 批量添加人员信息
     *
     * @param list
     * @param listPo
     * @return
     */
    boolean addBatch(List<SysEmployeeInfo> listPo);

    /**
     * 批量删除人员信息
     *
     * @param list
     * @param listPo
     * @return
     */
    boolean deleteBatchById(List<SysEmployeeInfo> listPo);

    /**
     * 批量更新人员信息
     *
     * @param list
     * @param listPo
     * @return
     */
    boolean updateBatchByIds(List<SysEmployeeInfo> listPo);

    /**
     * 获取人员信息
     *
     * @param param
     * @return
     */
    List<SysEmployeeInfo> getEmployee(EmpParamDto param);

    /**
     * @param empid
     * @return
     * @category 根据主键查找用户信息
     */
    SysEmployeeInfo findEmployeeById(String empid);

//	/**
//	 * @category 根据主键查找用户信息
//	 * @param empid
//	 * @param map 传入空map，会对人员信息二次缓存，加快速度
//	 * @return
//	 */
//	SysEmployeeInfo findEmployeeByIdMap(String empid,Map<String,SysEmployeeInfo> map);

    /**
     * 初始化人员信息到redis
     */
    void initRedis();

    /**
     * 更新人员信息redis信息
     *
     * @param emp
     */
    void updateReids(SysEmployeeInfo emp);

    /**
     * 更新人员信息redis信息
     *
     * @param empList
     */
    void updateReids(List<SysEmployeeInfo> empList);

    /**
     * 获取员工信息（从数据库获取）
     *
     * @param empidList
     * @return
     */
    List<SysEmployeeInfo> findEmployeeByIds(List<String> empids);

    /**
     * 获取员工信息map优先从redis获取
     *
     * @param empidList
     * @return
     */
    Map<String, SysEmployeeInfo> getEmployeeMap(List<String> empidList);

    /**
     * 根据指定参数获取人员信息
     *
     * @param param EmployeeQueryDto
     * @return
     * @category
     * <AUTHOR>
     */
    List<EmployeeVo> queryEmployeeByParam(EmployeeQueryDto param);

    /**
     * 根据指定权限获取人员信息
     *
     * @param param EmployeePermDto
     * @return
     * @category
     * <AUTHOR>
     */
    List<EmployeeVo> queryEmployeeByPerm(EmployeePermDto param);

    Integer getStaffBitNum();
    /**
         * 根据人员工号获取人员
     * @category 根据人员工号获取人员
     * <AUTHOR> 
     * @param staffNo 工号
     * @return SysEmployeeInfo
     */
    SysEmployeeInfo findEmployeeByStaffNo(String staffNo);
    /**
	  *获取兼岗指定信息(只取外委人员)
	 * @category 获取兼岗指定信息(只取外委人员)
	 * <AUTHOR> 
	 * @param orgCode 机构代码
	 * @param postId  岗位id
	 * @param hasSubOrg 是否包含指定机构的子机构
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
	 */
	List<EmployeeVo> getPartTimePost(String orgCode,String postId,boolean hasSubOrg);
    /**
         * 获取兼岗指定信息(只取外委人员)
     * @category 获取兼岗指定信息(只取外委人员)
     * <AUTHOR> 
     * @param orgCode 机构代码
     * @param postIdList  岗位id列表
     * @param hasSubOrg 是否包含指定机构的子机构
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
     */
    List<EmployeeVo> getPartTimePost(String orgCode,List<String> postIdList,boolean hasSubOrg);
    /**
         * 获取人员兼岗(只取外委人员)
     * @category 获取人员兼岗(只取外委人员)
     * <AUTHOR> 
     * @param userId
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
     */
    List<EmployeeVo> getUserPartTimePost(String userId);
    /**
	  * 获取兼岗指定信息
	 * @category 获取兼岗指定信息
	 * <AUTHOR> 
	 * @param orgCode 机构代码
	 * @param postId 岗位id
	 * @param hasSubOrg 是否包含指定机构的子机构
	 * @param staffType 员工类型  1全职 2兼职 3实习 4外委 5其他
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
	 */
	List<EmployeeVo> getPartTimePost(String orgCode,String postId,boolean hasSubOrg,Integer staffType);
    /**
	  * 获取兼岗指定信息
	 * @category 获取兼岗指定信息
	 * <AUTHOR> 
	 * @param orgCode 机构代码
	 * @param postIdList 岗位id列表
	 * @param hasSubOrg 是否包含指定机构的子机构
	 * @param staffType 员工类型  1全职 2兼职 3实习 4外委 5其他
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
	 */
	List<EmployeeVo> getPartTimePost(String orgCode,List<String> postIdList,boolean hasSubOrg,Integer staffType);
	/**
	  * 获取人员兼岗
	 * @category 获取人员兼岗
	 * <AUTHOR> 
	 * @param userId 人员ID
	 * @param staffType 员工类型  1全职 2兼职 3实习 4外委 5其他
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
	 */
	List<EmployeeVo> getUserPartTimePost(String userId,Integer staffType);
}
