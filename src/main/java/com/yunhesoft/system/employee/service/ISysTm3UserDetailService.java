package com.yunhesoft.system.employee.service;

import java.util.List;

import com.yunhesoft.system.employee.entity.vo.SysTm3UserTree;
import com.yunhesoft.system.employee.entity.vo.Tm3UserBeanVo;


/**
 * TM3人员
 * <AUTHOR>
 *
 */
public interface ISysTm3UserDetailService {
	/**
	 * 查询Tm3人员
	 * @param	porgDm	默认时0，当porgDm是null时，不返回结果	
	 * @return
	 * 			返回TM3机构
	 */
	List<SysTm3UserTree> listTm3UserData(String porgDm);

	/**
	 * 通过TM3人员名称查询
	 * @param userName
	 * @return
	 */
	List<SysTm3UserTree> listTm3LikeUserNameData(String userName);
	
	List<SysTm3UserTree> listTm3LikeUserNameData(Tm3UserBeanVo userBean);

	/**
	 * 通过IDS查询数据
	 * @param ids
	 * @return
	 */
	List<SysTm3UserTree> getUserIds(String ids);
}
