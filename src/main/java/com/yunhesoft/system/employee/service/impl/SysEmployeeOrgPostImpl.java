package com.yunhesoft.system.employee.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.employee.entity.dto.EmpOrgPostParamDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgPostService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;

import lombok.extern.log4j.Log4j2;

/**
 * 机构岗位人员信息
 * 
 * <AUTHOR>
 */
@Log4j2
@Service
public class SysEmployeeOrgPostImpl implements ISysEmployeeOrgPostService {

	@Autowired
	private EntityService entityService;

	/**
	 * 批量添加人员机构岗位信息
	 * 
	 * @param list
	 * @return
	 */
	@Override
	public boolean addBatch(List<SysEmployeeOrgPost> listPo) {
		boolean flag = true;

		try {
			if (listPo != null && listPo.size() > 0) {
//				flag = this.saveBatch(listPo);
				int b = entityService.insertBatch(listPo);
				flag = (b > 0 ? true : false);
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return flag;
	}

	/**
	 * 通过记录ID批量删除人员
	 * 
	 * @param list
	 * @return
	 */
	@Override
	public boolean deleteBatchById(List<SysEmployeeOrgPost> listPo) {
		boolean flag = true;

		try {
			if (listPo != null && listPo.size() > 0) {
//				List<String> idList = new ArrayList<String>();
//				for (SysEmployeeOrgPost b : listPo) {
//					idList.add(b.getId());
//				}
//				flag = this.removeByIds(idList);

				int b = entityService.deleteByIdBatch(listPo);
				flag = (b > 0 ? true : false);
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return flag;
	}

	/**
	 * 批量更新人员机构岗位信息
	 * 
	 * @param list
	 * @return
	 */
	@Override
	public boolean updateBatchByIds(List<SysEmployeeOrgPost> listPo) {
		boolean flag = true;

		try {
			if (listPo != null && listPo.size() > 0) {
//				flag = this.updateBatchById(listPo);

				int b = entityService.updateByIdBatch(listPo);
				flag = (b > 0 ? true : false);
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return flag;
	}

	/**
	 * 获取岗位使用数量
	 * 
	 * @param postId 岗位id
	 * @return
	 */
	@Override
	public int getEmployeePostCount(String postId) {
		int count = 0;
		// SELECT * from sys_employee_org_post WHERE postid='user_district' and USED=1
		Where query = Where.create();
		query.eq(SysEmployeeOrgPost::getUsed, 1);
		query.eq(SysEmployeeOrgPost::getPostid, postId);
		count = entityService.queryCount(SysEmployeeOrgPost.class, query).intValue();
		return count;
	}

	/**
	 * 根据人员id列表 获取机构岗位信息
	 * 
	 * @param empidList
	 * @return
	 */
	@Override
	public List<SysEmployeeOrgPost> getEmployeeOrgPost(List<String> empidList) {
		if (StringUtils.isNotEmpty(empidList)) {
			EmpOrgPostParamDto paramDto = new EmpOrgPostParamDto();
			paramDto.setEmpidList(empidList);
			return this.getEmployeeOrgPost(paramDto);
		} else {
			return null;
		}
	}

	/**
	 * 根据人员id列表 获取机构兼岗信息
	 * 
	 * @param empidList
	 * @return
	 */
	@Override
	public List<SysEmployeeOrgPost> getEmployeePartOrgPost(List<String> empidList) {
		if (StringUtils.isNotEmpty(empidList)) {
			EmpOrgPostParamDto paramDto = new EmpOrgPostParamDto();
			paramDto.setEmpidList(empidList);
			return this.getEmployeePartOrgPost(paramDto);
		} else {
			return null;
		}
	}

	/**
	 * 获取人员机构岗位信息
	 * 
	 * @return
	 */
	@Override
	public List<SysEmployeeOrgPost> getEmployeeOrgPost(EmpOrgPostParamDto paramDto) {
		List<SysEmployeeOrgPost> list = new ArrayList<SysEmployeeOrgPost>();

		try {
			// 参数
			String empid = paramDto.getEmpid();
			String orgcode = paramDto.getOrgcode();
			String postid = paramDto.getPostid();
			Integer used = paramDto.getUsed();
			Integer current = paramDto.getCurrent();
			Integer size = paramDto.getSize();
			Where query = Where.create();
			Order order = Order.create();

			// 数据有效标识（1：有效，0：无效）
			if (used != null) {
				query.eq(SysEmployeeOrgPost::getUsed, used.intValue());
			}

			// 过滤人员信息id，支持多个，逗号分隔
			List<String> empidList = paramDto.getEmpidList();
			if (StringUtils.isEmpty(empidList)) {
				if (empid != null && !"".equals(empid)) {
					empidList = Arrays.asList(empid.split(","));
				}
			}
			if (StringUtils.isNotEmpty(empidList)) {
				if (empidList.size() == 1) {
					query.eq(SysEmployeeOrgPost::getEmpid, empidList.get(0));
				} else {
					query.in(SysEmployeeOrgPost::getEmpid, empidList.toArray());
				}
			}
			// 过滤机构代码
			if (orgcode != null && !"".equals(orgcode)) {
				query.eq(SysEmployeeOrgPost::getOrgcode, orgcode);
			}

			// 过滤岗位代码
			if (postid != null && !"".equals(postid)) {
				query.eq(SysEmployeeOrgPost::getPostid, postid);
			}

			// 查询主岗位
			query.eq(SysEmployeeOrgPost::getStatus, 1);

			// 排序
			order.orderByAsc(SysEmployeeOrgPost::getTmSort);

			if (current != null && size != null) { // 分页
//				IPage<SysEmployeeOrgPost> page = new Page<SysEmployeeOrgPost>();
//				page.setCurrent(current.longValue());
//				page.setSize(size.longValue());
//				IPage<SysEmployeeOrgPost> r = this.page(page, query);
//				list = r.getRecords();
//				if (list.size() > 0) {
//					list.get(0).setRecordCount(r.getTotal());
//				}

				// 创建分页对象
				Pagination<?> page = Pagination.create(current, size);
				// 读取总记录数量
				page.setTotal(entityService.queryCount(SysEmployeeOrgPost.class, query).intValue());
				// 读取记录结果
				list = entityService.queryList(SysEmployeeOrgPost.class, query, page, order);
				if (list != null && list.size() > 0) {
					list.get(0).setRecordCount(page.getTotal());
				}

			} else { // 不分页
//				list = this.list(query);
				list = entityService.queryList(SysEmployeeOrgPost.class, query, order);

				if (list != null && list.size() > 0) {
					list.get(0).setRecordCount(new Long(list.size()));
				}
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return list;
	}

	/**
	 * 获取人员机构兼岗信息
	 * 
	 * @return
	 */
	@Override
	public List<SysEmployeeOrgPost> getEmployeePartOrgPost(EmpOrgPostParamDto paramDto) {
		List<SysEmployeeOrgPost> list = new ArrayList<SysEmployeeOrgPost>();

		try {
			// 参数
			String empid = paramDto.getEmpid();
			String orgcode = paramDto.getOrgcode();
			String postid = paramDto.getPostid();
			Integer used = paramDto.getUsed();
			Integer current = paramDto.getCurrent();
			Integer size = paramDto.getSize();
			Where query = Where.create();
			Order order = Order.create();

			// 数据有效标识（1：有效，0：无效）
			if (used != null) {
				query.eq(SysEmployeeOrgPost::getUsed, used.intValue());
			}

			// 过滤人员信息id，支持多个，逗号分隔
			List<String> empidList = paramDto.getEmpidList();
			if (StringUtils.isEmpty(empidList)) {
				if (empid != null && !"".equals(empid)) {
					empidList = Arrays.asList(empid.split(","));
				}
			}
			if (StringUtils.isNotEmpty(empidList)) {
				if (empidList.size() == 1) {
					query.eq(SysEmployeeOrgPost::getEmpid, empidList.get(0));
				} else {
					query.in(SysEmployeeOrgPost::getEmpid, empidList.toArray());
				}
			}
			// 过滤机构代码
			if (orgcode != null && !"".equals(orgcode)) {
				query.eq(SysEmployeeOrgPost::getOrgcode, orgcode);
			}

			// 过滤岗位代码
			if (postid != null && !"".equals(postid)) {
				query.eq(SysEmployeeOrgPost::getPostid, postid);
			}

			// 查询主岗位
			query.eq(SysEmployeeOrgPost::getStatus, 2);

			// 排序
			order.orderByAsc(SysEmployeeOrgPost::getTmSort);

			if (current != null && size != null) { // 分页
//				IPage<SysEmployeeOrgPost> page = new Page<SysEmployeeOrgPost>();
//				page.setCurrent(current.longValue());
//				page.setSize(size.longValue());
//				IPage<SysEmployeeOrgPost> r = this.page(page, query);
//				list = r.getRecords();
//				if (list.size() > 0) {
//					list.get(0).setRecordCount(r.getTotal());
//				}

				// 创建分页对象
				Pagination<?> page = Pagination.create(current, size);
				// 读取总记录数量
				page.setTotal(entityService.queryCount(SysEmployeeOrgPost.class, query).intValue());
				// 读取记录结果
				list = entityService.queryList(SysEmployeeOrgPost.class, query, page, order);
				if (list != null && list.size() > 0) {
					list.get(0).setRecordCount(page.getTotal());
				}

			} else { // 不分页
//				list = this.list(query);
				list = entityService.queryList(SysEmployeeOrgPost.class, query, order);

				if (list != null && list.size() > 0) {
					list.get(0).setRecordCount(new Long(list.size()));
				}
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return list;
	}

	/**
	 * 根据人员id列表 获取机构岗位信息map
	 * 
	 * @param empidList
	 * @return
	 */
	@Override
	public Map<String, SysEmployeeOrgPost> getEmployeeOrgPostMap(List<String> empidList) {
		List<SysEmployeeOrgPost> list = this.getEmployeeOrgPost(empidList);
		Map<String, SysEmployeeOrgPost> map = new HashMap<String, SysEmployeeOrgPost>();
		if (StringUtils.isNotEmpty(list)) {
			map = list.stream().collect(Collectors.toMap(SysEmployeeOrgPost::getEmpid, a -> a, (k1, k2) -> k1));
		}
		return map;
	}

	/**
	 * 根据人员id列表 获取机构兼岗信息map
	 * 
	 * @param empidList
	 * @return
	 */
	@Override
	public Map<String, SysEmployeeOrgPost> getEmployeeOrgPartPostMap(List<String> empidList) {
		List<SysEmployeeOrgPost> list = this.getEmployeePartOrgPost(empidList);
		Map<String, SysEmployeeOrgPost> map = new HashMap<String, SysEmployeeOrgPost>();
		if (StringUtils.isNotEmpty(list)) {
			map = list.stream().collect(Collectors.toMap(SysEmployeeOrgPost::getEmpid, a -> a, (k1, k2) -> k1));
		}
		return map;
	}

	/**
	 * 批量删除人员机构岗位信息
	 * 
	 * @param list
	 * @return
	 */
	@Override
	public boolean deleteBatchByUserId(String id,int status) {
		Where where = Where.create();
		where.in(SysEmployeeOrgPost::getEmpid, id);
		where.in(SysEmployeeOrgPost::getStatus, status);
		int i = entityService.rawDeleteByWhere(SysEmployeeOrgPost.class, where);
		return i > 0;
	}
}
