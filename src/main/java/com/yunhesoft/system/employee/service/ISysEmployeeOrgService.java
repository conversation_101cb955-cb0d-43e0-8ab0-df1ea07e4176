package com.yunhesoft.system.employee.service;

import java.util.List;

import com.yunhesoft.system.employee.entity.dto.EmpOrgParamDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;

/**
 * <AUTHOR>
 */
public interface ISysEmployeeOrgService {
	/**
	 * 批量添加人员机构信息
	 * 
	 * @param list
	 * @return
	 */
	boolean addBatch(List<SysEmployeeOrg> listPo);

	/**
	 * 批量删除人员机构信息
	 * 
	 * @param list
	 * @return
	 */
	boolean deleteBatchById(List<SysEmployeeOrg> listPo);

	/**
	 * 批量更新人员机构信息
	 * 
	 * @param list
	 * @return
	 */
	boolean updateBatchByIds(List<SysEmployeeOrg> listPo);

	/**
	 * 获取人员机构信息
	 * 
	 * @return
	 */
	List<SysEmployeeOrg> getEmployeeOrg(EmpOrgParamDto paramDto);

	/**
	 * 根据人员id获取机构信息
	 * 
	 * @param empId 人员id
	 * @param type  类型 equal:平级；parent:上级
	 * 
	 * @return
	 */
	String getOrgIdByEmpId(String empId, String type);

	List<SysEmployeeInfo> searchNameLike(String name, List<String> fields, int limit);
}
