package com.yunhesoft.system.employee.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.employee.entity.dto.EmpOrgPostParamDto;
import com.yunhesoft.system.employee.entity.dto.EmpParamDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.employee.service.IEmployeeExtraOperationService;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgPostService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.entity.po.SysOrgRelation;
import com.yunhesoft.system.org.service.ISysOrgRelationService;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.post.entity.vo.PostVo;
import com.yunhesoft.system.post.service.IPostBasicOperationService;
import com.yunhesoft.system.role.entity.po.SysRole;
import com.yunhesoft.system.role.entity.po.SysUserRole;
import com.yunhesoft.system.role.service.ISysRoleService;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
public class EmployeeExtraOperationImpl implements IEmployeeExtraOperationService {
	/** 外部模块服务接口 ----------------------------------- */
	/** 岗位模块服务接口 */
	@Autowired
	private IPostBasicOperationService extPostServ;
	/** 机构模块服务接口 */
	@Autowired
	private ISysOrgService extOrgServ;
	/** 角色模块服务接口 */
	@Autowired
	private ISysRoleService extRoleServ;

	/** 机构模块服务接口 */
	@Autowired
	private ISysOrgService orgServ;

	/** 机构人员岗位接口 */
	@Autowired
	private ISysEmployeeOrgPostService empOrgPostServ;
	/** 人员信息 */
	@Autowired
	private ISysEmployeeInfoService empInfoServ;

	/** 机构关系 */
	@Autowired
	private ISysOrgRelationService orgRelServ;

	@Autowired
	private EntityService entityService;

	/**
	 * 根据岗位id获取岗位信息（调用岗位模块接口）
	 * 
	 * @category 根据岗位id获取岗位信息
	 * @param postList
	 * @return
	 */
	@Override
	public List<PostVo> getExtPostList(List<SysEmployeeOrgPost> postList) {
		List<PostVo> extPostList = new ArrayList<PostVo>();

		try {
			if (postList != null && postList.size() > 0) {
				List<String> postidList = new ArrayList<String>();
				for (SysEmployeeOrgPost orgPost : postList) {
					postidList.add(orgPost.getPostid());
				}
				extPostList = extPostServ.getPostByIdList(postidList);
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return extPostList;
	}

	/**
	 * 根据岗位id获取岗位信息（调用岗位模块接口）
	 * 
	 * @category 根据岗位id获取岗位信息
	 * @param postList
	 * @return
	 */
	@Override
	public List<PostVo> getExtPostListById(List<String> postidList) {
		List<PostVo> extPostList = new ArrayList<PostVo>();

		try {
			if (postidList != null && postidList.size() > 0) {
				extPostList = extPostServ.getPostByIdList(postidList);
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return extPostList;
	}

	/**
	 * 根据机构代码获取机构信息（调用机构模块接口）
	 * 
	 * @category 根据机构代码获取机构信息
	 * @param empOrgList
	 * @return
	 */
	@Override
	public List<SysOrg> getExtOrgList(List<SysEmployeeOrg> empOrgList) {
		List<SysOrg> extOrgList = new ArrayList<SysOrg>();

		try {
			if (empOrgList != null && empOrgList.size() > 0) {
				List<String> orgcodeList = new ArrayList<String>();
				for (SysEmployeeOrg empOrg : empOrgList) {
					orgcodeList.add(empOrg.getOrgcode());
				}
				extOrgList = extOrgServ.listDatas(orgcodeList);
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return extOrgList;
	}

	/**
	 * 根据机构代码获取机构信息（调用机构模块接口）
	 * 
	 * @category 根据机构代码获取机构信息
	 * @param empOrgList
	 * @return
	 */
	@Override
	public List<SysOrg> getExtOrgListByCode(List<String> orgcodeList) {
		List<SysOrg> extOrgList = new ArrayList<SysOrg>();

		try {
			if (orgcodeList != null && orgcodeList.size() > 0) {
				extOrgList = extOrgServ.listDatas(orgcodeList);
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return extOrgList;
	}

	/**
	 * 根据角色id获取角色信息（调用角色模块接口）
	 * 
	 * @category 根据角色代码获取角色信息
	 */
	@Override
	public List<SysRole> getExtRoleList(List<SysUserRole> roleList) {
		List<SysRole> extRoleList = new ArrayList<SysRole>();

		try {
			if (roleList != null && roleList.size() > 0) {
				List<String> roleidList = new ArrayList<String>();
				for (SysUserRole empRole : roleList) {
					roleidList.add(empRole.getRoleid());
				}
				extRoleList = extRoleServ.queryRoleListByIds(roleidList);
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return extRoleList;
	}

	/**
	 * 根据企业注册信息查找企业对应的区域编码
	 * 
	 * @param orgcode
	 * @return
	 */
	private String getOrgNumber(String orgcode) {
		String code = "";
		List<SysOrgRelation> listParent = orgRelServ.listData(orgcode); // 查找父机构
		if (listParent != null && listParent.size() > 0) {
			String porgcode = listParent.get(0).getPorgcode();
			List<SysOrg> list = orgServ.listData(porgcode);
			if (StringUtils.isNotEmpty(list)) {
				code = list.get(0).getOrgNumber();
			}
		}
		return code;
	}

	/**
	 * 根据登录人的机构代码获取审批人列表
	 * 
	 * @param myOrgcode 我的机构代码
	 * @param orgType   机构类型 （区科技局 or 市科技局 代码是数据字典自定义的）
	 * @param postId    审批人的岗位id
	 * @return
	 */
	@Override
	public List<SysEmployeeInfo> getApproveEmpList(String myOrgcode, String orgType, String postId) {
		List<SysEmployeeInfo> list = new ArrayList<SysEmployeeInfo>();
		List<SysOrg> listOrg = this.orgServ.getOrgByType(orgType);
		if (StringUtils.isNotEmpty(listOrg)) {
			String approveOrgcode = ""; // 审批单位机构代码
			if (listOrg.size() == 1) {// 市科技局
				approveOrgcode = listOrg.get(0).getId();
			} else {// 查找对应区科技局
				// 根据企业注册信息查找企业对应的区域编码
				String orgNumber = this.getOrgNumber(myOrgcode); // 区编码
				if (StringUtils.isNotEmpty(orgNumber)) {
					for (SysOrg sysOrg : listOrg) {
						if (orgType.equals(sysOrg.getOrgType()) && orgNumber.equals(sysOrg.getOrgNumber())) {
							approveOrgcode = sysOrg.getId();
							break;
						}
					}
				}
			}
			if (StringUtils.isNotEmpty(approveOrgcode)) {
				// 查找机构下的对应岗位人员
				EmpOrgPostParamDto paramDto = new EmpOrgPostParamDto();
				paramDto.setUsed(1);
				paramDto.setPostid(postId);
				paramDto.setOrgcode(approveOrgcode);
				List<SysEmployeeOrgPost> listEmp = empOrgPostServ.getEmployeeOrgPost(paramDto);// 查找机构岗位下的人员
				if (StringUtils.isNotEmpty(listEmp)) {
					EmpParamDto param = new EmpParamDto();
					StringBuffer empIds = new StringBuffer();
					for (SysEmployeeOrgPost bean : listEmp) {
						empIds.append(bean.getEmpid()).append(",");
					}
					param.setEmpid(empIds.toString());
					param.setEmpid(param.getEmpid().substring(0, param.getEmpid().length() - 1));
					param.setUsed(1);
					list = empInfoServ.getEmployee(param);// 根据人员id查找人员信息
				}
			}
		}
		return list;
	}

	/**
	 * 根据用户ID查询用户的机构+岗位的列表
	 * 
	 * @param empid
	 * @return
	 */
	@Override
	public List<String> findEmplyeeOrgPostById(String empid) {
		List<String> res = new ArrayList<String>();
		List<SysEmployeeOrgPost> posts = entityService.queryList(SysEmployeeOrgPost.class,
				Where.create().eq(SysEmployeeOrgPost::getEmpid, empid));
		if (posts != null && posts.size() > 0) {
			List<String> ids = new ArrayList<String>();
			posts.forEach(post -> {
				ids.add(post.getOrgcode());
			});
			List<SysOrg> orgs = entityService.queryList(SysOrg.class,
					Where.create().andIns(SysOrg::getId, ids.toArray()));
			posts.forEach(post -> {
				Optional<SysOrg> org = orgs.stream().filter(org1 -> org1.getOrgcode().equals(post.getOrgcode()))
						.findFirst();
				if (org != null) {
					res.add(org.get().getOrgType() + "____" + post.getPostid());
				}
			});
		}
		return res;
	}

	/**
	 * 根据用户ID查询用户的机构代码+岗位ID的列表
	 * 
	 * @param empid
	 * @return
	 * 
	 *         by G.fj 2021.12.3
	 */
	@Override
	public List<String> findEmplyeeOrgCode____PostById(String empid) {
		List<String> res = new ArrayList<String>();
		List<SysEmployeeOrgPost> posts = entityService.queryList(SysEmployeeOrgPost.class,
				Where.create().eq(SysEmployeeOrgPost::getEmpid, empid).eq(SysEmployeeOrgPost::getUsed, 1));
		if (posts != null && posts.size() > 0) {
			List<String> ids = new ArrayList<String>();
			posts.forEach(post -> {
				ids.add(post.getOrgcode());
			});
			List<SysOrg> orgs = entityService.queryList(SysOrg.class,
					Where.create().andIns(SysOrg::getId, ids.toArray()));
			posts.forEach(post -> {
				Optional<SysOrg> org = orgs.stream().filter(org1 -> org1.getOrgcode().equals(post.getOrgcode()))
						.findFirst();
				if (org != null) {
					res.add(org.get().getOrgcode() + "____" + post.getPostid());
				}
			});
		}
		return res;
	}

	/**
	 * 根据用户ID查询用户的岗位ID和机构岗位ID
	 * 
	 * @param empid
	 * @return
	 * 
	 *         by G.fj 2022-11-11
	 */
	@Override
	public List<String> findEmplyeeOrgCodeMixPostById(String empid) {
		List<String> res = new ArrayList<String>();
		List<SysEmployeeOrgPost> posts = entityService.queryList(SysEmployeeOrgPost.class,
				Where.create().eq(SysEmployeeOrgPost::getEmpid, empid).eq(SysEmployeeOrgPost::getUsed, 1));
		if (posts != null && posts.size() > 0) {
			posts.forEach(post -> {
				if (!ObjUtils.isEmpty(post.getOrgcode())) {
					res.add(post.getOrgcode() + "____" + post.getPostid());
				}
				res.add(post.getPostid());
			});
		}
		return res;
	}

	/**
	 * 获取人员兼岗机构信息
	 * 
	 * @category 获取人员机构信息
	 * @return
	 */
	@Override
	public List<SysOrg> getExtPartOrgList(List<SysEmployeeOrgPost> partPostList) {
		List<String> orgIdList = new ArrayList<>();
		for (SysEmployeeOrgPost orgPost : partPostList) {
			orgIdList.add(orgPost.getOrgcode());
		}
		return extOrgServ.getOrgListById(orgIdList);
	}

}
