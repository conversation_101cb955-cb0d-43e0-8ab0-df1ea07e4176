package com.yunhesoft.system.employee.service;

import java.util.List;
import java.util.Map;

import com.yunhesoft.system.employee.entity.dto.EmpOrgPostParamDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;

/**
 * <AUTHOR>
 */
public interface ISysEmployeeOrgPostService {
	/**
	 * 批量添加人员机构岗位信息
	 * 
	 * @param list
	 * @return
	 */
	boolean addBatch(List<SysEmployeeOrgPost> listPo);

	/**
	 * 批量删除人员机构岗位信息
	 * 
	 * @param list
	 * @return
	 */
	boolean deleteBatchById(List<SysEmployeeOrgPost> listPo);

	/**
	 * 批量更新人员机构岗位信息
	 * 
	 * @param list
	 * @return
	 */
	boolean updateBatchByIds(List<SysEmployeeOrgPost> listPo);

	/**
	 * 获取人员机构岗位信息
	 * 
	 * @return
	 */
	List<SysEmployeeOrgPost> getEmployeeOrgPost(EmpOrgPostParamDto paramDto);
	
	/**
	 * 获取人员机构兼岗信息
	 * 
	 * @return
	 */
	List<SysEmployeeOrgPost> getEmployeePartOrgPost(EmpOrgPostParamDto paramDto);

	/**
	 * 获取岗位使用数量
	 * 
	 * @param postId 岗位id
	 * @return
	 */
	int getEmployeePostCount(String postId);

	/**
	 * 根据人员id列表 获取机构岗位信息
	 * 
	 * @param empidList
	 * @return
	 */
	List<SysEmployeeOrgPost> getEmployeeOrgPost(List<String> empidList);

	/**
	 * 根据人员id列表 获取机构岗位信息map
	 * 
	 * @param empidList
	 * @return
	 */
	Map<String, SysEmployeeOrgPost> getEmployeeOrgPostMap(List<String> empidList);
	/**
	 * 根据人员id列表 获取机构兼岗信息map
	 * 
	 * @param empidList
	 * @return
	 */
	Map<String, SysEmployeeOrgPost> getEmployeeOrgPartPostMap(List<String> empidList);

	/**
	 * 根据人员id列表 获取机构兼岗信息
	 * 
	 * @param empidList
	 * @return
	 */
	List<SysEmployeeOrgPost> getEmployeePartOrgPost(List<String> empidList);

	/**
	 * 删除指定人员的机构岗位信息
	 * 
	 * @param list
	 * @return
	 */
	boolean deleteBatchByUserId(String id,int status);
}
