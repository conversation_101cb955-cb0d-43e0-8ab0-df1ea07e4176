package com.yunhesoft.system.employee.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.rtdb.core.utils.DateTimeUtils;
import com.yunhesoft.system.employee.entity.dto.EmpParamDto;
import com.yunhesoft.system.employee.entity.dto.EmployeePermDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeQueryDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.IEmployeeBasicOperationService;
import com.yunhesoft.system.employee.service.ISysEmployeeChangeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.menu.service.SysMenuService;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.role.utils.IPermUtils;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;

import lombok.extern.log4j.Log4j2;

/**
 * 人员信息
 *
 * <AUTHOR>
 */
@Log4j2
@Service
public class SysEmployeeInfoImpl implements ISysEmployeeInfoService {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private EntityService entityService;

    @Autowired
    private RedisUtil redis;

    private static String RED_KEY = "SYSTEM:EMPLOYEE:INFO";

    @Autowired
    private IEmployeeBasicOperationService iEmpBaseSev;

    @Autowired
    private ISysOrgService orgSev;

    @Autowired
    private ISysEmployeeOrgService empOrgServ;
    /**
     * 人员机构信息表操作服务接口
     */
    @Autowired
    private ISysEmployeeChangeInfoService empChangeServ;

    @Autowired
    private IPermUtils permUtils; // 权限操作类

    @Autowired
    private SysMenuService menuService; // 菜单

    /**
     * 批量添加人员信息
     *
     * @param list
     * @return
     */
    @Override
    public boolean addBatch(List<SysEmployeeInfo> listPo) {
        boolean flag = true;
        try {
            if (listPo != null && listPo.size() > 0) {
                // int b = entityService.saveBatch(listPo);
                int b = entityService.insertBatch(listPo);
                flag = (b > 0 ? true : false);
                if (flag) {
                    this.updateReids(listPo); // 更新redis
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return flag;
    }

    /**
     * 通过记录ID批量删除人员
     *
     * @param list
     * @return
     */
    @Override
    public boolean deleteBatchById(List<SysEmployeeInfo> listPo) {
        boolean flag = true;
        try {
            if (listPo != null && listPo.size() > 0) {
                int b = entityService.deleteByIdBatch(listPo);
                flag = (b > 0 ? true : false);
                if (flag) {
                    for (SysEmployeeInfo sysEmployeeInfo : listPo) {
                        sysEmployeeInfo.setUsed(0);
                    }
                    this.updateReids(listPo);
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }

        return flag;
    }

    /**
     * 批量更新人员信息
     *
     * @param list
     * @return
     */
    @Override
    public boolean updateBatchByIds(List<SysEmployeeInfo> listPo) {
        boolean flag = true;
        try {
            if (listPo != null && listPo.size() > 0) {
                int b = entityService.updateByIdBatch(listPo);
                flag = (b > 0 ? true : false);
                if (flag) {
                    this.updateReids(listPo); // 更新redis
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }

        return flag;
    }

    /**
     * 获取人员信息
     *
     * @param paramDto
     * @return
     */
    @Override
    public List<SysEmployeeInfo> getEmployee(EmpParamDto paramDto) {
        List<SysEmployeeInfo> listEmp = new ArrayList<SysEmployeeInfo>();
        List<SysEmployeeInfo> listEmp1 = new ArrayList<SysEmployeeInfo>();
        List<SysEmployeeInfo> list = new ArrayList<SysEmployeeInfo>();
        try {
            // 参数
            String empid = paramDto.getEmpid();
            Integer invalid = paramDto.getInvalid();
            Integer used = paramDto.getUsed();
            Integer current = paramDto.getCurrent();
            Integer size = paramDto.getSize();
            String empname = paramDto.getEmpname();
            String staffNo = paramDto.getStaffNo();
            String mobile = paramDto.getMobile();
            String memo = paramDto.getMemo();
            Order order = paramDto.getOrder();

//			LambdaQueryWrapper<SysEmployeeInfo> query = new LambdaQueryWrapper<SysEmployeeInfo>();
            Where query = Where.create();
            // Order order = Order.create();

            // 过滤人员信息id，支持多个，逗号分隔
            Map<String, String> mapEmp = new HashMap<String, String>();
            if (empid != null && !"".equals(empid)) {
                List<String> empidList = Arrays.asList(empid.split(","));
                if (empidList != null && empidList.size() > 0) {
                    int m = empidList.size();
                    for (int i = 0; i < m; i++) {
                        String id = empidList.get(i);
                        mapEmp.put(id, "");
                    }
                }
            }
//			if (empid != null && !"".equals(empid)) {
//				List<String> empidList = Arrays.asList(empid.split(","));
//				if (empidList != null && empidList.size() > 0) {
//					int m = empidList.size();
//					for (int i = 0; i < m; i++) {
//						String id = empidList.get(i);
//						mapEmp.put(id, "");
//					}
//				}
//			}
            if (StringUtils.isNotEmpty(memo)) {// 这里只为了查询说明信息中存储的内容，数据量很小，直接使用in可以加快查询速度
                query.in(SysEmployeeInfo::getMemo, Arrays.asList(memo.split(",")).toArray());
            }

            // 只显示失效记录标识（1：是，0：不是）
            if (invalid != null && invalid.intValue() == 1) {
                query.eq(SysEmployeeInfo::getInvalid, invalid.intValue());
            }

//            // 数据有效标识（1：有效，0：无效）
//            if (used == null || used.intValue()==1) {
//                query.eq(SysEmployeeInfo::getUsed,1);
//            }else{
//                query.ne(SysEmployeeInfo::getUsed,1);
//                query.ne(SysEmployeeInfo::getUsed,0);
//            }
	          // 数据有效标识（1：有效，0：无效,-1退休 -2离职）
	         if (used == null || used.intValue()>=0) {//这里兼容非TLM程序（非TLM程序这里前台传入的是0)
	             query.eq(SysEmployeeInfo::getUsed,1);
	         }else{
	        	 query.lt(SysEmployeeInfo::getUsed, 0);//查询小于0的（退休-1 离职-2，用于还原已离职人员）
	         }
            
            // 人员姓名
            if (StringUtils.isNotEmpty(empname)) {
                query.like(SysEmployeeInfo::getEmpname, empname);
            }

            // 人员工号
            if (StringUtils.isNotEmpty(staffNo)) {
                query.like(SysEmployeeInfo::getStaffNo, staffNo);
            }

            // 手机号
            if (StringUtils.isNotEmpty(mobile)) {
                query.like(SysEmployeeInfo::getMobile, mobile);
            }

            // 排序

            if (order == null) {
                order = Order.create();
                order.orderByDesc(SysEmployeeInfo::getCreateTime);
                order.orderByAsc(SysEmployeeInfo::getTmSort);
                order.orderByDesc(SysEmployeeInfo::getId);
            }
            // 读取记录结果
            listEmp = entityService.queryList(SysEmployeeInfo.class, query, order);
            if (listEmp != null && listEmp.size() > 0) {
                // 判断是否需要过滤人员
                if (mapEmp != null && mapEmp.size() > 0) {
                    int n = listEmp.size();
                    for (int i = 0; i < n; i++) {
                        SysEmployeeInfo e = listEmp.get(i);
                        if (mapEmp.containsKey(e.getId())) {
                            listEmp1.add(e);
                        }
                    }
                } else {
                    listEmp1.addAll(listEmp);
                }
            }

            // 分页
            if (current != null && size != null && size.intValue() > 0) { // 分页
                // 分页
                if (current == 0) {
                    current = 1;
                }
                // 开始页
                Integer ks = (current - 1) * size;
                // 截止页
                Integer jz = current * size;
                if (jz > listEmp1.size()) {
                    jz = listEmp1.size();
                }
                list.addAll(listEmp1.subList(ks, jz));
            } else {
                list.addAll(listEmp1);
            }
            if (list != null && list.size() > 0) {
                list.get(0).setRecordCount(new Long(listEmp1.size()));
            }
        } catch (Exception e) {
            log.error("", e);
        }

        return list;
    }

    /**
     * @param empid
     * @return
     * @category 根据主键查找用户信息
     */
    @Override
    public SysEmployeeInfo findEmployeeById(String empid) {
        SysEmployeeInfo bean = getEmployeeFromRedis(empid);
        if (bean == null) {
            bean = entityService.queryObject(SysEmployeeInfo.class, Where.create().eq(SysEmployeeInfo::getId, empid));
            this.updateReids(bean);
        }
        return bean;
    }

    @Override
    public List<SysEmployeeInfo> findEmployeeByIds(List<String> empids) {
        Where where = Where.create().in(SysEmployeeInfo::getId, empids.toArray());
        return entityService.queryList(SysEmployeeInfo.class, where);
    }
//
//	@Override
//	public SysEmployeeInfo findEmployeeByIdMap(String empid, Map<String, SysEmployeeInfo> map) {
//		// TODO Auto-generated method stub
////		if(map.size()==0) {
////			Map<String, LinkedHashMap<String,Object>> map1 = redis.getMap(RED_KEY);
////			for(Entry<String, LinkedHashMap<String,Object>> temp:map1.entrySet()) {
////				map.put(temp.getKey(), ObjUtils.convertToObject(SysEmployeeInfo.class, temp.getValue()));
////			}
////		}
////		SysEmployeeInfo bean = map.get(empid);
////		if (bean == null) {
////			bean = entityService.queryObject(SysEmployeeInfo.class, Where.create().eq(SysEmployeeInfo::getId, empid));
////			this.updateReids(bean);
////		}
////		return bean;
//		SysEmployeeInfo result = null;
//        if (StringUtils.isNotEmpty(empid)) {
//        	if(map!=null) {
////				long i=System.currentTimeMillis();
//				if(map.size()==0) {
//					Map<String, LinkedHashMap<String,Object>> map1 = redis.getMap(RED_KEY);
//				//	System.out.println("X:"+(System.currentTimeMillis()-i));
//					for(Entry<String, LinkedHashMap<String,Object>> temp:map1.entrySet()) {
//						map.put(temp.getKey(), ObjUtils.convertToObject(SysEmployeeInfo.class, temp.getValue()));
//					}
//			      //  System.out.println("X:"+(System.currentTimeMillis()-i));
//				}
//				result = map.get(empid);
//		        if (result == null) {
//		        	result = entityService.queryObject(SysEmployeeInfo.class, Where.create().eq(SysEmployeeInfo::getId, empid));
//		        	if(result!=null) {
//		        		this.updateReids(result);
//		        		map.put(result.getId(), result);
//		        	}
//		        }
//        	}else {
//        		result = findEmployeeById(empid);
//        	}
//        }
//        return result;
//	}

    /**
     * 初始化人员信息到redis
     */
    @Override
    public void initRedis() {
        log.info("*****正在初始化人员信息到redis。。。");
        clearRedis();
        EmpParamDto paramDto = new EmpParamDto();
        paramDto.setUsed(1);
        List<SysEmployeeInfo> list = this.getEmployee(paramDto);
        if (StringUtils.isNotEmpty(list)) {
            updateReids(list);
        }
        log.info("*****人员信息初始化完毕！***********");
    }

    /**
     * 更新人员信息redis信息
     *
     * @param emp
     */
    @Override
    public void updateReids(SysEmployeeInfo emp) {
        if (emp != null) {
            Map<String, SysEmployeeInfo> map = new HashMap<String, SysEmployeeInfo>();
            map.put(emp.getId(), emp);
            redis.putMap(RED_KEY, map);
            iEmpBaseSev.clearEmployeeData(emp.getId());
        }
    }

    /**
     * 获取人员信息map
     *
     * @param empList
     * @return
     */
    private Map<String, SysEmployeeInfo> getEmpMap(List<SysEmployeeInfo> empList) {
        if (StringUtils.isNotEmpty(empList)) {
            Map<String, SysEmployeeInfo> map = empList.stream().collect(Collectors.toMap(SysEmployeeInfo::getId, a -> a, (k1, k2) -> k1));
            return map;
        } else {
            return null;
        }
    }

    /**
     * 更新redis信息
     */
    @Override
    public void updateReids(List<SysEmployeeInfo> empList) {
        if (empList != null) {
            Map<String, SysEmployeeInfo> map = getEmpMap(empList);
            redis.putMap(RED_KEY, map);
            for (SysEmployeeInfo sysEmployeeInfo : empList) {
                iEmpBaseSev.clearEmployeeData(sysEmployeeInfo.getId());
            }
        }
    }

    /**
     * 从redis中获取对象
     *
     * @param empid
     * @return
     */
    private SysEmployeeInfo getEmployeeFromRedis(String empid) {
        SysEmployeeInfo bean = null;
        if (redis.hasKey(RED_KEY)) {
            bean = redis.getMapValue(RED_KEY, empid, SysEmployeeInfo.class);
        }
        return bean;
    }

    /**
     * 获取员工信息map，优先从redis获取
     *
     * @param empidList
     * @return
     */
    @Override
    public Map<String, SysEmployeeInfo> getEmployeeMap(List<String> empidList) {
        Map<String, SysEmployeeInfo> rtnMap = new HashMap<>();
        List<SysEmployeeInfo> list = null;
        if (StringUtils.isNotEmpty(empidList)) {
            if (redis.hasKey(RED_KEY)) {
                list = getEmployeeFromRedis(empidList);//从redis中获取
            } else {
                this.initRedis();//初始化人员到redis
                list = getEmployeeFromRedis(empidList);//从redis中获取
            }
            if (StringUtils.isEmpty(list)) {//redis中不存在人员信息
                list = this.findEmployeeByIds(empidList);// 从数据库中获取
                rtnMap = this.getEmpMap(list);
            } else {//redis中存在人员信息
                Map<String, SysEmployeeInfo> tempMap = this.getEmpMap(list);
                if (tempMap != null) {
                    for (String empid : empidList) {
                        SysEmployeeInfo bean = tempMap.get(empid);
                        if (bean == null) {
                            bean = this.findEmployeeById(empid);//重新获取一次
                        }
                        rtnMap.put(empid, bean);
                    }
                }
            }
        }
        return rtnMap;
    }

    /**
     * 从redis中获取人员信息
     *
     * @param empidList
     * @return
     */
    private List<SysEmployeeInfo> getEmployeeFromRedis(List<String> empidList) {
        List<SysEmployeeInfo> list = new ArrayList<>();
        try {
            if (StringUtils.isNotEmpty(empidList)) {
                Collection<Object> params = new ArrayList<>(empidList);
                List<Object> tempList = redis.getMultiMapValue(RED_KEY, params);
                if (tempList != null && tempList.size() > 0) {
                    for (Object obj : tempList) {
                        if (obj != null) {
                            LinkedHashMap map = (LinkedHashMap<String, Object>) obj;
                            SysEmployeeInfo emp = ObjUtils.convertToObject(SysEmployeeInfo.class, map);
                            list.add(emp);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return list;
    }


    /**
     * 清除人员信息记录
     */
    public void clearRedis() {
        iEmpBaseSev.clearEmployeeData(null);
        redis.delete(RED_KEY);
    }

    /**
     * 根据指定参数获取人员信息
     *
     * @param param
     * @return
     * @category 根据指定参数获取人员信息
     * <AUTHOR>
     */
    @Override
    public List<EmployeeVo> queryEmployeeByParam(EmployeeQueryDto param) {
        // TODO Auto-generated method stub
        List<EmployeeVo> result = new ArrayList<EmployeeVo>();
        if (param != null) {
            boolean readAllSubOrg = true;
            if (param.getReadAllSubOrg() != null) {
                readAllSubOrg = param.getReadAllSubOrg().booleanValue();
            }
            boolean postChange = false;//是否包含岗位变动信息
            if (param.getHasPostChange() != null) {
                postChange = param.getHasPostChange().booleanValue();
            }
            String changeDt = param.getChangeDt();
            if (StringUtils.isEmpty(param.getChangeDt())) {
                changeDt = DateTimeUtils.getNowDateStr();//未指定日期则取当天
            }
//			Map<String, EmployeeChangeVo> empChangeMap = null;
//			List<SysEmployeeInfo> listEmp = null;
            if (StringUtils.isNotEmpty(param.getEmpIds()) || StringUtils.isNotEmpty(param.getStaffNos()) || StringUtils.isNotEmpty(param.getEmpNames())) {//指定了人员id或工号
                Where query = Where.create();
                query.eq(SysEmployeeInfo::getUsed, 1);
                boolean hasQuery = false;
                query.lb();
                if (StringUtils.isNotEmpty(param.getEmpIds())) {//指定了人员
                    hasQuery = true;
                    query.in(SysEmployeeInfo::getId, param.getEmpIds().toArray());
                }
                if (StringUtils.isNotEmpty(param.getStaffNos())) {//指定了工号
                    if (hasQuery) {
                        query.or();
                    }
                    hasQuery = true;
                    query.in(SysEmployeeInfo::getStaffNo, param.getStaffNos().toArray());
                }
                if (StringUtils.isNotEmpty(param.getEmpNames())) {//指定了姓名
                    if (hasQuery) {
                        query.or();
                    }
                    hasQuery = true;
                    query.in(SysEmployeeInfo::getEmpname, param.getEmpNames().toArray());
                }
                query.rb();
                List<SysEmployeeInfo> listEmp = entityService.queryList(SysEmployeeInfo.class, query);//查找人员
                if (StringUtils.isNotEmpty(listEmp)) {//查找到了人员
                    List<String> empIds = new ArrayList<String>();
                    for (SysEmployeeInfo temp : listEmp) {
                        empIds.add(temp.getId());
                    }
                    List<EmployeeVo> userList = empChangeServ.getUserChangeList(changeDt, empIds, postChange);//获取变动信息
                    if (StringUtils.isNotEmpty(param.getOrgCode())) {//指定了机构代码
                        List<String> orgCodeList = new ArrayList<String>();
                        if (readAllSubOrg) {//需要递归查询全部子机构
                            List<SysOrg> orgList = orgSev.getOrgList(param.getOrgCode());
                            if (StringUtils.isNotEmpty(orgList)) {//查找了指定机构和其全部子机构
                                for (SysOrg temp : orgList) {
                                    orgCodeList.add(temp.getOrgcode());
                                }
                            } else {
                                orgCodeList.add(param.getOrgCode());
                            }
                        } else {
                            orgCodeList.add(param.getOrgCode());
                        }
                        for (EmployeeVo temp : userList) {
                            if (orgCodeList.contains(temp.getOrgcode())) {//要求人员必须包含在机构人员的变动信息中
                                result.add(temp);
                            }
                        }
                    } else {
                        result = userList;
                    }
                }
            } else {
                if (StringUtils.isNotEmpty(param.getOrgCode())) {//指定了机构代码
                    result = empChangeServ.getOrgUserChange(changeDt, param.getOrgCode(), readAllSubOrg, postChange);
                }
            }
        }
        return result;
    }
//	/**
//	 * 根据指定权限获取人员信息
//	 * @category 
//	 * <AUTHOR> 
//	 * @param param EmployeePermDto
//	 * @return
//	 */
//	@Override
//	public List<EmployeeVo> queryEmployeeByPerm(EmployeePermDto param) {
//		// TODO Auto-generated method stub
//		List<EmployeeVo> result = new ArrayList<EmployeeVo>();
//		if(param!=null && StringUtils.isNotEmpty(param.getPermission())) {
//			int queryType =0;//0和null 只取人员当前信息（机构信息） 1取人员变动机构信息 2取人员变动的机构和岗位信息
//			if(param.getQueryType()!=null) {
//				queryType =param.getQueryType().intValue();
//			}
//			long i=System.currentTimeMillis();
//			List<String> permids = getPermIds(param.getPermission());//获取菜单对应的权限
//			List<String> listUser = permUtils.getEmpIdByPermid(permids); //获取权限人员ID
//			if(StringUtils.isNotEmpty(listUser)) {
//				Map<String,EmployeeVo> userMap =null;//变动信息map
//				if(queryType>0) {//查变动
//					String changeDt = param.getChangeDt();
//					if (StringUtils.isEmpty(param.getChangeDt())){
//						changeDt=DateTimeUtils.getNowDateStr();//未指定日期则取当天
//					}
//					boolean postChange =false;
//					if(queryType==2) {
//						postChange=true;
//					}
//					List<EmployeeVo> userList = empChangeServ.getUserChangeList(changeDt, listUser, postChange);//获取变动信息
//					if(StringUtils.isNotEmpty(userList)) {
//						userMap  = userList.stream().collect(Collectors.toMap(EmployeeVo::getEmpTmuid,
//								EmployeeVo -> EmployeeVo, (key1, key2) -> key1));// 将list转换为map// 重复键值时，第一个key不被第二个key覆盖
//					}
//				}else {//不查变动
//				}
//				List<String> getOrgList = new ArrayList<String>();
//				if(StringUtils.isNotEmpty(userMap)) {//有人员信息，则返回获取到的人员信息
//					for(String temp:listUser) {
//						EmployeeVo bean = userMap.get(temp);
//						if(bean!=null) {
//							result.add(bean);//对照到了人员信息
//						}else {
//							getOrgList.add(temp);
//						}
//					}
//				}else {
//					getOrgList.addAll(listUser);
//				}
//				if(getOrgList.size()>0) {//没有机构的人员
//					System.out.println("3:"+(System.currentTimeMillis()-i));
//					Map<String, SysEmployeeOrg> empOrgMap = new HashMap<String,SysEmployeeOrg>();//人员机构信息
//					EmpOrgParamDto orgParamDto = new EmpOrgParamDto();
//					orgParamDto.setCurrent(null);
//					orgParamDto.setSize(null);
//					orgParamDto.setUsed(1);
//					orgParamDto.setEmpid(StringUtils.join(getOrgList, ","));
//					List<SysEmployeeOrg> empOrgList = empOrgServ.getEmployeeOrg(orgParamDto);//获取当前的人员机构信息（不走变动）
//					System.out.println("4:"+(System.currentTimeMillis()-i));
//					if (StringUtils.isNotEmpty(empOrgList)) {		
//						for (SysEmployeeOrg temp : empOrgList) {
//							empOrgMap.put(temp.getEmpid(), temp);
//						}
//					}
//					System.out.println("4:"+(System.currentTimeMillis()-i));
//					Map<String,SysEmployeeInfo> umap = null;//new HashMap<String,SysEmployeeInfo>();
//					Map<String,SysOrg> omap = null;//new HashMap<String,SysOrg>();
//					int i1=0;
//					for(String temp:getOrgList) {
//						System.out.println("5:"+temp+":"+(System.currentTimeMillis()-i));
//						SysEmployeeInfo ubean = findEmployeeByIdMap(temp,umap);
//						System.out.println("5:"+temp+":"+(System.currentTimeMillis()-i));
//						if(ubean!=null) {
//							EmployeeVo vbean = ObjUtils.copyTo(ubean, EmployeeVo.class);// 复制属性
//							if(vbean!=null) {
//								vbean.setEmpTmuid(ubean.getId());
//								SysEmployeeOrg empOrg = empOrgMap.get(ubean.getId());
//								if(empOrg!=null) {
//									vbean.setOrgcode(empOrg.getOrgcode());
//									System.out.println("6:"+temp+":"+(System.currentTimeMillis()-i));
//									SysOrg org = orgSev.findOrgByIdMap(empOrg.getOrgcode(),omap);//获取机构
//									System.out.println("6:"+temp+":"+(System.currentTimeMillis()-i));
//									if(org!=null) {
//										vbean.setOrgname(org.getOrgname());
//									}
//								}
//								result.add(vbean);
//							}
//						}
//						System.out.println(i1++);
//					}
//				}
//				if(StringUtils.isNotEmpty(result)) {
//
//					if(StringUtils.isNotEmpty(param.getOrgCode())) {//指定了机构代码
//						boolean readAllSubOrg = true;//获取全部子机构
//						if(param.getReadAllSubOrg()!=null) {
//							readAllSubOrg=param.getReadAllSubOrg().booleanValue();
//						}
//						List<String> orgCodeList = new ArrayList<String>();
//						if(readAllSubOrg) {//需要递归查询全部子机构
//							List<SysOrg> orgList = orgSev.getOrgList(param.getOrgCode());
//							if(StringUtils.isNotEmpty(orgList)) {//查找了指定机构和其全部子机构
//								for(SysOrg temp:orgList) {
//									orgCodeList.add(temp.getOrgcode());
//								}
//							}else{
//								orgCodeList.add(param.getOrgCode());
//							}
//						}else {
//							orgCodeList.add(param.getOrgCode());
//						}
//						List<EmployeeVo> keepList = new ArrayList<EmployeeVo>();
//						for(EmployeeVo temp:result) {
//							if(orgCodeList.contains(temp.getOrgcode())) {//要求人员必须包含在机构中
//								keepList.add(temp);
//							}
//						}		
//						result=keepList;
//					}					
//				}			
//				Collections.sort(result, new Comparator<EmployeeVo>() {//根据人名进行排序
//					@Override
//					public int compare(EmployeeVo arg0, EmployeeVo arg1) {
////						System.out.println(arg0.getEmpname()+":"+arg1.getEmpname());
//						String arg0Py = PinYinUtils.ToPinYin2(arg0.getEmpname()==null?"":arg0.getEmpname());
//						String arg1Py = PinYinUtils.ToPinYin2(arg1.getEmpname()==null?"":arg1.getEmpname());
//						return arg0Py.compareTo(arg1Py);
//					}
//				});
//			}		
//		}
//		return result;
//	}

    /**
     * 根据指定权限获取人员信息
     *
     * @param param EmployeePermDto
     * @return
     * @category
     * <AUTHOR>
     */
    @Override
    public List<EmployeeVo> queryEmployeeByPerm(EmployeePermDto param) {
        // TODO Auto-generated method stub
        List<EmployeeVo> result = new ArrayList<EmployeeVo>();
        if (param != null && StringUtils.isNotEmpty(param.getPermission())) {
            List<String> permids = getPermIds(param.getPermission());//获取菜单对应的权限
            List<String> listUser = permUtils.getEmpIdByPermid(permids); //获取权限人员ID
            if (StringUtils.isNotEmpty(listUser)) {
                EmpParamDto paramDto = ObjUtils.copyTo(param, EmpParamDto.class);
                paramDto.setOrgcode(param.getOrgCode());//复制不一致的属性
                paramDto.setPremissions(param.getPermission());
                List<EmployeeVo> userList = iEmpBaseSev.getEmployeeByOrgcode(paramDto, null);
                if (StringUtils.isNotEmpty(userList)) {//指定了机构代码
                    Map<String, EmployeeVo> userMap = userList.stream().collect(Collectors.toMap(EmployeeVo::getEmpTmuid, EmployeeVo -> EmployeeVo, (key1, key2) -> key1));
                    if (StringUtils.isNotEmpty(listUser)) {
                        for (String temp : listUser) {
                            EmployeeVo vo = userMap.get(temp);
                            if (vo != null) {
                                result.add(vo);
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 根据菜单地址获取权限ID
     *
     * @param permission 菜单地址 例：prize:professionalAward:index?isld=1&iscompany=1
     * @return
     * @category
     * <AUTHOR>
     */
    private List<String> getPermIds(String permission) {
        //permission=",prize:professionalAward:index?isld=1&iscompany=1";
        List<String> result = new ArrayList<String>();
        if (StringUtils.isNotEmpty(permission)) {
            List<String> permissionList = new ArrayList<String>();
            if (permission.indexOf(",") >= 0) {
                String[] arr = permission.split(",");
                if (arr != null && arr.length != 0) {
                    for (String temp : arr) {
                        if (StringUtils.isNotEmpty(temp)) {
                            permissionList.add(temp);
                        }
                    }
                }
            } else {
                permissionList.add(permission);
            }
            Map<String, Map<String, Object>> mapMenu = menuService.getMenuMapFromRedis(Optional.ofNullable(SysUserHolder.getCurrentUser()).map(SysUser::getTenant_id).orElse(null));//获取系统菜单
            if (StringUtils.isNotEmpty(mapMenu)) {
                for (Entry<String, Map<String, Object>> temp : mapMenu.entrySet()) {
                    if ("1".equals(String.valueOf(temp.getValue().get("visible"))) && "0".equals(String.valueOf(temp.getValue().get("status")))) {//正在使用的菜单
                        String path = String.valueOf(temp.getValue().get("path"));
                        if (StringUtils.isEmpty(path) || "null".equals(path)) {
                            Map<String, Object> pDate = mapMenu.get(String.valueOf(temp.getValue().get("pid")));
                            if (pDate != null) {
                                path = String.valueOf(pDate.get("path"));
                            }
                        }
                        if (StringUtils.isNotEmpty(path) && !"null".equals(path)) {
                            path = path.substring(1).replace("/", ":");//替换成权限模式
                            String perms = String.valueOf(temp.getValue().get("perms"));
                            if (StringUtils.isNotEmpty(perms) && !"null".equals(perms)) {
                                path = path + ":" + perms;//追加按钮权限
                            }
                            if (permissionList.contains(path)) {
                                result.add(temp.getKey());
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    @Override
    public Integer getStaffBitNum() {
        String staffBitNumStr = sysConfigService.getSysConfig("staffBitNum");
        Integer staffBitNum = null;
        try {
            staffBitNum = Integer.valueOf(staffBitNumStr);
        }catch (Exception e){
            staffBitNum = 0;
        }
        return staffBitNum;
    }
    /**
         * 根据工号获取人员
     * TODO
     * @see com.yunhesoft.system.employee.service.ISysEmployeeInfoService#findEmployeeByStaffNo(java.lang.String)
     */
	@Override
	public SysEmployeeInfo findEmployeeByStaffNo(String staffNo) {
		// TODO Auto-generated method stub
		SysEmployeeInfo result = null;
		if (StringUtils.isNotEmpty(staffNo)) {
			Where where = Where.create();
			where.eq(SysEmployeeInfo::getUsed, 1);
			where.eq(SysEmployeeInfo::getStaffNo, staffNo);
			List<SysEmployeeInfo> queryList = entityService.queryList(SysEmployeeInfo.class, where);
			if (StringUtils.isNotEmpty(queryList)) {
				result = queryList.get(0);
			}
		}
		return result;
	}
    /**
	  *获取兼岗指定信息(只取外委人员)
	 * @category 获取兼岗指定信息(只取外委人员)
	 * <AUTHOR> 
	 * @param orgCode 机构代码
	 * @param postId  岗位id
	 * @param hasSubOrg 是否包含指定机构的子机构
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
	 */
	@Override
	public List<EmployeeVo> getPartTimePost(String orgCode, String postId, boolean hasSubOrg) {
		// TODO Auto-generated method stub
		List<EmployeeVo> result = null;
		if (StringUtils.isNotEmpty(postId)) {
			List<String> postIdList = new ArrayList<String>();
			postIdList.add(postId);
			result = this.getPartTimePost(orgCode, postIdList, hasSubOrg);
		}
		return result;
	}
    /**
	  * 获取兼岗指定信息(只取外委人员)
	 * @category 获取兼岗指定信息(只取外委人员)
	 * <AUTHOR> 
	 * @param orgCode 机构代码
	 * @param postIdList  岗位id列表
	 * @param hasSubOrg 是否包含指定机构的子机构
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
	 */
	@Override
	public List<EmployeeVo> getPartTimePost(String orgCode, List<String> postIdList, boolean hasSubOrg) {
		// TODO Auto-generated method stub
		return this.getPartTimePost(orgCode, postIdList, hasSubOrg, ISysEmployeeInfoService.staffType_4);
	}
    /**
	  * 获取人员兼岗(只取外委人员)
	 * @category 获取人员兼岗(只取外委人员)
	 * <AUTHOR> 
	 * @param userId
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
	 */
	@Override
	public List<EmployeeVo> getUserPartTimePost(String userId) {
		// TODO Auto-generated method stub
		return this.getUserPartTimePost(userId,ISysEmployeeInfoService.staffType_4);
	}
    /**
	  * 获取兼岗指定信息
	 * @category 获取兼岗指定信息
	 * <AUTHOR> 
	 * @param orgCode 机构代码
	 * @param postId 岗位id
	 * @param hasSubOrg 是否包含指定机构的子机构
	 * @param staffType 员工类型  1全职 2兼职 3实习 4外委 5其他
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
	 */
	@Override
	public List<EmployeeVo> getPartTimePost(String orgCode, String postId, boolean hasSubOrg, Integer staffType) {
		// TODO Auto-generated method stub
		List<EmployeeVo> result = null;
		if (StringUtils.isNotEmpty(postId)) {
			List<String> postIdList = new ArrayList<String>();
			postIdList.add(postId);
			result = this.getPartTimePost(orgCode, postIdList, hasSubOrg,staffType);
		}
		return result;
	}
    /**
	  * 获取兼岗指定信息
	 * @category 获取兼岗指定信息
	 * <AUTHOR> 
	 * @param orgCode 机构代码
	 * @param postIdList 岗位id列表
	 * @param hasSubOrg 是否包含指定机构的子机构
	 * @param staffType 员工类型  1全职 2兼职 3实习 4外委 5其他
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
	 */
	@Override
	public List<EmployeeVo> getPartTimePost(String orgCode, List<String> postIdList, boolean hasSubOrg,
			Integer staffType) {
		// TODO Auto-generated method stub
		List<EmployeeVo> result = new ArrayList<EmployeeVo>();
		if (StringUtils.isNotEmpty(postIdList)) {
			Where where = Where.create();
			where.eq(SysEmployeeOrgPost::getUsed, 1);
			where.eq(SysEmployeeOrgPost::getStatus, 2);//2为兼岗
			if(StringUtils.isNotEmpty(orgCode)) {//指定了机构代码
				if(hasSubOrg) {
					List<SysOrg> orgList = orgSev.getOrgList(orgCode);
					if (StringUtils.isNotEmpty(orgList)) {//查找了指定机构和其全部子机构
						List<String> orgCodeList = new ArrayList<String>();
						for (SysOrg temp : orgList) {
							orgCodeList.add(temp.getOrgcode());
						}
						where.in(SysEmployeeOrgPost::getOrgcode, orgCodeList.toArray());
					}else {
						where.eq(SysEmployeeOrgPost::getOrgcode, orgCode);
					}
				}else {
					where.eq(SysEmployeeOrgPost::getOrgcode, orgCode);
				}			
			}
			where.in(SysEmployeeOrgPost::getPostid, postIdList.toArray());
			List<SysEmployeeOrgPost> queryList = entityService.queryList(SysEmployeeOrgPost.class, where);
			if (StringUtils.isNotEmpty(queryList)) {
				HashSet<String> userSet = new HashSet<String>();//人员id列表
				for(SysEmployeeOrgPost temp:queryList) {
					if (StringUtils.isNotEmpty(temp.getEmpid())) {
						userSet.add(temp.getEmpid());
					}
				}
				if(userSet.size()>0) {
					List<String> empidList = new ArrayList<String>();
					empidList.addAll(userSet);
					Map<String, SysEmployeeInfo> userMap = getEmployeeMap(empidList);//缓存中获取人员列表
					if(userMap!=null) {
						if(staffType!=null) {
							Map<String, SysEmployeeInfo> keepMap = new HashMap<String, SysEmployeeInfo>();
							for(Entry<String, SysEmployeeInfo> temp:userMap.entrySet()) {
								if(temp.getValue().getStaffType()!=null && temp.getValue().getStaffType().intValue()==staffType.intValue()) {//指定人员类型
									keepMap.put(temp.getKey(), temp.getValue());//只保留指定人员类型
								}
							}
							userMap = keepMap;
						}
						if(userMap.size()>0) {//有人员信息
							for(SysEmployeeOrgPost temp:queryList) {
								SysEmployeeInfo info = userMap.get(temp.getEmpid());
								if(info!=null) {
									EmployeeVo bean = createEmployeeVo(info,temp);
									result.add(bean);
								}
							}
						}
					}
				}
			}
		}
		return result;
	}
	/**
	  * 获取人员兼岗
	 * @category 获取人员兼岗
	 * <AUTHOR> 
	 * @param userId 人员ID
	 * @param staffType 员工类型  1全职 2兼职 3实习 4外委 5其他
	 * @return EmployeeVo orgcode=兼岗设置的机构代码 postId=兼岗设置的岗位ID 一个人如果兼多岗，则返回多条记录
	 */
	@Override
	public List<EmployeeVo> getUserPartTimePost(String userId, Integer staffType) {
		// TODO Auto-generated method stub
		List<EmployeeVo> result = new ArrayList<EmployeeVo>();
		if (StringUtils.isNotEmpty(userId)) {		
			SysEmployeeInfo info = findEmployeeById(userId);
			if(info!=null) {
				if(staffType!=null) {
					if(info.getStaffType()!=null && info.getStaffType().intValue()==staffType.intValue()) {//指定人员类型
						//类型一致，才查找岗位
					}else{
						info=null;//不符合人员类型，不查找
					}
				}
				if(info!=null) {
					Where where = Where.create();
					where.eq(SysEmployeeOrgPost::getUsed, 1);
					where.eq(SysEmployeeOrgPost::getStatus, 2);//2为兼岗
					where.eq(SysEmployeeOrgPost::getEmpid, userId);
					List<SysEmployeeOrgPost> queryList = entityService.queryList(SysEmployeeOrgPost.class, where);
					if (StringUtils.isNotEmpty(queryList)) {
						for(SysEmployeeOrgPost temp:queryList) {
							EmployeeVo bean = createEmployeeVo(info,temp);
							result.add(bean);
						}
					}
				}
			}
		}
		return result;
	}
	/**
	 * 生成兼岗人员信息
	 * @category 
	 * <AUTHOR> 
	 * @param info
	 * @param post
	 * @return
	 */
	private EmployeeVo createEmployeeVo(SysEmployeeInfo info,SysEmployeeOrgPost post) {
		EmployeeVo result = new EmployeeVo();
		result.setEmpTmuid(info.getId());
		result.setEmpname(info.getEmpname());
		result.setStaffNo(info.getStaffNo());
		result.setCardno(info.getCardno());
		result.setStaffType(info.getStaffType());
		result.setOrgcode(post.getOrgcode());
		result.setOrgTmuid(result.getOrgcode());
		result.setPostid(post.getPostid());
		result.setPostTmuid(result.getPostid());
		return result;
	}

}