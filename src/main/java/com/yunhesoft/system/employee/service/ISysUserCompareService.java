package com.yunhesoft.system.employee.service;

import java.util.List;

import com.yunhesoft.system.employee.entity.po.SysUserCompare;

/**
 * 人员主键
 * 
 * <AUTHOR>
 *
 */
public interface ISysUserCompareService {

	/**
	 * 查询
	 * 
	 * @param targetOrgUserId TM4人员ID
	 * @return
	 */
	List<SysUserCompare> listData(String targetOrgUserId);

	/**
	 * 保存
	 * 
	 * @param list 人员对照表类
	 * @return
	 */
	boolean saveData(List<SysUserCompare> list);

	/**
	 * 更新
	 * 
	 * @param list 人员对照表类
	 * @return
	 */
	boolean updateData(List<SysUserCompare> list);

	/**
	 * 插入
	 * 
	 * @param list 人员对照表
	 * @return
	 */
	boolean insertData(List<SysUserCompare> list);

	/**
	 * 删除
	 * 
	 * @param id 人员对照表ID
	 * @return
	 */
	boolean deleteData(String id);
}
