package com.yunhesoft.system.employee.service.impl;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.PinYinUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.auth.entity.dto.LoginUserDto;
import com.yunhesoft.system.auth.entity.po.SysLoginUser;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.dataperm.service.ISysDataPermService;
import com.yunhesoft.system.employee.entity.dto.EmpInitLoginDto;
import com.yunhesoft.system.employee.entity.dto.EmpOrgParamDto;
import com.yunhesoft.system.employee.entity.dto.EmpOrgPostParamDto;
import com.yunhesoft.system.employee.entity.dto.EmpParamDto;
import com.yunhesoft.system.employee.entity.dto.EmpPartimePostParamDto;
import com.yunhesoft.system.employee.entity.dto.EmpTransferParamDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeOrgPostDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeChangeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeChangeTodo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.employee.entity.po.SysEmployeeStation;
import com.yunhesoft.system.employee.entity.vo.EmployeeDiaryVo;
import com.yunhesoft.system.employee.entity.vo.EmployeeOrgPostVo;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.IEmployeeBasicOperationService;
import com.yunhesoft.system.employee.service.IEmployeeExtraOperationService;
import com.yunhesoft.system.employee.service.ISysEmployeeChangeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgPostService;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgService;
import com.yunhesoft.system.employee.utils.DateUtil;
import com.yunhesoft.system.employee.utils.MapUtil;
import com.yunhesoft.system.employee.utils.PageUtil;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.DBTypeUtils;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.org.service.ISysOrgStationService;
import com.yunhesoft.system.post.entity.dto.PostParamDto;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.post.entity.vo.PostVo;
import com.yunhesoft.system.post.service.IPostBasicOperationService;
import com.yunhesoft.system.post.service.ISysDiyPost;
import com.yunhesoft.system.post.service.ISysPostService;
import com.yunhesoft.system.role.entity.dto.UserRoleDto;
import com.yunhesoft.system.role.entity.po.SysRole;
import com.yunhesoft.system.role.entity.po.SysUserRole;
import com.yunhesoft.system.role.entity.vo.RoleTree;
import com.yunhesoft.system.role.service.ISysRoleService;
import com.yunhesoft.system.role.service.ISysUserPermService;
import com.yunhesoft.system.role.service.ISysUserRoleService;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;
import com.yunhesoft.system.tools.todo.service.impl.TodoServiceImpl;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
public class EmployeeBasicOperationImpl implements IEmployeeBasicOperationService {
    /**
     * 内部服务接口 -----------------------------------
     */
    @Autowired
    private EntityService dao;
    /**
     * 人员信息表操作服务接口
     */
    @Autowired
    private ISysEmployeeInfoService empInfoServ;
    /**
     * 人员机构信息表操作服务接口
     */
    @Autowired
    private ISysEmployeeOrgService empOrgServ;
    /**
     * 人员机构岗位信息表操作服务接口
     */
    @Autowired
    private ISysEmployeeOrgPostService empPostServ;
    /**
     * 人员角色信息表操作服务接口
     */
    @Autowired
    private ISysUserRoleService userRoleServ;
    /**
     * 岗位操作服务接口
     */
    @Autowired
    private IPostBasicOperationService postServ;

    @Autowired
    private ISysPostService postService;
    /**
     * 登录信息服务接口
     */
    @Autowired
    private AuthService loginServ;

    @Autowired
    private ISysOrgService orgServ;

    /**
     * 外部模块服务接口
     */
    @Autowired
    private IEmployeeExtraOperationService extOperServ;

    @Autowired
    private ISysConfigService sysConfigServ;

    /**
     * 个人权限服务接口
     */
    @Autowired
    private ISysUserPermService userPermServ;
    @Autowired
    private ISysDiyPost sysDiyPost;

    //	/** 人员变动待办表服务接口 */
//	@Autowired
//	private ISysEmployeeChangeTodoService empChgTodoServ;
//	/** 人员变动流水表服务接口 */
    @Autowired
    private ISysEmployeeChangeInfoService empChgInfoServ;

    @Autowired
    private EntityService entityService;

    @Autowired
    private ISysRoleService sysRoleService; // 角色信息

    @Autowired
    private AuthService authServ;

    @Autowired
    private ISysDataPermService sysDataPermServ;// 数据权限

    @Autowired
    private TodoServiceImpl todoService;// 待办信息服务

    @Autowired
    private RedisUtil redis;

    @Autowired
    private ISysOrgStationService stationSrv;//工位服务

    /**
     * 上限日期
     */
    private String g_max_date = "9999-01-01";
    private String g_max_datetime = "9999-01-01 00:00:00";

    /** 错误编码 */
    // private int errCode = 509;

    /**
     * 批量添加人员信息
     *
     * @param listDto
     * @return
     * @category 批量添加人员信息
     */
    @Override
    public String addEmployee(List<EmployeeDto> listDto) {
        String err = "";
        err = addEmployee(listDto, false);
        return err;
    }

    /**
     * 判断工号是否重复
     *
     * @param staffNode 工号
     * @param empid     人员id
     * @return
     */
    private long queryStaffNoCount(String staffNo, String empid) {
        int count = 0;
        if (StringUtils.isNotEmpty(staffNo)) {
            Where where = Where.create();
            where.eq(SysEmployeeInfo::getStaffNo, staffNo);
            if (StringUtils.isNotEmpty(empid)) {
                where.ne(SysEmployeeInfo::getId, empid);
            }
            where.ne(SysEmployeeInfo::getUsed, 0);//只判断生效的
            Long lng = entityService.queryCount(SysEmployeeInfo.class, where);
            if (lng != null) {
                return lng;
            }
        }
        return count;
    }

    private String staffNoRepeat(String staffNo, String empid) {
        String result = null;
        if (StringUtils.isNotEmpty(staffNo)) {
            Where where = Where.create();
            where.eq(SysEmployeeInfo::getStaffNo, staffNo);
            if (StringUtils.isNotEmpty(empid)) {
                where.ne(SysEmployeeInfo::getId, empid);
            }
            where.ne(SysEmployeeInfo::getUsed, 0);//只判断生效的
            List<SysEmployeeInfo> list = entityService.rawQueryListByWhere(SysEmployeeInfo.class, where);
            if (StringUtils.isNotEmpty(list)) {
                //根据人员查询其岗位等
                List<String> empidList = new ArrayList<>();
                SysEmployeeInfo sysEmployeeInfo = list.get(0);
                empidList.add(sysEmployeeInfo.getId());
                List<SysEmployeeOrgPost> employeeOrgPost = empPostServ.getEmployeeOrgPost(empidList);
                if (StringUtils.isNotEmpty(employeeOrgPost)) {
                    String postName = "";
                    String orgName = "";
                    String postid = employeeOrgPost.get(0).getPostid();
                    if (StringUtils.isNotEmpty(postid)) {
                        PostParamDto postParamDto = new PostParamDto();
                        postParamDto.setId(postid);
                        List<SysPost> post = postService.getPost(postParamDto);
                        if (StringUtils.isNotEmpty(post)) {
                            postName = post.get(0).getName();
                        }
                    }
                    String orgcode = employeeOrgPost.get(0).getOrgcode();
                    SysOrg orgById = orgServ.findOrgById(orgcode);
                    if (orgById != null) {
                        orgName = orgById.getOrgname();
                    }
                    String status = "";
                    if (sysEmployeeInfo.getUsed() == -1) {
                        status = "离职";
                    } else if (sysEmployeeInfo.getUsed() == -2) {
                        status = "退休";
                    } else if (sysEmployeeInfo.getUsed() == 1) {
                        status = "在职";
                    }
                    result = "相同工号的人" + (StringUtils.isNotEmpty(orgName) ? ("在【" + orgName + "】部门") : "")
                            + (StringUtils.isNotEmpty(postName) ? ("在【" + postName + "】岗位，") : "") + "处于【" + status + "】状态,请去人员管理中取消离职";

                }
            }
        }
        return result;
    }

    /**
     * 创建人员信息
     *
     * @param listDto
     * @param sync    true:同步时调用，false：人员页面调用
     * @return
     */
    @Override
    public String addEmployee(List<EmployeeDto> listDto, boolean sync) {
        String err = "";
        try {
            if (listDto == null) {
                return "添加的人员信息为空";
            }
            String tenantId = "";// 租户id
            tenantId = listDto.get(0).getTenantId();

            List<SysEmployeeInfo> empListPoInsert = new ArrayList<SysEmployeeInfo>();
            List<SysEmployeeInfo> empListPoUpdate = new ArrayList<SysEmployeeInfo>();
            List<SysEmployeeOrg> empOrgListPoInsert = new ArrayList<SysEmployeeOrg>();
//			List<SysEmployeeOrg> empOrgListPoUpdate = new ArrayList<SysEmployeeOrg>();
            List<SysEmployeeOrgPost> empOrgPostListPoIn = new ArrayList<SysEmployeeOrgPost>();
//			List<SysEmployeeOrgPost> empOrgPostListPoUp = new ArrayList<SysEmployeeOrgPost>();
            List<SysUserRole> empRoleListPo = new ArrayList<SysUserRole>();
            List<SysEmployeeStation> stationList = new ArrayList<SysEmployeeStation>();
            List<String> empidList = new ArrayList<String>();
//			List<String> staffNoList = new ArrayList<String>();//不判断批量的重复工号防止导入丢失数据
            for (EmployeeDto bDto : listDto) {
                SysEmployeeInfo empPo = new SysEmployeeInfo();// 人员信息
                BeanUtils.copyProperties(bDto, empPo);
                if (!sync) {// 非同步模式，判断工号是否重复
                    String repeat = this.staffNoRepeat(empPo.getStaffNo(), null);
                    if (StringUtils.isNotEmpty(repeat)) { // 工号重复 staffNoList.contains(empPo.getStaffNo()) ||
                        err += "[" + empPo.getEmpname() + "]工号重复，原因：" + repeat;
                        continue;
                    } else {
//						staffNoList.add(empPo.getStaffNo());//判断一起导入的数据是否有工号重复的
                    }
                }

                String empName = empPo.getEmpname();
                if (StringUtils.isNotEmpty(empName)) {
                    empName.replaceAll(".", "·");
                }
                empPo.setEmpname(empName);
                empPo.setUsed(1);
                empPo.setCreateTime(new Date());
                if (empPo.getStatus() == null) {//添加时如果没传状态，则默认为在职
                    empPo.setStatus(1);
                }
//				empPo.setTenant_id(bDto.getTenantId());//====添加租户id
                if (StringUtils.isEmpty(bDto.getEmpTmuid())) {
                    empPo.setId(TMUID.getUID());
                    bDto.setEmpTmuid(empPo.getId());
                    empListPoInsert.add(empPo);
                } else {
                    empPo.setId(bDto.getEmpTmuid());
                    empListPoUpdate.add(empPo);
                }

                // 人员机构信息
                if (!sync && bDto.getOrgcode() != null && !"".equals(bDto.getOrgcode())) {
                    SysEmployeeOrg empOrgPo = new SysEmployeeOrg();
                    BeanUtils.copyProperties(bDto, empOrgPo);
                    empOrgPo.setId(TMUID.getUID());
                    empOrgPo.setEmpid(empPo.getId());
                    empOrgPo.setStatus(bDto.getOrgStatus());
                    empOrgPo.setTmSort(bDto.getOrgSort());
                    empOrgPo.setUsed(1);
//					empOrgPo.setTenant_id(bDto.getTenantId());//====添加租户id
                    empOrgListPoInsert.add(empOrgPo);
                }
                // 人员岗位信息
                if (!sync && bDto.getPostid() != null && !"".equals(bDto.getPostid())) {
                    SysEmployeeOrgPost empOrgPostPo = new SysEmployeeOrgPost();
                    BeanUtils.copyProperties(bDto, empOrgPostPo);
                    empOrgPostPo.setId(TMUID.getUID());
                    empOrgPostPo.setEmpid(empPo.getId());
                    empOrgPostPo.setStatus(bDto.getPostStatus());
                    empOrgPostPo.setTmSort(bDto.getPostSort());
                    empOrgPostPo.setUsed(1);
//					empOrgPostPo.setTenant_id(bDto.getTenantId());//====添加租户id
                    empOrgPostListPoIn.add(empOrgPostPo);
                }
                // 人员兼岗信息
                if (StringUtils.isNotEmpty(bDto.getPartTimePostId())) {
                    String partTimePostId = bDto.getPartTimePostId();
                    partTimePostId = partTimePostId.replaceAll("\\[", "");
                    partTimePostId = partTimePostId.replaceAll("\\]", "");
                    partTimePostId = partTimePostId.replaceAll("\"", "");
                    String[] ids = partTimePostId.split(",");

                    empPostServ.deleteBatchByUserId(bDto.getEmpTmuid(), 2);

                    for (String id : ids) {
                        // 新增兼岗信息
                        SysEmployeeOrgPost empOrgPostPo = new SysEmployeeOrgPost();
//						BeanUtils.copyProperties(bDto, empOrgPostPo);
                        String[] orgAndPostId = id.split("_");
                        if (orgAndPostId.length == 2) {
                            empOrgPostPo.setPostid(orgAndPostId[1]);
                            empOrgPostPo.setOrgcode(orgAndPostId[0]);

                            empOrgPostPo.setId(TMUID.getUID());
                            empOrgPostPo.setEmpid(empPo.getId());
                            empOrgPostPo.setStatus(2);
//							empOrgPostPo.setTmSort(bDto.getPostSort());
                            empOrgPostPo.setUsed(1);
//							empOrgPostPo.setTenant_id(bDto.getTenantId());//====添加租户id
                            empOrgPostListPoIn.add(empOrgPostPo);
                        }
                    }
                }
                // 人员角色信息
                if (!sync && bDto.getRoleid() != null && !"".equals(bDto.getRoleid())) {
                    empRoleListPo.addAll(this.convertUserRole(empPo.getId(), bDto.getRoleid()));
                }
                // 人员工位信息
                if (!sync && StringUtils.isNotEmpty(bDto.getStationCode()) && StringUtils.isNotEmpty(bDto.getStationName())) {
                    empidList.add(empPo.getId());
                    List<String> codelist = Coms.StrToList(bDto.getStationCode(), ",");
                    List<String> namelist = Coms.StrToList(bDto.getStationName(), ",");
                    for (int i = 0, il = codelist.size(); i < il; i++) {
                        String code = codelist.get(i);
                        String name = "";
                        if (namelist.size() > i) {
                            name = namelist.get(i);
                        }
                        SysEmployeeStation sta = new SysEmployeeStation();
                        sta.setEmpid(empPo.getId());
                        sta.setStationCode(code);
                        sta.setStationName(name);
                        stationList.add(sta);
                    }

                }
            }
            // 租户id 不为空
            if (tenantId != null && !"".equals(tenantId)) {// ====注册用，仅注册时才传入租户id，其他时间不会传入====
                int flag = 0;
                if (empListPoInsert.size() > 0) {
                    // 保存数据到数据库
                    try {
                        dao.rawInsertBatchWithTenant(tenantId, empListPoInsert);
                    } catch (Exception ex) {
                        return "保存人员信息到数据库失败";
                    }
                }
                if (empListPoUpdate.size() > 0) {
                    // 更新数据到数据库
                    flag = dao.updateByIdBatch(empListPoUpdate);
                    if (flag <= 0) {
                        return "保存人员信息到数据库失败";
                    }
                }

                if (empOrgListPoInsert.size() > 0) {
                    try {
                        dao.rawInsertBatchWithTenant(tenantId, empOrgListPoInsert);
                    } catch (Exception ex) {
                        return "保存人员机构信息到数据库失败";
                    }
                }
                if (empOrgPostListPoIn.size() > 0) {
                    try {
                        dao.rawInsertBatchWithTenant(tenantId, empOrgPostListPoIn);
                    } catch (Exception ex) {
                        return "保存人员机构岗位信息到数据库失败";
                    }
                }
                if (empRoleListPo.size() > 0) {
                    userRoleServ.addBatchWithTenant(empRoleListPo, tenantId);
                }
                if (empidList.size() > 0) {
                    try {
                        stationSrv.saveEmpStation(empidList, stationList);
                    } catch (Exception e) {
                        return "保存人员工位信息到数据库失败";
                    }
                }
            } else {
                // 保存数据到数据库
                boolean flag = empInfoServ.addBatch(empListPoInsert);
                if (flag == false) {
                    return "保存人员信息到数据库失败";
                }

                flag = empInfoServ.addBatch(empListPoUpdate);
                if (flag == false) {
                    return "保存人员信息到数据库失败";
                }
                if (empOrgListPoInsert.size() > 0) {
                    flag = empOrgServ.addBatch(empOrgListPoInsert);
                    if (flag == false) {
                        return "保存人员机构信息到数据库失败";
                    }
                }
                if (empOrgPostListPoIn.size() > 0) {
                    flag = empPostServ.addBatch(empOrgPostListPoIn);
                    if (flag == false) {
                        return "保存人员机构岗位信息到数据库失败";
                    }
                }
                String changeDt = getChangeDt(listDto.get(0).getChangeDt());// 获取变动日期
                // 机构变动
                flag = empChgInfoServ.saveUserOrgChange(changeDt, empOrgListPoInsert);
                if (flag == false) {
                    return "保存人员机构变动信息到数据库失败";
                }

                // 岗位变动
                flag = empChgInfoServ.saveUserOrgPostChange(changeDt, empOrgPostListPoIn);
                if (flag == false) {
                    return "保存人员岗位变动信息到数据库失败";
                }
                if (empRoleListPo.size() > 0) {
                    userRoleServ.addBatch(empRoleListPo);
                }
                if (empidList.size() > 0) {
                    try {
                        stationSrv.saveEmpStation(empidList, stationList);
                    } catch (Exception e) {
                        return "保存人员工位信息到数据库失败";
                    }
                }
            }
        } catch (Exception e) {
            log.error("", e);
            err = "后台处理有误";
        }

        return err;
    }

    private List<SysUserRole> convertUserRole(String userid, String roleid) {
        List<SysUserRole> list = new ArrayList<SysUserRole>();
        roleid = roleid.replaceAll("\\[", "");
        roleid = roleid.replaceAll("\\]", "");
        roleid = roleid.replaceAll("\"", "");
        String[] ary = roleid.split(",");
        for (String string : ary) {
            SysUserRole bean = new SysUserRole();
            bean.setId(TMUID.getUID());
            bean.setRoleid(string);
            bean.setUserid(userid);
//			bean.setTenant_id(tenant_id);
            list.add(bean);
        }
        return list;
    }

    /**
     * 批量删除人员信息
     *
     * @param listDto
     * @return
     * @category 批量删除人员信息
     */
    @Override
    public String delEmployee(List<EmployeeDto> listDto) {
        String err = "";

        try {
            if (listDto == null) {
                return "删除的人员信息为空";
            }

            List<SysEmployeeInfo> empListPo = new ArrayList<SysEmployeeInfo>();
            List<SysEmployeeOrg> empOrgListPo = new ArrayList<SysEmployeeOrg>();
            List<SysEmployeeOrgPost> empOrgPostListPo = new ArrayList<SysEmployeeOrgPost>();
            // List<SysUserRole> empRoleListPo = new ArrayList<SysUserRole>();
            List<String> listEmpid = new ArrayList<String>();
            List<String> listEmpid1 = new ArrayList<String>();
            for (EmployeeDto bDto : listDto) {
                // 人员信息
                if (bDto.getEmpTmuid() == null || "".equals(bDto.getEmpTmuid())) {
                    continue;
                }
                SysEmployeeInfo empPo = new SysEmployeeInfo();
                BeanUtils.copyProperties(bDto, empPo);
                empPo.setId(bDto.getEmpTmuid());
                empPo.setUsed(0);
                empListPo.add(empPo);
                // 人员机构信息
                if (bDto.getOrgTmuid() != null && !"".equals(bDto.getOrgTmuid())) {
                    SysEmployeeOrg empOrgPo = new SysEmployeeOrg();
                    BeanUtils.copyProperties(bDto, empOrgPo);
                    empOrgPo.setId(bDto.getOrgTmuid());
                    empOrgPo.setEmpid(empPo.getId());
                    empOrgPo.setStatus(bDto.getOrgStatus());
                    empOrgPo.setTmSort(bDto.getOrgSort());
                    empOrgPo.setUsed(0);
                    empOrgListPo.add(empOrgPo);
                }
                // 人员岗位信息
                if (bDto.getPostTmuid() != null && !"".equals(bDto.getPostTmuid())) {
                    SysEmployeeOrgPost empOrgPostPo = new SysEmployeeOrgPost();
                    BeanUtils.copyProperties(bDto, empOrgPostPo);
                    empOrgPostPo.setId(bDto.getPostTmuid());
                    empOrgPostPo.setEmpid(empPo.getId());
                    empOrgPostPo.setStatus(bDto.getPostStatus());
                    empOrgPostPo.setTmSort(bDto.getPostSort());
                    empOrgPostPo.setUsed(0);
                    empOrgPostListPo.add(empOrgPostPo);
                }

                // 人员角色信息
                if (StringUtils.isNotEmpty(bDto.getRoleid())) {
                    listEmpid.add(empPo.getId());
                }
                listEmpid1.add(empPo.getId());
            }

            // 保存数据到数据库
            boolean flag = empInfoServ.updateBatchByIds(empListPo);
            if (flag == false) {
                return "保存人员信息到数据库失败";
            }

            flag = empOrgServ.updateBatchByIds(empOrgListPo);
            if (flag == false) {
                return "保存人员机构信息到数据库失败";
            }

            flag = empPostServ.updateBatchByIds(empOrgPostListPo);
            if (flag == false) {
                return "保存人员机构岗位信息到数据库失败";
            }

            if (StringUtils.isNotEmpty(listEmpid)) {
                flag = userRoleServ.deleteUserRoleByUserIds(listEmpid); // empRoleServ.deleteBatchById(empRoleListPo);
                if (flag == false) {
                    return "保存人员角色信息到数据库失败";
                }
            }

            String changeDt = getChangeDt(listDto.get(0).getChangeDt());// 获取变动日期
            // 机构变动
            List<SysEmployeeOrg> changeOrgList = new ArrayList<SysEmployeeOrg>();
            changeOrgList.addAll(empOrgListPo);
            flag = empChgInfoServ.saveUserOrgChange(changeDt, changeOrgList);
            if (flag == false) {
                return "保存人员机构变动信息到数据库失败";
            }

            // 岗位变动
            List<SysEmployeeOrgPost> changePostList = new ArrayList<SysEmployeeOrgPost>();
            changePostList.addAll(empOrgPostListPo);
            flag = empChgInfoServ.saveUserOrgPostChange(changeDt, changePostList);
            if (flag == false) {
                return "保存人员岗位变动信息到数据库失败";
            }

            if (StringUtils.isNotEmpty(listEmpid1)) {
                userPermServ.deleteByUserIds(listEmpid1);// 删除个人权限信息
                loginServ.deleteLoginByUserId(listEmpid1);// 删除登录信息
                sysDataPermServ.deleteDataPerm(listEmpid1);// 删除数据权限
                this.clearEmployeeDataByIds(listEmpid1);// 清除缓存
                stationSrv.saveEmpStation(listEmpid1, null);//删除个人工位
            }
        } catch (Exception e) {
            log.error("", e);
        }

        return err;
    }

    /**
     * 批量修改人员信息
     *
     * @param listDto
     * @return
     * @category 批量修改人员信息
     */
    @Override
    public String updEmployee(List<EmployeeDto> listDto) {
        String err = "";

        try {
            if (listDto == null) {
                return "修改的人员信息为空";
            }
            List<SysEmployeeInfo> empListPo = new ArrayList<SysEmployeeInfo>();
            List<SysEmployeeOrg> updEmpOrgListPo = new ArrayList<SysEmployeeOrg>();
            List<SysEmployeeOrg> addEmpOrgListPo = new ArrayList<SysEmployeeOrg>();
            List<SysEmployeeOrgPost> updEmpOrgPostListPo = new ArrayList<SysEmployeeOrgPost>();
            List<SysEmployeeOrgPost> addEmpOrgPostListPo = new ArrayList<SysEmployeeOrgPost>();
            List<SysEmployeeOrg> upEmpUsed = new ArrayList<>();
            List<String> empidList = listDto.stream().map(EmployeeDto::getEmpTmuid).collect(Collectors.toList());
            Map<String, SysEmployeeOrgPost> orgPostMap = empPostServ.getEmployeeOrgPostMap(empidList);
//			Map<String, SysEmployeeOrgPost> orgPartPostMap = empPostServ.getEmployeeOrgPartPostMap(empidList);
            List<SysEmployeeStation> stationList = new ArrayList<SysEmployeeStation>();
            for (EmployeeDto bDto : listDto) {
                // 人员信息
                if (bDto.getEmpTmuid() == null || "".equals(bDto.getEmpTmuid())) {
                    continue;
                }
                SysEmployeeInfo empPo = new SysEmployeeInfo();
                BeanUtils.copyProperties(bDto, empPo);
                empPo.setId(bDto.getEmpTmuid());
                empPo.setUpdateTime(new Date());
                String empName = empPo.getEmpname();
                if (StringUtils.isNotEmpty(empName)) {
                    empName.replaceAll(".", "·");
                }
                empPo.setEmpname(empName);
                if (this.queryStaffNoCount(empPo.getStaffNo(), empPo.getId()) > 0) { // 工号重复
                    err += "[" + empPo.getEmpname() + "]工号重复 ";
                    continue;
                }
                empListPo.add(empPo);
                // 人员机构信息
                String empOrgcode = "";
                SysEmployeeOrg empOrgPo = new SysEmployeeOrg();
                BeanUtils.copyProperties(bDto, empOrgPo);
                empOrgPo.setEmpid(empPo.getId());
                empOrgPo.setStatus(bDto.getOrgStatus());
                empOrgPo.setTmSort(bDto.getOrgSort());
                empOrgPo.setUsed(empPo.getUsed());
                empOrgcode = empOrgPo.getOrgcode();
                if ((bDto.getOrgTmuid() != null && !"".equals(bDto.getOrgTmuid())) || empOrgPo.getUsed() <= 0) { // 修改机构
                    empOrgPo.setId(bDto.getOrgTmuid());
                    updEmpOrgListPo.add(empOrgPo);
                } else if ((bDto.getOrgcode() != null && !"".equals(bDto.getOrgcode())) || empOrgPo.getUsed() == 1) { // 新增机构
                    empOrgPo.setId(TMUID.getUID());
                    addEmpOrgListPo.add(empOrgPo);
                }
                if (StringUtils.isEmpty(updEmpOrgListPo) && StringUtils.isEmpty(addEmpOrgListPo)) {
                    upEmpUsed.add(empOrgPo);
                }
                // 人员岗位信息
                if (StringUtils.isNotEmpty(empOrgcode) || StringUtils.isNotEmpty(bDto.getPostid())) {
                    SysEmployeeOrgPost orgpostBean = orgPostMap.get(empPo.getId());
                    if (orgpostBean != null && orgpostBean.getStatus() == 1) {// 修改岗位
                        //
                        // SysEmployeeOrgPost empOrgPostPo = new SysEmployeeOrgPost();
                        // BeanUtils.copyProperties(bDto, empOrgPostPo);
                        // empOrgPostPo.setId(postTmuid);
                        // empOrgPostPo.setEmpid(empPo.getId());
                        orgpostBean.setStatus(1);
                        if (bDto.getPostSort() != null) {
                            orgpostBean.setTmSort(bDto.getPostSort());
                        }
                        if (StringUtils.isNotEmpty(empOrgcode)) {
                            orgpostBean.setOrgcode(empOrgcode);
                        }
                        orgpostBean.setPostid(bDto.getPostid());
                        orgpostBean.setUsed(bDto.getUsed());
                        updEmpOrgPostListPo.add(orgpostBean);
                    } else {// 新增岗位
                        SysEmployeeOrgPost empOrgPostPo = new SysEmployeeOrgPost();
                        BeanUtils.copyProperties(bDto, empOrgPostPo);
                        empOrgPostPo.setId(TMUID.getUID());
                        empOrgPostPo.setEmpid(empPo.getId());
                        empOrgPostPo.setStatus(1);
                        empOrgPostPo.setTmSort(bDto.getPostSort());
                        empOrgPostPo.setUsed(empPo.getUsed());
                        addEmpOrgPostListPo.add(empOrgPostPo);
                    }
                }

                // 人员兼岗信息

                if (StringUtils.isNotEmpty(bDto.getPartTimePostId())) {
                    String partTimePostId = bDto.getPartTimePostId();
//					List<SysUserRole> partTimePostIdList = new ArrayList<SysUserRole>();
                    partTimePostId = partTimePostId.replaceAll("\\[", "");
                    partTimePostId = partTimePostId.replaceAll("\\]", "");
                    partTimePostId = partTimePostId.replaceAll("\"", "");
                    String[] ids = partTimePostId.split(",");

                    empPostServ.deleteBatchByUserId(bDto.getEmpTmuid(), 2);

                    for (String id : ids) {
//						SysEmployeeOrgPost orgpostBean = orgPartPostMap.get(empPo.getId());
//						if (orgpostBean != null && orgpostBean.getStatus() == 2) {// 修改岗位
//							//
//							// SysEmployeeOrgPost empOrgPostPo = new SysEmployeeOrgPost();
//							// BeanUtils.copyProperties(bDto, empOrgPostPo);
//							// empOrgPostPo.setId(postTmuid);
//							// empOrgPostPo.setEmpid(empPo.getId());
//							orgpostBean.setStatus(2);
//							if (bDto.getPostSort() != null) {
//								orgpostBean.setTmSort(bDto.getPostSort());
//							}
//							if (StringUtils.isNotEmpty(empOrgcode)) {
//								orgpostBean.setOrgcode(empOrgcode);
//							}
//							orgpostBean.setPostid(id);
//							orgpostBean.setUsed(1);
//							updEmpOrgPostListPo.add(orgpostBean);
//						} else {
                        // 新增兼岗信息
                        SysEmployeeOrgPost empOrgPostPo = new SysEmployeeOrgPost();
//						BeanUtils.copyProperties(bDto, empOrgPostPo);
                        String[] orgAndPostId = id.split("_");
                        if (orgAndPostId.length == 2) {
                            empOrgPostPo.setPostid(orgAndPostId[1]);
                            empOrgPostPo.setOrgcode(orgAndPostId[0]);

                            empOrgPostPo.setId(TMUID.getUID());
                            empOrgPostPo.setEmpid(empPo.getId());
                            empOrgPostPo.setStatus(2);
//							empOrgPostPo.setTmSort(bDto.getPostSort());
                            empOrgPostPo.setUsed(empPo.getUsed());
                            addEmpOrgPostListPo.add(empOrgPostPo);
                        }
                    }
                } else {
                    empPostServ.deleteBatchByUserId(bDto.getEmpTmuid(), 2);
                }
                // }
                // 人员角色信息
                if (bDto.getRoleid() != null) {
                    UserRoleDto param = new UserRoleDto();
                    param.setUserid(empPo.getId());
                    userRoleServ.deleteUserRoleByUserId(param);// 先删除后添加
                    if (StringUtils.isNotEmpty(bDto.getRoleid())) {
                        userRoleServ.addBatch(this.convertUserRole(empPo.getId(), bDto.getRoleid()));
                    }
                }
                if (empPo.getMobile() != null || empPo.getMail() != null) {// 更新登录信息手机号、邮箱
                    SysLoginUser updateLoginUser = new SysLoginUser();
                    updateLoginUser.setPhone(empPo.getMobile());
                    updateLoginUser.setEmail(empPo.getMail());
                    loginServ.updateLoginUser(bDto.getEmpTmuid(), updateLoginUser);
                }
                // 人员工位信息
                if (StringUtils.isNotEmpty(bDto.getStationCode()) && StringUtils.isNotEmpty(bDto.getStationName())) {
                    List<String> codelist = Coms.StrToList(bDto.getStationCode(), ",");
                    List<String> namelist = Coms.StrToList(bDto.getStationName(), ",");
                    for (int i = 0, il = codelist.size(); i < il; i++) {
                        String code = codelist.get(i);
                        String name = "";
                        if (namelist.size() > i) {
                            name = namelist.get(i);
                        }
                        SysEmployeeStation sta = new SysEmployeeStation();
                        sta.setEmpid(empPo.getId());
                        sta.setStationCode(code);
                        sta.setStationName(name);
                        stationList.add(sta);
                    }

                }
            }
            // 保存数据到数据库
            boolean flag = empInfoServ.updateBatchByIds(empListPo);
            if (flag == false) {
                return "保存人员信息到数据库失败";
            }

            flag = empOrgServ.updateBatchByIds(updEmpOrgListPo);
            if (flag == false) {
                return "保存人员机构信息到数据库失败";
            }

            if (StringUtils.isNotEmpty(updEmpOrgPostListPo)) {
                flag = empPostServ.updateBatchByIds(updEmpOrgPostListPo);
                if (flag == false) {
                    return "保存人员机构岗位信息到数据库失败";
                }
            }
            flag = empPostServ.addBatch(addEmpOrgPostListPo);
            if (flag == false) {
                return "保存人员机构岗位信息到数据库失败";
            }

            /*
             * flag = empRoleServ.updateBatchByIds(updEmpRoleListPo); if (flag == false) {
             * return "保存人员角色信息到数据库失败"; }
             */

            flag = empOrgServ.addBatch(addEmpOrgListPo);
            if (flag == false) {
                return "保存人员机构信息到数据库失败";
            }

            String changeDt = getChangeDt(listDto.get(0).getChangeDt());// 获取变动日期
            // 机构变动
            List<SysEmployeeOrg> changeOrgList = new ArrayList<SysEmployeeOrg>();
            changeOrgList.addAll(addEmpOrgListPo);
            changeOrgList.addAll(updEmpOrgListPo);
            flag = empChgInfoServ.saveUserOrgChange(changeDt, changeOrgList);
            if (flag == false) {
                return "保存人员机构变动信息到数据库失败";
            }

            // 岗位变动
            List<SysEmployeeOrgPost> changePostList = new ArrayList<SysEmployeeOrgPost>();
            changePostList.addAll(addEmpOrgPostListPo);
            changePostList.addAll(updEmpOrgPostListPo);
            flag = empChgInfoServ.saveUserOrgPostChange(changeDt, changePostList);
            if (flag == false) {
                return "保存人员岗位变动信息到数据库失败";
            }
            //工位
            if (empidList.size() > 0) {
                try {
                    stationSrv.saveEmpStation(empidList, stationList);
                } catch (Exception e) {
                    return "保存人员工位信息到数据库失败";
                }
            }
            /*
             * flag = empRoleServ.addBatch(addEmpRoleListPo); if (flag == false) { return
             * "保存人员角色信息到数据库失败"; }
             */

            // 清理人员在redis缓存中的待办信息empidList
            List<EmployeeVo> list = getEmployee(empidList);
            List<String> loginNames = new ArrayList<String>();
            for (EmployeeVo emp : list) {
                loginNames.add(emp.getLoginUserName());
            }
            todoService.clearTodoCachedForUsers(loginNames);
            this.setEmployeeToReids(list);// 更新缓存

        } catch (Exception e) {
            log.error("", e);
        }

        return err;
    }

    /**
     * 批量增删改人员信息
     *
     * @param addList
     * @param delList
     * @param updList
     * @return
     * @category 批量增删改人员信息
     */
    @Override
    public String saveEmployee(List<EmployeeDto> addList, List<EmployeeDto> delList, List<EmployeeDto> updList) {
        String err = "";
        try {

            if (delList == null) {
                delList = new ArrayList<EmployeeDto>();
            }
            err = delEmployee(delList);
            if (err != null && !"".equals(err)) {
                // res.fail(this.errCode, err);
                // return res;
                return err;
            }
            // 增
            if (addList == null) {
                addList = new ArrayList<EmployeeDto>();
            }
            err = addEmployee(addList);
            if (err != null && !"".equals(err)) {
                // res.fail(this.errCode, err);
                // return res;
                return err;
            }
            // 改
            if (updList == null) {
                updList = new ArrayList<EmployeeDto>();
            }
            err = updEmployee(updList);
            if (err != null && !"".equals(err)) {
                // res.fail(this.errCode, err);
                // return res;
                return err;
            }

            // res.setMessage("保存成功");
        } catch (Exception e) {
            log.error("", e);
            err = e.getMessage();
            // res.fail(this.errCode, "后台处理有误，请查看日志");
        }
        return err;// res;
    }

    /**
     * 获取人员信息
     */
    @Override
    public List<EmployeeVo> getEmployee(String empId) {
        EmpParamDto paramDto = new EmpParamDto();
        paramDto.setEmpid(empId);
        List<EmployeeVo> list = getEmployeeFilter(paramDto);
        return list;
    }

    /**
     * 获取人员信息
     */
    @Override
    public List<EmployeeVo> getEmployee(List<String> empIds) {
        if (StringUtils.isEmpty(empIds)) {
            return null;
        }
        if (empIds.size() == 1) {
            return getEmployee(empIds.get(0));
        } else {
            EmpParamDto paramDto = new EmpParamDto();
            String ids = String.join(",", empIds);
            paramDto.setEmpid(ids);
            return getEmployeeFilter(paramDto);
        }
    }

    /**
     * 获取人员、人员机构、人员岗位信息
     *
     * @param param
     * @return
     * @category 获取人员机构岗位信息
     */
    @Override
    public List<EmployeeVo> getEmployee(EmpParamDto paramDto) {
        List<EmployeeVo> list = new ArrayList<EmployeeVo>();

        try {
            // 机构代码
            String orgcode = paramDto.getOrgcode();
            orgcode = orgcode == null ? "" : orgcode;
            // 只显示失效记录标识（1：是，0：不是）
            // Integer invalid = paramDto.getInvalid();
            // invalid = invalid == null ? 0 : invalid;

//			if ("".equals(orgcode)) { // 查看所有人员
            // 获取所有人员信息（查询时分页）
            list = getAllEmployee(paramDto);
//			} else { // 按机构过滤人员
//				list = getEmployeeFilter(paramDto);
//			}
        } catch (Exception e) {
            log.error("", e);
        }

        return list;
    }

    /**
     * 获取过滤后的人员信息，目前按多选机构过滤，以后可能按照岗位等信息过滤
     *
     * @param paramDto
     * @return
     * @category 获取过滤后的人员信息
     */

    private List<EmployeeVo> getEmployeeFilter(EmpParamDto paramDto) {
        List<EmployeeVo> list = new ArrayList<EmployeeVo>();

        try {
            MapUtil mapUtil = new MapUtil();

            paramDto.setOrgStatus(1);
            paramDto.setUsed(1);
            // 根据机构代码获取机构列表
            EmpOrgParamDto orgParamDto = new EmpOrgParamDto();
            BeanUtils.copyProperties(paramDto, orgParamDto);
            List<SysEmployeeOrg> orgList = empOrgServ.getEmployeeOrg(orgParamDto);
            if (orgList == null || orgList.size() <= 0) {
                return list;
            }
            Map<String, SysEmployeeOrg> empOrgMap = mapUtil.getEmpidOrgMap(orgList);
            // 从机构模块获取附加信息
            List<SysOrg> extOrgList = extOperServ.getExtOrgList(orgList);
            Map<String, SysOrg> extOrgMap = mapUtil.getOrgcodeMap(extOrgList);

            // 整理人员id串用于获取人员信息
            String empid = "";
            for (SysEmployeeOrg emp : orgList) {
                empid += "," + emp.getEmpid();
            }
            if (!"".equals(empid)) {
                empid = empid.substring(1);
            }
            // 根据机构列表获取人员信息（不分页）
            EmpParamDto empParamDto = new EmpParamDto();
            BeanUtils.copyProperties(paramDto, empParamDto);
            empParamDto.setEmpid(empid);
            empParamDto.setCurrent(null);
            empParamDto.setSize(null);
            empParamDto.setUsed(1);
            List<SysEmployeeInfo> empList = empInfoServ.getEmployee(empParamDto);
            if (empList == null || empList.size() <= 0) {
                return list;
            }
            // 手动分页
            PageUtil pageUtil = new PageUtil(paramDto.getCurrent(), paramDto.getSize());
            empList = pageUtil.getPageList(empList);
            // 整理人员id串用于获取岗位信息
            empid = "";
            List<String> listUserid = new ArrayList<String>();
            for (SysEmployeeInfo emp : empList) {
                empid += "," + emp.getId();
                listUserid.add(emp.getId());
            }
            if (!"".equals(empid)) {
                empid = empid.substring(1);
            }
            // 根据人员信息获取岗位信息
            EmpOrgPostParamDto postParamDto = new EmpOrgPostParamDto();
            postParamDto.setEmpid(empid);
            postParamDto.setUsed(1);
            List<SysEmployeeOrgPost> postList = empPostServ.getEmployeeOrgPost(postParamDto);
            Map<String, SysEmployeeOrgPost> empOrgPostMap = mapUtil.getEmpidOrgPostMap(postList);

            // 从岗位模块获取附加信息
            List<PostVo> extPostList = extOperServ.getExtPostList(postList);
            Map<String, PostVo> extPostMap = mapUtil.getPostIdMap(extPostList);

            // 根据人员信息获取角色信息
            // EmpRoleParamDto roleParamDto = new EmpRoleParamDto();
            postParamDto.setEmpid(empid);
            List<SysUserRole> roleList = userRoleServ.getUserRole(listUserid);
            Map<String, List<SysUserRole>> empRoleMap = mapUtil.getEmpidRoleMap(roleList);
            // 从角色模块获取附加信息
            List<SysRole> extRoleList = extOperServ.getExtRoleList(roleList);
            Map<String, SysRole> extRoleMap = mapUtil.getRoleIdMap(extRoleList);

            // 获取登录信息
            List<SysLoginUser> loginList = loginServ.getSysUserbyIdList(listUserid);
            Map<String, SysLoginUser> extLoginMap = mapUtil.getLoginIdMap(loginList);

            // 获取工位信息
            Map<String, List<SysEmployeeStation>> smap = stationSrv.getEmpStationMap(listUserid);

            // 制作完整的人员信息
            for (SysEmployeeInfo emp : empList) {
                EmployeeVo empVo = new EmployeeVo();
                BeanUtils.copyProperties(emp, empVo);
                empVo.setEmpTmuid(emp.getId());
                // 填充机构信息
                SysEmployeeOrg empOrg = empOrgMap.get(emp.getId());
                if (empOrg != null) {
                    BeanUtils.copyProperties(empOrg, empVo);
                    empVo.setOrgTmuid(empOrg.getId());
                    empVo.setOrgStatus(empOrg.getStatus());
                    // 填充机构名称
                    SysOrg sysOrg = extOrgMap.get(empOrg.getOrgcode());
                    if (sysOrg != null) {
                        empVo.setOrgname(sysOrg.getOrgname());
                        empVo.setOrgNumber(sysOrg.getOrgNumber());
                    }
                }
                // 填充岗位信息
                SysEmployeeOrgPost empOrgPost = empOrgPostMap.get(emp.getId());
                if (empOrgPost != null) {
                    BeanUtils.copyProperties(empOrgPost, empVo);
                    empVo.setPostTmuid(empOrgPost.getId());
                    empVo.setPostStatus(empOrgPost.getStatus());
                    // 填充岗位名称
                    PostVo postVo = extPostMap.get(empOrgPost.getPostid());
                    if (postVo != null) {
                        empVo.setPostname(postVo.getName());
                        empVo.setPostProfessionalInfoId(postVo.getProfessionalInfoId());
                    }

                }
                // 填充角色信息
                this.setRoleInfo(empVo, extRoleMap, empRoleMap.get(emp.getId()));
                /*
                 * List<SysUserRole> empRole = empRoleMap.get(emp.getId()); if (empRole != null)
                 * { BeanUtils.copyProperties(empRole, empVo);
                 * empVo.setRoleTmuid(empRole.getId()); empVo.setRoleid(empRole.getRoleid()); //
                 * 填充角色名称 SysRole sysRole = extRoleMap.get(empRole.getRoleid()); if (sysRole !=
                 * null) { empVo.setRolename(sysRole.getName()); } }
                 */

                // 填充登录信息
                this.setLoginInfo(empVo, extLoginMap);
                // 填充工位信息
                this.setStationInfo(empVo, smap);

                // 存储总记录数用于分页计算
                empVo.setRecordCount(new Long(empList.get(0).getRecordCount()));
                list.add(empVo);
            }
        } catch (Exception e) {
            log.error("", e);
        }

        return list;
    }

    /**
     * 获取所有人员信息（支持分页查询）
     *
     * @param paramDto
     * @return
     * @category 获取所有人员信息
     */
    @Override
    public List<EmployeeVo> getAllEmployee(EmpParamDto paramDto) {
        List<EmployeeVo> list = new ArrayList<EmployeeVo>();

        try {
            MapUtil mapUtil = new MapUtil();
            if (paramDto.getUsed() == null) {//为空时查询有效人员
                paramDto.setUsed(1);
            }
            String paramEmpid = paramDto.getEmpid();
            if (StringUtils.isEmpty(paramEmpid)) {
                // 根据机构条件过滤人员
                List<String> orgempidlist = new ArrayList<String>();
                if (paramDto.getOrgcode() != null && paramDto.getOrgcode().length() > 0) {
                    paramDto.setOrgStatus(1);

                    // 根据机构代码获取机构列表
                    EmpOrgParamDto orgParamDto = new EmpOrgParamDto();
                    BeanUtils.copyProperties(paramDto, orgParamDto);
                    orgParamDto.setCurrent(null);
                    orgParamDto.setSize(null);
                    if (paramDto.getUsed() != -1) {
                        orgParamDto.setUsed(1);
                    }
                    // 查询下级所有机构
                    List<String> orgList = new ArrayList<String>();
                    String paramOrgCode = orgParamDto.getOrgcode();
                    String[] orgArray = paramOrgCode.split(",");
                    for (String orgcode : orgArray) {
                        List<SysOrg> reslist = orgServ.getOrgList(orgcode);
                        if (reslist != null && reslist.size() > 0) {
                            for (SysOrg org : reslist) {
                                orgList.add(org.getOrgcode());
                            }
                        }
                    }
                    orgParamDto.setOrgcode(StringUtils.join(orgList, ","));
                    List<SysEmployeeOrg> empOrgList = empOrgServ.getEmployeeOrg(orgParamDto);
                    if (empOrgList == null || empOrgList.size() <= 0) {
                        return list;
                    }
                    // Map<String, SysEmployeeOrg> empOrgMap = mapUtil.getEmpidOrgMap(empOrgList);
                    // 从机构模块获取附加信息
                    // List<SysOrg> extOrgList = extOperServ.getExtOrgList(empOrgList);
                    // Map<String, SysOrg> extOrgMap = mapUtil.getOrgcodeMap(extOrgList);

                    // 整理人员id串用于获取人员信息
                    String empid = "";
                    for (SysEmployeeOrg emp : empOrgList) {
                        orgempidlist.add(emp.getEmpid());
                        empid += "," + emp.getEmpid();
                    }
                    if (!"".equals(empid)) {
                        empid = empid.substring(1);
                    }
                } else {
                    List<String> rightList = new ArrayList<String>();// 权限列表
                    boolean allRight;
                    if (paramDto.getIsJurisdiction() != null && paramDto.getIsJurisdiction()) {
                        //走管辖
                        allRight = orgServ.getOrgRight(rightList);
                    } else {
                        //不管辖
                        allRight = true;
                    }
                    //allRight = true;
                    if (!allRight) {// 没有全部权限
                        if (StringUtils.isNotEmpty(rightList)) {
                            EmpOrgParamDto orgParamDto = new EmpOrgParamDto();
                            orgParamDto.setCurrent(null);
                            orgParamDto.setSize(null);
                            if (paramDto.getUsed() != -1) {
                                orgParamDto.setUsed(1);
                            }
                            orgParamDto.setOrgcode(StringUtils.join(rightList, ","));
                            List<SysEmployeeOrg> empOrgList = empOrgServ.getEmployeeOrg(orgParamDto);// 查询管辖人员
                            if (StringUtils.isNotEmpty(empOrgList)) {
                                for (SysEmployeeOrg emp : empOrgList) {
                                    orgempidlist.add(emp.getEmpid());
                                }
                            } else {
                                return list;
                            }
                        } else {
                            return list;
                        }
                    }
                }

                // 根据登录名称条件过滤人员
                List<String> loginempidlist = new ArrayList<String>();
                if (paramDto.getLoginUserName() != null && paramDto.getLoginUserName().length() > 0) {
                    // Where where = Where.create();
                    // where.like(SysLoginUser::getUserName, paramDto.getLoginUserName());
                    // entityService.rawQueryListByWhere(SysLoginUser.class, where);
                    List<SysLoginUser> loginlist = this.getLoginUser(paramDto.getLoginUserName());
                    if (loginlist != null && loginlist.size() > 0) {
                        for (SysLoginUser login : loginlist) {
                            loginempidlist.add(login.getId());
                        }
                    }
                }

                // 最终人员
                List<String> empidList = new ArrayList<String>();

                if (orgempidlist.size() > 0 && loginempidlist.size() > 0) {
                    // 两个条件都有，取交集
                    empidList = orgempidlist.stream().filter(item -> loginempidlist.contains(item)).collect(Collectors.toList());
//					String paramEmpid = paramDto.getEmpid();
//					if (empidList.size() > 0) {
//						paramEmpid = StringUtils.join(empidList, ",");
//					} else {
//						paramEmpid = "";
//					}
//					paramDto.setEmpid(paramEmpid);
                } else if (orgempidlist.size() > 0) {
                    // 只有机构过滤
                    empidList = orgempidlist;
                } else if (loginempidlist.size() > 0) {
                    // 只有登录名称过滤
                    empidList = loginempidlist;
                }

                if (empidList.size() > 0) {
                    paramEmpid = StringUtils.join(empidList, ",");
                    paramDto.setEmpid(paramEmpid);
                } else {
                    // paramEmpid = "";
                }
            }
            List<SysEmployeeInfo> empList = empInfoServ.getEmployee(paramDto);
            if (empList != null && empList.size() > 0) {
                // 整理人员id串用于获取机构信息、岗位信息
                String empid = "";
                List<String> listUserid = new ArrayList<String>();
                for (SysEmployeeInfo emp : empList) {
                    empid += "," + emp.getId();
                    listUserid.add(emp.getId());
                }
                if (!"".equals(empid)) {
                    empid = empid.substring(1);
                }
                // 根据人员信息获取机构信息
                EmpOrgParamDto orgParamDto = new EmpOrgParamDto();
                orgParamDto.setEmpid(empid);
                if (paramDto.getUsed() != -1) {
                    orgParamDto.setUsed(1);
                }
                List<SysEmployeeOrg> orgList = empOrgServ.getEmployeeOrg(orgParamDto);
                Map<String, SysEmployeeOrg> empOrgMap = mapUtil.getEmpidOrgMap(orgList);

                // 从机构模块获取附加信息
                List<SysOrg> extOrgList = extOperServ.getExtOrgList(orgList);
                Map<String, SysOrg> extOrgMap = mapUtil.getOrgcodeMap(extOrgList);

                // 根据人员信息获取岗位信息
                EmpOrgPostParamDto postParamDto = new EmpOrgPostParamDto();
                postParamDto.setEmpid(empid);
                if (paramDto.getUsed() != -1) {
                    postParamDto.setUsed(1);
                }
                List<SysEmployeeOrgPost> postList = empPostServ.getEmployeeOrgPost(postParamDto);
                Map<String, SysEmployeeOrgPost> empOrgPostMap = mapUtil.getEmpidOrgPostMap(postList);
                // 获取兼岗信息
                List<SysEmployeeOrgPost> partPostList = empPostServ.getEmployeePartOrgPost(postParamDto);
                Map<String, List<SysEmployeeOrgPost>> empOrgPartPostMap = mapUtil.getEmpidPartOrgPostMap(partPostList);
                // 获取兼岗附加信息
                List<SysOrg> extPartOrgList = new ArrayList<>();
                if (partPostList.size() != 0) {
                    extPartOrgList = extOperServ.getExtPartOrgList(partPostList);
                }
                Map<String, SysOrg> extPartOrgMap = mapUtil.getOrgcodeMap(extPartOrgList);

                // 从岗位模块获取附加信息
                List<PostVo> extPostList = extOperServ.getExtPostList(postList);
                Map<String, PostVo> extPostMap = mapUtil.getPostIdMap(extPostList);

                // 从兼岗模块获取附加信息
                List<PostVo> extPartPostList = extOperServ.getExtPostList(partPostList);
                Map<String, PostVo> extPartPostMap = mapUtil.getPostIdMap(extPartPostList);

                // 根据人员信息获取角色信息
                // EmpRoleParamDto roleParamDto = new EmpRoleParamDto();
                postParamDto.setEmpid(empid);
                List<SysUserRole> roleList = userRoleServ.getUserRole(listUserid);// empRoleServ.getEmployeeRole(roleParamDto);
                Map<String, List<SysUserRole>> empRoleMap = mapUtil.getEmpidRoleMap(roleList);
                // 从角色模块获取附加信息
                List<SysRole> extRoleList = extOperServ.getExtRoleList(roleList);
                Map<String, SysRole> extRoleMap = mapUtil.getRoleIdMap(extRoleList);

                // 获取登录信息
                List<SysLoginUser> loginList = loginServ.getSysUserbyIdList(listUserid);
                Map<String, SysLoginUser> extLoginMap = mapUtil.getLoginIdMap(loginList);

                // 获取工位信息
                Map<String, List<SysEmployeeStation>> smap = stationSrv.getEmpStationMap(listUserid);

                boolean showUnit = paramDto.getShowUnit() == null ? false : paramDto.getShowUnit().booleanValue();//是否显示单位名称
                // 制作完整的人员信息
                for (SysEmployeeInfo emp : empList) {
                    EmployeeVo empVo = new EmployeeVo();
                    BeanUtils.copyProperties(emp, empVo);
                    empVo.setEmpTmuid(emp.getId());
                    // 填充机构信息
                    SysEmployeeOrg empOrg = empOrgMap.get(emp.getId());
                    if (empOrg != null) {
                        BeanUtils.copyProperties(empOrg, empVo);
                        empVo.setOrgTmuid(empOrg.getId());
                        empVo.setOrgStatus(empOrg.getStatus());
                        // 填充机构名称
                        SysOrg sysOrg = extOrgMap.get(empOrg.getOrgcode());
                        if (sysOrg != null) {
                            empVo.setOrgname(sysOrg.getOrgname());
                            empVo.setOrgSort(sysOrg.getOrglevel() + sysOrg.getTmSort());
                        }
                    }
                    // 填充岗位信息
                    SysEmployeeOrgPost empOrgPost = empOrgPostMap.get(emp.getId());
                    if (empOrgPost != null) {
                        BeanUtils.copyProperties(empOrgPost, empVo);
                        empVo.setPostTmuid(empOrgPost.getId());
                        empVo.setPostStatus(empOrgPost.getStatus());
                        // 填充岗位名称
                        PostVo postVo = extPostMap.get(empOrgPost.getPostid());
                        if (postVo != null) {
                            empVo.setPostname(postVo.getName());
                            empVo.setPostSort(postVo.getTmSort());
                        }
                    }

                    // 填充兼岗信息
                    this.setPartOrgPostInfo(empVo, extPartOrgMap, extPartPostMap, empOrgPartPostMap.get(emp.getId()));

                    // 填充角色信息
                    // List<SysUserRole> ListEmpRole = empRoleMap.get(emp.getId());
                    this.setRoleInfo(empVo, extRoleMap, empRoleMap.get(emp.getId()));
                    // if (StringUtils.isNotEmpty(ListEmpRole)) {
                    // BeanUtils.copyProperties(ListEmpRole.get(0), empVo);
                    // empVo.setRoleTmuid(empRole.getId());
                    // Map<String, String> map = this.convertRoleName(extRoleMap, ListEmpRole);
                    // empVo.setRoleid(map.get("id"));
                    // empVo.setRolename(map.get("name"));

                    // 填充角色名称
                    /*
                     * SysRole sysRole = extRoleMap.get(empRole.getRoleid()); if (sysRole != null) {
                     * empVo.setRolename(sysRole.getName()); }
                     */
                    // }

                    // 填充登录信息
                    this.setLoginInfo(empVo, extLoginMap);
                    // 填充工位信息
                    this.setStationInfo(empVo, smap);

                    // 存储总记录数用于分页计算
                    empVo.setRecordCount(new Long(empList.get(0).getRecordCount()));
                    if (showUnit) {
                        SysOrg org = orgServ.getParentOrgByOrgType(empVo.getOrgcode(), ISysOrgService.OrgWorkshopOrDepartment);//找人员所在车间，
                        if (org != null) {
                            empVo.setUnitCode(org.getOrgcode());
                            empVo.setUnitName(org.getOrgname());
                        } else {//找不到就显示人员所在机构名称
                            empVo.setUnitCode(empVo.getOrgcode());
                            empVo.setUnitName(empVo.getOrgname());
                        }
                    }
                    empVo.setUpdateTime(emp.getUpdateTime());
                    list.add(empVo);
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }

        return list;
    }

    /**
     * 模糊检索查找登陆信息
     *
     * @param loginUserName
     * @return
     */
    private List<SysLoginUser> getLoginUser(String loginUserName) {
        Where where = Where.create();
        where.like(SysLoginUser::getUserName, loginUserName);
        return entityService.rawQueryListByWhere(SysLoginUser.class, where);
    }

    private void setPartOrgPostInfo(EmployeeVo empVo, Map<String, SysOrg> extOrgMap, Map<String, PostVo> extPartPostMap, List<SysEmployeeOrgPost> orgPostList) {
        empVo.setPartTimePostId("");
        empVo.setPartTimePostName("");

        if (StringUtils.isNotEmpty(orgPostList)) {
            Map<String, String> map = this.convertPartPostName(extOrgMap, extPartPostMap, orgPostList);

            String id = map.get("id");
            if (id != null) {
                empVo.setPartTimePostId(id);
                empVo.setPartTimePostName(map.get("name"));
            }
        }

    }

    private Map<String, String> convertPartPostName(Map<String, SysOrg> extOrgMap, Map<String, PostVo> extPartPostMap, List<SysEmployeeOrgPost> orgPostList) {
        Map<String, String> rtnMap = new HashMap<String, String>();
        StringBuffer id = new StringBuffer("");
        StringBuffer name = new StringBuffer("");
        for (SysEmployeeOrgPost sysEmployeeOrgPost : orgPostList) {
            String postId = sysEmployeeOrgPost.getPostid();
            StringBuffer tempName = new StringBuffer("");
            StringBuffer tempId = new StringBuffer("");
            PostVo postVo = extPartPostMap.get(postId);
            if (postVo != null) {
                tempId.append(postId);
//				name += postVo.getName() + ",";
                tempName.append(postVo.getName());
            }

            SysOrg org = extOrgMap.get(sysEmployeeOrgPost.getOrgcode());
            if (org != null) {
                tempId.insert(0, "_");
                tempId.insert(0, org.getId());
//				tempId = org.getId() + "_" + tempId;

//				tempName = tempName + "(" + org.getOrgname() + ")";
                tempName.append("(");
                tempName.append(org.getOrgname());
                tempName.append(")");

            }
//			name += tempName + ",";
//			id += tempId + ",";
            id.append(tempId);
            id.append(",");
            name.append(tempName);
            name.append(",");
        }
        if (id.length() > 0) {
            rtnMap.put("id", id.substring(0, id.length() - 1));
            rtnMap.put("name", name.substring(0, name.length() - 1));
        }
        return rtnMap;
    }

//	private Map<String, String> convertPostName(Map<String, PostVo> extPartPostMap,
//			List<SysEmployeeOrgPost> orgPostList) {
//		Map<String, String> rtnMap = new HashMap<String, String>();
//		String id = "";
//		String name = "";
//		for (SysEmployeeOrgPost sysEmployeeOrgPost : orgPostList) {
//			String postId = sysEmployeeOrgPost.getPostid();
//
//			PostVo postVo = extPartPostMap.get(postId);
//			if (postVo != null) {
//				id += postId + ",";
//				name += postVo.getName() + ",";
//			}
//		}
//		if (id.length() > 0) {
//			rtnMap.put("id", id.substring(0, id.length() - 1));
//			rtnMap.put("name", name.substring(0, name.length() - 1));
//		}
//		return rtnMap;
//	}

    private void setRoleInfo(EmployeeVo empVo, Map<String, SysRole> roleMap, List<SysUserRole> listEmpRole) {
        empVo.setRoleid("");
        empVo.setRolename("");
        if (StringUtils.isNotEmpty(listEmpRole)) {
            Map<String, String> map = this.convertRoleName(roleMap, listEmpRole);
            String id = map.get("id");
            if (id != null) {
                empVo.setRoleid(id);
                empVo.setRolename(map.get("name"));
            }
        }
    }

    private void setLoginInfo(EmployeeVo empVo, Map<String, SysLoginUser> loginMap) {
        String id = empVo.getEmpTmuid();
        if (StringUtils.isNotEmpty(loginMap)) {
            SysLoginUser login = loginMap.get(id);
            if (login != null) {
                empVo.setLoginUserName(login.getUserName());
                empVo.setLoginStatus(login.getStatus());
//				empVo.setRoleid(id);
//				empVo.setRolename(map.get("name"));
            }
        }
    }

    private void setStationInfo(EmployeeVo empVo, Map<String, List<SysEmployeeStation>> smap) {
        String id = empVo.getEmpTmuid();
        List<SysEmployeeStation> slist = smap == null ? null : smap.get(id);
        if (StringUtils.isNotEmpty(slist)) {
            String code = "", name = "";
            for (SysEmployeeStation obj : slist) {
                code += "," + obj.getStationCode();
                name += "," + obj.getStationName();
            }
            if (code.length() > 0) {
                code = code.substring(1);
                name = name.substring(1);
            }
            empVo.setStationCode(code);
            empVo.setStationName(name);
        }
    }

    private Map<String, String> convertRoleName(Map<String, SysRole> map, List<SysUserRole> list) {
        Map<String, String> rtnMap = new HashMap<String, String>();
        String id = "";
        String name = "";
        for (SysUserRole sysUserRole : list) {
            String roleid = sysUserRole.getRoleid();

            SysRole sysRole = map.get(roleid);
            if (sysRole != null) {
                id += roleid + ",";
                name += sysRole.getName() + ",";
            }
        }
        if (id.length() > 0) {
            rtnMap.put("id", id.substring(0, id.length() - 1));
            rtnMap.put("name", name.substring(0, name.length() - 1));
        }
        return rtnMap;

    }

    /**
     * 批量添加人员调动信息
     *
     * @param listDto
     * @return
     * @category 批量添加人员调动信息
     */
    @Override
    public String addTransfer(List<EmployeeOrgPostDto> listDto) {
        // Res<?> res = new Res<>();
        String info = "";
        try {
            if (listDto == null || listDto.size() <= 0) {
//				res.error509("添加失败，数据为空");
                // res.fail(this.errCode, "添加失败，数据为空");
                // return res;
                return "添加失败，数据为空";
            }

            List<SysEmployeeChangeInfo> changeInfoList = new ArrayList<SysEmployeeChangeInfo>(); // 流水
            List<SysEmployeeChangeTodo> changeTodoList = new ArrayList<SysEmployeeChangeTodo>(); // 预变动
            List<SysEmployeeChangeTodo> updChangeTodoList = new ArrayList<SysEmployeeChangeTodo>(); // 删除预变动
            List<SysEmployeeOrg> empOrgList = new ArrayList<SysEmployeeOrg>(); // 人员机构
            List<SysEmployeeOrgPost> empPostList = new ArrayList<SysEmployeeOrgPost>(); // 人员岗位

            LocalDate now = LocalDate.now();
            for (EmployeeOrgPostDto empOrgPost : listDto) {
                String newOrgcode = empOrgPost.getNewOrgcode();
                String newOrgname = empOrgPost.getNewOrgname();
                String newPostid = empOrgPost.getNewPostid();
                String newPostname = empOrgPost.getNewPostname();

                // 默认为不选则继承当前机构和岗位
                if (newOrgcode == null || "".equals(newOrgcode)) { // 前台没选机构
                    // 继承当前机构
                    newOrgcode = empOrgPost.getOldOrgcode();
                    newOrgname = empOrgPost.getOldOrgname();
                }
                if (newPostid == null || "".equals(newPostid)) { // 前台没选岗位
                    // 继承当前机构
                    newPostid = empOrgPost.getOldPostid();
                    newPostname = empOrgPost.getOldPostname();
                }

                Boolean isAfter = DateUtil.isAfter(empOrgPost.getChangeDate(), now);
                if (isAfter != null) {
                    if (isAfter.booleanValue() == true) { // 预变动
                        // 【校验】单个人预变动记录最多只能有一条
//						List<SysEmployeeChangeTodo> todoDbList = empChgTodoServ.list(
//								new LambdaQueryWrapper<SysEmployeeChangeTodo>().eq(SysEmployeeChangeTodo::getUsed, 1)
//										.in(SysEmployeeChangeTodo::getDataType,
//												Stream.of(1, 2).collect(Collectors.toList()))
//										.eq(SysEmployeeChangeTodo::getStatus, 1)
//										.orderByAsc(SysEmployeeChangeTodo::getDataType));

                        // 【校验】单个人预变动记录最多只能有一条
                        Where query = Where.create().eq(SysEmployeeChangeTodo::getUsed, 1).in(SysEmployeeChangeTodo::getDataType, Stream.of(1, 2).collect(Collectors.toList()).toArray()).eq(SysEmployeeChangeTodo::getStatus, 1);
                        Order order = Order.create().orderByAsc(SysEmployeeChangeTodo::getDataType);
                        List<SysEmployeeChangeTodo> todoDbList = entityService.queryList(SysEmployeeChangeTodo.class, query, order);

                        if (todoDbList != null && todoDbList.size() > 0) {
                            String dbOrgname = "";
                            String dbPostname = "";
                            String dbChangeDate = "";
                            for (SysEmployeeChangeTodo todoDb : todoDbList) {
                                if (todoDb.getDataType().intValue() == 1) { // 机构
                                    dbOrgname = todoDb.getNewName();
                                } else if (todoDb.getDataType().intValue() == 2) { // 岗位
                                    dbPostname = todoDb.getNewName();
                                }
                                dbChangeDate = DateUtil.formatDate(todoDb.getChangeDate());
                            }
                            // res.fail(this.errCode,"【" + dbChangeDate + "】【" + dbOrgname + "】【" +
                            // dbPostname + "】预调动已存在！");
                            // return res;
                            return "【" + dbChangeDate + "】【" + dbOrgname + "】【" + dbPostname + "】预调动已存在！";
                        }

                        // TODO 可以在此处增加机构预删除判断逻辑

                        // 机构变动（机构岗位只要有1个变动，就都存到预变动表里，1对1关系，方便后续查询处理）
                        SysEmployeeChangeTodo empChgTodoOrg = new SysEmployeeChangeTodo(); // 机构变动
                        BeanUtils.copyProperties(empOrgPost, empChgTodoOrg);
                        empChgTodoOrg.setId(TMUID.getUID());
                        empChgTodoOrg.setDataType(1);
                        empChgTodoOrg.setStatus(1);
                        empChgTodoOrg.setChangeType(1);
                        empChgTodoOrg.setUsed(1);
                        empChgTodoOrg.setOldCode(empOrgPost.getOldOrgcode());
                        empChgTodoOrg.setOldName(empOrgPost.getOldOrgname());
                        empChgTodoOrg.setNewCode(newOrgcode);
                        empChgTodoOrg.setNewName(newOrgname);
                        empChgTodoOrg.setGroupid(TMUID.getUID());
                        empChgTodoOrg.setMemo("机构调动（预）：【" + empOrgPost.getEmpname() + "】机构从【" + empChgTodoOrg.getOldName() + "】变动为【" + newOrgname + "】");
                        changeTodoList.add(empChgTodoOrg);
                        // 机构变动流水（当机构真正变动时再写流水）
                        if (empOrgPost.getNewOrgcode() != null && !"".equals(empOrgPost.getNewOrgcode())) {
                            SysEmployeeChangeInfo empChgInfoOrg = new SysEmployeeChangeInfo();
                            BeanUtils.copyProperties(empChgTodoOrg, empChgInfoOrg);
                            empChgInfoOrg.setId(TMUID.getUID());
                            changeInfoList.add(empChgInfoOrg);
                        }

                        // 岗位变动（机构岗位只要有1个变动，就都存到预变动表里，1对1关系，方便后续查询处理）
                        SysEmployeeChangeTodo empChgTodoPost = new SysEmployeeChangeTodo(); // 岗位变动
                        BeanUtils.copyProperties(empOrgPost, empChgTodoPost);
                        empChgTodoPost.setId(TMUID.getUID());
                        empChgTodoPost.setDataType(2);
                        empChgTodoPost.setStatus(1);
                        empChgTodoPost.setChangeType(1);
                        empChgTodoPost.setUsed(1);
                        empChgTodoPost.setOldCode(empOrgPost.getOldPostid());
                        empChgTodoPost.setOldName(empOrgPost.getOldPostname());
                        empChgTodoPost.setNewCode(newPostid);
                        empChgTodoPost.setNewName(newPostname);
                        empChgTodoPost.setGroupid(empChgTodoOrg.getGroupid());
                        empChgTodoPost.setMemo("岗位调动（预）：【" + empOrgPost.getEmpname() + "】岗位从【" + empOrgPost.getOldOrgname() + " - " + empChgTodoPost.getOldName() + "】变动为【" + newOrgname + " - " + newPostname + "】");
                        changeTodoList.add(empChgTodoPost);
                        // 岗位变动流水（当岗位真正变动时再写流水）
                        if (empOrgPost.getNewPostid() != null && !"".equals(empOrgPost.getNewPostid())) {
                            SysEmployeeChangeInfo empChgInfoPost = new SysEmployeeChangeInfo();
                            BeanUtils.copyProperties(empChgTodoPost, empChgInfoPost);
                            empChgInfoPost.setId(TMUID.getUID());
                            changeInfoList.add(empChgInfoPost);
                        }
                    } else { // 立即变动
                        // 【校验】前台交互提示操作
                        if (empOrgPost.getDoCover() == null || empOrgPost.getDoCover().intValue() != 1) {
                            // Res<JSONObject> resObj = new Res<JSONObject>();
                            // JSONObject result = new JSONObject();
                            // result.put("confirm", true);
                            // resObj.fail(this.errCode,"【" + empOrgPost.getEmpname() +
                            // "】调动到历史日期，系统将从调动日期开始到未来的调动信息全部清除。请谨慎操作。");
                            // resObj.setResult(result);
                            // return resObj;
                            return "【" + empOrgPost.getEmpname() + "】调动到历史日期，系统将从调动日期开始到未来的调动信息全部清除。请谨慎操作。";
                        }

                        String groupid = TMUID.getUID();

                        // 机构变动
                        if (empOrgPost.getNewOrgcode() != null && !"".equals(empOrgPost.getNewOrgcode())) {
                            // 查询当前主机构
//							SysEmployeeOrg empOrg = empOrgServ.getOne(new LambdaQueryWrapper<SysEmployeeOrg>()
//									.eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 1)
//									.eq(SysEmployeeOrg::getEmpid, empOrgPost.getEmpid()));

                            // 查询当前主机构
                            Where query = Where.create().eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 1).eq(SysEmployeeOrg::getEmpid, empOrgPost.getEmpid());
                            SysEmployeeOrg empOrg = entityService.queryObject(SysEmployeeOrg.class, query);

                            if (empOrg != null) {
                                // 更新主机构
                                empOrg.setOrgcode(empOrgPost.getNewOrgcode());
                                empOrg.setStatus(1);
                                empOrg.setChangeDate(empOrgPost.getChangeDate());
                                empOrgList.add(empOrg);
                                // 流水
                                SysEmployeeChangeInfo empChgInfo = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(empOrgPost, empChgInfo);
                                empChgInfo.setId(TMUID.getUID());
                                empChgInfo.setDataType(1);
                                empChgInfo.setStatus(1);
                                empChgInfo.setChangeType(1);
                                empChgInfo.setUsed(1);
                                empChgInfo.setOldCode(empOrgPost.getOldOrgcode());
                                empChgInfo.setOldName(empOrgPost.getOldOrgname());
                                empChgInfo.setNewCode(newOrgcode);
                                empChgInfo.setNewName(newOrgname);
                                empChgInfo.setGroupid(groupid);
                                empChgInfo.setMemo("机构调动：【" + empOrgPost.getEmpname() + "】机构从【" + empChgInfo.getOldName() + "】变动为【" + empChgInfo.getNewName() + "】");
                                changeInfoList.add(empChgInfo);
                            }
                        }
                        // 岗位变动
                        if (empOrgPost.getNewPostid() != null && !"".equals(empOrgPost.getNewPostid())) {
                            // 查询当前主岗位
//							SysEmployeeOrgPost empPost = empPostServ.getOne(new LambdaQueryWrapper<SysEmployeeOrgPost>()
//									.eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 1)
//									.eq(SysEmployeeOrgPost::getEmpid, empOrgPost.getEmpid()));

                            // 查询当前主岗位
                            Where query = Where.create().eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 1).eq(SysEmployeeOrgPost::getEmpid, empOrgPost.getEmpid());
                            SysEmployeeOrgPost empPost = entityService.queryObject(SysEmployeeOrgPost.class, query);

                            if (empPost != null) {
                                // 更新主岗位
                                empPost.setPostid(empOrgPost.getNewPostid());
                                empPost.setStatus(1);
                                empPost.setChangeDate(empOrgPost.getChangeDate());
                                empPostList.add(empPost);
                                // 流水
                                SysEmployeeChangeInfo empChgInfo = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(empOrgPost, empChgInfo);
                                empChgInfo.setId(TMUID.getUID());
                                empChgInfo.setDataType(2);
                                empChgInfo.setStatus(1);
                                empChgInfo.setChangeType(1);
                                empChgInfo.setUsed(1);
                                empChgInfo.setOldCode(empOrgPost.getOldPostid());
                                empChgInfo.setOldName(empOrgPost.getOldPostname());
                                empChgInfo.setNewCode(newPostid);
                                empChgInfo.setNewName(newPostname);
                                empChgInfo.setGroupid(groupid);
                                empChgInfo.setMemo("岗位调动：【" + empOrgPost.getEmpname() + "】岗位从【" + empOrgPost.getOldOrgname() + " - " + empChgInfo.getOldName() + "】变动为【" + empOrgPost.getNewOrgname() + " - " + empChgInfo.getNewName() + "】");
                                changeInfoList.add(empChgInfo);
                            }
                        }

                        // 删除未来预调动数据
//						List<SysEmployeeChangeTodo> todoDbList = empChgTodoServ.list(
//								new LambdaQueryWrapper<SysEmployeeChangeTodo>().eq(SysEmployeeChangeTodo::getUsed, 1)
//										.in(SysEmployeeChangeTodo::getDataType,
//												Stream.of(1, 2).collect(Collectors.toList()))
//										.eq(SysEmployeeChangeTodo::getStatus, 1)
//										.orderByAsc(SysEmployeeChangeTodo::getDataType));

                        // 删除未来预调动数据
                        Where query = Where.create();
                        query.eq(SysEmployeeChangeTodo::getUsed, 1).in(SysEmployeeChangeTodo::getDataType, Stream.of(1, 2).collect(Collectors.toList()).toArray()).eq(SysEmployeeChangeTodo::getStatus, 1);
                        Order order = Order.create().orderByAsc(SysEmployeeChangeTodo::getDataType);
                        List<SysEmployeeChangeTodo> todoDbList = entityService.queryList(SysEmployeeChangeTodo.class, query, order);

                        if (todoDbList != null) {
                            for (SysEmployeeChangeTodo chgTodo : todoDbList) {
                                chgTodo.setUsed(0);
                                chgTodo.setChangeType(9);
                                // 流水
                                SysEmployeeChangeInfo empChgInfoPost = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(chgTodo, empChgInfoPost);
                                empChgInfoPost.setMemo("【删除】" + empChgInfoPost.getMemo());
                                empChgInfoPost.setChangeType(9);
                                changeInfoList.add(empChgInfoPost);
                            }
                            updChangeTodoList.addAll(todoDbList);
                        }

                        // TODO 可在此处增加删除人员变动历史版本数据逻辑
                    }
                }
            }

            boolean flag = true;
            if (flag == true && changeTodoList.size() > 0) {
                int b = entityService.insertBatch(changeTodoList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info = "保存人员变动待办信息到数据库失败";
                    // res.fail(this.errCode, "保存人员变动待办信息到数据库失败");
                }
            }

//			if (flag == true && updChangeTodoList.size() > 0) {
//				int b = entityService.updateByIdBatch(updChangeTodoList);
//				flag = (b > 0 ? true : false);
//				if (flag == false) {
//					info += "保存人员变动待办信息到数据库失败";
//					// res.fail(this.errCode, "保存人员变动待办信息到数据库失败");
//				}
//			}

            if (flag == true && empOrgList.size() > 0) {
                int b = entityService.updateByIdBatch(empOrgList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info += "保存人员机构信息到数据库失败";
                    // res.fail(this.errCode, "保存人员机构信息到数据库失败");
                }
            }

            if (flag == true && empPostList.size() > 0) {
                int b = entityService.updateByIdBatch(empPostList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info += "保存人员岗位信息到数据库失败";
                    // res.fail(this.errCode, "保存人员岗位信息到数据库失败");
                }
            }

//			if (flag == true && changeInfoList.size() > 0) {
//				int b = entityService.insertBatch(changeInfoList);
//				flag = (b > 0 ? true : false);
//				if (flag == false) {
//					info += "保存人员变动流水信息到数据库失败";
//					// res.fail(this.errCode, "保存人员变动流水信息到数据库失败");
//				}
//			}
        } catch (Exception e) {
            log.error("", e);
            info = e.getMessage();
            // res.fail(this.errCode, "后台处理有误，请查看日志");
        }
        return info;
    }

    /**
     * 批量删除人员调动信息（只能删除预调动记录）
     *
     * @param listDto
     * @return
     * @category 批量删除人员调动信息
     */
    @Override
    public String deleteTransfer(List<EmployeeOrgPostDto> listDto) {
        // Res<?> res = new Res<>();
        String info = "";
        try {
            if (listDto == null || listDto.size() <= 0) {
                // res.fail(this.errCode, "删除失败，数据为空");
                // return res;
                return "删除失败，数据为空";
            }

            List<SysEmployeeChangeInfo> changeInfoList = new ArrayList<SysEmployeeChangeInfo>(); // 流水
            List<SysEmployeeChangeTodo> changeTodoList = new ArrayList<SysEmployeeChangeTodo>(); // 预变动
            List<SysEmployeeOrg> empOrgList = new ArrayList<SysEmployeeOrg>(); // 人员机构
            List<SysEmployeeOrgPost> empPostList = new ArrayList<SysEmployeeOrgPost>(); // 人员岗位

            LocalDate now = LocalDate.now();
            for (EmployeeOrgPostDto empOrgPost : listDto) {
                Boolean isAfter = DateUtil.isAfter(empOrgPost.getChangeDate(), now);
                if (isAfter != null) {
                    if (isAfter.booleanValue() == true) { // 预变动
                        String newOrgcode = empOrgPost.getNewOrgcode();
                        String newOrgname = empOrgPost.getNewOrgname();
                        String newPostid = empOrgPost.getNewPostid();
                        String newPostname = empOrgPost.getNewPostname();

                        // 默认为不选则继承当前机构和岗位
                        if (newOrgcode == null || "".equals(newOrgcode)) { // 前台没选机构
                            // 继承当前机构
                            newOrgcode = empOrgPost.getOldOrgcode();
                            newOrgname = empOrgPost.getOldOrgname();
                        }
                        if (newPostid == null || "".equals(newPostid)) { // 前台没选岗位
                            // 继承当前机构
                            newPostid = empOrgPost.getOldPostid();
                            newPostname = empOrgPost.getOldPostname();
                        }

                        if (empOrgPost.getEmpOrgTmuidTodo() != null && !"".equals(empOrgPost.getEmpOrgTmuidTodo())) {
//							SysEmployeeChangeTodo chgTodo = empChgTodoServ
//									.getOne(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//											.eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpOrgTmuidTodo()));

                            Where query = Where.create().eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpOrgTmuidTodo());
                            SysEmployeeChangeTodo chgTodo = entityService.queryObject(SysEmployeeChangeTodo.class, query);

                            if (chgTodo != null) {
                                chgTodo.setUsed(0);
                                chgTodo.setChangeType(9);
                                changeTodoList.add(chgTodo);
                                // 流水
                                SysEmployeeChangeInfo empChgInfoOrg = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(chgTodo, empChgInfoOrg);
                                empChgInfoOrg.setId(TMUID.getUID());
                                empChgInfoOrg.setUsed(1);
                                empChgInfoOrg.setMemo("【删除】机构调动（预）：【" + empOrgPost.getEmpname() + "】机构从【" + empChgInfoOrg.getOldName() + "】变动为【" + newOrgname + "】");
                                changeInfoList.add(empChgInfoOrg);
                            }
                        }
                        if (empOrgPost.getEmpPostTmuidTodo() != null && !"".equals(empOrgPost.getEmpPostTmuidTodo())) {
//							SysEmployeeChangeTodo chgTodo = empChgTodoServ
//									.getOne(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//											.eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpPostTmuidTodo()));

                            Where query = Where.create().eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpPostTmuidTodo());
                            SysEmployeeChangeTodo chgTodo = entityService.queryObject(SysEmployeeChangeTodo.class, query);

                            if (chgTodo != null) {
                                chgTodo.setUsed(0);
                                chgTodo.setChangeType(9);
                                changeTodoList.add(chgTodo);
                                // 流水
                                SysEmployeeChangeInfo empChgInfoPost = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(chgTodo, empChgInfoPost);
                                empChgInfoPost.setId(TMUID.getUID());
                                empChgInfoPost.setUsed(1);
                                empChgInfoPost.setMemo("【删除】岗位调动（预）：【" + empOrgPost.getEmpname() + "】岗位从【" + empOrgPost.getOldOrgname() + " - " + empChgInfoPost.getOldName() + "】变动为【" + newOrgname + " - " + newPostname + "】");
                                changeInfoList.add(empChgInfoPost);
                            }
                        }
                    } else { // 立即变动（目前只能删除预变动数据）
                        /*
                         * if (empOrgPost.getEmpOrgTmuid() != null &&
                         * !"".equals(empOrgPost.getEmpOrgTmuid())) { SysEmployeeOrg empOrg =
                         * empOrgServ.getOne(new LambdaQueryWrapper<SysEmployeeOrg>()
                         * .eq(SysEmployeeOrg::getId, empOrgPost.getEmpOrgTmuid())); if (empOrg != null)
                         * { // 删除主机构 empOrg.setUsed(0); empOrgList.add(empOrg); // 流水
                         * SysEmployeeChangeInfo empChgInfo = new SysEmployeeChangeInfo();
                         * empChgInfo.setDataType(1); empChgInfo.setStatus(1);
                         * empChgInfo.setChangeType(1); empChgInfo.setUsed(1);
                         * empChgInfo.setOldCode(empOrgPost.getOldOrgcode());
                         * empChgInfo.setOldName(empOrgPost.getOldOrgname());
                         * empChgInfo.setNewCode(empOrgPost.getNewOrgcode());
                         * empChgInfo.setNewName(empOrgPost.getNewOrgname());
                         * empChgInfo.setMemo(getChangeInfoMemo("机构", "调动（删除）", empOrgPost.getEmpname(),
                         * empChgInfo.getOldName(), empChgInfo.getNewName()));
                         * changeInfoList.add(empChgInfo); } }
                         *
                         * if (empOrgPost.getEmpPostTmuid() != null &&
                         * !"".equals(empOrgPost.getEmpPostTmuid())) { SysEmployeeOrgPost empPost =
                         * empPostServ.getOne(new LambdaQueryWrapper<SysEmployeeOrgPost>()
                         * .eq(SysEmployeeOrgPost::getId, empOrgPost.getEmpPostTmuid())); if (empPost !=
                         * null) { // 删除主岗位 empPost.setUsed(0); empPostList.add(empPost); // 流水
                         * SysEmployeeChangeInfo empChgInfo = new SysEmployeeChangeInfo();
                         * empChgInfo.setDataType(2); empChgInfo.setStatus(1);
                         * empChgInfo.setChangeType(1); empChgInfo.setUsed(1);
                         * empChgInfo.setOldCode(empOrgPost.getOldPostid());
                         * empChgInfo.setOldName(empOrgPost.getOldPostname());
                         * empChgInfo.setNewCode(empOrgPost.getNewPostid());
                         * empChgInfo.setNewName(empOrgPost.getNewPostname());
                         * empChgInfo.setMemo(getChangeInfoMemo("岗位", "调动（删除）", empOrgPost.getEmpname(),
                         * empChgInfo.getOldName(), empChgInfo.getNewName()));
                         * changeInfoList.add(empChgInfo); } }
                         */
                    }
                }
            }

            boolean flag = true;
            if (flag == true && changeTodoList.size() > 0) {
                int b = entityService.updateByIdBatch(changeTodoList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info = "保存人员变动待办信息到数据库失败";
                    // res.fail(this.errCode, "保存人员变动待办信息到数据库失败");
                }
            }
            if (flag == true && empOrgList.size() > 0) {
                int b = entityService.updateByIdBatch(empOrgList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    // res.fail(this.errCode, "保存人员机构信息到数据库失败");
                    info = "保存人员机构信息到数据库失败";
                }
            }
            if (flag == true && empPostList.size() > 0) {
                int b = entityService.updateByIdBatch(empPostList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    // res.fail(this.errCode, "保存人员岗位信息到数据库失败");
                    info = "保存人员岗位信息到数据库失败";
                }
            }
        } catch (Exception e) {
            log.error("", e);
            info = e.getMessage();
            // res.fail(this.errCode, "后台处理有误，请查看日志");
        }

        return info;
    }

    /**
     * 批量修改人员调动信息
     *
     * @param listDto
     * @return
     * @category 批量修改人员调动信息
     */
    @Override
    public String updateTransfer(List<EmployeeOrgPostDto> listDto) {
        // Res<?> res = new Res<>();
        String info = "";
        try {
            if (listDto == null || listDto.size() <= 0) {
                // res.fail(this.errCode, "修改失败，数据为空");
                // return res;
                return "修改失败，数据为空";
            }

            List<SysEmployeeChangeInfo> changeInfoList = new ArrayList<SysEmployeeChangeInfo>(); // 流水
            List<SysEmployeeChangeTodo> changeTodoList = new ArrayList<SysEmployeeChangeTodo>(); // 预变动
            List<SysEmployeeOrg> empOrgList = new ArrayList<SysEmployeeOrg>(); // 人员机构
            List<SysEmployeeOrgPost> empPostList = new ArrayList<SysEmployeeOrgPost>(); // 人员岗位

            LocalDate now = LocalDate.now();
            for (EmployeeOrgPostDto empOrgPost : listDto) {
                // 直接更新预变动数据
                String newOrgcode = empOrgPost.getNewOrgcode();
                String newOrgname = empOrgPost.getNewOrgname();
                String newPostid = empOrgPost.getNewPostid();
                String newPostname = empOrgPost.getNewPostname();

                // 默认为不选则继承当前机构和岗位
                if (newOrgcode == null || "".equals(newOrgcode)) { // 前台没选机构
                    // 继承当前机构
                    newOrgcode = empOrgPost.getOldOrgcode();
                    newOrgname = empOrgPost.getOldOrgname();
                }
                if (newPostid == null || "".equals(newPostid)) { // 前台没选岗位
                    // 继承当前机构
                    newPostid = empOrgPost.getOldPostid();
                    newPostname = empOrgPost.getOldPostname();
                }

                Boolean isAfter = DateUtil.isAfter(empOrgPost.getChangeDate(), now);
                if (isAfter != null) {
                    if (isAfter.booleanValue() == true) { // 预变动
                        if (empOrgPost.getEmpOrgTmuidTodo() != null && !"".equals(empOrgPost.getEmpOrgTmuidTodo())) {
//							SysEmployeeChangeTodo chgTodo = empChgTodoServ
//									.getOne(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//											.eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpOrgTmuidTodo()));

                            Where query = Where.create().eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpOrgTmuidTodo());
                            SysEmployeeChangeTodo chgTodo = entityService.queryObject(SysEmployeeChangeTodo.class, query);

                            if (chgTodo != null) {
                                chgTodo.setOldCode(empOrgPost.getOldOrgcode());
                                chgTodo.setOldName(empOrgPost.getOldOrgname());
                                chgTodo.setNewCode(newOrgcode);
                                chgTodo.setNewName(newOrgname);
                                chgTodo.setMemo("机构调动（预）：【" + empOrgPost.getEmpname() + "】机构从【" + chgTodo.getOldName() + "】变动为【" + newOrgname + "】");
                                chgTodo.setChangeType(2);
                                changeTodoList.add(chgTodo);
                                // 流水
                                SysEmployeeChangeInfo empChgInfoOrg = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(chgTodo, empChgInfoOrg);
                                empChgInfoOrg.setId(TMUID.getUID());
                                empChgInfoOrg.setUsed(1);
                                empChgInfoOrg.setMemo("【修改】机构调动（预）：【" + empOrgPost.getEmpname() + "】机构从【" + empChgInfoOrg.getOldName() + "】变动为【" + newOrgname + "】");
                                changeInfoList.add(empChgInfoOrg);
                            }
                        }
                        if (empOrgPost.getEmpPostTmuidTodo() != null && !"".equals(empOrgPost.getEmpPostTmuidTodo())) {
//							SysEmployeeChangeTodo chgTodo = empChgTodoServ
//									.getOne(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//											.eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpPostTmuidTodo()));
                            Where query = Where.create().eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpPostTmuidTodo());
                            SysEmployeeChangeTodo chgTodo = entityService.queryObject(SysEmployeeChangeTodo.class, query);
                            if (chgTodo != null) {
                                chgTodo.setOldCode(empOrgPost.getOldPostid());
                                chgTodo.setOldName(empOrgPost.getOldPostname());
                                chgTodo.setNewCode(newPostid);
                                chgTodo.setNewName(newPostname);
                                chgTodo.setMemo("岗位调动（预）：【" + empOrgPost.getEmpname() + "】岗位从【" + empOrgPost.getOldOrgname() + " - " + chgTodo.getOldName() + "】变动为【" + newOrgname + " - " + newPostname + "】");
                                chgTodo.setChangeType(2);

                                changeTodoList.add(chgTodo);
                                // 流水
                                SysEmployeeChangeInfo empChgInfoPost = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(chgTodo, empChgInfoPost);
                                empChgInfoPost.setId(TMUID.getUID());
                                empChgInfoPost.setUsed(1);
                                empChgInfoPost.setMemo("【修改】岗位调动（预）：【" + empOrgPost.getEmpname() + "】岗位从【" + empOrgPost.getOldOrgname() + " - " + empChgInfoPost.getOldName() + "】变动为【" + newOrgname + " - " + newPostname + "】");
                                changeInfoList.add(empChgInfoPost);
                            }
                        }
                    } else { // 立即生效（变动时间往前调了）
                        // 【校验】前台交互提示操作
                        if (empOrgPost.getDoCover() == null || empOrgPost.getDoCover().intValue() != 1) {
                            // Res<JSONObject> resObj = new Res<JSONObject>();
                            // JSONObject result = new JSONObject();
                            // result.put("confirm", true);
                            // resObj.fail();
                            // resObj.setResult(result);
                            info = "【" + empOrgPost.getEmpname() + "】调动到历史日期，系统将从调动日期开始到未来的调动信息全部清除。请谨慎操作。";
                            return info;
                            // return resObj;
                        }

                        // 更新当前机构岗位数据
                        // 机构变动
                        if (empOrgPost.getNewOrgcode() != null && !"".equals(empOrgPost.getNewOrgcode())) {
                            // 查询当前主机构
//							SysEmployeeOrg empOrg = empOrgServ.getOne(new LambdaQueryWrapper<SysEmployeeOrg>()
//									.eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 1)
//									.eq(SysEmployeeOrg::getEmpid, empOrgPost.getEmpid()));

                            Where query = Where.create().eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 1).eq(SysEmployeeOrg::getEmpid, empOrgPost.getEmpid());
                            SysEmployeeOrg empOrg = entityService.queryObject(SysEmployeeOrg.class, query);

                            if (empOrg != null) {
                                // 更新主机构
                                empOrg.setOrgcode(empOrgPost.getNewOrgcode());
                                empOrg.setStatus(1);
                                empOrg.setChangeDate(empOrgPost.getChangeDate());
                                empOrgList.add(empOrg);
                                // 流水
                                SysEmployeeChangeInfo empChgInfo = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(empOrgPost, empChgInfo);
                                empChgInfo.setId(TMUID.getUID());
                                empChgInfo.setDataType(1);
                                empChgInfo.setStatus(1);
                                empChgInfo.setChangeType(1);
                                empChgInfo.setUsed(1);
                                empChgInfo.setOldCode(empOrgPost.getOldOrgcode());
                                empChgInfo.setOldName(empOrgPost.getOldOrgname());
                                empChgInfo.setNewCode(newOrgcode);
                                empChgInfo.setNewName(newOrgname);
                                empChgInfo.setChangeType(2);
                                empChgInfo.setMemo("【修改】机构调动：【" + empOrgPost.getEmpname() + "】机构从【" + empChgInfo.getOldName() + "】变动为【" + empChgInfo.getNewName() + "】");
                                changeInfoList.add(empChgInfo);
                            }
                        }
                        // 岗位变动
                        if (empOrgPost.getNewPostid() != null && !"".equals(empOrgPost.getNewPostid())) {
                            // 查询当前主岗位
//							SysEmployeeOrgPost empPost = empPostServ.getOne(new LambdaQueryWrapper<SysEmployeeOrgPost>()
//									.eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 1)
//									.eq(SysEmployeeOrgPost::getEmpid, empOrgPost.getEmpid()));

                            // 查询当前主岗位
                            Where query = Where.create().eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 1).eq(SysEmployeeOrgPost::getEmpid, empOrgPost.getEmpid());
                            SysEmployeeOrgPost empPost = entityService.queryObject(SysEmployeeOrgPost.class, query);

                            if (empPost != null) {
                                // 更新主岗位
                                empPost.setPostid(empOrgPost.getNewPostid());
                                empPost.setStatus(1);
                                empPost.setChangeDate(empOrgPost.getChangeDate());
                                empPostList.add(empPost);
                                // 流水
                                SysEmployeeChangeInfo empChgInfo = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(empOrgPost, empChgInfo);
                                empChgInfo.setId(TMUID.getUID());
                                empChgInfo.setDataType(2);
                                empChgInfo.setStatus(1);
                                empChgInfo.setChangeType(1);
                                empChgInfo.setUsed(1);
                                empChgInfo.setOldCode(empOrgPost.getOldPostid());
                                empChgInfo.setOldName(empOrgPost.getOldPostname());
                                empChgInfo.setNewCode(newPostid);
                                empChgInfo.setNewName(newPostname);
                                empChgInfo.setChangeType(2);
                                empChgInfo.setMemo("【修改】岗位调动：【" + empOrgPost.getEmpname() + "】岗位从【" + empOrgPost.getOldOrgname() + " - " + empChgInfo.getOldName() + "】变动为【" + empOrgPost.getNewOrgname() + " - " + empChgInfo.getNewName() + "】");
                                changeInfoList.add(empChgInfo);
                            }
                        }

                        // 删除未来预调动数据
//						List<SysEmployeeChangeTodo> todoDbList = empChgTodoServ.list(
//								new LambdaQueryWrapper<SysEmployeeChangeTodo>().eq(SysEmployeeChangeTodo::getUsed, 1)
//										.in(SysEmployeeChangeTodo::getDataType,
//												Stream.of(1, 2).collect(Collectors.toList()))
//										.eq(SysEmployeeChangeTodo::getStatus, 1)
//										.orderByAsc(SysEmployeeChangeTodo::getDataType));

                        Where query = Where.create().eq(SysEmployeeChangeTodo::getUsed, 1).in(SysEmployeeChangeTodo::getDataType, Stream.of(1, 2).collect(Collectors.toList()).toArray()).eq(SysEmployeeChangeTodo::getStatus, 1);
                        Order order = Order.create().orderByAsc(SysEmployeeChangeTodo::getDataType);
                        List<SysEmployeeChangeTodo> todoDbList = entityService.queryList(SysEmployeeChangeTodo.class, query, order);

                        if (todoDbList != null) {
                            for (SysEmployeeChangeTodo chgTodo : todoDbList) {
                                chgTodo.setUsed(0);
                                chgTodo.setChangeType(9);
                                // 流水
                                SysEmployeeChangeInfo empChgInfoPost = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(chgTodo, empChgInfoPost);
                                empChgInfoPost.setId(TMUID.getUID());
                                empChgInfoPost.setMemo("【删除】" + empChgInfoPost.getMemo());
                                changeInfoList.add(empChgInfoPost);
                            }
                            changeTodoList.addAll(todoDbList);
                        }

                        // TODO 可在此处增加删除人员变动历史版本数据逻辑
                    }
                }
            }

            boolean flag = true;
            if (flag == true && changeTodoList.size() > 0) {
                int b = entityService.updateByIdBatch(changeTodoList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    // res.fail(this.errCode, "保存人员变动待办信息到数据库失败");
                    info = "保存人员变动待办信息到数据库失败";
                }
            }

            if (flag == true && empOrgList.size() > 0) {
                int b = entityService.updateByIdBatch(empOrgList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    // res.fail(this.errCode, "保存人员机构信息到数据库失败");
                    info = "保存人员机构信息到数据库失败";
                }
            }

            if (flag == true && empPostList.size() > 0) {
                int b = entityService.updateByIdBatch(empPostList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    // res.fail(this.errCode, "保存人员岗位信息到数据库失败");
                    info = "保存人员岗位信息到数据库失败";
                }
            }

//			if (flag == true && changeInfoList.size() > 0) {
//				int b = entityService.insertBatch(changeInfoList);
//				flag = (b > 0 ? true : false);
//				if (flag == false) {
//					// res.fail(this.errCode, "保存人员变动流水信息到数据库失败");
//					info = "保存人员变动流水信息到数据库失败";
//				}
//			}
        } catch (Exception e) {
            log.error("", e);
            info = e.getMessage();
        }

        return info;
    }

    /**
     * 获取人员调动信息（历史+当前+未来）
     *
     * @param paramDto
     * @return
     * @category 获取人员调动信息
     */
    @Override
    public List<EmployeeOrgPostVo> getTransfer(EmpTransferParamDto paramDto) throws Exception {
        // Res<List<EmployeeOrgPostVo>> res = new Res<List<EmployeeOrgPostVo>>();
        List<EmployeeOrgPostVo> list = new ArrayList<EmployeeOrgPostVo>();
        // res.setResult(list);

        if (paramDto == null) {
            // res.fail(this.errCode, "参数为空");
            return null;
        }

        List<SysEmployeeOrg> orgList = new ArrayList<SysEmployeeOrg>();
        List<SysEmployeeOrgPost> postList = new ArrayList<SysEmployeeOrgPost>();

        // 获取此人未来待生效主部门与岗位（待变动）
//			List<SysEmployeeChangeTodo> todoDbList = empChgTodoServ
//					.list(new LambdaQueryWrapper<SysEmployeeChangeTodo>().eq(SysEmployeeChangeTodo::getUsed, 1)
//							.in(SysEmployeeChangeTodo::getDataType, Stream.of(1, 2).collect(Collectors.toList()))
//							.eq(SysEmployeeChangeTodo::getStatus, 1).orderByDesc(SysEmployeeChangeTodo::getChangeDate)
//							.orderByAsc(SysEmployeeChangeTodo::getDataType));
        Where queryECT = Where.create().eq(SysEmployeeChangeTodo::getUsed, 1).in(SysEmployeeChangeTodo::getDataType, Stream.of(1, 2).collect(Collectors.toList()).toArray()).eq(SysEmployeeChangeTodo::getStatus, 1);
        Order orderECT = Order.create().orderByDesc(SysEmployeeChangeTodo::getChangeDate).orderByAsc(SysEmployeeChangeTodo::getDataType);
        List<SysEmployeeChangeTodo> todoDbList = entityService.queryList(SysEmployeeChangeTodo.class, queryECT, orderECT);

        if (todoDbList != null && todoDbList.size() > 0) {
            Map<String, EmployeeOrgPostVo> oneRecMap = new LinkedHashMap<String, EmployeeOrgPostVo>();

            for (SysEmployeeChangeTodo chgTodo : todoDbList) {
                String changeDay = DateUtil.formatDate(chgTodo.getChangeDate());
                // 初始化记录
                EmployeeOrgPostVo vo = oneRecMap.get(changeDay);
                if (vo == null) {
                    vo = new EmployeeOrgPostVo();
                    vo.setEmpid(paramDto.getEmpid());
                    vo.setStatus(1);
                    vo.setTimeStatus(1);
                    oneRecMap.put(changeDay, vo);
                }
                // 填充记录
                if (chgTodo.getDataType() != null) {
                    if (chgTodo.getDataType().intValue() == 1) { // 机构
                        vo.setEmpOrgTmuidTodo(chgTodo.getId());
                        vo.setOldOrgcode(chgTodo.getOldCode());
                        vo.setOldOrgname(chgTodo.getOldName());
                        vo.setNewOrgcode(chgTodo.getNewCode());
                        vo.setNewOrgname(chgTodo.getNewName());
                    } else if (chgTodo.getDataType().intValue() == 2) { // 岗位
                        vo.setEmpPostTmuidTodo(chgTodo.getId());
                        vo.setOldPostid(chgTodo.getOldCode());
                        vo.setOldPostname(chgTodo.getOldName());
                        vo.setNewPostid(chgTodo.getNewCode());
                        vo.setNewPostname(chgTodo.getNewName());
                    } else {
                        continue;
                    }
                    vo.setChangeDate(chgTodo.getChangeDate());
                }
            }
            // 整合记录
            oneRecMap.keySet().forEach(key -> {
                EmployeeOrgPostVo vo = oneRecMap.get(key);
                list.add(vo);
            });
        }

        // 获取此人当前主部门与岗位
        EmployeeOrgPostVo curOrgPostVo = new EmployeeOrgPostVo();
        curOrgPostVo.setEmpid(paramDto.getEmpid());
        curOrgPostVo.setStatus(1);
        curOrgPostVo.setTimeStatus(0);
        // 获取主部门
//			SysEmployeeOrg empOrg = empOrgServ
//					.getOne(new LambdaQueryWrapper<SysEmployeeOrg>().eq(SysEmployeeOrg::getUsed, 1)
//							.eq(SysEmployeeOrg::getStatus, 1).eq(SysEmployeeOrg::getEmpid, paramDto.getEmpid()));

        Where queryEO = Where.create().eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 1).eq(SysEmployeeOrg::getEmpid, paramDto.getEmpid());
        SysEmployeeOrg empOrg = entityService.queryObject(SysEmployeeOrg.class, queryEO);

        if (empOrg != null) {
            curOrgPostVo.setEmpOrgTmuid(empOrg.getId());
            curOrgPostVo.setNewOrgcode(empOrg.getOrgcode());
            curOrgPostVo.setOldOrgcode(empOrg.getOrgcode());
            curOrgPostVo.setChangeDate(empOrg.getChangeDate());
            // 获取主部门名称
            orgList.add(empOrg);
            List<SysOrg> sysOrgList = extOperServ.getExtOrgList(orgList);
            if (sysOrgList != null && sysOrgList.size() > 0) {
                curOrgPostVo.setNewOrgname(sysOrgList.get(0).getOrgname());
                curOrgPostVo.setOldOrgname(sysOrgList.get(0).getOrgname());
            }
        }
        // 获取主岗位
//			SysEmployeeOrgPost empPost = empPostServ.getOne(new LambdaQueryWrapper<SysEmployeeOrgPost>()
//					.eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 1)
//					.eq(SysEmployeeOrgPost::getEmpid, paramDto.getEmpid()));

        Where queryEOP = Where.create().eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 1).eq(SysEmployeeOrgPost::getEmpid, paramDto.getEmpid());
        SysEmployeeOrgPost empPost = entityService.queryObject(SysEmployeeOrgPost.class, queryEOP);

        if (empPost != null) {
            curOrgPostVo.setEmpPostTmuid(empPost.getId());
            curOrgPostVo.setNewPostid(empPost.getPostid());
            curOrgPostVo.setOldPostid(empPost.getPostid());
            curOrgPostVo.setChangeDate(empPost.getChangeDate());
            // 获取主岗位名称
            postList.add(empPost);
            List<PostVo> postVoList = extOperServ.getExtPostList(postList);
            if (postVoList != null && postVoList.size() > 0) {
                curOrgPostVo.setNewPostname(postVoList.get(0).getName());
                curOrgPostVo.setOldPostname(postVoList.get(0).getName());
            }
        }
        // 整合记录
        list.add(curOrgPostVo);

        // TODO 获取此人历史生效主部门与岗位（有效的变动历史）

        return list;
    }

    /**
     * 获取人员调动流水信息
     *
     * @param paramDto
     * @return
     * @category 获取人员调动流水信息
     */
    public List<EmployeeDiaryVo> getTransferDiary(EmpTransferParamDto paramDto) throws Exception {
        // Res<List<EmployeeDiaryVo>> res = new Res<List<EmployeeDiaryVo>>();
        List<EmployeeDiaryVo> list = new ArrayList<EmployeeDiaryVo>();
        // res.setResult(list);

        if (paramDto == null) {
//			res.error509("参数为空");
            // res.fail(this.errCode, "参数为空");
            return null;
        }

        Where query = Where.create().eq(SysEmployeeChangeInfo::getUsed, 1).eq(SysEmployeeChangeInfo::getStatus, 1).eq(SysEmployeeChangeInfo::getEmpid, paramDto.getEmpid());
        Order order = Order.create().orderByDesc(SysEmployeeChangeInfo::getCreateTime);

        // 创建分页对象
        Pagination<?> page = Pagination.create(paramDto.getCurrent(), paramDto.getSize());
        // 读取总记录数量
        long total = entityService.queryCount(SysEmployeeChangeInfo.class, query);
        // 读取记录结果
        List<SysEmployeeChangeInfo> poList = entityService.queryList(SysEmployeeChangeInfo.class, query, order, page);

        if (poList != null && poList.size() > 0) {
            for (SysEmployeeChangeInfo po : poList) {
                EmployeeDiaryVo diaryVo = new EmployeeDiaryVo();
                BeanUtils.copyProperties(po, diaryVo);
                diaryVo.setRecordCount(total);
                list.add(diaryVo);
            }
            // res.setResult(list);
        }

        return list;
    }

    /**
     * 批量添加人员兼岗信息
     *
     * @param listDto
     * @return
     * @category 批量添加人员兼岗信息
     */
    @Override
    public String addPartimePost(List<EmployeeOrgPostDto> listDto) {
        // Res<?> res = new Res<>();
        String info = "";
        try {
            if (listDto == null || listDto.size() <= 0) {
                // res.fail(this.errCode, "添加失败，数据为空");
                // return res;
                return "添加失败，数据为空";
            }

            List<SysEmployeeChangeInfo> changeInfoList = new ArrayList<SysEmployeeChangeInfo>(); // 流水
            List<SysEmployeeChangeTodo> changeTodoList = new ArrayList<SysEmployeeChangeTodo>(); // 预变动
            List<SysEmployeeOrg> addEmpOrgList = new ArrayList<SysEmployeeOrg>(); // 人员机构
            List<SysEmployeeOrgPost> addEmpPostList = new ArrayList<SysEmployeeOrgPost>(); // 人员岗位

            LocalDate now = LocalDate.now();
            for (EmployeeOrgPostDto empOrgPost : listDto) {
                // 如果未选择截止日期，系统默认赋值9999-01-01，方便后续查询
                if (empOrgPost.getEndDate() == null) {
                    empOrgPost.setEndDate(DateUtil.parseDate(g_max_datetime));
                }

                String newOrgcode = empOrgPost.getNewOrgcode();
                String newOrgname = empOrgPost.getNewOrgname();
                String newPostid = empOrgPost.getNewPostid();
                String newPostname = empOrgPost.getNewPostname();

                // 默认为不选则继承当前机构和岗位
                if (newOrgcode == null || "".equals(newOrgcode)) { // 前台没选机构
                    // 继承当前机构
                    newOrgcode = empOrgPost.getOldOrgcode();
                    newOrgname = empOrgPost.getOldOrgname();
                }
                if (newPostid == null || "".equals(newPostid)) { // 前台没选岗位
                    // 继承当前机构
                    newPostid = empOrgPost.getOldPostid();
                    newPostname = empOrgPost.getOldPostname();
                }

                Boolean isAfter = DateUtil.isAfter(empOrgPost.getChangeDate(), now);
                if (isAfter != null) {
                    if (isAfter.booleanValue() == true) { // 预变动
                        // String newOrgcodeCond = newOrgcode;
                        // String newPostidCond = newPostid;
//						// 【校验】单个人预变动记录最多只能有一条（相同机构+岗位）
//						List<SysEmployeeChangeTodo> todoDbList = empChgTodoServ
//								.list(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//										.eq(SysEmployeeChangeTodo::getUsed, 1).eq(SysEmployeeChangeTodo::getStatus, 2)
//										// Java8在 lambda 表达式中使用局部变量会提示：Local variable flag defined in an enclosing scope
//										// must be final or effectively final
//										// 这是因为你使用的局部变量在初始化后，又对这个变量进行了赋值。赋值后会认为这个变量不是final了，所以报错
//										// 1)即将你想要使用的局部变量，在使用前赋值给一个新的变量，这样java8会认为这个新的变量是final，没有变化的，可以使用
//										// 2)将你需要的值放到一个数组里，也可以使用。此外放到map或是list里都可以
//										// 参考文献：https://blog.csdn.net/weixin_38883338/article/details/89195749
//										.and(wrapper1 -> wrapper1
//												.and(wrapper2 -> wrapper2.eq(SysEmployeeChangeTodo::getDataType, 1)
//														.eq(SysEmployeeChangeTodo::getNewCode, newOrgcodeCond))
//												.or()
//												.and(wrapper2 -> wrapper2.eq(SysEmployeeChangeTodo::getDataType, 2)
//														.eq(SysEmployeeChangeTodo::getNewCode, newPostidCond)))
//										.orderByAsc(SysEmployeeChangeTodo::getDataType));

                        // TODO 嵌套语句不会写
                        List<SysEmployeeChangeTodo> todoDbList = null;

                        if (todoDbList != null && todoDbList.size() > 0) {
                            String dbOrgname = "";
                            String dbPostname = "";
                            String dbChangeDate = "";
                            for (SysEmployeeChangeTodo todoDb : todoDbList) {
                                if (todoDb.getDataType().intValue() == 1) { // 机构
                                    dbOrgname = todoDb.getNewName();
                                } else if (todoDb.getDataType().intValue() == 2) { // 岗位
                                    dbPostname = todoDb.getNewName();
                                }
                                dbChangeDate = DateUtil.formatDate(todoDb.getChangeDate());
                            }
                            // res.fail(this.errCode,"【" + dbChangeDate + "】【" + dbOrgname + "】【" +
                            // dbPostname + "】预兼岗已存在！");
                            // return res;
                            return "【" + dbChangeDate + "】【" + dbOrgname + "】【" + dbPostname + "】预兼岗已存在！";
                        }

                        // TODO 可以在此处增加机构预删除判断逻辑

                        // 机构变动（机构岗位只要有1个变动，就都存到预变动表里，1对1关系，方便后续查询处理）
                        SysEmployeeChangeTodo empChgTodoOrg = new SysEmployeeChangeTodo(); // 机构变动
                        BeanUtils.copyProperties(empOrgPost, empChgTodoOrg);
                        empChgTodoOrg.setId(TMUID.getUID());
                        empChgTodoOrg.setDataType(1);
                        empChgTodoOrg.setStatus(2);
                        empChgTodoOrg.setChangeType(1);
                        empChgTodoOrg.setUsed(1);
                        empChgTodoOrg.setOldCode(empOrgPost.getOldOrgcode());
                        empChgTodoOrg.setOldName(empOrgPost.getOldOrgname());
                        empChgTodoOrg.setNewCode(newOrgcode);
                        empChgTodoOrg.setNewName(newOrgname);
                        empChgTodoOrg.setGroupid(TMUID.getUID());
                        // empChgTodoOrg.setMemo("兼岗机构（预变动）：【" + empOrgPost.getEmpname() + "】机构从【"
                        // + empChgTodoOrg.getOldName() + "】变动为【" + newOrgname + "】");
                        empChgTodoOrg.setMemo("兼岗部门变动（预）：【" + empOrgPost.getEmpname() + "】兼岗部门【" + newOrgname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                        changeTodoList.add(empChgTodoOrg);
                        // 机构变动流水（当机构真正变动时再写流水）
                        if (empOrgPost.getNewOrgcode() != null && !"".equals(empOrgPost.getNewOrgcode())) {
                            SysEmployeeChangeInfo empChgInfoOrg = new SysEmployeeChangeInfo();
                            BeanUtils.copyProperties(empChgTodoOrg, empChgInfoOrg);
                            empChgInfoOrg.setId(TMUID.getUID());
                            changeInfoList.add(empChgInfoOrg);
                        }

                        // 岗位变动（机构岗位只要有1个变动，就都存到预变动表里，1对1关系，方便后续查询处理）
                        SysEmployeeChangeTodo empChgTodoPost = new SysEmployeeChangeTodo(); // 岗位变动
                        BeanUtils.copyProperties(empOrgPost, empChgTodoPost);
                        empChgTodoPost.setId(TMUID.getUID());
                        empChgTodoPost.setDataType(2);
                        empChgTodoPost.setStatus(2);
                        empChgTodoPost.setChangeType(1);
                        empChgTodoPost.setUsed(1);
                        empChgTodoPost.setOldCode(empOrgPost.getOldPostid());
                        empChgTodoPost.setOldName(empOrgPost.getOldPostname());
                        empChgTodoPost.setNewCode(newPostid);
                        empChgTodoPost.setNewName(newPostname);
                        empChgTodoPost.setGroupid(empChgTodoOrg.getGroupid());
                        // empChgTodoPost.setMemo("兼职岗位（预变动）：【" + empOrgPost.getEmpname() + "】岗位从【"
                        // + empOrgPost.getOldOrgname() + " - " + empChgTodoPost.getOldName() + "】变动为【"
                        // + newOrgname + " - " + newPostname + "】");
                        empChgTodoPost.setMemo("兼职岗位变动（预）：【" + empOrgPost.getEmpname() + "】兼职岗位【" + newOrgname + " - " + newPostname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                        changeTodoList.add(empChgTodoPost);
                        // 岗位变动流水（当岗位真正变动时再写流水）
                        if (empOrgPost.getNewPostid() != null && !"".equals(empOrgPost.getNewPostid())) {
                            SysEmployeeChangeInfo empChgInfoPost = new SysEmployeeChangeInfo();
                            BeanUtils.copyProperties(empChgTodoPost, empChgInfoPost);
                            empChgInfoPost.setId(TMUID.getUID());
                            changeInfoList.add(empChgInfoPost);
                        }
                    } else { // 立即变动
                        // 【校验】新增数据的开始截止时间与历史+当前+未来的数据时间段不能有重叠（相同部门+岗位）
                        // String newOrgcodeCond = newOrgcode;
                        // String newPostidCond = newPostid;
//						// 检查当前数据（机构+岗位）：获取与当前记录时间重叠的岗位记录
//						SysEmployeeOrgPost empOrgCheck = empPostServ
//								.getOne(new LambdaQueryWrapper<SysEmployeeOrgPost>().eq(SysEmployeeOrgPost::getUsed, 1)
//										.eq(SysEmployeeOrgPost::getStatus, 2)
//										.eq(SysEmployeeOrgPost::getEmpid, empOrgPost.getEmpid())
//										.and(wrapper1 -> wrapper1
//												.and(wrapper2 -> wrapper2
//														.le(SysEmployeeOrgPost::getChangeDate,
//																empOrgPost.getChangeDate())
//														.ge(SysEmployeeOrgPost::getEndDate, empOrgPost.getChangeDate()))
//												.or()
//												.and(wrapper2 -> wrapper2
//														.le(SysEmployeeOrgPost::getChangeDate, empOrgPost.getEndDate())
//														.ge(SysEmployeeOrgPost::getEndDate, empOrgPost.getEndDate()))
//												.or()
//												.and(wrapper2 -> wrapper2
//														.gt(SysEmployeeOrgPost::getChangeDate,
//																empOrgPost.getChangeDate())
//														.lt(SysEmployeeOrgPost::getEndDate, empOrgPost.getEndDate())))
//										.eq(SysEmployeeOrgPost::getOrgcode, newOrgcodeCond)
//										.eq(SysEmployeeOrgPost::getPostid, newPostidCond));

                        // TODO 嵌套语句不会写
                        SysEmployeeOrgPost empOrgCheck = null;

                        if (empOrgCheck != null) {
                            // 制作截止时间描述
                            String endDtStr = DateUtil.formatDate(empOrgCheck.getEndDate());
                            if (g_max_date.equals(endDtStr)) { // 未来
                                endDtStr = "未来";
                            }
                            // 获取机构名
                            String orgName = "";
                            List<SysOrg> sysOrgList = extOperServ.getExtOrgListByCode(Stream.of(empOrgCheck.getOrgcode()).collect(Collectors.toList()));
                            if (sysOrgList != null && sysOrgList.size() > 0) {
                                orgName = sysOrgList.get(0).getOrgname();
                            }
                            // 获取岗位名
                            String postName = "";
                            List<PostVo> extPostList = extOperServ.getExtPostListById(Stream.of(empOrgCheck.getPostid()).collect(Collectors.toList()));
                            if (extPostList != null && extPostList.size() > 0) {
                                postName = extPostList.get(0).getName();
                            }
                            // 返回提示
                            // res.fail(this.errCode, "【" + DateUtil.formatDate(empOrgCheck.getChangeDate())
                            // + "~"+ endDtStr + "】【" + orgName + "】【" + postName + "】已存在");
                            // return res;
                            return "【" + DateUtil.formatDate(empOrgCheck.getChangeDate()) + "~" + endDtStr + "】【" + orgName + "】【" + postName + "】已存在";
                        }

//						// 检查未来数据：获取与当前记录时间重叠的机构+岗位记录
//						List<SysEmployeeChangeTodo> todoDbList = empChgTodoServ
//								.list(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//										.eq(SysEmployeeChangeTodo::getUsed, 1).eq(SysEmployeeChangeTodo::getStatus, 2)
//										.and(wrapper1 -> wrapper1
//												.and(wrapper2 -> wrapper2.eq(SysEmployeeChangeTodo::getDataType, 1)
//														.eq(SysEmployeeChangeTodo::getNewCode, newOrgcodeCond))
//												.or()
//												.and(wrapper2 -> wrapper2.eq(SysEmployeeChangeTodo::getDataType, 2)
//														.eq(SysEmployeeChangeTodo::getNewCode, newPostidCond)))
//										.and(wrapper1 -> wrapper1
//												.and(wrapper2 -> wrapper2
//														.le(SysEmployeeChangeTodo::getChangeDate,
//																empOrgPost.getChangeDate())
//														.ge(SysEmployeeChangeTodo::getEndDate,
//																empOrgPost.getChangeDate()))
//												.or()
//												.and(wrapper2 -> wrapper2
//														.le(SysEmployeeChangeTodo::getChangeDate,
//																empOrgPost.getEndDate())
//														.ge(SysEmployeeChangeTodo::getEndDate, empOrgPost.getEndDate()))
//												.or()
//												.and(wrapper2 -> wrapper2
//														.gt(SysEmployeeChangeTodo::getChangeDate,
//																empOrgPost.getChangeDate())
//														.lt(SysEmployeeChangeTodo::getEndDate,
//																empOrgPost.getEndDate())))
//										.orderByAsc(SysEmployeeChangeTodo::getDataType));

                        // TODO 嵌套语句不会写
                        List<SysEmployeeChangeTodo> todoDbList = null;

                        if (todoDbList != null && todoDbList.size() > 0) {
                            // 制作截止时间描述
                            String endDtStr = "";

                            String dbOrgname = "";
                            String dbPostname = "";
                            String dbChangeDate = "";
                            for (SysEmployeeChangeTodo todoDb : todoDbList) {
                                if (todoDb.getDataType().intValue() == 1) { // 机构
                                    dbOrgname = todoDb.getNewName();
                                } else if (todoDb.getDataType().intValue() == 2) { // 岗位
                                    dbPostname = todoDb.getNewName();
                                }
                                dbChangeDate = DateUtil.formatDate(todoDb.getChangeDate());
                                endDtStr = DateUtil.formatDate(todoDb.getEndDate());
                                if (g_max_date.equals(endDtStr)) { // 未来
                                    endDtStr = "未来";
                                }
                            }
                            // res.fail(this.errCode, "【" + dbChangeDate + "~" + endDtStr + "】【" + dbOrgname
                            // + "】【"+ dbPostname + "】已存在");
                            // return res;
                            return "【" + dbChangeDate + "~" + endDtStr + "】【" + dbOrgname + "】【" + dbPostname + "】已存在";
                        }

                        // TODO 【校验】检查历史版本变动数据

                        // 通过校验，开始处理机构岗位变动
                        // 判断记录是否为已过期记录（截止时间在今天以前）
                        isAfter = DateUtil.isBefore(empOrgPost.getEndDate(), now);
                        if (isAfter != null) {
                            if (isAfter.booleanValue() == true) { // 属于历史变动数据
                                // TODO 【待办】人员历史版本表
                            } else { // 可用数据（未过期数据）
                                // TODO 处理当前状态表
                                // 查询机构表中是否有此机构
//								SysEmployeeOrg empOrg = empOrgServ.getOne(new LambdaQueryWrapper<SysEmployeeOrg>()
//										.eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 2)
//										.eq(SysEmployeeOrg::getOrgcode, newOrgcode)
//										.eq(SysEmployeeOrg::getEmpid, empOrgPost.getEmpid()));

                                Where queryEO = Where.create().eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 2).eq(SysEmployeeOrg::getOrgcode, newOrgcode).eq(SysEmployeeOrg::getEmpid, empOrgPost.getEmpid());
                                SysEmployeeOrg empOrg = entityService.queryObject(SysEmployeeOrg.class, queryEO);

                                if (empOrg == null) {
                                    empOrg = new SysEmployeeOrg();
                                    empOrg.setOrgcode(newOrgcode);
                                    empOrg.setId(TMUID.getUID());
                                    empOrg.setEmpid(empOrgPost.getEmpid());
                                    empOrg.setStatus(2);
                                    // empOrg.setTmSort();
                                    empOrg.setUsed(1);
                                    empOrg.setChangeDate(empOrgPost.getChangeDate());
                                    empOrg.setEndDate(empOrgPost.getEndDate());
                                    addEmpOrgList.add(empOrg);
                                }
                                // 查询兼岗表中是否有此记录
//								SysEmployeeOrgPost empPost = empPostServ
//										.getOne(new LambdaQueryWrapper<SysEmployeeOrgPost>()
//												.eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 2)
//												.eq(SysEmployeeOrgPost::getOrgcode, newOrgcode)
//												.eq(SysEmployeeOrgPost::getPostid, newPostid)
//												.eq(SysEmployeeOrgPost::getEmpid, empOrgPost.getEmpid()));

                                Where queryEOP = Where.create().eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 2).eq(SysEmployeeOrgPost::getOrgcode, newOrgcode).eq(SysEmployeeOrgPost::getPostid, newPostid).eq(SysEmployeeOrgPost::getEmpid, empOrgPost.getEmpid());
                                SysEmployeeOrgPost empPost = entityService.queryObject(SysEmployeeOrgPost.class, queryEOP);

                                if (empPost == null) {
                                    empPost = new SysEmployeeOrgPost();
                                    empPost.setId(TMUID.getUID());
                                    empPost.setEmpid(empOrgPost.getEmpid());
                                    empPost.setStatus(2);
                                    empPost.setOrgcode(newOrgcode);
                                    empPost.setPostid(newPostid);
                                    // empPost.setTmSort();
                                    empPost.setUsed(1);
                                    empPost.setChangeDate(empOrgPost.getChangeDate());
                                    empPost.setEndDate(empOrgPost.getEndDate());
                                    addEmpPostList.add(empPost);
                                }
                                // TODO 【待办】往历史版本表中写入数据
                            }

                            // 流水
                            // 机构流水
                            String groupid = TMUID.getUID();
                            SysEmployeeChangeInfo empChgInfo = new SysEmployeeChangeInfo();
                            BeanUtils.copyProperties(empOrgPost, empChgInfo);
                            empChgInfo.setId(TMUID.getUID());
                            empChgInfo.setDataType(1);
                            empChgInfo.setStatus(2);
                            empChgInfo.setChangeType(1);
                            empChgInfo.setUsed(1);
                            empChgInfo.setOldCode(empOrgPost.getOldOrgcode());
                            empChgInfo.setOldName(empOrgPost.getOldOrgname());
                            empChgInfo.setNewCode(newOrgcode);
                            empChgInfo.setNewName(newOrgname);
                            empChgInfo.setGroupid(groupid);
                            empChgInfo.setMemo("兼岗部门变动：【" + empOrgPost.getEmpname() + "】兼岗部门【" + newOrgname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                            changeInfoList.add(empChgInfo);
                            // 岗位流水
                            // 流水
                            empChgInfo = new SysEmployeeChangeInfo();
                            BeanUtils.copyProperties(empOrgPost, empChgInfo);
                            empChgInfo.setId(TMUID.getUID());
                            empChgInfo.setDataType(2);
                            empChgInfo.setStatus(2);
                            empChgInfo.setChangeType(1);
                            empChgInfo.setUsed(1);
                            empChgInfo.setOldCode(empOrgPost.getOldPostid());
                            empChgInfo.setOldName(empOrgPost.getOldPostname());
                            empChgInfo.setNewCode(newPostid);
                            empChgInfo.setNewName(newPostname);
                            empChgInfo.setGroupid(groupid);
                            empChgInfo.setMemo("兼职岗位变动：【" + empOrgPost.getEmpname() + "】兼职岗位【" + newOrgname + " - " + newPostname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                            changeInfoList.add(empChgInfo);
                        }
                    }
                }
            }

            boolean flag = true;
//			if (flag == true && changeTodoList.size() > 0) {
//				int b = entityService.insertBatch(changeTodoList);
//				flag = (b > 0 ? true : false);
//				if (flag == false) {
//					info = "保存人员变动待办信息到数据库失败";// res.fail(this.errCode, "保存人员变动待办信息到数据库失败");
//				}
//			}

            if (flag == true && addEmpOrgList.size() > 0) {
                int b = entityService.insertBatch(addEmpOrgList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info = "保存人员机构信息到数据库失败";// res.fail(this.errCode, "保存人员机构信息到数据库失败");
                }
            }

            if (flag == true && addEmpPostList.size() > 0) {
                int b = entityService.insertBatch(addEmpPostList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info = "保存人员岗位信息到数据库失败";// res.fail(this.errCode, "保存人员岗位信息到数据库失败");
                }
            }

//			if (flag == true && changeInfoList.size() > 0) {
//				int b = entityService.insertBatch(changeInfoList);
//				flag = (b > 0 ? true : false);
//				if (flag == false) {
//					info = "保存人员变动流水信息到数据库失败";
//					// res.fail(this.errCode, "保存人员变动流水信息到数据库失败");
//				}
//			}
        } catch (Exception e) {
            log.error("", e);
            info = e.getMessage();
            // res.fail(this.errCode, "后台处理有误，请查看日志");
        }

        return info;
    }

    /**
     * 批量删除人员兼岗信息
     *
     * @param listDto
     * @return
     * @category 批量删除人员兼岗信息
     */
    @Override
    public String deletePartimePost(List<EmployeeOrgPostDto> listDto) {
        // Res<?> res = new Res<>();
        String info = "";
        try {
            if (listDto == null || listDto.size() <= 0) {
                // res.fail(this.errCode, "删除失败，数据为空");
                // return res;
                return "删除失败，数据为空";
            }

            List<SysEmployeeChangeInfo> changeInfoList = new ArrayList<SysEmployeeChangeInfo>(); // 流水
            List<SysEmployeeChangeTodo> changeTodoList = new ArrayList<SysEmployeeChangeTodo>(); // 预变动
            List<SysEmployeeOrg> empOrgList = new ArrayList<SysEmployeeOrg>(); // 人员机构
            List<SysEmployeeOrgPost> empPostList = new ArrayList<SysEmployeeOrgPost>(); // 人员岗位

            LocalDate now = LocalDate.now();
            for (EmployeeOrgPostDto empOrgPost : listDto) {
                Boolean isAfter = DateUtil.isAfter(empOrgPost.getChangeDate(), now);
                if (isAfter != null) {
                    if (isAfter.booleanValue() == true) { // 预变动
                        String newOrgcode = empOrgPost.getNewOrgcode();
                        String newOrgname = empOrgPost.getNewOrgname();
                        String newPostid = empOrgPost.getNewPostid();
                        String newPostname = empOrgPost.getNewPostname();

                        // 默认为不选则继承当前机构和岗位
                        if (newOrgcode == null || "".equals(newOrgcode)) { // 前台没选机构
                            // 继承当前机构
                            newOrgcode = empOrgPost.getOldOrgcode();
                            newOrgname = empOrgPost.getOldOrgname();
                        }
                        if (newPostid == null || "".equals(newPostid)) { // 前台没选岗位
                            // 继承当前机构
                            newPostid = empOrgPost.getOldPostid();
                            newPostname = empOrgPost.getOldPostname();
                        }

                        if (empOrgPost.getEmpOrgTmuidTodo() != null && !"".equals(empOrgPost.getEmpOrgTmuidTodo())) {
//							SysEmployeeChangeTodo chgTodo = empChgTodoServ
//									.getOne(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//											.eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpOrgTmuidTodo()));

                            Where queryECT = Where.create().eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpOrgTmuidTodo());
                            SysEmployeeChangeTodo chgTodo = entityService.queryObject(SysEmployeeChangeTodo.class, queryECT);

                            if (chgTodo != null) {
                                chgTodo.setUsed(0);
                                chgTodo.setChangeType(9);
                                changeTodoList.add(chgTodo);
                                // 流水
                                SysEmployeeChangeInfo empChgInfoOrg = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(chgTodo, empChgInfoOrg);
                                empChgInfoOrg.setId(TMUID.getUID());
                                empChgInfoOrg.setUsed(1);
                                // empChgInfoOrg.setMemo("【删除】兼岗机构（预）：【" + empOrgPost.getEmpname() + "】机构从【"
                                // + empChgInfoOrg.getOldName() + "】变动为【" + newOrgname + "】");
                                empChgInfoOrg.setMemo("【删除】兼岗部门变动（预）：【" + empOrgPost.getEmpname() + "】兼岗部门【" + newOrgname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                                changeInfoList.add(empChgInfoOrg);
                            }
                        }
                        if (empOrgPost.getEmpPostTmuidTodo() != null && !"".equals(empOrgPost.getEmpPostTmuidTodo())) {
//							SysEmployeeChangeTodo chgTodo = empChgTodoServ
//									.getOne(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//											.eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpPostTmuidTodo()));

                            Where queryECT = Where.create().eq(SysEmployeeChangeTodo::getId, empOrgPost.getEmpPostTmuidTodo());
                            SysEmployeeChangeTodo chgTodo = entityService.queryObject(SysEmployeeChangeTodo.class, queryECT);

                            if (chgTodo != null) {
                                chgTodo.setUsed(0);
                                chgTodo.setChangeType(9);
                                changeTodoList.add(chgTodo);
                                // 流水
                                SysEmployeeChangeInfo empChgInfoPost = new SysEmployeeChangeInfo();
                                BeanUtils.copyProperties(chgTodo, empChgInfoPost);
                                empChgInfoPost.setId(TMUID.getUID());
                                empChgInfoPost.setUsed(1);
                                // empChgInfoPost.setMemo("【删除】兼职岗位（预）：【" + empOrgPost.getEmpname() + "】岗位从【"
                                // + empOrgPost.getOldOrgname() + " - " + empChgInfoPost.getOldName() + "】变动为【"
                                // + newOrgname + " - " + newPostname + "】");
                                empChgInfoPost.setMemo("【删除】兼职岗位变动（预）：【" + empOrgPost.getEmpname() + "】兼职岗位【" + newOrgname + " - " + newPostname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                                changeInfoList.add(empChgInfoPost);
                            }
                        }
                    } else { // 立即变动（删除当前的或者历史版本的）
                        String newOrgcode = empOrgPost.getNewOrgcode();
                        String newOrgname = empOrgPost.getNewOrgname();
                        String newPostid = empOrgPost.getNewPostid();
                        String newPostname = empOrgPost.getNewPostname();

                        // 默认为不选则继承当前机构和岗位
                        if (newOrgcode == null || "".equals(newOrgcode)) { // 前台没选机构
                            // 继承当前机构
                            newOrgcode = empOrgPost.getOldOrgcode();
                            newOrgname = empOrgPost.getOldOrgname();
                        }
                        if (newPostid == null || "".equals(newPostid)) { // 前台没选岗位
                            // 继承当前机构
                            newPostid = empOrgPost.getOldPostid();
                            newPostname = empOrgPost.getOldPostname();
                        }

                        // 删除当前数据
                        if (empOrgPost.getEmpOrgTmuid() != null && !"".equals(empOrgPost.getEmpOrgTmuid())) {
//							SysEmployeeOrg empOrg = empOrgServ.getOne(new LambdaQueryWrapper<SysEmployeeOrg>()
//									.eq(SysEmployeeOrg::getId, empOrgPost.getEmpOrgTmuid()));

                            Where queryEO = Where.create().eq(SysEmployeeOrg::getId, empOrgPost.getEmpOrgTmuid());
                            SysEmployeeOrg empOrg = entityService.queryObject(SysEmployeeOrg.class, queryEO);

                            if (empOrg != null) {
                                // 删除主机构
                                empOrg.setUsed(0);
                                empOrgList.add(empOrg);
                            }
                        }
                        if (empOrgPost.getEmpPostTmuid() != null && !"".equals(empOrgPost.getEmpPostTmuid())) {
//							SysEmployeeOrgPost empPost = empPostServ.getOne(new LambdaQueryWrapper<SysEmployeeOrgPost>()
//									.eq(SysEmployeeOrgPost::getId, empOrgPost.getEmpPostTmuid()));

                            Where queryEOP = Where.create().eq(SysEmployeeOrgPost::getId, empOrgPost.getEmpPostTmuid());
                            SysEmployeeOrgPost empPost = entityService.queryObject(SysEmployeeOrgPost.class, queryEOP);

                            if (empPost != null) {
                                // 删除主岗位
                                empPost.setUsed(0);
                                empPostList.add(empPost);
                            }
                        }
                        // TODO 删除历史变动版本数据（制作下方流水描述）

                        // 机构变动流水
                        SysEmployeeChangeInfo empChgInfo = new SysEmployeeChangeInfo();
                        empChgInfo.setDataType(1);
                        empChgInfo.setStatus(2);
                        empChgInfo.setChangeType(1);
                        empChgInfo.setUsed(1);
                        empChgInfo.setOldCode(empOrgPost.getOldOrgcode());
                        empChgInfo.setOldName(empOrgPost.getOldOrgname());
                        empChgInfo.setNewCode(empOrgPost.getNewOrgcode());
                        empChgInfo.setNewName(empOrgPost.getNewOrgname());
                        // empChgInfo.setMemo("【删除】兼岗机构（预）：【" + empOrgPost.getEmpname() + "】机构从【"
                        // + empChgInfoOrg.getOldName() + "】变动为【" + newOrgname + "】");
                        empChgInfo.setMemo("【删除】兼岗部门变动：【" + empOrgPost.getEmpname() + "】兼岗部门【" + newOrgname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                        changeInfoList.add(empChgInfo);
                        // 岗位变动流水
                        SysEmployeeChangeInfo empChgInfoPost = new SysEmployeeChangeInfo();
                        empChgInfoPost.setDataType(2);
                        empChgInfoPost.setStatus(1);
                        empChgInfoPost.setChangeType(1);
                        empChgInfoPost.setUsed(1);
                        empChgInfoPost.setOldCode(empOrgPost.getOldPostid());
                        empChgInfoPost.setOldName(empOrgPost.getOldPostname());
                        empChgInfoPost.setNewCode(empOrgPost.getNewPostid());
                        empChgInfoPost.setNewName(empOrgPost.getNewPostname());
                        // empChgInfoPost.setMemo("【删除】兼职岗位（预）：【" + empOrgPost.getEmpname() + "】岗位从【"
                        // + empOrgPost.getOldOrgname() + " - " + empChgInfoPostPost.getOldName() +
                        // "】变动为【"
                        // + newOrgname + " - " + newPostname + "】");
                        empChgInfoPost.setMemo("【删除】兼职岗位变动：【" + empOrgPost.getEmpname() + "】兼职岗位【" + newOrgname + " - " + newPostname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                        changeInfoList.add(empChgInfoPost);
                    }
                }
            }

            boolean flag = true;
            if (flag == true && changeTodoList.size() > 0) {
                int b = entityService.updateByIdBatch(changeTodoList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info = "保存人员变动待办信息到数据库失败";// res.fail(this.errCode, "保存人员变动待办信息到数据库失败");
                }
            }
            if (flag == true && empOrgList.size() > 0) {
                int b = entityService.updateByIdBatch(empOrgList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info = "保存人员机构信息到数据库失败";// res.fail(this.errCode, "保存人员机构信息到数据库失败");
                }
            }
            if (flag == true && empPostList.size() > 0) {
                int b = entityService.updateByIdBatch(empPostList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info = "保存人员岗位信息到数据库失败";// res.fail(this.errCode, "保存人员岗位信息到数据库失败");
                }
            }
        } catch (Exception e) {
            log.error("", e);
            info = e.getMessage();
            // res.fail(this.errCode, "后台处理有误，请查看日志");
        }

        return info;
    }

    /**
     * 批量修改人员兼岗信息（实际操作为：添加+假删除）
     *
     * @param listDto
     * @return
     * @category 批量修改人员兼岗信息
     */
    @Override
    public String updatePartimePost(List<EmployeeOrgPostDto> listDto) {
        // Res<?> res = new Res<>();
        String info = "";
        try {
            if (listDto == null || listDto.size() <= 0) {
                // res.fail(this.errCode, "修改失败，数据为空");
                // return res;
                return "修改失败，数据为空";
            }

            List<SysEmployeeChangeInfo> changeInfoList = new ArrayList<SysEmployeeChangeInfo>(); // 流水
            List<SysEmployeeChangeTodo> addChangeTodoList = new ArrayList<SysEmployeeChangeTodo>(); // 预变动
            List<SysEmployeeChangeTodo> updChangeTodoList = new ArrayList<SysEmployeeChangeTodo>(); // 预变动（更新）
            List<SysEmployeeOrg> addEmpOrgList = new ArrayList<SysEmployeeOrg>(); // 人员机构
            List<SysEmployeeOrgPost> addEmpPostList = new ArrayList<SysEmployeeOrgPost>(); // 人员岗位
            List<SysEmployeeOrg> updEmpOrgList = new ArrayList<SysEmployeeOrg>(); // 人员机构
            List<SysEmployeeOrgPost> updEmpPostList = new ArrayList<SysEmployeeOrgPost>(); // 人员岗位

            LocalDate now = LocalDate.now();
            for (EmployeeOrgPostDto empOrgPost : listDto) {
                // 如果未选择截止日期，系统默认赋值9999-01-01，方便后续查询
                if (empOrgPost.getEndDate() == null) {
                    empOrgPost.setEndDate(DateUtil.parseDate(g_max_datetime));
                }

                String newOrgcode = empOrgPost.getNewOrgcode();
                String newOrgname = empOrgPost.getNewOrgname();
                String newPostid = empOrgPost.getNewPostid();
                String newPostname = empOrgPost.getNewPostname();

                // 默认为不选则继承当前机构和岗位
                if (newOrgcode == null || "".equals(newOrgcode)) { // 前台没选机构
                    // 继承当前机构
                    newOrgcode = empOrgPost.getOldOrgcode();
                    newOrgname = empOrgPost.getOldOrgname();
                }
                if (newPostid == null || "".equals(newPostid)) { // 前台没选岗位
                    // 继承当前机构
                    newPostid = empOrgPost.getOldPostid();
                    newPostname = empOrgPost.getOldPostname();
                }

                Boolean isAfter = DateUtil.isAfter(empOrgPost.getChangeDate(), now);
                if (isAfter != null) {
                    if (isAfter.booleanValue() == true) { // 预变动
//						String newOrgcodeCond = newOrgcode;
//						String newPostidCond = newPostid;
//						// 【校验】单个人预变动记录最多只能有一条（相同机构+岗位）
//						List<SysEmployeeChangeTodo> todoDbList = empChgTodoServ
//								.list(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//										.eq(SysEmployeeChangeTodo::getUsed, 1).eq(SysEmployeeChangeTodo::getStatus,
//												2)
//										.and(wrapper1 -> wrapper1
//												.and(wrapper2 -> wrapper2.eq(SysEmployeeChangeTodo::getDataType, 1)
//														.eq(SysEmployeeChangeTodo::getNewCode, newOrgcodeCond)
//														.ne(SysEmployeeChangeTodo::getId,
//																empOrgPost.getEmpOrgTmuidTodo()))
//												.or()
//												.and(wrapper2 -> wrapper2.eq(SysEmployeeChangeTodo::getDataType, 2)
//														.eq(SysEmployeeChangeTodo::getNewCode, newPostidCond)
//														.ne(SysEmployeeChangeTodo::getId,
//																empOrgPost.getEmpPostTmuidTodo())))
//
//										.orderByAsc(SysEmployeeChangeTodo::getDataType));

                        // TODO 嵌套语句不会写
                        List<SysEmployeeChangeTodo> todoDbList = null;

                        if (todoDbList != null && todoDbList.size() > 0) {
                            String dbOrgname = "";
                            String dbPostname = "";
                            String dbChangeDate = "";
                            for (SysEmployeeChangeTodo todoDb : todoDbList) {
                                if (todoDb.getDataType().intValue() == 1) { // 机构
                                    dbOrgname = todoDb.getNewName();
                                } else if (todoDb.getDataType().intValue() == 2) { // 岗位
                                    dbPostname = todoDb.getNewName();
                                }
                                dbChangeDate = DateUtil.formatDate(todoDb.getChangeDate());
                            }
                            // res.fail(this.errCode,"【" + dbChangeDate + "】【" + dbOrgname + "】【" +
                            // dbPostname + "】预兼岗已存在！");
                            // return res;
                            info = "【" + dbChangeDate + "】【" + dbOrgname + "】【" + dbPostname + "】预兼岗已存在！";
                        }

                        // TODO 可以在此处增加机构预删除判断逻辑

                        // 机构变动（机构岗位只要有1个变动，就都存到预变动表里，1对1关系，方便后续查询处理）
                        SysEmployeeChangeTodo empChgTodoOrg = new SysEmployeeChangeTodo(); // 机构变动
                        BeanUtils.copyProperties(empOrgPost, empChgTodoOrg);
                        empChgTodoOrg.setId(TMUID.getUID());
                        empChgTodoOrg.setDataType(1);
                        empChgTodoOrg.setStatus(2);
                        empChgTodoOrg.setChangeType(1);
                        empChgTodoOrg.setUsed(1);
                        empChgTodoOrg.setOldCode(empOrgPost.getOldOrgcode());
                        empChgTodoOrg.setOldName(empOrgPost.getOldOrgname());
                        empChgTodoOrg.setNewCode(newOrgcode);
                        empChgTodoOrg.setNewName(newOrgname);
                        empChgTodoOrg.setGroupid(TMUID.getUID());
                        // empChgTodoOrg.setMemo("【修改】兼岗机构（预变动）：【" + empOrgPost.getEmpname() + "】机构从【"
                        // + empChgTodoOrg.getOldName() + "】变动为【" + newOrgname + "】");
                        empChgTodoOrg.setMemo("【修改】兼岗部门变动（预）：【" + empOrgPost.getEmpname() + "】兼岗部门【" + newOrgname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                        addChangeTodoList.add(empChgTodoOrg);
                        // 机构变动流水（当机构真正变动时再写流水）
                        if (empOrgPost.getNewOrgcode() != null && !"".equals(empOrgPost.getNewOrgcode())) {
                            SysEmployeeChangeInfo empChgInfoOrg = new SysEmployeeChangeInfo();
                            BeanUtils.copyProperties(empChgTodoOrg, empChgInfoOrg);
                            empChgInfoOrg.setId(TMUID.getUID());
                            changeInfoList.add(empChgInfoOrg);
                        }

                        // 岗位变动（机构岗位只要有1个变动，就都存到预变动表里，1对1关系，方便后续查询处理）
                        SysEmployeeChangeTodo empChgTodoPost = new SysEmployeeChangeTodo(); // 岗位变动
                        BeanUtils.copyProperties(empOrgPost, empChgTodoPost);
                        empChgTodoPost.setId(TMUID.getUID());
                        empChgTodoPost.setDataType(2);
                        empChgTodoPost.setStatus(2);
                        empChgTodoPost.setChangeType(1);
                        empChgTodoPost.setUsed(1);
                        empChgTodoPost.setOldCode(empOrgPost.getOldPostid());
                        empChgTodoPost.setOldName(empOrgPost.getOldPostname());
                        empChgTodoPost.setNewCode(newPostid);
                        empChgTodoPost.setNewName(newPostname);
                        empChgTodoPost.setGroupid(empChgTodoOrg.getGroupid());
                        // empChgTodoPost.setMemo("【修改】兼职岗位（预变动）：【" + empOrgPost.getEmpname() + "】岗位从【"
                        // + empOrgPost.getOldOrgname() + " - " + empChgTodoPost.getOldName() + "】变动为【"
                        // + newOrgname + " - " + newPostname + "】");
                        empChgTodoPost.setMemo("【修改】兼职岗位变动（预）：【" + empOrgPost.getEmpname() + "】兼职岗位【" + newOrgname + " - " + newPostname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                        addChangeTodoList.add(empChgTodoPost);
                        // 岗位变动流水（当岗位真正变动时再写流水）
                        if (empOrgPost.getNewPostid() != null && !"".equals(empOrgPost.getNewPostid())) {
                            SysEmployeeChangeInfo empChgInfoPost = new SysEmployeeChangeInfo();
                            BeanUtils.copyProperties(empChgTodoPost, empChgInfoPost);
                            empChgInfoPost.setId(TMUID.getUID());
                            changeInfoList.add(empChgInfoPost);
                        }
                    } else { // 立即变动
                        // 【校验】新增数据的开始截止时间与历史+当前+未来的数据时间段不能有重叠（相同部门+岗位）
                        String newOrgcodeCond = newOrgcode;
                        String newPostidCond = newPostid;
//						// 检查当前数据（机构+岗位）：获取与当前记录时间重叠的岗位记录
//						SysEmployeeOrgPost empOrgCheck = empPostServ
//								.getOne(new LambdaQueryWrapper<SysEmployeeOrgPost>().eq(SysEmployeeOrgPost::getUsed, 1)
//										.eq(SysEmployeeOrgPost::getStatus, 2)
//										.eq(SysEmployeeOrgPost::getEmpid, empOrgPost.getEmpid())
//										.ne(SysEmployeeOrgPost::getId, empOrgPost.getEmpPostTmuid())
//										.and(wrapper1 -> wrapper1
//												.and(wrapper2 -> wrapper2
//														.le(SysEmployeeOrgPost::getChangeDate,
//																empOrgPost.getChangeDate())
//														.ge(SysEmployeeOrgPost::getEndDate, empOrgPost.getChangeDate()))
//												.or()
//												.and(wrapper2 -> wrapper2
//														.le(SysEmployeeOrgPost::getChangeDate, empOrgPost.getEndDate())
//														.ge(SysEmployeeOrgPost::getEndDate, empOrgPost.getEndDate()))
//												.or()
//												.and(wrapper2 -> wrapper2
//														.gt(SysEmployeeOrgPost::getChangeDate,
//																empOrgPost.getChangeDate())
//														.lt(SysEmployeeOrgPost::getEndDate, empOrgPost.getEndDate())))
//										.eq(SysEmployeeOrgPost::getOrgcode, newOrgcodeCond)
//										.eq(SysEmployeeOrgPost::getPostid, newPostidCond));

                        // TODO 嵌套语句不会写
                        SysEmployeeOrgPost empOrgCheck = null;

                        if (empOrgCheck != null) {
                            // 制作截止时间描述
                            String endDtStr = DateUtil.formatDate(empOrgCheck.getEndDate());
                            if (g_max_date.equals(endDtStr)) { // 未来
                                endDtStr = "未来";
                            }
                            // 获取机构名
                            String orgName = "";
                            List<SysOrg> sysOrgList = extOperServ.getExtOrgListByCode(Stream.of(empOrgCheck.getOrgcode()).collect(Collectors.toList()));
                            if (sysOrgList != null && sysOrgList.size() > 0) {
                                orgName = sysOrgList.get(0).getOrgname();
                            }
                            // 获取岗位名
                            String postName = "";
                            List<PostVo> extPostList = extOperServ.getExtPostListById(Stream.of(empOrgCheck.getPostid()).collect(Collectors.toList()));
                            if (extPostList != null && extPostList.size() > 0) {
                                postName = extPostList.get(0).getName();
                            }
                            // 返回提示
                            // res.fail(this.errCode, "【" + DateUtil.formatDate(empOrgCheck.getChangeDate())
                            // + "~"+ endDtStr + "】【" + orgName + "】【" + postName + "】已存在");
                            // return res;
                            info = "【" + DateUtil.formatDate(empOrgCheck.getChangeDate()) + "~" + endDtStr + "】【" + orgName + "】【" + postName + "】已存在";
                        }

//						// 检查未来数据：获取与当前记录时间重叠的机构+岗位记录
//						List<SysEmployeeChangeTodo> todoDbList = empChgTodoServ
//								.list(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//										.eq(SysEmployeeChangeTodo::getUsed, 1).eq(SysEmployeeChangeTodo::getStatus,
//												2)
//										.and(wrapper1 -> wrapper1
//												.and(wrapper2 -> wrapper2.eq(SysEmployeeChangeTodo::getDataType, 1)
//														.eq(SysEmployeeChangeTodo::getNewCode, newOrgcodeCond)
//														.ne(SysEmployeeChangeTodo::getId,
//																empOrgPost.getEmpOrgTmuidTodo()))
//												.or()
//												.and(wrapper2 -> wrapper2.eq(SysEmployeeChangeTodo::getDataType, 2)
//														.eq(SysEmployeeChangeTodo::getNewCode, newPostidCond)
//														.ne(SysEmployeeChangeTodo::getId,
//																empOrgPost.getEmpPostTmuidTodo())))
//										.and(wrapper1 -> wrapper1
//												.and(wrapper2 -> wrapper2
//														.le(SysEmployeeChangeTodo::getChangeDate,
//																empOrgPost.getChangeDate())
//														.ge(SysEmployeeChangeTodo::getEndDate,
//																empOrgPost.getChangeDate()))
//												.or()
//												.and(wrapper2 -> wrapper2
//														.le(SysEmployeeChangeTodo::getChangeDate,
//																empOrgPost.getEndDate())
//														.ge(SysEmployeeChangeTodo::getEndDate, empOrgPost.getEndDate()))
//												.or()
//												.and(wrapper2 -> wrapper2
//														.gt(SysEmployeeChangeTodo::getChangeDate,
//																empOrgPost.getChangeDate())
//														.lt(SysEmployeeChangeTodo::getEndDate,
//																empOrgPost.getEndDate())))
//										.orderByAsc(SysEmployeeChangeTodo::getDataType));

                        // TODO 嵌套语句不会写
                        List<SysEmployeeChangeTodo> todoDbList = null;

                        if (todoDbList != null && todoDbList.size() > 0) {
                            // 制作截止时间描述
                            String endDtStr = "";

                            String dbOrgname = "";
                            String dbPostname = "";
                            String dbChangeDate = "";
                            for (SysEmployeeChangeTodo todoDb : todoDbList) {
                                if (todoDb.getDataType().intValue() == 1) { // 机构
                                    dbOrgname = todoDb.getNewName();
                                } else if (todoDb.getDataType().intValue() == 2) { // 岗位
                                    dbPostname = todoDb.getNewName();
                                }
                                dbChangeDate = DateUtil.formatDate(todoDb.getChangeDate());
                                endDtStr = DateUtil.formatDate(todoDb.getEndDate());
                                if (g_max_date.equals(endDtStr)) { // 未来
                                    endDtStr = "未来";
                                }
                            }
                            // res.fail(this.errCode, "【" + dbChangeDate + "~" + endDtStr + "】【" + dbOrgname
                            // + "】【"+ dbPostname + "】已存在");
                            // return res;
                            return "【" + dbChangeDate + "~" + endDtStr + "】【" + dbOrgname + "】【" + dbPostname + "】已存在";
                        }

                        // TODO 【校验】检查历史版本变动数据

                        // 通过校验，开始处理机构岗位变动
                        // 判断记录是否为已过期记录（截止时间在今天以前）
                        isAfter = DateUtil.isBefore(empOrgPost.getEndDate(), now);
                        if (isAfter != null) {
                            if (isAfter.booleanValue() == true) { // 属于历史变动数据
                                // TODO 【待办】人员历史版本表
                            } else { // 可用数据（未过期数据）
                                // TODO 处理当前状态表
                                // 查询机构表中是否有此机构
//								SysEmployeeOrg empOrg = empOrgServ.getOne(new LambdaQueryWrapper<SysEmployeeOrg>()
//										.eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 2)
//										.eq(SysEmployeeOrg::getOrgcode, newOrgcode)
//										.eq(SysEmployeeOrg::getEmpid, empOrgPost.getEmpid()));

                                Where queryEO = Where.create().eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 2).eq(SysEmployeeOrg::getOrgcode, newOrgcode).eq(SysEmployeeOrg::getEmpid, empOrgPost.getEmpid());
                                SysEmployeeOrg empOrg = entityService.queryObject(SysEmployeeOrg.class, queryEO);

                                if (empOrg == null) {
                                    empOrg = new SysEmployeeOrg();
                                    empOrg.setOrgcode(newOrgcode);
                                    empOrg.setId(TMUID.getUID());
                                    empOrg.setEmpid(empOrgPost.getEmpid());
                                    empOrg.setStatus(2);
                                    // empOrg.setTmSort();
                                    empOrg.setUsed(1);
                                    empOrg.setChangeDate(empOrgPost.getChangeDate());
                                    empOrg.setEndDate(empOrgPost.getEndDate());
                                    addEmpOrgList.add(empOrg);
                                } else {
                                    empOrg.setChangeDate(empOrgPost.getChangeDate());
                                    empOrg.setEndDate(empOrgPost.getEndDate());
                                }
                                // 查询兼岗表中是否有此记录
//								SysEmployeeOrgPost empPost = empPostServ
//										.getOne(new LambdaQueryWrapper<SysEmployeeOrgPost>()
//												.eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 2)
//												.eq(SysEmployeeOrgPost::getOrgcode, newOrgcode)
//												.eq(SysEmployeeOrgPost::getPostid, newPostid)
//												.eq(SysEmployeeOrgPost::getEmpid, empOrgPost.getEmpid()));

                                Where queryEOP = Where.create().eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 2).eq(SysEmployeeOrgPost::getOrgcode, newOrgcode).eq(SysEmployeeOrgPost::getPostid, newPostid).eq(SysEmployeeOrgPost::getEmpid, empOrgPost.getEmpid());
                                SysEmployeeOrgPost empPost = entityService.queryObject(SysEmployeeOrgPost.class, queryEOP);

                                if (empPost == null) {
                                    empPost = new SysEmployeeOrgPost();
                                    empPost.setId(TMUID.getUID());
                                    empPost.setEmpid(empOrgPost.getEmpid());
                                    empPost.setStatus(2);
                                    empPost.setOrgcode(newOrgcode);
                                    empPost.setPostid(newPostid);
                                    // empPost.setTmSort();
                                    empPost.setUsed(1);
                                    empPost.setChangeDate(empOrgPost.getChangeDate());
                                    empPost.setEndDate(empOrgPost.getEndDate());
                                    addEmpPostList.add(empPost);
                                }
                                // TODO 【待办】往历史版本表中写入数据
                            }

                            // 流水
                            // 机构流水
                            String groupid = TMUID.getUID();
                            SysEmployeeChangeInfo empChgInfo = new SysEmployeeChangeInfo();
                            BeanUtils.copyProperties(empOrgPost, empChgInfo);
                            empChgInfo.setId(TMUID.getUID());
                            empChgInfo.setDataType(1);
                            empChgInfo.setStatus(2);
                            empChgInfo.setChangeType(1);
                            empChgInfo.setUsed(1);
                            empChgInfo.setOldCode(empOrgPost.getOldOrgcode());
                            empChgInfo.setOldName(empOrgPost.getOldOrgname());
                            empChgInfo.setNewCode(newOrgcode);
                            empChgInfo.setNewName(newOrgname);
                            empChgInfo.setGroupid(groupid);
                            empChgInfo.setMemo("兼岗部门变动：【" + empOrgPost.getEmpname() + "】兼岗部门【" + newOrgname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                            changeInfoList.add(empChgInfo);
                            // 岗位流水
                            // 流水
                            empChgInfo = new SysEmployeeChangeInfo();
                            BeanUtils.copyProperties(empOrgPost, empChgInfo);
                            empChgInfo.setId(TMUID.getUID());
                            empChgInfo.setDataType(2);
                            empChgInfo.setStatus(2);
                            empChgInfo.setChangeType(1);
                            empChgInfo.setUsed(1);
                            empChgInfo.setOldCode(empOrgPost.getOldPostid());
                            empChgInfo.setOldName(empOrgPost.getOldPostname());
                            empChgInfo.setNewCode(newPostid);
                            empChgInfo.setNewName(newPostname);
                            empChgInfo.setGroupid(groupid);
                            empChgInfo.setMemo("兼职岗位变动：【" + empOrgPost.getEmpname() + "】兼职岗位【" + newOrgname + " - " + newPostname + "】【" + DateUtil.formatDate(empOrgPost.getChangeDate()) + " ~ " + DateUtil.formatDate(empOrgPost.getEndDate()) + "】");
                            changeInfoList.add(empChgInfo);
                        }

                        // TODO 【待办】往历史版本表中写入数据
                    }

                    // 删除当前记录
                    Integer timeStatus = empOrgPost.getTimeStatus();
                    if (timeStatus != null) {
                        if (timeStatus.intValue() == -1) { // 历史
                            // TODO 删除历史数据
                        } else if (timeStatus.intValue() == 0) { // 当前
//							SysEmployeeOrgPost empPost = empPostServ.getOne(new LambdaQueryWrapper<SysEmployeeOrgPost>()
//									.eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 2)
//									.eq(SysEmployeeOrgPost::getId, empOrgPost.getEmpOrgTmuid()));

                            Where queryEOP = Where.create().eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 2).eq(SysEmployeeOrgPost::getId, empOrgPost.getEmpOrgTmuid());
                            SysEmployeeOrgPost empPost = entityService.queryObject(SysEmployeeOrgPost.class, queryEOP);

                            if (empPost != null) {
                                empPost.setUsed(0);
                                updEmpPostList.add(empPost);
                            }

//							SysEmployeeOrg empOrg = empOrgServ.getOne(new LambdaQueryWrapper<SysEmployeeOrg>()
//									.eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 2)
//									.eq(SysEmployeeOrg::getId, empOrgPost.getEmpPostTmuid()));

                            Where queryEO = Where.create().eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 2).eq(SysEmployeeOrg::getId, empOrgPost.getEmpPostTmuid());
                            SysEmployeeOrg empOrg = entityService.queryObject(SysEmployeeOrg.class, queryEO);

                            if (empOrg != null) {
                                empOrg.setUsed(0);
                                updEmpOrgList.add(empOrg);
                            }
                            // TODO 删除历史数据
                        } else if (timeStatus.intValue() == 1) { // 未来
//							SysEmployeeChangeTodo empTodo = empChgTodoServ
//									.getOne(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//											.eq(SysEmployeeChangeTodo::getUsed, 1)
//											.eq(SysEmployeeChangeTodo::getStatus, 2));

                            Where queryECT = Where.create().eq(SysEmployeeChangeTodo::getUsed, 1).eq(SysEmployeeChangeTodo::getStatus, 2);
                            SysEmployeeChangeTodo empTodo = entityService.queryObject(SysEmployeeChangeTodo.class, queryECT);

                            if (empTodo != null) {
                                empTodo.setUsed(0);
                            }
                            updChangeTodoList.add(empTodo);
                        }
                    }
                }
            }

            boolean flag = true;
//			if (flag == true && addChangeTodoList.size() > 0) {
//				int b = entityService.insertBatch(addChangeTodoList);
//				flag = (b > 0 ? true : false);
//				if (flag == false) {
//					info = "保存人员变动待办信息到数据库失败";// res.fail(this.errCode, "保存人员变动待办信息到数据库失败");
//				}
//			}

            if (flag == true && addEmpOrgList.size() > 0) {
                int b = entityService.insertBatch(addEmpOrgList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info = "保存人员机构信息到数据库失败";// res.fail(this.errCode, "保存人员机构信息到数据库失败");
                }
            }

            if (flag == true && addEmpPostList.size() > 0) {
                int b = entityService.insertBatch(addEmpPostList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info = "保存人员岗位信息到数据库失败";// res.fail(this.errCode, "保存人员岗位信息到数据库失败");
                }
            }

            if (flag == true && updEmpOrgList.size() > 0) {
                int b = entityService.updateByIdBatch(updEmpOrgList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info = "保存人员机构信息到数据库失败";// res.fail(this.errCode, "保存人员机构信息到数据库失败");
                }
            }

            if (flag == true && updEmpPostList.size() > 0) {
                int b = entityService.updateByIdBatch(updEmpPostList);
                flag = (b > 0 ? true : false);
                if (flag == false) {
                    info = "保存人员岗位信息到数据库失败";// res.fail(this.errCode, "保存人员岗位信息到数据库失败");
                }
            }
//			if (flag == true && changeInfoList.size() > 0) {
//				int b = entityService.insertBatch(changeInfoList);
//				flag = (b > 0 ? true : false);
//				if (flag == false) {
//					info = "保存人员变动流水信息到数据库失败";
//					// res.fail(this.errCode, "保存人员变动流水信息到数据库失败");
//				}
//			}
        } catch (Exception e) {
            log.error("", e);
            info = e.getMessage();// res.fail(this.errCode, "后台处理有误，请查看日志");
        }

        return info;
    }

    /**
     * 获取人员兼岗信息（历史+当前+未来）
     *
     * @param paramDto
     * @return
     * @category 获取人员兼岗信息
     */
    @Override
    public List<EmployeeOrgPostVo> getPartimePost(EmpPartimePostParamDto paramDto) {
        // Res<List<EmployeeOrgPostVo>> res = new Res<List<EmployeeOrgPostVo>>();
        List<EmployeeOrgPostVo> list = new ArrayList<EmployeeOrgPostVo>();
        // res.setResult(list);

        try {
            if (paramDto == null) {
                // res.fail(this.errCode, "参数为空");
                return null;
            }

            // List<SysEmployeeOrg> orgList = new ArrayList<SysEmployeeOrg>();
            // List<SysEmployeeOrgPost> postList = new ArrayList<SysEmployeeOrgPost>();

            // 获取此人未来待生效主部门与岗位（待变动）
//			List<SysEmployeeChangeTodo> todoDbList = empChgTodoServ.list(new LambdaQueryWrapper<SysEmployeeChangeTodo>()
//					.eq(SysEmployeeChangeTodo::getUsed, 1)
//					.in(SysEmployeeChangeTodo::getDataType, Stream.of(1, 2).collect(Collectors.toList()))
//					.eq(SysEmployeeChangeTodo::getStatus, 2).orderByDesc(SysEmployeeChangeTodo::getChangeDate)
//					.orderByAsc(SysEmployeeChangeTodo::getGroupid).orderByAsc(SysEmployeeChangeTodo::getDataType));

            Where queryECT = Where.create().eq(SysEmployeeChangeTodo::getUsed, 1).in(SysEmployeeChangeTodo::getDataType, Stream.of(1, 2).collect(Collectors.toList()).toArray()).eq(SysEmployeeChangeTodo::getStatus, 2);
            Order orderECT = Order.create().orderByDesc(SysEmployeeChangeTodo::getChangeDate).orderByAsc(SysEmployeeChangeTodo::getGroupid).orderByAsc(SysEmployeeChangeTodo::getDataType);
            List<SysEmployeeChangeTodo> todoDbList = entityService.queryList(SysEmployeeChangeTodo.class, queryECT, orderECT);

            if (todoDbList != null && todoDbList.size() > 0) {
                Map<String, EmployeeOrgPostVo> oneRecMap = new LinkedHashMap<String, EmployeeOrgPostVo>();

                for (SysEmployeeChangeTodo chgTodo : todoDbList) {
                    // String changeDay = DateUtil.formatDate(chgTodo.getChangeDate());
                    String groupid = chgTodo.getGroupid();
                    // 初始化记录
                    EmployeeOrgPostVo vo = oneRecMap.get(groupid);
                    if (vo == null) {
                        vo = new EmployeeOrgPostVo();
                        vo.setEmpid(paramDto.getEmpid());
                        vo.setStatus(2);
                        vo.setTimeStatus(1);
                        oneRecMap.put(groupid, vo);
                    }
                    // 填充记录
                    if (chgTodo.getDataType() != null) {
                        if (chgTodo.getDataType().intValue() == 1) { // 机构
                            vo.setEmpOrgTmuidTodo(chgTodo.getId());
                            vo.setOldOrgcode(chgTodo.getOldCode());
                            vo.setOldOrgname(chgTodo.getOldName());
                            vo.setNewOrgcode(chgTodo.getNewCode());
                            vo.setNewOrgname(chgTodo.getNewName());
                        } else if (chgTodo.getDataType().intValue() == 2) { // 岗位
                            vo.setEmpPostTmuidTodo(chgTodo.getId());
                            vo.setOldPostid(chgTodo.getOldCode());
                            vo.setOldPostname(chgTodo.getOldName());
                            vo.setNewPostid(chgTodo.getNewCode());
                            vo.setNewPostname(chgTodo.getNewName());
                        } else {
                            continue;
                        }
                        vo.setChangeDate(chgTodo.getChangeDate());
                    }
                }
                // 整合记录
                oneRecMap.keySet().forEach(key -> {
                    EmployeeOrgPostVo vo = oneRecMap.get(key);
                    list.add(vo);
                });
            }

            // 获取此人当前兼岗
            // 获取兼岗部门
            Map<String, SysEmployeeOrg> empOrgcodeMap = new LinkedHashMap<String, SysEmployeeOrg>();
//			List<SysEmployeeOrg> empOrgList = empOrgServ
//					.list(new LambdaQueryWrapper<SysEmployeeOrg>().eq(SysEmployeeOrg::getUsed, 1)
//							.eq(SysEmployeeOrg::getStatus, 2).eq(SysEmployeeOrg::getEmpid, paramDto.getEmpid()));

            Where queryEO = Where.create().eq(SysEmployeeOrg::getUsed, 1).eq(SysEmployeeOrg::getStatus, 2).eq(SysEmployeeOrg::getEmpid, paramDto.getEmpid());
            List<SysEmployeeOrg> empOrgList = entityService.queryList(SysEmployeeOrg.class, queryEO);

            if (empOrgList != null) {
                empOrgcodeMap = empOrgList.stream().collect(Collectors.toMap(item -> item.getOrgcode(), item -> item, (oldVal, currVal) -> currVal, LinkedHashMap::new));
            }

            // 获取兼岗岗位
//			List<SysEmployeeOrgPost> empPostList = empPostServ.list(new LambdaQueryWrapper<SysEmployeeOrgPost>()
//					.eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 2)
//					.eq(SysEmployeeOrgPost::getEmpid, paramDto.getEmpid()));

            Where queryEOP = Where.create().eq(SysEmployeeOrgPost::getUsed, 1).eq(SysEmployeeOrgPost::getStatus, 2).eq(SysEmployeeOrgPost::getEmpid, paramDto.getEmpid());
            List<SysEmployeeOrgPost> empPostList = entityService.queryList(SysEmployeeOrgPost.class, queryEOP);

            if (empPostList != null) {
                // 按机构分堆
                Map<String, Map<String, EmployeeOrgPostVo>> oneRecMap = new LinkedHashMap<String, Map<String, EmployeeOrgPostVo>>();
                for (SysEmployeeOrgPost orgPost : empPostList) {
                    Map<String, EmployeeOrgPostVo> postidMap = oneRecMap.get(orgPost.getOrgcode());
                    if (postidMap == null) {
                        postidMap = new LinkedHashMap<String, EmployeeOrgPostVo>();
                        oneRecMap.put(orgPost.getOrgcode(), postidMap);
                    }
                    EmployeeOrgPostVo vo = postidMap.get(orgPost.getPostid());
                    if (vo == null) {
                        vo = new EmployeeOrgPostVo();
                        postidMap.put(orgPost.getPostid(), vo);
                        vo.setEmpid(paramDto.getEmpid());
                        vo.setStatus(2);
                        vo.setTimeStatus(0);
                    }
                    vo.setEmpPostTmuid(orgPost.getId());
                    SysEmployeeOrg sysEmpOrg = empOrgcodeMap.get(orgPost.getOrgcode());
                    if (sysEmpOrg != null) {
                        vo.setEmpOrgTmuid(sysEmpOrg.getId());
                    }

                    // vo.setOldPostid(orgPost.getOldCode());
                    // vo.setOldPostname(orgPost.getOldName());
                    vo.setNewPostid(orgPost.getPostid());
                    vo.setNewPostname("后续填充");
                    vo.setNewOrgcode(orgPost.getOrgcode());
                    vo.setNewOrgname("后续填充");
                    vo.setChangeDate(orgPost.getChangeDate());
                    vo.setEndDate(orgPost.getEndDate());
                }
                // 整理机构id列表、岗位id列表
                List<String> orgcodeList = new ArrayList<String>();
                List<String> postidList = new ArrayList<String>();
                List<EmployeeOrgPostVo> postList = new ArrayList<EmployeeOrgPostVo>();
                for (String orgcode : oneRecMap.keySet()) {
                    orgcodeList.add(orgcode);
                    Map<String, EmployeeOrgPostVo> postMap = oneRecMap.get(orgcode);
                    for (String postid : postMap.keySet()) {
                        EmployeeOrgPostVo vo = postMap.get(postid);
                        postList.add(vo);
                        postidList.add(postid);
                    }
                }
                MapUtil mapUtil = new MapUtil();
                // 从机构模块获取附加信息
                List<SysOrg> extOrgList = extOperServ.getExtOrgListByCode(orgcodeList);
                Map<String, SysOrg> extOrgMap = mapUtil.getOrgcodeMap(extOrgList);
                // 从岗位模块获取附加信息
                List<PostVo> extPostList = extOperServ.getExtPostListById(postidList);
                Map<String, PostVo> extPostMap = mapUtil.getPostIdMap(extPostList);
                // 根据变动时间排序
                List<EmployeeOrgPostVo> sortedPostList = postList.stream().sorted((p1, p2) -> {
                    Boolean flag = DateUtil.isAfter(p1.getChangeDate(), p2.getChangeDate());
                    if (flag != null && flag.booleanValue() == true) {
                        return 1;
                    } else {
                        return 0;
                    }
                }).collect(Collectors.toList());
                // 填充机构名、岗位名
                for (EmployeeOrgPostVo vo : sortedPostList) {
                    SysOrg sysOrg = extOrgMap.get(vo.getNewOrgcode());
                    if (sysOrg != null) {
                        vo.setNewOrgname(sysOrg.getOrgname());
                    }
                    PostVo postVo = extPostMap.get(vo.getNewPostid());
                    if (postVo != null) {
                        vo.setNewPostname(postVo.getName());
                    }
                }

                // 整合记录
                list.addAll(sortedPostList);

            }

            // TODO 获取此人当前与历史的部门与岗位（直接从历史变动表中查）

        } catch (Exception e) {
            log.error("", e);
        }

        return list;
    }

    /**
     * 获取人员兼岗流水信息
     *
     * @param paramDto
     * @return
     * @category 获取人员兼岗流水信息
     */
    @Override
    public List<EmployeeDiaryVo> getPartimePostDiary(EmpPartimePostParamDto paramDto) throws Exception {
        // Res<List<EmployeeDiaryVo>> res = new Res<List<EmployeeDiaryVo>>();
        List<EmployeeDiaryVo> list = new ArrayList<EmployeeDiaryVo>();
        // res.setResult(list);

        if (paramDto == null) {
            // res.fail(this.errCode, "参数为空");
            // return res;
            return null;
        }

//		LambdaQueryWrapper<SysEmployeeChangeInfo> query = new LambdaQueryWrapper<SysEmployeeChangeInfo>();
//		query.eq(SysEmployeeChangeInfo::getUsed, 1).eq(SysEmployeeChangeInfo::getStatus, 2)
//				.eq(SysEmployeeChangeInfo::getEmpid, paramDto.getEmpid())
//				.orderByDesc(SysEmployeeChangeInfo::getCreateTime);

        Where query = Where.create().eq(SysEmployeeChangeInfo::getUsed, 1).eq(SysEmployeeChangeInfo::getStatus, 2).eq(SysEmployeeChangeInfo::getEmpid, paramDto.getEmpid());
        Order order = Order.create().orderByDesc(SysEmployeeChangeInfo::getCreateTime);

        // 分页
//		IPage<SysEmployeeChangeInfo> page = new Page<SysEmployeeChangeInfo>();
//		page.setCurrent(paramDto.getCurrent().longValue());
//		page.setSize(paramDto.getSize().longValue());
//		IPage<SysEmployeeChangeInfo> r = empChgInfoServ.page(page, query);
//		List<SysEmployeeChangeInfo> poList = r.getRecords();
//		long total = r.getTotal();

        // 创建分页对象
        Pagination<?> page = Pagination.create(paramDto.getCurrent(), paramDto.getSize());
        // 读取总记录数量
        long total = entityService.queryCount(SysEmployeeChangeInfo.class, query);
        // 读取记录结果
        List<SysEmployeeChangeInfo> poList = entityService.queryList(SysEmployeeChangeInfo.class, query, order, page);

        if (poList != null && poList.size() > 0) {
            for (SysEmployeeChangeInfo po : poList) {
                EmployeeDiaryVo diaryVo = new EmployeeDiaryVo();
                BeanUtils.copyProperties(po, diaryVo);
                diaryVo.setRecordCount(total);
                list.add(diaryVo);
            }
            // res.setResult(list);
        }

        return list;
    }

    /**
     * 批量添加人员借调信息
     *
     * @param listDto
     * @return
     * @category 批量添加人员借调信息
     */
    @Override
    public String addOnloan(List<EmployeeOrgPostDto> listDto) {
        String err = "";

        // TODO

        return err;
    }

    /**
     * 批量删除人员借调信息
     *
     * @param listDto
     * @return
     * @category 批量删除人员借调信息
     */
    @Override
    public String deleteOnloan(List<EmployeeOrgPostDto> listDto) {
        String err = "";

        // TODO

        return err;
    }

    /**
     * 批量修改人员借调信息
     *
     * @param listDto
     * @return
     * @category 批量修改人员借调信息
     */
    @Override
    public String updateOnloan(List<EmployeeOrgPostDto> listDto) {
        String err = "";
        return err;
    }

    /**
     * 获取登录信息
     *
     * @return
     */
    @Override
    public LoginUserDto getLoginInfo(String id) {
        LoginUserDto res = null;
        SysLoginUser login = loginServ.getSysUserbyId(id);
        if (login != null) {
            res = new LoginUserDto();
            res.setId(login.getId());
            res.setUsername(login.getUserName());
            res.setPassword(login.getPassword());
        }
        return res;
    }

    /**
     * 保存登录信息
     *
     * @param 传入实体类EmployeeDto
     * @return
     */
    @Override
    public String saveLoginInfo(EmployeeDto dto) {
        String id = dto.getEmpTmuid();
        if (StringUtils.isEmpty(id)) {
            return "传入参数有误";
        }

        // 用组员id查找，看是否已经创建过账号
        SysLoginUser user = null;
        String username = dto.getLoginUserName();
        if (StringUtils.isEmpty(username)) {
            return "未传入账号";
        }
        String old_username = "";// 原登录名
        if (!StringUtils.isEmpty(id)) {
            user = loginServ.getSysUserbyId(id);
        }

        // 新建登录账号或者修改登录名称时，需校验登录名称是否重复
        if (user == null || !username.equals(user.getUserName())) {
            SysLoginUser login = loginServ.getLoginUser(username);
            if (login != null && !login.getId().equals(id)) {
                return "登录名称重复，不能保存";
            }
        }
        boolean isInsert = true;
        if (user == null) {
            user = this.createLoginUser(null, dto, null);
            // 新建
//			user = new SysLoginUser();
//			user.setId(id);
//			user.setPassword(loginServ.encryptPassword(getDefaultPass()));
//			user.setRealName(dto.getEmpname());
//			user.setNickName(dto.getEmpname());
//			user.setPhone(dto.getMobile());
//			user.setEmail(dto.getMail());
//			user.setBirthday(dto.getBirthday());

        } else {
            isInsert = false;
            old_username = user.getUserName();
            // 修改
//			user.setStatus(dto.getLoginStatus());
        }
        user.setStatus(dto.getLoginStatus());
        user.setUserName(username);

        // 人员角色信息
        // 先删除后添加
        UserRoleDto param = new UserRoleDto();
        param.setUserid(dto.getEmpTmuid());
        userRoleServ.deleteUserRoleByUserId(param);
        if (StringUtils.isNotEmpty(dto.getRoleid())) {
//			userRoleServ.addBatch(this.convertUserRole(dto.getEmpTmuid(), dto.getRoleid()));
            this.batchAddRole(this.convertUserRole(dto.getEmpTmuid(), dto.getRoleid()));
        }
        int res = 0;// entityService.save(user);
        if (isInsert) {
            res = entityService.insert(user);
        } else {
            res = entityService.rawUpdateById(user);
        }
        if (res > 0) {
            if (StringUtils.isNotEmpty(old_username)) {
                authServ.deleteLoginUserFromRedis(old_username);
            }
            authServ.setLoginUserToRedis(user);
        }
        return res > 0 ? "" : "保存失败";
    }

    /**
     * 批量添加角色
     *
     * @param list
     * @return
     */
    private boolean batchAddRole(List<SysUserRole> list) {
        return userRoleServ.addBatch(list);
    }

    /**
     * 保存登录信息
     *
     * @param 传入实体类EmployeeDto
     * @return
     */
    @Override
    public SysLoginUser saveLoginInfo(String empid, String username, String password) {
        if (StringUtils.isEmpty(empid)) {
            return null;
        }
        if (StringUtils.isEmpty(username)) {//无账号，就不创建
            return null;
        }
        // 新建登录账号或者修改登录名称时，需校验登录名称是否重复
        SysLoginUser login = loginServ.getLoginUser(username);
        if (login != null && !login.getId().equals(empid)) {
            return null;
        }
        EmployeeDto dto = new EmployeeDto();// user = new SysLoginUser();
        dto.setEmpTmuid(empid);// user.setId(empid);
        // user.setPassword(loginServ.encryptPassword(password));
        dto.setEmpname(username);
        dto.setLoginUserName(username);// user.setUserName(username);
        SysLoginUser user = this.createLoginUser(null, dto, password);
        int res = 0;
        SysLoginUser oldLogUser = loginServ.getSysUserbyId(empid);
        String oldLoginid = "";
        if (oldLogUser == null) {
            res = entityService.insert(user);
        } else {
            res = entityService.updateById(user);
            oldLoginid = oldLogUser.getUserName();
        }
        if (res > 0) {
            if (StringUtils.isNotEmpty(oldLoginid)) {
                authServ.deleteLoginUserFromRedis(oldLoginid);
            }
            authServ.setLoginUserToRedis(user);
        }
        return res > 0 ? user : null;
    }

    private SysLoginUser createLoginUser(String tenantId, EmployeeDto dto, String password) {
        SysLoginUser user = new SysLoginUser();
        user = new SysLoginUser();
        user.setId(dto.getEmpTmuid());
        if (StringUtils.isEmpty(password)) {
            password = getDefaultPass();
        }
        user.setPassword(loginServ.encryptPassword(password));
        user.setRealName(dto.getEmpname());
        user.setNickName(dto.getEmpname());
        user.setPhone(dto.getMobile());
        user.setEmail(dto.getMail());
        user.setBirthday(dto.getBirthday());
        if (dto.getLoginStatus() == null) {
            user.setStatus(1);
        } else {
            user.setStatus(dto.getLoginStatus());
        }
        user.setPswModifyTime(new Date());
        user.setUserName(dto.getLoginUserName());
        if (StringUtils.isEmpty(tenantId)) {
            user.setTenant_id(MultiTenantUtils.getTenantId());
        }
        return user;
    }

    /**
     * 初始化登录密码
     *
     * @param idList 要初始化密码的人员id集合
     * @return
     */
    @Override
    public String initLoginPassword(List<String> idList) {
        String defaultPass = getDefaultPass();
        if (idList == null || idList.size() == 0) {
            return "";
        }
        // 查找原账号
        List<SysLoginUser> loginList = loginServ.getSysUserbyIdList(idList);
        if (loginList == null || loginList.size() == 0) {
            return "";
        }
        // 循环初始化密码
        for (SysLoginUser login : loginList) {
            login.setPassword(loginServ.encryptPassword(defaultPass));
            login.setPswModifyTime(new Date());
            loginServ.updateLoginUser(login.getId(), login);
        }
        return defaultPass;
    }

    /**
     * 数据导入
     *
     * @param dataList
     * @param params
     * @return
     */
    @Override
    public String importData(List<EmployeeVo> dataList, String params) {
        String info = "";
        if (StringUtils.isNotEmpty(dataList)) {
            String orgcode = "";
            if (StringUtils.isNotEmpty(params)) {
                JSONObject obj = JSONObject.parseObject(params);
                orgcode = obj.getString("orgcode");
            }
            List<EmployeeDto> insertList = new ArrayList<EmployeeDto>();
            List<EmployeeDto> updateList = new ArrayList<EmployeeDto>();
            Map<String, String> mapRole = getRoleNameMap();
            int sort = 0;
            for (EmployeeVo employeeVo : dataList) {
                EmployeeDto bean = ObjUtils.copyTo(employeeVo, EmployeeDto.class);
                bean.setUsed(1);
                bean.setStatus(1);
                sort++;
                bean.setTmSort(sort);
                if (StringUtils.isNotEmpty(employeeVo.getPostid())) {// 岗位
                    bean.setPostStatus(1);
                }
                if (StringUtils.isNotEmpty(employeeVo.getRolename())) {// 角色
                    List<String> roleid = this.convertRoleid(mapRole, employeeVo.getRolename());
                    if (StringUtils.isNotEmpty(roleid)) {
                        if (roleid.size() == 1) {
                            bean.setRoleid(roleid.get(0));
                        } else {
                            bean.setRoleid(String.join(",", roleid));
                        }
                    }
                }
                if (StringUtils.isNotEmpty(employeeVo.getEmpTmuid())) {// 修改
                    updateList.add(bean);
                } else {// 添加
                    bean.setOrgcode(orgcode);
                    bean.setOrgStatus(1);
                    insertList.add(bean);
                }
            }
            if (insertList.size() > 0) {
                this.addEmployee(insertList);
            }
            if (updateList.size() > 0) {
                this.updEmployee(updateList);
            }
        }
        return info;
    }

    /**
     * 获得岗位map
     *
     * @return
     */
    @Override
    public void getPostMap(Map<String, String> mapPost, List<String> listPost, EmpParamDto paramDto) {
        String orgCode = paramDto.getOrgcode();
        PostParamDto postParamDto = new PostParamDto();
        postParamDto.setIncludeCurNode(0);
        postParamDto.setUsed(1);
        postParamDto.setId("");
        if (paramDto != null) {
            ISysDiyPost bean = SpringUtils.getBean(ISysDiyPost.class);
            if (StringUtils.isNotEmpty(bean.isUseOrgDiyPost())) {
                postParamDto.setId(paramDto.getOrgcode() + "_post");
                postParamDto.setOrgCode(paramDto.getOrgcode());
            }
        }
        List<PostVo> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(sysDiyPost.isUseOrgDiyPost())) {
            //
            List<PostVo> diyPost = sysDiyPost.getDiyPostByOrgCode(paramDto.getOrgcode(), null, null);
            if (StringUtils.isNotEmpty(diyPost)) {
                list = diyPost.get(0).getChildren();
            }
        } else {
            list = postServ.getPostChildrenAll(postParamDto);
        }
        if (StringUtils.isNotEmpty(list)) {
            for (PostVo postVo : list) {
                if (mapPost != null) {
                    mapPost.put(postVo.getId(), postVo.getName());
                }
                if (listPost != null) {
                    listPost.add(postVo.getName());
                }
            }
        }
    }

    /**
     * 获得角色map
     *
     * @return
     */
    @Override
    public void getRoleMap(Map<String, String> mapRole, List<String> listRole) {
        //获取角色列表
        List<RoleTree> list = sysRoleService.querySysRoleList(false);
        if (StringUtils.isNotEmpty(list)) {
            list = list.stream().filter(item -> SysUserHolder.getCurrentUser().getRoles().contains(item.getId())).collect(Collectors.toList());
            for (RoleTree bean : list) {
                if (mapRole != null) {
                    mapRole.put(bean.getId(), bean.getName());
                }
                if (listRole != null) {
                    listRole.add(bean.getName());
                }
            }
        }
    }

    /**
     * 传入角色名称，返回角色id
     *
     * @param map
     * @param rolename
     * @return
     */
    private List<String> convertRoleid(Map<String, String> map, String rolename) {
        List<String> list = new ArrayList<String>();
        String[] ary = rolename.split(",");
        for (String name : ary) {
            if (map != null) {
                String roleid = map.get(name);
                if (StringUtils.isNotEmpty(roleid)) {
                    if (!list.contains(roleid)) {
                        list.add(roleid);
                    }
                }
            }
        }
        return list;
    }

    /**
     * key值为角色名称的map
     *
     * @return
     */
    private Map<String, String> getRoleNameMap() {
        Map<String, String> map = new HashMap<String, String>();
        List<RoleTree> list = sysRoleService.querySysRoleList(false);
        if (StringUtils.isNotEmpty(list)) {
            for (RoleTree bean : list) {
                map.put(bean.getName(), bean.getId());
            }
        }
        return map;
    }

    /**
     * 获取默认密码
     *
     * @return
     */
    private String getDefaultPass() {
        return authServ.getDefaultPassword();
    }

    /**
     * @param orgId
     * @param postId
     * @return create by G.fj 2022.4.15
     * @category 读取机构-岗位下的人员ID列表
     */
    @Override
    public Set<String> getEmployeeIdsByOrgIdPostId(String orgId, String postId) {
        List<SysEmployeeOrgPost> sysEmployeeOrgPosts = getSysEmployeeOrgPostsByOrgIdPostId(orgId, postId);
        Set<String> empIds = new HashSet<String>();
        for (SysEmployeeOrgPost sysEmployeeOrgPost : sysEmployeeOrgPosts) {
            empIds.add(sysEmployeeOrgPost.getEmpid());
        }
        return empIds;
    }

    public Set<String> getEmployeeIdsByPostId(String postId) {
        List<SysEmployeeOrgPost> sysEmployeeOrgPosts = getSysEmployeeOrgPostsByPostId(postId);
        Set<String> empIds = new HashSet<String>();
        for (SysEmployeeOrgPost sysEmployeeOrgPost : sysEmployeeOrgPosts) {
            empIds.add(sysEmployeeOrgPost.getEmpid());
        }
        return empIds;
    }

    /**
     * @param orgId
     * @param postId
     * @return create by G.fj 2022.4.15
     * @category 读取机构-岗位下的人员相互关系
     */
    @Override
    public List<SysEmployeeOrgPost> getSysEmployeeOrgPostsByOrgIdPostId(String orgId, String postId) {
        return entityService.queryList(SysEmployeeOrgPost.class, Where.create().eq(SysEmployeeOrgPost::getOrgcode, orgId).eq(SysEmployeeOrgPost::getPostid, postId).eq(SysEmployeeOrgPost::getUsed, 1));
    }

    @Override
    public Set<String> getEmployeeIdsByOrgIdPostIdDisableTenant(String orgId, String postId) {
        List<SysEmployeeOrgPost> sysEmployeeOrgPosts = getSysEmployeeOrgPostsByOrgIdPostIdDisableTenant(orgId, postId);
        Set<String> empIds = new HashSet<String>();
        for (SysEmployeeOrgPost sysEmployeeOrgPost : sysEmployeeOrgPosts) {
            empIds.add(sysEmployeeOrgPost.getEmpid());
        }
        return empIds;
    }

    @Override
    public List<SysEmployeeOrgPost> getSysEmployeeOrgPostsByOrgIdPostIdDisableTenant(String orgId, String postId) {
        return entityService.queryDataDisableTenant(SysEmployeeOrgPost.class, Where.create().eq(SysEmployeeOrgPost::getOrgcode, orgId).eq(SysEmployeeOrgPost::getPostid, postId).eq(SysEmployeeOrgPost::getUsed, 1), null, null);
    }

    private EmployeeDto empVo2LoginDto(EmployeeVo vo) {
        EmployeeDto dto = new EmployeeDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setLoginStatus(1);
        return dto;
    }

    /**
     * 生成登陆名
     *
     * @param type        1:工号；2：姓名全拼；3：手机号
     * @param logname     登录名
     * @param logNameList 所有登陆名
     * @return
     */
    private String createLoginName(String type, String logname, List<String> logNameList) {
        if (StringUtils.isNotEmpty(logname)) {
            // type 1:工号；2：姓名全拼；3：手机号
            if ("2".equals(type)) {// 姓名全拼
                return this.createLoginName(logname, logNameList);
            } else {
                if (logNameList.indexOf(logname) >= 0) {// 登陆名重复
                    return null;
                }
            }
        }
        return logname;
    }

    /**
     * 递归生成登陆名
     *
     * @param logname
     * @param logNameList
     * @return
     */
    private String createLoginName(String logname, List<String> logNameList) {
        if (StringUtils.isEmpty(logNameList)) {
            return logname;
        } else {
            int suffix = 0;// 后缀
            String rtnName = logname;
            while (logNameList.indexOf(rtnName) >= 0) {
                suffix++;
                rtnName = logname + suffix;
            }
            return rtnName;
        }
    }

    /**
     * @param postId
     * @return
     * @category 读取岗位下的人员列表
     */
    @Override
    public List<SysEmployeeOrgPost> getSysEmployeeOrgPostsByPostId(String postId) {
        return entityService.queryList(SysEmployeeOrgPost.class, Where.create().eq(SysEmployeeOrgPost::getPostid, postId).eq(SysEmployeeOrgPost::getUsed, 1));
    }

    /**
     * 初始化全部人员的登录信息
     *
     * @param 类型 1 工号 2 拼写 3 手机号
     * @return
     */
    @Override
    public synchronized String initLoginInfoAll(EmpInitLoginDto dto) {
        String msg = "";
        List<SysLoginUser> logUserList = new ArrayList<SysLoginUser>();
        List<SysUserRole> listUserRole = new ArrayList<SysUserRole>();
        List<String> loginNameList = new ArrayList<String>();
        int failCount = 0;
        int succCount = 0;
        StringBuffer sb = new StringBuffer();
        StringBuffer sb_empty = new StringBuffer();
        // 查询全部人员
        EmpParamDto paramDto = new EmpParamDto();
        paramDto.setOrgStatus(0);
        if (StringUtils.isNotEmpty(dto.getEmpidList())) {
            String ids = "";
            for (String id : dto.getEmpidList()) {
                ids += id + ",";
            }
            ids = ids.substring(0, ids.length() - 1);
            paramDto.setEmpid(ids);
        }
        List<EmployeeVo> empList = this.getAllEmployee(paramDto);
        // 查询没有登录名称的人员
        List<EmployeeVo> userNoLoginNameList = empList.stream().filter(i -> i.getLoginUserName() == null || i.getLoginUserName().length() == 0).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(userNoLoginNameList)) {
            List<SysLoginUser> loginList = loginServ.getAllSysUserList(); // 获得所有登陆信息

            if (loginList != null) {
                for (SysLoginUser bean : loginList) {
                    loginNameList.add(bean.getUserName());
                }
            }
            String type = dto.getLoginType();// 1:工号；2：姓名全拼；3：手机号
            for (EmployeeVo vo : userNoLoginNameList) {
                String loginName = "";
                if ("1".equals(type)) {// 1:工号
                    loginName = vo.getStaffNo();
                } else if ("3".equals(type)) {// 3：手机号
                    loginName = vo.getMobile();
                } else {// 姓名全拼
                    loginName = PinYinUtils.ToPinyin(vo.getEmpname());
                }
                if (loginName == null) { // 由于账号为null判定为重复，所以在此处将null变成空字符串
                    loginName = "";
                }
                loginName = createLoginName(type, loginName, loginNameList);// 生成登录名
                if (StringUtils.isNotEmpty(loginName)) {
                    loginNameList.add(loginName);
                    EmployeeDto empdto = this.empVo2LoginDto(vo);
                    empdto.setLoginUserName(loginName);
                    SysLoginUser user = this.createLoginUser(null, empdto, null);
                    logUserList.add(user);
                    List<SysUserRole> listRole = this.convertUserRole(user.getId(), dto.getRoleId());
                    listUserRole.addAll(listRole);
                    succCount++;
                } else {
                    failCount++;
                    if (loginName == null) { // 账号重复
                        sb.append(",");
                        sb.append(vo.getEmpname());
                    } else { // 无账号
                        sb_empty.append(",");
                        sb_empty.append(vo.getEmpname());
                    }
                }
            }
        }
        if (logUserList.size() > 0) {
            int c = entityService.insertBatch(logUserList, 500);
            if (c > 0) {// 添加角色
                this.batchAddRole(listUserRole);
                for (SysLoginUser e : logUserList) {
                    loginServ.setLoginUserToRedis(e);
                }
            } else {
                succCount = 0;
            }
        }
        if (succCount > 0) {
            msg = "成功生成：[" + succCount + "]个登录账号";
        } else {
            msg = "生成：[" + succCount + "]个登录账号";
        }
        if (failCount > 0) {
            msg += "，未生成[" + failCount + "]个账号";
            String sbStr = sb.toString();
            if (StringUtils.isNotEmpty(sbStr)) {
                msg += "，(" + sbStr.substring(1) + ")账号重复";
            }
            String sb_emptyStr = sb_empty.toString();
            if (StringUtils.isNotEmpty(sb_emptyStr)) {
                msg += "，(" + sb_emptyStr.substring(1) + ")";
                String type = dto.getLoginType();
                if (type != null) {
                    if ("1".equals(type)) {// 1:工号
                        msg += "工号";
                    } else if ("3".equals(type)) {// 3：手机号
                        msg += "手机号";
                    } else {// 姓名全拼
                        msg += "姓名(全拼)";
                    }
                }
                msg += "为空";
            }
            msg += "，无法生成";
        }
        msg += "！";
        return msg;
    }

    /**
     * 初始化全部人员的密码
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @Override
    public Boolean initPasswordForAllUser() {
        // 查找原账号
        List<SysLoginUser> loginList = loginServ.getAllSysUserList();
        if (loginList == null || loginList.size() == 0) {
            return false;
        }
        List<SysLoginUser> initLoginList = new ArrayList<>();
        // 循环初始化密码
        String defaultPwd = loginServ.encryptPassword(getDefaultPass());
        for (SysLoginUser login : loginList) {
            // 这里为了提高效率只初始化和默认密码不一样的人员
            if (StringUtils.isNotEmpty(defaultPwd) && !login.getPassword().equals(defaultPwd)) {
                login.setPassword(defaultPwd);
                login.setPswModifyTime(new Date());
                initLoginList.add(login);
            }
        }
        return loginServ.initPassWordBatch(initLoginList);
    }

    /**
     * 获取推荐的用户名
     *
     * @param userName 传入的用户名
     * @param type     2：拼音全拼
     * @return
     */
    @Override
    public String getLoginName(String userName, Integer type) {
        String loginName = null;
        if (StringUtils.isNotEmpty(userName)) {
            if (type == 2) {// 拼音全拼
                loginName = PinYinUtils.ToPinyin(userName);
            } else {
                loginName = userName;
            }
            List<String> userNameList = new ArrayList<String>();
            List<SysLoginUser> loginlist = this.getLoginUser(loginName);
            if (StringUtils.isNotEmpty(loginlist)) {
                for (SysLoginUser bean : loginlist) {
                    userNameList.add(bean.getUserName());
                }
            }
            loginName = this.createLoginName(loginName, userNameList);
        }
        return loginName;
    }

    /**
     * 获取变动日期
     *
     * @param dt 变动日期
     * @return
     * @category <AUTHOR>
     */
    private String getChangeDt(Date dt) {
        String result = null;
        if (dt != null) {
            result = DateTimeUtils.formatDate(dt, DateTimeUtils.DateFormat_YMD);// 获取变动日期
        }
        if (result == null) {
            result = DateTimeUtils.getNowDateStr();// 未选时间则使用当前日期
        }
        return result;
    }

    /**
     * 获取人员的基本信息，不关联表，只查询未删除的
     *
     * @return
     */
    @Override
    public List<SysEmployeeInfo> getEmpAllList() {
        List<SysEmployeeInfo> result = new ArrayList<SysEmployeeInfo>();
        Where where = Where.create();
        where.eq(SysEmployeeInfo::getUsed, 1);
        result = entityService.queryData(SysEmployeeInfo.class, where, null, null);
        return result;
    }

    /**
     * 获取人员信息
     */
    @Override
    public EmployeeVo getEmployeeFromReids(String empId) {
        String key = this.getEmpInfoKey(null);
        EmployeeVo emp = redis.getMapValue(key, empId, EmployeeVo.class);
        if (emp == null) {
            emp = this.setEmployeeToReids(null, empId);
        }
        return emp;
    }

    /**
     * 人员信息保存到redis
     */
    @Override
    public EmployeeVo setEmployeeToReids(String tenantId, String empId) {
        String key = this.getEmpInfoKey(tenantId);
        EmployeeVo emp = null;
        List<EmployeeVo> list = this.getEmployee(empId);
        if (StringUtils.isNotEmpty(list)) {
            emp = list.get(0);
            redis.setMapValue(key, empId, emp);
        }
        return emp;
    }

    private void setEmployeeToReids(List<EmployeeVo> list) {
        if (StringUtils.isNotEmpty(list)) {
            String key = this.getEmpInfoKey(null);
            for (EmployeeVo e : list) {
                redis.setMapValue(key, e.getEmpTmuid(), e);
            }
        }
    }

    /**
     * 初始化人员信息到redis
     */
    @Override
    public void initEmployeeData(List<SysLoginUser> list) {
        log.info("正在初始化人员信息到redis....");
        this.clearEmployeeData(null);
        if (StringUtils.isNotEmpty(list)) {
            for (SysLoginUser sysLoginUser : list) {
                this.setEmployeeToReids(null, sysLoginUser.getId());
            }
        }
        log.info("初始化人员信息到redis完毕！！！");
    }

    /**
     * 清除redis
     */
    @Override
    public void clearEmployeeData(String empid) {
        String key = this.getEmpInfoKey(null);
        if (StringUtils.isNotEmpty(empid)) {
            redis.hDelete(key, empid);
        } else {
            redis.delete(key);
        }

    }


    private void clearEmployeeDataByIds(List<String> empids) {
        String key = this.getEmpInfoKey(null);
        if (StringUtils.isNotEmpty(empids)) {
            redis.hDelete(key, empids.toArray());
        } else {
            // redis.delete(key);
        }

    }

    private String getEmpInfoKey(String tenantId) {
        String key = "SYSTEM:EMP_INFO";
        if (MultiTenantUtils.enalbe()) {
            String tid = null;
            if (tenantId == null) {
                tid = MultiTenantUtils.getTenantId();
            }
            if (tid != null) {
                key += ":" + tid;
            }
        }
        return key;
    }

    /**
     * 根据机构代码获取人员信息
     *
     * @param orgcode
     * @return
     */
    @Override
    public List<EmployeeVo> getEmployeeByOrgcode(String orgcode) {
        EmpParamDto paramDto = new EmpParamDto();
        paramDto.setOrgcode(orgcode);
        return this.getEmployeeByOrgcode(paramDto, null);
    }

    /**
     * 根据机构代码获取人员信息
     *
     * @param orgcode 机构代码
     * @param page    分页
     * @param size    每页大小
     * @return
     */
    @Override
    public List<EmployeeVo> getEmployeeByOrgcode(EmpParamDto paramDto, Pagination<?> page) {
        List<EmployeeVo> list = new ArrayList<>();
        try {
            List<Object> params = new ArrayList<>();
            String queryCol = "a.orgcode,c.orgname,c.orglevel,c.org_number,c.tm_sort as orgsort, d.postid,e.name as postname,b.* ";
            String sql = " from ";
            sql += "sys_employee_org a ";
            sql += "LEFT JOIN sys_employee_info b on a.empid = b.id ";
            sql += "LEFT JOIN sys_org c on a.orgcode=c.id ";
            sql += "LEFT JOIN sys_employee_org_post d on b.id=d.empid and d.status=1 ";
            sql += "LEFT JOIN sys_post e on e.id=d.postid ";
            sql += "where a.used = 1 and a.status = 1  and b.used=1  and  b.status = 1 ";
            sql += "and c.used=1 and c.ishidden=0 ";

            if (StringUtils.isNotEmpty(paramDto.getEmpname())) {
                sql += " and b.empname like ? ";
                params.add("%" + paramDto.getEmpname() + "%");
            }
            if (StringUtils.isNotEmpty(paramDto.getStaffNo())) {
                sql += " and b.staff_no like ? ";
                params.add("%" + paramDto.getStaffNo() + "%");
            }
            if (StringUtils.isNotEmpty(paramDto.getMobile())) {
                sql += " and b.mobile like ? ";
                params.add("%" + paramDto.getMobile() + "%");
            }
            //orgcode = "";
            if (StringUtils.isNotEmpty(paramDto.getOrgcode()) && !"ALL".equalsIgnoreCase(paramDto.getOrgcode())) {
                params.add("%/" + paramDto.getOrgcode() + "/%");
                Where where = Where.create();
                String s = where.addRight(SysOrg::getOrgpath, "/");//where.like(), "/" + orgcode + "/");
                sql += "and a.orgcode in ";
                sql += "(SELECT id from sys_org where (" + s + ") LIKE ? ";
                //sql += "a.orgcode in (SELECT id from sys_org where (ORGPATH  || '/') LIKE '%/" + orgcode + "/%' ";
                sql += "and used = 1 and (ishidden = 0 or ishidden is null)) ";
            }

            String orderby = " order by c.orglevel,c.tm_sort,c.orgpath, b.tm_sort ";

            String querySql = "SELECT " + queryCol + sql + orderby;
            if (params.size() == 0) {
                params = null;
            }
            SqlRowSet rs = null;
            if (page == null) {//不分页
                if (params == null) {
                    rs = dao.rawQuery(querySql);
                } else {
                    rs = dao.rawQuery(querySql, params.toArray());
                }
            } else {
                String countSql = "SELECT count(*) " + sql;
                if (DBTypeUtils.isSqlServer(dao.getDbType())) {//sqlserver
                    if (dao.getDatabaseVersion() < 11.0) {//sqlserver 2012 以下版本分页
                        querySql = "SELECT * FROM (SELECT ROW_NUMBER() OVER (" + orderby + ") AS rownum, " + queryCol + sql + ") AS t WHERE t.rownum BETWEEN " + ((page.getPage() - 1) * page.getSize() + 1) + " AND " + page.getPage() * page.getSize();
                    }
                }
                rs = dao.rawQueryPage(querySql, countSql, params, page);
            }
            if (rs != null) {
                while (rs.next()) {
                    EmployeeVo vo = new EmployeeVo();
                    // vo.setRecordCount(pageRes.getTotal());
                    vo.setOrgcode(this.getStringValue(rs, "orgcode"));
                    vo.setCardno(this.getStringValue(rs, "cardno"));
                    vo.setUsed(this.getIntValue(rs, "used"));
                    vo.setStatus(1);
                    vo.setEmpname(this.getStringValue(rs, "empname"));
                    vo.setEmpTmuid(this.getStringValue(rs, "id"));
                    //vo.setIsHead(this.getStringValue(rs, "ishead"));
                    vo.setMobile(this.getStringValue(rs, "mobile"));
                    vo.setOrgname(this.getStringValue(rs, "orgname"));
                    vo.setOrgNumber(this.getStringValue(rs, "org_number"));
                    vo.setOrgSort(this.getIntValue(rs, "orgsort"));
                    vo.setOrgTmuid(vo.getOrgcode());
                    vo.setPostid(this.getStringValue(rs, "postid"));
                    vo.setPostTmuid(vo.getPostid());
                    vo.setPostname(this.getStringValue(rs, "postname"));
                    vo.setPostStatus(1);
                    vo.setSex(this.getStringValue(rs, "sex"));
                    vo.setStaffNo(this.getStringValue(rs, "staff_no"));
                    vo.setMemo(this.getStringValue(rs, "memo"));
                    vo.setTmSort(this.getIntValue(rs, "tm_sort"));
                    vo.setOrgStatus(1);
                    list.add(vo);
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return list;
    }

    private Integer getIntValue(SqlRowSet rs, String key) {
        Integer rtn = null;
        try {
            rtn = rs.getInt(key);
        } catch (Exception e) {
            log.error("", e);
        }
        return rtn;
    }


    private String getStringValue(SqlRowSet rs, String key) {
        String rtn = null;
        try {
            rtn = rs.getString(key);
        } catch (Exception e) {
            log.error("", e);
        }
        return rtn;
    }

}
