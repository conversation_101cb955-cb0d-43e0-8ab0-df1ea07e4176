package com.yunhesoft.system.employee.service.impl;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.system.employee.entity.po.SysUserCompare;
import com.yunhesoft.system.employee.service.ISysUserCompareService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrgVersionsDetail;

@Service
public class SysUserCompareImpl implements ISysUserCompareService {

	@Autowired
	private EntityService entityService;
	
	
	@Override
	public List<SysUserCompare> listData(String targetOrgUserId) {
		List<SysUserCompare> list = null;
		Where where = Where.create();
		where.eq(SysUserCompare::getTargetOrgUserId, targetOrgUserId);
		list = entityService.queryList(SysUserCompare.class, where);
		return list;
	}

	@Override
	public boolean saveData(List<SysUserCompare> list) {
		int rs = entityService.insertBatch(list);
		if (rs <= 0) {
			return false;
		}
		return true;
	}

	@Override
	public boolean updateData(List<SysUserCompare> list) {
		int rs = entityService.updateByIdBatch(list);
		if (rs <= 0) {
			return false;
		}
		return true;
		// return this.updateData(list);
	}

	@Override
	@Transactional
	public boolean insertData(List<SysUserCompare> list) {
		return this.saveData(list);
	}

	@Override
	@Transactional
	public boolean deleteData(String id) {
		Where where = Where.create();
		where.eq(SysOrgVersionsDetail::getId, id);
		int rs = entityService.delete(SysUserCompare.class, where);
		if (rs <= 0) {
			return false;
		} else {
			return true;
		}
	}

}
