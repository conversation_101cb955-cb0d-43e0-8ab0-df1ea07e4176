package com.yunhesoft.system.employee.service;

import java.util.List;

import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;

/**
 * SysEmployeeChangeInfoMapper
 * 
 * @category SysEmployeeChangeInfoMapper
 * <AUTHOR>
 * @date 2020/03/30
 */
public interface ISysEmployeeChangeInfoService {

	/**
	 * 记录人员机构变动信息
	 * @category 记录人员机构变动信息
	 * <AUTHOR> 
	 * @param changeDt 变动日期
	 * @param changeEmpOrgList 变动机构列表
	 * @return
	 */
	public boolean saveUserOrgChange(String changeDt,List<SysEmployeeOrg> changeEmpOrgList);
	
	/**
	 * 记录人员岗位变动信息
	 * @category 记录人员岗位变动信息
	 * <AUTHOR> 
	 * @param changeDt 变动日期
	 * @param changeEmpOrgPostList 变动岗位列表
	 * @return
	 */
	public boolean saveUserOrgPostChange(String changeDt,List<SysEmployeeOrgPost> changeEmpOrgPostList);
	/**
	 * 获取指定机构的人员变动信息
	 * @category 获取指定机构的人员变动信息
	 * <AUTHOR> 
	 * @param changeDt 变动日期
	 * @param orgCode 机构代码
	 * @param readAllSubOrg 是否读取全部子机构的变动信息 true读取子机构，false不读取子机构，只读取给定的机构
	 * @param readPostChange 是否读取岗位变动 true读取 false不读取。 不读取的情况下，人员的岗位信息为空
	 * @return List<EmployeeVo>
	 */
	public List<EmployeeVo> getOrgUserChange(String changeDt,String orgCode,Boolean readAllSubOrg,Boolean readPostChange);
	/**
	 * 获取指定机构的人员变动信息
	 * @category 获取指定机构的人员变动信息
	 * <AUTHOR> 
	 * @param changeDt 变动日期
	 * @param orgCodeList 机构代码列表
	 * @param readPostChange 是否读取岗位变动 true读取 false不读取。 不读取的情况下，人员的岗位信息为空
	 * @return List<EmployeeVo>
	 */
	public List<EmployeeVo> getOrgUserChange(String changeDt,List<String> orgCodeList,Boolean readPostChange);
	/**
	 * 获取指定岗位的人员变动信息
	 * @category 获取指定岗位的人员变动信息
	 * <AUTHOR> 
	 * @param changeDt 变动日期
	 * @param orgCode 机构代码
	 * @param postId 岗位id
	 * @param readAllSubOrg 是否读取全部子机构的变动信息 true读取子机构，false不读取子机构，只读取给定的机构
	 * @return List<EmployeeVo>
	 */
	public List<EmployeeVo> getPostUserChange(String changeDt,String orgCode,String postId,Boolean readAllSubOrg);
	/**
	 * 获取指定人员的变动信息
	 * @category 获取指定人员的变动信息 
	 * <AUTHOR> 
	 * @param changeDt 变动日期
	 * @param userIdList 人员ID列表
	 * @param readPostChange 是否读取岗位变动 true读取 false不读取。 不读取的情况下，人员的岗位信息为空
	 * @return List<EmployeeVo>
	 */
	public List<EmployeeVo> getUserChangeList(String changeDt,List<String> userIdList,Boolean readPostChange);
	/**
	 * 获取指定人员的变动信息
	 * @category 获取指定人员的变动信息
	 * <AUTHOR> 
	 * @param changeDt 变动日期
	 * @param userId 人员ID
	 * @param readPostChange 是否读取岗位变动 true读取 false不读取。 不读取的情况下，人员的岗位信息为空
	 * @return List<EmployeeVo>
	 */
	public EmployeeVo getUserChange(String changeDt,String userId,Boolean readPostChange);
}
