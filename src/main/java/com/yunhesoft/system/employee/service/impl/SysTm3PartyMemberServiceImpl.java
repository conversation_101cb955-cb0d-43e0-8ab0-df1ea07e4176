package com.yunhesoft.system.employee.service.impl;


import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.HttpUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.auth.entity.po.SysLoginUser;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;
import com.yunhesoft.system.employee.entity.vo.SysTm3PartyMember;
import com.yunhesoft.system.employee.service.ISysTm3PartyMemberService;
import com.yunhesoft.system.kernel.service.impl.EntityServiceImpl;
import com.yunhesoft.system.org.entity.po.SysOrgCompare;
import com.yunhesoft.system.org.service.ISysOrgCompareService;
import com.yunhesoft.system.role.entity.po.SysUserRole;
import com.yunhesoft.system.synchronous.entity.po.SysComparison;
import com.yunhesoft.system.synchronous.service.ISynchronousService;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;


@Service
public class SysTm3PartyMemberServiceImpl implements ISysTm3PartyMemberService {
	
	@Autowired
	private ISysConfigService sysConfigService;
	
	@Autowired
	private EntityServiceImpl entservice;
	
	@Autowired
	private ISynchronousService syncService;
	
	@Autowired
	private ISysOrgCompareService orgCompService;
	
	
	/**
	 * 登录信息服务接口
	 */
	@Autowired
	private AuthService loginServ;

	
	/**
	 *	同步党员数据（只追加）
	 */
	@Override
	public int syncPartyMember() {
		int result = 0;
		String COMMODULE = "emp";
		String COMTYPE = "tm3";
		String ROLEID = "guest"; //角色ID：普通用户
		
		List<SysComparison> addCompList = new ArrayList<SysComparison>();
		List<SysEmployeeInfo> addEmpList = new ArrayList<SysEmployeeInfo>();
		List<SysUserRole> addUserRoleList = new ArrayList<SysUserRole>();
		List<SysEmployeeOrg> addEmpOrgList = new ArrayList<SysEmployeeOrg>();
		List<SysLoginUser> sysLoginUsersList = new ArrayList<SysLoginUser>();
		
		//获取tm3党员数据
		List<SysTm3PartyMember> tm3PartyMemberList = this.getTm3PartyMemberList();
		if (StringUtils.isNotEmpty(tm3PartyMemberList)) {
			//机构对照数据Map
			HashMap<String, String> orgCompMap = this.getSysOrgCompareMap();
			if (StringUtils.isNotEmpty(orgCompMap)) {
				//获取tm4党员数据
				Map<String,SysComparison> tm4PartyMemberMap = syncService.getSysComparisonMap("",COMMODULE); //党员对比表数据Map
				if(tm4PartyMemberMap==null) {
					tm4PartyMemberMap = new HashMap<String, SysComparison>();
				}
				HashMap<String, Integer> tm4PartyStaffNoMap = this.getStaffNoMap(); //已存在工号Map
				
				for (int i = 0; i < tm3PartyMemberList.size(); i++) {
					SysTm3PartyMember tm3Obj = tm3PartyMemberList.get(i);
					String empId_tm3 = tm3Obj.getEmpid(); //人员ID
					String orgdm_tm3 = tm3Obj.getOrgdm(); //机构代码
					String staffNo_tm3 = tm3Obj.getStaffNo(); //工号
					
					if(!tm4PartyMemberMap.containsKey(empId_tm3)&&!tm4PartyStaffNoMap.containsKey(staffNo_tm3)) { //新加
						String sourceOrgCode = this.getOrgCompareCode(orgdm_tm3,orgCompMap);
						if (StringUtils.isNotEmpty(sourceOrgCode)) {
							//TM4新人员编码
							String newEmpId = TMUID.getUID();
							
							//TM4党员绑定机构
							SysEmployeeOrg empOrgObj = new SysEmployeeOrg();
							empOrgObj.setId(TMUID.getUID());
							empOrgObj.setEmpid(newEmpId);
							empOrgObj.setOrgcode(sourceOrgCode);
							empOrgObj.setStatus(1);
							empOrgObj.setUsed(1);
							addEmpOrgList.add(empOrgObj);
							
							//TM4党员信息
							SysEmployeeInfo empObj = this.setEmpInfo(newEmpId,tm3Obj);
							addEmpList.add(empObj);
							if(staffNo_tm3!=null&&!"".equals(staffNo_tm3.trim())) {
								tm4PartyStaffNoMap.put(staffNo_tm3.trim(), 1);
							}
							
							//TM4与TM3党员编码对照
							SysComparison compObj = new SysComparison();
							compObj.setId(TMUID.getUID());
							compObj.setCodetm4(newEmpId);
							compObj.setCode(empId_tm3);
							compObj.setCommodule(COMMODULE);
							compObj.setComtype(COMTYPE);
							compObj.setUsed(1);
							addCompList.add(compObj);
							tm4PartyMemberMap.put(empId_tm3, compObj);
							
							//TM4党员角色
							SysUserRole userRoleObj = new SysUserRole();
							userRoleObj.setId(TMUID.getUID());
							userRoleObj.setRoleid(ROLEID);
							userRoleObj.setUserid(newEmpId);
							addUserRoleList.add(userRoleObj);
							
							
							if(tm3Obj.getDlmc()!=null&&tm3Obj.getDlmc().trim().length()>0) {
								SysLoginUser user = new SysLoginUser();
								user.setId(empObj.getId());//人员表的id
								user.setUserName(tm3Obj.getDlmc());
								user.setPassword(loginServ.encryptPassword(tm3Obj.getYhkl()));//密码加密
								user.setRealName(empObj.getEmpname());
								user.setNickName(empObj.getEmpname());
								user.setPhone(empObj.getMobile());
								user.setEmail(empObj.getMail());
								user.setBirthday(empObj.getBirthday());
								user.setStatus(1);
								sysLoginUsersList.add(user);
							}
							
							
							
						}
					}
				}
			}
		}
		//-------------------------执行事务----------------------------
		try {
			entservice.begin();//事务开始
			//添加人员信息
			if (StringUtils.isNotEmpty(addEmpList)) {
				entservice.insertBatch(addEmpList);
			}
			//添加对照信息
			if (StringUtils.isNotEmpty(addCompList)) {
				result = addCompList.size();
				entservice.insertBatch(addCompList);
			}
			//添加角色信息
			if (StringUtils.isNotEmpty(addUserRoleList)) {
				entservice.insertBatch(addUserRoleList);
			}
			//添加人员绑定机构信息
			if (StringUtils.isNotEmpty(addEmpOrgList)) {
				entservice.insertBatch(addEmpOrgList);
			}
			if (StringUtils.isNotEmpty(sysLoginUsersList)) {
				entservice.insertBatch(sysLoginUsersList);
			}
			entservice.commit();//事务提交
		}catch(Exception e) {
			entservice.rollback();//事务回滚
			result = -1;
		}
		return result;
	}
	
	
	/**
	 *	获取tm3党员数据
	 * @return
	 */
	private List<SysTm3PartyMember> getTm3PartyMemberList() {
		//tm3党员数据
		String url = sysConfigService.getSysConfig("tm3_url")+"/getTm3PartyMemberList";
		Map<String, String> header = new LinkedHashMap<String, String>();
		String str = HttpUtils.doPost(true, url, header, "");
		//党员信息数据
		List<SysTm3PartyMember> list = new ArrayList<SysTm3PartyMember>();
		JSONObject jsonObject = JSONObject.parseObject(str);
		if (jsonObject != null && jsonObject.size() > 0) {
			String result = jsonObject.getString("result");
			JSONArray jsonArray = JSONArray.parseArray(result);
			if (jsonArray != null && jsonArray.size() > 0) {
				for (int i = 0; i < jsonArray.size(); i++) {
					JSONObject obj = jsonArray.getJSONObject(i);
					String empid = obj.getString("empid");
					String empName = obj.getString("empName");
					String orgdm = obj.getString("orgdm");
					String staffNo = obj.getString("staffNo");
					String sex = obj.getString("sex");
					String mobile = obj.getString("mobile");
					Integer politicsStatus = Integer.valueOf(obj.getString("politicsStatus"));
					String nationality = obj.getString("nationality");
					Integer education = Integer.valueOf(obj.getString("education"));
					Date birthday = obj.getDate("birthday");
					
					String dlmc = obj.getString("dlmc");//登入账号
					String yhkl = obj.getString("yhkl");//登入密码
					//赋值
					SysTm3PartyMember partyMemberObj = new SysTm3PartyMember();
					partyMemberObj.setEmpid(empid);
					partyMemberObj.setEmpName(empName);
					partyMemberObj.setOrgdm(orgdm);
					partyMemberObj.setStaffNo(staffNo);
					partyMemberObj.setSex(sex);
					partyMemberObj.setMobile(mobile);  //移动电话
					partyMemberObj.setPoliticsStatus(politicsStatus);  //政治面貌（1党员、2预备党员、3团员、4群众、5其它）
					partyMemberObj.setNationality(nationality);  //民族
					partyMemberObj.setEducation(education);  //最高学历（1博士、2硕士、3本科、4大专、5高中、6初中、7小学、8其它）
					partyMemberObj.setBirthdayType(1);  //生日类型（1公立、2农历）
					partyMemberObj.setBirthday(birthday);  //出生日期
					partyMemberObj.setDlmc(dlmc);
					partyMemberObj.setYhkl(yhkl);
					list.add(partyMemberObj);
				}
			}
		}
		return list;
	}
	
	
	/**
	 *	获取机构对照数据Map
	 * @return
	 */
	private HashMap<String, String> getSysOrgCompareMap() {
		HashMap<String, String> map = new HashMap<String, String>();
		List<SysOrgCompare> orgCompList = orgCompService.listData(null);
		if (StringUtils.isNotEmpty(orgCompList)) {
			//按照 班组 ———> 公司 排序
			Collections.sort(orgCompList, new Comparator<SysOrgCompare>() {
				public int compare(SysOrgCompare arg0, SysOrgCompare arg1) {
					String sourceOrgCode0 = arg0.getSourceOrgCode();
					String sourceOrgCode1 = arg1.getSourceOrgCode();
					if(sourceOrgCode0!=null&&sourceOrgCode0.length()==10&&sourceOrgCode0.endsWith("00")) {
						sourceOrgCode0 = sourceOrgCode0.substring(0,8);
					}
					if(sourceOrgCode1!=null&&sourceOrgCode1.length()==10&&sourceOrgCode1.endsWith("00")) {
						sourceOrgCode1 = sourceOrgCode1.substring(0,8);
					}
					if (sourceOrgCode0.length() < sourceOrgCode1.length()) {
						return 1;
					} else if (sourceOrgCode0.length() > sourceOrgCode1.length()) {
						return -1;
					} else {
						return 0;
					}
				}
			});
			for (int i = 0; i < orgCompList.size(); i++) {
				SysOrgCompare orgCompObj = orgCompList.get(i);
				String sourceOrgCode = orgCompObj.getSourceOrgCode();
				if(sourceOrgCode!=null&&sourceOrgCode.length()==10&&sourceOrgCode.endsWith("00")) {
					sourceOrgCode = sourceOrgCode.substring(0,8);
				}
				String targetOrgCode = orgCompObj.getTargetOrgCode();
				if(map!=null&&!map.containsKey(sourceOrgCode)) {
					map.put(sourceOrgCode, targetOrgCode);
				}
			}
		}
		return map;
	}
	
	
	/**
	 *	获取TM4机构绑定编码
	 * @param orgdm
	 * @param orgCompareMap
	 * @return
	 */
	private String getOrgCompareCode(String orgdm,HashMap<String, String> orgCompareMap) {
		String result = "";
		if (StringUtils.isNotEmpty(orgdm)&&StringUtils.isNotEmpty(orgCompareMap)) {
			orgdm = orgdm.trim();
			if (StringUtils.isEmpty(result)&&orgdm.length()>10) {
				if(orgCompareMap.containsKey(orgdm)) {
					result = orgCompareMap.get(orgdm);
				}else {
					orgdm = orgdm.substring(0,10);
				}
			}
			if (StringUtils.isEmpty(result)&&orgdm.length()==10) {
				if(orgCompareMap.containsKey(orgdm)) {
					result = orgCompareMap.get(orgdm);
				}else {
					orgdm = orgdm.substring(0,8);
				}
			}
			if (StringUtils.isEmpty(result)&&orgdm.length()==8) {
				if(orgCompareMap.containsKey(orgdm)) {
					result = orgCompareMap.get(orgdm);
				}else {
					orgdm = orgdm.substring(0,6);
				}
			}
			if (StringUtils.isEmpty(result)&&orgdm.length()==6) {
				if(orgCompareMap.containsKey(orgdm)) {
					result = orgCompareMap.get(orgdm);
				}else {
					orgdm = orgdm.substring(0,3);
				}
			}
			if (StringUtils.isEmpty(result)&&orgdm.length()==3) {
				if(orgCompareMap.containsKey(orgdm)) {
					result = orgCompareMap.get(orgdm);
				}
			}
		}
		return result;
	}
	
	/**
	 *	设置人员信息
	 * @param newEmpId
	 * @param tm3Obj
	 * @return
	 */
	private SysEmployeeInfo setEmpInfo(String newEmpId,SysTm3PartyMember tm3Obj) {
		SysEmployeeInfo empObj = new SysEmployeeInfo();
		int politicsStatus = tm3Obj.getPoliticsStatus();
		String political = null;
		if(politicsStatus==1) {//党员
			political = "01";
		}
		empObj.setId(newEmpId);
		empObj.setEmpname(tm3Obj.getEmpName());  //姓名
		empObj.setStaffNo(tm3Obj.getStaffNo());  //工号
		empObj.setSex(tm3Obj.getSex());  //性别
		empObj.setUsed(1);
		empObj.setEntryDate(null);  //入职日期
		empObj.setMobile(tm3Obj.getMobile());  //手机号
		empObj.setCardTypeCode("");  //证件类型编码（1居民身份证，2军人证，3中国护照，4外国护照，5台湾居民来往大陆通行证，6港澳居民来往内地通行证，7其它）
		empObj.setCardTypeName("");  //证件类型名称
		empObj.setCardno("");  //证件号
		empObj.setDutyid("");  //职务ID
		empObj.setPositionlevelid("");  //职级ID
		empObj.setPoliticsStatus(politicsStatus);  //政治面貌（1党员、2预备党员、3团员、4群众、5其它）
		empObj.setMail(null);  //邮箱
		empObj.setQq("");  //QQ
		empObj.setWechat("");  //微信
		empObj.setNationality(tm3Obj.getNationality());  //民族
		empObj.setNativePlace("");  //籍贯
		empObj.setEducation(tm3Obj.getEducation());  //最高学历（1博士、2硕士、3本科、4大专、5高中、6初中、7小学、8其它）
		empObj.setMarital(null);  //婚姻状况（1是0否）
		empObj.setBirthdayType(tm3Obj.getBirthdayType());  //生日类型（1公立、2农历）
		empObj.setBirthday(tm3Obj.getBirthday());  //出生日期
		empObj.setOldWorkNum(null);  //历史工龄
		empObj.setStaffType(1);  //员工类型（1全职、2兼职、3实习、4外派、5其它）
		empObj.setWorkplace("");  //工作地点
		empObj.setAccountType(null);  //户口类型(1城镇、2非城镇)
		empObj.setLiveAddress("");  //居住地址
		empObj.setMemo("");  //描述
		empObj.setTmSort(null);  //人员排序
		empObj.setDimissionDate(null);  //离退日期
		empObj.setStatus(1);  //人员状态 - 在职（1）、离职（0）、退休（-1）
		empObj.setInvalid(null);  //失效记录标识（1：失效，0：有效）
		empObj.setPolitical(political);  //政治面貌
		empObj.setSys(null);  //是否为系统内置人员（1：是，0：否）
		return empObj;
	}
	
	/**
	 *	获取已存在的工号Map
	 * @return
	 */
	private HashMap<String, Integer> getStaffNoMap() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		List<SysEmployeeInfo> employeeList = syncService.getSysEmployeeInfoList();
		if (StringUtils.isNotEmpty(employeeList)) {
			for (int i = 0; i < employeeList.size(); i++) {
				SysEmployeeInfo employeeObj = employeeList.get(i);
				String staffNo = employeeObj.getStaffNo();
				if(staffNo!=null&&!"".equals(staffNo.trim())&&!map.containsKey(staffNo.trim())) {
					map.put(staffNo.trim(), 1);
				}
			}
		}
		return map;
	}
	
}
