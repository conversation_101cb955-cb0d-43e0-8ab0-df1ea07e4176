package com.yunhesoft.system.employee.service;

import java.util.List;

import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.post.entity.vo.PostVo;
import com.yunhesoft.system.role.entity.po.SysRole;
import com.yunhesoft.system.role.entity.po.SysUserRole;

/**
 * <AUTHOR>
 */
public interface IEmployeeExtraOperationService {
	/**
	 * 根据岗位id获取岗位信息（调用岗位模块接口）
	 * 
	 * @category 根据岗位id获取岗位信息
	 * @param postList
	 * @return
	 */
	List<PostVo> getExtPostList(List<SysEmployeeOrgPost> postList);

	/**
	 * 根据岗位id获取岗位信息（调用岗位模块接口）
	 * 
	 * @category 根据岗位id获取岗位信息
	 * @param postList
	 * @return
	 */
	List<PostVo> getExtPostListById(List<String> postidList);

	/**
	 * 根据机构代码获取机构信息（调用机构模块接口）
	 * 
	 * @category 根据机构代码获取机构信息
	 * @param empOrgList
	 * @return
	 */
	List<SysOrg> getExtOrgList(List<SysEmployeeOrg> empOrgList);

	/**
	 * 根据机构代码获取机构信息（调用机构模块接口）
	 * 
	 * @category 根据机构代码获取机构信息
	 * @param empOrgList
	 * @return
	 */
	List<SysOrg> getExtOrgListByCode(List<String> orgcodeList);

	/**
	 * 根据角色id获取角色信息（调用角色模块接口）
	 * 
	 * @category 根据角色代码获取角色信息
	 */
	List<SysRole> getExtRoleList(List<SysUserRole> roleList);

	/**
	 * 根据登录人的机构代码获取审批人列表
	 * 
	 * @param myOrgcode 我的机构代码
	 * @param orgType   机构类型 （区科技局 or 市科技局 代码是数据字典自定义的）
	 * @param postId    审批人的岗位id
	 * @return
	 */
	List<SysEmployeeInfo> getApproveEmpList(String myOrgcode, String orgType, String postId);

	/**
	 * 根据用户ID查询用户的机构+岗位的列表
	 * 
	 * @param empid
	 * @return
	 */
	List<String> findEmplyeeOrgPostById(String empid);

	/**
	 * 根据用户ID查询用户的机构+岗位的列表
	 * 
	 * @param empid
	 * @return
	 * 
	 *         by G.fj 2021.12.3
	 */
	List<String> findEmplyeeOrgCode____PostById(String empid);

	/**
	 * 获取人员兼岗机构信息
	 * 
	 * @category 获取人员机构信息
	 * @return
	 */
	List<SysOrg> getExtPartOrgList(List<SysEmployeeOrgPost> partPostList);

	/**
	 * 根据用户ID查询用户的岗位ID和机构岗位ID
	 * 
	 * @param empid
	 * @return
	 * 
	 *         by G.fj 2022-11-11
	 */
	List<String> findEmplyeeOrgCodeMixPostById(String empid);
}
