package com.yunhesoft.system.employee.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import com.yunhesoft.system.auth.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.common.utils.Tree;
import com.yunhesoft.core.common.utils.TreeNode;
import com.yunhesoft.core.utils.PinYinUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.employee.entity.po.SysEmployeeChangeOrg;
import com.yunhesoft.system.employee.entity.po.SysEmployeeChangePost;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.employee.entity.vo.EmployeeChangeVo;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.IEmployeeExtraOperationService;
import com.yunhesoft.system.employee.service.ISysEmployeeChangeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.post.service.ISysPostService;

@Service
public class SysEmployeeChangeInfoImpl implements ISysEmployeeChangeInfoService {
    /**
     * 人员信息表操作服务接口
     */
    @Autowired
    private ISysEmployeeInfoService empInfoServ;
    /**
     * 人员扩展信息表操作服务接口
     */
    @Autowired
    private IEmployeeExtraOperationService employeeExtService;
    /**
     * 人员岗位信息表操作服务接口
     */
    @Autowired
    private ISysPostService postService;
    /**
     * 数据库操作类
     */
    @Autowired
    private EntityService entityService;

    /**
     * 机构信息操作服务接口
     */
    @Autowired
    private ISysOrgService orgService;


    private Date baseDate;//初始化时间

    /**
     * 登录信息服务接口
     */
    @Autowired
    private AuthService loginServ;


    /**
     * 记录人员机构变动信息
     *
     * @param changeDt         变动日期
     * @param changeEmpOrgList 变动机构列表
     * @return
     * @category 记录人员机构变动信息
     * <AUTHOR>
     */
    @Override
    public boolean saveUserOrgChange(String changeDt, List<SysEmployeeOrg> changeEmpOrgList) {
        // TODO Auto-generated method stub
        boolean result = true;
        if (StringUtils.isNotEmpty(changeDt) && StringUtils.isNotEmpty(changeEmpOrgList)) {// 参数不为空
            List<SysEmployeeChangeOrg> addList = new ArrayList<SysEmployeeChangeOrg>();
            List<SysEmployeeChangeOrg> updateList = new ArrayList<SysEmployeeChangeOrg>();
            List<SysEmployeeChangeOrg> deleteList = new ArrayList<SysEmployeeChangeOrg>();
            HashMap<String, SysEmployeeChangeOrg> hisMap = new HashMap<String, SysEmployeeChangeOrg>();

            List<String> userIdsList = new ArrayList<String>();//人员列表

            HashMap<String, SysEmployeeOrg> changeMap = new HashMap<String, SysEmployeeOrg>();// 变动数据map
            for (SysEmployeeOrg temp : changeEmpOrgList) {
                changeMap.put(temp.getEmpid(), temp);
                userIdsList.add(temp.getEmpid());
            }
//			if (StringUtils.isNotEmpty(changeEmpOrgList)) {// 参数不为空
//				changeMap=changeEmpOrgList.stream().collect(Collectors.toMap(SysEmployeeOrg::getEmpid,
//						SysEmployeeOrg -> SysEmployeeOrg, (key1, key2) -> key1));
//			}
            List<SysEmployeeChangeOrg> hisList = getEditOrgChangeList(changeDt, userIdsList);//获取历史的人员变动信息
            if (StringUtils.isNotEmpty(hisList)) {
                for (SysEmployeeChangeOrg temp : hisList) {
                    SysEmployeeChangeOrg hisData = hisMap.get(temp.getEmployeeId());
                    if (hisData == null) {
                        hisMap.put(temp.getEmployeeId(), temp);
                    } else {
                        deleteList.add(temp);//数据是按时间顺序排列的,找到第一个，就是要更新的数据，其他的都可以删除掉了（其他的比要更新的数据新，都要删除掉，保证从要更新的数据位数计算时间轴）
                    }
                }
            }

            Date changeDate = DateTimeUtils.parseDate(changeDt);//变动日期
            Date lastDate = DateTimeUtils.doDate(changeDate, -1);//变动日期的上一天
            for (String empId : userIdsList) {
                SysEmployeeOrg changeOrg = changeMap.get(empId);//获取本次的变动数据
                if (changeOrg != null) {//有本次的变动信息
                    boolean isDel = false;//是否是离职
                    if (changeOrg.getUsed() != null && changeOrg.getUsed().intValue() <= 0) {
                        isDel = true;
                    }
                    SysEmployeeChangeOrg hisData = hisMap.get(empId);//获取历史记录
                    if (hisData == null) {//没有历史数据，需要初始化历史数据
                        SysEmployeeChangeOrg bean = new SysEmployeeChangeOrg();
                        bean.setId(TMUID.getUID());
                        bean.setEmployeeId(empId);
                        bean.setOrgCode(changeOrg.getOrgcode());
//						bean.setStartDt(getBaseDate());
                        bean.setStartDt(changeDate);//以变动日期为准生成记录
                        bean.setUserStatus(changeOrg.getUsed());//记录人员当前状态
                        bean.setDepartStatus(0);
                        if (isDel) {//这个是删除操作
                            bean.setDepartStatus(1);//离职
                            bean.setEndDt(changeDate);//离职记录把最后一条的截止日期补全
                        }
                        addList.add(bean);
                    } else {
                        boolean isChangeStatus = false;//判断人员状态是否有变化
                        Integer hisStatus = hisData.getUserStatus();
                        if (hisStatus!=null){
                            Integer changeStatus = changeOrg.getUsed();
                            if(changeStatus==null){
                                changeStatus=1;
                            }
                            if(hisStatus.intValue()!=changeStatus.intValue()){
                                isChangeStatus=true;
                            }
                        }else{
                            isChangeStatus = true;
                        }
                        if ((changeOrg.getOrgcode() != null && !changeOrg.getOrgcode().equals(hisData.getOrgCode())) || isChangeStatus) {//机构有变动,或者是离职操作，都认为是变动
                            updateList.add(hisData);
                            if (DateTimeUtils.bjDate(hisData.getStartDt(), lastDate) == 1) {//往前计算一天结果比开始日期小了（也就是变动日期是等于这个开始日期的，那就需要更正当前记录即可，不需要新加记录了）
                                hisData.setUserStatus(changeOrg.getUsed());//记录人员当前状态
                                hisData.setOrgCode(changeOrg.getOrgcode());
                                hisData.setStartDt(changeDate);//需要更新成最新的变动日期（2种情况 1多次往同一天变动 2变动到最早变动日期之前）
                                hisData.setEndDt(null);
                                if (isDel) {//这个是删除操作
                                    hisData.setDepartStatus(1);//离职
                                    hisData.setEndDt(changeDate);//离职记录把最后一条的截止日期补全
                                }else {
                                	hisData.setDepartStatus(0);//非离职记录
                                }
                            } else {
                                hisData.setEndDt(lastDate);
                                SysEmployeeChangeOrg bean = new SysEmployeeChangeOrg();
                                bean.setId(TMUID.getUID());
                                bean.setEmployeeId(empId);
                                bean.setOrgCode(changeOrg.getOrgcode());
                                bean.setStartDt(changeDate);
                                bean.setUserStatus(changeOrg.getUsed());//记录人员当前状态
                                if (isDel) {//这个是删除操作
                                    bean.setDepartStatus(1);//离职
                                    bean.setEndDt(changeDate);//离职记录把最后一条的截止日期补全
                                }else {
                                    bean.setDepartStatus(0);
                                }
                                addList.add(bean);
                            }
                        } else {//机构没变动
                            boolean update = false;//是否更新
                            if (DateTimeUtils.bjDate(hisData.getStartDt(), changeDate) == 1) {//开始日期比变动日期大了，需要更新开始日期（这种情况只有在第一条变动记录的开始时间都比变动时间大的情况，才出现，也就是往最开始变动时间之前变动）
                                update = true;
                                hisData.setStartDt(changeDate);
                            }
                            if (hisData.getEndDt() != null) {//查到的不是最后一条记录，那需要把这条记录更新成最后记录
                                update = true;
                                hisData.setEndDt(null);
                            }
//							if(changeOrg.getUsed()!=null && changeOrg.getUsed().intValue()==0) {//如果是离职，要强制变动
//								update=true;
//							}
                            if (update) {
                                updateList.add(hisData);
                                hisData.setUserStatus(changeOrg.getUsed());
								if(isDel) {//这个是删除操作 离职走不到这里
									hisData.setDepartStatus(1);//离职
									hisData.setEndDt(changeDate);//离职记录把最后一条的截止日期补全
								}else {
                                    hisData.setDepartStatus(0);
                                    hisData.setEndDt(null);
								}
                            }
                        }
                    }
                }
            }
            result = this.saveOrgChangeDate(addList, updateList, deleteList);
        }
        return result;
    }

    /**
     * 记录人员岗位变动信息
     *
     * @param changeDt             变动日期
     * @param changeEmpOrgPostList 变动岗位列表
     * @return
     * @category 记录人员岗位变动信息
     * <AUTHOR>
     */
    @Override
    public boolean saveUserOrgPostChange(String changeDt, List<SysEmployeeOrgPost> changeEmpOrgPostList) {
        // TODO Auto-generated method stub
        boolean result = true;
        if (StringUtils.isNotEmpty(changeDt) && StringUtils.isNotEmpty(changeEmpOrgPostList)) {// 参数不为空
            List<SysEmployeeChangePost> addList = new ArrayList<SysEmployeeChangePost>();
            List<SysEmployeeChangePost> updateList = new ArrayList<SysEmployeeChangePost>();
            List<SysEmployeeChangePost> deleteList = new ArrayList<SysEmployeeChangePost>();
            HashMap<String, SysEmployeeChangePost> hisMap = new HashMap<String, SysEmployeeChangePost>();

            List<String> userIdsList = new ArrayList<String>();//人员列表

            HashMap<String, SysEmployeeOrgPost> changeMap = new HashMap<String, SysEmployeeOrgPost>();// 变动数据map
            for (SysEmployeeOrgPost temp : changeEmpOrgPostList) {
                changeMap.put(temp.getEmpid(), temp);
                userIdsList.add(temp.getEmpid());
            }
//			if (StringUtils.isNotEmpty(changeEmpOrgList)) {// 参数不为空
//				changeMap=changeEmpOrgList.stream().collect(Collectors.toMap(SysEmployeeOrg::getEmpid,
//						SysEmployeeOrg -> SysEmployeeOrg, (key1, key2) -> key1));
//			}
            List<SysEmployeeChangePost> hisList = getEditPostChangeList(changeDt, userIdsList);//获取历史的人员变动信息
            if (StringUtils.isNotEmpty(hisList)) {
                for (SysEmployeeChangePost temp : hisList) {
                    SysEmployeeChangePost hisData = hisMap.get(temp.getEmployeeId());
                    if (hisData == null) {
                        hisMap.put(temp.getEmployeeId(), temp);
                    } else {
                        deleteList.add(temp);//数据是按时间顺序排列的,找到第一个，就是要更新的数据，其他的都可以删除掉了（其他的比要更新的数据新，都要删除掉，保证从要更新的数据位数计算时间轴）
                    }
                }
            }

            Date changeDate = DateTimeUtils.parseDate(changeDt);//变动日期
            Date lastDate = DateTimeUtils.doDate(changeDate, -1);//变动日期的上一天
            List<String> delIdList = new ArrayList<>();//离职人员列表
            List<String> useingIdList = new ArrayList<>();//在用人员列表
            for (String empId : userIdsList) {
                SysEmployeeOrgPost changeOrg = changeMap.get(empId);//获取本次的变动数据
                if (changeOrg != null) {//有本次的变动信息
                    boolean isDel = false;//是否是离职
                    if (changeOrg.getUsed() != null && changeOrg.getUsed().intValue() <= 0) {
                        isDel = true;
                        delIdList.add(empId);
                    }else{
                        useingIdList.add(empId);
                    }
                    SysEmployeeChangePost hisData = hisMap.get(empId);//获取历史记录
                    if (hisData == null) {//没有历史数据，需要初始化历史数据
                        SysEmployeeChangePost bean = new SysEmployeeChangePost();
                        bean.setId(TMUID.getUID());
                        bean.setEmployeeId(empId);
                        bean.setOrgCode(changeOrg.getOrgcode());
                        bean.setPostId(changeOrg.getPostid());
//						bean.setStartDt(getBaseDate());
                        bean.setStartDt(changeDate);//以变动日期为准生成记录
                        bean.setUserStatus(changeOrg.getUsed());//记录人员当前状态
                        bean.setDepartStatus(0);
                        if (isDel) {//这个是删除操作
                            bean.setDepartStatus(1);//离职
                            bean.setEndDt(changeDate);//离职记录把最后一条的截止日期补全
                        }
                        addList.add(bean);
                    } else {
                        boolean isChangeStatus = false;//判断人员状态是否有变化
                        Integer hisStatus = hisData.getUserStatus();
                        if (hisStatus!=null){
                            Integer changeStatus = changeOrg.getUsed();
                            if(changeStatus==null){
                                changeStatus=1;
                            }
                            if(hisStatus.intValue()!=changeStatus.intValue()){
                                isChangeStatus=true;
                            }
                        }else{
                            isChangeStatus = true;
                        }
                        if (((changeOrg.getOrgcode() != null && !changeOrg.getOrgcode().equals(hisData.getOrgCode())) || (changeOrg.getPostid() != null && !changeOrg.getPostid().equals(hisData.getPostId()))) || isChangeStatus) {//机构岗位有变动,或者是离职等人员状态变动操作，都认为是变动
                            updateList.add(hisData);
                            if (DateTimeUtils.bjDate(hisData.getStartDt(), lastDate) == 1) {//往前计算一天结果比开始日期小了（也就是变动日期是等于这个开始日期的，那就需要更正当前记录即可，不需要新加记录了）
                                hisData.setUserStatus(changeOrg.getUsed());//记录人员当前状态
                                hisData.setOrgCode(changeOrg.getOrgcode());
                                hisData.setPostId(changeOrg.getPostid());
                                hisData.setStartDt(changeDate);//需要更新成最新的变动日期（2种情况 1多次往同一天变动 2变动到最早变动日期之前）
                                hisData.setEndDt(null);
                                if (isDel) {//这个是删除操作
                                    hisData.setDepartStatus(1);//离职
                                    hisData.setEndDt(changeDate);//离职记录把最后一条的截止日期补全
                                }else {
                                    hisData.setDepartStatus(0);//非离职记录
                                }
                            } else {
                                hisData.setEndDt(lastDate);
                                SysEmployeeChangePost bean = new SysEmployeeChangePost();
                                bean.setId(TMUID.getUID());
                                bean.setEmployeeId(empId);
                                bean.setOrgCode(changeOrg.getOrgcode());
                                bean.setPostId(changeOrg.getPostid());
                                bean.setStartDt(changeDate);
                                bean.setUserStatus(changeOrg.getUsed());//记录人员当前状态
                                if (isDel) {//这个是删除操作
                                    bean.setDepartStatus(1);//离职
                                    bean.setEndDt(changeDate);//离职记录把最后一条的截止日期补全
                                }else {
                                    bean.setDepartStatus(0);
                                }
                                addList.add(bean);
                            }
                        } else {//机构岗位没变动
                            boolean update = false;//是否更新
                            if (DateTimeUtils.bjDate(hisData.getStartDt(), changeDate) == 1) {//开始日期比变动日期大了，需要更新开始日期（这种情况只有在第一条变动记录的开始时间都比变动时间大的情况，才出现，也就是往最开始变动时间之前变动）
                                update = true;
                                hisData.setStartDt(changeDate);
                            }
                            if (hisData.getEndDt() != null) {//查到的不是最后一条记录，那需要把这条记录更新成最后记录
                                update = true;
                                hisData.setEndDt(null);
                            }
//							if(changeOrg.getUsed()!=null && changeOrg.getUsed().intValue()==0) {//如果是离职，要强制变动
//								update=true;
//							}
                            if (update) {
                                updateList.add(hisData);
                                hisData.setUserStatus(changeOrg.getUsed());
								if(isDel) {//这个是删除操作 删除走不到这
                                    hisData.setDepartStatus(1);//离职
                                    hisData.setEndDt(changeDate);//离职记录把最后一条的截止日期补全
                                }else {
                                    hisData.setDepartStatus(0);
                                    hisData.setEndDt(null);
								}
                            }
                        }
                    }
                }
            }
            result = this.savePostChangeDate(addList, updateList, deleteList);
            if(delIdList.size()>0){//离职人员更改登录状态，禁止登录
                loginServ.batchUpdateLoginStatus(delIdList,0);
            }
            if(useingIdList.size()>0){//在职人员更改登录状态，允许登录
                loginServ.batchUpdateLoginStatus(useingIdList,1);
            }
        }
        return result;
    }

    /**
     * 获取指定机构的人员变动信息
     *
     * @param changeDt       变动日期
     * @param orgCode        机构代码
     * @param readAllSubOrg  是否读取全部子机构的变动信息 true读取子机构，false不读取子机构，只读取给定的机构
     * @param readPostChange 是否读取岗位变动 true读取 false不读取。 不读取的情况下，人员的岗位信息为空
     * @return List<EmployeeVo>
     * @category 获取指定机构的人员变动信息
     * <AUTHOR>
     */
    @Override
    public List<EmployeeVo> getOrgUserChange(String changeDt, String orgCode, Boolean readAllSubOrg, Boolean readPostChange) {
        // TODO Auto-generated method stub
        List<EmployeeVo> result = new ArrayList<EmployeeVo>();
        if (StringUtils.isNotEmpty(changeDt) && StringUtils.isNotEmpty(orgCode)) {// 参数不为空
            List<String> orgCodeList = new ArrayList<String>();
            List<SysOrg> orgList = null;
            if (readAllSubOrg != null && readAllSubOrg.booleanValue()) {//需要递归查询全部子机构
                orgList = orgService.getOrgList(orgCode);
                if (StringUtils.isNotEmpty(orgList)) {//查找了指定机构和其全部子机构
                    for (SysOrg temp : orgList) {
                        orgCodeList.add(temp.getOrgcode());
                    }
                } else {
                    orgCodeList.add(orgCode);
                }
            } else {
                orgCodeList.add(orgCode);
            }
            result = getOrgUserChange(changeDt, orgCodeList, readPostChange, orgList);
//			List<SysEmployeeChangeOrg> orgChangeList = this.getOrgChangeDataByOrg(changeDt, orgCodeList);
//			List<SysEmployeeChangePost> postChangeList = new ArrayList<SysEmployeeChangePost>();
//			if(readPostChange!=null && readPostChange.booleanValue()) {//获取岗位变动
//				if(StringUtils.isNotEmpty(orgChangeList)) {
//					List<String> userIdList = new ArrayList<String>();
//					int size=0;
//					for(SysEmployeeChangeOrg temp:orgChangeList) {
//						userIdList.add(temp.getEmployeeId());//获取人员列表
//						size++;
//						if(size==500) {//500条查询一次
//							List<SysEmployeeChangePost> pList = getPostChangeDataByUser(changeDt, userIdList);	
//							if (StringUtils.isNotEmpty(pList)) {
//								postChangeList.addAll(pList);
//							}
//							userIdList.clear();
//							size=0;
//						}
//					}
//					if(userIdList.size()>0) {//还有没查的，最后再查一次
//						List<SysEmployeeChangePost> pList = getPostChangeDataByUser(changeDt, userIdList);	
//						if (StringUtils.isNotEmpty(pList)) {
//							postChangeList.addAll(pList);
//						}
//					}
////					postChangeList = getPostChangeDataByUser(changeDt, userIdList);	
//				}
//			}
//			List<SysEmployeeChangeOrg> orgChangeList = null;
//			List<SysEmployeeChangePost> postChangeList = null;
//			if(readPostChange!=null && readPostChange.booleanValue()) {//获取岗位变动
//				postChangeList =this.getPostChangeDataByOrg(changeDt, orgCodeList);
//			}else {//获取机构变动
//				orgChangeList = this.getOrgChangeDataByOrg(changeDt, orgCodeList);
//			}		
//			List<EmployeeChangeVo> changeList = this.createChangeData(orgChangeList, postChangeList);//创建变动记录
//			if (StringUtils.isNotEmpty(changeList)) {
//				result = getChangeEmployeeList(changeList,orgList);
//			}
        }
        return result;
    }

    /**
     * 获取指定机构的人员变动信息
     *
     * @param changeDt       变动日期
     * @param orgCodeList    机构代码列表
     * @param readAllSubOrg  是否读取全部子机构的变动信息 true读取子机构，false不读取子机构，只读取给定的机构
     * @param readPostChange 是否读取岗位变动 true读取 false不读取。 不读取的情况下，人员的岗位信息为空
     * @return List<EmployeeVo>
     * @category 获取指定机构的人员变动信息
     * <AUTHOR>
     */
    public List<EmployeeVo> getOrgUserChange(String changeDt, List<String> orgCodeList, Boolean readPostChange) {
        return getOrgUserChange(changeDt, orgCodeList, readPostChange, null);
    }

    private List<EmployeeVo> getOrgUserChange(String changeDt, List<String> orgCodeList, Boolean readPostChange, List<SysOrg> orgList) {
        List<EmployeeVo> result = new ArrayList<EmployeeVo>();
        List<SysEmployeeChangeOrg> orgChangeList = null;
        List<SysEmployeeChangePost> postChangeList = null;
        //long a = System.currentTimeMillis();
        if (readPostChange != null && readPostChange.booleanValue()) {//获取岗位变动
            postChangeList = this.getPostChangeDataByOrg(changeDt, orgCodeList);
            //a = System.currentTimeMillis() - a;
            //System.out.println("1:getPostChangeDataByOrg耗时：" + a);
        } else {//获取机构变动
            orgChangeList = this.getOrgChangeDataByOrg(changeDt, orgCodeList);
            //a = System.currentTimeMillis() - a;
            //System.out.println("getOrgChangeDataByOrg耗时：" + a);
        }
        //a = System.currentTimeMillis();
        List<EmployeeChangeVo> changeList = this.createChangeData(orgChangeList, postChangeList);//创建变动记录
        //a = System.currentTimeMillis() - a;
        //System.out.println("2:createChangeData：" + a);
        //a = System.currentTimeMillis();
        if (StringUtils.isNotEmpty(changeList)) {
            result = getChangeEmployeeList(changeList, orgList);
        }
        //a = System.currentTimeMillis() - a;
        //System.out.println("3:getOrgUserChange：" + a);
        return result;
    }

    /**
     * 获取指定岗位的人员变动信息
     *
     * @param changeDt      变动日期
     * @param orgCode       机构代码
     * @param postId        岗位id
     * @param readAllSubOrg 是否读取全部子机构的变动信息 true读取子机构，false不读取子机构，只读取给定的机构
     * @return List<EmployeeVo>
     * @category 获取指定岗位的人员变动信息
     * <AUTHOR>
     */
    @Override
    public List<EmployeeVo> getPostUserChange(String changeDt, String orgCode, String postId, Boolean readAllSubOrg) {
        List<EmployeeVo> result = new ArrayList<EmployeeVo>();
        if (StringUtils.isNotEmpty(postId)) {// 指定了岗位
            List<EmployeeVo> orgList = getOrgUserChange(changeDt, orgCode, readAllSubOrg, true);//获取待岗位的机构人员变动数据
            if (StringUtils.isNotEmpty(orgList)) {
                for (EmployeeVo temp : orgList) {
                    if (postId.equals(temp.getPostTmuid())) {//只取相同岗位的人员
                        result.add(temp);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 获取指定人员的变动信息
     *
     * @param changeDt       变动日期
     * @param userIdList     人员ID列表
     * @param readPostChange 是否读取岗位变动 true读取 false不读取。 不读取的情况下，人员的岗位信息为空
     * @return List<EmployeeVo>
     * @category 获取指定人员的变动信息
     * <AUTHOR>
     */
    @Override
    public List<EmployeeVo> getUserChangeList(String changeDt, List<String> userIdList, Boolean readPostChange) {
        // TODO Auto-generated method stub
        List<EmployeeVo> result = new ArrayList<EmployeeVo>();
        if (StringUtils.isNotEmpty(changeDt) && StringUtils.isNotEmpty(userIdList)) {// 参数不为空
            List<SysEmployeeChangeOrg> orgChangeList = null;
            List<SysEmployeeChangePost> postChangeList = null;
            if (readPostChange != null && readPostChange.booleanValue()) {//获取岗位变动
                postChangeList = getPostChangeDataByUser(changeDt, userIdList);
            } else {//获取机构变动
                orgChangeList = getOrgChangeDataByUser(changeDt, userIdList);
            }
            List<EmployeeChangeVo> changeList = this.createChangeData(orgChangeList, postChangeList);//创建变动记录
            if (StringUtils.isNotEmpty(changeList)) {
                result = getChangeEmployeeList(changeList, null);
            }
        }
        return result;
    }

    /**
     * 获取指定人员的变动信息
     *
     * @param changeDt       变动日期
     * @param userId         人员ID
     * @param readPostChange 是否读取岗位变动 true读取 false不读取。 不读取的情况下，人员的岗位信息为空
     * @return List<EmployeeVo>
     * @category
     * <AUTHOR>
     */
    @Override
    public EmployeeVo getUserChange(String changeDt, String userId, Boolean readPostChange) {
        // TODO Auto-generated method stub
        EmployeeVo result = null;
        List<String> userList = new ArrayList<String>();
        userList.add(userId);
        List<EmployeeVo> queryResult = getUserChangeList(changeDt, userList, readPostChange);
        if (StringUtils.isNotEmpty(queryResult)) {// 参数不为空
            result = queryResult.get(0);
        }
        return result;
    }

    /**
     * 生成变动数据
     *
     * @param orgChangeList
     * @param postChangeList
     * @return
     * @category
     * <AUTHOR>
     */
    private List<EmployeeChangeVo> createChangeData(List<SysEmployeeChangeOrg> orgChangeList, List<SysEmployeeChangePost> postChangeList) {
        List<EmployeeChangeVo> result = new ArrayList<EmployeeChangeVo>();
//		if (StringUtils.isNotEmpty(orgChangeList)) {// 参数不为空
//			Map<String, SysEmployeeChangePost> postMap = new HashMap<String, SysEmployeeChangePost>();// 数据map
//			if (StringUtils.isNotEmpty(postChangeList)) {// 参数不为空
//					postMap=postChangeList.stream().collect(Collectors.toMap(SysEmployeeChangePost::getEmployeeId,
//							SysEmployeeChangePost -> SysEmployeeChangePost, (key1, key2) -> key1));
//			}
//			for(SysEmployeeChangeOrg temp:orgChangeList) {
//				EmployeeChangeVo bean = ObjUtils.copyTo(temp, EmployeeChangeVo.class);// 复制属性
//				if(bean!=null) {
//					SysEmployeeChangePost post = postMap.get(temp.getEmployeeId());
//					if(post!=null) {
//						bean.setPostId(post.getPostId());
//					}
//					result.add(bean);
//				}
//			}
//		}	 

        if (StringUtils.isNotEmpty(postChangeList)) {//岗位不为空，按岗位生成变动
            for (SysEmployeeChangePost temp : postChangeList) {
                EmployeeChangeVo bean = new EmployeeChangeVo();//ObjUtils.copyTo(temp, EmployeeChangeVo.class);// 复制属性
                bean.setEmployeeId(temp.getEmployeeId());//人员ID
                bean.setOrgCode(temp.getOrgCode());//机构代码
                bean.setPostId(temp.getPostId());//岗位ID
                bean.setStartDt(temp.getStartDt());//开始日期
                bean.setEndDt(temp.getEndDt());//截止日期
                bean.setDepartStatus(temp.getDepartStatus());//离职标识 0在职 1离职
                result.add(bean);
            }
        } else if (StringUtils.isNotEmpty(orgChangeList)) {//机构不为空 按机构生成变动
            for (SysEmployeeChangeOrg temp : orgChangeList) {
                EmployeeChangeVo bean = new EmployeeChangeVo();//ObjUtils.copyTo(temp, EmployeeChangeVo.class);// 复制属性
                bean.setEmployeeId(temp.getEmployeeId());//人员ID
                bean.setOrgCode(temp.getOrgCode());//机构代码
//				bean.setPostId(temp.getPostId());//岗位ID
                bean.setStartDt(temp.getStartDt());//开始日期
                bean.setEndDt(temp.getEndDt());//截止日期
                bean.setDepartStatus(temp.getDepartStatus());//离职标识 0在职 1离职
                result.add(bean);
            }
        }

        return result;
    }

    /**
     * 根据变动信息获取人员信息
     *
     * @param changeList
     * @param orgList    人员机构列表
     * @return
     * @category
     * <AUTHOR>
     */
    private List<EmployeeVo> getChangeEmployeeList(List<EmployeeChangeVo> changeList, List<SysOrg> orgList) {
        List<EmployeeVo> result = new ArrayList<EmployeeVo>();
        if (StringUtils.isNotEmpty(changeList)) {// 参数不为空
            HashSet<String> orgCodeSet = new HashSet<String>();//机构列表
            //HashSet<String> postCodeSet = new HashSet<String>();//岗位列表
            LinkedHashMap<String, EmployeeChangeVo> changeMap = new LinkedHashMap<String, EmployeeChangeVo>();
            List<String> employeeIdList = new ArrayList<String>();//人员ID列表
            List<String> postIdList = new ArrayList<String>();//岗位ID列表
            for (EmployeeChangeVo temp : changeList) {
                if (StringUtils.isNotEmpty(temp.getOrgCode())) {
                    orgCodeSet.add(temp.getOrgCode());
                }
                if (StringUtils.isNotEmpty(temp.getPostId())) {
                    //postCodeSet.add(temp.getPostId());
                    if (!postIdList.contains(temp.getPostId())) {
                        postIdList.add(temp.getPostId());
                    }
                }
                changeMap.put(temp.getEmployeeId(), temp);
                if (!employeeIdList.contains(temp.getEmployeeId())) {
                    employeeIdList.add(temp.getEmployeeId());
                }
            }
            Map<String, SysOrg> orgMap = new HashMap<String, SysOrg>();// 数据map
            Map<String, SysPost> postMap = new HashMap<String, SysPost>();// 数据map
            if (StringUtils.isNotEmpty(orgList)) {//给定了机构列表
                sortOrg(orgList);//对传进来的机构进行大排序
                orgMap = orgList.stream().collect(Collectors.toMap(SysOrg::getOrgcode, SysOrg -> SysOrg, (key1, key2) -> key1));
            } else {
                if (orgCodeSet.size() > 0) {//有机构
                    List<String> orgCodeList = new ArrayList<String>();
                    orgCodeList.addAll(orgCodeSet);
                    //Long a = System.currentTimeMillis();
                    List<SysOrg> orgInfoList = employeeExtService.getExtOrgListByCode(orgCodeList);//查机构
                    //a = System.currentTimeMillis() - a;
                    //System.out.println("3.1查询机构耗时：" + a);
                    if (StringUtils.isNotEmpty(orgInfoList)) {// 参数不为空
                        sortOrg(orgList);//对传进来的机构进行大排序
                        orgMap = orgInfoList.stream().collect(Collectors.toMap(SysOrg::getOrgcode, SysOrg -> SysOrg, (key1, key2) -> key1));
                    }
                }
            }
//            Long b = System.currentTimeMillis();
            if (postIdList.size() > 0) {//有岗位
                postMap = postService.getPostByCode(postIdList);
                if (postMap == null) {
                    postMap = new HashMap<String, SysPost>();
                }

//                PostParamDto paramDto = new PostParamDto();
//                List<SysPost> postInfoList = new ArrayList<SysPost>();
//                int size = 0;
//                StringBuffer sql = new StringBuffer();
//                for (String tempPostId : postCodeSet) {
//                    sql.append("," + tempPostId);
//                    size++;
//                    if (size == 1000) {//1000条查询一次
//                        paramDto.setId(sql.substring(1));
//                        List<SysPost> queryList = postService.getPost(paramDto);//查岗位
//                        if (StringUtils.isNotEmpty(queryList)) {// 参数不为空
//                            postInfoList.addAll(queryList);
//                        }
//                        sql = new StringBuffer();
//                        size = 0;
//                    }
//                }
//                if (sql.length() > 0) {//还有没查的，最后再查一次
//                    paramDto.setId(sql.substring(1));
//                    List<SysPost> queryList = postService.getPost(paramDto);//查岗位
//                    if (StringUtils.isNotEmpty(queryList)) {// 参数不为空
//                        postInfoList.addAll(queryList);
//                    }
//                }
//                if (StringUtils.isNotEmpty(postInfoList)) {// 参数不为空
//                    postMap = postInfoList.stream().collect(Collectors.toMap(SysPost::getId, SysPost -> SysPost, (key1, key2) -> key1));
//                }
            }
//            b = System.currentTimeMillis() - b;
//            System.out.println("3.2查询岗位耗时：" + b);

            //b = System.currentTimeMillis();
            //if(employeeIdList.size()>0) {
            //long c = System.currentTimeMillis();
            //获取人员信息map，有限从缓存中获取
            Map<String, SysEmployeeInfo> empMap = empInfoServ.getEmployeeMap(employeeIdList);
            //c = System.currentTimeMillis() - c;
            //System.out.println("3.3c==getEmployeeMap：" + c);
//				for(SysEmployeeInfo temp:userList) {
            for (Entry<String, EmployeeChangeVo> entry : changeMap.entrySet()) {
//					EmployeeChangeVo changeVo = changeMap.get(temp.getId());//获取人员变动信息
                EmployeeChangeVo changeVo = entry.getValue();
                //SysEmployeeInfo userInfo =empInfoServ.findEmployeeById(entry.getKey());
                SysEmployeeInfo userInfo = null;
                if (empMap != null) {
                    userInfo = empMap.get(entry.getKey());
                }
//                if (userInfo == null) {
//                    userInfo = empInfoServ.findEmployeeById(entry.getKey());//从缓存中获取,循环获取速度慢
//                }
                //e = System.currentTimeMillis()-e;
                //System.out.println("3.3findEmployeeById："+e);
//					userInfo.gets
                if (userInfo != null) {
//					EmployeeVo bean = ObjUtils.copyTo(userInfo, EmployeeVo.class);// 复制属性
                    EmployeeVo bean = new EmployeeVo();
                    bean.setEmpTmuid(userInfo.getId());
                    bean.setEmpname(userInfo.getEmpname());
                    bean.setStaffNo(userInfo.getStaffNo());
                    bean.setMobile(userInfo.getMobile());
                    bean.setMemo(userInfo.getMemo()); 
                    bean.setSortStr(bean.getEmpname()==null?"":PinYinUtils.ToPinyinFirstChar(bean.getEmpname()));//用拼音排序                   
                    SysOrg tempOrg = orgMap.get(changeVo.getOrgCode());//获取机构
                    if (tempOrg != null) {
                        bean.setOrgTmuid(tempOrg.getId());
                        bean.setOrgcode(tempOrg.getOrgcode());
                        bean.setOrgname(tempOrg.getOrgname());
                        bean.setOrgNumber(tempOrg.getOrgNumber());
                        bean.setOrgSort(tempOrg.getTmSort());
                    }
                    SysPost tempPost = postMap.get(changeVo.getPostId());//获取岗位
                    if (tempPost != null) {
                        bean.setPostid(tempPost.getId());
                        bean.setPostTmuid(tempPost.getId());
                        bean.setPostname(tempPost.getName());
                        bean.setPostProfessionalInfoId(tempPost.getProfessionalInfoId());//专业id
                        bean.setPostSort(tempPost.getTmSort());
                    }
                    result.add(bean);
                }
            }
            //b = System.currentTimeMillis() - b;
            //System.out.println("3.3查询人员耗时：" + b);
            //b = System.currentTimeMillis();
            if (StringUtils.isNotEmpty(result)) {//查到了人
                //按机构排序
//                Collections.sort(result, new Comparator<EmployeeVo>() {
//                    @Override
//                    public int compare(EmployeeVo a, EmployeeVo b) {
//                        int result = 0;
//                        int aSort = a.getOrgSort() == null ? 99999999 : a.getOrgSort().intValue();
//                        int bSort = b.getOrgSort() == null ? 99999999 : b.getOrgSort().intValue();
//                        if (aSort > bSort) {
//                            result = 1;
//                        } else if (aSort < bSort) {
//                            result = -1;
//                        }
//                        return result;
//                    }
//                });
				Collections.sort(result, new Comparator<EmployeeVo>() {//根据姓名进行排序
					public int compare(EmployeeVo arg0, EmployeeVo arg1) {
						String sort0 = arg0.getSortStr()==null?"":arg0.getSortStr();
						String sort1 = arg1.getSortStr()==null?"":arg1.getSortStr();
						return sort0.compareTo(sort1);
					}
				});
            }
            //b = System.currentTimeMillis() - b;
            //System.out.println("3.4查询排序人员耗时：" + b);
        }
        return result;
    }

    /**
     * 获取一个机构的人员机构变动信息
     *
     * @param changeDt 变动日期
     * @param orgCode  机构代码
     * @return
     * @category 获取一个机构的人员机构变动信息
     * <AUTHOR>
     */
    private List<SysEmployeeChangeOrg> getOrgChangeDataByOrg(String changeDt, List<String> orgCodes) {
        List<SysEmployeeChangeOrg> result = new ArrayList<SysEmployeeChangeOrg>();
        if (StringUtils.isNotEmpty(changeDt) && StringUtils.isNotEmpty(orgCodes)) {
            Where where = Where.create();
            if (orgCodes.size() == 1) {
                where.eq(SysEmployeeChangeOrg::getOrgCode, orgCodes.get(0));
            } else {
                where.in(SysEmployeeChangeOrg::getOrgCode, orgCodes.toArray());
            }
            Date changeDate = DateTimeUtils.parseDate(changeDt);
            where.le(SysEmployeeChangeOrg::getStartDt, changeDate);
            where.and();
            where.lb();//左括号
            where.ge(SysEmployeeChangeOrg::getEndDt, changeDate);
            where.or();
            where.isNull(SysEmployeeChangeOrg::getEndDt);
            where.rb();//右括号
//			Order order = Order.create();
//			order.orderByAsc(SysEmployeeChangeOrg::getEmployeeId);
            List<SysEmployeeChangeOrg> queryList = entityService.queryList(SysEmployeeChangeOrg.class, where);
            if (StringUtils.isNotEmpty(queryList)) {// 不为空
                for (SysEmployeeChangeOrg temp : queryList) {
                    if (temp.getDepartStatus() == null || temp.getDepartStatus().intValue() == 0) {//只查在职的
                        result.add(temp);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 获取指定人员的机构变动信息
     *
     * @param changeDt 变动日期
     * @param orgCode
     * @return
     * @category 获取指定人员的机构变动信息
     * <AUTHOR>
     */
    private List<SysEmployeeChangeOrg> getOrgChangeDataByUser(String changeDt, List<String> userIds) {
        List<SysEmployeeChangeOrg> result = new ArrayList<SysEmployeeChangeOrg>();
        if (StringUtils.isNotEmpty(changeDt) && StringUtils.isNotEmpty(userIds)) {
            Where where = Where.create();
            if (userIds.size() == 1) {
                where.eq(SysEmployeeChangeOrg::getEmployeeId, userIds.get(0));
            } else {
                where.in(SysEmployeeChangeOrg::getEmployeeId, userIds.toArray());
            }
            Date changeDate = DateTimeUtils.parseDate(changeDt);
            where.le(SysEmployeeChangeOrg::getStartDt, changeDate);
            where.and();
            where.lb();//左括号
            where.ge(SysEmployeeChangeOrg::getEndDt, changeDate);
            where.or();
            where.isNull(SysEmployeeChangeOrg::getEndDt);
            where.rb();//右括号
//			Order order = Order.create();
//			order.orderByAsc(SysEmployeeChangeOrg::getEmployeeId);
            List<SysEmployeeChangeOrg> queryList = entityService.queryList(SysEmployeeChangeOrg.class, where);
            if (StringUtils.isNotEmpty(queryList)) {// 不为空
                for (SysEmployeeChangeOrg temp : queryList) {
                    if (temp.getDepartStatus() == null || temp.getDepartStatus().intValue() == 0) {//只查在职的
                        result.add(temp);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 获取一个机构的人员机构变动信息
     *
     * @param changeDt 变动日期
     * @param orgCode  机构代码
     * @return
     * @category 获取一个机构的人员机构变动信息
     * <AUTHOR>
     */
    private List<SysEmployeeChangePost> getPostChangeDataByOrg(String changeDt, List<String> orgCodes) {
        List<SysEmployeeChangePost> result = new ArrayList<SysEmployeeChangePost>();
        if (StringUtils.isNotEmpty(changeDt) && StringUtils.isNotEmpty(orgCodes)) {
            Where where = Where.create();
            if (orgCodes.size() == 1) {
                where.eq(SysEmployeeChangePost::getOrgCode, orgCodes.get(0));
            } else {
                where.in(SysEmployeeChangePost::getOrgCode, orgCodes.toArray());
            }
            Date changeDate = DateTimeUtils.parseDate(changeDt);
            where.le(SysEmployeeChangePost::getStartDt, changeDate);
            where.and();
            where.lb();//左括号
            where.ge(SysEmployeeChangePost::getEndDt, changeDate);
            where.or();
            where.isNull(SysEmployeeChangePost::getEndDt);
            where.rb();//右括号
//			Order order = Order.create();
//			order.orderByAsc(SysEmployeeChangeOrg::getEmployeeId);
            List<SysEmployeeChangePost> queryList = entityService.queryList(SysEmployeeChangePost.class, where);
            if (StringUtils.isNotEmpty(queryList)) {// 不为空
                for (SysEmployeeChangePost temp : queryList) {
                    if (temp.getDepartStatus() == null || temp.getDepartStatus().intValue() == 0) {//只查在职的
                        result.add(temp);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 获取指定人员的机构变动信息
     *
     * @param changeDt 变动日期
     * @param orgCode
     * @return
     * @category 获取指定人员的变动信息
     * <AUTHOR>
     */
    private List<SysEmployeeChangePost> getPostChangeDataByUser(String changeDt, List<String> userIds) {
        List<SysEmployeeChangePost> result = new ArrayList<SysEmployeeChangePost>();
        if (StringUtils.isNotEmpty(changeDt) && StringUtils.isNotEmpty(userIds)) {
            Where where = Where.create();
            if (userIds.size() == 1) {
                where.eq(SysEmployeeChangePost::getEmployeeId, userIds.get(0));
            } else {
                where.in(SysEmployeeChangePost::getEmployeeId, userIds.toArray());
            }
            Date changeDate = DateTimeUtils.parseDate(changeDt);
            where.le(SysEmployeeChangePost::getStartDt, changeDate);
            where.and();
            where.lb();//左括号
            where.ge(SysEmployeeChangePost::getEndDt, changeDate);
            where.or();
            where.isNull(SysEmployeeChangePost::getEndDt);
            where.rb();//右括号
//			Order order = Order.create();
//			order.orderByAsc(SysEmployeeChangeOrg::getEmployeeId);
            List<SysEmployeeChangePost> queryList = entityService.queryList(SysEmployeeChangePost.class, where);
            if (StringUtils.isNotEmpty(queryList)) {// 不为空
                for (SysEmployeeChangePost temp : queryList) {
                    if (temp.getDepartStatus() == null || temp.getDepartStatus().intValue() == 0) {//只查在职的
                        result.add(temp);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 查找用于编辑的人员机构变动信息
     *
     * @param changeDt 变动日期
     * @param userIds
     * @return
     * @category 查找用于编辑的人员岗位变动信息
     * <AUTHOR>
     */
    private List<SysEmployeeChangeOrg> getEditOrgChangeList(String changeDt, List<String> userIds) {
        List<SysEmployeeChangeOrg> result = new ArrayList<SysEmployeeChangeOrg>();
        Where where = Where.create();
        if (userIds.size() == 1) {
            where.eq(SysEmployeeChangeOrg::getEmployeeId, userIds.get(0));
        } else {
            where.in(SysEmployeeChangeOrg::getEmployeeId, userIds.toArray());
        }
        Date changeDate = DateTimeUtils.parseDate(changeDt);
//		where.le(SysEmployeeChangeOrg::getStartDt, changeDate);
        where.and();
        where.lb();//左括号
        where.ge(SysEmployeeChangeOrg::getEndDt, changeDate);
        where.or();
        where.isNull(SysEmployeeChangeOrg::getEndDt);
        where.rb();//右括号
        Order order = Order.create();
        order.orderByAsc(SysEmployeeChangeOrg::getStartDt);
        List<SysEmployeeChangeOrg> queryList = entityService.queryList(SysEmployeeChangeOrg.class, where);
        if (StringUtils.isNotEmpty(queryList)) {// 不为空
            result = queryList;
        }
        return result;
    }

    /**
     * 查找用于编辑的人员岗位变动信息
     *
     * @param changeDt 变动日期
     * @param userIds
     * @return
     * @category 查找用于编辑的人员机构变动信息
     * <AUTHOR>
     */
    private List<SysEmployeeChangePost> getEditPostChangeList(String changeDt, List<String> userIds) {
        List<SysEmployeeChangePost> result = new ArrayList<SysEmployeeChangePost>();
        Where where = Where.create();
        if (userIds.size() == 1) {
            where.eq(SysEmployeeChangePost::getEmployeeId, userIds.get(0));
        } else {
            where.in(SysEmployeeChangePost::getEmployeeId, userIds.toArray());
        }
        Date changeDate = DateTimeUtils.parseDate(changeDt);
//		where.le(SysEmployeeChangePost::getStartDt, changeDate);
        where.and();
        where.lb();//左括号
        where.ge(SysEmployeeChangePost::getEndDt, changeDate);
        where.or();
        where.isNull(SysEmployeeChangePost::getEndDt);
        where.rb();//右括号
        Order order = Order.create();
        order.orderByAsc(SysEmployeeChangePost::getStartDt);
        List<SysEmployeeChangePost> queryList = entityService.queryList(SysEmployeeChangePost.class, where);
        if (StringUtils.isNotEmpty(queryList)) {// 不为空
            result = queryList;
        }
        return result;
    }

    /**
     * 保存人员机构变动数据
     *
     * @param addList
     * @param updateList
     * @param deleteList
     * @return
     * @category 保存人员机构变动数据
     * <AUTHOR>
     */
    private boolean saveOrgChangeDate(List<SysEmployeeChangeOrg> addList, List<SysEmployeeChangeOrg> updateList, List<SysEmployeeChangeOrg> deleteList) {
        boolean result = true;
        if (StringUtils.isNotEmpty(addList)) {
            int a = entityService.insertBatch(addList);
            if (a <= 0) {
                result = false;
            }
        }
        if (StringUtils.isNotEmpty(updateList)) {
            int a = entityService.updateByIdBatchIncludeNull(updateList, 0);
            if (a <= 0) {
                result = false;
            }
        }
        if (StringUtils.isNotEmpty(deleteList)) {
            int a = entityService.deleteByIdBatch(deleteList);
            if (a <= 0) {
                result = false;
            }
        }
        return result;
    }

    /**
     * 保存人员机构变动数据
     *
     * @param addList
     * @param updateList
     * @param deleteList
     * @return
     * @category 保存人员机构变动数据
     * <AUTHOR>
     */
    private boolean savePostChangeDate(List<SysEmployeeChangePost> addList, List<SysEmployeeChangePost> updateList, List<SysEmployeeChangePost> deleteList) {
        boolean result = true;
        if (StringUtils.isNotEmpty(addList)) {
            int a = entityService.insertBatch(addList);
            if (a <= 0) {
                result = false;
            }
        }
        if (StringUtils.isNotEmpty(updateList)) {
            int a = entityService.updateByIdBatchIncludeNull(updateList, 0);
            if (a <= 0) {
                result = false;
            }
        }
        if (StringUtils.isNotEmpty(deleteList)) {
            int a = entityService.deleteByIdBatch(deleteList);
            if (a <= 0) {
                result = false;
            }
        }
        return result;
    }

    /**
     * 对指定机构进行大排序
     *
     * @param orgList 机构列表
     * @category
     * <AUTHOR>
     */
    private void sortOrg(List<SysOrg> orgList) {
        if (StringUtils.isNotEmpty(orgList)) {
            List<TreeNode<SysOrg>> treeNodeList = new ArrayList<TreeNode<SysOrg>>();
            TreeNode<SysOrg> rootNode = new TreeNode<SysOrg>("root", "", null);//根节点
            treeNodeList.add(rootNode);
            Map<String, SysOrg> orgMap = orgList.stream().collect(Collectors.toMap(SysOrg::getOrgpath, SysOrg -> SysOrg, (key1, key2) -> key1));
            for (SysOrg temp : orgList) {
                String pid = null;
                if (StringUtils.isNotEmpty(temp.getOrgpath())) {
//					pid=(temp.getOrgpath()+"____").replace("/"+temp.getId()+"____", "");//确保替换的是最后一个
                    pid = temp.getOrgpath().replaceAll("/" + temp.getId() + "$", "");//替换末尾的
                }
                if (pid == null || !orgMap.containsKey(pid)) {//树形上无上级节点，直接附加到根节点下面
                    pid = "root";
                } else {
                    //在树形上有上级节点，则附加到上级节点下面
                }
                TreeNode<SysOrg> node = new TreeNode<SysOrg>(temp.getOrgpath(), pid, temp);
                treeNodeList.add(node);
            }
            Tree<SysOrg> tree = new Tree<SysOrg>(rootNode);
            tree.buildTree(treeNodeList);//构建树形

            setOrgSort(tree.rootNode, new ArrayList<SysOrg>());//先根序访问计算节点排序
        }
    }

    /**
     * 递归设置机构大排序
     *
     * @category
     * <AUTHOR>
     */
    private void setOrgSort(TreeNode<SysOrg> node, List<SysOrg> sortList) {
        if (node.bean != null) {
            sortList.add(node.bean);
            node.bean.setTmSort(sortList.size());
        }
        if (StringUtils.isNotEmpty(node.childNodes)) {
            for (TreeNode<SysOrg> temp : node.childNodes) {//这里不需要对子节点重新排序，数据在查询出来的时候，已经带有了同级别节点排序
                setOrgSort(temp, sortList);
            }
        }
    }

    /**
     * 获取数据初始化基础日期
     *
     * @return
     * @category
     * <AUTHOR>
     */
    private Date getBaseDate() {
        if (this.baseDate == null) {
            this.baseDate = DateTimeUtils.parseDate("1900-01-01");
        }
        return this.baseDate;
    }
}
