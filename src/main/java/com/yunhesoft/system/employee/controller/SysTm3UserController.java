package com.yunhesoft.system.employee.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.system.employee.entity.vo.SysTm3UserTree;
import com.yunhesoft.system.employee.entity.vo.Tm3UserBeanVo;
import com.yunhesoft.system.employee.service.ISysTm3UserDetailService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@Api(tags = "获取TM3人员管理接口")
@RestController
@RequestMapping("/system/org/SysTm3UserDetail")
public class SysTm3UserController {

	@Autowired
	private ISysTm3UserDetailService sysTm3UserDetailService;

	@ApiOperation(value = "获得TM3人员信息", notes = "获得TM3人员信息")
	@RequestMapping(value = "/getTreeOrgList", method = { RequestMethod.GET })
	public List<SysTm3UserTree> getTreeUserList(
			@ApiParam(value = "TM3机构代码") String orgDm) {
		List<SysTm3UserTree> list = sysTm3UserDetailService.listTm3UserData(orgDm);
		return list;
	}
	
	
	
	@ApiOperation(value = "通过Tm3人员名称模糊检索获得TM3人员信息", notes = "通过Tm3人员名称模糊检索获得TM3人员信息")
	@RequestMapping(value = "/getTm3LikeUserNameList", method = { RequestMethod.GET })
	public List<SysTm3UserTree> getTm3LikeUserNameList(
			@ApiParam(value = "TM3人员名称") String userName) {
		List<SysTm3UserTree> list = sysTm3UserDetailService.listTm3LikeUserNameData(userName);
		return list;
	}
	
	@RequestMapping(value = "/getTm3LikeUserName", method = { RequestMethod.POST })
	public List<SysTm3UserTree> getTm3LikeUserName(@RequestBody Tm3UserBeanVo userBean) {
		List<SysTm3UserTree> list = sysTm3UserDetailService.listTm3LikeUserNameData(userBean);
		return list;
	}
	
	@RequestMapping(value = "/getUserIds", method = { RequestMethod.GET })
	public List<SysTm3UserTree> getUserIds(String ids) {
		List<SysTm3UserTree> list = sysTm3UserDetailService.getUserIds(ids);
		return list;
	}

}
