package com.yunhesoft.system.employee.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.PinYinUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.employee.entity.dto.EmpChangeDto;
import com.yunhesoft.system.employee.entity.dto.EmpInitLoginDto;
import com.yunhesoft.system.employee.entity.dto.EmpParamDto;
import com.yunhesoft.system.employee.entity.dto.EmpPartimePostParamDto;
import com.yunhesoft.system.employee.entity.dto.EmpTransferParamDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeOrgPostDto;
import com.yunhesoft.system.employee.entity.dto.EmployeePermDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeQueryDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeSaveDto;
import com.yunhesoft.system.employee.entity.dto.EmployeeSearchDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.vo.EmployeeDiaryVo;
import com.yunhesoft.system.employee.entity.vo.EmployeeExcelVo;
import com.yunhesoft.system.employee.entity.vo.EmployeeOrgPostVo;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.IEmployeeBasicOperationService;
import com.yunhesoft.system.employee.service.IEmployeeExtraOperationService;
import com.yunhesoft.system.employee.service.ISysEmployeeChangeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.kernel.utils.excel.ExcelImport;
import com.yunhesoft.system.post.service.ISysDiyPost;
import com.yunhesoft.system.professional.entity.vo.SysProfessionalInfoVo;
import com.yunhesoft.system.professional.service.ISysProfessionalInfoService;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
@Api(tags = "人员管理接口")
@RestController
@RequestMapping("/system/employee")
public class EmployeeController extends BaseRestController {

    /**
     * 基础操作服务接口（增、删、改、查）
     */
    @Autowired
    private IEmployeeBasicOperationService bscOperSrv;

    @Autowired
    private IEmployeeExtraOperationService extOperSrv;

    // 人员机构关系服务
    @Autowired
    private ISysEmployeeOrgService empOrgServ;
    // 人员变动信息
    @Autowired
    private ISysEmployeeChangeInfoService changeServ;

    @Autowired
    private IEmployeeBasicOperationService employeeBasicOperationService;

    @Autowired
    private ISysProfessionalInfoService sysProfessionalInfoService;
    @Autowired
    private ISysDiyPost diyPost;
    @Autowired
    private ISysConfigService sysConfigService;
    /**
     * 错误编码
     */
    private int errCode = 509;

    @Autowired
    private ISysEmployeeInfoService empService;

    /**
     * 添加人员信息
     *
     * @param employee
     * @return
     * @category 添加人员信息
     */
    @ResponseBody
    @RequestMapping(value = "/add", method = {RequestMethod.POST})
    @ApiOperation(value = "添加人员信息")
    @ApiImplicitParam(name = "listDto", value = "人员信息列表", required = true, paramType = "body", dataType = "EmployeeDto")
    public Res<String> add(@RequestBody List<EmployeeDto> listDto) {
        Res<String> resp = new Res<String>();
        resp.setSuccess(true);
        resp.setMessage("添加人员信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                resp.setSuccess(false);
                resp.setMessage("添加人员信息失败（数据为空）");
                return resp;
            }
            //获取默认时间
            String defaultChangeDate = this.getDefaultChangeDate();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date parse = sdf.parse(defaultChangeDate);
            for (EmployeeDto employeeDto : listDto) {
                employeeDto.setChangeDt(parse);
            }
            // 添加人员
            String err = bscOperSrv.addEmployee(listDto);
            if (err != null && !"".equals(err)) {
                resp.setSuccess(false);
                resp.setMessage("添加人员信息失败（" + err + "）");
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }

        return resp;
    }

    /**
     * 删除人员信息
     *
     * @param listDto
     * @return
     * @category 删除人员信息
     */
    @ResponseBody
    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    @ApiOperation(value = "删除人员信息")
    @ApiImplicitParam(name = "listDto", value = "人员信息列表", required = true, paramType = "body", dataType = "EmployeeDto")
    public Res<String> delete(@RequestBody List<EmployeeDto> listDto) {
        Res<String> resp = new Res<String>();
        resp.setSuccess(true);
        resp.setMessage("删除人员信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                resp.setSuccess(false);
                resp.setMessage("删除人员信息失败（数据为空）");
                return resp;
            }
            // 删除人员
            String err = bscOperSrv.delEmployee(listDto);
            if (err != null && !"".equals(err)) {
                resp.setSuccess(false);
                resp.setMessage("删除人员信息失败（" + err + "）");
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }

        return resp;
    }

    /**
     * 修改人员信息
     *
     * @param employee
     * @return
     * @category 修改人员信息
     */
    @ResponseBody
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    @ApiOperation(value = "修改人员信息")
    @ApiImplicitParam(name = "listDto", value = "人员信息列表", required = true, paramType = "body", dataType = "EmployeeDto")
    public Res<String> update(@RequestBody List<EmployeeDto> listDto) {
        Res<String> resp = new Res<String>();
        resp.setSuccess(true);
        resp.setMessage("修改人员信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                resp.setSuccess(false);
                resp.setMessage("修改人员信息失败（数据为空）");
                return resp;
            }
            // 修改人员
            String err = bscOperSrv.updEmployee(listDto);
            if (err != null && !"".equals(err)) {
                resp.setSuccess(false);
                resp.setMessage("修改人员信息失败（" + err + "）");
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }

        return resp;
    }

    /**
     * 批量增删改人员信息
     *
     * @param saveDto
     * @return
     * @category 批量增删改人员信息
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = {RequestMethod.POST})
    @ApiOperation(value = "批量增删改人员信息")
    @ApiImplicitParam(name = "saveDto", value = "新增人员信息列表", required = true, paramType = "body", dataType = "EmployeeSaveDto")
    public Res<?> save(@RequestBody EmployeeSaveDto saveDto) {
        Res<?> res = new Res<>();
        res.setMessage("保存人员信息成功");
        try {
            if (saveDto == null) {
                res.fail(this.errCode, "保存失败，人员信息为空");
                return res;
            }
            String err = bscOperSrv.saveEmployee(saveDto.getAddList(), saveDto.getDelList(), saveDto.getUpdList());
            if (StringUtils.isNotEmpty(err)) {
                res.fail(this.errCode, "保存失败，错误信息：" + err);
                return res;
            }
        } catch (Exception e) {
            log.error("", e);
            res.fail(this.errCode, "后台处理有误," + e.getMessage());
        }
        return res;
    }

    /**
     * 人员在职情况调整
     * <AUTHOR>
     * @date 2025/2/13
     * @params
     * @return
     *
    */
    @ResponseBody
    @RequestMapping(value = "/employeeMove", method = {RequestMethod.POST})
    public Res<?> employeeMove(@RequestBody List<EmployeeDto> listDto) {
        return Res.OK(employeeBasicOperationService.updEmployee(listDto));
    }

    @ResponseBody
    @RequestMapping(value = "/getEmployee", method = {RequestMethod.POST})
    @ApiOperation(value = "获取人员信息")
//	@ApiImplicitParam(name = "paramDto", value = "请求参数", required = true, paramType = "body", dataType = "EmpParamDto")
    public Res<List<EmployeeVo>> getEmployee(@RequestBody EmpParamDto paramDto) {
        List<EmployeeVo> list = new ArrayList<EmployeeVo>();
        Res<List<EmployeeVo>> resp = new Res<List<EmployeeVo>>();
        resp.setResult(list);
        resp.setSuccess(true);
        try {
            paramDto.setIsJurisdiction(true);
            if(paramDto.getUsed()==null){
                paramDto.setUsed(1);
            }
            list = bscOperSrv.getEmployee(paramDto);

            List<EmployeeVo> finalList = reSortPersonByOnlyPost(list);
            resp.setResult(finalList);
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }
        return resp;
    }

    private static List<EmployeeVo> reSortPersonByOnlyPost(List<EmployeeVo> list) {
        //人员重排序
        //岗位分组
        Map<String, List<EmployeeVo>> postGroup = Optional.ofNullable(list).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.groupingBy((key1) -> {
                    if (StringUtils.isNotEmpty(key1.getPostname()))
                        return key1.getPostname();
                    else
                        return "无岗位";
                }));
        //对于分组再次排序
        Map<Integer, List<EmployeeVo>> linkMap = new LinkedHashMap<>();
        postGroup.entrySet().stream().forEach(entry -> {
            //组内排序
            List<EmployeeVo> value = entry.getValue();
//            ZR1T9M7UQ093H3U46N8050
            value.sort(new Comparator<EmployeeVo>() {
                           @Override
                           public int compare(EmployeeVo o1, EmployeeVo o2) {
                               if (o1.getPostSort() != null && o2.getPostSort() != null) {
                                   if (o1.getPostSort() > (o2.getPostSort())) {
                                       return 1;
                                   } else if (o1.getPostSort() < (o2.getPostSort())) {
                                       return -1;
                                   } else {
                                       return 0;
                                   }
                               } else {
                                   if (o1.getPostSort() == null) {
                                       return 1;
                                   } else {
                                       return -1;
                                   }
                               }
                           }
                       }
            );
            //排序完毕
            List<EmployeeVo> orDefault = linkMap.getOrDefault(entry.getValue().get(0).getPostSort(), new ArrayList<>());
            orDefault.addAll(value);
            linkMap.put(entry.getValue().get(0).getPostSort(), orDefault);
        });
        //对linkMap重新排序
        List<EmployeeVo> finalList = new ArrayList<EmployeeVo>();
        linkMap.entrySet().stream().sorted(new Comparator<Map.Entry<Integer, List<EmployeeVo>>>() {
            @Override
            public int compare(Map.Entry<Integer, List<EmployeeVo>> o1, Map.Entry<Integer, List<EmployeeVo>> o2) {
                if (o1.getKey() != null && o2.getKey() != null) {
                    if (o1.getKey() > o2.getKey()) {
                        return 1;
                    } else if (o1.getKey() < o2.getKey()) {
                        return -1;
                    } else {
                        return 0;
                    }
                } else {
                    if (o1.getKey() != null && o2.getKey() == null) {
                        return -1;
                    } else if (o1.getKey() == null && o2.getKey() != null) {
                        return 1;
                    } else {
                        return 0;
                    }
                }
            }
        }).forEach(entry -> {
            finalList.addAll(entry.getValue());
        });
        return finalList;
    }

    /**
     * 添加人员调动信息
     *
     * @param employee
     * @return
     * @category 添加人员调动信息
     */
    @ResponseBody
    @RequestMapping(value = "/addTransfer", method = {RequestMethod.POST})
    @ApiOperation(value = "添加人员调动信息")
    @ApiImplicitParam(name = "listDto", value = "人员调动信息列表", required = true, paramType = "body", dataType = "EmployeeOrgPostDto")
    public Res<?> addTransfer(@RequestBody List<EmployeeOrgPostDto> listDto) {
        Res<?> resp = new Res<>();
        resp.setMessage("添加人员调动信息成功");
        try {
            if (listDto == null || listDto.size() <= 0) {
                resp.fail(this.errCode, "人员调动失败（数据为空）");
                return resp;
            }
            String info = bscOperSrv.addTransfer(listDto);
            if (StringUtils.isNotEmpty(info)) {
                resp.fail(this.errCode, "人员调动失败，失败原因：" + info);
                return resp;
            }
        } catch (Exception e) {
            log.error("", e);
            resp.fail(this.errCode, "后台处理有误，请查看日志");
        }

        return resp;
    }

    /**
     * 删除人员调动信息
     *
     * @param listDto
     * @return
     * @category 删除人员调动信息
     */
    @ResponseBody
    @RequestMapping(value = "/deleteTransfer", method = {RequestMethod.POST})
    @ApiOperation(value = "删除人员调动信息")
    @ApiImplicitParam(name = "listDto", value = "人员调动信息列表", required = true, paramType = "body", dataType = "EmployeeOrgPostDto")
    public Res<?> deleteTransfer(@RequestBody List<EmployeeOrgPostDto> listDto) {
        Res<?> res = new Res<String>();
        res.setMessage("删除人员调动信息成功");
        try {
            if (listDto == null || listDto.size() <= 0) {
                res.fail(this.errCode, "删除人员调动信息失败（数据为空）");
                return res;
            }
            String info = bscOperSrv.deleteTransfer(listDto);
            if (StringUtils.isNotEmpty(info)) {
                res.fail(this.errCode, "删除人员调动，失败原因：" + info);
                return res;
            }
        } catch (Exception e) {
            log.error("", e);
            res.fail(this.errCode, "后台处理有误，请查看日志");
        }
        return res;
    }

    /**
     * 修改人员调动信息
     *
     * @param employee
     * @return
     * @category 修改人员调动信息
     */
    @ResponseBody
    @RequestMapping(value = "/updateTransfer", method = {RequestMethod.POST})
    @ApiOperation(value = "修改人员调动信息")
    @ApiImplicitParam(name = "listDto", value = "人员调动信息列表", required = true, paramType = "body", dataType = "EmployeeOrgPostDto")
    public Res<?> updateTransfer(@RequestBody List<EmployeeOrgPostDto> listDto) {
        Res<?> res = new Res<>();
        res.setMessage("修改人员调动信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                res.fail(this.errCode, "修改人员调动信息失败（数据为空）");
                return res;
            }
            String info = bscOperSrv.updateTransfer(listDto);
            if (StringUtils.isNotEmpty(info)) {
                res.fail(this.errCode, "修改人员调动信息失败，失败原因：" + info);
                return res;
            }
        } catch (Exception e) {
            log.error("", e);
            res.fail(this.errCode, "后台处理有误，请查看日志");
        }

        return res;
    }

    @ResponseBody
    @RequestMapping(value = "/getTransfer", method = {RequestMethod.POST})
    @ApiOperation(value = "获取人员调动信息")
    @ApiImplicitParam(name = "paramDto", value = "请求参数", required = true, paramType = "body", dataType = "EmpTransferParamDto")
    public Res<List<EmployeeOrgPostVo>> getTransfer(@RequestBody EmpTransferParamDto paramDto) {
        Res<List<EmployeeOrgPostVo>> res = new Res<List<EmployeeOrgPostVo>>();
        try {
            List<EmployeeOrgPostVo> list = bscOperSrv.getTransfer(paramDto);
            res.setResult(list);
        } catch (Exception e) {
            log.error("", e);
            res.fail(this.errCode, "后台处理有误," + e.getMessage());
        }

        return res;
    }

    @ResponseBody
    @RequestMapping(value = "/getTransferDiary", method = {RequestMethod.POST})
    @ApiOperation(value = "获取人员调动流水信息")
    @ApiImplicitParam(name = "paramDto", value = "请求参数", required = true, paramType = "body", dataType = "EmpTransferParamDto")
    public Res<List<EmployeeDiaryVo>> getTransferDiary(@RequestBody EmpTransferParamDto paramDto) {
        Res<List<EmployeeDiaryVo>> res = new Res<List<EmployeeDiaryVo>>();
        try {
            List<EmployeeDiaryVo> list = bscOperSrv.getTransferDiary(paramDto);
            res.setResult(list);
        } catch (Exception e) {
            log.error("", e);
            res.fail(this.errCode, "后台处理有误," + e.getMessage());
        }
        return res;
    }

    /**
     * 添加人员兼岗信息
     *
     * @param employee
     * @return
     * @category 添加人员兼岗信息
     */
    @ResponseBody
    @RequestMapping(value = "/addPartimePost", method = {RequestMethod.POST})
    @ApiOperation(value = "添加人员兼岗信息")
    @ApiImplicitParam(name = "listDto", value = "人员兼岗信息列表", required = true, paramType = "body", dataType = "EmployeeOrgPostDto")
    public Res<?> addPartimePost(@RequestBody List<EmployeeOrgPostDto> listDto) {
        Res<?> res = new Res<>();
        res.setSuccess(true);
        res.setMessage("添加人员兼岗信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                res.setSuccess(false);
                res.setMessage("添加人员兼岗信息失败（数据为空）");
                return res;
            }
            String info = bscOperSrv.addPartimePost(listDto);
            if (StringUtils.isNotEmpty(info)) {
                res.fail(this.errCode, "添加人员兼岗信息失败，失败原因：" + info);
                return res;
            }
        } catch (Exception e) {
            log.error("", e);
            res.setSuccess(false);
            res.setMessage("后台处理有误，请查看日志," + e.getMessage());
        }

        return res;
    }

    /**
     * 删除人员兼岗信息
     *
     * @param listDto
     * @return
     * @category 删除人员兼岗信息
     */
    @ResponseBody
    @RequestMapping(value = "/deletePartimePost", method = {RequestMethod.POST})
    @ApiOperation(value = "删除人员兼岗信息")
    @ApiImplicitParam(name = "listDto", value = "人员兼岗信息列表", required = true, paramType = "body", dataType = "EmployeeOrgPostDto")
    public Res<?> deletePartimePost(@RequestBody List<EmployeeOrgPostDto> listDto) {
        Res<?> res = new Res<>();
        res.setSuccess(true);
        res.setMessage("删除人员兼岗信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                res.setSuccess(false);
                res.setMessage("删除人员兼岗信息失败（数据为空）");
                return res;
            }
            String info = bscOperSrv.deletePartimePost(listDto);
            if (StringUtils.isNotEmpty(info)) {
                res.fail(this.errCode, "删除人员兼岗信息失败，失败原因：" + info);
                return res;
            }
        } catch (Exception e) {
            log.error("", e);
            res.setSuccess(false);
            res.setMessage("后台处理有误，请查看日志");
        }

        return res;
    }

    /**
     * 修改人员兼岗信息
     *
     * @param employee
     * @return
     * @category 修改人员兼岗信息
     */
    @ResponseBody
    @RequestMapping(value = "/updatePartimePost", method = {RequestMethod.POST})
    @ApiOperation(value = "修改人员兼岗信息")
    @ApiImplicitParam(name = "listDto", value = "人员兼岗信息列表", required = true, paramType = "body", dataType = "EmployeeOrgPostDto")
    public Res<?> updatePartimePost(@RequestBody List<EmployeeOrgPostDto> listDto) {
        Res<?> res = new Res<>();
        res.setSuccess(true);
        res.setMessage("修改人员兼岗信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                res.setSuccess(false);
                res.setMessage("修改人员兼岗信息失败（数据为空）");
                return res;
            }
            String info = bscOperSrv.updatePartimePost(listDto);
            if (StringUtils.isNotEmpty(info)) {
                res.fail(this.errCode, "修改人员兼岗信息失败，失败原因：" + info);
                return res;
            }
        } catch (Exception e) {
            log.error("", e);
            res.setSuccess(false);
            res.setMessage("后台处理有误，请查看日志");
        }

        return res;
    }

    @ResponseBody
    @RequestMapping(value = "/getPartimePost", method = {RequestMethod.POST})
    @ApiOperation(value = "获取人员调动信息")
    @ApiImplicitParam(name = "paramDto", value = "请求参数", required = true, paramType = "body", dataType = "EmpPartimePostParamDto")
    public Res<List<EmployeeOrgPostVo>> getPartimePost(@RequestBody EmpPartimePostParamDto paramDto) {
        Res<List<EmployeeOrgPostVo>> res = new Res<List<EmployeeOrgPostVo>>();
        try {
            res.setResult(bscOperSrv.getPartimePost(paramDto));
        } catch (Exception e) {
            log.error("", e);
            res.fail(this.errCode, "后台处理有误," + e.getMessage());
        }
        return res;
    }

    @ResponseBody
    @RequestMapping(value = "/getPartimePostDiary", method = {RequestMethod.POST})
    @ApiOperation(value = "获取人员兼岗流水信息")
    @ApiImplicitParam(name = "paramDto", value = "请求参数", required = true, paramType = "body", dataType = "EmpPartimePostParamDto")
    public Res<List<EmployeeDiaryVo>> getPartimePostDiary(@RequestBody EmpPartimePostParamDto paramDto) {
        Res<List<EmployeeDiaryVo>> res = new Res<List<EmployeeDiaryVo>>();
        try {
            res.setResult(bscOperSrv.getPartimePostDiary(paramDto));
        } catch (Exception e) {
            log.error("", e);
            res.fail(this.errCode, "后台处理有误，" + e.getMessage());
        }

        return res;
    }

    /**
     * 添加人员借调信息
     *
     * @param employee
     * @return
     * @category 添加人员借调信息
     */
    @ResponseBody
    @RequestMapping(value = "/addOnloan", method = {RequestMethod.POST})
    @ApiOperation(value = "添加人员借调信息")
    @ApiImplicitParam(name = "listDto", value = "人员借调信息列表", required = true, paramType = "body", dataType = "EmployeeOrgPostDto")
    public Res<?> addOnloan(@RequestBody List<EmployeeOrgPostDto> listDto) {
        Res<?> resp = new Res<>();
        resp.setSuccess(true);
        resp.setMessage("添加人员借调信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                resp.setSuccess(false);
                resp.setMessage("添加人员借调信息成功（数据为空）");
                return resp;
            }
            String err = bscOperSrv.addOnloan(listDto);
            if (err != null && !"".equals(err)) {
                resp.setSuccess(false);
                resp.setMessage("添加人员借调信息失败（" + err + "）");
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }

        return resp;
    }

    /**
     * 删除人员借调信息
     *
     * @param listDto
     * @return
     * @category 删除人员借调信息
     */
    @ResponseBody
    @RequestMapping(value = "/deleteOnloan", method = {RequestMethod.POST})
    @ApiOperation(value = "删除人员借调信息")
    @ApiImplicitParam(name = "listDto", value = "人员借调信息列表", required = true, paramType = "body", dataType = "EmployeeOrgPostDto")
    public Res<String> deleteOnloan(@RequestBody List<EmployeeOrgPostDto> listDto) {
        Res<String> resp = new Res<String>();
        resp.setSuccess(true);
        resp.setMessage("删除人员借调信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                resp.setSuccess(false);
                resp.setMessage("删除人员借调信息失败（数据为空）");
                return resp;
            }
            String err = bscOperSrv.deleteOnloan(listDto);
            if (err != null && !"".equals(err)) {
                resp.setSuccess(false);
                resp.setMessage("删除人员借调信息失败（" + err + "）");
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }

        return resp;
    }

    /**
     * 修改人员借调信息
     *
     * @param employee
     * @return
     * @category 修改人员借调信息
     */
    @ResponseBody
    @RequestMapping(value = "/updateOnloan", method = {RequestMethod.POST})
    @ApiOperation(value = "修改人员借调信息")
    @ApiImplicitParam(name = "listDto", value = "人员借调信息列表", required = true, paramType = "body", dataType = "EmployeeOrgPostDto")
    public Res<String> updateOnloan(@RequestBody List<EmployeeOrgPostDto> listDto) {
        Res<String> resp = new Res<String>();
        resp.setSuccess(true);
        resp.setMessage("修改人员借调信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                resp.setSuccess(false);
                resp.setMessage("修改人员借调信息失败（数据为空）");
                return resp;
            }
            String err = bscOperSrv.updateOnloan(listDto);
            if (err != null && !"".equals(err)) {
                resp.setSuccess(false);
                resp.setMessage("修改人员借调信息失败（" + err + "）");
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }

        return resp;
    }

    /**
     * 获取审核人员列表
     *
     * @param myOrgcode 我的机构代码
     * @param orgType   机构类型 （区科技局 or 市科技局 代码是数据字典自定义的）
     * @param postId    审批人的岗位id
     * @return
     */
    @ApiOperation(value = "获取审核人员列表", notes = "获取审核人员列表")
    @RequestMapping(value = "getApproveEmpList", method = {RequestMethod.GET})
    public Res<?> getApproveEmpList(@RequestParam String myOrgcode, @RequestParam String orgType,
                                    @RequestParam String postId) {
        return Res.OK(extOperSrv.getApproveEmpList(myOrgcode, orgType, postId));
    }

    @ApiOperation(value = "获取用户登录信息", notes = "获取用户登录信息")
    @RequestMapping(value = "/getLoginInfo", method = {RequestMethod.GET})
    public Res<?> getLoginInfo(@RequestParam String id) {
        return Res.OK(bscOperSrv.getLoginInfo(id));
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "获取用户名全拼", notes = "获取用户名全拼")
    @GetMapping("/getPinyin")
    public Res<String> getPinyin(@RequestParam String userName) {
        return Res.OK(PinYinUtils.ToPinyin(userName));
    }

    @ApiOperation(value = "获取推荐的用户名", notes = "获取推荐的用户名")
    @RequestMapping(value = "getLoginName", method = {RequestMethod.POST})
    public Res<?> getLoginName(@RequestParam String userName, @RequestParam Integer type) {
        return Res.OK(bscOperSrv.getLoginName(userName, type));
    }

    @ApiOperation(value = "保存用户登录信息", notes = "保存用户登录信息")
    @PostMapping("/saveLoginInfo")
    public Res<?> saveLoginInfo(@RequestBody EmployeeDto dto) {
        return Res.OK(bscOperSrv.saveLoginInfo(dto));
    }

    @ApiOperation(value = "初始化全部用户登录信息", notes = "保存用户登录信息")
    @PostMapping("/initLoginInfoAll")
    public Res<?> initLoginInfoAll(@RequestBody EmpInitLoginDto dto) {
        return Res.OK(bscOperSrv.initLoginInfoAll(dto));
    }

    @ApiOperation(value = "初始化全部人员的登录密码", notes = "初始化登录密码")
    @PostMapping("/initPasswordForAllUser")
    public Res<?> initPasswordForAllUser() {
        return Res.OK(bscOperSrv.initPasswordForAllUser());
    }

    @ApiOperation(value = "初始化登录密码", notes = "初始化登录密码")
    @PostMapping("/initLoginPassword")
    public Res<?> initLoginPassword(@RequestBody List<String> idList) {
        return Res.OK(bscOperSrv.initLoginPassword(idList));
    }

    /**
     * 导出Excel
     *
     * @param querySysRoleDto
     */
    @RequestMapping(value = "/toExcel", method = RequestMethod.POST)
    @ApiOperation(value = "人员导出Excel")
    public void toExcel(@RequestBody EmpParamDto paramDto) {
        try {
            List<EmployeeVo> listData = new ArrayList<EmployeeVo>();
            if ("2".equals(paramDto.getExportType())) {// 导出数据
                listData = bscOperSrv.getEmployee(paramDto);
            }
            // String s = iser.getImportLevelChar();
            String sub = "1、ID列为标识列，空代表添加记录，否则为修改记录； 2、标题名称不可以修改；3、*列为必填列";

            Map<String, List<String>> selectMap = new HashMap<String, List<String>>();//
            // 设置下拉框
            selectMap.put("sex", new ArrayList<String>());
            selectMap.get("sex").add("男");
            selectMap.get("sex").add("女");
            selectMap.put("staffType", new ArrayList<String>());
            selectMap.get("staffType").add("全职");
            selectMap.get("staffType").add("兼职");
            selectMap.get("staffType").add("实习");
            selectMap.get("staffType").add("外派");
            selectMap.get("staffType").add("其它");

            List<SysProfessionalInfoVo> infoVos = sysProfessionalInfoService.getData(null);

            selectMap.put("professionalInfoId", new ArrayList<String>());
            Map<String, String> professions = new HashMap<>();
            if (StringUtils.isNotEmpty(infoVos)) {
                professions = infoVos.stream().collect(Collectors.toMap(SysProfessionalInfoVo::getId, SysProfessionalInfoVo::getProfessionalName));
                professions.put("null", "");
                for (SysProfessionalInfoVo beanVo : infoVos) {
                    String professionalName = beanVo.getProfessionalName();
                    selectMap.get("professionalInfoId").add(professionalName);
                }
            }
//			selectMap.get("professionalInfoId").add("全职");
//			selectMap.get("professionalInfoId").add("兼职");
//			selectMap.get("professionalInfoId").add("实习");
//			selectMap.get("professionalInfoId").add("外派");
//			selectMap.get("professionalInfoId").add("其它");

            // 岗位列表
            Map<String, String> mapPost = new HashMap<String, String>();
            List<String> listPost = new ArrayList<String>();

            bscOperSrv.getPostMap(mapPost, listPost, paramDto);
            // 角色列表
            Map<String, String> mapRole = new HashMap<String, String>();
            List<String> listRole = new ArrayList<String>();
            bscOperSrv.getRoleMap(mapRole, listRole);

            selectMap.put("postid", listPost);// 岗位下拉框
            selectMap.put("rolename", listRole);// 角色下拉框
            if (listData != null) {
                for (EmployeeVo vo : listData) {
                    if (vo.getPostid() == null) {
                        vo.setPostid("");
                    }
                    if (vo.getSex() == null) {
                        vo.setSex("");
                    }
                }
            }
            // 替换值
            Class<EmployeeVo> clazz = ExcelExport.setRepalce(EmployeeVo.class, "postid", mapPost);
            clazz = ExcelExport.setRepalce(clazz, "professionalInfoId", professions);
            List<EmployeeVo> finalList = reSortPersonByOnlyPost(listData);
            ExcelExport.exportExcel("人员信息", sub, false, clazz, finalList, selectMap, response);
        } catch (Exception e) {
            log.error("人员信息导出", e);
        }
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "人员数据导入")
    @RequestMapping(value = "/import", method = {RequestMethod.POST})
    public Res<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam("params") String params)
            throws Exception {
        // 导入时下拉框替换值
        Integer staffBitNum = empService.getStaffBitNum();
        // 岗位列表
        JSONObject paramObj = new JSONObject();
        if (StringUtils.isNotEmpty(params)) {
            paramObj = JSONObject.parseObject(params);
        }
        Map<String, String> mapPost = new HashMap<String, String>();
        EmpParamDto paramDto = new EmpParamDto();
        if (StringUtils.isNotEmpty(diyPost.isUseOrgDiyPost())) {
            paramDto.setOrgcode(paramObj.getString("orgcode"));
        }
        bscOperSrv.getPostMap(mapPost, null, paramDto);
        Class<EmployeeVo> clazz = ExcelImport.setRepalce(EmployeeVo.class, "postid", mapPost);
        boolean needVerify = false;
        ExcelImportResult<?> result = ExcelImport.importExcel(file.getInputStream(), clazz, 2, 1, needVerify);
        if (result != null) {
            if (result.isVerifyFail()) {// 校验失败
                return Res.OK("数据校验失败！");// Res.OK(result.getFailList());// 校验失败的数据
            }
            List<?> list = result.getList();// 导入的结果数据
            if (StringUtils.isNotEmpty(list)) {
                List<EmployeeVo> dataList = (List<EmployeeVo>) list;
                // 因easypoi的问题临时解决校验问题
                if (StringUtils.isNotEmpty(dataList)) {
                    List<EmployeeVo> employeeNameNull = dataList.stream()
                            .filter(item -> StringUtils.isEmpty(item.getEmpname())).collect(Collectors.toList());
                    if (StringUtils.isNotEmpty(employeeNameNull)) {
                        return Res.FAIL(500, "导入数据中姓名不能为空");
//						throw new Exception("导入数据中姓名不能为空");
                    }
                    List<EmployeeVo> employeeScaffNoNull = dataList.stream()
                            .filter(item -> StringUtils.isEmpty(item.getStaffNo())).collect(Collectors.toList());
                    if (StringUtils.isNotEmpty(employeeScaffNoNull)) {
                        return Res.FAIL(500, "导入数据中工号不能为空");
//						throw new Exception("导入数据中工号不能为空");
                    }
                    if(staffBitNum>0){
                        List<EmployeeVo> staffBitList = dataList.stream()
                                .filter(item -> item.getStaffNo().length()!=staffBitNum).collect(Collectors.toList());
                        if (StringUtils.isNotEmpty(staffBitList)) {
                            return Res.FAIL(500, "导入数据中工号长度必须是"+staffBitNum+"位");
                        }
                    }
                }
                if(StringUtils.isNotEmpty(dataList)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    for (EmployeeVo employeeVo : dataList) {
                        String defaultChangeDate = getDefaultChangeDate();
                        employeeVo.setChangeDt(sdf.parse(defaultChangeDate));
                    }
                }
                return Res.OK(bscOperSrv.importData(dataList, params));
            } else {
                return Res.OK("未解析出导入数据！");
            }
        }
        return Res.FAIL("");
    }

    @RequestMapping(value = "/getOrgIdByEmpId", method = RequestMethod.POST)
    @ApiOperation(value = "根据人员id获取机构信息")
    public Res<?> getOrgIdByEmpId(@RequestParam String empId, @RequestParam String type) {
        String orgcode = empOrgServ.getOrgIdByEmpId(empId, type);
        return Res.OK(orgcode);
    }

    @RequestMapping(value = "/getCurrentUser", method = RequestMethod.POST)
    @ApiOperation(value = "获取当前用户id")
    public Res<?> getCurrentUser() {
        return Res.OK(SysUserUtil.getCurrentUser().getId());
    }

    @ResponseBody
    @RequestMapping(value = "/getEmpChange", method = {RequestMethod.POST})
    @ApiOperation(value = "获取人员变动信息")
    @ApiImplicitParam(name = "paramDto", value = "请求参数", required = true, paramType = "body", dataType = "EmpChangeDto")
    public Res<List<EmployeeVo>> getEmpChange(@RequestBody EmpChangeDto paramDto) {
        Res<List<EmployeeVo>> res = new Res<List<EmployeeVo>>();
        if (paramDto != null) {
            try {
                if (StringUtils.isNotEmpty(paramDto.getEmpIds())) {// 有人员列表，直接查指定人员的变动信息
                    res.setResult(changeServ.getUserChangeList(paramDto.getChangeDt(), paramDto.getEmpIds(),
                            paramDto.getReadPostChange()));
                } else {// 无人员列表，按机构查询变动信息
                    res.setResult(changeServ.getOrgUserChange(paramDto.getChangeDt(), paramDto.getOrgCode(),
                            paramDto.getReadAllSubOrg(), paramDto.getReadPostChange()));
                }
            } catch (Exception e) {
                log.error("", e);
                res.fail(this.errCode, "后台处理有误," + e.getMessage());
            }
        }
        return res;
    }

    @RequestMapping(value = "/searchNameLike", method = RequestMethod.POST)
    @ApiOperation(value = "根据人员名称模糊查询人员列表")
    public Res<List<SysEmployeeInfo>> searchNameLike(@RequestBody EmployeeSearchDto dto) {
        if (dto.getName() == null || dto.getName().trim() == "") {
            ArrayList<SysEmployeeInfo> list = new ArrayList<SysEmployeeInfo>();
            return new Res<List<SysEmployeeInfo>>().ok(list);
        }
        List<String> fieldList = new ArrayList<String>();
        if (dto.getFields() != null) {
            for (String field : dto.getFields()) {
                fieldList.add(field);
            }
        }
        return new Res<List<SysEmployeeInfo>>().ok(empOrgServ.searchNameLike(dto.getName(), fieldList, dto.getLimit()));
    }

    @RequestMapping(value = "/queryEmployeeByParam", method = {RequestMethod.POST})
    @ApiOperation(value = "根据指定参数获取人员信息")
    public Res<?> queryEmployeeByParam(@RequestBody EmployeeQueryDto param) {
        return Res.OK(empService.queryEmployeeByParam(param));
    }

    @RequestMapping(value = "/queryEmployeeByPerm", method = {RequestMethod.POST})
    @ApiOperation(value = "根据指定权限获取人员信息")
    public Res<?> queryEmployeeByPerm(@RequestBody EmployeePermDto param) {
        return Res.OK(empService.queryEmployeeByPerm(param));
    }


    /**
     * 获取机构下的所有人员信息
     *
     * @param orgcode 机构代码
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getEmployeeByOrgcode", method = {RequestMethod.POST})
    @ApiOperation(value = "根据机构代码获取人员信息")
    public Res<List<EmployeeVo>> getEmployeeByOrgcode(@RequestParam String orgcode, @RequestParam String empname, @RequestParam Integer pageNum, @RequestParam Integer size) {
        Pagination<?> page = new Pagination<>(pageNum, size, 0);
        List<EmployeeVo> list = new ArrayList<EmployeeVo>();
        Res<List<EmployeeVo>> resp = new Res<List<EmployeeVo>>();
        resp.setResult(list);
        resp.setSuccess(true);
        try {
            //paramDto.setIsJurisdiction(true);
            EmpParamDto paramDto = new EmpParamDto();
            paramDto.setOrgcode(orgcode);
            paramDto.setEmpname(empname);
            list = bscOperSrv.getEmployeeByOrgcode(paramDto, page);
            if (StringUtils.isNotEmpty(list)) {
                resp.setTotal(page.getTotal());
            }
            resp.setResult(list);
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }
        return resp;
    }


    /**
     * 获得人员变动记录
     *
     * @param changedt 变动日期
     * @param orgcode  机构编码，多个逗号分隔
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getOrgUserChange", method = {RequestMethod.POST})
    @ApiOperation(value = "根据机构代码获取人员变动信息")
    public Res<List<EmployeeVo>> getEmployeeByOrgcode(@RequestParam String changedt, @RequestParam String orgcode) {
        List<EmployeeVo> list = new ArrayList<EmployeeVo>();
        Res<List<EmployeeVo>> resp = new Res<List<EmployeeVo>>();
        resp.setResult(list);
        resp.setSuccess(true);
        try {
            String[] orgs = orgcode.split(",");
            if (StringUtils.isEmpty(changedt)) {
                changedt = DateTimeUtils.getDate();
            }
            long a = System.currentTimeMillis();
            list = changeServ.getOrgUserChange(changedt, Arrays.asList(orgs), true);
            a = System.currentTimeMillis() - a;
            log.info("查询人员变动记录耗时：" + a + "ms");
            if (StringUtils.isNotEmpty(list)) {
                resp.setTotal(list.size());
            }
            resp.setResult(list);
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }
        return resp;
    }


    @ApiOperation(value = "获取默认的变动日期")
    @ResponseBody
    @RequestMapping(value = "/getDefalutEmpoloyeeChangeDate", method = {RequestMethod.POST})
    public Res<?> getDefalutEmpoloyeeChangeDate() {
        String defaultDate = getDefaultChangeDate();
        return Res.OK(defaultDate);
    }

    private String getDefaultChangeDate() {
        String defaultEmployeeChangeDate = sysConfigService.getSysConfig("defaultEmployeeChangeDate");
        String defaultDate = "";
        //获取默认的变动日期
        if("0".equals(defaultEmployeeChangeDate)){
            //当前月
            defaultDate = DateTimeUtils.getDate();
        }else{
            //上个月
            Date date1 = DateTimeUtils.doMonth(new Date(), -1);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            defaultDate = sdf.format(date1);
        }
        //月一号
        defaultDate = defaultDate.substring(0,7)+"-01";
        return defaultDate;
    }

    @ApiOperation(value = "是否只允许将人员放在最后一个机构上")
    @ResponseBody
    @RequestMapping(value = "/isonlySelectEndOrg", method = {RequestMethod.POST})
    public Res<?> isonlySelectEndOrg() {
        String defaultEmployeeChangeDate = sysConfigService.getSysConfig("isonlySelectEndOrg");
        String defaultDate = "";
        if("1".equals(defaultEmployeeChangeDate)){
            //仅允许将人员放在最后一个机构上
            return Res.OK(true);
        }else{
            return Res.OK(false);
        }
    }
    @ApiOperation(value = "是否允许在人员信息编辑窗口进行人员调动")
    @ResponseBody
    @RequestMapping(value = "/isAllowEditChange", method = {RequestMethod.POST})
    public Res<?> isAllowEditChange() {
        String defaultEmployeeChangeDate = sysConfigService.getSysConfig("isAllowEditChange");
        String defaultDate = "";
        if("1".equals(defaultEmployeeChangeDate)){
            //是否允许调动
            return Res.OK(true);
        }else{
            return Res.OK(false);
        }
    }

    @ApiOperation(value = "工号位数")
    @ResponseBody
    @RequestMapping(value = "/getStaffBitNum", method = {RequestMethod.POST})
    public Res<?> getStaffBitNum() {
        Integer staffBitNumStr = empService.getStaffBitNum();
        return Res.OK(staffBitNumStr);
    }

    @ApiOperation(value = "人员导出")
    @ResponseBody
    @RequestMapping(value = "/exportEmployee", method = {RequestMethod.POST})
    public void exportEmployee(@RequestBody EmpParamDto paramDto) {
        paramDto.setSize(0);
        paramDto.setExportType("2");
        Res<List<EmployeeVo>> employee = this.getEmployee(paramDto);
        List<EmployeeVo> result = employee.getResult();
        List<EmployeeExcelVo> finalList = new ArrayList<>();
        for (EmployeeVo employeeVo : result) {
            EmployeeExcelVo vo = ObjUtils.copyTo(employeeVo, EmployeeExcelVo.class);
            finalList.add(vo);
        }
        ExcelExport.exportExcel("人员信息", "", false, EmployeeExcelVo.class, finalList, null, response);
    }
//    @ApiOperation(value = "测试")
//    @ResponseBody
//    @RequestMapping(value = "/test", method = {RequestMethod.POST})
//    public Res<?> test() {
//    	List<String> a = new ArrayList<String>();
//    	a.add("ZQXHPL5MP02L17H8SM0164");
//    	a.add("ZQXHPL5MT02L17HGWD0119");
//    	a.add("ZR27GDRI00007B37UW0404");
//    	a.add("ZQXHO5GLU02HYW9MN60575");
//        return Res.OK(empService.findEmployeeByStaffNo("20250428"));
//    }
}
