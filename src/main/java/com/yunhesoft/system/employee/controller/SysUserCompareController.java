package com.yunhesoft.system.employee.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.system.employee.entity.po.SysUserCompare;
import com.yunhesoft.system.employee.service.ISysUserCompareService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "人员对照表管理")
@RestController
@RequestMapping("/system/employee/SysUserCompare")
public class SysUserCompareController {

	@Autowired
	private ISysUserCompareService iSysUserCompareService;

	/**
	 * 获取数据
	 * 
	 * @param targetOrgUserId TM4人员ID
	 * @return List<SysUserCompare>
	 */
	@ApiOperation(value = "获得TM3人员对照信息", notes = "获得TM3人员对照信息")
	@RequestMapping(value = "/getSysUserCompare", method = { RequestMethod.POST })
	public List<SysUserCompare> getSysUserCompare(@RequestBody SysUserCompare data) {
		String targetOrgUserId = data.getTargetOrgUserId();
		List<SysUserCompare> list = iSysUserCompareService.listData(targetOrgUserId);
		return list;
	}

	/**
	 * 更新数据
	 * 
	 * 将获取的数据中相关联的数据全部清除，然后在新增
	 * 
	 * @param dates
	 * @return boolean
	 */
	@ApiOperation(value = "更新人员对照信息", notes = "更新人员对照信息")
	@RequestMapping(value = "/updateSysUserCompare", method = { RequestMethod.POST })
	public boolean updateSysUserCompare(@RequestBody List<SysUserCompare> datas) {
		Boolean key = false;
		List<SysUserCompare> addlist = new ArrayList<SysUserCompare>();
		List<SysUserCompare> updatalist = new ArrayList<SysUserCompare>();
		if (datas != null) {
			if (datas != null && datas.size() > 0) {
				for (SysUserCompare SysUserCompare : datas) {
					Integer rowflag = SysUserCompare.getRowflag();
					if (rowflag == 0) {
						// 添加
						SysUserCompare.setId(TMUID.getUID());
						addlist.add(SysUserCompare);
					} else if (rowflag == 1) {
						// 修改
						updatalist.add(SysUserCompare);
					}
				}
				try {
					if (addlist.size() > 0) {
						iSysUserCompareService.insertData(addlist);
					}
					if (updatalist.size() > 0) {
						iSysUserCompareService.updateData(updatalist);
					}
					key = true;
				} catch (Exception e) {
					e.printStackTrace();
					// TODO: handle exception
				}
			}
		}
		return key;
	}
	
	
	
	/**
	 * 删除数据
	 * 
	 * @param SysOrgCompare
	 * @return boolean
	 */
	@ApiOperation(value = "删除机构对照信息", notes = "删除机构对照信息")
	@RequestMapping(value = "/deleteSysUserCompare", method = { RequestMethod.POST })
	public boolean deleteSysOrgCompare(@RequestBody List<SysUserCompare> datas) {
		boolean key = false;
		try {
			if (datas.size() > 0) {
				for (SysUserCompare sysUserCompare : datas) {
					String id = sysUserCompare.getId();
					iSysUserCompareService.deleteData(id);
				}
				
			}
			key = true;
		} catch (Exception e) {
			// TODO: handle exception
		}
		return key;
	}

}
