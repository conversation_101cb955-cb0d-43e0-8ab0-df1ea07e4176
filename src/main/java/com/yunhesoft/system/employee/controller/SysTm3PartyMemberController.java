package com.yunhesoft.system.employee.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.employee.service.ISysTm3PartyMemberService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "同步TM3党员接口")
@RestController
@RequestMapping("/system/employee/SysTm3PartyMember")
public class SysTm3PartyMemberController {

	@Autowired
	private ISysTm3PartyMemberService sysTm3PartyMemberService;

	@ApiOperation(value = "同步TM3党员", notes = "同步TM3党员")
	@RequestMapping(value = "/syncPartyMember", method = {RequestMethod.GET})
	public Res<?> syncPartyMember() {
		return Res.OK(sysTm3PartyMemberService.syncPartyMember());
	}
	
}
