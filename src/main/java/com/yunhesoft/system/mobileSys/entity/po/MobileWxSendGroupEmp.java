package com.yunhesoft.system.mobileSys.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 接收小组明细
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "MOBILE_WX_SEND_GROUP_EMP")
public class MobileWxSendGroupEmp extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 小组id */
	@Column(name = "GROUPID", length = 50)
	private String groupid;

	/** 人员id */
	@Column(name = "EMPID", length = 50)
	private String empid;

}