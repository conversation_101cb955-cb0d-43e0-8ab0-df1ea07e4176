package com.yunhesoft.system.mobileSys.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 微信接收对象
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "MOBILE_WX_SEND_OBJ")
public class MobileWxSendObj extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 父编号 */
	@Column(name = "PID", length = 50)
	private String pid;

	/** 接收对象类型 */
	@Column(name = "OBJTYPE")
	private Integer objType;

	/** 接收对象id */
	@Column(name = "OBJID", length = 50)
	private String objId;

}
