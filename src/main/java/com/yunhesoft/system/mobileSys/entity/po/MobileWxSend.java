package com.yunhesoft.system.mobileSys.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 发送微信设置
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "MOBILE_WX_SEND")
public class MobileWxSend extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 名称 */
	@Column(name = "NAME", length = 300)
	private String name;

	/** 描述 */
	@Column(name = "MEMO", length = 500)
	private String memo;

	/** 标题 */
	@Column(name = "MSGTITLE", length = 500)
	private String msgTitle;

	/** 消息内容 */
	@Column(name = "MSGCONTENT", length = 4000)
	private String msgContent;

	/** 消息显示类型  消息类型(*微信生效)(目前只支持前两种[text],[textcard])；类型列表：[text],[textcard],[image],[voice],[file],[news],[mpnews]*/
	@Column(name = "MSGTYPE", length = 30)
	private String msgType;

	/** 发送消息类型  1：数据源，2：超链接 */
	@Column(name = "SENDTYPE")
	private Integer sendType;

	/** 超链接地址 */
	@Column(name = "WEBHTTP", length = 500)
	private String webHttp;
	
	/** 是否关联日历 1:关联日历（跳过节假日） 0:不关联日历(默认)*/
	@Column(name = "ISLINKCALENDAR")
	private Integer isLinkCalendar;

	/** 数据源别名 */
	@Column(name = "TDSALIAS", length = 255)
	private String tdsAlias;

	/** 数据源输入参数 */
	@Column(name = "INPARAALIAS", length = 500)
	private String inparaAlias;

	/** 数据源输入参数 - 前台用于解析的原始值*/
	@Column(name = "INPARAALIAS_ORIG", length = 500)
	private String inparaAlias_Orig;
	
	/** 地址栏参数 */
	@Column(name = "URLPARAMS", length = 500)
	private String urlParams;

	/** TDSZYID */
	@Column(name = "TDSZYID", length = 50)
	private String tdsZyid;

	/** 发送消息模式 */
	@Column(name = "RECEPTIONTYPE")
	private Integer receptionType; // 发送消息模式：1：系统消息2：微信消息3：短信消息

	/** 发送周期 */
	@Column(name = "TIMETEMPLET", length = 50)
	private String timeTemplet;

	/** 周期描述 */
	@Column(name = "TIMEMEMO", length = 300)
	private String timeMemo;

	/** 是否使用 */
	@Column(name = "TMUSED")
	private Integer tmUsed;

	/** 单条推送：1：单条，0：整体(默认) */
	@Column(name = "SINGLESEND")
	private Integer singleSend;


}
