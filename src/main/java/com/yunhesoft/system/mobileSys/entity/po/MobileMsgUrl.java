package com.yunhesoft.system.mobileSys.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 发送消息地址配置
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "MOBILE_MSG_URL")
public class MobileMsgUrl extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 模块编码 */
	@Column(name = "MODULECODE", length = 100)
	private String modulecode;

	/** 功能名称 */
	@Column(name = "FUNNAME", length = 100)
	private String funname;

	/** 功能编码 */
	@Column(name = "FUNCODE", length = 100)
	private String funcode;

	/** 移动端地址 */
	@Column(name = "APPURL", length = 500)
	private String appurl;

	/** 移动端地址参数 */
	@Column(name = "APPURL_PARAMS", length = 500)
	private String appurlParams;

	/** 是否使用批量审批 */
	@Column(name = "BATCH_USE")
	private Integer batchUse;

	/** 批量处理地址 */
	@Column(name = "BATCH_APPURL", length = 500)
	private String batchAppurl;

	/** 批量处理地址 */
	@Column(name = "BATCH_APPURL_PARAMS", length = 500)
	private String batchAppurlParams;

	/** 备注 */
	@Column(name = "MEMO", length = 2000)
	private String memo;

	/** 排序 */
	@Column(name = "TMSORT")
	private Integer tmsort;

	/** 是否使用 */
	@Column(name = "TMUSED")
	private Integer tmused;

}
