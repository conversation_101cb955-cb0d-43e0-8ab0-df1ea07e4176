package com.yunhesoft.system.mobileSys.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

@Entity
@Setter
@Getter
@Table(name = "MOBILE_USERBIND")
public class MobileUserBind extends BaseEntity {

	private static final long serialVersionUID = 1L;

	@Column(name = "sysCode", length = 50)
	private String sysCode;

	@Column(name = "sysUser", length = 50)
	private String sysUser;

	@Column(name = "zyid")
	private String zyid;

	@Column(name = "msg", length = 200)
	private String msg;
}
