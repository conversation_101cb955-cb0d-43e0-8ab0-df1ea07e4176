package com.yunhesoft.system.mobileSys.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * AMediaFile UTF-8 code
 */

@Entity
@Setter
@Getter
@Table(name = "MOBILE_MEDIAFILE")
public class MobileMediaFile extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 模块编码
	 */
	@Column(name = "moduleCode", length = 500)
	private String moduleCode;

	/**
	 * 数据ID：相关模块的ID自定义存入，例如任务反馈表，存入反馈task_feedback.tmuid
	 */
	@Column(name = "dataId", length = 500)
	private String dataId;

	/**
	 * 文件名包括扩展名
	 */
	@Column(name = "fileName", length = 500)
	private String fileName;

	/**
	 * 文件路径
	 */
	@Column(name = "filePath", length = 1000)
	private String filePath;

	/**
	 * 扩展名
	 */
	@Column(name = "fileExpandedName", length = 10)
	private String fileExpandedName;

	/**
	 * 文件绝对路径（全路径）规则：[系统参数FileUpLoadPath] + [字段type] + [字段moduleCode] + [字段fileName]
	 * + [字段fileExpandedName]
	 */
	@Column(name = "fileFullPathName", length = 1500)
	private String fileFullPathName;

	/**
	 * 文件相对路径（不包含系统设置的相对路径/upLoadFiles）（示例：/upLoadFiles[/WeiXin/0505]只存方括号部分）
	 */
	@Column(name = "fileRelativePath", length = 1000)
	private String fileRelativePath;

	/**
	 * 图片来源地址
	 */
	@Column(name = "srcUrl", length = 1500)
	private String srcUrl;

	/**
	 * 类型：如，微信(WeiXin)等。
	 */
	@Column(name = "type", length = 100)
	private String type;

	/**
	 * 上传文件时的错误信息
	 */
	@Column(name = "error", length = 1000)
	private String error;

	/**
	 * 创建记录时间
	 */
	@Column(name = "ctime", length = 23)
	private Date ctime;

	/**
	 * 是否使用
	 */
	@Column(name = "used")
	private Integer used;

}
