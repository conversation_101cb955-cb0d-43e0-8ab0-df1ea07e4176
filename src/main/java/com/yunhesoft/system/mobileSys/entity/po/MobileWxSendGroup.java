package com.yunhesoft.system.mobileSys.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 接收对象分组
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "MOBILE_WX_SEND_GROUP")
public class MobileWxSendGroup extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 小组名称 */
	@Column(name = "NAME", length = 50)
	private String name;

	/** 描述 */
	@Column(name = "MEMO", length = 255)
	private String memo;

	/** 是否使用 */
	@Column(name = "TMUSED")
	private Integer tmused;
	
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmsort;
    

}
