package com.yunhesoft.system.mobileSys.task.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.config.CustomScheduledConfig;
import com.yunhesoft.core.common.scheduledTask.ScheduledTaskBean;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.mobileSys.service.IWeixinSendService;
import com.yunhesoft.system.mobileSys.task.ITaskWeixinSendService;

import lombok.extern.log4j.Log4j2;

/**
 * 微信定时发送调度任务实现类
 * 
 * <AUTHOR>
 * @since 2022-08-16
 *
 */
@Log4j2
@Service
public class TaskWeixinSendServiceImpl implements ITaskWeixinSendService {

	@Autowired
	private IWeixinSendService wxSendServ;

	// spring Scheduled 自定义
	@Autowired
	private CustomScheduledConfig customScheduled;

	/**
	 * 注册任务
	 */
	@Override
	public String addJob(String taskId, String corn) {
		String info = null;
		ScheduledTaskBean bean = newTask(taskId, corn);
		try {
			if (StringUtils.isNotEmpty(bean.getInfo())) {
				info = bean.getInfo();
			} else {
				customScheduled.addTriggerTask(bean);
			}
		} catch (Exception e) {
			log.error("", e);
			info = e.getMessage();
		}
		return info;
	}

	/**
	 * 判断任务是否运行
	 * 
	 * @param taskId
	 * @return
	 */
	@Override
	public boolean isJobRunning(String taskId) {
		return customScheduled.isTaskRunning(taskId);
	}

	/**
	 * 删除注册
	 * 
	 * @param taskId
	 */
	@Override
	public void removeJob(String taskId) {
		customScheduled.removeTriggerTask(taskId);
	}

	/**
	 * 创建新任务
	 * 
	 * @param taskId
	 * @param corn
	 * @return
	 */
	private ScheduledTaskBean newTask(String taskId, String corn) {
		ScheduledTaskBean e = new ScheduledTaskBean();
		e.setCorn(corn);
		e.setTaskId(taskId);
		try {
			// 定时任务要执行的方法
			Runnable task = new Runnable() {
				@Override
				public void run() {
					try {
						wxSendServ.execSendMsg(taskId);// 发送微信消息
					} catch (Exception e) {
						log.error("消息发送错误", e);
					}

				}
			};
			e.setTask(task);
		} catch (Exception ex) {
			e.setInfo(ex.getMessage());
			log.error("微信发送任务注册失败,Id:" + e.getTaskId() + ",corn:" + e.getCorn(), ex);
		}
		return e;
	}

}
