package com.yunhesoft.system.mobileSys.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.mobileSys.entity.po.MobileMediaFile;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;

/**
 * 媒体文件工具类(手机端)
 * zouhao
 * 2017.08.02
 * <AUTHOR>
 *
 */
public class MediaFileUtil {
	
	public MediaFileUtil(){
	}
	

	/**
	 * 获取媒体文件List数组
	 * @param dataIds 数据ID列表（不加单引号） 例如： ABCDEFG_1 , ABCDEFG_2 , ABCDEFG_3
	 * @return HashMap<String(数据ID),List<String>(图片列表)>
	 */
	public static HashMap<String,List<String>> getMediaWanUrlsToMapList(String dataIds){
		HashMap<String,List<String>> map = new HashMap<String,List<String>>();
		try{
			List<MobileMediaFile> listMediaFile = MediaFileUtil.getMediaFileList(dataIds);
			if(listMediaFile != null && listMediaFile.size()>0){
				for(MobileMediaFile item : listMediaFile){
					String dataid = item.getDataId();
					String wanUrl = MediaFileUtil.getMediaWanUrl(item);
					
					//判断是否添加过此ID
					if(!map.containsKey(dataid)){//未添加过
						map.put(dataid, new ArrayList<String>());
					}
					
					//将地址加入列表
					List<String> list = map.get(dataid);
					list.add(wanUrl);
					
				}
			}
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return map;
	}
	
	/**
	 * 获取媒体文件List数组
	 * @param dataIds 数据ID列表（不加单引号） 例如： ABCDEFG_1 , ABCDEFG_2 , ABCDEFG_3
	 * @return
	 */
	public static List<String> getMediaWanUrlsToList(String dataIds){
		List<String> list = new ArrayList<String>();
		try{
			JSONArray mediaArr =  MediaFileUtil.getMediaWanUrls(dataIds);
			if(mediaArr!=null && mediaArr.size()>0){
				for(Object obj : mediaArr){
					list.add(obj.toString());
				}
			}
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return list;
	}
	
	
	/**
	 * 获取媒体文件JSON数组
	 * @param dataIds 数据ID列表（不加单引号） 例如： ABCDEFG_1 , ABCDEFG_2 , ABCDEFG_3
	 * @return
	 */
	public static JSONArray getMediaWanUrls(String dataIds){
		JSONArray mediaArr = new JSONArray();
		try{
			List<MobileMediaFile> listMediaFile = MediaFileUtil.getMediaFileList(dataIds);
			if(listMediaFile!=null && listMediaFile.size()>0){
				for(MobileMediaFile item : listMediaFile){
					String wanUrl = MediaFileUtil.getMediaWanUrl(item);
					mediaArr.add(wanUrl);
				}
			}
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return mediaArr;
	}
	
	/**
	 * 获取媒体文件广域网相对路径地址
	 * @param mediaFile 媒体文件对象
	 * @return
	 */
	public static String getMediaWanUrl(MobileMediaFile mediaFile){
		String wanUrl = null;//广域网相对路径
		try{
			wanUrl = MediaFileUtil.getMediaWanUrl(mediaFile.getFileRelativePath(), mediaFile.getFileName());
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return wanUrl;
	}
	
	/**
	 * 获取媒体文件广域网相对路径地址
	 * @param fileRelativePath 文件相对路径（不包含系统设置的相对路径/upLoadFiles）（示例：/upLoadFiles[/WeiXin/0505]只传方括号部分）
	 * @param fileName 文件名包括扩展名
	 * @return
	 */
	public static String getMediaWanUrl(String fileRelativePath ,String fileName){
		String wanUrl = null;//广域网相对路径
		try{
			//FileUpHelper fileUpHelper = new FileUpHelper();
			String file_url = SpringUtils.getBean(ISysConfigService.class).getSysConfig("file_url");// 系统参数中获取程序名称
			String path0 = "http://" + WeiXinSDK.sysDomain_Front.trim().replace("http://", "").split("/")[0];//系统域名包含tm3需要进行处理
			String path1 = file_url;//tomcat虚拟目录，相对路径
			String path2 = fileRelativePath==null?"":fileRelativePath;//虚拟目录下的子目录，相对路径
			if(path0 != null && !"".equals(path0) && !path0.endsWith("/")){ path0 = (path0 + "/");}
			if(path1 != null && !"".equals(path1) && !path1.endsWith("/")){ path1 = (path1 + "/");}
			if(path1 != null && !"".equals(path1) && path1.startsWith("/")){ path1 = path1.substring(1);}
			if(path2 != null && !"".equals(path2) && !path2.endsWith("/")){ path2 = (path2 + "/");}
			if(path2 != null && !"".equals(path2) && path2.startsWith("/")){ path2 = path2.substring(1);}
			wanUrl = path0 + path1 + path2 + fileName;
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return wanUrl;
	}
	
	
	/**
	 * 获取媒体文件列表
	 * @param dataIds 数据ID列表（不加单引号） 例如： ABCDEFG_1 , ABCDEFG_2 , ABCDEFG_3
	 * @return
	 */
	public static List<MobileMediaFile> getMediaFileList(String dataIds){
		List<MobileMediaFile> list = null;
		try{
			if(dataIds!=null && !"".equals(dataIds)){
				List<String> listId = new ArrayList<String>();
				String[] arrId = dataIds.split(",");
				for(String id : arrId) {
					listId.add(id);
				}
				list = SpringUtils.getBean(EntityService.class).queryList(MobileMediaFile.class, Where.create().eq(MobileMediaFile::getUsed, 1).in(MobileMediaFile::getDataId, listId.toArray()));
			}
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return list;
	}
	
	
	/**
	 * 批量插入媒体文件数据
	 * 
	 * @param list
	 * @return
	 * @throws Exception
	 */
	public static boolean insertMediaFile(List<MobileMediaFile> list) throws Exception {
		boolean bln = false;
		try {
			if (list != null & list.size() > 0) {
				bln = SpringUtils.getBean(EntityService.class).insertBatch(list) == 1;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return bln;
	}
	/**
	 * 批量插入媒体文件数据
	 * 
	 * @param list
	 * @return
	 * @throws Exception
	 */
	public static boolean saveMediaFile(List<MobileMediaFile> list,String dataid) throws Exception {
		boolean bln = false;
		try {
			bln = SpringUtils.getBean(EntityService.class).deleteByColumn(MobileMediaFile.class, MobileMediaFile::getDataId, dataid)==1;
			if (bln&&list != null & list.size() > 0) {
				bln = SpringUtils.getBean(EntityService.class).insertBatch(list) == 1;
			}
		} catch (Exception e) {
			e.printStackTrace();
		} 
		return bln;
	}
//	/**
//	 * 批量插入媒体文件数据
//	 * 
//	 * @param list
//	 * @return
//	 * @throws Exception
//	 */
//	public static boolean insertFileInfoList(List<FileInfoList> list) throws Exception {
//		boolean bln = false;
//		try {
//			if (list != null & list.size() > 0) {
//				EntityDao<FileInfoList> ed = new EntityDao<FileInfoList>(FileInfoList.class);
//				bln = ed.insert(list);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return bln;
//	}
//	/**
//	 * 获取媒体文件列表
//	 * @param sql文
//	 * @return
//	 */
//	public static List<FileInfoList> getFileInfoList(String sql){
//		List<FileInfoList> list = null;
//		try{
//			if(sql!=null && !"".equals(sql)){
//				EntityDao<FileInfoList> dao = new EntityDao<FileInfoList>(FileInfoList.class);
//				list = dao.findByWhereString(sql);
//			}
//		}catch(Exception ex){
//			ex.printStackTrace();
//		}
//		return list;
//	}
//	/**
//	 * 获取媒体文件列表
//	 * @param sql文
//	 * @return
//	 */
//	public static List<ImageForm> getImageFormList(String sql){
//		List<ImageForm> list = null;
//		try{
//			if(sql!=null && !"".equals(sql)){
//				EntityDao<ImageForm> dao = new EntityDao<ImageForm>(ImageForm.class);
//				list = dao.findByWhereString(sql);
//			}
//		}catch(Exception ex){
//			ex.printStackTrace();
//		}
//		return list;
//	}
//	/**
//	 * 批量插入媒体文件数据
//	 * 
//	 * @param list
//	 * @return
//	 * @throws Exception
//	 */
//	public static boolean insertImageFormList(List<ImageForm> list) throws Exception {
//		boolean bln = false;
//		try {
//			if (list != null & list.size() > 0) {
//				EntityDao<ImageForm> ed = new EntityDao<ImageForm>(ImageForm.class);
//				bln = ed.insert(list);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return bln;
//	}
	
	/**
	 * 批量更新媒体文件数据
	 * 
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public static boolean updateMediaFile(List<MobileMediaFile> list) throws Exception {
		boolean bln = false;
		try {
			if (list != null & list.size() > 0) {
				bln = SpringUtils.getBean(EntityService.class).updateByIdBatch(list)==1;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return bln;
	}

	/**
	 * 批量删除媒体文件数据
	 * 
	 * @param list
	 * @return
	 * @throws Exception
	 */
	public static boolean deleteMediaFile(List<MobileMediaFile> list) throws Exception {
		boolean bln = false;
		try {
			if (list != null & list.size() > 0) {
				bln = SpringUtils.getBean(EntityService.class).deleteByIdBatch(list)==1;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return bln;
	}
//	/**
//	 * 批量删除媒体文件数据
//	 * 
//	 * @param list
//	 * @return
//	 * @throws Exception
//	 */
//	public static boolean deleteFileInfoList(List<FileInfoList> list) throws Exception {
//		boolean bln = false;
//		try {
//			if (list != null & list.size() > 0) {
//				EntityDao<FileInfoList> ed = new EntityDao<FileInfoList>(FileInfoList.class);
//				bln = ed.delete(list);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return bln;
//	}
//	/**
//	 * 批量删除媒体文件数据
//	 * 
//	 * @param list
//	 * @return
//	 * @throws Exception
//	 */
//	public static boolean deleteImageForm(List<ImageForm> list) throws Exception {
//		boolean bln = false;
//		try {
//			if (list != null & list.size() > 0) {
//				EntityDao<ImageForm> ed = new EntityDao<ImageForm>(ImageForm.class);
//				bln = ed.delete(list);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return bln;
//	}
//	/**
//	 * 批量执行sql文
//	 * 
//	 * @param sql
//	 * @return
//	 * @throws Exception
//	 */
//	public static boolean UpdateSql(String sql) {
//		boolean bool=false;
//		EntityDao ed = new EntityDao(null);
//		try {
//			bool=ed.execute(sql);
//		} catch (Exception e) {
//			e.printStackTrace();
//		} 
//		return bool;
//	}
//	/**
//	 * 返回sql查询结果
//	 * @return IDataSource
//	 */
//	public static IDataSource getSqlList(String sql) {
//		IDataSource ids = null;
//		EntityDao ed = new EntityDao(null);
//		try {
//			ids = ed.executeQuery(sql);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return ids;
//
//	}
	
//	
//	/**
//	 * 图片上传
//	 * 
//	 * @category 图片上传
//	 * @param request(HttpServletRequest)
//	 * @return
//	 */
//	public static String upLoadMedia(HttpServletRequest request,SysUser user,String in_JsonData) {
//		/**
//		-- 涉及到调试的数据表
//		select * from FileUpLoadConfig
//		select * from FileUploadLimit
//		
//		select * from FileInfoList order by uptime desc --文件上传流水数据表
//		select * from ImageForm where formId='FormId_MobileSys_Test' --关系表（组ID表，主表）
//		select * from A_MediaFile order by ctime desc --移动端媒体文件数据表
//		select * from FileRelation --没用
//		 */
//		String result = "";
//		try {
//			if (in_JsonData == null || "".equals(in_JsonData)) {
//				System.out.println("【错误信息】【logicsys.mobileSys.MediaFileUtil.upLoadMedia()】：json字符串为空！");
//				return result;
//			}
//			
//			//获取参数工具
//			JsonParamUtil jpu = new JsonParamUtil(in_JsonData);
//			
//			// 上传规则
//			int confId = 0; 
//			try {confId = Integer.parseInt(jpu.getParamInfo("confId"));} catch (Exception e) {}
//
//			// 组ID
//			String formId = jpu.getParamInfo("formId");
//			if (formId == null || formId.length() == 0) {
//				formId = TMUID.getUID();// 第一次上传，生成 表单id
//			}
//			
//			//读取模块编码(用来创建目录)
//			String moduleCode = "other";//默认模块编码
//			String in_moduleCode = jpu.getParamInfo("moduleCode");
//			if(in_moduleCode!=null && !"".equals(in_moduleCode.trim())){
//				moduleCode = in_moduleCode.trim();
//			}
//			
//			//是否同步PC与移动端数据(如果不同步，只上传文件，媒体信息不记录到数据库)
//			String in_isSaveDB = jpu.getParamInfo("isSaveDB");
//			boolean isSaveDB = false;
//			if(in_isSaveDB!=null && ("1".equals(in_isSaveDB.trim()) || "true".equals(in_isSaveDB.toLowerCase().trim()))){
//				isSaveDB = true;//同步媒体信息到数据表
//			}
//			
//			String dirName = "mobileSys";
//			if(user!=null){
//				dirName = user.getId()+"";// 获取人员ID
//				if (dirName == null || dirName.length() == 0) {
//					dirName = "mobileSys";
//				}
//			}
//			FileUpManager fu = new FileUpManager(1, dirName, confId, request);
//			if (fu.getItems() != null) {// 建立成功
//				fu.setUpFileType(2);// 图片
//				fu.setCompressPictures(true);// 压缩图片
//				// 获取压缩参数
//				int compressPicturesSize = 0;// 压缩最大宽高
//				try {compressPicturesSize = Integer.parseInt(jpu.getParamInfo("compressPicturesSize"));} catch (Exception e) {}
//				fu.setCompressPicturesSize(compressPicturesSize);// 压缩大小
//				//是否同步媒体记录到数据库
//				if(!isSaveDB){
//					//不同步数据库
//					fu.setSaveDB(isSaveDB);
//				}
//				FileUpResult fs = fu.upload();
//				if (fs.isResult()) {
//					List<FileInfoList> fileInfoList = fs.getFileInfoList();//获取文件上上传列表
//					if(fileInfoList!=null && fileInfoList.size()>0){
//						MobileUtil util = new MobileUtil();
//						List<AMediaFile> listAMediaFile = null;//返回的图片记录
//						//同步媒体记录到数据库
//						if(isSaveDB){
//							//存储图片应谁关系
//							ImageFormSql dbo = new ImageFormSql();
//							//同步关系表
//							List<ImageFormBean> listImageFormBean = new ArrayList<ImageFormBean>();
//							int sort = 1;
//							for(FileInfoList fileInfo : fileInfoList){
//								ImageFormBean bean = new ImageFormBean();
//								bean.setTmuid(TMUID.getUID());// id
//								bean.setFormId(formId);// 表单id
//								bean.setFileinfolistid(fileInfo.getId());// 文件id
//								bean.setSort(sort++);// 排序
//								//加入列表
//								listImageFormBean.add(bean);
//							}
//							dbo.createImage(listImageFormBean);// 插入数据
//							
//							//同步移动端图片
//							listAMediaFile = util.saveImgAsYd(formId,moduleCode,"local");
//						}else{
//							//不同步数据到数据库，直接生成图片对象
//							listAMediaFile = util.FileInfoListToAMediaFile(fileInfoList, formId, moduleCode, "local");
//						}
//						
//						//生成对象
//						JSONObject mediaObj = MediaFileUtil.getMediaWanUrlsJSONObject(listAMediaFile);
//						//生成返回值
//						if(mediaObj!=null && mediaObj.size() > 0){
//							//遍历对象中所有key值
//							@SuppressWarnings("unchecked")
//							Set<String> keySet = mediaObj.keySet();
//					        for (String key : keySet) {
//					        	jpu.setData(key, mediaObj.get(key));
//					        }
//							jpu.setData("formId", formId);//组ID
//							jpu.success("上传成功！");
//						}else{
//							jpu.err("未找到需要上传的图片！");
//						}
//						
//					}else{
//						jpu.err("未找到需要上传的图片！");
//					}
//				} else {
//					jpu.err(fs.getErrMsg());
//				}
//			} else {
//				FileUpResult errFs = fu.getResult();
//				if (errFs != null) {
//					jpu.err(errFs.getErrMsg());
//				} else {
//					jpu.err("图片上传失败！");
//				}
//			}
//			
//			result = jpu.JsonToString();
//		} catch (Exception e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//		return result;
//	}
//	
	
	/**
	 * 获取媒体文件JSON数组及对象
	 * @param listAMediaFile 媒体文件列表（不加单引号）
	 * @return
	 */
	public static JSONObject getMediaWanUrlsJSONObject(List<MobileMediaFile> listAMediaFile){
		JSONObject mediaObj = new JSONObject();
		try{
			
			//生成返回值数据
			JSONArray mediaListAdd = new JSONArray();
			JSONArray mediaListId = new JSONArray();
			if(listAMediaFile!=null && listAMediaFile.size()>0){
				//循环取出媒体ID
				for(int i=0,iCount=listAMediaFile.size();i<iCount;i++){
					//获取已上传到本地的配置信息
					MobileMediaFile file = listAMediaFile.get(i);
					
					//生成广域网相对路径
					String wanUrl = MediaFileUtil.getMediaWanUrl(file);
					
					mediaListAdd.add(wanUrl);
					mediaListId.add(JSONObject.toJSON(file));
				}
				mediaObj.put("mediaAdds", mediaListAdd);
				mediaObj.put("mediaIds", mediaListId);
			}

		}catch(Exception ex){
			ex.printStackTrace();
		}
		return mediaObj;
	}
	
	
	/**
	 * 获取图片信息
	 * 
	 * @category 获取图片
	 * @return
	 */
	public static String getMediaWanUrlsJSONStr(String in_JsonData) {
		
		String result = "";
		try {
			if (in_JsonData == null || "".equals(in_JsonData)) {
				System.out.println("【错误信息】【logicsys.mobileSys.MediaFileUtil.getMediaWanUrls()】：json字符串为空！");
				return result;
			}
			
			//获取参数工具
			JsonParamUtil jpu = new JsonParamUtil(in_JsonData);
			
			// 组ID
			String formId = jpu.getParamInfo("formId");
			if (formId == null || formId.length() == 0) {
				formId = TMUID.getUID();// 第一次上传，生成 表单id
			}
			//查询媒体文件
			List<MobileMediaFile> listAMediaFile = MediaFileUtil.getMediaFileList(formId);
			//生成对象
			JSONObject mediaObj = MediaFileUtil.getMediaWanUrlsJSONObject(listAMediaFile);
			//生成返回值
			if(mediaObj!=null && mediaObj.size() > 0){
				//遍历对象中所有key值
				@SuppressWarnings("unchecked")
				Set<String> keySet= mediaObj.keySet();
		        for (String key : keySet) {
		        	jpu.setData(key, mediaObj.get(key));
		        }
				jpu.setData("formId", formId);//组ID
				jpu.success("上传成功！");
			}else{
				jpu.err("无图片记录");
			}
			result = jpu.JsonToString();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return result;
	}
	
}
