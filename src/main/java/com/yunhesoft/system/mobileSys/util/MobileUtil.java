package com.yunhesoft.system.mobileSys.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.auth.entity.po.SysLoginUser;
import com.yunhesoft.system.auth.service.AuthService;

/**
 * 移动端公共工具类
 * zouhao
 * 2017.08.09
 */
public class MobileUtil {
	
	public MobileUtil(){
	}
	/**
	 * 自动绑定微信账号(如果微信通信录账号 与 TM系统账号保持一致，则自动绑定)
	 * @param sysUser 微信用户名称
	 * @return 成功返回人员ID
	 */
	public static String bindWeiXin(String sysUser){
		String zyid = null;
		try{
			if(sysUser!=null && !"".equals(sysUser)){
				SysLoginUser loginUser = SpringUtils.getBean(AuthService.class).getLoginUser(sysUser);
	    		if(loginUser!=null){
	    			String tempZyid = loginUser.getId();
	    			if(tempZyid!=null){
	    				SpringUtils.getBean(BindData.class).bind(BindData.SysCode_WeiXin, sysUser, tempZyid);
						zyid = tempZyid;
	    			}
	    		}
			}
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return zyid;
	}
	
	
	
	/**
	 * 将传入的APP地址转换成相对路径 
	 * @param {} inUrl url地址(格式为：app://....)
	 * @param {} param 附加参数(不需要增加 & 或 ? 等关键字)
	 * @return {}
	 */
	public static String getAppUrl(String inUrl, String param){
		String retValue = "";		
		if(inUrl!=null && !"".equals(inUrl.trim())){
			String tempUrl = inUrl = inUrl.trim(); //移动端首页：http://www.lhppi.com:8882/app/index.html#/
			
			//TM4移动端地址示例：http://www.lhppi.com:8882/app/index.html#/pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
			int indexApp = tempUrl.toLowerCase().indexOf("app://");// app://pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
			int indexPage = tempUrl.toLowerCase().indexOf("/pages/".toLowerCase());// /pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
			
			//TM4移动端H5地址示例：http://www.lhppi.com:8882/app/static/yhApp/page/ppiStop/workList.html
			int indexAppstatic = tempUrl.toLowerCase().indexOf("appstatic://"); // appstatic://static/yhApp/page/ppiStop/workList.html
			int indexPagestatic = tempUrl.toLowerCase().indexOf("/static/yhApp/page/".toLowerCase()); // /static/yhApp/page/ppiStop/workList.html
			
			if(indexApp > -1){// app://格式解析
				//传入格式
				//app://pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
				
				//替换后格式
				//./pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
				
				//方法1：
				//tempUrl = inUrl.replace(new RegExp("app://","igm"),"./");//不区分大小写
				
				//后台
				//tempUrl = WeiXinSDK.sysDomain_Front + "/" + tempUrl.replace("app://", "");
				//移动端地址示例：http://www.lhppi.com:8882/app/index.html#/pages/flow/index
				tempUrl = WeiXinSDK.sysDomain_Front + "/app/index.html#/" + IgnoreCaseReplace(tempUrl,"app://", "");//不区分大小写替换 
				
				//方法2：(未完成)
//				var pathname = window.location.pathname+"";
//				var arrpathname = pathname.split("/");
				
			}else if(indexPage > -1){// /pages/ 格式解析(老版本格式)
				//tempUrl = inUrl.replace(new RegExp("/pages/","igm"),"../");//不区分大小写
				
				//判断字符串中是否包含了域名，如果包含域名则不需要处理字符串（主要解决消息推送时，配置外网路径）
				int indexPageFirst = tempUrl.toLowerCase().trim().indexOf(WeiXinSDK.sysDomain_Front.toLowerCase().trim());
				if(indexPageFirst > -1){//地址中包含了域名，无操作
				}else{//未包含域名，增加域名
					tempUrl = WeiXinSDK.sysDomain_Front + "/app/index.html#" + tempUrl;
				}
			}else if(indexAppstatic > -1){// appstatic://格式解析
				//tempUrl = WeiXinSDK.sysDomain_Front + "/" + tempUrl.replace("appstatic://", "");
				//移动端地址示例：http://www.lhppi.com:8882/app/static/yhApp/page/ppiStop/workList.html
				tempUrl = WeiXinSDK.sysDomain_Front + "/app/" + IgnoreCaseReplace(tempUrl,"appstatic://", "");//不区分大小写替换
				
			}else if(indexPagestatic > -1){// /static/yhApp/page/ 格式解析(老版本格式)
				//判断字符串中是否包含了域名，如果包含域名则不需要处理字符串（主要解决消息推送时，配置外网路径）
				int indexPageFirst = tempUrl.toLowerCase().trim().indexOf(WeiXinSDK.sysDomain_Front.toLowerCase().trim());
				if(indexPageFirst > -1){//地址中包含了域名，无操作
				}else{//未包含域名，增加域名
					tempUrl = WeiXinSDK.sysDomain_Front + "/app" + tempUrl;
				}
			}else{//直接写地址， 或相对路径，不解析
				tempUrl = inUrl;
			}
			
			//去掉空格
			if(tempUrl!=null && !"".equals(tempUrl)){
				tempUrl = tempUrl.trim();
			}
			
			//判断如果有传入参数 需要在地址后面追加
			if(param!=null && !"".equals(param)){
				if(tempUrl.indexOf("?") > -1){//地址后面有参数
					retValue = tempUrl+"&"+param;
				}else{//地址后面无参数
					retValue = tempUrl+"?"+param;
				}
			}else{
				retValue = tempUrl;
			}
			
		}
		return retValue.trim();
	}
	
	/** 
	  * java实现不区分大小写替换 
	  * @param source 
	  * @param oldstring 
	  * @param newstring 
	  * @return 
	  */ 
	public static String IgnoreCaseReplace(String source, String oldstring, String newstring){ 
	      Pattern p = Pattern.compile(oldstring, Pattern.CASE_INSENSITIVE); 
	      Matcher m = p.matcher(source); 
	      String ret=m.replaceAll(newstring); 
	      return ret; 
	} 
//	
//	/**
//	 * 移动端保存图片信息
//	 * @param json 移动端传递参数
//	 * @return
//	 */
//	public String saveImgInfo(String json) {
//		String jsons = "";
//		JsonParamUtil jpu = new JsonParamUtil(json);
//
//		if (json == null || "".equals(json)) {
//			System.out.println("【错误信息】【logicsys.mobileSys.UtilLogic.saveImgInfo()】：json字符串为空！");
//			return jsons;
//		}
//
//		try {
//			// 待保存数据-----------------------------
//			String mediaIds=jpu.getData("mediaIds");
//			JSONArray mediaIds1 =JSONArray.parseArray(mediaIds);
//			String dataid=jpu.getParamInfo("dataid");
//			String wheres="select imgid from b_ImgData_Info where ptmuid='"+dataid+"'";
//			IDataSource ids=MediaFileUtil.getSqlList(wheres);
//			if(ids!=null&&ids.getRowCount()>0){
//				dataid=ids.get(0).gets("imgid");
//			}
//			// 开始保存
//			List<AMediaFile> list = new ArrayList<AMediaFile>();
//			for(int i=0,iCount=mediaIds1.size();i<iCount;i++){
//				JSONObject item = mediaIds1.getJSONObject(i);
//				AMediaFile e = item.toJavaObject(AMediaFile.class);
//				e.setDataId(dataid);//增加数据ID
//				list.add(e);
//			}
//			MediaFileUtil.saveMediaFile(list,dataid);
//			saveImgAsPc(list, dataid);//同步pc端图片
//			jpu.success("获取数据成功");
//			jsons = jpu.JsonToString();
//
//		} catch (Exception e) {
//			e.printStackTrace();
//			jpu.err("获取数据失败");
//			jsons = jpu.JsonToString();
//		}
//
//		return jsons;
//	}
////	/**
////	 * 测试代码
////	 * @param args
////	 */
////	public static void main(String args[]){
////		try{
////			//调用自动绑定功能
////			Long zyid = UtilLogic.bindWeiXin("kongfr");
////			System.out.println(zyid);
////			
////		}catch(Exception ex){
////			ex.printStackTrace();
////		}
////	}
//	/**
//	 * 移动端保存图片信息-另存一份给pc端
//	 * @param list 移动端图片信息集合
//	 * @return
//	 */
//	public void saveImgAsPc(List<AMediaFile> list,String pcImgId){
//		List<FileInfoList> Flist = new ArrayList<FileInfoList>();//pc端保存图片表
//		List<ImageForm> ilist = new ArrayList<ImageForm>();//pc端保存图片表
//		try{
//			String where ="where id in (select fileinfolistid from ImageForm where formId='"+pcImgId+"')";
//			List<FileInfoList> listf=MediaFileUtil.getFileInfoList(where);
//			MediaFileUtil.deleteFileInfoList(listf);//删除图片
//			where =" where formId='"+pcImgId+"'";
//			List<ImageForm> listi=MediaFileUtil.getImageFormList(where);
//			MediaFileUtil.deleteImageForm(listi);//删除图片
//			String imgsql="";//pc端图片关联表
//			if(list!=null&&list.size()>0){
//				for(int i=0;i<list.size();i++){//遍历移动端图片信息
//					AMediaFile a=list.get(i);//移动端图片信息
//					//子数据
//					FileInfoList e=new FileInfoList();//pc端图片信息
//					e.setId(TMUID.getUID());
//					e.setName(a.getFileName());
//					e.setFileExt(a.getFileExpandedName().substring(1));
//					e.setAddress("/"+a.getFileRelativePath()+a.getFileName());
//					e.setFileSize(Long.valueOf("0"));
//					e.setUpTime(new Date());
//					e.setUpType(0);
//					Flist.add(e);
//					
//					//主数据
//					ImageForm bean=new ImageForm();
//					bean.setTmuid(TMUID.getUID());
//					bean.setFormId(pcImgId);
//					bean.setFileinfolistid(e.getId());
//					bean.setSort(i);
//					ilist.add(bean);
//					imgsql=imgsql+";insert into b_ImgData_info(tmuid,moduleBm,ptmuid,imgid) "
//							+ " values('"+TMUID.getUID()+"','"+a.getModuleCode()+"','"+a.getDataId()+"','"+pcImgId+"')";
//				}
//				if(MediaFileUtil.insertFileInfoList(Flist)){
//					if(MediaFileUtil.insertImageFormList(ilist)){
//						MediaFileUtil.UpdateSql(imgsql);
//					}
//				}
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		
//	}
//	
//
//	/**
//	 * pc端保存图片信息-另存一份给移动端
//	 * @param formId  pc端存图片的父id（imageForm.formId）
//	 * @return
//	 */
//	public List<AMediaFile> saveImgAsYd(String formId){
//		String module="0000";
//		String type="local";
//		return this.saveImgAsYd(formId, module, type);
//	}
//	
//	/**
//	 * pc端保存图片信息-另存一份给移动端
//	 * @param formId  pc端存图片的父id（imageForm.formId）
//	 * @return
//	 */
//	public List<AMediaFile> saveImgAsYd(String formId ,String moduleCode ,String type){
//		List<AMediaFile> alist = new ArrayList<AMediaFile>();//移动端保存图片表
//		try{
//			List<AMediaFile> lista=MediaFileUtil.getMediaFileList(formId);//获取移动端图片信息
//			MediaFileUtil.deleteMediaFile(lista);//删除图片数据
//			
//			String where ="where id in (select fileinfolistid from ImageForm where formId='"+formId+"')";
//			List<FileInfoList> list=MediaFileUtil.getFileInfoList(where);
//			if(list!=null&&list.size()>0){
//				alist = this.FileInfoListToAMediaFile(list, formId, moduleCode, type);
//				MediaFileUtil.insertMediaFile(alist);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return alist;
//	}
	
//	/**
//	 * FileInfoList 对象转换为 AMediaFile 
//	 * @param listData 文件对象
//	 * @param formId 数据ID
//	 * @param moduleCode 模块编码
//	 * @param type 类型如：WEIXIN,local
//	 * @param savePath 服务器硬盘路径，例如 D:\UpLoadFiles
//	 * @return
//	 */
//	public List<AMediaFile> FileInfoListToAMediaFile(List<FileInfoList> listData,String formId ,String moduleCode ,String type){
//		List<AMediaFile> alist = null;//移动端保存图片表
//		try{
//			if(listData!=null && listData.size()>0){
//				String savePath = FileUpHelper.getAddPath();
//				alist = new ArrayList<AMediaFile>();
//				for(FileInfoList e: listData){
//					alist.add(this.FileInfoListToAMediaFile(e, formId, moduleCode, type, savePath));
//				}
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return alist;
//	}
	
//	/**
//	 * FileInfoList 对象转换为 AMediaFile 
//	 * @param e 文件对象
//	 * @param formId 数据ID
//	 * @param moduleCode 模块编码
//	 * @param type 类型如：WEIXIN,local
//	 * @param savePath 服务器硬盘路径，例如 D:\UpLoadFiles
//	 * @return
//	 */
//	public AMediaFile FileInfoListToAMediaFile(FileInfoList e,String formId ,String moduleCode ,String type,String savePath){
//		AMediaFile a = null;
//		try{
//			if(e!=null){
//				a=new AMediaFile();//移动端图片信息
//				a.setTmuid(TMUID.getUID());
//				a.setModuleCode(moduleCode==null?"":moduleCode);
//				a.setDataId(formId==null?"":formId);
//				a.setFileName(e.getName());
//				a.setFileRelativePath(e.getAddress().replace(e.getName(), ""));
//				a.setFilePath(savePath+a.getFileRelativePath());
//				a.setFileExpandedName("."+e.getFileExt());
//				a.setFileFullPathName(savePath+e.getAddress());
//				a.setSrcUrl(type);//来源：本地
//				a.setType(type);
//				a.setError("");
//				a.setCtime(new Date());
//				a.setUsed(1);
//			}
//		} catch (Exception ex) {
//			ex.printStackTrace();
//		}
//		return a;
//	}
//	
	
}
