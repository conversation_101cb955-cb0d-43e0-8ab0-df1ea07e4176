package com.yunhesoft.system.mobileSys.util;

import com.alibaba.fastjson.JSONObject;

/**
 * 移动端后台通用JSON处理工具类
 * 
 * <AUTHOR>
 */
public class JsonParamUtil {
	private JSONObject jsonObj;

	/**
	 * @category 构造函数
	 * @param json json字符串
	 * @return
	 */
	public JsonParamUtil(String json) {
		if (json != null && !"".equals(json)) {
			this.jsonObj = JSONObject.parseObject(json);// json 对象
		}
	}

	/**
	 * @category 如果有参数，获取data串中 对象值
	 * @param Param data中的key
	 * 
	 * @return data中的值"Param"
	 */
	public String getData(String Param) {
		String value = "";
		try {
			if (this.jsonObj.get("data") != null) {
				if (Param != null && !"".equals(Param)) {
					JSONObject dataObject = this.jsonObj.getJSONObject("data");// 获取data对象
					value = dataObject.getString(Param) == null ? "" : dataObject.getString(Param);// 根据参数 获取data值
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return value;
	}

	/**
	 * @category 如果有参数，获取ParamInfo串中 对象值
	 * @param Param ParamInfo中的KEY
	 * 
	 * @return data中的值"Param"
	 */
	public String getParamInfo(String Param) {
		String value = "";
		try {
			if (this.jsonObj != null && this.jsonObj.containsKey("paramInfo")
					&& this.jsonObj.get("paramInfo") != null) {
				if (Param != null && !"".equals(Param)) {
					JSONObject paramInfo = this.jsonObj.getJSONObject("paramInfo");// 获取data对象
					value = (paramInfo != null && paramInfo.containsKey(Param) && paramInfo.getString(Param) != null)
							? paramInfo.getString(Param)
							: "";// 根据参数 获取data值
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return value;
	}

	/**
	 * @category 获取ParamInfo串中
	 * 
	 * @return 对象 paramInfo 字符串
	 */
	public String getParamInfo() {
		String value = "";
		try {
			value = this.jsonObj.getString("paramInfo") == null ? "" : this.jsonObj.getString("paramInfo");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return value;
	}

	/**
	 * 获取页分页信息
	 * 
	 * @return
	 */
	public String getPageInfo() {
		String value = "";
		try {
			value = this.getPageInfo(null);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return value;
	}

	/**
	 * 获取页分页信息
	 * 
	 * @param Param paramInfo.pageInfo 中的KEY <br>
	 *              paramInfo.pageInfo:{<br>
	 *              "recordCount":84 //总记录数<br>
	 *              ,"pageCount":42 //总页数<br>
	 *              ,"page":1 //当前页<br>
	 *              ,"pageSize":2 //每页显示记录数量<br>
	 *              }
	 * @return
	 */
	public String getPageInfo(String Param) {
		String value = "";
		try {
			value = this.getParamInfo("pageInfo");// pageInfo 分页对象KEY
			if (Param != null && !"".equals(Param) && value != null && !"".equals(value)) {
				JSONObject pageInfoObj = JSONObject.parseObject(value);
				value = (pageInfoObj != null && pageInfoObj.containsKey(Param) && pageInfoObj.getString(Param) != null)
						? pageInfoObj.getString(Param)
						: "";// 根据参数 获取值
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return value;
	}

	/**
	 * 设置页分页信息
	 * 
	 * @param key paramInfo.pageInfo 中的KEY <br>
	 *            paramInfo.pageInfo:{<br>
	 *            "recordCount":84 //总记录数<br>
	 *            ,"pageCount":42 //总页数<br>
	 *            ,"page":1 //当前页<br>
	 *            ,"pageSize":2 //每页显示记录数量<br>
	 *            }
	 * @param obj 对象值
	 * @return
	 */
	public void setPageInfo(String key, Object obj) {
		try {
			String value = this.getPageInfo();
			if (key != null && !"".equals(key)) {
				JSONObject pageInfoObj = null;
				if (value != null && !"".equals(value)) {// 有pageInfo参数
					pageInfoObj = JSONObject.parseObject(value);
					if (pageInfoObj != null) {
						pageInfoObj.put(key, obj);
					}
				} else {// 无pageInfo参数
					pageInfoObj = new JSONObject();
					pageInfoObj.put(key, obj);
				}

				// 修改对象值
				if (pageInfoObj != null) {
					this.jsonObj.getJSONObject("paramInfo").put("pageInfo", pageInfoObj);
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * @category 获取json字符串中的 data串
	 * @return String data字符串
	 */
	public String getData() {
		String data = "";
		try {
			data = this.jsonObj.getString("data") == null ? "" : this.jsonObj.getString("data");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return data;
	}

	/**
	 * @category 给json中添加data，不管存不存在 都重置data
	 */
	public void setData(Object obj) {
		this.jsonObj.put("data", obj);
	}

	/**
	 * @category 给json中的data添加对象
	 * @param key 对象名称
	 * @param obj 对象值
	 */
	public void setData(String key, Object obj) {
		try {
			this.jsonObj.getJSONObject("data").put(key, obj);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * @category 给json中的data添加对象
	 * @param key 对象名称
	 * @param obj 对象值
	 */
	public void setParamInfo(String key, Object obj) {
		try {
			this.jsonObj.getJSONObject("paramInfo").put(key, obj);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 获取json字符串
	 * 
	 */
	public String JsonToString() {
		return this.jsonObj.toString();
	}

	/**
	 * 成功提示
	 * 
	 * @param message 成功提示
	 */
	public void success(String message) {
		this.jsonObj.put("bReturn", "true");
		this.jsonObj.put("sReturn", message);
	}

	/**
	 * 失败提示
	 * 
	 * @param message 成功提示
	 */
	public void err(String message) {
		this.jsonObj.put("bReturn", "false");
		this.jsonObj.put("sReturn", message);
	}

	/**
	 * @category 获取状态（true/false）
	 * @return
	 */
	public String getBReturn() {
		String rst = null;

		if (this.jsonObj.containsKey("bReturn")) {
			rst = this.jsonObj.getString("bReturn");
		}

		return rst;
	}

	/**
	 * @category 获取提示信息
	 * @return
	 */
	public String getSReturn() {
		String rst = null;

		if (this.jsonObj.containsKey("sReturn")) {
			rst = this.jsonObj.getString("sReturn");
		}

		return rst;
	}

	public JSONObject getJsonObj() {
		return this.jsonObj;
	}
}
