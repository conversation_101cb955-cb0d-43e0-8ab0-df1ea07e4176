package com.yunhesoft.system.mobileSys.util;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.Date;
import java.util.List;
import java.util.Map;

//import org.terracotta.agent.repkg.de.schlichtherle.io.FileOutputStream;

import java.util.Set;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.system.mobileSys.entity.po.MobileMediaFile;

import lombok.extern.log4j.Log4j2;

/**
 * HttpRequest 请求类 2017.05.22 zouhao
 */
@Log4j2
public class HttpRequest {

	/**
	 * 向指定URL发送GET方法的请求
	 * 
	 * @param url   发送请求的URL
	 * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
	 * @return URL 所代表远程资源的响应结果
	 */
	public static String sendGet(String url, String param) {
		return HttpRequest.sendGet(url, param, false, null);
	}

	/**
	 * 向指定URL发送GET方法的请求
	 * 
	 * @param url   发送请求的URL
	 * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
	 * @return URL 所代表远程资源的响应结果
	 */
	public static String sendGet(String url, String param, boolean IsUtf8) {
		return HttpRequest.sendGet(url, param, IsUtf8, null);
	}

	/**
	 * 向指定URL发送GET方法的请求
	 * 
	 * @param url    发送请求的URL
	 * @param param  请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
	 * @param IsUtf8 请求返回是否设置为utf-8
	 * @return URL 所代表远程资源的响应结果
	 */
	public static String sendGet(String url, String param, boolean IsUtf8, Map<String, String> header) {
		String result = "";
		BufferedReader in = null;
		try {
			String urlNameString = url + "?" + param;
			URL realUrl = new URL(urlNameString);
			// 打开和URL之间的连接
			URLConnection connection = realUrl.openConnection();
			// 设置通用的请求属性

			connection.setRequestProperty("accept", "*/*");
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");

			// header
			if (header != null && header.size() > 0) {
				Set<String> headerKeys = header.keySet();
				for (String hK : headerKeys) {
					connection.setRequestProperty(hK, header.get(hK));
				}
			}

			// 建立实际的连接
			connection.connect();
			// 获取所有响应头字段
			Map<String, List<String>> map = connection.getHeaderFields();
//            // 遍历所有的响应头字段
//            for (String key : map.keySet()) {
//                System.out.println(key + "--->" + map.get(key));
//            }
			// 定义 BufferedReader输入流来读取URL的响应
			if (IsUtf8) {
				in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
			} else {
				in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
			}
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			System.out.println("发送GET请求出现异常！" + e);
			e.printStackTrace();
		}
		// 使用finally块来关闭输入流
		finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e2) {
				e2.printStackTrace();
			}
		}
		return result;
	}

	/**
	 * 向指定 URL 发送POST方法的请求
	 * 
	 * @param url   发送请求的 URL
	 * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
	 * @return 所代表远程资源的响应结果
	 */
	public static String sendPost(String url, String param) {
		return sendPost(url, param, false);
	}

	/**
	 * 向指定 URL 发送POST方法的请求
	 * 
	 * @param url   发送请求的 URL
	 * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
	 * @return 所代表远程资源的响应结果
	 */
	public static String sendPost(String url, String param, boolean IsUtf8) {
		PrintWriter out = null;
		BufferedReader in = null;
		String result = "";
		try {
			URL realUrl = new URL(url);
			// 打开和URL之间的连接
			URLConnection conn = realUrl.openConnection();
			// 设置通用的请求属性
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			// 发送POST请求必须设置如下两行
			conn.setDoOutput(true);
			conn.setDoInput(true);
			// 获取URLConnection对象对应的输出流
			OutputStreamWriter outWriter = new OutputStreamWriter(conn.getOutputStream(), "utf-8");
			out = new PrintWriter(outWriter);
			// 发送请求参数
			out.print(param);
			// flush输出流的缓冲
			out.flush();
			// 定义BufferedReader输入流来读取URL的响应
			if (IsUtf8) {
				in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
			} else {
				in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			}

			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			System.out.println("发送 POST 请求出现异常！" + e);
			e.printStackTrace();
		}
		// 使用finally块来关闭输出流、输入流
		finally {
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
		log.error("微信发送返回结果="+result);
		return result;
	}

	/**
	 * 
	 * 获取媒体文件流
	 * 
	 * @param url   发送请求的URL
	 * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
	 * @throws Exception
	 */

	public static InputStream getMediaStream(String url, String param) {
		InputStream is = null;
		try {
			String urlNameString = url + "?" + param;
			URL urlGet = new URL(urlNameString);
			HttpURLConnection http = (HttpURLConnection) urlGet.openConnection();
			http.setRequestMethod("GET"); // 必须是get方式请求
			http.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			http.setDoOutput(true);
			http.setDoInput(true);
			System.setProperty("sun.net.client.defaultConnectTimeout", "30000");// 连接超时30秒
			System.setProperty("sun.net.client.defaultReadTimeout", "30000"); // 读取超时30秒
			http.connect();
			// 获取文件转化为byte流
			is = http.getInputStream();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return is;
	}

	/**
	 * 根据内容类型判断文件扩展名
	 * 
	 * @param contentType 内容类型
	 * @return
	 */
	public static String getFileexpandedName(String contentType) {
		String fileEndWitsh = "";
		if ("image/jpeg".equals(contentType))
			fileEndWitsh = ".jpg";
		else if ("audio/mpeg".equals(contentType))
			fileEndWitsh = ".mp3";
		else if ("audio/amr".equals(contentType))
			fileEndWitsh = ".amr";
		else if ("video/mp4".equals(contentType))
			fileEndWitsh = ".mp4";
		else if ("video/mpeg4".equals(contentType))
			fileEndWitsh = ".mp4";
		return fileEndWitsh;
	}

	/**
	 * 获取媒体文件
	 * 
	 * @param url        发送请求的URL
	 * @param param      请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
	 * @param fileName   文件名称（无扩展名）
	 * @param savePath   文件在本地服务器上的存储路径(自动创建目录)
	 * @param type       类型：如，微信(WeiXin)等。(自动创建目录)
	 * @param moduleCode 模块编码：用于生成目录。(自动创建目录)
	 * @return 返回文件绝对路径规则：savePath + "/" + type + "/" + moduleCode + "/" + fileName
	 *         + 扩展名
	 */
	public static MobileMediaFile downloadMedia(String url, String param, String fileName, String savePath, String type,
			String moduleCode) {
		MobileMediaFile retFile = null;
		try {
			String urlNameString = url + "?" + param;
			URL urlGet = new URL(urlNameString);
			HttpURLConnection conn = (HttpURLConnection) urlGet.openConnection();
			conn.setDoInput(true);
			conn.setRequestMethod("GET");

			if (savePath != null && !"".equals(savePath) && !savePath.endsWith("/")) {
				savePath += "/";
			}

			// 根据内容类型获取扩展名
			String fileExt = getFileexpandedName(conn.getHeaderField("Content-Type"));

			// 文件相对路径（不包含系统设置的相对路径/upLoadFiles）（示例：/upLoadFiles[/WeiXin/0505]只存方括号部分）
			String fileRelativePath = "";
			if (type != null && !"".equals(type)) {
				fileRelativePath += (type + "/");
			}
			if (moduleCode != null && !"".equals(moduleCode)) {
				fileRelativePath += (moduleCode + "/");
			}

			// 将mediaId作为文件名
			String filePath = savePath + fileRelativePath + fileName + fileExt;

			BufferedInputStream bis = new BufferedInputStream(conn.getInputStream());
			File file = new File(filePath);

			// 判断目标文件所在的目录是否存在
			if (!file.getParentFile().exists()) {
				// 如果目标文件所在的目录不存在，则创建父目录
				System.out.println("目标文件所在目录不存在，准备创建它！" + file.getParentFile().getPath());
				if (!file.getParentFile().mkdirs()) {
					System.out.println("创建目标文件所在目录失败！" + file.getParentFile().getPath());
					retFile = null;
					return retFile;
				}
			}

			FileOutputStream fos = new FileOutputStream(file);
			byte[] buf = new byte[8096];
			int size = 0;
			while ((size = bis.read(buf)) != -1)
				fos.write(buf, 0, size);
			fos.close();
			bis.close();

			conn.disconnect();

			String info = String.format("[微信]下载媒体文件成功，filePath=" + filePath);
			System.out.println(info);

			// 创建文件对象（每个字段的详细说明见，bean文件字段说明）
			retFile = new MobileMediaFile();
			retFile.setId(TMUID.getUID());
			retFile.setModuleCode(moduleCode == null ? "" : moduleCode);// 模块编码
			retFile.setDataId("");
			retFile.setFileName(fileName + fileExt);// 文件名（每个字段的详细说明见，bean文件字段说明）
			retFile.setFilePath(savePath + fileRelativePath);// 文件存储路径（每个字段的详细说明见，bean文件字段说明）
			retFile.setFileExpandedName(fileExt);// 文件扩展名（每个字段的详细说明见，bean文件字段说明）
			retFile.setFileFullPathName(filePath);// 文件全路径（每个字段的详细说明见，bean文件字段说明）
			retFile.setFileRelativePath(fileRelativePath);// 相对路径（每个字段的详细说明见，bean文件字段说明）
			retFile.setSrcUrl(urlNameString);// 图片来源地址
			retFile.setType(type == null ? "" : type);
			retFile.setError("");
			retFile.setCtime(new Date());
			retFile.setUsed(1);

		} catch (Exception e) {
			retFile = null;
			String error = String.format("[微信]下载媒体文件失败：%s", e);
			System.out.println(error);
		}
		return retFile;
	}

}
