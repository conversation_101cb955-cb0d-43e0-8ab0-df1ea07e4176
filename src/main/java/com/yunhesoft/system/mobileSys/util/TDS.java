package com.yunhesoft.system.mobileSys.util;

import com.yunhesoft.core.common.model.SysUser;


/**
 * 数据源查看手机App端逻辑类
 * 
 * <AUTHOR>
 * @version 2017.06.20
 */
public class TDS {

	private SysUser user = null;

	public TDS(SysUser user) {
		this.user = user;
	}

//	/**
//	 * 获取数据源数据
//	 * 
//	 * @param in_JsonData
//	 *            app发送过来的JSON数据
//	 * @return
//	 */
//	public String getData(String in_JsonData) {
//		String retValue = "";
//		try {
//			if (in_JsonData == null || "".equals(in_JsonData)) {
//				System.out
//						.println("【错误信息】【logicsys.mobileSys.TDS.getData()】：json字符串为空！");
//				return retValue;
//			}
//			JSONObject in_JsonObject = JSONObject.parseObject(in_JsonData);// 解析JSON对象
//			if (in_JsonObject != null) {// JSON对象解析成功
//				if (in_JsonObject.containsKey("paramInfo")) {// 判断是否传入了，输入参数
//					JSONObject in_paramInfo = in_JsonObject
//							.getJSONObject("paramInfo");// 参数
//					if (in_paramInfo.containsKey("dataid")
//							&& !"".equals(in_paramInfo.getString("dataid")
//									.trim())) {// 取静态数据
//						WinXinMessageLogic logic = new WinXinMessageLogic(
//								this.user);
//						String json = "";
//						WeixinSend ws = logic.getMessage(in_paramInfo
//								.getString("dataid")); // 取静态html数据
//						if (ws != null && ws.getHtml() != null) {
//							json = ws.getHtml();
//							try {
//								logic.updateReadCount(ws.getTmuid(),
//										this.user.getId()); // 更新阅读次数
//							} catch (Exception ex) {
//								ex.printStackTrace();
//							}
//						}
//						in_JsonObject.put("data", json); // 将data追加到原json对象中
//						retValue = in_JsonObject.toString(); // json对象转换为字符串
//					} else {// 根据数据源别名和检索条件获取动态数据源数据
//						String tdsAlias = in_paramInfo.getString("tdsAlias");
//						String inParaAlias = "";
//						String bpmModeStr= "";
//						if (in_paramInfo.get("inParaAlias") != null) {
//							inParaAlias = in_paramInfo.getString("inParaAlias");
//						}
//						if (in_paramInfo.get("bpmMode") != null) {
//							bpmModeStr = in_paramInfo.getString("bpmMode");
//						}
//						if (inParaAlias != null && inParaAlias.length() > 0) {
//							inParaAlias = java.net.URLDecoder.decode(
//									inParaAlias, "UTF-8");
//						}
//						String s="";
//						
//						if(bpmModeStr.length() > 0){
//							Integer bpmMode=0;
//							bpmMode=Integer.valueOf(bpmModeStr);
//							s = this.getTdsData(tdsAlias, inParaAlias,bpmMode);
//						}else{
//							s = this.getTdsData(tdsAlias, inParaAlias, "");
//						}
//						
//						//String html = this.getHtml(tdsAlias, inParaAlias);
//						//System.out.println(html);
//						in_JsonObject.put("data", s); // 将data追加到原json对象中
//						retValue = in_JsonObject.toString(); // json对象转换为字符串
//					}
//				}
//			}
//		} catch (Exception ex) {
//			ex.printStackTrace();
//			// 生成返回数据
//			JSONObject in_JsonObject = JSONObject.parseObject(in_JsonData); // 转换json对象
//			in_JsonObject.put("bReturn", "false");
//			in_JsonObject.put("sReturn", "操作失败");
//			retValue = in_JsonObject.toString();// json对象转换为字符串
//		}
//		return retValue;
//	}
	
//	public String getBpmTdsName(String in_JsonData) {
//		String retValue = "";
//		try {
//			if (in_JsonData == null || "".equals(in_JsonData)) {
//				System.out.println("【错误信息】【logicsys.mobileSys.TDS.getData()】：json字符串为空！");
//				return retValue;
//			}
//			JSONObject in_JsonObject = JSONObject.parseObject(in_JsonData);// 解析JSON对象
//			if (in_JsonObject != null) {// JSON对象解析成功
//				if (in_JsonObject.containsKey("paramInfo")) {
//					String dsCodeStr="";
//					String queryStr="";
//					String dataId="";
//					JSONObject in_paramInfo = in_JsonObject.getJSONObject("paramInfo");// 参数
//					if (in_paramInfo.containsKey("dataid")&& !"".equals(in_paramInfo.getString("dataid").trim())) {
//						dataId=in_paramInfo.getString("dataid");
//					}
//					TdsSendBpmLogic logic=new TdsSendBpmLogic(user);
//					Map<String, TdssendBpmInfo> map=logic.getBpmInfoList(dataId);
//					if(map.get("dsCode")!=null){
//						TdssendBpmInfo	dsObj=map.get("dsCode");
//						dsCodeStr=dsObj.getParamValue();
//					}
//					if(map.get("queryStr")!=null){
//						TdssendBpmInfo	dsObj=map.get("queryStr");
//						queryStr=dsObj.getParamValue();
//					}
//					in_JsonObject.put("dsCodeStr", dsCodeStr); // 将data追加到原json对象中
//					in_JsonObject.put("queryStr", queryStr);
//					retValue = in_JsonObject.toString(); // json对象转换为字符串
//				}
//			}
//		} catch (Exception ex) {
//			ex.printStackTrace();
//			// 生成返回数据
//			JSONObject in_JsonObject = JSONObject.parseObject(in_JsonData); // 转换json对象
//			in_JsonObject.put("bReturn", "false");
//			in_JsonObject.put("sReturn", "操作失败");
//			retValue = in_JsonObject.toString();// json对象转换为字符串
//		}
//		return retValue;
//	}
	
	
	
//	/**
//	 * 获取数据源数据
//	 * 
//	 * @param tdsAlias
//	 *            数据源别名
//	 * @param inParaAlias
//	 *            数据源输入条件
//	 * @return
//	 */
//	public String getTdsData(String tdsAlias, String inParaAlias) {
//		return this.getTdsData(tdsAlias, inParaAlias, "");
//	}

//	/**
//	 * 获得数据源数据
//	 * 
//	 * @param tdsAlias
//	 *            数据源别名
//	 * @param inParaAlias
//	 *            条件字符串
//	 * @param dataFilter
//	 *            过滤数据
//	 * @return
//	 */
//	public String getTdsData(String tdsAlias, String inParaAlias,
//			String dataFilter) {
//		return this.getTdsData(tdsAlias, inParaAlias, dataFilter,null, 1, 0);
//	}
//
//	/**
//	 * 获得数据源数据
//	 * 
//	 * @param tdsAlias
//	 *            数据源别名
//	 * @param inParaAlias
//	 *            条件字符串
//	 * @param dataFilter
//	 *            过滤数据
//	 * @return
//	 */
//	public String getTdsData(String tdsAlias, String inParaAlias,
//			Integer bpmMode) {
//		return this.getTdsData(tdsAlias, inParaAlias, null,bpmMode, 1, 0);
//	}
//	
//	/**
//	 * 获得数据源json数据
//	 * 
//	 * @param tdsAlias
//	 *            数据源别名
//	 * @param inParaAlias
//	 *            数据源条件
//	 * @param dataFilter
//	 *            过滤字符串
//	 * @param page
//	 * @param pageSize
//	 * @return
//	 */
//	public String getTdsData(String tdsAlias, String inParaAlias,
//			String dataFilter, int page, int pageSize) {
//		return this.getTdsData(tdsAlias, inParaAlias, null,null, page, pageSize);
//	}
//	/**
//	 * 获得数据源json数据
//	 * 
//	 * @param tdsAlias
//	 *            数据源别名
//	 * @param inParaAlias
//	 *            数据源条件
//	 * @param dataFilter
//	 *            过滤字符串
//	 * @param page
//	 * @param pageSize
//	 * @return
//	 */
//	public String getTdsData(String tdsAlias, String inParaAlias,
//			String dataFilter,Integer bpmMode, int page, int pageSize) {
//		String json = "";
//		try {
//			TDataSourceManager dsm = new TDataSourceManager(this.user);// 数据源管理
//			if(bpmMode!=null){
//				dsm.setBpmMode(bpmMode);
//			}
//			if (dataFilter == null || dataFilter.length() == 0) {
//				TdstableInfo tInfo = dsm.getTDSTableInfo(tdsAlias);// 获取数据源设置中的过滤条件
//				if (tInfo != null && tInfo.getFilterCondition() != null) {
//					dataFilter = tInfo.getFilterCondition();
//				}
//			}
//			if (inParaAlias == null || inParaAlias.length() == 0) {
//				String[] dsAry = tdsAlias.split(",");
//				json = dsm.getData(dsAry, "json", dataFilter, false, page,
//						pageSize);
//			} else {
//				// 根据条件字符串获得数据
//				String queryStr = "";// 检索条件字符串
//				// 解析条件(直接传入的条件值)
//				HashMap<String, String> inParaAliasMap = new HashMap<String, String>();
//				// 传入的条件
//				String[] aryStr = inParaAlias.split("\\|");
//				String dataStr = "";
//				// 生成条件json字符串
//				for (int i = 0; i < aryStr.length; i++) {
//					String[] aryPara = (aryStr[i] + "=a").split("=");
//					inParaAliasMap.put(aryPara[0], aryPara[1]);
//				}
//				if (inParaAliasMap.size() > 0) {
//					StringBuffer dataBuf = new StringBuffer();
//					for (Entry<String, String> temp : inParaAliasMap.entrySet()) {
//						dataBuf.append(",{name:\"" + temp.getKey()
//								+ "\",value:\"" + temp.getValue() + "\"}");
//					}
//					dataStr = dataBuf.substring(1);
//				}
//				if (dataFilter == null)
//					dataFilter = "";
//				queryStr = "[{name:\"" + tdsAlias + "\",filter:\"" + dataFilter
//						+ "\",pageInfo:{page:" + page + ",pageSize:" + pageSize
//						+ "},data:[" + dataStr + "]}]";
//				json = dsm.getData(queryStr, "json", false);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return json;
//	}

//	/**
//	 * 获得前台html代码
//	 * 
//	 * @param tdsAlias
//	 * @param inParaAlias
//	 * @return
//	 */
//	public String getHtml(String tdsAlias, String inParaAlias) {
//		String rtn = "";
//		try {
//			String json = this.getTdsData(tdsAlias, inParaAlias, "");
//			if (json != null && json.length() > 0) {
//				JSONArray ja = JSONArray.parseArray(json);
//				if (ja != null && ja.size() > 0) {
//					StringBuffer html = new StringBuffer();
//					boolean isGroupStart = true;
//					String group1 = "";
//					String group2 = "";
//					StringBuffer content = new StringBuffer(); // 内容列
//					int xh = 0;// 序号
//					JSONObject dataObj = ja.getJSONObject(0);
//					JSONObject cfg = dataObj.getJSONObject("mobileCfg");
//					JSONArray datas = dataObj.getJSONArray("data");
//					String appShowStyle = "";// cfg.getJSONObject("datas").getString("appShowStyle");//手机端显示风格
//					try {
//						appShowStyle = cfg.getJSONObject("datas").getString(
//								"appShowStyle");// 手机端显示风格
//					} catch (Exception e) {
//						appShowStyle = "";
//					}
//					if ("".equals(cfg.getString("group1"))
//							&& !"".equals(cfg.getString("group2"))) {
//						cfg.put("group1", cfg.getString("group2"));
//						cfg.put("group2", "");
//					}
//					for (int i = 0; i < datas.size(); i++) {
//						JSONObject row = datas.getJSONObject(i);
//						if (row == null)
//							continue;
//						if (i == 0) {
//							isGroupStart = true;
//						} else {
//							if (!"".equals(cfg.getString("group1"))
//									&& !group1.equals(row.getString(cfg
//											.getString("group1")))) {
//								isGroupStart = true;
//							} else {
//								if (!"".equals(cfg.getString("group2"))) {
//									String g2 = group1 + group2;
//									String g2_temp = row.getString(cfg
//											.getString("group1"))
//											+ row.getString(cfg
//													.getString("group2"));
//									if (!g2.equals(g2_temp)) {
//										isGroupStart = true;
//									}
//								}
//							}
//						}
//						if (isGroupStart) { // 分组开始
//							xh = 0;
//							if (content.length() > 0) {
//								html.append(content.toString());
//								html.append("</ul></div>");
//								content.delete(0, content.length());// content
//																	// =// [];
//							}
//							// 分组<!--标题-->
//							if ("card".equals(appShowStyle)) {
//								html.append("<div class=\"mui-card\">");
//							}
//							html.append("<div class=\"tab_title\">");
//							if (cfg.getString("group1") != null
//									&& cfg.getString("group1").length() > 0) {
//								String g1 = row.getString(cfg
//										.getString("group1"));
//								if (!"".equals(cfg.getString("group1"))
//										&& g1 != null) {
//									if (!group1.equals(g1)) {
//										html.append("<div class=\"tab_title_up\">"
//												+ g1 + "</div>");
//									}
//								}
//							}
//							if (cfg.getString("group2") != null
//									&& cfg.getString("group2").length() > 0) {
//								String g2 = row.getString(cfg
//										.getString("group2"));
//								if (!"".equals(cfg.getString("group2"))
//										&& g2 != null) {
//									html.append("<div class=\"tab_title_down\">"
//											+ g2 + "</div>");
//								}
//							}
//							html.append("</div>");
//							if ("card".equals(appShowStyle)) {
//								html.append("</div>");
//							}
//							isGroupStart = false;
//						}
//						xh++;
//						if (xh == 1) {
//							content.append("<div class=\"tab_content\">");
//							if ("card".equals(appShowStyle)) {
//							} else {
//								content.append("<ul class=\"mui-table-view mui-table-view-chevron\">");
//							}
//						}
//						if ("card".equals(appShowStyle)) {
//							content.append("<div class=\"mui-card\">");
//						} else {
//							content.append("<li class=\"mui-table-view-cell mui-collapse\">");
//							content.append("<span class=\"mui-badge tab_dis mui-badge-primary\">"
//									+ xh + "</span>");// 序号列
//						}
//						// 显示列=======================================================================================
//						JSONObject items = cfg.getJSONObject("items");
//						if (items != null) {
//							int rownum = 0;
//							for (Object k : items.keySet()) {
//								Object text = row.get(k);
//								if (text != null) {
//									rownum++;
//									String lable = items
//											.getString(k.toString());
//									if ("card".equals(appShowStyle)) {
//										if (rownum == 1) {
//											content.append("<div class=\"tab_content_div_card card_inner\" style=\"font-size: 16px; font-family: 微软雅黑; font-weight: bold; color: #000;background:rgb(234, 234, 234); padding:10px 15px;\">");
//										} else {
//											content.append("<div class=\"tab_content_div_card\">");
//										}
//										content.append(text);
//									} else {
//										content.append("<div class=\"tab_content_div\">");
//										content.append("<span>" + lable
//												+ "：</span>" + text);
//									}
//									content.append("</div>");
//								}
//							}
//						}
//						if ("card".equals(appShowStyle)) {
//							content.append("</div>");
//						} else {
//							content.append("</li>");
//						}
//						if (!"".equals(cfg.getString("group1"))) {
//							group1 = row.getString(cfg.getString("group1"));// row[cfg.group1];
//						}
//						if (!"".equals(cfg.getString("group2"))) {
//							group2 = row.getString(cfg.getString("group2"));
//						}
//						// end for
//						// ================================================================
//					}
//					if (content.length() > 0) {
//						html.append(content.toString());
//						if ("card".equals(appShowStyle)) {
//						} else {
//							html.append("</ul>");
//						}
//						html.append("</div>");
//					}
//					if ("card".equals(appShowStyle)) {
//						rtn = html.toString();
//					} else {
//						rtn = "<div class=\"mui-card\">" + html.toString()
//								+ "</div>";
//					}
//				}
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return rtn;
//	}

}
