package com.yunhesoft.system.mobileSys.util;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.mobileSys.entity.po.MobileUserBind;

import dm.jdbc.util.StringUtil;

/**
 * 用户与外部系统绑定处理
 */
@Service
public class BindData implements Serializable {

	@Autowired
	private EntityService entityService;
	
	/**
	 * Simple to Introduction
	 */
	private static final long serialVersionUID = 1L;
	// 本地RTX实例名称
	public static String SysCode_RTX = "RTX";
	// 本地域控实例名称
	public static String SysCode_Domain = "Domain";
	// 本地微信实例名称
	public static String SysCode_WeiXin = "WeiXin";

	// redis绑定Key
	private final static String MOBILE_KEY = "SYSTEM_MOBILE:BINDDATA";

//	/**
//	 * 获取 本地绑定数据缓存。格式：SysCode_SysUser = zyid
//	 * 
//	 * @return
//	 */
//	public Map<String, String> getBindListIn() {
//		Map<String, String> value = null;
//		// 缓存数据主键
//		String mapKey = "getBindListInMap";
//		String sKey = (new StringBuffer(MOBILE_KEY).append(":").append(mapKey)).toString();
//		value = SpringUtils.getBean(RedisUtil.class).getMap(sKey);
//
//		return value;
//	}

	/**
	 * 获取 本地绑定数据缓存。格式：SysCode_SysUser = zyid
	 * 
	 * @return
	 */
	public String getBindListInValue(String key) {
		String value = null;
		try {
			// 缓存数据主键
			String mapKey = "getBindListInMap";
			String sKey = (new StringBuffer(MOBILE_KEY).append(":").append(mapKey)).toString();
			value = SpringUtils.getBean(RedisUtil.class).getMapValue(sKey, key);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return value;
	}

	/**
	 * 设置 本地绑定数据缓存。格式：SysCode_SysUser = zyid
	 * 
	 * @return
	 */
	public void setBindListInValue(String key, String value) {
		String mapKey = "getBindListInMap";
		if (key != null && !"".equals(key.trim()) && value != null && !"".equals(value.trim())) {
			String sKey = (new StringBuffer(MOBILE_KEY).append(":").append(mapKey)).toString();
			SpringUtils.getBean(RedisUtil.class).setMapValue(sKey, key, value);
		}
	}

	/**
	 * 删除 本地绑定数据缓存。格式：SysCode_SysUser = zyid
	 * 
	 * @return
	 */
	public void removeBindListInValue(String key) {
		String mapKey = "getBindListInMap";
		String sKey = (new StringBuffer(MOBILE_KEY).append(":").append(mapKey)).toString();
		SpringUtils.getBean(RedisUtil.class).hDelete(sKey, key);
	}

//	/**
//	 * 获取 外部绑定数据缓存。格式：SysCode_zyid = SysUser
//	 * 
//	 * @return
//	 */
//	public Map<String, String> getBindListOut() {
//		Map<String, String> value = null;
//		// 缓存数据主键
//		String mapKey = "getbindListOutMap";
//		String sKey = (new StringBuffer(MOBILE_KEY).append(":").append(mapKey)).toString();
//		value = SpringUtils.getBean(RedisUtil.class).getMap(sKey);
//		return value;
//	}

	/**
	 * 获取 外部绑定数据缓存。格式：SysCode_zyid = SysUser
	 * 
	 * @return
	 */
	public String getbindListOutValue(String key) {
		String value = null;
		try {
			String mapKey = "getbindListOutMap";
			String sKey = (new StringBuffer(MOBILE_KEY).append(":").append(mapKey)).toString();
			value = SpringUtils.getBean(RedisUtil.class).getMapValue(sKey, key);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return value;
	}

	/**
	 * 设置 外部绑定数据缓存。格式：SysCode_zyid = SysUser
	 * 
	 * @return
	 */
	public void setbindListOutValue(String key, String value) {
		String mapKey = "getbindListOutMap";
		if (key != null && !"".equals(key.trim()) && value != null && !"".equals(value.trim())) {
			String sKey = (new StringBuffer(MOBILE_KEY).append(":").append(mapKey)).toString();
			SpringUtils.getBean(RedisUtil.class).setMapValue(sKey, key, value);
		}
	}

	/**
	 * 删除 外部绑定数据缓存。格式：SysCode_zyid = SysUser
	 * 
	 * @return
	 */
	public void removebindListOutValue(String key) {
		String mapKey = "getbindListOutMap";
		String sKey = (new StringBuffer(MOBILE_KEY).append(":").append(mapKey)).toString();
		SpringUtils.getBean(RedisUtil.class).hDelete(sKey, key);
	}

	/**
	 * 返回指定RTX账号绑定的本地用户ID
	 * 
	 * @category 返回指定RTX账号绑定的本地用户ID
	 * @param sysUser
	 * @return
	 */
	public String getTmuidByRtx(String sysUser) {
		return this.getTmuid(SysCode_RTX, sysUser);
	}

	/**
	 * 返回指定Domain账号绑定的本地用户ID
	 * 
	 * @category 返回指定Domain账号绑定的本地用户ID
	 * @param sysUser
	 * @return
	 */
	public String getTmuidByDomain(String sysUser) {
		return this.getTmuid(SysCode_Domain, sysUser);
	}

	/**
	 * 返回指定外部账号绑定的本地用户ID
	 * 
	 * @category 返回指定外部账号绑定的本地用户ID
	 * @param sysCode 系统编码
	 * @param sysUser 第三方系统账号
	 * @return
	 */
	public String getTmuid(String sysCode, String sysUser) {
		String key = sysCode + "_" + sysUser;
		// Long uid = bindListIn.get(key); // 先从本地缓存中检索
		String uid = this.getBindListInValue(key); // 先从本地缓存中检索
		if (uid == null || StringUtils.isEmpty(uid)) { // 未从本地缓存中找到数据
			MobileUserBind ub = entityService.queryObject(MobileUserBind.class, Where.create().eq(MobileUserBind::getSysCode, sysCode).eq(MobileUserBind::getSysUser, sysUser));
			if (ub != null) { // 已绑定
				uid = ub.getZyid();
			} else { // 未绑定，暂时以-1存入缓存，避免下次再从数据库读取
				uid = "-1";
			}
			// bindListIn.put(key, uid); // 加入本地缓存
			this.setBindListInValue(key, uid);
			if (!"-1".equals(uid)) {// 不等于空在增加
				// bindListOut.put(sysCode + "_" + uid, sysUser); // 加入本地缓存
				this.setbindListOutValue(sysCode + "_" + uid, sysUser);
			}
		}
		if ("-1".equals(uid)) { // 从本地缓存中找到-1，转换成null值用于逻辑判读
			uid = null;
		}
		return uid;
	}

	/**
	 * 返回该组员绑定的外部RTX账号
	 * 
	 * @category 返回该组员绑定的外部RTX账号
	 * @param tmuid
	 * @return
	 */
	public String getBindRtx(String tmuid) {
		return this.getBind(SysCode_RTX, tmuid);
	}

	/**
	 * 返回该组员绑定的外部Domain账号
	 * 
	 * @category 返回该组员绑定的外部Domain账号
	 * @param tmuid
	 * @return
	 */
	public String getBindDomain(String tmuid) {
		return this.getBind(SysCode_Domain, tmuid);
	}

	/**
	 * 返回该组员绑定的外部账号
	 * 
	 * @category 返回该组员绑定的外部账号
	 * @param sysCode 系统编码
	 * @param tmuid TM4系统人员ID
	 * @return
	 */
	public String getBind(String sysCode, String tmuid) {
		String key = sysCode + "_" + tmuid;
		// String sysUser = bindListOut.get(key); // 先从本地缓存中检索
		String sysUser = this.getbindListOutValue(key);
		if (sysUser == null || "".equals(sysUser)) { // 未从本地缓存中找到数据
			MobileUserBind ub = entityService.queryObject(MobileUserBind.class, Where.create().eq(MobileUserBind::getSysCode, sysCode).eq(MobileUserBind::getZyid, tmuid));
			if (ub != null) { // 已绑定
				sysUser = ub.getSysUser();
			} else { // 未绑定，暂时以空白存入缓存，避免下次再从数据库读取
				sysUser = "";
			}
			// bindListOut.put(key, sysUser);
			this.setbindListOutValue(key, sysUser);
			if (!"".equals(sysUser)) {// 不等于空在增加
				// bindListIn.put(sysCode + "_" + sysUser, tmuid);
				this.setBindListInValue(sysCode + "_" + sysUser, tmuid);
			}
		}
		if ("".equals(sysUser)) { // 从本地缓存中找到空白，转换成null值用于逻辑判读
			sysUser = null;
		}
		return sysUser;
	}

	/**
	 * 存入RTX绑定数据
	 * 
	 * @category 存入RTX绑定数据
	 * @param sysUser
	 * @param tmuid
	 */
	public void bindRtx(String sysUser, String tmuid) {
		this.bind(SysCode_RTX, sysUser, tmuid);
	}

	/**
	 * 存入Domain绑定数据
	 * 
	 * @category 存入Domain绑定数据
	 * @param sysUser
	 * @param tmuid
	 */
	public void bindDomain(String sysUser, String tmuid) {
		this.bind(SysCode_Domain, sysUser, tmuid);
	}

	/**
	 * 存入指定系统的绑定数据
	 * 
	 * @category 存入指定系统的绑定数据
	 * @param sysCode 系统编码：RTX、域账号、微信等
	 * @param sysUser 第三方系统账号
	 * @param tmuid TM4系统人员ID
	 */
	public void bind(String sysCode, String sysUser, String tmuid) {

		// 绑定前先解绑之前绑定的账号
		this.removeBind(sysCode, sysUser, tmuid);

		// 绑定账号
		MobileUserBind ub = new MobileUserBind();
		ub.setId(TMUID.getUID());
		ub.setSysCode(sysCode);
		ub.setSysUser(sysUser);
		ub.setZyid(tmuid);
		if (this.getTmuid(sysCode, sysUser) == null) {
			entityService.insert(ub);
		} else {
			MobileUserBind ubTemp = entityService.queryObject(MobileUserBind.class, Where.create().eq(MobileUserBind::getSysCode, sysCode).eq(MobileUserBind::getSysUser, sysUser));
			if(ubTemp!=null) {
				ub.setId(ubTemp.getId());
				entityService.updateById(ub);
			}
		}
		// 存入本地缓存
		// bindListIn.put(sysCode + "_" + sysUser, tmuid);
		this.setBindListInValue(sysCode + "_" + sysUser, tmuid);
		// bindListOut.put(sysCode + "_" + tmuid, sysUser);
		this.setbindListOutValue(sysCode + "_" + tmuid, sysUser);
	}

	/**
	 * 移除RTX的绑定数据
	 * 
	 * @category 移除RTX的绑定数据
	 * @param tmuid
	 */
	public void removeBindRtx(String tmuid) {
		removeBind(SysCode_RTX, null, tmuid);
	}


	/**
	 * 移除绑定数据
	 * 
	 * @category 移除绑定数据
	 * @param sysCode 外部系统编码
	 * @param sysUser 外部系统用户编码(此参数可以为NULL，如果为空的情况下，不会自动解绑，该外部系统编码，绑定的其它TM系统的zyid（*只针对缓存未解绑，实际数据库中已经解绑，未重新启动服务器的情况下，可能会对之前绑定过的人员，造成视觉上，以为绑定的情况，但实际上使用时，不会有问题）)
	 * @param tmuid   TM系统用户ID
	 */
	public void removeBind(String sysCode, String sysUser, String tmuid) {
		String key = sysCode + "_" + tmuid;
		// String inKey = bindListOut.get(key);
		String inKey = this.getbindListOutValue(key);
		try {
			entityService.rawDeleteByWhere(MobileUserBind.class, Where.create().eq(MobileUserBind::getSysCode, sysCode).eq(MobileUserBind::getZyid, tmuid));
			if (inKey != null) {
				// bindListIn.remove(sysCode + "_" + inKey);
				this.removeBindListInValue(sysCode + "_" + inKey);
				this.removebindListOutValue(key);
			}

//			// 最后移除该[外部系统用户编码]绑定的所有记录（目的解绑，该外部系统ID，绑定的其它TM系统ID）
//			if (sysUser != null && !"".equals(sysUser.trim())) {
//				Map<String, String> mapTemp = this.getBindListOut();
//				if (mapTemp != null) {
//					for (String keyTemp : mapTemp.keySet()) {
//						String v = mapTemp.get(keyTemp);
//						if (v != null && v.equals(sysUser)) {
//							this.removebindListOutValue(keyTemp);// 移除该外部系统ID，绑定的TM系统其它账号（自动解绑功能）
//						}
//					}
//				}
//			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	/**
	 * 移除Domain的绑定数据
	 * 
	 * @category 移除Domain的绑定数据
	 * @param tmuid 组员ID
	 */
	public void removeBindDomainZyid(String tmuid) {
		removeBind(SysCode_Domain, null, tmuid);
	}
	
	/**
	 * 移除Domain的绑定数据
	 * 
	 * @category 移除Domain的绑定数据
	 * @param sysUser 域账号
	 */
	public void removeBindDomainSysUser(String sysUser) {
		String tmuid = null;
		tmuid = this.getTmuidByDomain(sysUser);
		if (tmuid != null && !StringUtil.isEmpty(tmuid)) {
			removeBindDomainZyid(tmuid);
		}
	}
}
