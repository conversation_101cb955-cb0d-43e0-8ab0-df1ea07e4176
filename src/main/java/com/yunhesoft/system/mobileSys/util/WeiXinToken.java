package com.yunhesoft.system.mobileSys.util;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.spring.SpringUtils;

/**
 * @category 系统缓存操作类
 * <AUTHOR>
 * @version 1.0
 */
public class WeiXinToken {
	
	private final static String MOBILE_KEY = "SYSTEM_MOBILE:WEIXIN";

	
	/**
	 * 设置json字符到内存map
	 * 
	 * @param key
	 * @param json
	 * @return
	 */
	public static String setData(String key, String json) {
		try {
			if (key != null && !"".equals(key.trim()) && json!=null && !"".equals(json.trim())) {
				String sKey = (new StringBuffer(MOBILE_KEY).append(":").append(key)).toString();
				SpringUtils.getBean(RedisUtil.class).set(sKey, json, calcTime(json));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return key;
	}

	/**
	 * 通过key值获得json字符串
	 * 
	 * @param key
	 * @return
	 */
	public static String getData(String key) {
		String json = null;
		try {
			if (key != null && !"".equals(key)) {
				String sKey = (new StringBuffer(MOBILE_KEY).append(":").append(key)).toString();
				json = (String)SpringUtils.getBean(RedisUtil.class).get(sKey);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return json;
	}

	/**
	 * 计算微信token超期时间
	 */
	public static long calcTime(String value) {
		long expires_in = 1800;//默认30分钟
		try {
			if (value != null && value.trim().length()>0) {
				// 判断是否获取到了 token
				JSONObject jsonObj = JSONObject.parseObject(value);
				if (jsonObj != null && jsonObj.containsKey("cdatetime") && jsonObj.containsKey("expires_in")) {

//					String cdatetime = jsonObj.getString("cdatetime");// 创建凭证时间
					expires_in = jsonObj.getLong("expires_in");// 凭证的有效时间（秒）

					// 将凭证的有效时间 - 1/4 ：目的保证获取的凭证在使用过程中一直有效
					// 例如：expires_in = 7200秒 （2小时），那就将有效时间改为1.5个小时,预留出半个小时的时间，操作数据
					expires_in = expires_in - (expires_in / 4l);

//					// 判断是否到期
//					long secends = System.currentTimeMillis() - Dates.parseDateTime(cdatetime).getTime();
//					boolean isExpires = secends > (expires_in * 1000) ? true : false;
//
//					if (isExpires) { // 超期
//						mapJson.remove(key);
//					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return expires_in;
	}




}
