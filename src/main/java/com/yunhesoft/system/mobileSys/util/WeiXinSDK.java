package com.yunhesoft.system.mobileSys.util;

import java.text.SimpleDateFormat;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.digest.DigestUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.HtmlUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.mobileSys.entity.po.MobileMediaFile;
import com.yunhesoft.system.mobileSys.entity.po.MobileMsgUrl;
import com.yunhesoft.system.mobileSys.service.IMobileService;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;

import dm.jdbc.util.StringUtil;
import lombok.extern.log4j.Log4j2;

/**
 * 调用微信SDK 2017.05.21 zouhao 微信API：https://work.weixin.qq.com/api/doc
 */
@Log4j2
public class WeiXinSDK {

	/**
	 * 微信企业号ID
	 */
	public static String appid = null;// "ww0ef518287bfe5026";

	/**
	 * 企业应用ID
	 */
	public static String agentid = null;// "1000008";

	/**
	 * 应用的凭证密钥
	 */
	public static String secret = null;// "av7tecB3o-GfrI4kaUhNvLMle0rnhrDsZ-YreRr0u7M";

	/**
	 * 系统后台域名
	 */
	public static String sysDomain = null; // "http://www.mytm3.com:9668/tm3"

	/**
	 * 系统前台域名
	 */
	public static String sysDomain_Front = null; // http://www.xxxx.com:81

	/**
	 * 是否启用微信消息推送
	 */
	public static String useSendMsg = null;

	/**
	 * 启用微信消息推送License文件的绝对路径与文件名称
	 */
	public static String licenseFilePath = null;

	/**
	 * 企业微信登录验证的IP(默认为腾讯企业微信:https://open.weixin.qq.com)
	 */
	public static String WeiXin_loginIp = "https://open.weixin.qq.com";

	/**
	 * 企业微信逻辑调用的IP(默认为腾讯企业微信:https://qyapi.weixin.qq.com)
	 */
	public static String WeiXin_logicIp = "https://qyapi.weixin.qq.com";

	private static String getAgentid(HttpServletRequest request) {
		if (request == null || request.getParameter("yh_agentid") == null) {
			return agentid;
		} else {
			return request.getParameter("yh_agentid");
		}
	}

	private static String getSecret(HttpServletRequest request) {
		if (request == null || request.getParameter("yh_secret") == null) {
			return secret;
		} else {
			return request.getParameter("yh_secret");
		}
	}

	/**
	 * 初始化静态变量
	 */
	static {
		UpdateWeiXinParam();
	}

	/**
	 * 更新微信配置参数
	 */
	public static void UpdateWeiXinParam() {
		try {
			// 读取服务器配置
			ISysConfigService cfgServ = SpringUtils.getBean(ISysConfigService.class);
			String sys_weixin_cfg = cfgServ.getSysConfig("sys_weixin_cfg");
			JSONObject json_sys_weixin_cfg_main = JSONObject.parseObject(sys_weixin_cfg);
			JSONObject json_sys_weixin_cfg = json_sys_weixin_cfg_main.getJSONObject("value");

			appid = json_sys_weixin_cfg.getString("WeiXin_Appid");
			agentid = json_sys_weixin_cfg.getString("WeiXin_Agentid");
			secret = json_sys_weixin_cfg.getString("WeiXin_Secret");
			sysDomain = json_sys_weixin_cfg.getString("WeiXin_Domain");
			sysDomain_Front = json_sys_weixin_cfg.getString("WeiXin_Domain_Front");
			useSendMsg = json_sys_weixin_cfg.getString("WeiXin_UseSendMsg");
			licenseFilePath = json_sys_weixin_cfg.getString("WeiXin_LicenseFilePath");

			// 企业微信登录验证的IP(默认为腾讯企业微信:https://open.weixin.qq.com)
			String WeiXin_loginIpTemp = json_sys_weixin_cfg.getString("WeiXin_loginIp");
			if (WeiXin_loginIpTemp != null && !"".equals(WeiXin_loginIpTemp.trim())) {
				WeiXin_loginIp = WeiXin_loginIpTemp;
			}

			// 企业微信逻辑调用的IP(发消息、上传图片等)(默认为腾讯企业微信:https://qyapi.weixin.qq.com)
			String WeiXin_logicIpTemp = json_sys_weixin_cfg.getString("WeiXin_logicIp");
			if (WeiXin_logicIpTemp != null && !"".equals(WeiXin_logicIpTemp.trim())) {
				WeiXin_logicIp = WeiXin_logicIpTemp;
			}

//			//本地待办_测试,映射到公司IP：***********:8088
//			appid = "ww0ef518287bfe5026";
//			agentid =  "1000011";
//			secret =  "6e-xm4a2pXQyUc9z57ryFqXeDP0AGMR8555E9xbWj-M";
//			sysDomain = "http://weixin.yunhesoft.net:9668/tm4main";
//			useSendMsg = "true";
//			licenseFilePath = "c:\\LicenseWinXin.xml";

			// 开发部_测试 ,映射到公司IP：************:9969 [可用后台映射：9967 -> 8087]
//			appid = "ww0ef518287bfe5026";
//			agentid =  "1000017";
//			secret =  "XLVj9kymXuXKrEW7sbftZ_mHyXNNIJkf23yBNSMP6dA";
//			sysDomain = "http://www.yunhesoft.net:9969/tm4main";
//			sysDomain_Front = "http://www.yunhesoft.net:9969";
//			useSendMsg = "true";
//			licenseFilePath = "c:\\LicenseWinXin.xml";

			// 企业微信打卡签到测试
//			appid = "wwd06078e8e57613cc";
//			agentid =  "3010011";
//			secret =  "M2nbK8VQB5G6boBpvhsyRxJHtYp28cZ3aW74SdKVbE8";

			// 功能列表(全部)
			// http://www.yunhesoft.com:9969/tm4main/system/mobileSys/Login/MLogin?type=wx&page=mobileSys/yhApp/page/toDo/index.html&params=todoType=-1

			// 考勤签到
			// http://www.yunhesoft.com:9969/tm4main/system/mobileSys/Login/MLogin?type=wx&page=mobileSys/yhApp/page/attend/index.html&params=test

		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}

	/**
	 * 微信登录验证(此函数需要调用两次，才能获取人员信息，第一次获取微信的跳转链接，第二次才能真正获取人员信息)
	 * API地址：http://work.weixin.qq.com/api/doc#10028
	 *
	 * @param in_type   验证类型 wx：第一次获取跳转链接 wxLogin:第二次根据code获取人员信息（自己维护的,不可以为NULL）
	 * @param in_page   需要跳转的内部页面（自己维护的,不可以为NULL）
	 * @param in_params 地址栏参数值（自己维护的,不可以为NULL）
	 * @param in_code   微信返回的code可以用来获取人员细信息，第一次进入此函数时，此字段值为空（微信维护的）
	 * @param in_state  微信附加参数值（微信维护的）
	 * @param request   没有可以传null
	 * @param response  没有可以传null
	 * @return
	 */
	public static String wxLogin(String in_type, String in_page, String in_params, String in_code, String in_state,
								 HttpServletRequest request, HttpServletResponse response) {
		String userInfo = null;
		try {
			// 更新微信配置参数
			WeiXinSDK.UpdateWeiXinParam();
			if (in_type != null && in_page != null && in_params != null) {

				// 微信相关参数定义
//				String appid = "ww0ef518287bfe5026";
//				String agentid = "1000008";
//				String Secret = "av7tecB3o-GfrI4kaUhNvLMle0rnhrDsZ-YreRr0u7M";
//				String agentid = "1000006";
//				String Secret = "2tgCuecNICwgtiIZOvoOEe5tTAvq1x_Idz-_oseIM8Q";

				// 第一次获取微信的跳转链接
				if ("wx".equals(in_type)) {
					/**
					 * API说明(第一次：跳转) 1、获取code 如果企业需要在打开的网页里面携带用户的身份信息，第一步需要构造如下的链接来获取code参数：
					 * https://open.weixin.qq.com/connect/oauth2/authorize?appid=ww0ef518287bfe5026&redirect_uri=REDIRECT_URI&response_type=code&scope=SCOPE&agentid=AGENTID&state=STATE#wechat_redirect
					 *
					 * 参数 必须 说明 appid 是 企业的CorpID redirect_uri 是 授权后重定向的回调链接地址，请使用urlencode对链接进行处理
					 * response_type 是 返回类型，此时固定为：code scope 是 应用授权作用域。 snsapi_base：静默授权，可获取成员的基础信息；
					 * snsapi_userinfo：静默授权，可获取成员的详细信息，但不包含手机、邮箱；
					 * snsapi_privateinfo：手动授权，可获取成员的详细信息，包含手机、邮箱。 agentid 否 企业应用的id。
					 * 当scope是snsapi_userinfo或snsapi_privateinfo时，该参数必填。
					 * 注意redirect_uri的域名必须与该应用的可信域名一致。 state 否
					 * 重定向后会带上state参数，企业可以填写a-zA-Z0-9的参数值，长度不可超过128个字节 #wechat_redirect 是
					 * 终端使用此参数判断是否需要带上身份信息
					 *
					 *
					 * 员工点击后，页面将跳转至
					 * redirect_uri?code=CODE&state=STATE，企业可根据code参数获得员工的userid。code长度最大为512字节
					 * 权限说明： 企业无限制；第三方使用snsapi_privateinfo的scope时，应用必须有’成员敏感信息授权’的权限。
					 */

					JSONObject jsonObj = new JSONObject();

					// 拼接微信的回调地址
					// String redirect_uri=sysDomain +
					// "/system/mobileSys/login/MobileLogin?&type=wxLogin&page="+in_page
					StringBuffer redirect_uri = new StringBuffer();
					// redirect_uri.append(sysDomain_Front);
					// redirect_uri.append("/ex-auth.html?");
					redirect_uri.append("accessToken=wx&type=wxLogin&page=");
					redirect_uri.append(in_page);
					redirect_uri.append("&params=");
					redirect_uri.append(in_params);
					if (request.getParameter("yh_agentid") != null) {
						redirect_uri.append("&yh_agentid=");
						redirect_uri.append(request.getParameter("yh_agentid"));
					}
					if (request.getParameter("yh_secret") != null) {
						redirect_uri.append("&yh_secret=");
						redirect_uri.append(request.getParameter("yh_secret"));
					}
					// redirect_uri = java.net.URLEncoder.encode(redirect_uri, "utf-8");

					// 拼接调用微信登录接口的地址
					String response_type = "code";
					String scope = "snsapi_userinfo";
					String state = "0";
					String wechat_redirect = "#wechat_redirect";

					String urlAdd = WeiXinSDK.WeiXin_loginIp + "/connect/oauth2/authorize";
					StringBuffer urlParams1 = new StringBuffer();
					urlParams1.append("appid=");
					urlParams1.append(appid);
					urlParams1.append("&redirect_uri=");

					StringBuffer urlParams2 = new StringBuffer();
					// urlParams2.append(redirect_uri);
					urlParams2.append("&response_type=");
					urlParams2.append(response_type);
					urlParams2.append("&scope=");
					urlParams2.append(scope);
					urlParams2.append("&agentid=");
					urlParams2.append(getAgentid(request));
					urlParams2.append("&state=");
					urlParams2.append(state);
					urlParams2.append(wechat_redirect);

					// 将URL拼接给前台
					jsonObj.put("redirect_uri", redirect_uri.toString());
					jsonObj.put("url_1", urlAdd);
					jsonObj.put("url_2", urlParams1.toString());
					jsonObj.put("url_3", urlParams2.toString());

					userInfo = jsonObj.toString();
				} else if ("wxLogin".equals(in_type)) {// 第二次才能真正获取人员信息
					// 获取微信的人员信息
					String token = getAccessToken(appid, getSecret(request), false);// 获取授权令牌 _包含缓存功能
					log.error("企业微信获取token="+ token);
					if (token != null && !"".equals(token.trim())) {
						log.error("企业微信通过token和code获取用户信息"+ in_code);
						String UserId = getUserInfo_UserId(token, in_code);// 获取微信中的用户信息
						// 使用微信企业号ID 获取 系统中绑定的zyid
						if (UserId != null && !"".equals(UserId)) {

							// 创建UserInfo对象
							JSONObject jsonObj = new JSONObject();
							jsonObj.put("wxname", UserId);// 微信企业通信录中账号

							// 查找绑定到系统的zyid
							String zyid = SpringUtils.getBean(BindData.class).getTmuid(BindData.SysCode_WeiXin, UserId);

							// 如果未找到,进行一次自动绑定（判断当前用户ID是否与我公司绩效系统中人员账号一致）
							if (zyid == null || StringUtil.isEmpty(zyid)) {// 找到绑定ID
								zyid = MobileUtil.bindWeiXin(UserId);
							}

							if (zyid != null && !StringUtil.isEmpty(zyid)) {// 找到绑定ID
								// 获取人员信息
								// HashMap<Long, BZyxx> mapZyxx =
								// SystemOptionTools.getUserName(String.valueOf(zyid));
								// if(mapZyxx!=null && mapZyxx.containsKey(zyid)){
								// BZyxx zyxx = mapZyxx.get(zyid);
								SysUser user = WeiXinSDK.getUser(zyid, null);
								if (user != null) {

									// 加载人员信息
									WeiXinSDK.putUserInfoToJsonObj(jsonObj, user);

									jsonObj.put("errcode", "0");// errcode错误编码(0：无错误 1：有错误)
									jsonObj.put("errmsg", "ok");
								} else {
									jsonObj.put("errcode", "1");// errcode错误编码(0：无错误 1：有错误)
									jsonObj.put("errmsg", "user object init() error! user dimission...");
								}
							} else {// 未找到绑定ID
//								String errmsg ="当前微信账户("+UserId+")未绑定到系统中！请前往系统绑定微信账号！<br>微信账号绑定地址：tm3://manager/rtx/?sysCode=WeiXin&sysName=%E5%BE%AE%E4%BF%A1%E4%BC%81%E4%B8%9A%E8%B4%A6%E5%8F%B7%E7%BB%91%E5%AE%9A";
								// String errmsg = "<font size=20 color=red
								// style=\"font-weight:bold;\">["+UserId+"]</font><font
								// size=18>当前微信帐户没有绑定到系统.</font>";
								String errmsg = "<font size=20 color=red style=\"font-weight:bold;\">[" + UserId
										+ "]</font><font size=18>The current WeChat account not bound to the system.</font>";
								jsonObj.put("errmsg", errmsg);
								jsonObj.put("errcode", "1");// errcode错误编码(0：无错误 1：有错误)
							}

							// 将json字符串返回
							userInfo = jsonObj.toString();
						}
					}
				}
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return userInfo;
	}

	/**
	 * 加载人员信息
	 */
	public static JSONObject putUserInfoToJsonObj(JSONObject jsonObj, SysUser user) {
		JSONObject retJsonObj = jsonObj;
		try {
			if (retJsonObj != null && user != null) {
				retJsonObj.put("zyid", String.valueOf(user.getId()));// 组员ID
				retJsonObj.put("zyxm", user.getNickName());// 组员ID
				retJsonObj.put("zzdm", user.getOrgId());// 装置代码
				retJsonObj.put("zzmc", user.getOrgName());// 装置名称
//				retJsonObj.put("bzdm", user.getMyOrg().getBzdm());
//				retJsonObj.put("bzmc", user.getMyOrg().getBzmc());
				retJsonObj.put("appapi", WeiXinSDK.sysDomain);// API调用地址（固定）
				retJsonObj.put("login_page_url", WeiXinSDK.sysDomain_Front);// 前台调用地址（固定）
				retJsonObj.put("avatar", user.getAvatar());
//				try{ retJsonObj.put("gh", user.getEno()); }catch(Exception ex){}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return retJsonObj;
	}

	/**
	 * 加载人员信息
	 */
	public static String getUserInfoEx(String json) {
		if (json == null || json.equals("")) {
			return "";
		}
		JSONObject jsonObject = JSONObject.parseObject(json);// 外层数据
		try {
			// JSONArray data2=new JSONArray();
			JSONObject retJsonObj = new JSONObject();

			JSONObject paramInfo = jsonObject.getJSONObject("paramInfo");// 参数
			String zyid = paramInfo.getString("zyid");

			String result = "false";
			String msg = "人员ID为空";
			retJsonObj.put("rightIds", ""); // 登录用户的权限列表
			if (zyid != null) {
				SysUser user = WeiXinSDK.getUser(zyid, null);
				retJsonObj.put("roleLevel", user.getRoles()); // 登录用户的角色等级
				retJsonObj.put("USER_ADMIN", SysUserUtil.isAdmin(user.getId())); // 超级管理员的角色等级
				retJsonObj.put("rightIds", user.getPermissions()); // 登录用户的权限列表
				result = "true";
				msg = "";
			}

			// data2.add(retJsonObj);

			jsonObject.put("data", retJsonObj);
			jsonObject.put("bReturn", result);//
			jsonObject.put("sReturn", msg);//
			return jsonObject.toString();
		} catch (Exception e) {
			e.printStackTrace();
			jsonObject.put("bReturn", "false");// 失败
			jsonObject.put("sReturn", "获取数据失败");// 失败
			return jsonObject.toString();
		}
	}

	/**
	 * 获取User对象
	 *
	 * @return
	 */
	public static SysUser getUser(String zyid, String dlmc) {
		SysUser user = null;
		try {
			if ((zyid == null || zyid.length() == 0) && (dlmc == null || dlmc.length() == 0)) {
				return null;
			} else {
				boolean initUser = false;
				user = SysUserUtil.getCurrentUser();
				if (user != null) {
					if (zyid != user.getId()) {
						initUser = true;
					}
				} else {
					initUser = true;
				}
				if (initUser) {
					// System.out.println("weixinGetUser-initUser:"+ zyid+";dlmc:"+dlmc);
					if (zyid != null && zyid.trim().length() > 0) {
						user = SpringUtils.getBean(AuthService.class).getUser(zyid);
					} else if (dlmc != null && !dlmc.equals("")) {
						user = SpringUtils.getBean(AuthService.class).getUserByLoginName(dlmc);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return user;
	}

	/**
	 * 获取授权令牌 _包含缓存功能（access_token） API地址：http://work.weixin.qq.com/api/doc#10013
	 *
	 * @param corpid    企业ID
	 * @param secrect   应用的凭证密钥
	 * @param isRefresh 强制刷新token
	 * @return
	 */
	public static String getAccessToken(String corpid, String secrect, boolean isRefresh) {
		String token = "";
		try {
			/**
			 * API说明
			 *
			 * 术语介绍 corpid：每个企业都拥有唯一的corpid，获取此信息可在管理后台“我的企业”－“企业信息”下查看（需要有管理员权限）
			 * secret：secret是企业应用里面用于保障数据安全的“钥匙”，每一个应用都有一个独立的访问密钥，为了保证数据的安全，secret务必不能泄漏。通讯录接口的密钥在“管理工具”-“通讯录同步助手”里面查看，企业自定义的应用的密钥可以在企业应用的详情里面手动生成
			 * access_token：access_token是企业后台去企业微信的后台获取信息时的重要票据，所有接口在通信时都需要携带此信息用于验证接口的访问权限
			 * 开始
			 *
			 * 第一步：创建企业应用
			 *
			 * 登录企业管理后台进入“企业应用”页面，通讯录管理是企业微信默认集成的应用，可以直接开启，如果企业需要开发自定义的应用，可点击“添加应用”完成应用的添加和配置，详细步骤请参见应用概述。
			 *
			 * 第二步：开启接收消息模式
			 *
			 * 开启接收消息模式并不是必须步骤，但是如果在你的企业应用中需要用到如下功能时需提前开启接收消息模式
			 *
			 * 获取企业成员的地理位置信息 动态调整企业应用的信息 获取企业成员点击事件类型的应用菜单行为 获取企业成员通过应用给企业后台发送的消息
			 * 关于如何开启接收消息模式，请阅读接收消息模式设置章节。
			 *
			 * 第三步：获取access_token
			 *
			 * 请求方式：GET（HTTPS）
			 * 请求URL：https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=id&corpsecret=secrect
			 *
			 * 参数说明：
			 *
			 * 参数 必须 说明 corpid 是 企业ID corpsecret 是 应用的凭证密钥 权限说明：
			 *
			 * 每个应用有独立的secret，所以每个应用的access_token应该分开来获取
			 *
			 * 返回结果：
			 *
			 * { "errcode":0， "errmsg":""， "access_token": "accesstoken000001",
			 * "expires_in": 7200 } 参数 说明 access_token 获取到的凭证。长度为64至512个字节 expires_in
			 * 凭证的有效时间（秒） 出错返回示例：
			 *
			 * { "errcode":40091, "errmsg":"provider_secret is invalid" }
			 * 第四步：对access_token进行缓存处理
			 *
			 * 为了保障服务器的稳定性，企业微信对access_token每天的调用有频次限制，目前为2000次/天/应用，每个access_token的有效期为7200秒（2小时），所以为了防止因为频率调用次数超出限制而影响功能正常使用的问题，建议开发者将中间生成的access_token
			 * 进行缓存，过期以后再重新获取。同时由于企业微信每个应用的access_token是彼此独立的，所以进行缓存时需要区分应用来进行存储。
			 *
			 * 第五步：开发应用逻辑
			 *
			 * 到这里，准备工作已经就绪，你可以继续阅读下面的接口文档了解更多关于企业微信提供的开放能力，并将这些能力与你的应用进行结合，开始吧！
			 */
			JSONObject jsonObj = null;
			String key = corpid + "___" + secrect + "___token";
			String jsonData = WeiXinToken.getData(key);
			if (jsonData != null && !"".equals(jsonData)) {// 判断是否已缓存
				jsonObj = JSONObject.parseObject(jsonData);
			}

			if (isRefresh || jsonObj == null) {// 强制刷新 或 未缓存过，重新获取
				// 判断是否需要重新获取
				String cdatetime = DateTimeUtils.getNowDateTimeStr();
				String urlAdd = WeiXinSDK.WeiXin_logicIp + "/cgi-bin/gettoken";
				StringBuffer urlParams = new StringBuffer();
				urlParams.append("corpid=");
				urlParams.append(corpid);
				urlParams.append("&corpsecret=");
				urlParams.append(secrect);
//				String url = urlAdd+"?"+urlParams.toString();
				jsonData = HttpRequest.sendGet(urlAdd, urlParams.toString());
				jsonObj = JSONObject.parseObject(jsonData);
				// 判断是否获取到了 token
				if (jsonObj != null && jsonObj.containsKey("access_token")
						&& !"".equals(jsonObj.getString("access_token").trim())) {// && jsonObj.containsKey("errcode")
					// &&
					// "0".equals(jsonObj.getString("errcode").trim())

					// 将创建时间写入JSON后加入MAP
					jsonObj.put("cdatetime", cdatetime);
					WeiXinToken.setData(key, jsonObj.toString());
				}
			}

			// 判断是否获取到了 token
			if (jsonObj != null && jsonObj.containsKey("access_token")
					&& !"".equals(jsonObj.getString("access_token").trim())) {// && jsonObj.containsKey("errcode") &&
				// "0".equals(jsonObj.getString("errcode").trim())
				token = jsonObj.getString("access_token");
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return token;
	}

	/**
	 * 获取用户信息
	 *
	 * @param token access_token
	 * @param code  微信返回的code可以用来获取人员细信息，第一次进入此函数时，此字段值为空（微信维护的）
	 * @return
	 */
	public static String getUserInfo(String token, String code) {
		String userInfo = "";
		try {

			/**
			 * API说明： 2、根据code获取成员信息
			 *
			 * 请求方式：GET（HTTPS）
			 * 请求地址：https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=ACCESS_TOKEN&code=CODE
			 *
			 * 参数说明：
			 *
			 * 参数 必须 说明 access_token 是 调用接口凭证 code 是
			 * 通过成员授权获取到的code，最大为512字节。每次成员授权带上的code将不一样，code只能使用一次，5分钟未被使用自动过期。 权限说明：
			 * 跳转的域名须完全匹配access_token对应应用的可信域名。
			 *
			 * 返回结果： a) 当用户为企业成员时返回示例如下：
			 *
			 * { "errcode": 0, "errmsg": "ok", "UserId":"USERID", "DeviceId":"DEVICEID",
			 * "user_ticket": "USER_TICKET"， "expires_in":7200 } 参数 说明 errcode 返回码 errmsg
			 * 对返回码的文本描述内容 UserId 成员UserID DeviceId 手机设备号(由企业微信在安装时随机生成，删除重装会改变，升级不受影响)
			 * user_ticket 成员票据，最大为512字节。
			 * scope为snsapi_userinfo或snsapi_privateinfo，且用户在应用可见范围之内时返回此参数。
			 * 后续利用该参数可以获取用户信息或敏感信息。 expires_in user_token的有效时间（秒），随user_ticket一起返回 b)
			 * 非企业成员授权时返回示例如下：
			 *
			 * { "errcode": 0, "errmsg": "ok", "OpenId":"OPENID", "DeviceId":"DEVICEID" } 参数
			 * 说明 errcode 返回码 errmsg 对返回码的文本描述内容 OpenId 非企业成员的标识，对当前企业唯一 DeviceId
			 * 手机设备号(由企业微信在安装时随机生成，删除重装会改变，升级不受影响) 出错返回示例：
			 *
			 * { "errcode": 40029, "errmsg": "invalid code" }
			 */
			String urlAdd = WeiXinSDK.WeiXin_logicIp + "/cgi-bin/user/getuserinfo";
			StringBuffer urlParams = new StringBuffer();
			urlParams.append("access_token=");
			urlParams.append(token);
			urlParams.append("&code=");
			urlParams.append(code);
//			String url = urlAdd+"?"+urlParams.toString();

			//log.error("企业微信获取用户信息地址:"+ urlAdd+"?"+urlParams.toString());
			userInfo = HttpRequest.sendGet(urlAdd, urlParams.toString());
			//log.error("企业微信获取用户信息结果userInfo=="+ userInfo);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return userInfo;
	}

	/**
	 * 获取用户信息 (UserId)
	 *
	 * @param token access_token
	 * @param code  微信返回的code可以用来获取人员细信息，第一次进入此函数时，此字段值为空（微信维护的）
	 * @return
	 */
	public static String getUserInfo_UserId(String token, String code) {
		String UserId = "";
		try {
			String userInfo = getUserInfo(token, code);
			if (userInfo != null && !"".equals(userInfo)) {
				JSONObject jsonObj = JSONObject.parseObject(userInfo);
				// 判断是否获取到了 token
				if (jsonObj != null && jsonObj.containsKey("UserId")
						&& !"".equals(jsonObj.getString("UserId").trim())) { // && jsonObj.containsKey("errcode") &&
					// "0".equals(jsonObj.getString("errcode").trim())
					UserId = jsonObj.getString("UserId");
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return UserId;
	}

	/**
	 * @category 发送微信消息
	 * @param zyidf         发送人ID
	 * @param zyxmf         发送人名称
	 * @param zyidj         接收人ID
	 * @param zyxmj         接收人名称
	 * @param modulecode    模板编码(必填) 例如：system
	 * @param funcode       功能编码(必填) 例如：tds_query
	 * @param title         标题
	 * @param content       内容
	 * @param jsonDataParam 参数
	 */
	public static void sendMsgWeiXin(String zyidf, String zyxmf, String zyidj, String modulecode, String funcode,
									 String title, String content, String jsonDataParam) {
		try {
			// 判断是否启用了微信消息推送功能
			if (useSendMsg != null && "true".equals(useSendMsg.trim().toLowerCase())) {
				String MessageText = content;// 推送内容

				IMobileService mobileServ = SpringUtils.getBean(IMobileService.class);
				JSONObject jsonDataParamObj = null;
				if (jsonDataParam != null && !"".equals(jsonDataParam.trim())) {
					try {
						// 解析传入的JSON对象,并添加到JSON对象中
						jsonDataParamObj = JSONObject.parseObject(jsonDataParam);

					} catch (Exception ex) {
						ex.printStackTrace();
					}
				}
				String appurl = null;
				String appurlParams = null;
				Integer isBatch = 0;
				MobileMsgUrl mmu = mobileServ.getMobileMsgUrl(modulecode, funcode);
				if (mmu != null) {
					isBatch = mmu.getBatchUse();
					String batchAppURL = mmu.getBatchAppurl();
					// 批量审核并且填写批量审核地址时读取批量审核地址
					if (isBatch != null && isBatch.intValue() == 1 && StringUtil.isNotEmpty(batchAppURL)) {
						appurl = batchAppURL;
						appurlParams = mmu.getBatchAppurlParams();
					} else {
						// 读取普通跳转地址
						appurl = mmu.getAppurl();
						appurlParams = mmu.getAppurlParams();
					}

				}else {
					String modulecodeStr = modulecode;
					if(StringUtils.isEmpty(modulecodeStr)) {
						modulecodeStr = "空";
					}
					String funcodeStr = funcode;
					if(StringUtils.isEmpty(funcodeStr)) {
						funcodeStr = "空";
					}
					String titleStr = title;
					if(StringUtils.isEmpty(titleStr)) {
						titleStr = "空";
					}
					log.info("【错误】发送微信消息，未配置【"+modulecodeStr+"】模块编码，【"+funcodeStr+"】功能编码的消息地址，请到（MOBILE_MSG_URL）表中配置。注意：标题为【"+titleStr+"】的消息，未发送成功。");
				}
				// 生成链接地址
				if (appurl != null && !"".equals(appurl)) {
					// http://www.lhppi.com:8882/app/static/ex-auth.html?accessToken=wx&type=wx&params=a=1&page=
					String[] urlAdd = appurl.split("\\?");
					appurl = (WeiXinSDK.sysDomain_Front + "/app/static/ex-auth.html?accessToken=wx&type=wx&page="
							+ urlAdd[0]);
//					if(urlAdd.length > 1){
//						//参数示例：&params={tmuid=@tmuid,dataParam={}}
//						//数据库中读取的待办附加参数，未替换变量值
//						appurl += ("&params={" + urlAdd[1]);
//						//各模块传入的附加参数：目的是可以替换上面的
//						if(jsonDataParam!=null && !"".equals(jsonDataParam.trim())){
//							appurl += (",jsonDataParam=" + jsonDataParam);
//						}
//						appurl += "}";
//					}
					if (jsonDataParamObj != null) {

						// 此处为发消息，将参数中增加调用类型为发消息
						/**
						 * //消息调用传入参数JSON格式 params:{ //公共部分： callType（调用类型）:0：保留 1：消息 2：其它
						 *
						 * //各功能模块相关自己的参数： tmuid:''//例如：任务调用消息时传入的任务ID dataid://例如：公告传入的数据ID }
						 */

						try {
							// 创建JSON对象
							JSONObject jsonObj = new JSONObject();
							jsonObj.put("callType", "1");// callType（调用类型）:0：保留 1：消息 2：其它
							jsonObj.put("isBatch", isBatch);
							jsonObj.put("modulecode", modulecode);
							jsonObj.put("funcode", funcode);

							// 累加地址参数
							try {
								if (StringUtil.isNotEmpty(appurlParams)) {// 示例：TDSALIAS=pm_clxxcx&queryMode=true
									String[] p = appurlParams.split("&");
									for (String ps : p) {
										String[] pss = ps.split("=");
										if (pss != null) {
											if (pss.length > 1) {
												jsonObj.put(pss[0], pss[1]);
											} else if (pss.length > 0) {
												jsonObj.put(pss[0], "");
											}
										}
									}
								}
							} catch (Exception ex) {
								ex.printStackTrace();
							}

							// 解析传入的JSON对象,并添加到JSON对象中
							for (String key : jsonDataParamObj.keySet()) {
								jsonObj.put(key, jsonDataParamObj.get(key));
							}
							// 转换JSON字符串返回
							jsonDataParam = jsonObj.toString();
						} catch (Exception ex) {
							ex.printStackTrace();
						}

						appurl += ("&params=" + java.net.URLEncoder.encode(jsonDataParam, "utf-8"));
					}

//					MessageText = "<a href=\""+appurl+"\">" + MessageText + "</a>";
				}
				String counttext = "";
				// 消息类型(目前只支持前两种[text],[textcard])
				String msgtype = "textcard";
				MessageText = HtmlUtils.clearHtml(MessageText);
				if (appurl == null || "".equals(appurl.trim())) {// 如果是文本格式，合并字符串
					msgtype = "text";
					Date d = new Date();
					SimpleDateFormat sdf = new SimpleDateFormat("M月dd日 HH时mm分");
					StringBuffer strBuf = new StringBuffer();
					strBuf.append(title);
					strBuf.append("\n");
					strBuf.append("---------------\n");
					strBuf.append(sdf.format(d));
					strBuf.append("(");
					strBuf.append(zyxmf);
					strBuf.append(")\n");
					strBuf.append("[消息内容]：\n");
					strBuf.append(MessageText);
				}
				if ("".equals(counttext)) {
					counttext = MessageText;
				}
				// 将信息推送到微信中
				WeiXinSDK.sendMsg(msgtype, zyidj, counttext, title, appurl, zyxmf);

			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 发送消息 API地址：http://work.weixin.qq.com/api/doc#10167
	 *
	 * @param msgtype 消息类型(目前只支持前两种[text],[textcard]) <br>
	 *                类型列表：[text],[textcard],[image],[voice],[file],[news],[mpnews]
	 * @param zyids   人员ID
	 * @param content 发送内容
	 * @param title   标题
	 * @param skipUrl 跳转地址
	 * @return
	 */
	public static String sendMsg(String msgtype, String zyids, String content, String title, String skipUrl) {
		return sendMsg(msgtype, zyids, content, title, skipUrl, "");
	}

	public static String sendMsg(String msgtype, String zyids, String content, String title, String skipUrl,
								 String fsrName) {
		/**
		 * 接口定义
		 *
		 * 应用支持推送文本、图片、视频、文件、图文等类型。
		 *
		 * 请求方式：POST（HTTPS） 请求地址：
		 * https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=ACCESS_TOKEN
		 *
		 * 参数说明：
		 *
		 * 参数 是否必须 说明 access_token 是 调用接口凭证 各个消息类型的具体POST格式参考以下文档。 返回示例：
		 *
		 * { "errcode" : 0, "errmsg" : "ok", "invaliduser" : "UserID1", //
		 * 不区分大小写，返回的列表都统一转为小写 "invalidparty" : "PartyID1", }
		 * 如果部分接收人无权限或不存在，发送仍然执行，但会返回无效的部分 消息类型
		 *
		 * 文本消息
		 *
		 * 请求示例：
		 *
		 * { "touser" : "UserID1|UserID2|UserID3", "toparty" : " PartyID1|PartyID2 ",
		 * "totag" : " TagID1 | TagID2 ", "msgtype" : "text", "agentid" : 1, "text" : {
		 * "content" : "你的快递已到，请携带工卡前往邮件中心领取。<br>
		 * 出发前可查看<a href=\"http://work.weixin.qq.com\">邮件中心视频实况</a>，聪明避开排队。" } } 参数说明：
		 *
		 * 参数 是否必须 说明 touser 否
		 * 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向该企业应用的全部成员发送 toparty 否
		 * 部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 totag 否
		 * 标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 msgtype 是 消息类型，此时固定为：text
		 * agentid 是 企业应用的id，整型。可在应用的设置页面查看 content 是 消息内容，最长不超过2048个字节 文本消息展现：
		 *
		 *
		 * 特殊说明： 其中text参数的content字段可以支持换行、以及A标签，即可打开自定义的网页（可参考以上示例代码）
		 *
		 * 图片消息
		 *
		 * 请求示例：
		 *
		 * { "touser" : "UserID1|UserID2|UserID3", "toparty" : " PartyID1|PartyID2 ",
		 * "totag" : " TagID1 | TagID2 ", "msgtype" : "image", "agentid" : 1, "image" :
		 * { "media_id" : "MEDIA_ID" } } 请求参数：
		 *
		 * 参数 是否必须 说明 touser 否
		 * 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送 toparty 否
		 * 部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 totag 否
		 * 标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 msgtype 是 消息类型，此时固定为：image
		 * agentid 是 企业应用的id，整型。可在应用的设置页面查看 media_id 是 图片媒体文件id，可以调用上传临时素材接口获取 语音消息
		 *
		 * 请求示例：
		 *
		 * { "touser" : "UserID1|UserID2|UserID3", "toparty" : " PartyID1|PartyID2 ",
		 * "totag" : " TagID1 | TagID2 ", "msgtype" : "voice", "agentid" : 1, "voice" :
		 * { "media_id" : "MEDIA_ID" } } 参数说明：
		 *
		 * 参数 是否必须 说明 touser 否
		 * 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送 toparty 否
		 * 部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 totag 否
		 * 标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 msgtype 是 消息类型，此时固定为：voice
		 * agentid 是 企业应用的id，整型。可在应用的设置页面查看 media_id 是 语音文件id，可以调用上传临时素材接口获取 视频消息
		 *
		 * 请求示例：
		 *
		 * { "touser" : "UserID1|UserID2|UserID3", "toparty" : " PartyID1|PartyID2 ",
		 * "totag" : " TagID1 | TagID2 ", "msgtype" : "video", "agentid" : 1, "video" :
		 * { "media_id" : "MEDIA_ID", "title" : "Title", "description" : "Description" }
		 * } 参数说明：
		 *
		 * 参数 是否必须 说明 touser 否
		 * 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送 toparty 否
		 * 部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 totag 否
		 * 标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 msgtype 是 消息类型，此时固定为：video
		 * agentid 是 企业应用的id，整型。可在应用的设置页面查看 media_id 是 视频媒体文件id，可以调用上传临时素材接口获取 title 否
		 * 视频消息的标题，不超过128个字节，超过会自动截断 description 否 视频消息的描述，不超过512个字节，超过会自动截断 文件消息
		 *
		 * 请求示例：
		 *
		 * { "touser" : "UserID1|UserID2|UserID3", "toparty" : " PartyID1|PartyID2 ",
		 * "totag" : " TagID1 | TagID2 ", "msgtype" : "file", "agentid" : 1, "file" : {
		 * "media_id" : "1Yv-zXfHjSjU-7LH-GwtYqDGS-zz6w22KmWAT5COgP7o" } } 参数说明：
		 *
		 * 参数 是否必须 说明 touser 否
		 * 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送 toparty 否
		 * 部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 totag 否
		 * 标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 msgtype 是 消息类型，此时固定为：file
		 * agentid 是 企业应用的id，整型。可在应用的设置页面查看 media_id 是 文件id，可以调用上传临时素材接口获取 文件消息展现：
		 *
		 *
		 * 文本卡片消息
		 *
		 * 请求示例：
		 *
		 * { "touser" : "UserID1|UserID2|UserID3", "toparty" : " PartyID1 | PartyID2 ",
		 * "totag" : " TagID1 | TagID2 ", "msgtype" : "textcard", "agentid" : 1,
		 * "textcard" : { "title" : "领奖通知", "description" : "<div
		 * class=\"gray\">2016年9月26日</div> <div class=\"normal\">恭喜你抽中iPhone
		 * 7一台，领奖码：xxxx</div><div class=\"highlight\">请于2016年10月10日前联系行政同事领取</div>",
		 * "url" : "URL" } } 参数说明：
		 *
		 * 参数 是否必须 说明 touser 否
		 * 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送 toparty 否
		 * 部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 totag 否
		 * 标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 msgtype 是 消息类型，此时固定为：textcard
		 * agentid 是 企业应用的id，整型。可在应用的设置页面查看 title 是 标题，不超过128个字节，超过会自动截断 description 是
		 * 描述，不超过512个字节，超过会自动截断 url 是 点击后跳转的链接。 btntxt 否 按钮文字。 默认为“详情”， 不超过4个文字，超过自动截断。
		 * 文本卡片消息展现 ：
		 *
		 *
		 * 特殊说明：
		 * 卡片消息的展现形式非常灵活，支持使用br标签或者空格来进行换行处理，也支持使用div标签来使用不同的字体颜色，目前内置了3种文字颜色：灰色(gray)、高亮(highlight)、默认黑色(normal)，将其作为div标签的class属性即可，具体用法请参考上面的示例。
		 *
		 * 图文消息
		 *
		 * 请求示例：
		 *
		 * { "touser" : "UserID1|UserID2|UserID3", "toparty" : " PartyID1 | PartyID2 ",
		 * "totag" : " TagID1 | TagID2 ", "msgtype" : "news", "agentid" : 1, "news" : {
		 * "articles" : [ { "title" : "中秋节礼品领取", "description" : "今年中秋节公司有豪礼相送", "url" :
		 * "URL", "picurl" :
		 * "http://res.mail.qq.com/node/ww/wwopenmng/images/independent/doc/test_pic_msg1.png"
		 * } ] } } 参数说明：
		 *
		 * 参数 是否必须 说明 touser 否
		 * 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送 toparty 否
		 * 部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 totag 否
		 * 标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 msgtype 是 消息类型，此时固定为：news
		 * agentid 是 企业应用的id，整型。可在应用的设置页面查看 articles 是 图文消息，一个图文消息支持1到8条图文 title 是
		 * 标题，不超过128个字节，超过会自动截断 description 否 描述，不超过512个字节，超过会自动截断 url 是 点击后跳转的链接。
		 * picurl 是 图文消息的图片链接，支持JPG、PNG格式，较好的效果为大图640320，小图8080。 btntxt 否
		 * 按钮文字，仅在图文数为1条时才生效。 默认为“阅读全文”， 不超过4个文字，超过自动截断。 图文消息展现：
		 *
		 *
		 * 图文消息（mpnews）
		 *
		 * mpnews类型的图文消息，跟普通的图文消息在客户端表现上是一致的。 mpnews可以通过管理后台的“管理工具” -
		 * “消息群发助手”来发送，也可以通过以下描述的API接口，通过企业自己的平台进行发送。 请求示例：
		 *
		 * { "touser" : "UserID1|UserID2|UserID3", "toparty" : " PartyID1 | PartyID2 ",
		 * "totag": " TagID1 | TagID2 ", "msgtype" : "mpnews", "agentid" : 1, "mpnews" :
		 * { "articles":[ { "title": "Title", "thumb_media_id": "MEDIA_ID", "author":
		 * "Author", "content_source_url": "URL", "content": "Content", "digest":
		 * "Digest description" } ] } } 参数说明：
		 *
		 * 参数 是否必须 说明 touser 否
		 * 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送 toparty 否
		 * 部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 totag 否
		 * 标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数 msgtype 是 消息类型，此时固定为：news
		 * agentid 是 企业应用的id，整型。可在应用的设置页面查看 articles 是 图文消息，一个图文消息支持1到8条图文 title 是
		 * 标题，不超过128个字节，超过会自动截断 thumb_media_id 是 图文消息缩略图的media_id,
		 * 可以在上传多媒体文件接口中获得。此处thumb_media_id即上传接口返回的media_id author 否 图文消息的作者，不超过64个字节
		 * content_source_url 否 图文消息点击“阅读原文”之后的页面链接 content 是 图文消息的内容，支持html标签，不超过666
		 * K个字节 digest 否 图文消息的描述，不超过512个字节，超过会自动截断
		 */

		String info = null;
		// 判断是否启用了微信消息推送功能
		if (useSendMsg != null && "true".equals(useSendMsg.trim().toLowerCase())// 判断系统参数，是否开启了微信发消息
				&& getLicenseWinXin_IsWinXinMsg() // 获取本地许可文件的值(IsWinXinMsg:是否发送微信消息 true：发消息 false:不发消息)
		) {
			try {
				if (zyids != null && !"".equals(zyids.trim())) {
					if (skipUrl == null)
						skipUrl = "";
					String userids = "";// 微信账号列表

					// 查找绑定到系统的微信账号ID
					String[] zyidArr = zyids.split(",");
					for (int i = 0, iCount = zyidArr.length; i < iCount; i++) {
						// 判断每一个人员是否绑定微信账号
						String zyid = zyidArr[i];
						String userid_wx = "";// 微信绑定的账号
						try {
							userid_wx = SpringUtils.getBean(BindData.class).getBind(BindData.SysCode_WeiXin, zyid);
						} catch (Exception ex) {
							ex.printStackTrace();
						}
						// 判断是否查找到对应的微信账号
						if (userid_wx != null && !"".equals(userid_wx.trim())) {
							userids += "|";
							userids += userid_wx;
						}
					}

					// 判断是否对照出了有效人员
					if (userids != null && !"".equals(userids.trim())) {
						userids = userids.substring(1);// 删除一个 | 竖线
						String token = getAccessToken(appid, getSecret(null), false);// 获取授权令牌 _包含缓存功能
						if (token != null && !"".equals(token.trim())) {
							// 生成链接地址
							String urlAdd = WeiXinSDK.WeiXin_logicIp + "/cgi-bin/message/send";
							StringBuffer urlParams = new StringBuffer();
							urlParams.append("access_token=");
							urlParams.append(token);
							String url = urlAdd + "?" + urlParams.toString();

							// 生成数据包

							if (msgtype == null)
								msgtype = "textcard";// 默认为，文本卡片消息

							// 消息对象
							JSONObject msgData = new JSONObject();

							// 判断消息类型
							if ("text".equals(msgtype)) {// 文本消息("text")
								JSONObject jsonContent = new JSONObject();
								jsonContent.put("content", content);
								msgData.put("touser", userids);// 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向该企业应用的全部成员发送
								msgData.put("toparty", "");// 部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数
								msgData.put("totag", "");// 标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数
								msgData.put("msgtype", "text");// 消息类型，此时固定为：text
								msgData.put("agentid", getAgentid(null));// 企业应用的id，整型。可在应用的设置页面查看
								msgData.put("text", jsonContent);// 消息内容，最长不超过2048个字节
							} else {
								// 默认为：文本卡片消息("textcard")

//			    				特殊说明：
//			    				卡片消息的展现形式非常灵活，支持使用br标签或者空格来进行换行处理，也支持使用div标签来使用不同的字体颜色，目前内置了3种文字颜色：灰色(gray)、高亮(highlight)、默认黑色(normal)，将其作为div标签的class属性即可，具体用法请参考上面的示例。

								// 消息标题增加系统时间
								if (content == null)
									content = "";
								// content += "<font size=\"6\"
								// color=\"#FF0000\">["+Dates.getNowDateStr()+"]</font>";
								String fsrstr = "";
								if (fsrName != null && !"".equals(fsrName.trim())) {
									fsrstr = "(" + fsrName + ")";
								}
								content = (("<div class=\"gray\">"
										+ DateTimeUtils.format(DateTimeUtils.getNowDate(), "yyyy年MM月dd日HH时mm分") + fsrstr
										+ "</div><br>") + content);

								JSONObject jsonContent = new JSONObject();
								jsonContent.put("title", title);// 标题，不超过128个字节，超过会自动截断
								jsonContent.put("description", content);// 描述，不超过512个字节，超过会自动截断
								jsonContent.put("url", skipUrl);// 点击后跳转的链接。
								jsonContent.put("btntxt", "详情");// 按钮文字。 默认为“详情”， 不超过4个文字，超过自动截断。

								msgData.put("touser", userids);// 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向该企业应用的全部成员发送
								msgData.put("toparty", "");// 部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数
								msgData.put("totag", "");// 标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数
								msgData.put("msgtype", "textcard");// 消息类型，此时固定为：text
								msgData.put("agentid", getAgentid(null));// 企业应用的id，整型。可在应用的设置页面查看
								msgData.put("textcard", jsonContent);// 消息内容，最长不超过2048个字节
							}

							// POST方式请求数据
							info = HttpRequest.sendPost(url, msgData.toString());
						}
					}

				}
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}
		return info;
	}

	/**
	 * 发送消息
	 *
	 * @param 例子1           :4148，管理员，4149，管东红，0505，dsh，审核，XX任务待审核 ----（待审核，发送给审核人）
	 * @param 例子2           :4148，管理员，4149，管东红，0505，dsp，审批，XX任务待审批 ----（待审批，发送给审批人）
	 * @param 例子3           :4148，管理员，4149，管东红，0505，dfk，反馈，XX任务待反馈 ----（待反馈，发送给反馈人）
	 * @param 例子4           :4148，管理员，4149，管东红，0505，dqr，确认，XX任务待确认 ----（待确认，发送给确认人）
	 *
	 * @param zyidf         发送人id
	 * @param zyxmf         发送人姓名
	 * @param zyidj         接收人id（多人逗号分割）
	 * @param zyxmj         接收人姓名（多人逗号分割）
	 * @param module        模块编码 （例如 0505）
	 * @param modelebm      模块别名（例如
	 *                      dsh(督办-待审核),dsp(督办-待审批),dfk(督办-待反馈),dqr(督办-待确认),bpm_dsh(工作流-待审核)）
	 * @param title         标题（例如 审核）
	 * @param content       内容（例如 XXX任务待审核）
	 * @param jsonDataParam JSON形式 参数：例如任务功能 {"tmuid":"AAABBBCCC","state":"1"}
	 *
	 * @return
	 */
//	public static void sendMsg(String zyidf, String zyxmf, String zyidj, String zyxmj, String module, String modelebm,
//			String title, String content, String jsonDataParam) {
//		try {
//			// 发送微信消息
//			sendMsgWeiXin(zyidf, zyxmf, zyidj, zyxmj, module, modelebm, title, content, jsonDataParam);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}

//		try {
//			// 发送GCP消息（效能系统）
//			String err = GCPSDK.sendMsgGCP(zyidf, zyxmf, zyidj, zyxmj, module, modelebm, title, content, jsonDataParam);
//			if (err != null && !"".equals(err)) {
//				System.out.println("\n【错误信息】【发送GCP消息】【" + DateTimeUtils.getNowDateTimeStr() + "】：" + err);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//
//		try {
//			// 发送TM4APP消息
//			TM4APPSDK tm4AppSdk = new TM4APPSDK();
//			String err = tm4AppSdk.sendMsgTM4APP(zyidf, zyxmf, zyidj, zyxmj, module, modelebm, title, content,
//					jsonDataParam, null);
//			if (err != null && !"".equals(err)) {
//				System.out.println("\n【错误信息】【发送TM4APP消息】【" + DateTimeUtils.getNowDateTimeStr() + "】：" + err);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//
//		try {
//			// 发送中油系统消息
//			String err = PPISDK.sendMsgPPI(zyidf, zyxmf, zyidj, zyxmj, module, modelebm, title, content, jsonDataParam);
//			if (err != null && !"".equals(err)) {
//				System.out.println("\n【错误信息】【发送中油APP消息】【" + DateTimeUtils.getNowDateTimeStr() + "】：" + err);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}

//	/**
//	 * @category 发送微信消息
//	 * @param zyidf
//	 * @param zyxmf
//	 * @param zyidj
//	 * @param zyxmj
//	 * @param module
//	 * @param modelebm
//	 * @param title
//	 * @param content
//	 * @param jsonDataParam
//	 */
//	public static void sendMsgWeiXin(String zyidf, String zyxmf, String zyidj, String zyxmj, String module,
//			String modelebm, String title, String content, String jsonDataParam) {
//		try {
//			// 判断是否启用了微信消息推送功能
//			if (useSendMsg != null && "true".equals(useSendMsg.trim().toLowerCase())) {
//				String MessageText = content;// 推送内容
//				TodoLogic todoLogic = new TodoLogic();
//				String where = "";// 查询待办表的 条件语句
////				String where="where moduleCode='0505' and todoCode='dsh'";
//				where = "where todoCode='" + modelebm + "'";
////				if(modelebm.equals("dsh")){//（待审核，发送给审核人）
////					MessageText="您有一条待审核记录";
////				}else if(modelebm.equals("dsp")){//（待审批，发送给审批人）
////					MessageText="您有一条待审批记录";
////				}else if(modelebm.equals("dfk")){//（待反馈，发送给反馈人）
////					MessageText="您有一条待反馈记录";
////				}else if(modelebm.equals("dqr")){//（待确认，发送给确认人）
////					MessageText="您有一条待确认记录";
////				}
//				JSONObject jsonDataParamObj = null;
//				if (jsonDataParam != null && !"".equals(jsonDataParam.trim())) {
//					try {
//						// 解析传入的JSON对象,并添加到JSON对象中
//						jsonDataParamObj = JSONObject.parseObject(jsonDataParam);
//
//					} catch (Exception ex) {
//						ex.printStackTrace();
//					}
//				}
//				String appurl = null;
//				Integer isBatch = 0;
//				List<ATodoInfo> list = todoLogic.getATodoInfoList(where);
//				if (list != null && list.size() > 0) {
//					ATodoInfo a = list.get(0);
//					isBatch = a.getIsBatch();
//					String batchAppURL = a.getBatchAppURL();
//					// 批量审核并且填写批量审核地址时读取批量审核地址
//					if (isBatch != null && isBatch.intValue() == 1 && batchAppURL != null
//							&& batchAppURL.trim().length() > 0) {
//						appurl = batchAppURL;
//					} else {
//						// 读取普通跳转地址
//						appurl = a.getAppURL();// url
//					}
//
//				}
//				// 生成链接地址
//				if (appurl != null && !"".equals(appurl)) {
//					// http://www.mytm3.com:9668/tm3/MLogin?type=wx&page=mobileSys/appTodo.jsp&params=test
//					String[] urlAdd = appurl.split("\\?");
//					appurl = (WeiXinSDK.sysDomain + "/MLogin?type=wx&page=" + urlAdd[0]);
////					if(urlAdd.length > 1){
////						//参数示例：&params={tmuid=@tmuid,dataParam={}}
////						//数据库中读取的待办附加参数，未替换变量值
////						appurl += ("&params={" + urlAdd[1]);
////						//各模块传入的附加参数：目的是可以替换上面的
////						if(jsonDataParam!=null && !"".equals(jsonDataParam.trim())){
////							appurl += (",jsonDataParam=" + jsonDataParam);
////						}
////						appurl += "}";
////					}
//					if (jsonDataParamObj != null) {
//
//						// 此处为发消息，将参数中增加调用类型为发消息
//						/**
//						 * //消息调用传入参数JSON格式 params:{ //公共部分： callType（调用类型）:0：保留 1：消息 2：其它
//						 *
//						 * //各功能模块相关自己的参数： tmuid:''//例如：任务调用消息时传入的任务ID dataid://例如：公告传入的数据ID }
//						 */
//
//						try {
//							// 创建JSON对象
//							JSONObject jsonObj = new JSONObject();
//							jsonObj.put("callType", "1");// callType（调用类型）:0：保留 1：消息 2：其它
//							jsonObj.put("isBatch", isBatch);
//							jsonObj.put("todoCode", modelebm);
//							// 解析传入的JSON对象,并添加到JSON对象中
//							for (Object key : jsonDataParamObj.keySet()) {
//								jsonObj.put(key, jsonDataParamObj.get(key));
//							}
//							// 转换JSON字符串返回
//							jsonDataParam = jsonObj.toString();
//						} catch (Exception ex) {
//							ex.printStackTrace();
//						}
//
//						appurl += ("&params=" + java.net.URLEncoder.encode(jsonDataParam, "utf-8"));
//					}
//
////					MessageText = "<a href=\""+appurl+"\">" + MessageText + "</a>";
//				}
//				String counttext = "";
//				// 消息类型(目前只支持前两种[text],[textcard])
//				String msgtype = "textcard";
//				MessageText = Htmls.delHtml(MessageText, 10000);
//				if (appurl == null || "".equals(appurl.trim())) {// 如果是文本格式，合并字符串
//					msgtype = "text";
//					Date d = new Date();
//					SimpleDateFormat sdf = new SimpleDateFormat("M月dd日 HH时mm分");
//					counttext = title + "\n" + "---------------\n"
//					// + zyxmf+"\n"
//							+ sdf.format(d) + "(" + zyxmf + ")\n" + "[消息内容]：\n" + MessageText;
//				}
//				if ("".equals(counttext)) {
//					counttext = MessageText;
//				}
//				// 将信息推送到微信中
//				WeiXinSDK.sendMsg(msgtype, zyidj, counttext, title, appurl, zyxmf);
//
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}

	// ----------------------------------------------------JS-SDK
	// 开始----------------------------------------------------

	/**
	 * 获取权限签名编码 _包含缓存功能（jsapi_ticket）
	 * API地址：https://work.weixin.qq.com/api/doc#10029/附录1-JS-SDK使用权限签名算法
	 *
	 * @param corpid    企业ID
	 * @param secrect   应用的凭证密钥
	 * @param isRefresh 强制刷新jsapiTicket
	 * @return
	 */
	public static String getJsapiTicket(String corpid, String secrect, boolean isRefresh) {
		String jsapiTicket = "";
		try {
			/**
			 * API说明
			 *
			 * JS-SDK使用权限签名算法
			 *
			 * jsapi_ticket：
			 *
			 * 生成签名之前必须先了解一下jsapi_ticket，jsapi_ticket是H5应用调用企业微信JS接口的临时票据。正常情况下，jsapi_ticket的有效期为7200秒，通过access_token来获取。由于获取jsapi_ticket的api调用次数非常有限，频繁刷新jsapi_ticket会导致api调用受限，影响自身业务，开发者必须在自己的服务全局缓存jsapi_ticket。
			 *
			 * 第一步获取access_token（有效期7200秒，开发者必须在自己的服务全局缓存access_token）
			 *
			 * 用第一步拿到的access_token 采用http
			 * GET方式请求获得jsapi_ticket（有效期7200秒，开发者必须在自己的服务全局缓存jsapi_ticket）：https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=ACCESS_TOKEN
			 * 成功返回如下JSON：
			 *
			 * { "errcode":0, "errmsg":"ok",
			 * "ticket":"bxLdikRXVbTPdHSM05e5u5sUoXNKd8-41ZO3MhKoyN5OfkWITDGgnr2fwJ0m9E8NYzWKVZvdVtaUgWvsdshFKA",
			 * "expires_in":7200 }
			 * jsapi_ticket最长为512字节。获得jsapi_ticket之后，就可以生成JS-SDK权限验证的签名了。
			 *
			 *
			 */

			JSONObject jsonObj = null;
			String key = corpid + "___" + secrect + "___jsapiTicket";
			String jsonData = WeiXinToken.getData(key);
			if (jsonData != null && !"".equals(jsonData)) {// 判断是否已缓存
				jsonObj = JSONObject.parseObject(jsonData);
			}

			if (isRefresh || jsonObj == null) {// 强制刷新 或 未缓存过，重新获取
				// 判断是否需要重新获取
				String cdatetime = DateTimeUtils.getNowDateTimeStr();
				String urlAdd = WeiXinSDK.WeiXin_logicIp + "/cgi-bin/get_jsapi_ticket";
				StringBuffer urlParams = new StringBuffer();
				urlParams.append("access_token=");
				urlParams.append(getAccessToken(appid, getSecret(null), isRefresh));// 获取授权令牌 _包含缓存功能
//				String url = urlAdd+"?"+urlParams.toString();
				jsonData = HttpRequest.sendGet(urlAdd, urlParams.toString());
				jsonObj = JSONObject.parseObject(jsonData);
				// 判断是否获取到了 token
				if (jsonObj != null && jsonObj.containsKey("ticket")
						&& !"".equals(jsonObj.getString("ticket").trim())) {
					// 将创建时间写入JSON后加入MAP
					jsonObj.put("cdatetime", cdatetime);
					WeiXinToken.setData(key, jsonObj.toString());
				}
			}

			// 判断是否获取到了 jsapiTicket
			if (jsonObj != null && jsonObj.containsKey("ticket") && !"".equals(jsonObj.getString("ticket").trim())) {
				jsapiTicket = jsonObj.getString("ticket");
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return jsapiTicket;
	}

	/**
	 * 获取微信JS-SDK 初始化对象信息
	 *
	 * @param url 必须是调用JS接口页面的完整URL
	 * @return 返回格式： { appId: '', // 必填，企业微信的cropID timestamp: , // 必填，生成签名的时间戳
	 *         nonceStr: '', // 必填，生成签名的随机串 signature: '',// 必填，签名，见附录1
	 *         corpsecret：'',//应用凭证秘钥 access_token : '' //token jsapi_ticket
	 *         :''//ticket生成签名用到的秘钥 }
	 */
	public static JSONObject getWxConfigObj(String url) {

		/**
		 * API说明地址：https://work.weixin.qq.com/api/doc#10029/步骤一：引入JS文件 wx.config(
		 * ------------------------------------------------------------- { debug: true,
		 * //
		 * 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		 * appId: '', // 必填，企业微信的cropID timestamp: , // 必填，生成签名的时间戳 nonceStr: '', //
		 * 必填，生成签名的随机串 signature: '',// 必填，签名，见附录1 jsApiList: [ onMenuShareAppMessage
		 * onMenuShareWechat startRecord stopRecord onVoiceRecordEnd playVoice
		 * pauseVoice stopVoice onVoicePlayEnd uploadVoice downloadVoice chooseImage
		 * previewImage uploadImage downloadImage getNetworkType openLocation
		 * getLocation hideOptionMenu showOptionMenu hideMenuItems showMenuItems
		 * hideAllNonBaseMenuItem showAllNonBaseMenuItem closeWindow scanQRCode ] //
		 * 必填，需要使用的JS接口列表，所有JS接口列表见附录2 }
		 * ------------------------------------------------------------- );
		 *
		 * //https://work.weixin.qq.com/api/doc#10029/附录1-JS-SDK使用权限签名算法 签名算法:
		 * 签名生成规则如下：参与签名的字段包括noncestr（随机字符串）, 有效的jsapi_ticket, timestamp（时间戳）,
		 * url（当前网页的URL，不包含#及其后面部分） 。对所有待签名参数按照字段名的ASCII 码从小到大排序（字典序）后，使用URL键值对的格式 （即
		 * key1=value1&key2=value2…）拼接成字符串string1。这里需要注意的是所有参数名均为小写字符。对string1作sha1加密，字段名和字段值都采用原始值，不进行URL
		 * 转义。
		 *
		 * 即signature=sha1(string1)。 示例
		 *
		 * noncestr=Wm3WZYTPz0wzccnW
		 * jsapi_ticket=sM4AOVdWfPE4DxkXGEs8VMCPGGVi4C3VM0P37wVUCFvkVAy_90u5h9nbSlYy3-Sl-HhTdfl2fzFy1AOcHKP7qg
		 * timestamp=1414587457 url=http://mp.weixin.qq.com 步骤1. 对所有待签名参数按照字段名的ASCII
		 * 码从小到大排序（字典序）后，使用URL键值对的格式（即key1=value1&key2=value2…）拼接成字符串string1：
		 *
		 * jsapi_ticket=sM4AOVdWfPE4DxkXGEs8VMCPGGVi4C3VM0P37wVUCFvkVAy_90u5h9nbSlYy3-Sl-HhTdfl2fzFy1AOcHKP7qg&noncestr=Wm3WZYTPz0wzccnW&timestamp=1414587457&url=http://mp.weixin.qq.com
		 * 步骤2. 对string1进行sha1签名，得到signature：
		 *
		 * 0f9de62fce790f9a083d5c99e95740ceb90c27ed
		 *
		 * 注意事项： 签名用的noncestr和timestamp必须与wx.config中的nonceStr和timestamp相同。
		 * 签名用的url必须是调用JS接口页面的完整URL。 出于安全考虑，开发者必须在服务器端实现签名的逻辑。 如出现invalid signature
		 * 等错误详见附录4常见错误及解决办法。
		 */

		JSONObject config = new JSONObject();
		try {
			if (url != null && !"".equals(url)) {// 地址栏不能为空

				Date newDate = new Date();
				boolean isRefresh = false;// 是否强制刷新，微信令牌缓存
				String corpsecret = WeiXinSDK.secret;// 应用凭证秘钥
				String appId = WeiXinSDK.appid;// 必填，公众号的唯一标识
				String access_token = WeiXinSDK.getAccessToken(appId, corpsecret, isRefresh);// token
				String jsapi_ticket = WeiXinSDK.getJsapiTicket(appId, corpsecret, isRefresh);// ticket生成签名用到的秘钥
				String timestamp = String.valueOf(newDate.getTime()); // 必填，生成签名的时间戳
				String nonceStr = TMUID.getUID(); // 必填，生成签名的随机串
				String signature = "";// 必填，签名

				// 加密算法：
				String data = ("jsapi_ticket=" + jsapi_ticket + "&noncestr=" + nonceStr + "&timestamp=" + timestamp
						+ "&url=" + url);
				;
				signature = DigestUtils.shaHex(data);// 生成签名

				// 最终生成JSON返回
				config.put("corpsecret", corpsecret);
				config.put("appId", appId);
				config.put("access_token", access_token);
				config.put("jsapi_ticket", jsapi_ticket);
				config.put("timestamp", timestamp);
				config.put("nonceStr", nonceStr);
				config.put("signature", signature);// 最终的签名
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return config;
	}

	/**
	 * 获取微信JS-SDK 初始化JSON信息
	 *
	 * @param url 必须是调用JS接口页面的完整URL
	 * @return
	 */
	public static String getWxConfigJson(String url) {
		String json = "";
		try {
			JSONObject config = WeiXinSDK.getWxConfigObj(url);
			if (config != null) {
				json = config.toString();
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return json;
	}

	/**
	 * 获取微信JS-SDK 初始化JSON信息
	 *
	 * @param in_JsonData app发送过来的JSON数据
	 * @return
	 */
	public static String getWxConfig(String in_JsonData) {
		String retValue = "";
		try {
			if (in_JsonData == null || "".equals(in_JsonData)) {
				System.out.println("【错误信息】【logicsys.mobileSys.WeiXinSDK.getWxConfig()】：json字符串为空！");
				return retValue;
			}

			JSONObject in_JsonObject = JSONObject.parseObject(in_JsonData);// 解析JSON对象
			if (in_JsonObject != null) {// JSON对象解析成功
				if (in_JsonObject.containsKey("paramInfo")) {// 判断是否传入了，输入参数
					JSONObject in_paramInfo = in_JsonObject.getJSONObject("paramInfo");// 参数
					if (in_paramInfo.containsKey("url") && !"".equals(in_paramInfo.getString("url").trim())) {// 判断输入参数中是否拥有，数据
						String url = in_paramInfo.getString("url");// 是否传入了RUL
						String json = WeiXinSDK.getWxConfigJson(url);
						// 生成返回数据
						in_JsonObject.put("bReturn", "true");
						in_JsonObject.put("sReturn", "操作成功");
						in_JsonObject.put("data", json); // 将data追加到原json对象中
						retValue = in_JsonObject.toString(); // json对象转换为字符串
					}
				}
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			// 生成返回数据
			JSONObject in_JsonObject = JSONObject.parseObject(in_JsonData); // 转换json对象
			in_JsonObject.put("bReturn", "false");
			in_JsonObject.put("sReturn", "操作失败");
			retValue = in_JsonObject.toString();// json对象转换为字符串
		}
		return retValue;
	}

	/**
	 * 获取微信服务器媒体文件,并保存到本地服务器
	 *
	 * @param token      access_token
	 * @param media_id   媒体文件ID
	 * @param moduleCode 根据模块编码创建目录(自动创建目录)
	 * @return 获得媒体流
	 */
	public static MobileMediaFile getMediaFile(String token, String media_id, String moduleCode) {
		MobileMediaFile file = null;
		try {

			/**
			 * API说明：
			 *
			 * 获取临时素材文件 请求方式：GET（HTTPS）
			 * 请求地址：https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token=ACCESS_TOKEN&media_id=MEDIA_ID
			 *
			 * 参数说明 ：
			 *
			 * 参数 必须 说明 access_token 是 调用接口凭证 media_id 是 媒体文件id 权限说明：
			 * 完全公开，media_id在同一企业内所有应用之间可以共享。
			 *
			 * 返回说明 ： 正确时返回（和普通的http下载相同，请根据http头做相应的处理）：
			 *
			 * { HTTP/1.1 200 OK Connection: close Content-Type: image/jpeg
			 * Content-disposition: attachment; filename="MEDIA_ID.jpg" Date: Sun, 06 Jan
			 * 2013 10:20:18 GMT Cache-Control: no-cache, must-revalidate Content-Length:
			 * 339721 Xxxx }
			 *
			 * 错误时返回（这里省略了HTTP首部）： { "errcode": 40007, "errmsg": "invalid media_id" }
			 *
			 */
			String urlAdd = WeiXinSDK.WeiXin_logicIp + "/cgi-bin/media/get";
			StringBuffer urlParams = new StringBuffer();
			urlParams.append("access_token=");
			urlParams.append(token);
			urlParams.append("&media_id=");
			urlParams.append(media_id);
			file = WeiXinSDK.downloadWeiXinMedia(urlAdd, urlParams.toString(), media_id, moduleCode);
		} catch (Exception ex) {
			ex.printStackTrace();
			file = null;
		}
		return file;
	}

	/**
	 * 获取媒体文件
	 *
	 * @param url        发送请求的URL
	 * @param param      请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
	 * @param fileName   文件名称（无扩展名）
	 * @param moduleCode 根据模块编码创建目录(自动创建目录)
	 * @return 返回文件绝对路径 + 文件名 + 扩展名
	 */
	public static MobileMediaFile downloadWeiXinMedia(String url, String param, String fileName, String moduleCode) {

		/*
		 * 参数获取并判断是否存入指定物理地址 参数获取网络访问的地址
		 */
		String addr = SpringUtils.getBean(ISysConfigService.class).getSysConfig("file_path");
		if (addr == null) {
			addr = "d:/tm4UploadFiles";
		}

//		String url = SpringUtils.getBean(ISysConfigService.class).getSysConfig("file_url");
//		if (url == null) {
//			url = "/files";
//		}

		return HttpRequest.downloadMedia(url, param, fileName, addr, "WeiXin", moduleCode);
	}

	/**
	 * 从微信服务器，下载媒体文件到本地服务器
	 *
	 * @param in_JsonData app发送过来的JSON数据
	 * @return
	 */
	public static String upLoadMedia(String in_JsonData) {
		String retValue = "";
		try {
			if (in_JsonData == null || "".equals(in_JsonData)) {
				System.out.println("【错误信息】【logicsys.mobileSys.WeiXinSDK.upLoadMedia()】：json字符串为空！");
				return retValue;
			}

			JSONObject in_JsonObject = JSONObject.parseObject(in_JsonData);// 解析JSON对象
			if (in_JsonObject != null) {// JSON对象解析成功
				if (in_JsonObject.containsKey("paramInfo")) {// 判断是否传入了，输入参数
					JSONObject in_paramInfo = in_JsonObject.getJSONObject("paramInfo");// 参数
					if (in_paramInfo.containsKey("mediaData")
							&& !"".equals(in_paramInfo.getString("mediaData").trim())) {// 判断输入参数中是否拥有，数据
						String mediaData = in_paramInfo.getString("mediaData");// 是否传入了RUL

						// 读取模块编码(用来创建目录)
						String moduleCode = "other";// 默认模块编码
						if (in_paramInfo.containsKey("moduleCode")
								&& !"".equals(in_paramInfo.getString("moduleCode").trim())) {
							moduleCode = in_paramInfo.getString("moduleCode").trim();
						}

						String json = "{}";
						JSONObject mediaList = new JSONObject();
						JSONArray mediaListAdd = new JSONArray();
						JSONArray mediaListId = new JSONArray();
						// 获取服务器图片ID
						if (mediaData != null && !"".equals(mediaData)) {
							JSONObject mediaDataObj = in_paramInfo.getJSONObject("mediaData");
							if (mediaDataObj.containsKey("serverId")
									&& !"".equals(mediaDataObj.getString("serverId").trim())) {
								JSONArray serverIdArr = mediaDataObj.getJSONArray("serverId");
								if (serverIdArr != null && serverIdArr.size() > 0) {// 媒体大于1个

									boolean isRefresh = false;// 是否强制刷新，微信令牌缓存
									String corpsecret = WeiXinSDK.secret;// 应用凭证秘钥
									String appId = WeiXinSDK.appid;// 必填，公众号的唯一标识

									// 循环取出媒体ID
									for (int i = 0, iCount = serverIdArr.size(); i < iCount; i++) {
										String token = WeiXinSDK.getAccessToken(appId, corpsecret, isRefresh);// token
										String mediaId = serverIdArr.getString(i);

										// 获取微信服务器媒体文件,并保存到本地服务器
										MobileMediaFile file = WeiXinSDK.getMediaFile(token, mediaId, moduleCode);

										// 生成广域网相对路径
										String wanUrl = MediaFileUtil.getMediaWanUrl(file);

										mediaListAdd.add(wanUrl);
										mediaListId.add(JSONObject.toJSON(file));
									}
									mediaList.put("mediaAdds", mediaListAdd);
									mediaList.put("mediaIds", mediaListId);

									json = mediaList.toString();
								}
							}
						}

//						String json = mediaData;//WeiXinSDK.getWxConfigJson(url);
						// 生成返回数据
						in_JsonObject.put("bReturn", "true");
						in_JsonObject.put("sReturn", "操作成功");
						in_JsonObject.put("data", json); // 将data追加到原json对象中
						retValue = in_JsonObject.toString(); // json对象转换为字符串
					}
				}
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			// 生成返回数据
			JSONObject in_JsonObject = JSONObject.parseObject(in_JsonData); // 转换json对象
			in_JsonObject.put("bReturn", "false");
			in_JsonObject.put("sReturn", "操作失败");
			retValue = in_JsonObject.toString();// json对象转换为字符串
		}
		return retValue;
	}

	/**
	 * 获取本地许可文件的值(IsWinXinMsg:是否发送微信消息 true：发消息 false:不发消息)
	 *
	 * @return
	 */
	public static boolean getLicenseWinXin_IsWinXinMsg() {
		boolean val = false;
		try {
			String text = getLicenseWinXin("IsWinXinMsg");
			if (text != null && "1".equals(text.trim()))
				val = true;
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return val;
	}

	/**
	 * 获取本地许可文件的值
	 *
	 * @param key 许可文件中key
	 * @return
	 */
	public static String getLicenseWinXin(String key) {
		/*
		 * 注意事项：保存文件时，最好使用EditPlus保存为UTF-8格式，否则无法读取文件内容。
		 * ------------------XML文件示例------------------ <?xml version="1.0"
		 * encoding="UTF-8"?> <xml-body> <!-- IsWinXinMsg 是否发送微信消息 1：发消息 0:不发消息 -->
		 * <IsWinXinMsg>1</IsWinXinMsg> </xml-body>
		 * ------------------XML文件示例------------------
		 */
		String val = null;
		try {
			val = "1";
//			if (key != null && !"".equals(key)) {
//				// 从缓存中读取
//				JsonCachedLicenseWX jsonCached = JsonCachedLicenseWX.getInstance();
//				String tempVal = jsonCached.getData(key);
//				if (tempVal != null && !"".equals(tempVal.trim())) {
//					val = tempVal;
//				} else {
//					// 读取XML文件
//					String temp_licenseFilePath = "c:\\LicenseWinXin.xml";
//					if (licenseFilePath != null && !"".equals(licenseFilePath.trim())) {// 读取系统配置的文件路径及名称
//						temp_licenseFilePath = licenseFilePath.trim();
//					}
//					File readFile = new File(temp_licenseFilePath);// 定位资源文件
//					if (readFile != null && readFile.exists() && !readFile.isDirectory()) {// 找到配置文件，且不是文件夹
//						SAXReader reader = new SAXReader();
//						reader.setEncoding("UTF-8");
//						Document document = reader.read(readFile);
//						Element root = document.getRootElement();// 得到根节点
//						Element value = root.element(key);
//						if (value != null) {// 判断KEY是否存在
//							val = value.getTextTrim();
//							jsonCached.setData(key, val);
//						}
//					}
//				}
//			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return val;
	}

//	/**
//	 * 获取企业微信签到数据
//	 *
//	 * @param startDate
//	 * @param endDate
//	 * @param zyids
//	 * @return
//	 */
//	public static String opencheckindata(String startDate, String endDate, String zyids) {
//		/*
//		 * 返回值 { "errcode":0, "errmsg":"ok", "checkindata": [{ "userid" : "james",
//		 * "groupname" : "打卡一组", "checkin_type" : "上班打卡", "exception_type" : "地点异常",
//		 * "checkin_time" : 1492617610, "location_title" : "依澜府", "location_detail" :
//		 * "四川省成都市武侯区益州大道中段784号附近", "wifiname" : "办公一区", "notes" : "路上堵车，迟到了5分钟",
//		 * "wifimac" : "3c:46:d8:0c:7a:70", "mediaids":[
//		 * "WWCISP_G8PYgRaOVHjXWUWFqchpBqqqUpGj0OyR9z6WTwhnMZGCPHxyviVstiv_2fTG8YOJq8L8zJT2T2OvTebANV-2MQ"]
//		 * },{ "userid" : "paul", "groupname" : "打卡二组", "checkin_type" : "外出打卡",
//		 * "exception_type" : "时间异常", "checkin_time" : 1492617620, "location_title" :
//		 * "重庆出口加工区", "location_detail" : "重庆市渝北区金渝大道101号金渝大道", "wifiname" : "办公室二区",
//		 * "notes" : "", "wifimac" : "3c:46:d8:0c:7a:71", "mediaids":[
//		 * "WWCISP_G8PYgRaOVHjXWUWFqchpBqqqUpGj0OyR9z6WTwhnMZGCPHxyviVstiv_2fTG8YOJq8L8zJT2T2OvTebANV-2MQ"
//		 * ], "lat": 30547645, "lng": 104063236,
//		 * "deviceid":"E5FA89F6-3926-4972-BE4F-4A7ACF4701E2" }] }
//		 */
//		String appid_kq = "";// 企业id：wwd06078e8e57613cc
//		String AgentId_kq = "";// 模块：3010011
//		String Secret_kq = "";// 模块：M2nbK8VQB5G6boBpvhsyRzZdIns7nO-acO3t-AGmU1k
//		String sqlcong = "select paramValue from  a_config where paramCode='WeiXin_Attendance'";
//		IDataSource ids = getDataSources(sqlcong);
//		if (ids != null && ids.getRowCount() > 0) {
//			String paramValue = ids.get(0).getString("paramValue");
//			String arr[] = paramValue.split(",");
//			try {
//				appid_kq = arr[0];
//			} catch (Exception e) {
//				appid_kq = "";
//			}
//			try {
//				AgentId_kq = arr[1];
//			} catch (Exception e) {
//				AgentId_kq = "";
//			}
//			try {
//				Secret_kq = arr[2];
//			} catch (Exception e) {
//				Secret_kq = "";
//			}
//		}
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//		String info = null;
//		try {
//			Date kssj = new Date();
//			Date jzsj = new Date();
//			if (startDate != null && !startDate.equals("")) {
//				if (startDate.length() == 10) {
//					startDate = startDate + " 00:00:00";
//				}
//				kssj = sdf.parse(startDate);
//			} else {
//				String ks = DateTimeUtils.getNowDateStr() + " 00:00:00";
//				kssj = sdf.parse(ks);
//			}
//			if (endDate != null && !endDate.equals("")) {
//				if (endDate.length() == 10) {
//					endDate = endDate + " 00:00:00";
//				}
//				jzsj = sdf.parse(endDate);
//			} else {
//				String jz = DateTimeUtils.getNowDateStr() + " 23:59:59";
//				jzsj = sdf.parse(jz);
//			}
//
//			if (zyids != null && !"".equals(zyids.trim())) {
//				JSONArray userids = new JSONArray();// 微信账号列表
//				// 查找绑定到系统的微信账号ID
//				BindData bindData = BindData.getInstance();
//				String[] zyidArr = zyids.split(",");
//				// 定义redis，缓存人员id和微信id
//				String key_weixin = "Check_Attendance_weixin";
//				// 定义缓存微信帐号和组员id关系map
//				Map<String, String> map = RedisOps.getMap(key_weixin);
//				if (map == null || map.size() <= 0) {
//					map = new HashMap<String, String>();
//				}
//				for (int i = 0, iCount = zyidArr.length; i < iCount; i++) {
//					// 判断每一个人员是否绑定微信账号
//					String zyid = zyidArr[i];
//					String userid_wx = "";// 微信绑定的账号
//					try {
//						userid_wx = bindData.getBind(BindData.SysCode_WeiXin, Long.parseLong(zyid));
//						if (userid_wx != null && !userid_wx.equals("")) {
//							map.put(userid_wx, zyid);
//							// 查找到对应的微信账号
//							userids.add(userid_wx);
//						}
//					} catch (Exception ex) {
//						ex.printStackTrace();
//					}
//				}
//				RedisOps.setMap(key_weixin, map);
//				// 判断是否对照出了有效人员
//				if (userids != null && userids.size() > 0) {
//					String token = getAccessToken(appid_kq, Secret_kq, false);// 获取授权令牌 _包含缓存功能
//					if (token != null && !"".equals(token.trim())) {
//						// 生成链接地址
//						String urlAdd = WeiXinSDK.WeiXin_logicIp + "/cgi-bin/checkin/getcheckindata";
//						StringBuffer urlParams = new StringBuffer();
//						urlParams.append("access_token=");
//						urlParams.append(token);
//						String url = urlAdd + "?" + urlParams.toString();
//
//						// 获取数据参数实例
////		    			{
////	    				   "opencheckindatatype": 3,       //打卡类型。1：上下班打卡；2：外出打卡；3：全部打卡
////	    				   "starttime": 1492617600,        //获取打卡记录的开始时间。Unix时间戳
////	    				   "endtime": 1492790400,          //获取打卡记录的结束时间。Unix时间戳
////	    				   "useridlist": ["james","paul"]  //需要获取打卡记录的用户列表["james","paul"]
////	    				}
//						JSONObject msgData = new JSONObject();
//						msgData.put("opencheckindatatype", 3);
//						msgData.put("starttime", kssj.getTime() / 1000);
//						msgData.put("endtime", jzsj.getTime() / 1000);
//						msgData.put("useridlist", userids);
//
//						// POST方式请求数据
//						info = HttpRequest.sendPost(url, msgData.toString(), true);
//					}
//				}
//
//			}
//		} catch (Exception ex) {
//			ex.printStackTrace();
//		}
//		return info;
//	}

	// ----------------------------------------------------JS-SDK
	// 结束----------------------------------------------------
//	public static IDataSource getDataSources(String sql) {
//		IDataSource ids = null;
//		EntityDao<Object> ed = new EntityDao<Object>(null);
//		try {
//			ids = ed.executeQuery(sql);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return ids;
//	}
//    /**
//     * 代码测试
//     * @param args
//     */
//    public static void main(String[] args){
//    	try{
////    		sendMsg("2777","<a href=\"http://www.mytm3.com:9668/tm3/MLogin?type=wx&page=mobileSys/yhApp/page/task/dsp_0505.html&params={\"tmuid\":\"ZZSGN9TG05Q388XDYJ\"}\">任务【16aaaaaaaaaaaaac】待审批!</a>");
//    		boolean val = getLicenseWinXin_IsWinXinMsg();//获取本地许可文件的值(IsWinXinMsg:是否发送微信消息 true：发消息 false:不发消息)
//    		System.out.println(val);
//    	}catch(Exception ex){
//    		ex.printStackTrace();
//    	}
//    }
}