package com.yunhesoft.system.mobileSys.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.PropertyUtils;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.mobileSys.util.WeiXinSDK;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

@Api(tags = "移动端登录验证")
@RestController
@RequestMapping("/system/mobileSys/login")
@Log4j2
public class MobileLogin extends BaseRestController {
	@Autowired
	private AuthService authService;

	// 调用示例：http://www.yunhesoft.net:9969/ex-auth.html?accessToken=wx&type=wx&params=a=1&page=system/config/index
	// 调用示例（调试模式-zyid登录）：http://localhost:81/ex-auth.html?accessToken=wx&type=wxLogin&params=a=1&page=system/config/index&debug=1&zyid=administrator
	// 调用示例（调试模式-绑定登录）：http://localhost:81/ex-auth.html?accessToken=wx&type=wxLogin&params=a=1&page=system/config/index&debug=1&zyid=

	@ApiOperation(value = "获取微信用户CODE")
	@RequestMapping(value = "/getWeiXinUserCode", method = { RequestMethod.GET })
	public Res<?> getWeiXinUserCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
		try {
			response.setHeader("Access-Control-Allow-Origin", "*");
			request.setCharacterEncoding("UTF-8");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Pragma", "No-cache");
			response.setHeader("Cache-Control", "no-cache");
			response.setDateHeader("Expires", 0);
//			response.setContentType("text/html");

			String type = request.getParameter("type") == null ? "" : request.getParameter("type");// wx:微信登录验证，TM:tm系统登录验证
			String page = request.getParameter("page") == null ? "" : request.getParameter("page");// 需要跳转的功能
			String params = request.getParameter("params") == null ? "" : request.getParameter("params");// 附加参数
			String jsonObjStr = WeiXinSDK.wxLogin(type, page, params, null, null, request, response);

			return Res.OK(jsonObjStr);
		} catch (Exception e) {
			log.error("获取微信用户CODE失败", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "获取微信用户信息")
	@RequestMapping(value = "/getWeiXinUserInfo", method = { RequestMethod.GET })
	public Res<?> getWeiXinUserInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
		try {
			response.setHeader("Access-Control-Allow-Origin", "*");
			request.setCharacterEncoding("UTF-8");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Pragma", "No-cache");
			response.setHeader("Cache-Control", "no-cache");
			response.setDateHeader("Expires", 0);
//			response.setContentType("text/html");

			String type = request.getParameter("type") == null ? "" : request.getParameter("type");// wx:微信登录验证，TM:tm系统登录验证
			String page = request.getParameter("page") == null ? "" : request.getParameter("page");// 需要跳转的功能
			String params = request.getParameter("params") == null ? "" : request.getParameter("params");// 附加参数

			String code = request.getParameter("code") == null ? "" : request.getParameter("code");// 微信提供的code
			String state = request.getParameter("state") == null ? "" : request.getParameter("state");// 微信提供的附加参数

			// 调试参数
			String debug = request.getParameter("debug") == null ? "" : request.getParameter("debug");// 调试模式
			String zyid = request.getParameter("zyid") == null ? "" : request.getParameter("zyid");// 调试模式下，可传入组员ID

			String userInfoJson = "";
			debug = "false";// 关闭调试模式
			if (debug != null && ("1".equals(debug) || "true".equals(debug.trim()))) {// 调试模式
				// 模拟登录信息
				userInfoJson = "{\"errcode\":\"0\",\"wxname\":\"wxUserName\",\"zyid\":\"" + zyid
						+ "\",\"errmsg\":\"ok\",\"zyxm\":\"运和超管\"}";
			} else {
				userInfoJson = WeiXinSDK.wxLogin(type, page, params, code, state, request, response);
			}

			// 2、判断跳转页面 或 提示错误信息
			if (userInfoJson != null && !"".equals(userInfoJson.trim())) {
				// 判断登录验证是否通过
				JSONObject jsonObj = JSONObject.parseObject(userInfoJson);
				jsonObj.put("v", PropertyUtils.getProperty("app.build_version").replaceAll("[\r\n]+", ""));// 添加版本号功能，防止浏览器缓存
				jsonObj.put("type", "wx");
				jsonObj.put("page", page);
				jsonObj.put("params", params);
				jsonObj.put("token", "");
				// 验证通过，errcode错误编码(0：无错误1：有错误)
				if (jsonObj != null && jsonObj.containsKey("errcode")
						&& "0".equals(jsonObj.getString("errcode").trim())) {
					try {// 获取TM4 token,并写入到 人员信息中
						String accessToken = authService.getAppAccessToken("TM4-APP");
						// 获取用户访问token
						String token = authService.getApploginToken(request, jsonObj.get("zyid").toString(), "userId",
								accessToken);
						jsonObj.put("token", token);
					} catch (Exception ex) {
						log.error("获取微信用户信息失败", ex);
					}
				}
				userInfoJson = jsonObj.toString();
			}
			return Res.OK(userInfoJson);
		} catch (Exception e) {
			log.error("获取微信用户信息失败", e);
			return Res.FAIL(e.getMessage());
		}
	}

}
