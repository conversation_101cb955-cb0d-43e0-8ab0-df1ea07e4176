package com.yunhesoft.system.mobileSys.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.mobileSys.util.BindData;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;

@Api(tags = "用户绑定")
@RestController
@Log4j2
@RequestMapping("/system/mobileSys/bindData")
public class BindDataController extends BaseRestController {

	/**
	 * 用户绑定服务类
	 */
	@Autowired
	private BindData bindData;

	@ApiOperation(value = "绑定")
	@RequestMapping(value = "/bind", method = { RequestMethod.POST })
	public Res<?> bind(@RequestParam @ApiParam(value = "系统编码：RTX、域账号、微信等") String sysCode,
			@RequestParam @ApiParam(value = "第三方系统账号") String sysUser,
			@RequestParam @ApiParam(value = "TM4系统人员ID") String zyid) {
		try {
			// 绑定
			bindData.bind(sysCode, sysUser, zyid);
			return Res.OK(true);
		} catch (Exception e) {
			log.error("调用失败:绑定", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "解绑")
	@RequestMapping(value = "/removeBind", method = { RequestMethod.POST })
	public Res<?> removeBind(@RequestParam @ApiParam(value = "系统编码：RTX、域账号、微信等") String sysCode,
			@RequestParam @ApiParam(value = "第三方系统账号") String sysUser,
			@RequestParam @ApiParam(value = "TM4系统人员ID") String zyid) {
		try {
			// 解绑
			bindData.removeBind(sysCode, sysUser, zyid);
			return Res.OK(true);
		} catch (Exception e) {
			log.error("调用失败:解绑", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "获取绑定信息（返回第三方账号）")
	@RequestMapping(value = "/getOtherSysId", method = { RequestMethod.POST })
	public Res<?> getOtherSysId(@RequestParam @ApiParam(value = "系统编码：RTX、域账号、微信等") String sysCode,
			@RequestParam @ApiParam(value = "TM4系统人员ID") String zyid) {
		try {
			return Res.OK(bindData.getBind(sysCode, zyid));
		} catch (Exception e) {
			log.error("调用失败:获取绑定信息（返回第三方账号）", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "获取绑定信息（返回TM系统人员ID）")
	@RequestMapping(value = "/getMySysId", method = { RequestMethod.POST })
	public Res<?> getMySysId(@RequestParam @ApiParam(value = "系统编码：RTX、域账号、微信等") String sysCode,
			@RequestParam @ApiParam(value = "第三方系统账号") String sysUser) {
		try {
			return Res.OK(bindData.getTmuid(sysCode, sysUser));
		} catch (Exception e) {
			log.error("调用失败:获取绑定信息（返回第三方账号）", e);
			return Res.FAIL(e.getMessage());
		}
	}

	// ------------------------------------------------------------------------------------------------------------------------------------------

	@ApiOperation(value = "[微信]绑定")
	@RequestMapping(value = "/bind_WeiXin", method = { RequestMethod.POST })
	public Res<?> bind_WeiXin(@RequestParam @ApiParam(value = "第三方系统账号") String sysUser,
			@RequestParam @ApiParam(value = "TM4系统人员ID") String zyid) {
		try {
			// 绑定
			bindData.bind(BindData.SysCode_WeiXin, sysUser, zyid);
			return Res.OK(true);
		} catch (Exception e) {
			log.error("调用失败:绑定", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "[微信]解绑")
	@RequestMapping(value = "/removeBind_WeiXin", method = { RequestMethod.POST })
	public Res<?> removeBind_WeiXin(@RequestParam @ApiParam(value = "第三方系统账号") String sysUser,
			@RequestParam @ApiParam(value = "TM4系统人员ID") String zyid) {
		try {
			// 解绑
			bindData.removeBind(BindData.SysCode_WeiXin, sysUser, zyid);
			return Res.OK(true);
		} catch (Exception e) {
			log.error("调用失败:解绑", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "[微信]获取绑定信息（返回第三方账号）")
	@RequestMapping(value = "/getOtherSysId_WeiXin", method = { RequestMethod.POST })
	public Res<?> getOtherSysId_WeiXin(@RequestParam @ApiParam(value = "TM4系统人员ID") String zyid) {
		try {
			return Res.OK(bindData.getBind(BindData.SysCode_WeiXin, zyid));
		} catch (Exception e) {
			log.error("调用失败:获取绑定信息（返回第三方账号）", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "[微信]获取绑定信息（返回TM系统人员ID）")
	@RequestMapping(value = "/getMySysId_WeiXin", method = { RequestMethod.POST })
	public Res<?> getMySysId_WeiXin(@RequestParam @ApiParam(value = "第三方系统账号") String sysUser) {
		try {
			return Res.OK(bindData.getTmuid(BindData.SysCode_WeiXin, sysUser));
		} catch (Exception e) {
			log.error("调用失败:获取绑定信息（返回第三方账号）", e);
			return Res.FAIL(e.getMessage());
		}
	}

}
