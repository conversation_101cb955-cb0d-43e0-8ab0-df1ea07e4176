package com.yunhesoft.system.mobileSys.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.mobileSys.entity.dto.MobileWxSendGroupEmpDto;
import com.yunhesoft.system.mobileSys.entity.dto.MobileWxSendQueryDto;
import com.yunhesoft.system.mobileSys.entity.dto.MobileWxSendSaveDto;
import com.yunhesoft.system.mobileSys.entity.po.MobileWxSend;
import com.yunhesoft.system.mobileSys.entity.po.MobileWxSendGroup;
import com.yunhesoft.system.mobileSys.entity.vo.TaskWeixinDataVo;
import com.yunhesoft.system.mobileSys.service.IWeixinSendService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

@Api(tags = "微信推送")
@RestController
@Log4j2
@RequestMapping("/system/mobileSys/weixinSend")
public class WeixinSendController extends BaseRestController {

	@Autowired
	private IWeixinSendService wxSendServ;

	@ApiOperation(value = "获取数据列表")
	@RequestMapping(value = "/getList", method = { RequestMethod.POST })
	public Res<?> getWxSendList(@RequestBody MobileWxSendQueryDto dto) {
		try {
			Pagination<?> pageInfo = null;
			if (dto.getPageSize() > 0) {// 创建分页信息
				pageInfo = Pagination.create(dto.getPageNum(), dto.getPageSize());
			}
			List<TaskWeixinDataVo> list = wxSendServ.getWxSendList(dto.getQueryStr(), pageInfo);
			return Res.OK(list, pageInfo.getTotal());
		} catch (Exception e) {
			log.error("发送消息失败", e);
			return Res.FAIL(e.getMessage());
		}

	}

	@ApiOperation(value = "发送微信消息")
	@RequestMapping(value = "/sendMsg", method = { RequestMethod.POST })
	public Res<?> execSendMsg(@RequestParam String id) {
		try {
			boolean bln = wxSendServ.execSendMsg(id);
			return Res.OK(bln);
		} catch (Exception e) {
			log.error("发送消息失败", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "启动任务")
	@RequestMapping(value = "/startJob", method = { RequestMethod.POST })
	public Res<?> startJob(@RequestParam String id) {
		String s = wxSendServ.startJob(id);
		//判断是否启动成功，更新数据库状态
		if(StringUtils.isEmpty(s)) {
			MobileWxSend item = wxSendServ.getMobileWxSendById(id);
			if(item!=null) {
				MobileWxSendSaveDto bean = ObjUtils.copyTo(item, MobileWxSendSaveDto.class);
				bean.setTmUsed(1);
				wxSendServ.updateData(bean);
			}
		}
		return Res.OK(s);
	}

	@ApiOperation(value = "停止任务")
	@RequestMapping(value = "/endJob", method = { RequestMethod.POST })
	public Res<?> endJob(@RequestParam String id) {
		try {
			wxSendServ.endJob(id);
			
			//更新数据库状态
			MobileWxSend item = wxSendServ.getMobileWxSendById(id);
			if(item!=null) {
				MobileWxSendSaveDto bean = ObjUtils.copyTo(item, MobileWxSendSaveDto.class);
				bean.setTmUsed(0);
				wxSendServ.updateData(bean);
			}
			
			return Res.OK();
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "开始所有任务")
	@RequestMapping(value = "/startAllJob", method = { RequestMethod.POST })
	public Res<?> startAllJob() {
		try {
			wxSendServ.startAllJob();
			return Res.OK();
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "添加记录")
	@RequestMapping(value = "/insertData", method = { RequestMethod.POST })
	public Res<?> insertData(@RequestBody MobileWxSendSaveDto dto) {
		try {
			return Res.OK(wxSendServ.insertData(dto));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "删除单条记录")
	@RequestMapping(value = "/deleteData", method = { RequestMethod.POST })
	public Res<?> deleteData(@RequestBody MobileWxSendSaveDto dto) {
		try {
			return Res.OK(wxSendServ.deleteData(dto));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "批量删除记录")
	@RequestMapping(value = "/batchDeleteData", method = { RequestMethod.POST })
	public Res<?> batchDeleteData(@RequestParam String ids) {
		try {
			List<String> list = new ArrayList<String>();
			String[] ary = ids.split(",");
			for (String s : ary) {
				list.add(s);
			}
			return Res.OK(wxSendServ.deleteData(list));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "修改记录")
	@RequestMapping(value = "/updateData", method = { RequestMethod.POST })
	public Res<?> updateData(@RequestBody MobileWxSendSaveDto dto) {
		try {
			return Res.OK(wxSendServ.updateData(dto));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}
	
	@ApiOperation(value = "添加分组信息")
	@RequestMapping(value = "/insertMobileWxSendGroup", method = { RequestMethod.POST })
	public Res<?> insertMobileWxSendGroup(@RequestBody MobileWxSendGroup dto) {
		
		try {
			return Res.OK(wxSendServ.insertMobileWxSendGroup(dto));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}
//	@ApiOperation(value = "添加小组人员")
//	@RequestMapping(value = "/insertMobileWxSendGroupEmpList", method = { RequestMethod.POST })
//	public Res<?> insertMobileWxSendGroupEmpList(@RequestBody MobileWxSendGroup dto) {
//		
//		try {
//			return Res.OK(wxSendServ.insertMobileWxSendGroup(dto));
//		} catch (Exception e) {
//			log.error("", e);
//			return Res.FAIL(e.getMessage());
//		}
//	}
	@ApiOperation(value = "更新分组信息")
	@RequestMapping(value = "/updateMobileWxSendGrou", method = { RequestMethod.POST })
	public Res<?> updateMobileWxSendGrou(@RequestBody MobileWxSendGroup dto) {
		try {
			return Res.OK(wxSendServ.updateMobileWxSendGrou(dto));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}
	
	@ApiOperation(value = "删除分组信息")
	@RequestMapping(value = "/deleteMobileWxSendGroup", method = { RequestMethod.POST })
	public Res<?> deleteMobileWxSendGroup(@RequestParam String id) {
		try {
			return Res.OK(wxSendServ.deleteMobileWxSendGroup(id));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}
	
	@ApiOperation(value = "获取分组信息")
	@RequestMapping(value = "/getMobileWxSendGroupList", method = { RequestMethod.POST })
	public Res<?> getMobileWxSendGroupList() {
		try {
			return Res.OK(wxSendServ.getMobileWxSendGroupList());
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}
//	@ApiOperation(value = "批量修改状态")
//	@RequestMapping(value = "/batchUpdateData", method = { RequestMethod.POST })
//	public Res<?> batchUpdateData(@RequestParam String ids, @RequestParam Integer used) {
//		try {
//			List<String> list = new ArrayList<String>();
//			String[] ary = ids.split(",");
//			for (String s : ary) {
//				list.add(s);
//			}
//			return Res.OK(wxSendServ.updateData(list, used == null ? 0 : used));
//		} catch (Exception e) {
//			log.error("", e);
//			return Res.FAIL(e.getMessage());
//		}
//	}
	
	@ApiOperation(value = "获取分组人员列表")
	@RequestMapping(value = "/getMobileWxSendGroupEmpList", method = { RequestMethod.POST })
	public Res<?> getMobileWxSendGroupEmpList(@RequestBody MobileWxSendGroupEmpDto dto) {
		try {
			return Res.OK(wxSendServ.getMobileWxSendGroupEmpList(dto.getGroupId()));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}
	
	@ApiOperation(value = "删除分组人员")
	@RequestMapping(value = "/deleteMobileWxSendGroupEmpList", method = { RequestMethod.POST })
	public Res<?> deleteMobileWxSendGroupEmpList(@RequestBody MobileWxSendGroupEmpDto dto) {
		try {
			return Res.OK(wxSendServ.deleteMobileWxSendGroupEmpList(dto.getGroupId(), dto.getEmpId()));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}
	
	@ApiOperation(value = "添加分组人员")
	@RequestMapping(value = "/insertMobileWxSendGroupEmpList", method = { RequestMethod.POST })
	public Res<?> insertMobileWxSendGroupEmpList(@RequestBody MobileWxSendGroupEmpDto dto) {
		try {
			return Res.OK(wxSendServ.insertMobileWxSendGroupEmpList(dto.getGroupId(), dto.getEmpList()));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}
	
	@ApiOperation(value = "更新分组人员")
	@RequestMapping(value = "/updateMobileWxSendGroupEmpList", method = { RequestMethod.POST })
	public Res<?> updateMobileWxSendGroupEmpList(@RequestBody MobileWxSendGroupEmpDto dto) {
		try {
			return Res.OK(wxSendServ.updateMobileWxSendGroupEmpList(dto.getGroupId(), dto.getEmpList()));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}
}
