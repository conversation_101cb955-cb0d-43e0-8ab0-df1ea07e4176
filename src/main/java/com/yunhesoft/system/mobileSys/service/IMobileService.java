package com.yunhesoft.system.mobileSys.service;

import java.util.List;

import com.yunhesoft.system.mobileSys.entity.po.MobileMsgUrl;

public interface IMobileService {

	/**
	 * 创建移动端消息跳转地址对象（不操作数据库）
	 * 
	 * @param modulecode   模板编码(必填) 例如：system
	 * @param funname      功能名称(必填) 例如：系统模块_数据源查询
	 * @param funcode      功能编码(必填) 例如：tds_query
	 * @param appurl       APP页面跳转地址(必填)/pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
	 * @param appurlParams APP页面跳转地址参数
	 * @param memo         描述
	 * @return
	 */
	MobileMsgUrl createMobileMsgUrl(String modulecode, String funname, String funcode, String appurl,
			String appurlParams, String memo);

	/**
	 * 创建移动端消息跳转地址对象（不操作数据库）
	 * 
	 * @param modulecode        模板编码(必填) 例如：system
	 * @param funname           功能名称(必填) 例如：系统模块_数据源查询
	 * @param funcode           功能编码(必填) 例如：tds_query
	 * @param appurl            APP页面跳转地址(必填)/pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
	 * @param appurlParams      APP页面跳转地址参数
	 * @param batchAppurl       APP批量页面跳转地址(必填)/pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
	 * @param batchAppurlParams APP批量页面跳转地址参数
	 * @param memo              描述
	 * @return
	 */
	MobileMsgUrl createMobileMsgUrl(String modulecode, String funname, String funcode, String appurl,
			String appurlParams, String batchAppurl, String batchAppurlParams, String memo);

	/**
	 * 初始化移动端消息跳转地址（操作数据库-校验当前功能是否存在）
	 * 
	 * @param modulecode   模板编码(必填) 例如：system
	 * @param funname      功能名称(必填) 例如：系统模块_数据源查询
	 * @param funcode      功能编码(必填) 例如：tds_query
	 * @param appurl       APP页面跳转地址(必填)/pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
	 * @param appurlParams APP页面跳转地址参数
	 * @param memo         描述
	 * @return
	 */
	MobileMsgUrl initMobileMsgUrl(String modulecode, String funname, String funcode, String appurl, String appurlParams,
			String memo);

	/**
	 * 获取移动端消息跳转地址列表（不读取redis）
	 * 
	 * @param showAll    true:是否显示所有 ;false :只显示used=1
	 * @param modulecode 模块编码 例如：system
	 * @param funcode    功能编码 例如：tds_query
	 * @return
	 */
	List<MobileMsgUrl> getMobileMsgUrl(boolean showAll, String modulecode, String funcode);

	/**
	 * 获取移动端消息跳转地址列表（读取redis缓存）
	 * 
	 * @param modulecode 模块编码 例如：system
	 * @param funcode    功能编码 例如：tds_query
	 * @return
	 */
	MobileMsgUrl getMobileMsgUrl(String modulecode, String funcode);

	/**
	 * 初始化系统包移动端数据
	 * 
	 * @return
	 */
	boolean initMobile(List<MobileMsgUrl> listMobileMsgUrl);

	/**
	 * 清除移动端所有redis缓存（慎用）
	 */
	void clearCachedAll();

	/**
	 * 清除移动端消息跳转地址redis缓存
	 * 
	 * @param modulecode 模块编码 例如：system
	 * @param funcode    功能编码 例如：tds_query
	 */
	void clearCached_MsgUrl(String modulecode, String funcode);

}
