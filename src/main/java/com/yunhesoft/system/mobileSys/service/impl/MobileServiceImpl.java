package com.yunhesoft.system.mobileSys.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.menu.service.ISysMenuLibInitService;
import com.yunhesoft.system.mobileSys.entity.po.MobileMsgUrl;
import com.yunhesoft.system.mobileSys.service.IMobileService;

import dm.jdbc.util.StringUtil;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class MobileServiceImpl implements IMobileService {

	private static String READ_KEY = "SYSTEM_MOBILE";// redis key值
	private static String READ_KEY_MSG_URL = READ_KEY + ":MSG_URL";// redis key值

	@Autowired
	private RedisUtil redis; // redis实例

	@Autowired
	private EntityService entityService; // 数据库操作

	@Autowired
	private ISysMenuLibInitService menuLibInitServ; // 模块初始化

	private String getReadKey_MsgUrl() {
//		if (MultiTenantUtils.enalbe()) {// 多租户模式
//			return READ_KEY_MSG_URL + ":" + MultiTenantUtils.getTenantId();
//		} else {
		return READ_KEY_MSG_URL;
//		}
	}

	/**
	 * 初始化系统移动端数据
	 * 
	 * @return
	 */
	@Override
	public boolean initMobile(List<MobileMsgUrl> mobileMsgUrlList) {
		boolean b = true;
		try {
			// 初始化移动端跳转链接
			List<MobileMsgUrl> insertMobileMsgUrlList = new ArrayList<MobileMsgUrl>();
			if (StringUtils.isNotEmpty(mobileMsgUrlList)) {// 跳转链接
				List<MobileMsgUrl> list = this.getMobileMsgUrl(true, null, null);
				int sort = list.size();
				Map<String, MobileMsgUrl> map = new HashMap<String, MobileMsgUrl>();
				if (StringUtils.isNotEmpty(list)) {
					for (MobileMsgUrl e : list) {
						map.put(e.getModulecode() + ":" + e.getFuncode(), e);
					}
				}
				for (MobileMsgUrl e : mobileMsgUrlList) {
					String key = e.getModulecode() + ":" + e.getFuncode();
					if (!map.containsKey(key)) {
						sort++;
						e.setTmsort(sort);
						insertMobileMsgUrlList.add(e);
					}
				}
			}

			if (insertMobileMsgUrlList.size() > 0) {
				this.insertMobileMsgUrl(insertMobileMsgUrlList);
			}
		} catch (Exception ex) {
			b = false;
			ex.printStackTrace();
		}
		return b;
	}

	/**
	 * 创建移动端消息跳转地址对象（不操作数据库）
	 * 
	 * @param modulecode   模板编码(必填) 例如：system
	 * @param funname      功能名称(必填) 例如：系统模块_数据源查询
	 * @param funcode      功能编码(必填) 例如：tds_query
	 * @param appurl       APP页面跳转地址(必填)/pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
	 * @param appurlParams APP页面跳转地址参数
	 * @param memo         描述
	 * @return
	 */
	@Override
	public MobileMsgUrl createMobileMsgUrl(String modulecode, String funname, String funcode, String appurl,
			String appurlParams, String memo) {
		MobileMsgUrl msgurl = null;
		try {
			if (StringUtils.isNotEmpty(modulecode) && StringUtils.isNotEmpty(funname) && StringUtils.isNotEmpty(funcode)
					&& StringUtils.isNotEmpty(appurl)) {
				msgurl = this.createMobileMsgUrl(modulecode, funname, funcode, appurl, appurlParams, null, null, memo);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return msgurl;
	}

	/**
	 * 创建移动端消息跳转地址对象（不操作数据库）
	 * 
	 * @param modulecode        模板编码(必填) 例如：system
	 * @param funname           功能名称(必填) 例如：系统模块_数据源查询
	 * @param funcode           功能编码(必填) 例如：tds_query
	 * @param appurl            APP页面跳转地址(必填)/pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
	 * @param appurlParams      APP页面跳转地址参数
	 * @param batchAppurl       APP批量页面跳转地址(必填)/pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
	 * @param batchAppurlParams APP批量页面跳转地址参数
	 * @param memo              描述
	 * @return
	 */
	@Override
	public MobileMsgUrl createMobileMsgUrl(String modulecode, String funname, String funcode, String appurl,
			String appurlParams, String batchAppurl, String batchAppurlParams, String memo) {
		MobileMsgUrl msgurl = null;
		try {
			if (StringUtils.isNotEmpty(modulecode) && StringUtils.isNotEmpty(funname) && StringUtils.isNotEmpty(funcode)
					&& StringUtils.isNotEmpty(appurl)) {
				msgurl = new MobileMsgUrl();
				msgurl.setId(TMUID.getUID());
				msgurl.setModulecode(modulecode);
				msgurl.setFuncode(funcode);
				msgurl.setFunname(funname);
				msgurl.setAppurl(appurl);
				msgurl.setAppurlParams(appurlParams);
				msgurl.setMemo(memo);
				msgurl.setTmsort(0);
				msgurl.setTmused(1);
				msgurl.setCreateTime(DateTimeUtils.getNowDate());
				//初始化批量处理菜单
				if(StringUtils.isNotEmpty(batchAppurl)) {
					msgurl.setBatchUse(1);//是否使用批量功能
					msgurl.setBatchAppurl(batchAppurl);
					msgurl.setBatchAppurlParams(batchAppurlParams);
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return msgurl;
	}

	/**
	 * 初始化移动端消息跳转地址（操作数据库-校验当前功能是否存在）
	 * 
	 * @param modulecode   模板编码(必填) 例如：system
	 * @param funname      功能名称(必填) 例如：系统模块_数据源查询
	 * @param funcode      功能编码(必填) 例如：tds_query
	 * @param appurl       APP页面跳转地址(必填)/pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
	 * @param appurlParams APP页面跳转地址参数
	 * @param memo         描述
	 * @return
	 */
	@Override
	public MobileMsgUrl initMobileMsgUrl(String modulecode, String funname, String funcode, String appurl,
			String appurlParams, String memo) {
		MobileMsgUrl msgurl = null;
		List<MobileMsgUrl> list = this.getMobileMsgUrl(true, modulecode, funcode);
		if (list != null && list.size() > 0) {
			// 已存在该功能，无操作
			msgurl = list.get(0);
		} else {
			// 初始化此功能
			msgurl = this.createMobileMsgUrl(modulecode, funname, funcode, appurl, appurlParams, memo);
			if (msgurl != null) {
				List<MobileMsgUrl> listInsert = new ArrayList<MobileMsgUrl>();
				listInsert.add(msgurl);
				boolean b = this.insertMobileMsgUrl(listInsert);
				if (!b) {// 插入失败
					msgurl = null;
				}
			}
		}
		return msgurl;
	}

	/**
	 * 获取移动端消息跳转地址列表
	 * 
	 * @param modulecode 模块编码 例如：system
	 * @param funcode    功能编码 例如：tds_query
	 * @return
	 */
	private String getMobileMsgUrlKey(String modulecode, String funcode) {
		String v = null;
		if (StringUtil.isNotEmpty(modulecode) && StringUtil.isNotEmpty(funcode)) {
			StringBuffer sbuf = new StringBuffer();
			sbuf.append(modulecode);
			sbuf.append(":");
			sbuf.append(funcode);
			v = sbuf.toString();
		}
		return v;
	}

	/**
	 * 获取移动端消息跳转地址列表
	 * 
	 * @param modulecode 模块编码 例如：system
	 * @param funcode    功能编码 例如：tds_query
	 * @return
	 */
	private MobileMsgUrl getRedis_MobileMsgUrl(String modulecode, String funcode) {
		MobileMsgUrl v = null;
		String hkey = this.getMobileMsgUrlKey(modulecode, funcode);
		if (StringUtil.isNotEmpty(hkey)) {
			if (menuLibInitServ.isModuleRegister(modulecode)) {// 判断模块是否注册
				v = this.getRedis(this.getReadKey_MsgUrl(), hkey);
			}
		}
		return v;
	}

	/**
	 * 获取移动端消息跳转地址列表
	 * 
	 * @param modulecode 模块编码 例如：system
	 * @param funcode    功能编码 例如：tds_query
	 * @param v          功能编码 例如：要存储的数据
	 * @return
	 */
	private void setRedis_MobileMsgUrl(String modulecode, String funcode, MobileMsgUrl v) {
		String hkey = this.getMobileMsgUrlKey(modulecode, funcode);
		if (StringUtil.isNotEmpty(hkey)) {
			if (menuLibInitServ.isModuleRegister(modulecode)) {// 判断模块是否注册
				this.setRedis(this.getReadKey_MsgUrl(), hkey, v);
			}
		}
	}

	/**
	 * 从redis中获取数据
	 * 
	 * @param key  Redis键
	 * @param hKey Hash键
	 * @return
	 */
	private MobileMsgUrl getRedis(String key, String hkey) {
		MobileMsgUrl v = null;
		try {
			StringBuffer sbuf = new StringBuffer();
			sbuf.append(key);
			sbuf.append(":");
			sbuf.append(hkey);
			String strKey = sbuf.toString().toUpperCase();
			if (redis.hasKey(strKey)) {
				v = redis.getClassObject(MobileMsgUrl.class, strKey);
			}
		} catch (Exception e) {
			log.error("", e);
		}
		return v;
	}

	/**
	 * 数据存入redis
	 * 
	 * @param key   Redis键
	 * @param hKey  Hash键
	 * @param value 值
	 */
	private void setRedis(String key, String hkey, MobileMsgUrl v) {
		try {
			StringBuffer sbuf = new StringBuffer();
			sbuf.append(key);
			sbuf.append(":");
			sbuf.append(hkey);
			String strKey = sbuf.toString().toUpperCase();
			if (v == null) {
				redis.delete(strKey);
			} else {
				redis.setObject(strKey, v);
			}
		} catch (Exception e) {
			log.error("", e);
		}
	}

	/**
	 * 清除移动端所有redis缓存（慎用）
	 */
	@Override
	public void clearCachedAll() {
		log.info("正在清除移动端缓存。。。");
		// 清除所有，不考虑多租户
		Collection<String> keys = redis.keys(READ_KEY + "*");
		redis.delete(keys);
		log.info("清除移动端缓存完毕！！！");
	}

	/**
	 * 清除移动端消息跳转地址redis缓存（慎用）
	 * 
	 * @param modulecode 模块编码(可为空，为空时清除跳转地址所有缓存) 例如：system
	 * @param funcode    功能编码(可为空，为空时清除模块下数据) 例如：tds_query
	 */
	@Override
	public void clearCached_MsgUrl(String modulecode, String funcode) {
		if (StringUtils.isNotEmpty(modulecode)) {
			if (StringUtils.isNotEmpty(funcode)) {// 清除某模块下某功能
				this.setRedis_MobileMsgUrl(modulecode, funcode, null);
			} else {// 清除模块下数据
				StringBuffer sbuf = new StringBuffer();
				sbuf.append(this.getReadKey_MsgUrl());
				sbuf.append(":");
				sbuf.append(modulecode);
				String strKey = sbuf.toString().toUpperCase();
				Collection<String> keys = redis.keys(strKey + "*");
				redis.delete(keys);
			}
		} else {// 清除跳转地址所有缓存
			Collection<String> keys = redis.keys(this.getReadKey_MsgUrl() + "*");
			redis.delete(keys);
		}
	}

	/**
	 * 获取移动端消息跳转地址列表（读取redis缓存）
	 * 
	 * @param modulecode 模块编码 例如：system
	 * @param funcode    功能编码 例如：tds_query
	 * @return
	 */
	@Override
	public MobileMsgUrl getMobileMsgUrl(String modulecode, String funcode) {
		MobileMsgUrl v = null;
		if (StringUtil.isNotEmpty(modulecode) && StringUtil.isNotEmpty(funcode)) {
			// 先从redis读取
			MobileMsgUrl vtemp = this.getRedis_MobileMsgUrl(modulecode, funcode);
			if (vtemp != null) {
				v = vtemp;
			} else {// 从数据库读取
				List<MobileMsgUrl> list = this.getMobileMsgUrl(true, modulecode, funcode);
				if (list != null && list.size() > 0) {
					v = list.get(0);
					this.setRedis_MobileMsgUrl(modulecode, funcode, v);
				}
			}
		}
		return v;
	}

	/**
	 * 获取移动端消息跳转地址列表（不读取redis）
	 * 
	 * @param showAll    true:是否显示所有 ;false :只显示used=1
	 * @param modulecode 模块编码 例如：system
	 * @param funcode    功能编码 例如：tds_query
	 * @return
	 */
	@Override
	public List<MobileMsgUrl> getMobileMsgUrl(boolean showAll, String modulecode, String funcode) {
		Where where = Where.create();

		if (!showAll) {
			where.eq(MobileMsgUrl::getTmused, 1);
		}

		if (StringUtils.isNotEmpty(modulecode)) {
			where.eq(MobileMsgUrl::getModulecode, modulecode);
		}

		if (StringUtils.isNotEmpty(funcode)) {
			where.eq(MobileMsgUrl::getFuncode, funcode);
		}

		Order order = Order.create();
		order.orderByAsc(MobileMsgUrl::getModulecode);
		order.orderByAsc(MobileMsgUrl::getFuncode);
		order.orderByAsc(MobileMsgUrl::getTmsort);
		return entityService.queryList(MobileMsgUrl.class, where, order);
	}

	/**
	 * 添加移动端消息跳转地址
	 * 
	 * @param list
	 * @return
	 */
	private boolean insertMobileMsgUrl(List<MobileMsgUrl> list) {
		int i = entityService.insertBatch(list);
		boolean bln = i > 0 ? true : false;
		if (bln) {
			this.clearCached_MsgUrl(null, null);// 清除缓存
		}
		return bln;
	}

	/**
	 * 修改移动端消息跳转地址
	 * 
	 * @param list
	 * @return
	 */
	private boolean updateMobileMsgUrl(List<MobileMsgUrl> list) {
		int i = entityService.updateBatch(list);
		boolean bln = i > 0 ? true : false;
		if (bln) {
			this.clearCached_MsgUrl(null, null);// 清除缓存
		}
		return bln;
	}

	/**
	 * 删除移动端消息跳转地址
	 * 
	 * @param list
	 * @return
	 */
	private boolean deleteMobileMsgUrl(List<MobileMsgUrl> list) {
		int i = entityService.deleteByIdBatch(list);
		boolean bln = i > 0 ? true : false;
		if (bln) {
			this.clearCached_MsgUrl(null, null);// 清除缓存
		}
		return bln;
	}

}
