package com.yunhesoft.system.mobileSys.service;

import java.util.List;
import java.util.Map;

import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.mobileSys.entity.dto.MobileWxSendSaveDto;
import com.yunhesoft.system.mobileSys.entity.po.MobileWxSend;
import com.yunhesoft.system.mobileSys.entity.po.MobileWxSendGroup;
import com.yunhesoft.system.mobileSys.entity.po.MobileWxSendGroupEmp;
import com.yunhesoft.system.mobileSys.entity.vo.TaskWeixinDataVo;

public interface IWeixinSendService {

	/**
	 * 获取表格数据
	 * 
	 * @param pageInfo
	 * @return
	 */
	List<TaskWeixinDataVo> getWxSendList(String queryStr, Pagination<?> pageInfo);

	/**
	 * 获取发送基础信息
	 * 
	 * @param id
	 * @return
	 */
	MobileWxSend getMobileWxSendById(String id);
	
	/**
	 * 添加数据
	 * 
	 * @param dto
	 * @return
	 */
	String insertData(MobileWxSendSaveDto dto);

	/**
	 * 修改数据
	 * 
	 * @param dto
	 * @return
	 */
	String updateData(MobileWxSendSaveDto dto);

	/**
	 * 批量修改状态
	 * 
	 * @param idList
	 * @param used   1：使用；0：停用
	 * @return
	 */
	// String updateData(List<String> idList, int used);

	/**
	 * 删除单条数据
	 * 
	 * @param id
	 * @return
	 */
	String deleteData(MobileWxSendSaveDto dto);

	/**
	 * 批量删除
	 * 
	 * @param idList
	 * @return
	 */
	String deleteData(List<String> idList);

	/**
	 * 自动发送消息
	 * 
	 * @param dataId        (数据ID列表:ZZSQB42F07PORY8HEB,ZZSQB42F07PORY8HEC):
	 * 
	 * @param isHasDataSend 无数据时是否推送：true：有数据才推送 flase：无数据也推送
	 * 
	 * @return
	 */
	// boolean autoSendMsg(List<String> idList, boolean isHasDataSend);

	/**
	 * 发送消息
	 * 
	 * @param id
	 * @return
	 */
	boolean execSendMsg(String id) throws Exception;

	/**
	 * 启动任务
	 * 
	 * @param id
	 * @return
	 */
	String startJob(String id);

	/**
	 * 注销任务
	 * 
	 * @param id
	 */
	void endJob(String id);

	/**
	 * 启动所有任务
	 * 
	 * @return
	 */
	void startAllJob();

	/**
	 * 获取小组人员列表
	 * 
	 * @param groupId
	 * @return
	 */
	List<Map<String,String>> getMobileWxSendGroupEmpList(String groupId);

	/**
	 * 添加小组人员列表
	 * 
	 * @param list
	 * @return
	 */

	boolean insertMobileWxSendGroupEmpList(String groupId, List<String> empList);

	/**
	 * 更新小组人员列表
	 * 
	 * @param list
	 * @return
	 */
	boolean updateMobileWxSendGroupEmpList(String groupId, List<String> empList);

	/**
	 * 删除分组信息
	 * 
	 * @param list
	 * @return
	 */
	boolean deleteMobileWxSendGroup(String id);

	List<MobileWxSendGroup> getMobileWxSendGroupList();
	
	/**
	 * 添加分组信息
	 * 
	 * @param list
	 * @return
	 */
	boolean insertMobileWxSendGroup(MobileWxSendGroup bean);

	/**
	 * 更新分组信息
	 * 
	 * @param list
	 * @return
	 */
	boolean updateMobileWxSendGrou(MobileWxSendGroup bean);

	List<MobileWxSendGroupEmp> getMobileWxSendGroupEmpList(List<String> groupIdList);
	/**
	 * 删除分组人员
	 * 
	 * @param list
	 * @return
	 */
	boolean deleteMobileWxSendGroupEmpList(String groupId, String empId);

}
