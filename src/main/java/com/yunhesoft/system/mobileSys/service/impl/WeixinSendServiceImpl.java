package com.yunhesoft.system.mobileSys.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yunhesoft.core.common.script.ScriptEngineUtils;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.mobileSys.entity.dto.MobileWxSendSaveDto;
import com.yunhesoft.system.mobileSys.entity.po.MobileWxSend;
import com.yunhesoft.system.mobileSys.entity.po.MobileWxSendGroup;
import com.yunhesoft.system.mobileSys.entity.po.MobileWxSendGroupEmp;
import com.yunhesoft.system.mobileSys.entity.po.MobileWxSendObj;
import com.yunhesoft.system.mobileSys.entity.vo.TaskWeixinDataVo;
import com.yunhesoft.system.mobileSys.service.IWeixinSendService;
import com.yunhesoft.system.mobileSys.task.ITaskWeixinSendService;
import com.yunhesoft.system.mobileSys.util.MobileUtil;
import com.yunhesoft.system.msg.entity.dto.MsgObject;
import com.yunhesoft.system.msg.entity.dto.MsgObjectParamWeiXin;
import com.yunhesoft.system.msg.entity.dto.MsgObjectRet;
import com.yunhesoft.system.msg.service.IMessageService;
import com.yunhesoft.system.tds.model.IDataSource;
import com.yunhesoft.system.tds.model.TDataSourceManager;
import com.yunhesoft.system.tds.model.TInPara;
import com.yunhesoft.system.tools.calendar.service.CalendarService;
import com.yunhesoft.system.tools.eval.CompiledScriptEngine;
import com.yunhesoft.system.tools.eval.model.CustomFun;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class WeixinSendServiceImpl implements IWeixinSendService {

    private static boolean isDebug = true;

    @Autowired
    private EntityService dao;

    @Autowired
    private RedisUtil redis;

    @Autowired
    private ITaskWeixinSendService taskServ;

    @Autowired
    private ISysEmployeeInfoService empServ; // 人员信息

    /**
     * 发送消息服务对象
     */
    @Autowired
    IMessageService msgSer;

    private static String REDKEY = "SYSTEM_MOBILE:WEIXINSENDINFO";

    // 预编译脚本解析引擎
    private static ConcurrentHashMap<String, CompiledScriptEngine> evalMap = new ConcurrentHashMap<String, CompiledScriptEngine>();

    /**
     * 日历服务
     */
    @Autowired
    CalendarService calendarSer;

    // 调度任务是否启动
    @Value("${app.auto.sendmsg:false}")
    private boolean enable = false;

    private boolean isEnable() {
        if (!enable) {
            log.error("微信发送调度功能未启动，请修改配置文件[app.auto.sendmsg=true]");
        }
        return enable;
    }

    @Override
    public boolean execSendMsg(String id) throws Exception {
        log.info("发送微信消息：" + id);
        MobileWxSend bean = this.getMobileWxSendById(id);
        if (bean != null && bean.getTmUsed() == 1) {
            boolean bln = this.autoSendMsg(bean, true); // 发送消息
            return bln;
        } else {
            return false;
        }
    }

    /**
     * 启动所有任务
     *
     * @return
     */
    @Override
    public void startAllJob() {
        String info = "";
        if (isEnable()) {
            List<MobileWxSend> dataList = this.getWxSendList();
            if (dataList != null) {
                for (MobileWxSend e : dataList) {
                    info = this.startJob(e);
                    if (StringUtils.isEmpty(info)) {
                        this.setRedis(e);
                    } else {
                        log.error("任务启动失败(ID:" + e.getId() + ")," + info);
                    }
                }
            }
        }
    }

    /**
     * 判断任务是否运行
     *
     * @param id
     * @return
     */

    private boolean isJobRunning(String id) {
        return taskServ.isJobRunning(id);
    }

    /**
     * 启动任务
     *
     * @param id
     * @return
     */
    @Override
    public String startJob(String id) {
        String info = "";
        MobileWxSend bean = this.getMobileWxSendById(id);
        if (bean == null) {
            info = "未检索到数据（ID:" + id + "）";
            return info;
        }
        if (StringUtils.isEmpty(bean.getTimeTemplet())) {
            info = "时间表达式为空（ID:" + id + "）";
            return info;
        }
        info = this.startJob(bean);
        return info;
    }

    /**
     * 微信发送任务启动
     *
     * @param bean
     * @return
     */
    private String startJob(MobileWxSend bean) {
        String info = "";
        if (isEnable()) {
            String id = bean.getId();
            if (this.isJobRunning(id)) {
                this.endJob(id);
            }
            info = taskServ.addJob(id, bean.getTimeTemplet());
        } else {
            info = "调度功能未启动，请在配置文件中配置[app.auto.sendmsg=true]！";
        }
        return info;
    }

    /**
     * 注销任务
     *
     * @param id
     */
    @Override
    public void endJob(String id) {
        try {
            taskServ.removeJob(id);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    /**
     * 添加数据
     */
    @Override
    public String insertData(MobileWxSendSaveDto dto) {
        String info = "";
        try {
            MobileWxSend bean = ObjUtils.copyTo(dto, MobileWxSend.class);
            bean.setId(TMUID.getUID());
            int i = dao.insert(bean);
            if (i > 0) {// 开始保存子记录
                this.saveSendObj(bean.getId(), dto.getEmpList(), dto.getGroupList());
            }
            this.clearCachedAll();// 清除缓存
        } catch (Exception e) {
            log.error("", e);
            info = e.getMessage();
        }
        return info;
    }

    @Override
    public String updateData(MobileWxSendSaveDto dto) {
        String info = "";
        try {
            MobileWxSend bean = ObjUtils.copyTo(dto, MobileWxSend.class);
            int i = dao.rawUpdateById(bean);
            if (i > 0) {// 开始保存子记录
                this.saveSendObj(bean.getId(), dto.getEmpList(), dto.getGroupList());
            }
            this.clearCachedAll();// 清除缓存
        } catch (Exception e) {
            log.error("", e);
            info = e.getMessage();
        }
        return info;
    }

    /**
     * 批量更新状态
     */
//	@Override
//	public String updateData(List<String> idList, int used) {
//		String info = "";
//		try {
//			if (StringUtils.isNotEmpty(idList)) {
//				List<MobileWxSend> list = new ArrayList<MobileWxSend>();
//				for (String id : idList) {
//					MobileWxSend bean = new MobileWxSend();
//					bean.setId(id);
//					bean.setTmUsed(used);
//				}
//				String[] cols = { "TMUSED" };
//				dao.rawUpdateByIdBatch(list, cols);
//			}
//		} catch (Exception e) {
//			log.error("", e);
//			info = e.getMessage();
//		}
//		return info;
//	}

    /**
     * 删除数据
     *
     * @param dto
     * @return
     */
    @Override
    public String deleteData(MobileWxSendSaveDto dto) {
        String info = "";
        try {
            MobileWxSend bean = ObjUtils.copyTo(dto, MobileWxSend.class);
            int i = dao.rawDeleteById(bean);
            if (i > 0) {// 删除子表数据
                this.deleteSendObj(bean.getId());
            }
            this.clearCachedAll();// 清除缓存
        } catch (Exception e) {
            log.error("", e);
            info = e.getMessage();
        }
        return info;
    }

    /**
     * 批量删除数据
     */
    @Override
    public String deleteData(List<String> idList) {
        String info = "";
        try {
            if (StringUtils.isNotEmpty(idList)) {
                List<MobileWxSend> list = new ArrayList<MobileWxSend>();
                for (String id : idList) {
                    MobileWxSend bean = new MobileWxSend();
                    bean.setId(id);
                    list.add(bean);
                }
                int i = dao.deleteByIdBatch(list);
                if (i > 0) {// 删除子表数据
                    this.deleteSendObj(idList);
                }
            }
            this.clearCachedAll();// 清除缓存
        } catch (Exception e) {
            log.error("", e);
            info = e.getMessage();
        }
        return info;
    }

    private void deleteSendObj(List<String> pidList) {
        dao.deleteIn(MobileWxSendObj.class, MobileWxSendObj::getPid, pidList.toArray());
        this.clearCachedAll();// 清除缓存
    }

    /**
     * 自动发送消息
     *
     * @param staticData    true:静态数据；false:动态数据
     * @param dataId        (数据ID列表:ZZSQB42F07PORY8HEB,ZZSQB42F07PORY8HEC):tm3://message/
     *                      msgSetModuleWeixin
     *                      .jsp微信消息推送配置页面的[数据ID]列(表：msg_setModule_weixin.tmuid)
     * @param orgDm         (机构代码):目前人员动态
     * @param isAllSend     (机构代码): isAllSend:是否发送所有数据 true：used=0 的数据也推送
     * @param isHasDataSend 无数据时是否推送：true：有数据才推送 flase：无数据也推送
     * @param isSingleSend  是否单条推送： true：单条数据推送（模式1：数据源每条记录要包含人员ID列表，消息内容等信息。<br/>
     *                      模式2：数据源每条记录要包含消息内容等信息，但是不包含人员ID列表，可通过[配置]列，设置接收人员及岗位等信息。<br/>
     *                      *两种模式支持短信与微信推送，但【接收人员】列无效。） false：整体数据推送（短信模式不支持此模式）
     * @return
     */

    // @Override
//	public boolean autoSendMsg(List<String> idList, boolean isHasDataSend) {
//		return autoSendMsg(idList, isHasDataSend, false);
//	}

    /**
     * 自动发送消息
     *
     * @param temp          MobileWxSend 配置
     * @param isHasDataSend 无数据时是否推送：true：有数据才推送 flase：无数据也推送
     * @return
     */
    private boolean autoSendMsg(MobileWxSend temp, boolean isHasDataSend) throws Exception {
        if (temp == null) {
            return false;
        }
        boolean result = false;
        boolean isSingleSend = false;

        // @param isSingleSend 是否单条推送：
        // true：单条数据推送
        // 模式1：数据源每条记录要包含人员ID列表，消息内容等信息。<br/>
        // 模式2：数据源每条记录要包含消息内容等信息，但是不包含人员ID列表，可通过[配置]列，设置接收人员及岗位等信息。<br/>
        // *两种模式支持短信与微信推送，但【接收人员】列无效。
        // false：整体数据推送（短信模式不支持此模式）
        if (temp.getSingleSend() != null && temp.getSingleSend() == 1) {
            isSingleSend = true;
        }

        Date nowDt = new Date();
        // 判断是否需要关联日历
        Integer isLinkCalendar = temp.getIsLinkCalendar();
        if (isLinkCalendar != null && isLinkCalendar == 1) {
            // 判断是否为节假日
            if (this.isHolidays(nowDt)) {// 是节假日
                if (isDebug) {
                    log.info("【消息推送】[" + DateTimeUtils.format(nowDt, "yyyy-MM-dd HH:mm:ss") + "]是休息日，不发消息。");
                }
                return false;
                // continue;// 跳出当前消息推送
            }
        }

        TDataSourceManager tsm = new TDataSourceManager();
        List<String> sendList = this.getSendList(temp); // 发送人员列表

        // 判断是否有接收人
        if (StringUtils.isNotEmpty(sendList)) {
            // receptionType 发送消息模式：1：系统消息2：微信消息3：短信消息
            Integer receptionType = temp.getReceptionType();
            if (receptionType == null) {
                receptionType = 2;
            }
            // 判断调试模式
            if (isDebug) {
                try {
                    // 打印配置项
                    System.out.println(DateTimeUtils.getNowDateTimeStr() + ":WeixinSendServiceImpl.autoSendMsg.消息推送配置项:"
                            + JSON.toJSONString(temp));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            Integer sendType = temp.getSendType();// 1：数据源，2：超链接
            if (sendType == null) {
                sendType = 1;
            }
            String msgType = temp.getMsgType();
            if (sendType == 2) {// 推送消息类型：1：数据源，2：超链接
                String msgTitle = temp.getMsgTitle();// 标题
                String msgContent = temp.getMsgContent();// 发送内容
                String webHttp = temp.getWebHttp();// web地址
                try {
                    msgTitle = this.getScriptValue(msgTitle);// this.tdsExec.getScriptValue(descText).toString();
                } catch (Exception ex1) {
                    log.error("", ex1);
                }
                try {
                    msgContent = this.getScriptValue(msgContent);// this.tdsExec.getScriptValue(msgContent).toString();
                } catch (Exception ex1) {
                    log.error("", ex1);
                }
                String params = temp.getUrlParams();// temp.getUrlparams();
                // 参数支持变量 by x.zhong 2024.11.7
                if (StringUtils.isNotEmpty(params)) {
                    try {
                        params = this.getScriptValue(params);// this.tdsExec.getScriptValue(title).toString();
                    } catch (Exception ex) {
                        log.error("", ex);
                    }
                } else {
                    if ("".equals(params)) {
                        params = null;
                    }
                }
                String appUrl = this.getAppUrl(webHttp, params);
                // 发送消息（超链接模式）
                this.sendMsgLink(receptionType, msgType, sendList, msgContent, msgTitle, appUrl);
            } else if (sendType == 1) {// 1：数据源
                // 微信推送数据源默认参数
                String msg_inpara = temp.getInparaAlias();
                if (msg_inpara != null && msg_inpara.startsWith("\"")) {
                    try {
                        msg_inpara = this.getScriptValue(msg_inpara);// this.tdsExec.getScriptValue(msg_inpara).toString();
                        msg_inpara = msg_inpara.replaceAll("==", "=");
                    } catch (Exception localException3) {
                        msg_inpara = temp.getInparaAlias();
                    }
                }
                IDataSource ids = getDs(tsm, temp.getTdsAlias(), msg_inpara);
                boolean isSend = true;
                if (isHasDataSend) {// 无数据时是否推送：true：有数据才推送 flase：无数据也推送
                    if ((ids != null) && (ids.getRowCount() > 0)) {
                        isSend = true;
                    } else {
                        isSend = false;
                    }
                } else {
                    isSend = true;
                }
                if (isSend) {
                    String inParaAlias = "";

                    Map<String, String> map = new HashMap<String, String>();
                    // 附加参数
                    if ((temp.getUrlParams() != null) && (temp.getUrlParams().length() > 0)) {
                        String[] aryStr = temp.getUrlParams().split("\\&");
                        for (int i = 0; i < aryStr.length; i++) {
                            String[] aryPara = (aryStr[i] + "=a").split("=");
                            if ((aryPara[0] != null) && (aryPara[0].length() > 0)) {
                                if ("null".equalsIgnoreCase(aryPara[1])) {
                                    map.put(aryPara[0], "NULL");
                                } else {
                                    map.put(aryPara[0], aryPara[1]);
                                }
                            }
                        }
                    }
                    List<TInPara> inList = ids.getInParaList();
                    for (TInPara item : inList) {
                        String itemAlias = item.getParaAlias();
                        Object itemValue = item.getValue();
                        if (map.get(itemAlias) != null) {
                            // 进行参数的替换
                            if ("NULL".equalsIgnoreCase(map.get(itemAlias))) {// 移除这个参数
                                continue;
                            } else {// 替换参数值
                                itemValue = map.get(itemAlias);
                            }
                        }
                        if ((itemValue != null) && (!"".equals(itemValue))) {
                            inParaAlias = inParaAlias + "|" + itemAlias + "=" + itemValue;
                        }
                    }
                    if ((inParaAlias != null) && (!"".equals(inParaAlias))) {
                        inParaAlias = inParaAlias.substring(1);
                    }
                    String descText = temp.getMsgTitle();// temp.getDescText();
                    String title = (descText != null) && (!"".equals(descText)) ? descText : ids.getDSName();
                    try {
                        title = this.getScriptValue(title);// this.tdsExec.getScriptValue(title).toString();
                    } catch (Exception ex) {
                        log.error("", ex);
                    }

                    // 获取固定接收人
//					String wx_zyid = temp.getSendToUserIds();// 接收人
//					List<Long> listZyid = new ArrayList<Long>();
//					if (wx_zyid != null && wx_zyid.length() > 0) {
//						String[] ary_zyid = wx_zyid.split(",");
//						for (String s : ary_zyid) {
//							listZyid.add(Long.parseLong(s));
//						}
//					}
                    // 根据数据动态生成接收人================================================================
                    if (ids != null && temp.getTdsZyid() != null && temp.getTdsZyid().length() > 0) {

                        String colCfgInfo = temp.getTdsZyid();
                        // 如果为空默认值为[按顺序]：zyids(接收人员),msgInfo(消息内容)
                        String zyids = "zyids";// (接收人员)
                        if (colCfgInfo != null && !"".equals(colCfgInfo.trim())) {
                            colCfgInfo = colCfgInfo.trim();
                            String[] arrColCfgInfo = colCfgInfo.split(",");
                            // 取接收人员列名称
                            if (arrColCfgInfo != null && arrColCfgInfo.length > 0) {
                                zyids = arrColCfgInfo[0];
                            }
                        }
                        for (int n = 0; n < ids.getRowCount(); n++) {
                            String s = ids.gets(n, zyids);
                            if (s != null && s.length() > 0) {
                                s = s.trim();
                                if (s.startsWith(",")) {
                                    s = s.substring(1);
                                }
                                if (s.endsWith(",")) {
                                    s = s.substring(0, s.length() - 1);
                                }
                                if (s.length() > 0) {
                                    String[] ary_zyid = s.split(",");
                                    for (String s1 : ary_zyid) {
                                        // long id = Long.parseLong(s1);
                                        if (!sendList.contains(s1)) {
                                            sendList.add(s1);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // wx_zyid = listZyid.toString().replaceAll(" ", "");
                    // wx_zyid = wx_zyid.substring(1, wx_zyid.length() - 1);
                    // System.out.println("zyids:"+wx_zyid+"json:"+json);
                    // ======================================================================================
                    // 判断是否有接收人
                    if (StringUtils.isNotEmpty(sendList)) {
                        // if (wx_zyid != null && wx_zyid.length() > 0) {
                        // String wx_zyxm = temp.getSendToUserNames();

                        // 判断是否静态数据

                        String json = "\"tdsAlias\":\"" + ids.getDSAlias() + "\",\"inParaAlias\":\"" + inParaAlias
                                + "\"";
                        String paras = "";
                        if ((temp.getUrlParams() != null) && (temp.getUrlParams().length() > 0)) {
                            String[] aryStr = temp.getUrlParams().split("\\&");
                            for (int i = 0; i < aryStr.length; i++) {
                                String[] aryPara = (aryStr[i] + "=a").split("=");
                                if ((aryPara[0] != null) && (aryPara[0].length() > 0)) {
                                    if ("null".equals(aryPara[1])) {
                                        continue;
                                    }
                                    paras = paras + "\"" + aryPara[0] + "\":\"" + aryPara[1] + "\",";
                                }
                            }
                            json = paras + json;
                        }
                        json = "{" + json + "}";
                        // System.out.println(json);

                        // 是否单条推送： true单条推送（每条记录里要包含人员ID列表，消息内容等信息） false：整体数据推送
                        if (isSingleSend) {
                            // 单条推送
                            // 判断调试模式
                            if (isDebug) {
                                try {
                                    System.out.println(DateTimeUtils.getNowDateTimeStr()
                                            + ":WeixinSendServiceImpl.autoSendMsg.【单条推送模式】");
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                }
                            }

                            // 判断如果附加参数有值，使用附加参数查询数据源（保留原有逻辑）
                            if (inParaAlias != null && !"".equals(inParaAlias)) {
                                ids = getDs(tsm, temp.getTdsAlias(), inParaAlias);
                            }

                            // 逐条发送消息
                            this.sendSingleMsg(ids, receptionType, msgType, title, temp);

                        } else {
                            // 判断调试模式
                            if (isDebug) {
                                try {
                                    System.out.println(DateTimeUtils.getNowDateTimeStr()
                                            + ":WeixinSendServiceImpl.autoSendMsg.【整体推送模式】");
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                }
                            }

                            // receptionType 发送消息模式：1：系统消息2：微信消息3：短信消息
                            if (receptionType != null && receptionType == 2) {// 为了保留原有逻辑，此处代码不变
                                // 微信消息
                                this.sendMsg(receptionType, msgType, sendList, null, title, "system", "tds_query",
                                        json);

                                // 判断调试模式
                                if (isDebug) {
                                    try {
                                        System.out.println(DateTimeUtils.getNowDateTimeStr()
                                                + ":WeixinSendServiceImpl.autoSendMsg.发送【微信】:[接收人]:"
                                                + sendList.toString() + ",[调用方法]WeiXinSDK.sendMsgWeiXin");
                                    } catch (Exception ex) {
                                        ex.printStackTrace();
                                    }
                                }

                            } else {
                                // 发送短信或其它消息

                                // 获取消息内容
                                // String msgContent = "暂时未开发，短信或其它消息，整体数据推送模式！";
                                // this.sendMsgEx(receptionType, msgType, wx_zyid, msgContent, title, null);
                            }
                        }

                    }
                }
            }

        }

        return result;
    }

    /**
     * 发送消息(超链接消息)
     *
     * @param receptionType 发送消息模式：1：系统消息2：微信消息3：短信消息
     * @param msgType       消息类型(*微信生效)(目前只支持前两种[text],[textcard]) <br>
     *                      类型列表：[text],[textcard],[image],[voice],[file],[news],[mpnews]
     * @param sendList      人员ID 多个使用逗号分隔
     * @param msgContent    内容
     * @param msgTitle      标题
     * @param skipUrl       跳转地址
     */
    private void sendMsgLink(int receptionType, String msgType, List<String> sendList, String msgContent,
                             String msgTitle, String appUrl) {
//		log.info("发送消息，msgType:" + msgType + ",title:" + descText + ",msgContent:" + msgContent + ",zyid:"
//				+ sendList.toString());

        MsgObject msgObje = new MsgObject();
        msgObje.setTitle(msgTitle);
        msgObje.setContent(msgContent);
        msgObje.setRecvEmpIds(String.join(",", sendList));
        msgObje.setMsgType(String.valueOf(receptionType));
        msgObje.setUrl(appUrl);

        // 微信消息参数
        MsgObjectParamWeiXin weixinParam = new MsgObjectParamWeiXin();
        weixinParam.setMsgType(msgType);
        msgObje.setParamWeiXin(weixinParam);

        MsgObjectRet retObj = msgSer.sendMsg(msgObje);
    }

    /**
     * 发送消息(按功能模块发消息)
     *
     * @param receptionType 发送消息模式：1：系统消息2：微信消息3：短信消息
     * @param msgType       消息类型(*微信生效)(目前只支持前两种[text],[textcard]) <br>
     *                      类型列表：[text],[textcard],[image],[voice],[file],[news],[mpnews]
     * @param sendList      人员ID 多个使用逗号分隔
     * @param msgContent    内容
     * @param msgTitle      标题
     * @param modulecode    模板编码(必填) 例如：system
     * @param funcode       功能编码(必填) 例如：tds_query
     * @param params        消息参数
     */
    private void sendMsg(int receptionType, String msgType, List<String> sendList, String msgContent, String msgTitle,
                         String modulecode, String funcode, String params) {
//		log.info("发送消息，msgType:" + msgType + ",title:" + descText + ",msgContent:" + msgContent + ",zyid:"
//				+ sendList.toString());

        MsgObject msgObje = new MsgObject();
        msgObje.setSendEmpId("0");// 系统消息
        msgObje.setTitle(msgTitle);
        msgObje.setContent(msgContent);
        msgObje.setRecvEmpIds(String.join(",", sendList));
        msgObje.setMsgType(String.valueOf(receptionType));
        msgObje.setModulecode(modulecode);
        msgObje.setFuncode(funcode);
        msgObje.setParams(params);

        // 微信消息参数
        MsgObjectParamWeiXin weixinParam = new MsgObjectParamWeiXin();
        weixinParam.setMsgType(msgType);
        msgObje.setParamWeiXin(weixinParam);

        MsgObjectRet retObj = msgSer.sendMsg(msgObje);
    }

    /**
     * 将传入的APP地址转换成相对路径
     *
     * @param {} inUrl url地址(格式为：app://....)
     * @param {} param 附加参数(不需要增加 & 或 ? 等关键字)
     * @return {}
     */
    private String getAppUrl(String inUrl, String params) {
        return MobileUtil.getAppUrl(inUrl, params);
    }

    /**
     * 脚本解析
     *
     * @param script
     * @return
     */
    private String getScriptValue(String script) {
        String value = script;
        try {
            if (StringUtils.isNotEmpty(value)) {
                String evalKey = ScriptEngineUtils.getMd5(value);
                CompiledScriptEngine cse = evalMap.get(evalKey);
                if (cse == null) {
                    String calScript = CustomFun.getCuntomUsedFunScript(script) + " " + value;
                    cse = new CompiledScriptEngine(calScript);
                    if (cse.getScriptException() == null) {
                        evalMap.put(evalKey, cse);
                    } else {// 脚本编译错误
                        return value;
                    }
                }
                Object result = cse.eval();
                if (result != null) {
                    value = result.toString();
                }
            }
        } catch (Exception e) {
            log.error(script, e);
        }
        return value;
    }

    /**
     * 获取发送人员列表
     *
     * @return
     */
    private List<String> getSendList(MobileWxSend bean) {
        List<String> rtnList = new ArrayList<String>();
        Map<String, List<String>> map = this.getSendObjMap(bean.getId());
        List<String> empList = map.get("emp");
        if (StringUtils.isNotEmpty(empList)) {
            rtnList.addAll(empList);
        }
        List<String> groupList = map.get("group");
        if (StringUtils.isNotEmpty(groupList)) {// 获取小组的所有成员
            this.getSendListByGroupId(rtnList, groupList);
        }
        Map<String, List<String>> mapName = this.getSendNameList(rtnList);// 获得人名，并过滤离职人员
        return mapName.get("id");
    }

    /**
     * 获取人员姓名
     *
     * @param empList
     * @return
     */
    private Map<String, List<String>> getSendNameList(List<String> empList) {
        Map<String, List<String>> map = new HashMap<String, List<String>>();
        List<String> idList = new ArrayList<String>();
        List<String> nameList = new ArrayList<String>();
        if (StringUtils.isNotEmpty(empList)) {
            for (String empid : empList) {
                SysEmployeeInfo bean = empServ.findEmployeeById(empid);
                if (bean != null && bean.getUsed() == 1 && bean.getStatus() == 1) {// 在职
                    idList.add(empid);
                    nameList.add(bean.getEmpname());
                }
            }
        }
        map.put("id", idList);
        map.put("name", nameList);
        return map;
    }

    /**
     * 根据ID获取发送人列表
     *
     * @param sendList
     * @param groupList
     */
    private void getSendListByGroupId(List<String> sendList, List<String> groupList) {
        if (sendList == null) {
            sendList = new ArrayList<String>();
        }

        List<MobileWxSendGroupEmp> list = this.getMobileWxSendGroupEmpList(groupList);
        if (StringUtils.isNotEmpty(list)) {
            for (MobileWxSendGroupEmp e : list) {
                if (!sendList.contains(e.getEmpid())) {
                    sendList.add(e.getEmpid());
                }
            }
        }
    }

    /**
     * 获取接收对象信息
     *
     * @param pid 主表ID
     * @return
     */
    private Map<String, List<String>> getSendObjMap(String pid) {
        Map<String, List<String>> map = new HashMap<String, List<String>>();
        List<MobileWxSendObj> list = this.getSendObjList(pid);
        if (StringUtils.isNotEmpty(list)) {
            List<String> empList = new ArrayList<String>();
            List<String> groupList = new ArrayList<String>();
            for (MobileWxSendObj e : list) {
                String id = e.getObjId();
                if (e.getObjType() == null || e.getObjType() == 0) {// 人员
                    if (!empList.contains(id)) {
                        empList.add(id);
                    }
                } else {// 分组
                    if (!groupList.contains(id)) {
                        groupList.add(id);
                    }
                }
            }
            map.put("emp", empList);
            map.put("group", groupList);
        }
        return map;
    }

    /**
     * 获取消息接收对象
     *
     * @param pid
     * @return
     */
    private List<MobileWxSendObj> getSendObjList(String pid) {
        Where where = Where.create();
        where.eq(MobileWxSendObj::getPid, pid);
        List<MobileWxSendObj> list = dao.queryList(MobileWxSendObj.class, where);
        return list;
    }

    private boolean deleteSendObj(String pid) {
        Where where = Where.create();
        where.eq(MobileWxSendObj::getPid, pid);
        dao.delete(MobileWxSendObj.class, where);
        return true;
    }

    /**
     * 保存发送人子数据
     *
     * @param pid
     * @param empList     人员ID
     * @param groupIdList 分组id
     */
    private void saveSendObj(String pid, List<String> empList, List<String> groupIdList) {
        if (deleteSendObj(pid)) {
            List<MobileWxSendObj> list = new ArrayList<MobileWxSendObj>();
            if (StringUtils.isNotEmpty(empList)) {
                for (String id : empList) {
                    MobileWxSendObj e = new MobileWxSendObj();
                    e.setId(TMUID.getUID());
                    e.setPid(pid);
                    e.setObjId(id);
                    e.setObjType(0); // 人员
                    list.add(e);
                }
            }
            if (StringUtils.isNotEmpty(groupIdList)) {
                for (String id : groupIdList) {
                    MobileWxSendObj e = new MobileWxSendObj();
                    e.setId(TMUID.getUID());
                    e.setPid(pid);
                    e.setObjId(id);
                    e.setObjType(1); // 分组
                    list.add(e);
                }

            }
            if (list.size() > 0) {
                dao.insertBatch(list);
            }
        }
    }

    /**
     * 获取小组接收人信息
     *
     * @param groupIdList
     * @return
     */
    @Override
    public List<MobileWxSendGroupEmp> getMobileWxSendGroupEmpList(List<String> groupIdList) {
        Where where = Where.create();
        if (groupIdList.size() == 1) {
            where.eq(MobileWxSendGroupEmp::getGroupid, groupIdList.get(0));
        } else {
            where.in(MobileWxSendGroupEmp::getGroupid, groupIdList.toArray());
        }
        List<MobileWxSendGroupEmp> list = dao.queryList(MobileWxSendGroupEmp.class, where);
        return list;
    }

    /**
     * 判断是否为节假日
     *
     * @param nowDt
     * @return
     */
    private boolean isHolidays(Date nowDt) {

//		int n = DateTimeUtils.getWeedNum(nowDt);//只判断周末
//		if (n == 6 || n == 7) {
//			return true;
//		} else {
//			return false;
//		}

        //判断节假日
        return calendarSer.isHolidays(nowDt);
    }

    /**
     * 加载数据源
     *
     * @param tsm
     * @param tdsAlias
     * @param inParaAlias
     * @return
     */
    private IDataSource getDs(TDataSourceManager tsm, String tdsAlias, String inParaAlias) {
        IDataSource result = null;
        if (tsm != null && tdsAlias != null && tdsAlias.length() != 0) {// 参数有效
            result = tsm.getDataSource(tdsAlias, inParaAlias);
            if (!result.getAutoLoad()) {// 自动加载
                result.load();
            }
        }
        return result;
    }

    /**
     * 获取表格数据
     *
     * @param pageInfo
     * @return
     */
    @Override
    public List<TaskWeixinDataVo> getWxSendList(String queryStr, Pagination<?> pageInfo) {
        List<TaskWeixinDataVo> rtnList = new ArrayList<TaskWeixinDataVo>();
        List<MobileWxSend> list = getWxSendList(queryStr, true, pageInfo);
        if (StringUtils.isNotEmpty(list)) {
            for (MobileWxSend e : list) {
                TaskWeixinDataVo vo = ObjUtils.copyTo(e, TaskWeixinDataVo.class);
                if (this.isJobRunning(e.getId())) {
                    vo.setIsRunning(1);// 任务运行中
                } else {
                    vo.setIsRunning(0);
                }
                Map<String, List<String>> map = this.getSendObjMap(e.getId());
                List<String> empList = map.get("emp");
                List<String> groupIdList = map.get("group");
                Map<String, List<String>> mapName = this.getSendNameList(empList);
                vo.setEmpIdList(mapName.get("id"));
                vo.setEmpNameList(mapName.get("name"));
                vo.setGroupIdList(groupIdList);
                rtnList.add(vo);
            }
        }
        return rtnList;
    }

    private List<MobileWxSend> getWxSendList() {
        return getWxSendList(null, false, null);

    }

    private List<MobileWxSend> getWxSendList(String queryStr, boolean showUsed, Pagination<?> pageInfo) {
        Where where = Where.create();
        if (!showUsed) {
            where.eq(MobileWxSend::getTmUsed, 1);
        }
        if (StringUtils.isNotEmpty(queryStr)) {// 模糊检索
            where.like(MobileWxSend::getName, queryStr);
        }
        Order order = Order.create();
        order.orderByDesc(MobileWxSend::getCreateTime);
        List<MobileWxSend> list = dao.queryData(MobileWxSend.class, where, order, pageInfo);
        return list;

    }

    /**
     * 获取发送基础信息
     *
     * @param id
     * @return
     */
    public MobileWxSend getMobileWxSendById(String id) {
        MobileWxSend bean = redis.getMapValue(REDKEY, id, MobileWxSend.class);
        if (bean == null) {
            bean = dao.queryObjectById(MobileWxSend.class, id);
            setRedis(bean);
        }
        return bean;
    }

    private void setRedis(MobileWxSend bean) {
        if (bean != null) {
            redis.setMapValue(REDKEY, bean.getId(), bean);
        }

    }

    public void clearCachedAll() {
        log.info("正在清除移动端缓存。。。");
        // 清除所有，不考虑多租户
        Collection<String> keys = redis.keys(REDKEY + "*");
        redis.delete(keys);
        log.info("清除移动端缓存完毕！！！");
    }

    /**
     * 单条消息推送（按记录逐条发送）
     *
     * @param ids           数据来源
     * @param receptionType 发送消息模式：1：系统消息2：微信消息3：短信消息
     * @param msgType       消息类型(*微信生效)(目前只支持前两种[text],[textcard]) <br>
     *                      类型列表：[text],[textcard],[image],[voice],[file],[news],[mpnews]
     * @param title         标题
     * @param msgCfgBean    消息推送配置Bean
     * @return
     */
    @SuppressWarnings("unused")
    private String sendSingleMsg(IDataSource ids, Integer receptionType, String msgType, String title,
                                 MobileWxSend msgCfgBean) {
        String retValue = "true";
        try {
            // 判断是否按照
            if (ids != null && ids.getRowCount() > 0) {
                String colCfgInfo = msgCfgBean.getTdsZyid();
                // 如果为空默认值为[按顺序]：zyids(接收人员),msgInfo(消息内容),msgTitle(消息标题),msgClass(消息分类),msgErrorType(消息错误类型)
                String zyids = "zyids";// (接收人员)
                String msgInfo = "msgInfo";// (消息内容)
                String msgTitle = "msgTitle";// (消息标题)
                String msgClass = "msgClass";// (消息分类)
                String msgErrorType = "msgErrorType";// (消息错误类型)
                if (colCfgInfo != null && !"".equals(colCfgInfo.trim())) {
                    colCfgInfo = colCfgInfo.trim();
                    String[] arrColCfgInfo = colCfgInfo.split(",");

                    // 取接收人员列名称
                    if (arrColCfgInfo != null && arrColCfgInfo.length > 0) {
                        String strTemp = arrColCfgInfo[0];
                        if (strTemp != null && strTemp.trim().length() > 0) {
                            zyids = strTemp;
                        }
                    }

                    // 取消息内容列名称
                    if (arrColCfgInfo != null && arrColCfgInfo.length > 1) {
                        String strTemp = arrColCfgInfo[1];
                        if (strTemp != null && strTemp.trim().length() > 0) {
                            msgInfo = strTemp;
                        }
                    }

                    // 取消息标题
                    if (arrColCfgInfo != null && arrColCfgInfo.length > 2) {
                        String strTemp = arrColCfgInfo[2];
                        if (strTemp != null && strTemp.trim().length() > 0) {
                            msgTitle = strTemp;
                        }
                    }

                    // 取消息分类
                    if (arrColCfgInfo != null && arrColCfgInfo.length > 3) {
                        String strTemp = arrColCfgInfo[3];
                        if (strTemp != null && strTemp.trim().length() > 0) {
                            msgClass = strTemp;
                        }
                    }

                    // 取消息内错误类型
                    if (arrColCfgInfo != null && arrColCfgInfo.length > 4) {
                        String strTemp = arrColCfgInfo[4];
                        if (strTemp != null && strTemp.trim().length() > 0) {
                            msgErrorType = strTemp;
                        }
                    }

                }
                // 发消息
                for (int i = 0, iCount = ids.getRowCount(); i < iCount; i++) {
                    // 微信消息 或者 发送短信或其它消息
                    try {
                        String zyidsTemp = ids.gets(i, zyids);// 接收消息人员
                        // 获取消息内容
                        String msgContent = ids.gets(i, msgInfo);// 消息内容

                        String idsMsgTitle = ids.gets(i, msgTitle);// 数据源行中的标题
                        // String idsMsgClass = ids.gets(i, msgClass) == null ? "" : ids.gets(i,
                        // msgClass);// 数据源行中的分类
                        // String idsMsgErrorType = ids.gets(i, msgErrorType) == null ? "" : ids.gets(i,
                        // msgErrorType);// 数据源行中错误类型

//						List<String> zyidlist = new ArrayList<String>();
                        // LinkedHashSet<String> zyidlist = new LinkedHashSet<String>();
                        List<String> zyidlist = new ArrayList<String>();
                        List<String> setzylist = new ArrayList<String>();
                        // 根据分类和错误类型匹配的设置中的接收人
//						if (setlist != null && setlist.size() > 0) {
//							for (MsgSetModuleSetorg set : setlist) {
//								String classcode = set.getAppclasscode() == null ? "" : set.getAppclasscode();
//								String errcode = set.getErrcode() == null ? "" : set.getErrcode();
//								String setzyid = set.getUserid() == null ? "": set.getUserid();
//								if (idsMsgClass != null && idsMsgClass.length() > 0 && classcode != null && classcode.length() > 0
//										&& (","+classcode+",").indexOf(","+idsMsgClass+",") < 0) {
//									//数据中和设置中都有分类，且不包含
//									continue;
//								}
//								if (idsMsgErrorType != null && idsMsgErrorType.length() > 0 && errcode != null && errcode.length() > 0
//										&& (","+errcode+",").indexOf(","+idsMsgErrorType+",") < 0) {
//									//数据中和设置中都有错误信息，且不包含
//									continue;
//								}
//								String[] setzyarray = setzyid.split(",");
//								if (setzyarray.length == 0) {
//									continue;
//								}
//								List<String> list = Arrays.asList(setzyarray);
//								setzylist.addAll(list);
//							}
//						}

                        List<String> tdszylist = new ArrayList<String>();
                        // 数据源每行中的接收人
                        if (zyidsTemp != null && zyidsTemp.length() > 0) {
                            String[] zyidArray = zyidsTemp.split(",");
                            tdszylist = Arrays.asList(zyidArray);
                        }

                        // 去重取并集
                        zyidlist.addAll(tdszylist);
                        zyidlist.addAll(setzylist);

                        /*
                         * if (tdszylist.size() == 0) { //数据源结果中没接收人，直接取配置的接收人 zyidlist = setzylist; }
                         * else if (setzylist.size() > 0){ //数据源结果有接收人，并且也配置了接收人，取二者交集
                         * zyidlist.addAll(tdszylist); zyidlist.retainAll(setzylist); }
                         */

                        // String sendZyids = StringUtils.join(zyidlist, ",");

                        String sendTitle = title;
                        if (idsMsgTitle != null && idsMsgTitle.length() > 0) {
                            sendTitle = idsMsgTitle;
                        }
                        // this.sendMsgEx(receptionType, msgType, sendList, msgContent, descText,
                        // appUrl);

                        this.sendMsgLink(receptionType, msgType, zyidlist, msgContent, sendTitle, null);

//						this.sendMsgEx(receptionType, msgType, zyidsTemp,msgContent,title,null);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
        } catch (Exception ex) {
            log.error("", ex);
            retValue = "false";
        }
        return retValue;
    }

    @Override
    public List<Map<String, String>> getMobileWxSendGroupEmpList(String groupId) {
        List<Map<String, String>> rtnList = new ArrayList<Map<String, String>>();
        List<String> groupIdList = new ArrayList<String>();
        groupIdList.add(groupId);
        List<MobileWxSendGroupEmp> dataList = getMobileWxSendGroupEmpList(groupIdList);
        if (StringUtils.isNotEmpty(dataList)) {
            for (MobileWxSendGroupEmp e : dataList) {
                Map<String, String> map = new HashMap<String, String>();
                String empid = e.getEmpid();
                SysEmployeeInfo bean = empServ.findEmployeeById(empid);
                if (bean != null && bean.getUsed() == 1 && bean.getStatus() == 1) {// 在职
                    map.put(empid, bean.getEmpname());
                }
                rtnList.add(map);
            }
        }
        return rtnList;
    }

    @Override
    public boolean insertMobileWxSendGroupEmpList(String groupId, List<String> empList) {
        if (StringUtils.isNotEmpty(empList)) {
            List<MobileWxSendGroupEmp> list = new ArrayList<MobileWxSendGroupEmp>();
            for (String id : empList) {
                MobileWxSendGroupEmp e = new MobileWxSendGroupEmp();
                e.setId(TMUID.getUID());
                e.setGroupid(groupId);
                e.setEmpid(id);
                list.add(e);
            }
            int i = dao.insertBatch(list);
            return i > 0 ? true : false;
        }
        return false;
    }

    @Override
    public boolean updateMobileWxSendGroupEmpList(String groupId, List<String> empList) {
        if (deleteMobileWxSendGroupEmpList(groupId, null)) {
            return this.insertMobileWxSendGroupEmpList(groupId, empList);
        }
        return false;
    }

    @Override
    public boolean deleteMobileWxSendGroupEmpList(String groupId, String empId) {
        Where where = Where.create();
        where.eq(MobileWxSendGroupEmp::getGroupid, groupId);
        if (empId != null) {
            where.eq(MobileWxSendGroupEmp::getEmpid, empId);
        }
        dao.rawDeleteByWhere(MobileWxSendGroupEmp.class, where);
        return true;
    }

    /**
     * 获取分组信息
     *
     * @return
     */
    @Override
    public List<MobileWxSendGroup> getMobileWxSendGroupList() {

        Where where = Where.create();
        where.eq(MobileWxSendGroup::getTmused, 1);

        Order order = Order.create();
        order.orderByAsc(MobileWxSendGroup::getTmsort);
        List<MobileWxSendGroup> list = dao.queryList(MobileWxSendGroup.class, where, order);
        return list;
    }

    /**
     * 添加分组信息
     *
     * @param bean
     * @return
     */
    @Override
    public boolean insertMobileWxSendGroup(MobileWxSendGroup bean) {
        bean.setId(TMUID.getUID());
        int i = dao.insert(bean);
        return i > 0 ? true : false;
    }

    /**
     * 更新分组信息
     *
     * @param bean
     * @return
     */
    @Override
    public boolean updateMobileWxSendGrou(MobileWxSendGroup bean) {
        int i = dao.updateById(bean);
        return i > 0 ? true : false;
    }

    /**
     * 删除分组信息
     *
     * @param id
     * @return
     */
    @Override
    public boolean deleteMobileWxSendGroup(String id) {
        MobileWxSendGroup bean = new MobileWxSendGroup();
        bean.setId(id);
        bean.setTmused(0);
        int i = dao.rawUpdateById(bean);
        return i > 0 ? true : false;
    }

}
