package com.yunhesoft.system.kernel.service.model;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import lombok.Data;

/**
 * @category sql 执行参数
 * <AUTHOR>
 *
 * @since 2022-11-1 8:28:40
 */
@Data
public class ExecParam {
	/** 执行语句 */
	private String sql;

	/** 参数值 */
	List<Object[]> batchArgs = new ArrayList<Object[]>();

	/** 批量执行数量 */
	private Integer batchSize;

	/** 函数参数类型 */
	private int[] argType;

	private List<Field> fields = new ArrayList<Field>();

	private String tableName = null;

}
