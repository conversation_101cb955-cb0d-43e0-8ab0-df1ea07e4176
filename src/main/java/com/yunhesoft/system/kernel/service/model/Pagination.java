package com.yunhesoft.system.kernel.service.model;

import com.yunhesoft.system.kernel.service.EntityService;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class Pagination<T> extends ASQLObject {
	/** 当前页数 **/
	private Integer page;
	/** 每页条数 **/
	private int size;
	/** 总条数 **/
	private long total;
	/** limit数量 */
	private int limit;
	/** 列表数据 **/
	private T result;
	/** 排序 */
	private String order;

	public static <T> Pagination<T> create(int page, int size) {
		return new Pagination<T>(page, size);

	}

	public static <T> Pagination<T> create(int page, int size, long total, T result) {
		return new Pagination<T>(page, size, total, result);
	}

	public Pagination() {
	}

	public Pagination(int page, int size) {
		this.page = page;
		this.size = size;
	}

	public Pagination(int page, int size, long total) {
		this.page = page;
		this.size = size;
		this.total = total;
	}

	public Pagination(int page, int size, long total, T result) {
		this.page = page;
		this.size = size;
		this.total = total;
		this.result = result;
	}

	public Pagination<T> setResult(T result, long total) {
		this.result = result;
		this.total = total;
		return this;
	}

	public int getStart() {
		return (page > 0 ? (page - 1) : 0) * size;
	}

	public Pagination<T> setTotal(int total) {
		this.total = total;
		return this;
	}

	public Pagination<T> setLimit(int limit) {
		this.limit = limit;
		return this;
	}

	public String sql(EntityService.DBTYPE category) {
		String sql = "";
		if (this.limit > 0) {
			sql = " limit " + this.limit;
		} else {
			if (size > 0) {
				if (DBTypeUtils.isPg(category) || DBTypeUtils.isKingBase(category)) {// pg数据库
					// sql = " LIMIT " + size + " OFFSET " + limit;// LIMIT 5 OFFSET 2
					sql = " LIMIT " + size + " OFFSET " + getStart();// LIMIT 5 OFFSET 2
				} else if (DBTypeUtils.isOracle(category)) {
					sql = " OFFSET " + getStart() + " ROWS FETCH NEXT " + size + " ROWS ONLY ";
				} else {
					sql = " limit " + getStart() + "," + size;
				}
			}
		}
		// System.out.println("sql:" + sql);
		return sql;
	}

	public String sql() {
		return this.sql(EntityService.DBTYPE.MYSQL);
	}
}