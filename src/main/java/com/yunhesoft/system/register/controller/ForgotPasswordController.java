package com.yunhesoft.system.register.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Req;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.register.entity.dto.ForgotPasswordDto;
import com.yunhesoft.system.register.service.IForgotPasswordService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

@RestController
@Log4j2
@RequestMapping("/system/register/forgotpassword")
@Api(tags = "找回密码")
public class ForgotPasswordController extends BaseRestController {

	@Autowired
	IForgotPasswordService forgotPasswordService;

	@RequestMapping(value = "/checkPhone", method = RequestMethod.POST)
	@ApiOperation(value = "检查手机号码是否存在")
	public Res<?> checkPhone(@RequestBody Req<String> req) {
		Res<?> result = new Res<>();
		try {
			String phoneNumber = req.getParams();
			result = forgotPasswordService.getSysLoginUserByPhone(phoneNumber);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			result.fail(500, "操作失败");
		}
		return result;
	}

	@RequestMapping(value = "/updateUserPassword", method = RequestMethod.POST)
	@ApiOperation(value = "更新用户密码")
	public Res<?> updateUserPassword(@RequestBody ForgotPasswordDto forgotPassWordDto) {
		Res<Boolean> result = new Res<Boolean>();
		try {
			Boolean res = forgotPasswordService.updateUserPassword(forgotPassWordDto);
			result.setResult(res).setSuccess(true);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			result.fail(500, "操作失败");
		}
		return result;
	}
}
