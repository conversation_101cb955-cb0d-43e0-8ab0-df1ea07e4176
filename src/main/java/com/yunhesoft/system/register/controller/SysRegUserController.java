package com.yunhesoft.system.register.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Req;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.register.entity.dto.SysRegUserMoblieDto;
import com.yunhesoft.system.register.entity.dto.SysRegUserPhoneDto;
import com.yunhesoft.system.register.entity.po.SysRegUser;
import com.yunhesoft.system.register.entity.vo.SysRegUserCompanyImgVo;
import com.yunhesoft.system.register.entity.vo.SysRegUserCompanyVo;
import com.yunhesoft.system.register.entity.vo.SysRegUserLoginVo;
import com.yunhesoft.system.register.service.IForgotPasswordService;
import com.yunhesoft.system.register.service.ISysRegUserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * 用户注册临时表Controller
 * 
 * <AUTHOR>
 * @date 2021-04-01
 */
@Log4j2
@RestController
@Api(tags = "用户注册临时表")
@RequestMapping("/system/register")
public class SysRegUserController extends BaseRestController {

	@Autowired
	private ISysRegUserService sysRegUserService;

	@Autowired
	IForgotPasswordService forgotPasswordService;

	/**
	 * 查询用户注册临时表列表
	 */
	@ApiOperation(value = "用户注册临时表 列表")
	@GetMapping("/list")
	public Res<List<SysRegUser>> list(SysRegUser sysRegUser) {
		Pagination<?> page = getRequestPagination();
		return sysRegUserService.selectSysRegUserList(sysRegUser, page);
	}

	/**
	 * 查询用户注册表的同时查询公司
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "查询用户信息同时查询公司信息")
	@GetMapping("/queryUserCompany")
	public Res<List<SysRegUserCompanyVo>> queryUserCompany(SysRegUserCompanyImgVo sysRegUserCompanyImgVo) {
		return Res.OK(sysRegUserService.seleceRrgUserCompany(sysRegUserCompanyImgVo));
	}

	/**
	 * 用户登录
	 */
	@ApiOperation(value = "用户登录")
	@GetMapping("/sysRegUserLogin")
	public Res<?> sysRegUserLogin(SysRegUserLoginVo sysRegUserLoginVo) {
		return Res.OK(sysRegUserService.sysRegUserLogin(sysRegUserLoginVo));
	}

	/**
	 * 获取用户注册临时表详细信息
	 */
	@ApiOperation(value = "获取用户注册临时表 详细信息")
	@GetMapping(value = "/{id}")
	public Res<SysRegUser> getInfo(@PathVariable("id") String id) {
		return Res.OK(sysRegUserService.selectSysRegUserById(id));
	}

	@ApiOperation(value = "根据手机号获取用户注册临时表 详细信息")
	@PostMapping(value = "/mobile")
	public Res<?> getInfoByMoblie(SysRegUserMoblieDto mobile) {
		log.info("mo" + mobile);
		return Res.OK(sysRegUserService.selectSysRegUserByPhone(mobile));
	}

	/**
	 * 新增用户注册临时表
	 */
	@ApiOperation(value = "新增用户注册临时表")
	@PostMapping(value = "/add")
	public Res<?> add(@RequestBody SysRegUser sysRegUser) {
		return Res.OK(sysRegUserService.insertSysRegUser(sysRegUser));
	}

	/**
	 * 新增用户注册临时表同时新增企业信息表
	 */
	@ApiOperation(value = "新增用户注册的同时新增企业信息")
	@PostMapping(value = "/addUserCompany")
	public Res<?> addUserCompany(@RequestBody SysRegUserCompanyVo sysRegUserCompanyVo) {
		return Res.OK(sysRegUserService.insertSysRegUserCompany(sysRegUserCompanyVo));
	}

	/**
	 * 修改用户注册临时表
	 */
	@ApiOperation(value = "修改用户注册临时表")
	@PostMapping(value = "/edit")
	public Res<?> edit(@RequestBody SysRegUserPhoneDto sysRegUser) {
		return Res.OK(sysRegUserService.updateSysRegUser(sysRegUser));
	}

	/**
	 * 删除用户注册临时表
	 */

	@ApiOperation(value = "删除用户注册临时表")

	@GetMapping("/delete/{ids}")
	public Res<?> remove(@PathVariable String id) {
		return Res.OK(sysRegUserService.deleteSysRegUserByIds(id));
	}

	@RequestMapping(value = "/checkPhone", method = RequestMethod.POST)
	@ApiOperation(value = "检查手机号码是否存在")
	public Res<?> checkPhone(@RequestBody Req<String> req) {
		Res<?> result = new Res<>();
		try {
			String phoneNumber = req.getParams();
			result = sysRegUserService.getSysLoginUserByPhone(phoneNumber);
		} catch (Exception e) {
			// log.error(e.getMessage(), e);
			result.fail(500, "操作失败");
		}
		return result;
	}
}
