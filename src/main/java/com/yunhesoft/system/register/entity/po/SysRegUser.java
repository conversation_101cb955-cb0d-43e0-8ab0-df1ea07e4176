package com.yunhesoft.system.register.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.yunhesoft.core.common.entity.BaseEntity;

/**
 * 用户注册临时表对象 SYS_REG_USER
 * 
 * <AUTHOR>
 * @date 2021-04-01
 */
@ApiModel(value = "用户注册临时表 对象")
@Data
@Entity
@Table(name = "SYS_REG_USER")
public class SysRegUser extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/** 手机号 通常11位 */
	@ApiModelProperty(value = "手机号 通常11位")
	@Column(name = "MOBILE", length=20)
	private String mobile;
	/** 密码 最长18位 */
	@ApiModelProperty(value = "密码 最长18位")
	@Column(name = "PASSWORD", length=125)
	private String password;
	/** 真实姓名 */
	@ApiModelProperty(value = "真实姓名")
	@Column(name = "NAME", length=50)
	private String name;
	/** 身份证号码 通常18位 */
	@ApiModelProperty(value = "身份证号码 通常18位")
	@Column(name = "IDCARD_NO", length=20)
	private String idcardNo;
	/** 通讯地址 */
	@ApiModelProperty(value = "通讯地址")
	@Column(name = "ADDR", length=200)
	private String addr;
	/** 个人邮箱 */
	@ApiModelProperty(value = "个人邮箱")
	@Column(name = "EMAIL", length=100)
	private String email;
	/** 企业ID */
	@ApiModelProperty(value = "企业ID")
	@Column(name = "COMP_ID", length=50)
	private String compId;
	/** 有效标识 */
	@ApiModelProperty(value = "有效标识")
	@Column(name = "USED")
	private int used;

}
