package com.yunhesoft.system.register.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.auth.entity.po.SysLoginUser;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.register.entity.dto.ForgotPasswordDto;
import com.yunhesoft.system.register.service.IForgotPasswordService;



@Service
public class ForgotPasswordServiceImpl implements IForgotPasswordService {

	@Autowired
	private EntityService dao;

	@Autowired
	private AuthService authService;
	
	@Override
	public boolean updateUserPassword(ForgotPasswordDto forgotPassWordDto) {
		String phoneNumber=forgotPassWordDto.getPhoneNumber();
		SysLoginUser user = authService.getSysLoginUserByPhone(phoneNumber);
		if(user!=null) {
			user.setPassword(authService.encryptPassword(forgotPassWordDto.getPassWord()));
			dao.rawUpdateById(user, new String[] { "password" });
			return true;
		}
		return false;
	}

	@Override
	public Res<?> getSysLoginUserByPhone(String phoneNumber) {
		Res<String> res = new Res<>();
		SysLoginUser user = authService.getSysLoginUserByPhone(phoneNumber);
		if(user==null) {
			res.ok( "该手机号未在系统中注册！").setSuccess(false);
		}else {
			res.ok().setSuccess(true);
		}
		return res;
	}

}
