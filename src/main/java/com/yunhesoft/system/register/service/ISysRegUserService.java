package com.yunhesoft.system.register.service;

import java.util.List;


import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.register.entity.dto.SysRegUserMoblieDto;
import com.yunhesoft.system.register.entity.dto.SysRegUserPhoneDto;
import com.yunhesoft.system.register.entity.po.SysRegUser;
import com.yunhesoft.system.register.entity.vo.SysRegUserCompanyImgVo;
import com.yunhesoft.system.register.entity.vo.SysRegUserCompanyVo;
import com.yunhesoft.system.register.entity.vo.SysRegUserLoginVo;


/**
 * 用户注册临时表Service接口
 * 
 * <AUTHOR>
 * @date 2021-04-01
 */
public interface ISysRegUserService 
{
    /**
     * 查询用户注册临时表
     * 
     * @param id 用户注册临时表ID
     * @return 用户注册临时表
     */
    public SysRegUser selectSysRegUserById(String id);

    /**
     * 查询用户注册临时表列表
     * 
     * @param sysRegUser 用户注册临时表
     * @return 用户注册临时表集合
     */
    public Res<List<SysRegUser>> selectSysRegUserList(SysRegUser sysRegUser, Pagination<?> page);

    /**
     * 新增用户注册临时表
     * 
     * @param sysRegUser 用户注册临时表
     * @return 结果
     */
    public SysRegUser insertSysRegUser(SysRegUser sysRegUser);

    /**
     * 批量删除用户注册临时表
     * 
     * @param ids 需要删除的用户注册临时表ID
     * @return 结果
     */
    public int deleteSysRegUserByIds(String id);

    /**
     * 删除用户注册临时表信息
     * 
     * @param id 用户注册临时表ID
     * @return 结果
     */
    public int deleteSysRegUserById(String id);

	/**
	 * 新增用户注册临时表同时新增企业用户表
	 */
	int insertSysRegUserCompany(SysRegUserCompanyVo sysRegUserCompanyVo);

	/**
	 * 查询用户信息加企业信息
	 */
	List<SysRegUserCompanyVo> seleceRrgUserCompany(SysRegUserCompanyImgVo sysRegUserCompanyImgVo);

	/**
	 * 用户登录
	 */
	SysRegUserCompanyVo sysRegUserLogin(SysRegUserLoginVo sysRegUserLoginVo);
	/**
	 * 根据手机号查询用户
	 * @param mobile
	 * @return
	 */
	SysRegUser selectSysRegUserByPhone(SysRegUserMoblieDto mobiledDto);

	/**
	 * 修改用户注册临时表
	 * 
	 * @param sysRegUser 用户注册临时表
	 * @return 结果
	 */
	int updateSysRegUser(SysRegUserPhoneDto sysRegUserPhoneDto);
	
	/**
	 * 
	 * @Description:  检测手机号是否存在
	 * @param: @param phoneNumber
	 * @param: @return      
	 * @return: Res<?>      
	 * @throws
	 */
	Res<?> getSysLoginUserByPhone(String phoneNumber);

	/**
	 * 通过企业id查找注册的用户
	 * @Description: TODO   
	 * @param: @param eid
	 * @param: @return      
	 * @return: List<SysRegUser>      
	 * @throws
	 */
	List<SysRegUser> getSysRegUserByEid(String eid);
 }
