package com.yunhesoft.system.register.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.exception.AuthException;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.EntityUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.system.auth.entity.po.SysLoginUser;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.register.entity.dto.SysRegUserMoblieDto;
import com.yunhesoft.system.register.entity.dto.SysRegUserPhoneDto;
import com.yunhesoft.system.register.entity.po.SysRegUser;
import com.yunhesoft.system.register.entity.vo.SysRegUserCompanyImgVo;
import com.yunhesoft.system.register.entity.vo.SysRegUserCompanyVo;
import com.yunhesoft.system.register.entity.vo.SysRegUserLoginVo;
import com.yunhesoft.system.register.service.ISysRegUserService;

import lombok.extern.log4j.Log4j2;

/**
 * 用户注册临时表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-04-01
 */
@Log4j2
@Service
public class SysRegUserServiceImpl implements ISysRegUserService {

	@Autowired
	private EntityService entityService;
	@Autowired
	AuthService authService;

	/**
	 * 查询用户注册临时表
	 * 
	 * @param id 用户注册临时表ID
	 * @return 用户注册临时表
	 */
	@Override
	public SysRegUser selectSysRegUserById(String id) {
		return entityService.queryObject(SysRegUser.class, Where.create("id=?", id));
	}

	/**
	 * 根据手机号查询用户
	 */

	@Override
	public SysRegUser selectSysRegUserByPhone(SysRegUserMoblieDto mobiledDto) {
		return entityService.queryObject(SysRegUser.class, Where.create("mobile=?", mobiledDto.getMoblie()));
	}

	/**
	 * 查询用户信息加企业信息
	 */

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public List<SysRegUserCompanyVo> seleceRrgUserCompany(SysRegUserCompanyImgVo sysRegUserCompanyImgVo) {
		log.info("aaaaa:" + sysRegUserCompanyImgVo);
		List<SysRegUserCompanyVo> lists = new ArrayList<SysRegUserCompanyVo>();
		// 人员表的查询条件
		Where where = Where.create();
		where.and("used = 1");
		// 公司表的查询条件
		Where where2 = Where.create();
		where2.and("used = 1");
		List<SysRegUser> sysRegUserList = new ArrayList<SysRegUser>();
		if (ObjUtils.notEmpty(sysRegUserCompanyImgVo.getMobile())) {
			where.and("mobile = ?", sysRegUserCompanyImgVo.getMobile());
		}
		if (ObjUtils.notEmpty(sysRegUserCompanyImgVo.getName())) {
			where.and("name like ?", "%" + sysRegUserCompanyImgVo.getName() + "%");
		}
		if (sysRegUserCompanyImgVo.getAuditStat() != -1) {
			where2.and("audit_stat = ?", sysRegUserCompanyImgVo.getAuditStat());
		}
		if (ObjUtils.notEmpty(sysRegUserCompanyImgVo.getCompName())) {
			where2.and("comp_name like ?", "%" + sysRegUserCompanyImgVo.getCompName() + "%");
		}
		List<SysRegUser> querySysRegUserList = entityService.queryList(SysRegUser.class, where);
		for (SysRegUser sysRegUser : querySysRegUserList) {
			sysRegUserList.add(sysRegUser);
		}
		List<String> sysRegCompanyIdList = new ArrayList<String>();
		for (SysRegUser sysRegUser : querySysRegUserList) {
			String compId = sysRegUser.getCompId();
			sysRegCompanyIdList.add(compId);
		}

		// 根据id查询企业信息
//		if(sysRegCompanyIdList.size() > 0) {
//			List<SysRegCompany> rawQueryListIn = entityService.rawQueryListIn(SysRegCompany.class, "used = 1 and id",
//					sysRegCompanyIdList.toArray());
//			sysRegCompanieList.addAll(rawQueryListIn);
//		}
//		
//		// 根据企业id查询人信息
//		if(sysRegUserComIdList.size() > 0) {
//			List<SysRegUser> rawQueryListIn2 = entityService.rawQueryListIn(SysRegUser.class, "used = 1 and comp_id",
//					sysRegUserComIdList.toArray());
//			sysRegUserList.addAll(rawQueryListIn2);
//		}

		// 进行去重处理
		Set set = new HashSet(sysRegUserList);
		sysRegUserList = new ArrayList<SysRegUser>(set);

		// 进行数据匹配
		log.info("人员信息：" + sysRegUserList);

		return lists;
	}

	/**
	 * 用户登录
	 */
	@Override
	public SysRegUserCompanyVo sysRegUserLogin(SysRegUserLoginVo sysRegUserLoginVo) {
		SysRegUserCompanyVo sysRegUserCompanyVo = new SysRegUserCompanyVo();
		Where where = Where.create();
		where.and("used = 1 and mobile = ?", sysRegUserLoginVo.getMobile());
		try {
			SysRegUser sysRegUser = entityService.queryObject(SysRegUser.class, where);
			if (sysRegUser == null) {
				throw new AuthException("手机号不存在");
			} else {
				String encryptPassword = authService.encryptPassword(sysRegUserLoginVo.getPassword());
				if (!encryptPassword.equals(sysRegUser.getPassword())) {
					throw new AuthException("密码错误");
				} else {

					sysRegUserCompanyVo.setSysRegUser(sysRegUser);
					return sysRegUserCompanyVo;
				}
			}
		} catch (Exception e) {
			log.error("", e);
			throw new RuntimeException(e);
		}

	}

	/**
	 * 查询用户注册临时表列表
	 * 
	 * @param sysRegUser 用户注册临时表
	 * @return 用户注册临时表
	 */
	@Override
	public Res<List<SysRegUser>> selectSysRegUserList(SysRegUser sysRegUser, Pagination<?> page) {
		Where where = Where.create();
		try {
			Object ID_value = EntityUtils.getValue("ID", sysRegUser);
			if (ObjUtils.notEmpty(ID_value)) {
				where.and("ID = ?", ID_value);
			}
			Object MOBILE_value = EntityUtils.getValue("MOBILE", sysRegUser);
			if (ObjUtils.notEmpty(MOBILE_value)) {
				where.and("MOBILE like ?", "%" + MOBILE_value + "%");
			}
			Object PASSWORD_value = EntityUtils.getValue("PASSWORD", sysRegUser);
			if (ObjUtils.notEmpty(PASSWORD_value)) {
				where.and("PASSWORD = ?", PASSWORD_value);
			}
			Object NAME_value = EntityUtils.getValue("NAME", sysRegUser);
			if (ObjUtils.notEmpty(NAME_value)) {
				where.and("NAME like ?", "%" + NAME_value + "%");
			}
			Object IDCARD_NO_value = EntityUtils.getValue("IDCARD_NO", sysRegUser);
			if (ObjUtils.notEmpty(IDCARD_NO_value)) {
				where.and("IDCARD_NO like ?", "%" + IDCARD_NO_value + "%");
			}
			Object ADDR_value = EntityUtils.getValue("ADDR", sysRegUser);
			if (ObjUtils.notEmpty(ADDR_value)) {
				where.and("ADDR like ?", "%" + ADDR_value + "%");
			}
			Object EMAIL_value = EntityUtils.getValue("EMAIL", sysRegUser);
			if (ObjUtils.notEmpty(EMAIL_value)) {
				where.and("EMAIL = ?", EMAIL_value);
			}
			Object CREATE_BY_value = EntityUtils.getValue("CREATE_BY", sysRegUser);
			if (ObjUtils.notEmpty(CREATE_BY_value)) {
				where.and("CREATE_BY = ?", CREATE_BY_value);
			}
			Object CREATE_TIME_value = EntityUtils.getValue("CREATE_TIME", sysRegUser);
			if (ObjUtils.notEmpty(CREATE_TIME_value)) {
				where.and("CREATE_TIME = ?", CREATE_TIME_value);
			}
			Object UPDATE_BY_value = EntityUtils.getValue("UPDATE_BY", sysRegUser);
			if (ObjUtils.notEmpty(UPDATE_BY_value)) {
				where.and("UPDATE_BY = ?", UPDATE_BY_value);
			}
			Object UPDATE_TIME_value = EntityUtils.getValue("UPDATE_TIME", sysRegUser);
			if (ObjUtils.notEmpty(UPDATE_TIME_value)) {
				where.and("UPDATE_TIME = ?", UPDATE_TIME_value);
			}
			Object COMP_ID_value = EntityUtils.getValue("COMP_ID", sysRegUser);
			if (ObjUtils.notEmpty(COMP_ID_value)) {
				where.and("COMP_ID = ?", COMP_ID_value);
			}
			// 读取总记录数量
			Res<List<SysRegUser>> res = new Res<List<SysRegUser>>();
			res.setTotal(entityService.queryCount(SysRegUser.class, where));
			// 读取记录结果
			res.setResult(entityService.queryList(SysRegUser.class, where, page));
			return res;
		} catch (IllegalArgumentException | IllegalAccessException e) {
			log.error("", e);
			throw new RuntimeException(e);
		}
	}

	/**
	 * 新增用户注册临时表
	 * 
	 * @param sysRegUser 用户注册临时表
	 * @return 结果
	 */
	@Override
	@Transactional
	public SysRegUser insertSysRegUser(SysRegUser sysRegUser) {
		String encryptPassword = authService.encryptPassword(sysRegUser.getPassword());
		String tmuid = TMUID.getUID();
		sysRegUser.setId(tmuid);
		sysRegUser.setCreateTime(new Date());
		sysRegUser.setUsed(1);
		sysRegUser.setPassword(encryptPassword);
		sysRegUser.setCompId(TMUID.getUID());

		SysLoginUser sysLoginUser = new SysLoginUser();
		sysLoginUser.setId(tmuid);
		sysLoginUser.setCreateTime(new Date());
		sysLoginUser.setUserName(sysRegUser.getMobile());
		sysLoginUser.setRealName(sysRegUser.getName());
		sysLoginUser.setPassword(encryptPassword);
		sysLoginUser.setPhone(sysRegUser.getMobile());
		sysLoginUser.setEmail(sysRegUser.getEmail());
		sysLoginUser.setStatus(1);
		sysLoginUser.setRegisterTime(new Date());

		int insert = entityService.insert(sysRegUser);
		int insert2 = entityService.insert(sysLoginUser);
		if (insert + insert2 > 1) {
			return sysRegUser;
		} else {
			throw new AuthException("新增失败");

		}

	}

	/**
	 * 新增用户注册临时表同时新增企业用户表
	 */
	@Override
	@Transactional
	public int insertSysRegUserCompany(SysRegUserCompanyVo sysRegUserCompanyVo) {

		SysRegUser sysRegUser = sysRegUserCompanyVo.getSysRegUser();

		sysRegUser.setId(TMUID.getUID());
		// 将公司id赋给用户表

		// 密码md5加密
		String encryptPassword = authService.encryptPassword(sysRegUser.getPassword());
		log.info(encryptPassword);
		sysRegUser.setPassword(encryptPassword);
		sysRegUser.setCreateTime(DateTimeUtils.getNowDate());
		// 默认可用
		sysRegUser.setUsed(1);
		int insert = entityService.insert(sysRegUser);
		return insert;
	}

	/**
	 * 修改用户注册临时表
	 * 
	 * @param sysRegUser 用户注册临时表
	 * @return 结果
	 */
	@Override
	@Transactional
	public int updateSysRegUser(SysRegUserPhoneDto sysRegUserPhoneDto) {
		SysRegUser sysRegUser = sysRegUserPhoneDto.getSysRegUser();
		sysRegUser.setUpdateTime(new Date());
		String encryptPassword = authService.encryptPassword(sysRegUser.getPassword());
		sysRegUser.setPassword(encryptPassword);

		SysLoginUser sysLoginUser = entityService.queryObject(SysLoginUser.class,
				Where.create("user_name=?", sysRegUserPhoneDto.getPhone()));
		sysLoginUser.setUserName(sysRegUser.getMobile());
		sysLoginUser.setRealName(sysRegUser.getName());
		sysLoginUser.setPassword(encryptPassword);
		sysLoginUser.setPhone(sysRegUser.getMobile());
		sysLoginUser.setEmail(sysRegUser.getEmail());
		sysLoginUser.setStatus(0);
		int updateById = entityService.updateById(sysLoginUser);
		int updateById2 = entityService.updateById(sysRegUser);
		if (updateById + updateById2 > 1) {
			return 1;
		} else {
			throw new AuthException("修改失败");
		}

	}

	/**
	 * 批量删除用户注册临时表
	 * 
	 * @param ids 需要删除的用户注册临时表ID
	 * @return 结果
	 */
	@Override
	public int deleteSysRegUserByIds(String id) {
		// return entityService.deleteSysRegUserByIds(ids);
		SysRegUser bean = new SysRegUser();
		bean.setId(id);
		return entityService.rawDeleteById(bean);
		// return entityService.delete(SysRegUser.class, Where.create().andIns("id",
		// ids));
	}

	/**
	 * 删除用户注册临时表信息
	 * 
	 * @param id 用户注册临时表ID
	 * @return 结果
	 */
	@Override
	public int deleteSysRegUserById(String id) {
		// return entityService.deleteSysRegUserById(id);
		return entityService.delete(SysRegUser.class, Where.create("id=?", id));
	}

	@Override
	public Res<?> getSysLoginUserByPhone(String phoneNumber) {
		Res<String> res = new Res<>();
		SysLoginUser user = authService.getSysLoginUserByPhone(phoneNumber);
		if (user != null) {
			res.ok("该手机号已经注册！").setSuccess(false);
		} else {
			res.ok().setSuccess(true);
		}
		return res;
	}

	@Override
	public List<SysRegUser> getSysRegUserByEid(String eid) {
		List<SysRegUser> list = entityService.queryList(SysRegUser.class,
				Where.create().eq(SysRegUser::getCompId, eid));
		return list;
	}
}
