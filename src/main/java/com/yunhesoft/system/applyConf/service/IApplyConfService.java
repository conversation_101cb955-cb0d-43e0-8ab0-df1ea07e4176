package com.yunhesoft.system.applyConf.service;

import com.yunhesoft.system.applyConf.entity.dto.ApplyParams;
import com.yunhesoft.system.applyConf.entity.dto.ResultObj;
import com.yunhesoft.system.tds.entity.po.TdsAccountMeter;
import com.yunhesoft.system.tds.entity.po.TdsAccountTag;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @category 应用接口
 * <AUTHOR>
 *
 */
public interface IApplyConfService {

	/**
	 * @category 查询数据列表
	 * @param dto
	 * @return
	 */
	public ResultObj queryList(ApplyParams dto);
	/**
	 * @category 保存应用配置
	 * @param dto
	 * @return
	 */
	public ResultObj saveConf(ApplyParams dto);
	/**
	 * @category 批量删除
	 * @param idList
	 * @return
	 */
	public Boolean deleteDataByIdList(List<String> idList);

	/**
	 * @category 获取参数结果值
	 * @param dto
	 * @return null or string value
	 */
	public String getApplyConfValue(ApplyParams dto);
	/**
	 * 初始化配置信息
	 */
	public void initData();


	/**
	 * @category 同步仪表信息
	 * @param tag 仪表（采集点）对象
	 * @param op op add添加  del删除  upd更新
	 */
	public void syncTaginfo(TdsAccountTag tag, String op);

	/**
	 * @category 批量同步仪表新
	 * @param tlist 采集点信息
	 * @param unitCode 核算对象
	 * @param op add添加  del删除  upd更新
	 */
	public void syncBatchTaginfo(List<TdsAccountTag> tlist, String unitCode, String op);
	/**
	 * @category 批量重新同步单个核算对象仪表
	 * @param unitCode
	 */
	public void syncBatchTagSort(String unitCode);

	/**
	 * @category 获取核算对象指定日期应用的版本日期
	 * @param unitCode
	 * @param sendRq
	 * @return
	 */
	public String getAaccuntVerRq(String unitCode, String sendRq);
	/**
	 * @category 获取核算对象相关信息，包括模式及区域列表
	 * @param unitCode
	 * @return
	 */
	public Map<String, String> getUnitAccountConf(String unitCode, String tdsAlias);

	/**
	 * @category 从外部接口获取采集点
	 * @param param
	 * @return
	 */
	public List<TdsAccountMeter> getExtPotList(ApplyParams param);

	/**
	 * @category 从外部接口获取时间点
	 * @param param
	 * @return
	 */
	public List<Map<String, Object>> getExtTimeList(ApplyParams param);

	/**
	 * @category 获取核算对象信息
	 * @param unitCode
	 * @return
	 */
	public Map<String, Object> getUnitInfoMap(String unitCode);

	/**
	 * @category 调用外部台账数据保存接口
	 * @param sendUnitCode
	 * @param shiftCode
	 * @param dataId
	 * @param sjlist
	 * @param infoTimeIdMap
	 * @param sbsj
	 * @param xbsj
	 * @param colList
	 * @param tagIdObjMap
	 * @param map
	 * @return
	 */
	public Boolean extDataSave(String sendUnitCode, String shiftCode, String dataId, List<Date> sjlist, Map<Date, String> infoTimeIdMap, String sbsj, String xbsj,
			List<String> colList, Map<String, TdsAccountMeter> tagIdObjMap, Map<String, Map<Date, String>> map, Map<String, Map<Date, String>> dataList, Map<Date, String> jobInputTimeIdMap, String teamId);
	/**
	 * @category 获取班次时间点列表
	 * @param sbsj
	 * @param xbsj
	 * @return
	 */
	public List<String> getUnitOntimeList(String sbsj, String xbsj);
	/**
	 * @category 查询核算单元是否有指向，如果有，转换核算单元代码，否则，直接返回本核算单元
	 * @param unitCode
	 * @return
	 */
	public String transferUnitCode(String unitCode);
}
