package com.yunhesoft.system.applyConf.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.applyConf.entity.dto.ApplyParams;
import com.yunhesoft.system.applyConf.entity.dto.TagObj;
import com.yunhesoft.system.applyConf.entity.po.AccoutAutosaveMark;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountApplyConf;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountForm;
import com.yunhesoft.system.applyConf.service.IAccountSynService;
import com.yunhesoft.system.applyConf.service.IApplyConfService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.mongodb.service.MongoDBService;
import com.yunhesoft.system.tds.entity.po.AccountTagData;
import com.yunhesoft.system.tds.entity.vo.TdsAccountTagVo;
import com.yunhesoft.system.tds.service.IDataSourceAccountService;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class AccountSynServiceImpl implements IAccountSynService {

	@Autowired
    private EntityService srv;
	@Autowired
	private IDataSourceAccountService accountSrv;
	@Autowired
	private IApplyConfService applySrv;
	@Autowired
	private MongoDBService mongoDBServ;
	

	private final String accountTabName = "accountTab";
	private String tdsAlias = "";
	
	/**
	 * @category 台账移动端数据修改
	 * @param dto
	 * @return
	 */
	@Override
	public Boolean mobileUpd(TagObj param) {
		Boolean flag = true;
		
		String kssj = param.getKssj();
		String jzsj = param.getJzsj();
		String userId = param.getUserId();
		String userName = param.getUserName();
		List<TdsAccountTagVo> list = param.getList();
		
		Date lastUpdTime = getLastUpdTime();//获取最后更新日期
		
		if(lastUpdTime!=null && DateTimeUtils.bjDate(DateTimeUtils.parseDateTime(kssj), lastUpdTime) == 1) {
			return true;
		}
		
		if(StringUtils.isNotEmpty(list)) {
			Date currTime = DateTimeUtils.getNowDate();
			
			Map<String, Map> mmap = getMongoDbDataMap(kssj, jzsj);
			Map<String, AccountTagData> dmap = getRelationDataMap(kssj, jzsj);
			
			List<Map> insertMongodbList = new ArrayList<Map>();
			List<Map> updateMongodbList = new ArrayList<Map>();
			List<AccountTagData> insertDataList = new ArrayList<AccountTagData>();
			List<AccountTagData> updateDataList = new ArrayList<AccountTagData>();
			
			for (TdsAccountTagVo vo : list) {
				
				if(lastUpdTime!=null && DateTimeUtils.bjDate(DateTimeUtils.parseDateTime(vo.getSj()), lastUpdTime) == 1) {
					continue;
				}
				
				String key = vo.getSj()+"_"+vo.getTagid();
				String val = vo.getVal();
				if(mmap.containsKey(key)) {
					Map map = mmap.get(key);
					map.put("valstr", val);
					if(Coms.judgeDouble(val)) {
						map.put("val", Double.parseDouble(val));
					}
					updateMongodbList.add(map);
				}else {
					//添加
					String rq =  vo.getSj().substring(0, 10);
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("_id", TMUID.getUID());
					map.put("dsAlias", tdsAlias);
					map.put("formId", null);
					map.put("dataId", null);
					map.put("col", vo.getTagid());// 列名
					map.put("tag", "");
					map.put("dateType", "tag");
					map.put("tagId", vo.getTagid());
					map.put("rq", rq);
					map.put("rqlong", DateTimeUtils.parseDate(rq).getTime());
					map.put("sj", vo.getSj());
					map.put("sjlong", DateTimeUtils.parseDateTime(vo.getSj()).getTime());
					map.put("valstr", val);
					if(Coms.judgeDouble(val)) {
						map.put("val", Double.parseDouble(val));
					}else {
						map.put("val", null);
					}
					map.put("creTime", currTime);
					map.put("creUserId", userId);
					map.put("creUserName", userName);
					map.put("updTime", null);
					map.put("updUserId", null);
					map.put("updUserName", null);
					map.put("additionVal", null);

					map.put("timetype", "point");
					map.put("edit", true);
					map.put("tmused", 1);
					map.put("confirmDiff", 0);// 确认差（秒）
					map.put("rowConfirmBound", null);// 确认范围（分
					insertMongodbList.add(map);
				}
				
				if(dmap.containsKey(key)) {
					AccountTagData data = dmap.get(key);
					data.setValstr(val);
					if(Coms.judgeDouble(val)) {
						data.setVal(Double.parseDouble(val));
					}
					updateDataList.add(data);
				}else {
					//添加
					String rq =  vo.getSj().substring(0, 10);
					
					AccountTagData data = new AccountTagData();
					data.setId(TMUID.getUID());
					data.setApplyDay(rq);
					data.setApplyTime(DateTimeUtils.parseDateTime(vo.getSj()));
					data.setApplyTimeStr(vo.getSj());
					data.setTagId(vo.getTagid());
					data.setValstr(val);
					if(Coms.judgeDouble(val)) {
						data.setVal(Double.parseDouble(val));
					}
					data.setEditMark(1);
					data.setTimeType("point");
					data.setTmused(1);
					insertDataList.add(data);
				}
			}
			
			if(StringUtils.isNotEmpty(insertMongodbList)) {
				JSONArray insertData = new JSONArray();
				for (Map map : updateMongodbList) {
					insertData.add(new JSONObject(map));
				}
				mongoDBServ.insertBatch(accountTabName + "_" + tdsAlias, insertData);
			}
			if(StringUtils.isNotEmpty(updateMongodbList)) {
				JSONArray updateData = new JSONArray();
				for (Map map : updateMongodbList) {
					updateData.add(new JSONObject(map));
				}
				mongoDBServ.updateBatchById(accountTabName + "_" + tdsAlias, updateData);
			}
			
			if(StringUtils.isNotEmpty(insertDataList)) {
				flag = flag && 1==srv.insertBatch(insertDataList);
			}
			if(StringUtils.isNotEmpty(updateDataList)) {
				flag = flag && 1==srv.insertBatch(updateDataList);
			}
			
			updateLastTime(jzsj);
		}
		
		return flag;
	}
	/**
	 * 保存最后更新时间
	 * @param jzsj
	 */
	private void updateLastTime(String jzsj) {
		Where where = Where.create();
		where.eq(AccoutAutosaveMark::getApplyType, "mongodb");
		List<AccoutAutosaveMark> list = srv.queryData(AccoutAutosaveMark.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			AccoutAutosaveMark obj = list.get(0);
			obj.setUpdTime(DateTimeUtils.parseDateTime(jzsj));
			srv.updateById(obj);
		}else {
			AccoutAutosaveMark obj = new AccoutAutosaveMark();
			obj.setId(TMUID.getUID());
			obj.setApplyType("mongodb");
			obj.setUpdTime(DateTimeUtils.parseDateTime(jzsj));
			obj.setTmused(1);
			srv.insert(obj);
		}
		
	}
	/**
	 * 获取最后更新时间
	 * @return
	 */
	private Date getLastUpdTime() {
		Where where = Where.create();
		where.eq(AccoutAutosaveMark::getApplyType, "mongodb");
		List<AccoutAutosaveMark> list = srv.queryData(AccoutAutosaveMark.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			AccoutAutosaveMark obj = list.get(0);
			return obj.getUpdTime();
		}
		return null;
	}
	
	/**
	 * 获取已有MongoDB数据
	 * @param kssj
	 * @param jzsj
	 * @return
	 */
	private List<Map> getMongoDBData(String kssj, String jzsj) {
		tdsAlias = "tz";
		ApplyParams dto = new ApplyParams();
		dto.setApplyAlias("account_tds_alias");
		String appTdsAlias = applySrv.getApplyConfValue(dto);
		if(StringUtils.isNotEmpty(appTdsAlias)) {
			tdsAlias = appTdsAlias;
		}
		
		List<Map> mdata = accountSrv.getAccountSaveData(this.accountTabName+"_"+tdsAlias, tdsAlias, DateTimeUtils.parseDateTime(kssj).getTime(), DateTimeUtils.parseDateTime(jzsj).getTime());
		return mdata;
	}
	/**
	 * 获取MongoDB数据map
	 * @param kssj
	 * @param jzsj
	 * @return
	 */
	private Map<String, Map> getMongoDbDataMap(String kssj, String jzsj) {
		Map<String, Map> rmap = new HashMap<String, Map>();
		
		List<Map> list = getMongoDBData(kssj, jzsj);
		if(StringUtils.isNotEmpty(list)) {
			for (Map map : list) {
				String key = String.valueOf(map.get("sj"))+"_"+String.valueOf(map.get("col"));
				rmap.put(key, map);
			}
		}
		
		return rmap;
	}
	/**
	 * 获取关系数据库数据
	 * @param kssj
	 * @param jzsj
	 * @return
	 */
	private List<AccountTagData> getRelationData(String kssj, String jzsj) {
		Where where = Where.create();
		where.ge(AccountTagData::getApplyTime, DateTimeUtils.parseDateTime(kssj)).le(AccountTagData::getApplyTime, DateTimeUtils.parseDateTime(jzsj));
		List<AccountTagData> rlist = srv.queryData(AccountTagData.class, where, null, null);
		return rlist;
	}
	/**
	 * 获取关系数据库数据map
	 * @param kssj
	 * @param jzsj
	 * @return
	 */
	private Map<String, AccountTagData> getRelationDataMap(String kssj, String jzsj) {
		Map<String, AccountTagData> rmap = new HashMap<String, AccountTagData>();
		List<AccountTagData> list = getRelationData(kssj, jzsj);
		if(StringUtils.isNotEmpty(list)) {
			for (AccountTagData obj : list) {
				String key = obj.getApplyTimeStr()+"_"+obj.getTagId();
				rmap.put(key, obj);
			}
		}
		return rmap;
	}
	
	/**
	 * @category 台账自动保存
	 * @param param
	 */
	@Override
	public void accountAutoSave(TagObj param) {
		
		/**
		 * 处理流程
		 * 1、根据最后保存时间和当前时间，获取需要处理的时间段
		 * 2、根据时间段，获取台账配置（自定义台账），获取需要生成的台账信息
		 * 3、获取台账相关的核算对象信息，与台账信息做交集处理，确定需要生成的台账（默认台账、自定义台账）
		 * 4、根据日期、核算对象，推出倒班班次信息
		 * 5、根据日期、班次循环，创建所有台账信息（读取已有信息，做判断处理，避免重复）
		 * 6、台账数据生成后，根据台账配置，自动保存仪表数据
		 * 7、同时，整理相关数据，提供对外接口，同步更新关联功能数据（班组记事等）
		 */
		Date currTime = DateTimeUtils.getNowDate();
		Date lastUpdTime = getLastUpdTime();//获取最后更新日期
		
		List<TdsAccountForm> customList = getCustomList();
		
	}
	//
	private List<TdsAccountForm> getCustomList() {
		Where where = Where.create();
		where.eq(TdsAccountForm::getTmused, 1);
		where.notEmpty(TdsAccountForm::getFormCode);
		where.notEmpty(TdsAccountForm::getUnitCode);
		Order order = Order.create();
		order.order(TdsAccountForm::getUnitCode);
		List<TdsAccountForm> rlist = srv.queryData(TdsAccountForm.class, where, order, null);
		return rlist;
	}

	
}
