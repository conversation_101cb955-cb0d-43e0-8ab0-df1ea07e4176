package com.yunhesoft.system.applyConf.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.applyConf.entity.dto.ApplyParams;
import com.yunhesoft.system.applyConf.entity.dto.ResultObj;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountApplyConf;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountForm;
import com.yunhesoft.system.applyConf.entity.vo.*;
import com.yunhesoft.system.applyConf.service.IApplyConfService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.entity.po.*;
import com.yunhesoft.system.tds.event.AccountSaveEvent;
import com.yunhesoft.system.tds.service.IDataSourceAccountService;
import com.yunhesoft.system.tds.service.impl.ControlTypeConverter;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class ApplyConfServiceImpl implements IApplyConfService {

	@Autowired
	private EntityService srv;
	@Autowired
	private IDataSourceAccountService accountSrv;
	@Autowired
	private RedisUtil redis;

	@Resource
	private ApplicationContext context;// 应用事件服务

	private final String accountKey = "TDS:ACCOUUNT:TITLE";//
	private int sortno = 1;

	@Override
	public ResultObj queryList(ApplyParams dto) {
		String mode = dto.getApplyMode();
		String alias = dto.getApplyAlias();
		String name = dto.getApplyName();
		String org = dto.getApplyOrg();
		String desc = dto.getApplyDesc();

		Pagination<TdsAccountApplyConf> page = dto.getPage();
		if (page == null || page.getSize() == 0) {
			page = null;
		}

		Where where = Where.create();
		where.eq(TdsAccountApplyConf::getTmused, 1);
		if (StringUtils.isNotEmpty(alias)) {
			where.eq(TdsAccountApplyConf::getApplyAlias, alias);
		}
		if (StringUtils.isNotEmpty(name)) {
			where.like(TdsAccountApplyConf::getApplyName, name);
		}
		if (StringUtils.isNotEmpty(mode)) {
			where.eq(TdsAccountApplyConf::getApplyMode, mode);
		}
		if (StringUtils.isNotEmpty(org)) {
			where.eq(TdsAccountApplyConf::getApplyMode, org);
		} else {
			where.isEmpty(TdsAccountApplyConf::getApplyOrg);
		}
		if (StringUtils.isNotEmpty(desc)) {
			where.like(TdsAccountApplyConf::getApplyDesc, desc);
		}

		Order order = Order.create();
		order.order(TdsAccountApplyConf::getTmsort);
		List<TdsAccountApplyConf> list = srv.queryData(TdsAccountApplyConf.class, where, order, page);

		ResultObj robj = new ResultObj();
		robj.setList(list);
		robj.setPage(page);

		return robj;
	}

	@Override
	public ResultObj saveConf(ApplyParams dto) {
		List<TdsAccountApplyConf> rlist = new ArrayList<TdsAccountApplyConf>();

		List<ApplyConfVo> list = dto.getList();
		if (StringUtils.isNotEmpty(list)) {
			List<TdsAccountApplyConf> addList = new ArrayList<TdsAccountApplyConf>();
			List<TdsAccountApplyConf> updList = new ArrayList<TdsAccountApplyConf>();
			for (ApplyConfVo vo : list) {
				if (StringUtils.isEmpty(vo.getId())) {// 插入
					TdsAccountApplyConf obj = ObjUtils.copyTo(vo, TdsAccountApplyConf.class);
					obj.setId(TMUID.getUID());
					obj.setTmused(1);
					addList.add(obj);
				} else {// 更新
					TdsAccountApplyConf obj = ObjUtils.copyTo(vo, TdsAccountApplyConf.class);
					updList.add(obj);
				}
			}
			if (StringUtils.isNotEmpty(addList)) {
				srv.insertBatch(addList);
				rlist.addAll(addList);
			}
			if (StringUtils.isNotEmpty(updList)) {
				srv.updateByIdBatch(updList);
				rlist.addAll(addList);
			}
		}

		ResultObj robj = new ResultObj();
		robj.setList(rlist);

		return robj;
	}

	@Override
	public Boolean deleteDataByIdList(List<String> idList) {
		Boolean rv = true;
		if (StringUtils.isNotEmpty(idList)) {
			Where where = Where.create();
			where.eq(TdsAccountApplyConf::getTmused, 1);
			where.in(TdsAccountApplyConf::getId, idList.toArray());
			List<TdsAccountApplyConf> list = srv.queryData(TdsAccountApplyConf.class, where, null, null);
			if (StringUtils.isNotEmpty(list)) {
				for (TdsAccountApplyConf obj : list) {
					obj.setTmused(0);
				}
				rv = 1 == srv.updateBatch(list);
			}
		}
		return rv;
	}

	@Override
	/**
	 * 获取应用配置结果
	 */
	public String getApplyConfValue(ApplyParams dto) {
		String mode = dto.getApplyMode();
		String alias = dto.getApplyAlias();
		String name = dto.getApplyName();
		String org = dto.getApplyOrg();
		String desc = dto.getApplyDesc();

		Pagination<TdsAccountApplyConf> page = dto.getPage();
		if (page == null || page.getSize() == 0) {
			page = null;
		}

		Where where = Where.create();
		where.eq(TdsAccountApplyConf::getTmused, 1);
		if (StringUtils.isNotEmpty(alias)) {
			where.eq(TdsAccountApplyConf::getApplyAlias, alias);
		}
		if (StringUtils.isNotEmpty(name)) {
			where.like(TdsAccountApplyConf::getApplyName, name);
		}
		if (StringUtils.isNotEmpty(mode)) {
			where.eq(TdsAccountApplyConf::getApplyMode, mode);
		}
		if (StringUtils.isNotEmpty(desc)) {
			where.like(TdsAccountApplyConf::getApplyDesc, desc);
		}
		// 有机构查对应机构和无机构的结果
		if (StringUtils.isNotEmpty(org)) {
			where.and().lb();
			where.eq(TdsAccountApplyConf::getApplyOrg, org);
			where.or().isEmpty(TdsAccountApplyConf::getApplyOrg);
			where.rb();
		}
		Order order = Order.create();
		order.order(TdsAccountApplyConf::getTmsort);
		List<TdsAccountApplyConf> list = srv.queryData(TdsAccountApplyConf.class, where, order, page);

		String val = null;
		if (StringUtils.isNotEmpty(list)) {
			if (StringUtils.isNotEmpty(org)) {// 有机构
				for (TdsAccountApplyConf obj : list) {
					if (org.equals(obj.getApplyOrg())) {
						return obj.getApplyValue();
					}
				}
				for (TdsAccountApplyConf obj : list) {
					if (StringUtils.isEmpty(obj.getApplyOrg())) {
						val = obj.getApplyValue();
						break;
					}
				}
			} else {// 无机构
				for (TdsAccountApplyConf obj : list) {
					if (StringUtils.isNotEmpty(obj.getApplyValue())) {
						val = obj.getApplyValue();
						break;
					}
				}
			}
		}

		return val;
	}

	@Override
	/**
	 * 初始化配置信息
	 */
	public void initData() {
		Where where = Where.create();
		where.eq(TdsAccountApplyConf::getTmused, 1);
		Long len = srv.queryCount(TdsAccountApplyConf.class, where);
		if (len <= 0) {
			List<TdsAccountApplyConf> list = new ArrayList<TdsAccountApplyConf>();

			TdsAccountApplyConf c1 = new TdsAccountApplyConf();
			c1.setId(TMUID.getUID());
			c1.setApplyMode("account");
			c1.setApplyOrg("");
			c1.setApplyName("台账多层表头配置");
			c1.setApplyAlias("ledger_title_name");
			c1.setApplyValue("zone,单元;dev,设备;name,名称;tag,位号;unit,单位;uplow,范围");
			c1.setApplyDesc("默认六层表头，key值不能修改，默认不包括cost核算对象，默认为zone单元,dev设备,name名称,tag位号,unit单位,uplow范围");
			c1.setApplyCom("textfield");
			c1.setApplyKeys("");
			c1.setApplyValues("");
			c1.setTmsort(0);
			c1.setTmused(1);
			list.add(c1);

			TdsAccountApplyConf c2 = new TdsAccountApplyConf();
			c2.setId(TMUID.getUID());
			c2.setApplyMode("account");
			c2.setApplyOrg("");
			c2.setApplyName("台账倒班录入延时分钟");
			c2.setApplyAlias("ledger_class_input_delay");
			c2.setApplyValue("30");
			c2.setApplyDesc("默认下班后30分钟内可录入当班数据");
			c2.setApplyCom("textfield");
			c2.setApplyKeys("");
			c2.setApplyValues("");
			c2.setTmsort(1);
			c2.setTmused(1);

			list.add(c2);

			TdsAccountApplyConf c3 = new TdsAccountApplyConf();
			c3.setId(TMUID.getUID());
			c3.setApplyMode("account");
			c3.setApplyOrg("");
			c3.setApplyName("台账确认间隔分钟");
			c3.setApplyAlias("account_confirm_step");
			c3.setApplyValue("0");
			c3.setApplyDesc("台账确认间隔分钟默认0，即每个时间点确认，从每日0点开始计算，0点需要确认");
			c3.setApplyCom("textfield");
			c3.setApplyKeys("");
			c3.setApplyValues("");
			c3.setTmsort(2);
			c3.setTmused(1);

			list.add(c3);

			TdsAccountApplyConf c4 = new TdsAccountApplyConf();
			c4.setId(TMUID.getUID());
			c4.setApplyMode("account");
			c4.setApplyOrg("");
			c4.setApplyName("台账limis数据更新延迟天数");
			c4.setApplyAlias("account_limis_overdayget");
			c4.setApplyValue("0");
			c4.setApplyDesc("当班日期后，进入台账后limis数据更新获取的延迟天数，例如：设置为2，当班日期后，2天内，进入台账会自动更新limis数据");
			c4.setApplyCom("numberfield");
			c4.setApplyKeys("");
			c4.setApplyValues("");
			c4.setTmsort(3);
			c4.setTmused(1);

			list.add(c4);

			TdsAccountApplyConf c5 = new TdsAccountApplyConf();
			c5.setId(TMUID.getUID());
			c5.setApplyMode("account");
			c5.setApplyOrg("");
			c5.setApplyName("台账自定义表单机构权限是否向上判断");
			c5.setApplyAlias("account_formperm_judgeup");
			c5.setApplyValue("0");
			c5.setApplyDesc("台账自定义表单机构权限是否向上判断，0不向上判断，1向上判断");
			c5.setApplyCom("textfield");
			c5.setApplyKeys("");
			c5.setApplyValues("");
			c5.setTmsort(4);
			c5.setTmused(1);

			list.add(c5);

			TdsAccountApplyConf c6 = new TdsAccountApplyConf();
			c6.setId(TMUID.getUID());
			c6.setApplyMode("account");
			c6.setApplyOrg("");
			c6.setApplyName("台账干部确认判断天数");
			c6.setApplyAlias("account_confirm_todo_days");
			c6.setApplyValue("7");
			c6.setApplyDesc("台账干部确认判断天数，大于0，从当前时间向前计算范围");
			c6.setApplyCom("textfield");
			c6.setApplyKeys("");
			c6.setApplyValues("");
			c6.setTmsort(5);
			c6.setTmused(1);

			list.add(c6);

			srv.insertBatch(list);
		}
		// serv.initConfig("台账多层表头名称", "ledger_title_name", "单元,设备,名称,位号,单位,范围",
		// "默认六层表头，单元,设备,名称,位号,单位,范围");
		// serv.initConfig("台账倒班录入延时分钟", "ledger_class_input_delay", "30",
		// "默认下班后30分钟内可录入当班数据");

	}

	/**
	 * @category 同步仪表信息（单个仪表，暂无用）
	 * @param tag 仪表（采集点）对象
	 * @param op  add添加 del删除 upd更新
	 */
	public void syncTaginfo(TdsAccountTag tag, String op) {
		// 进行初始化
		accountSrv.initTagInfo();

		Date nd = DateTimeUtils.getNowDate();
		String currDay = DateTimeUtils.getNowDateStr().substring(0, 10);// 当前日期
		TdsAccountTagVersion ver = null;
		TdsAccountTagVersion upver = null;
		List<TdsAccountTag> tagList = null;
		List<TdsAccountTag> uptagList = null;

		String unitName = getUnitName(tag.getUnitCode());

		// 获取全部版本
		Where wherever = Where.create();
		wherever.eq(TdsAccountTagVersion::getUnitcode, tag.getUnitCode());
		// wherever.eq(TdsAccountTagVersion::getRq, currDay);
		Order order = Order.create();
		order.order(TdsAccountTagVersion::getRq);
		List<TdsAccountTagVersion> tagVerList = srv.queryData(TdsAccountTagVersion.class, wherever, order, null);

		if (StringUtils.isNotEmpty(tagVerList)) {
			// 根据日期获取对应版本
			upver = null;
			for (TdsAccountTagVersion v : tagVerList) {
				if (v.getRq().equals(currDay)) {
					ver = v;
					break;
				} else {
					upver = v;
				}
			}
			if (ver != null) {
				Where wheretag = Where.create();
				wheretag.eq(TdsAccountTag::getVermark, ver.getId());
				wheretag.eq(TdsAccountTag::getRq, currDay);
				tagList = srv.queryData(TdsAccountTag.class, wheretag, null, null);
			}
			if (upver != null) {
				Where wheretag = Where.create();
				wheretag.eq(TdsAccountTag::getVermark, upver.getId());
				wheretag.eq(TdsAccountTag::getRq, upver.getRq());
				uptagList = srv.queryData(TdsAccountTag.class, wheretag, null, null);
			}
		}

		// op add添加 del删除 upd
		if ("add".equals(op)) {// 添加仪表
			Map<String, List<Map<String, String>>> clsMap = getTagParentInfo(tag.getUnitCode());
			if (ver != null) {
				tag.setId(TMUID.getUID());
				tag.setRq(currDay);
				tag.setVermark(ver.getId());
				tag.setUnitName(unitName);
				// 单元、设备、核算对象名称需要获取
				addClsInfo(tag, clsMap);

				srv.insert(tag);
			} else {
				// 创建版本
				TdsAccountTagVersion v = new TdsAccountTagVersion();
				v.setId(TMUID.getUID());
				v.setRq(currDay);
				v.setOccuTime(nd);
				v.setUnitcode(tag.getUnitCode());
				v.setTmused(1);
				srv.insert(v);

				List<TdsAccountTag> addList = new ArrayList<TdsAccountTag>();
				if (StringUtils.isNotEmpty(uptagList)) {
					for (TdsAccountTag vertag : uptagList) {
						vertag.setId(TMUID.getUID());
						vertag.setVermark(v.getId());
						vertag.setRq(v.getRq());
						addList.add(vertag);
					}
					tag.setId(TMUID.getUID());
					tag.setRq(currDay);
					tag.setVermark(ver.getId());
					tag.setUnitName(unitName);
					// 单元、设备、核算对象名称需要获取
					addClsInfo(tag, clsMap);

					addList.add(tag);

					srv.insertBatch(addList);
				} else {// 新核算对象，只添加了一个采集点的情况
					tag.setId(TMUID.getUID());
					tag.setRq(currDay);
					tag.setVermark(v.getId());
					tag.setUnitName(unitName);
					// 单元、设备、核算对象名称需要获取
					addClsInfo(tag, clsMap);

					srv.insert(tag);
				}

			}
		} else if ("del".equals(op)) {// 删除仪表
			if (ver != null) {
				if (StringUtils.isNotEmpty(tagList)) {
					TdsAccountTag delTag = null;
					for (TdsAccountTag vertag : tagList) {
						if (vertag.getTagid().equals(tag.getTagid())) {
							delTag = vertag;
							break;
						}
					}
					if (delTag != null) {
						srv.deleteById(delTag);
					}
				}
			} else {
				// 创建版本
				TdsAccountTagVersion v = new TdsAccountTagVersion();
				v.setId(TMUID.getUID());
				v.setRq(currDay);
				v.setOccuTime(nd);
				v.setUnitcode(tag.getUnitCode());
				v.setTmused(1);
				srv.insert(v);

				List<TdsAccountTag> addList = new ArrayList<TdsAccountTag>();
				if (StringUtils.isNotEmpty(uptagList)) {
					for (TdsAccountTag vertag : uptagList) {
						if (vertag.getTagid().equals(tag.getTagid())) {
							continue;
						}
						vertag.setId(TMUID.getUID());
						vertag.setVermark(v.getId());
						vertag.setRq(v.getRq());
						addList.add(vertag);
					}
					addList.add(tag);

					srv.insertBatch(addList);
				}
			}
		} else {// 修改
			Map<String, List<Map<String, String>>> clsMap = getTagParentInfo(tag.getUnitCode());
			if (ver != null) {
				TdsAccountTag upTag = null;
				if (StringUtils.isNotEmpty(tagList)) {
					for (TdsAccountTag vertag : tagList) {
						if (vertag.getTagid().equals(tag.getTagid())) {
							upTag = vertag;
							break;
						}
					}
					if (upTag != null) {
						upTag.setTagname(tag.getTagname());
						upTag.setTagnumber(tag.getTagnumber());
						upTag.setSdunit(tag.getSdunit());
						upTag.setUpLimit(tag.getUpLimit());
						upTag.setLowerLimit(tag.getLowerLimit());
						upTag.setProcessUnitName(tag.getProcessUnitName());
						upTag.setProductName(tag.getProductName());
						upTag.setSamplingPoint(tag.getSamplingPoint());
						upTag.setAnalysisName(tag.getAnalysisName());
						upTag.setAnalysisSubName(tag.getAnalysisSubName());
						upTag.setDecimalDegit(tag.getDecimalDegit());
						// 设备默认值,手动输入控件类型
						upTag.setDeviceDefaultVal(tag.getDeviceDefaultVal() == null ? "" : tag.getDeviceDefaultVal());
						upTag.setDefaultVals(tag.getDefaultVals() == null ? "" : tag.getDefaultVals());
						upTag.setMultiSelectDisplayMode(tag.getMultiSelectDisplayMode() == null ? "" : tag.getMultiSelectDisplayMode());
						upTag.setCopyAddDefaultMode(tag.getCopyAddDefaultMode() == null ? "" : tag.getCopyAddDefaultMode());
						addClsInfo(upTag, clsMap);
						srv.updateById(upTag);
					}
				}
				if (upTag == null) {
					tag.setId(TMUID.getUID());
					tag.setRq(currDay);
					tag.setVermark(ver.getId());
					tag.setUnitName(unitName);
					// 单元、设备、核算对象名称需要获取
					addClsInfo(tag, clsMap);
					srv.insert(tag);
				}
			} else {
				// 创建版本
				TdsAccountTagVersion v = new TdsAccountTagVersion();
				v.setId(TMUID.getUID());
				v.setRq(currDay);
				v.setOccuTime(nd);
				v.setUnitcode(tag.getUnitCode());
				v.setTmused(1);
				srv.insert(v);

				List<TdsAccountTag> addList = new ArrayList<TdsAccountTag>();
				if (StringUtils.isNotEmpty(uptagList)) {
					for (TdsAccountTag vertag : uptagList) {
						if (vertag.getTagid().equals(tag.getTagid())) {
							continue;
						}
						vertag.setId(TMUID.getUID());
						vertag.setVermark(v.getId());
						vertag.setRq(v.getRq());
						addList.add(vertag);
					}
					addList.add(tag);

					srv.insertBatch(addList);
				} else {
					tag.setId(TMUID.getUID());
					tag.setRq(currDay);
					tag.setVermark(v.getId());
					tag.setUnitName(unitName);
					// 单元、设备、核算对象名称需要获取
					addClsInfo(tag, clsMap);

					srv.insert(tag);
				}
			}

		}
	}

	/**
	 * @category 批量同步仪表新
	 * @param tlist    采集点信息
	 * @param unitCode 核算对象
	 * @param op       add添加 del删除 upd更新
	 */
	public void syncBatchTaginfo(List<TdsAccountTag> tlist, String unitCode, String op) {
		// 进行初始化
		accountSrv.initTagInfo();

		Date nd = DateTimeUtils.getNowDate();
		String currDay = DateTimeUtils.getNowDateStr().substring(0, 10);// 当前日期
		TdsAccountTagVersion ver = null;
		TdsAccountTagVersion upver = null;
		List<TdsAccountTag> tagList = null;
		List<TdsAccountTag> uptagList = null;

		String unitName = getUnitName(unitCode);

		// 获取全部版本
		Where wherever = Where.create();
		wherever.eq(TdsAccountTagVersion::getUnitcode, unitCode);
		Order order = Order.create();
		order.order(TdsAccountTagVersion::getRq);
		List<TdsAccountTagVersion> tagVerList = srv.queryData(TdsAccountTagVersion.class, wherever, order, null);

		if (StringUtils.isNotEmpty(tagVerList)) {
			// 根据日期获取对应版本
			upver = null;
			for (TdsAccountTagVersion v : tagVerList) {
				if (v.getRq().equals(currDay)) {
					ver = v;
					break;
				} else {
					upver = v;
				}
			}
			if (ver != null) {
				Where wheretag = Where.create();
				wheretag.eq(TdsAccountTag::getVermark, ver.getId());
				wheretag.eq(TdsAccountTag::getRq, currDay);
				tagList = srv.queryData(TdsAccountTag.class, wheretag, null, null);
			}
			if (upver != null) {
				Where wheretag = Where.create();
				wheretag.eq(TdsAccountTag::getVermark, upver.getId());
				wheretag.eq(TdsAccountTag::getRq, upver.getRq());
				uptagList = srv.queryData(TdsAccountTag.class, wheretag, null, null);
			}
		}

		// op add添加 del删除 upd
		if ("add".equals(op)) {// 添加仪表
			Map<String, List<Map<String, String>>> clsMap = getTagParentInfo(unitCode);
			List<TdsAccountTag> addList = new ArrayList<TdsAccountTag>();
			if (ver != null) {
				for (TdsAccountTag tag : tlist) {
					tag.setId(TMUID.getUID());
					tag.setRq(currDay);
					tag.setVermark(ver.getId());
					tag.setUnitName(unitName);
					// 设备默认值,手动输入控件类型
					tag.setDeviceDefaultVal(tag.getDeviceDefaultVal());
					tag.setDefaultVals(tag.getDefaultVals());
					tag.setMultiSelectDisplayMode(tag.getMultiSelectDisplayMode());
					tag.setCopyAddDefaultMode(tag.getCopyAddDefaultMode());
					// 单元、设备、核算对象名称需要获取
					addClsInfo(tag, clsMap);
					addList.add(tag);
				}
				srv.insertBatch(addList);

				String preKey = unitCode + "_" + currDay;
				deleteRedis(preKey, unitCode);
			} else {
				syncBatchTagSort(unitCode);
				// //创建版本
				// TdsAccountTagVersion v = new TdsAccountTagVersion();
				// v.setId(TMUID.getUID());
				// v.setRq(currDay);
				// v.setOccuTime(nd);
				// v.setUnitcode(unitCode);
				// v.setTmused(1);
				// srv.insert(v);
				//
				// if(StringUtils.isNotEmpty(uptagList)) {
				// for (TdsAccountTag vertag : uptagList) {
				// vertag.setId(TMUID.getUID());
				// vertag.setVermark(v.getId());
				// vertag.setRq(v.getRq());
				// addList.add(vertag);
				// }
				// for (TdsAccountTag tag : tlist) {
				// tag.setId(TMUID.getUID());
				// tag.setRq(currDay);
				// tag.setVermark(v.getId());
				// tag.setUnitName(unitName);
				// //单元、设备、核算对象名称需要获取
				// addClsInfo(tag, clsMap);
				// addList.add(tag);
				// }
				// srv.insertBatch(addList);
				// }else {//新核算对象，只添加采集点的情况
				// for (TdsAccountTag tag : tlist) {
				// tag.setId(TMUID.getUID());
				// tag.setRq(currDay);
				// tag.setVermark(v.getId());
				// tag.setUnitName(unitName);
				// //单元、设备、核算对象名称需要获取
				// addClsInfo(tag, clsMap);
				// addList.add(tag);
				// }
				// srv.insertBatch(addList);
				// }
			}
		} else if ("del".equals(op)) {// 删除仪表
			if (ver != null) {
				if (StringUtils.isNotEmpty(tagList)) {
					List<String> delTagIds = new ArrayList<String>();
					for (TdsAccountTag obj : tlist) {
						delTagIds.add(obj.getTagid());
					}
					List<TdsAccountTag> delTagList = new ArrayList<TdsAccountTag>();
					for (TdsAccountTag vertag : tagList) {
						if (delTagIds.contains(vertag.getTagid())) {
							delTagList.add(vertag);
						}
					}
					if (StringUtils.isNotEmpty(delTagList)) {
						srv.deleteByIdBatch(delTagList);
					}
				}
				String preKey = unitCode + "_" + currDay;
				deleteRedis(preKey, unitCode);
			} else {
				syncBatchTagSort(unitCode);
				// //创建版本
				// TdsAccountTagVersion v = new TdsAccountTagVersion();
				// v.setId(TMUID.getUID());
				// v.setRq(currDay);
				// v.setOccuTime(nd);
				// v.setUnitcode(unitCode);
				// v.setTmused(1);
				// srv.insert(v);
				//
				// List<TdsAccountTag> addList = new ArrayList<TdsAccountTag>();
				// if(StringUtils.isNotEmpty(uptagList)) {
				// List<String> tagIds = new ArrayList<String>();
				// for (TdsAccountTag obj : tlist) {
				// tagIds.add(obj.getTagid());
				// }
				//
				// for (TdsAccountTag vertag : uptagList) {
				// if(tagIds.contains(vertag.getTagid())) {
				// continue;
				// }
				// vertag.setId(TMUID.getUID());
				// vertag.setVermark(v.getId());
				// vertag.setRq(v.getRq());
				// addList.add(vertag);
				// }
				// if(StringUtils.isNotEmpty(addList)) {
				// srv.insertBatch(addList);
				// }
				// }
			}
		} else {// 修改
			Map<String, List<Map<String, String>>> clsMap = getTagParentInfo(unitCode);

			Map<String, TdsAccountTag> tagMap = new HashMap<String, TdsAccountTag>();
			for (TdsAccountTag obj : tlist) {
				tagMap.put(obj.getTagid(), obj);
			}

			if (ver != null) {
				if (StringUtils.isNotEmpty(tagList)) {
					List<TdsAccountTag> updList = new ArrayList<TdsAccountTag>();
					for (TdsAccountTag vertag : tagList) {
						if (tagMap.containsKey(vertag.getTagid())) {
							TdsAccountTag tag = tagMap.get(vertag.getTagid());

							vertag.setTagname(tag.getTagname());
							vertag.setDatasource(tag.getDatasource());
							vertag.setTagnumber(tag.getTagnumber());
							vertag.setSdunit(tag.getSdunit());
							vertag.setUpLimit(tag.getUpLimit());
							vertag.setLowerLimit(tag.getLowerLimit());
							vertag.setProcessUnitName(tag.getProcessUnitName());
							vertag.setProductName(tag.getProductName());
							vertag.setSamplingPoint(tag.getSamplingPoint());
							vertag.setAnalysisName(tag.getAnalysisName());
							vertag.setAnalysisSubName(tag.getAnalysisSubName());
							// vertag.setTmsort(tag.getTmsort());//由台账仪表版本进行排序，不更新采集点排序字段
							vertag.setUnitName(unitName);
							vertag.setCtype(tag.getCtype());
							// 单元、设备、核算对象名称需要获取
							vertag.setTagpid(tag.getTagpid());
							vertag.setDecimalDegit(tag.getDecimalDegit());

							vertag.setAlign(tag.getAlign());
							vertag.setIswriteinput(tag.getIswriteinput());
							vertag.setControltype(tag.getControltype());
							vertag.setCombinitkey(tag.getCombinitkey());
							vertag.setCombinitval(tag.getCombinitval());
							vertag.setDefaultval(tag.getDefaultval());

							// 设备默认值,手动输入控件类型
							vertag.setDeviceDefaultVal(
									tag.getDeviceDefaultVal() == null ? "" : tag.getDeviceDefaultVal());
							vertag.setDefaultVals(tag.getDefaultVals() == null ? "" : tag.getDefaultVals());
							vertag.setMultiSelectDisplayMode(tag.getMultiSelectDisplayMode() == null ? "" : tag.getMultiSelectDisplayMode());
							vertag.setCopyAddDefaultMode(tag.getCopyAddDefaultMode() == null ? "" : tag.getCopyAddDefaultMode());

							addClsInfo(vertag, clsMap);

							updList.add(vertag);
						}
					}
					if (StringUtils.isNotEmpty(updList)) {
						srv.updateBatch(updList, 100);
					}
					// 修改的仪表未添加过
					for (TdsAccountTag vertag : tagList) {
						if (tagMap.containsKey(vertag.getTagid())) {
							tagMap.remove(vertag.getTagid());
						}
					}
				}
				if (!tagMap.isEmpty()) {
					// List<TdsAccountTag> addList = new ArrayList<TdsAccountTag>();
					// for (TdsAccountTag tag : tagMap.values()) {
					// tag.setId(TMUID.getUID());
					// tag.setVermark(ver.getId());
					// tag.setRq(ver.getRq());
					// tag.setUnitName(unitName);
					// //单元、设备、核算对象名称需要获取
					// addClsInfo(tag, clsMap);
					// addList.add(tag);
					// }
					// srv.insertBatch(addList);
					syncBatchTagSort(unitCode);
				} else {
					String preKey = unitCode + "_" + currDay;
					deleteRedis(preKey, unitCode);
				}
			} else {
				syncBatchTagSort(unitCode);
				// //创建版本
				// TdsAccountTagVersion v = new TdsAccountTagVersion();
				// v.setId(TMUID.getUID());
				// v.setRq(currDay);
				// v.setOccuTime(nd);
				// v.setUnitcode(unitCode);
				// v.setTmused(1);
				// srv.insert(v);
				//
				// List<TdsAccountTag> addList = new ArrayList<TdsAccountTag>();
				// if(StringUtils.isNotEmpty(uptagList)) {
				// for (TdsAccountTag vertag : uptagList) {
				// vertag.setId(TMUID.getUID());
				// vertag.setVermark(v.getId());
				// vertag.setRq(v.getRq());
				//
				// if(tagMap.containsKey(vertag.getTagid())) {
				// TdsAccountTag tag = tagMap.get(vertag.getTagid());
				//
				// vertag.setTagname(tag.getTagname());
				// vertag.setDatasource(tag.getDatasource());
				// vertag.setTagnumber(tag.getTagnumber());
				// vertag.setSdunit(tag.getSdunit());
				// vertag.setUpLimit(tag.getUpLimit());
				// vertag.setLowerLimit(tag.getLowerLimit());
				// vertag.setProcessUnitName(tag.getProcessUnitName());
				// vertag.setProductName(tag.getProductName());
				// vertag.setSamplingPoint(tag.getSamplingPoint());
				// vertag.setAnalysisName(tag.getAnalysisName());
				// vertag.setAnalysisSubName(tag.getAnalysisSubName());
				// vertag.setUnitName(unitName);
				// vertag.setCtype(tag.getCtype());
				// //单元、设备、核算对象名称需要获取
				// vertag.setTagpid(tag.getTagpid());
				// addClsInfo(vertag, clsMap);
				//
				// addList.add(vertag);
				// }else {
				// addList.add(vertag);
				// }
				// }
				// //修改的仪表未添加过
				// for (TdsAccountTag vertag : uptagList) {
				// if(tagMap.containsKey(vertag.getTagid())) {
				// tagMap.remove(vertag.getTagid());
				// }
				// }
				// if(!tagMap.isEmpty()) {
				// for (TdsAccountTag tag : tagMap.values()) {
				// tag.setId(TMUID.getUID());
				// tag.setVermark(v.getId());
				// tag.setRq(v.getRq());
				// tag.setUnitName(unitName);
				// //单元、设备、核算对象名称需要获取
				// addClsInfo(tag, clsMap);
				//
				// addList.add(tag);
				// }
				// }
				//
				// srv.insertBatch(addList);
				// }else {
				// for (TdsAccountTag tag : tagMap.values()) {
				// tag.setId(TMUID.getUID());
				// tag.setVermark(v.getId());
				// tag.setRq(v.getRq());
				// tag.setUnitName(unitName);
				// //单元、设备、核算对象名称需要获取
				// addClsInfo(tag, clsMap);
				//
				// addList.add(tag);
				// }
				//
				// srv.insertBatch(addList);
				// }
			}

		}
	}

	// 添加仪表分类信息
	private void addClsInfo(TdsAccountTag tag, Map<String, List<Map<String, String>>> clsMap) {
		List<Map<String, String>> clist = clsMap.get(tag.getTagpid());
		if (StringUtils.isNotEmpty(clist)) {
			if (clist.size() >= 3) {
				Map<String, String> zonemap = clist.get(clist.size() - 3);
				Map<String, String> devmap = clist.get(clist.size() - 2);
				Map<String, String> tagmap = clist.get(clist.size() - 1);
				tag.setBelongZone(zonemap.get("name"));
				tag.setBelongDev(devmap.get("name"));
				tag.setBelongTag(tagmap.get("name"));
				tag.setTagpname(tag.getBelongTag());
			} else if (clist.size() == 2) {
				Map<String, String> zonemap = clist.get(clist.size() - 2);
				Map<String, String> devmap = clist.get(clist.size() - 1);
				tag.setBelongZone(zonemap.get("name"));
				tag.setBelongDev(devmap.get("name"));
				tag.setTagpname(tag.getBelongDev());
			} else if (clist.size() == 1) {
				Map<String, String> zonemap = clist.get(clist.size() - 1);
				tag.setBelongZone(zonemap.get("name"));
				tag.setTagpname(tag.getBelongZone());
			}

			for (int i = 0, il = clist.size(); i < il; i++) {
				Map<String, String> _map = clist.get(i);
				String name = _map.get("name");
				if (i == 0) {
					tag.setClass1(name);
				} else if (i == 1) {
					tag.setClass2(name);
				} else if (i == 2) {
					tag.setClass3(name);
				} else if (i == 3) {
					tag.setClass4(name);
				} else if (i == 4) {
					tag.setClass5(name);
				} else if (i == 5) {
					tag.setClass6(name);
				} else if (i == 6) {
					tag.setClass7(name);
				} else if (i == 7) {
					tag.setClass8(name);
				} else if (i == 8) {
					tag.setClass9(name);
				} else if (i == 9) {
					tag.setClass10(name);
				}
			}
		}

	}

	// 获取分类信息方法
	private Map<String, List<Map<String, String>>> getTagParentInfo(String unitCode) {

		Map<String, List<Map<String, String>>> rmap = new HashMap<String, List<Map<String, String>>>();

		// 获取分类数据，用于构建仪表单元、设备
		String sql = "select ID, PID, NAME from COSTUNITSAMPLECLASS where UNITID=? and TMUSED=1 order by TMSORT";
		List<Object> plist = new ArrayList<Object>();
		plist.add(unitCode);
		List<Map<String, Object>> list = srv.queryListMap(sql, plist.toArray());

		Map<String, List<Map<String, Object>>> clsMap = new HashMap<String, List<Map<String, Object>>>();// Map<pid,
																											// 分类列表>
		for (Map<String, Object> map : list) {
			String pid = String.valueOf(map.get("PID"));
			if (clsMap.containsKey(pid)) {
				clsMap.get(pid).add(map);
			} else {
				List<Map<String, Object>> _list = new ArrayList<Map<String, Object>>();
				_list.add(map);
				clsMap.put(pid, _list);
			}
		}

		List<Map<String, Object>> clslist = clsMap.get("root");// 获取分类节点
		if (StringUtils.isNotEmpty(clslist)) {
			for (Map<String, Object> map : clslist) {
				// List<String> nameList = new ArrayList<String>();
				// List<String> idList = new ArrayList<String>();
				// nameList.add(String.valueOf(map.get("NAME")));
				// idList.add(String.valueOf(map.get("ID")));
				List<Map<String, String>> inList = new ArrayList<Map<String, String>>();
				Map<String, String> _map = new HashMap<String, String>();
				String id = String.valueOf(map.get("ID"));
				String name = String.valueOf(map.get("NAME"));
				_map.put("id", id);
				_map.put("name", name);
				inList.add(_map);

				rmap.put(id, inList);
				setParentData(map, clsMap, rmap);
			}
		}

		return rmap;
	}

	// 查分类关系递归方法
	private void setParentData(Map<String, Object> map, Map<String, List<Map<String, Object>>> clsMap,
			Map<String, List<Map<String, String>>> rmap) {
		String id = String.valueOf(map.get("ID"));// 数据标识
		List<Map<String, Object>> clist = clsMap.get(id);// 子数据列表
		if (StringUtils.isNotEmpty(clist)) {
			for (Map<String, Object> vo : clist) {
				String iid = String.valueOf(map.get("ID"));
				String name = String.valueOf(map.get("NAME"));
				// //获取父名称信息，同时保存子信息
				List<Map<String, String>> plist = rmap.get(id);
				if (StringUtils.isNotEmpty(plist)) {
					List<Map<String, String>> inList = ObjUtils.copyTo(plist, List.class);
					Map<String, String> _map = new HashMap<String, String>();
					_map.put("id", iid);
					_map.put("name", name);
					inList.add(_map);
					rmap.put(iid, inList);
				} else {
					List<Map<String, String>> inList = new ArrayList<Map<String, String>>();
					Map<String, String> _map = new HashMap<String, String>();
					_map.put("id", iid);
					_map.put("name", name);
					inList.add(_map);
					rmap.put(iid, inList);
				}
				setParentData(vo, clsMap, rmap);
			}
		}
	}

	private String getUnitName(String unitCode) {
		String unitName = "";

		// 获取核算单元
		String sql = "select NAME from COSTUINT where ID=?";
		List<Object> plist = new ArrayList<Object>();
		plist.add(unitCode);
		List<Map<String, Object>> ulist = srv.queryListMap(sql, plist.toArray());
		if (StringUtils.isNotEmpty(ulist)) {
			Object o = ulist.get(0).get("NAME");
			if (o != null) {
				unitName = String.valueOf(o);
			}
		}
		return unitName;

	}

	@Override
	/**
	 * @category 获取核算对象信息
	 * @param unitCode
	 * @return
	 */
	public Map<String, Object> getUnitInfoMap(String unitCode) {

		String sql = "select * from COSTUINT where id=?";
		List<Object> param = new ArrayList<Object>();
		param.add(unitCode);
		List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
		if (StringUtils.isNotEmpty(list)) {
			return list.get(0);
		}
		return new HashMap<String, Object>(0);

	}

	@Override
	public void syncBatchTagSort(String unitCode) {

		// 进行初始化
		accountSrv.initTagInfo();

		Date nd = DateTimeUtils.getNowDate();
		String currDay = DateTimeUtils.getNowDateStr().substring(0, 10);// 当前日期
		String unitName = getUnitName(unitCode);

		TdsAccountTagVersion ver = accuntCurrVer(unitCode, currDay);

		List<TdsAccountTag> delList = null;

		if (ver == null) {
			// 创建版本
			ver = new TdsAccountTagVersion();
			ver.setId(TMUID.getUID());
			ver.setRq(currDay);
			ver.setOccuTime(nd);
			ver.setUnitcode(unitCode);
			ver.setTmused(1);
			srv.insert(ver);
		} else {// 有版本数据，需清除原数据 TODO
			Where wheretag = Where.create();
			wheretag.eq(TdsAccountTag::getVermark, ver.getId());
			wheretag.eq(TdsAccountTag::getRq, currDay);
			delList = srv.queryData(TdsAccountTag.class, wheretag, null, null);

			String preKey = unitCode + "_" + currDay;
			deleteRedis(preKey, unitCode);
		}
		String verMark = ver.getId();

		List<TdsAccountTag> addTagList = synTagSort(unitCode, verMark, currDay, unitName);// 同步顺序的新仪表

		if (StringUtils.isNotEmpty(delList)) {
			srv.deleteByIdBatch(delList);
		}
		if (StringUtils.isNotEmpty(addTagList)) {
			srv.insertBatch(addTagList, 500);
		}
	}

	private void deleteRedis(String preKey, String unitCode) {
		// 判断是否有redis,有就清除，需要查一下相关的自定义台账，如果没有，只清除默认台账的
		Where wheref = Where.create();
		wheref.eq(TdsAccountForm::getTmused, 1);
		wheref.like(TdsAccountForm::getUnitCode, unitCode);
		List<TdsAccountForm> flist = srv.queryData(TdsAccountForm.class, wheref, null, null);

		List<String> listKey = new ArrayList<String>();
		if (StringUtils.isNotEmpty(flist)) {
			for (TdsAccountForm form : flist) {
				String accountId = form.getId();
				String key = preKey + "_" + accountId + "_2";
				String key2 = preKey + "_" + accountId + "_3";
				listKey.add(key);
				listKey.add(key2);
			}
		} else {
			String key = preKey + "__2";
			String key2 = preKey + "__3";
			listKey.add(key);
			listKey.add(key2);
		}
		redis.hDelete(accountKey, listKey.toArray());
	}

	private List<TdsAccountTag> synTagSort(String unitCode, String verMark, String currDay, String unitName) {
		// 获取分类数据，用于构建仪表单元、设备
		String sql = "select ID, PID, NAME, CTYPE, UNITID from COSTUNITSAMPLECLASS where UNITID='" + unitCode
				+ "' and TMUSED=1 order by UNITID, TMSORT";
		List<Map<String, Object>> list = srv.queryListMap(sql, null);
		Map<String, List<Map<String, Object>>> clsMap = new HashMap<String, List<Map<String, Object>>>();// Map<pid,
																											// 分类列表>
		for (Map<String, Object> map : list) {
			String pid = String.valueOf(map.get("PID"));
			if (clsMap.containsKey(pid)) {
				clsMap.get(pid).add(map);
			} else {
				List<Map<String, Object>> _list = new ArrayList<Map<String, Object>>();
				_list.add(map);
				clsMap.put(pid, _list);
			}
		}

		// 只同步控制指标、化验分析(lims)指标, ctype 1成本指标（核算功能内部使用）2控制（平稳率）指标 3 lims指标，这里只同步2和3
		// 实际应用时，恢复过滤TODO
		// sql = "select ID, PID, UNITID, NAME, TAGNUMBER,DATASOURCE,SDUNIT, CTYPE,
		// SOURCEYPE, INDEXRANGEUPPER as uplimit, INDEXRANGELOWER as lowlimit,
		// POINTCOUNTLEDGER as bit,"
		// + " PROCESSUNITNAME, PRODUCTNAME, SAMPLINGPOINT, ANALYSISNAME,
		// ANALYSISSUBNAME,ISWRITEBACKINFLUXDB "
		// + "from COSTUNITSAMPLEDOT where TMUSED=1 and UNITID='"+unitCode+"' and CTYPE
		// in ('2','3') and (ISSHOWLEDGER is null or ISSHOWLEDGER=1) order by UNITID,
		// TMSORT";//and ISSHOWLEDGER=1
		sql = "select * from COSTUNITSAMPLEDOT where TMUSED=1 and UNITID='" + unitCode
				+ "' and CTYPE in ('2','3') and (ISSHOWLEDGER is null or ISSHOWLEDGER=1) order by UNITID, TMSORT";
		list = srv.queryListMap(sql, null);
		Map<String, List<Map<String, Object>>> tagMap = new HashMap<String, List<Map<String, Object>>>();// Map<pid,
																											// 仪表列表>
		for (Map<String, Object> map : list) {
			String pid = String.valueOf(map.get("PID"));
			if (tagMap.containsKey(pid)) {
				tagMap.get(pid).add(map);
			} else {
				List<Map<String, Object>> _list = new ArrayList<Map<String, Object>>();
				_list.add(map);
				tagMap.put(pid, _list);
			}
		}

		List<Map<String, Object>> clslist = clsMap.get("root");// 获取分类节点
		if (StringUtils.isNotEmpty(clslist)) {
			List<TdsAccountTag> addList = new ArrayList<TdsAccountTag>();

			sortno = 1;
			Map<String, List<String>> clsNameMap = new HashMap<String, List<String>>();
			for (Map<String, Object> map : clslist) {

				List<String> nameList = new ArrayList<String>();
				nameList.add(String.valueOf(map.get("NAME")));
				clsNameMap.put(String.valueOf(map.get("ID")), nameList);
				setTagData(map, clsMap, tagMap, addList, clsNameMap, verMark, currDay, unitName);
			}

			if (addList.size() > 0) {
				return addList;// 仪表数据
			}

		}

		return null;
	}

	private void setTagData(Map<String, Object> map, Map<String, List<Map<String, Object>>> clsMap,
			Map<String, List<Map<String, Object>>> tagMap,
			List<TdsAccountTag> addList, Map<String, List<String>> clsNameMap, String verMark, String currDay,
			String unitName) {

		String id = String.valueOf(map.get("ID"));
		List<Map<String, Object>> clist = clsMap.get(id);
		if (StringUtils.isNotEmpty(clist)) {
			for (Map<String, Object> vo : clist) {
				String itemid = String.valueOf(vo.get("ID"));
				String name = String.valueOf(vo.get("NAME"));
				// 获取父名称信息，同时保存子信息
				List<String> nameList = clsNameMap.get(id);
				if (StringUtils.isNotEmpty(nameList)) {
					List<String> tlist = new ArrayList<String>();
					for (String cname : nameList) {
						tlist.add(cname);
					}
					tlist.add(name);
					clsNameMap.put(itemid, tlist);
				} else {
					nameList = new ArrayList<String>();
					nameList.add(name);
					clsNameMap.put(itemid, nameList);
				}

				setTagData(vo, clsMap, tagMap, addList, clsNameMap, verMark, currDay, unitName);
			}
		}

		List<Map<String, Object>> tagList = tagMap.get(id);
		if (StringUtils.isNotEmpty(tagList)) {
			for (Map<String, Object> tag : tagList) {
				// ID, PID, UNITID, NAME, TAGNUMBER,DATASOURCE,SDUNIT, CTYPE, SOURCEYPE,
				// uplimit, lowlimit,bit
				String pid = String.valueOf(tag.get("PID"));

				// 带入单元、设备名称（分类名）
				List<String> nameList = clsNameMap.get(pid);
				String zone = "", dev = "", tname = "", tagpname = "";
				if (StringUtils.isNotEmpty(nameList)) {
					if (nameList.size() == 1) {
						zone = nameList.get(0);
						tagpname = zone;
					} else if (nameList.size() >= 3) {
						zone = nameList.get(nameList.size() - 3);
						dev = nameList.get(nameList.size() - 2);
						tname = nameList.get(nameList.size() - 1);
						tagpname = tname;
					} else if (nameList.size() >= 2) {
						zone = nameList.get(nameList.size() - 2);
						dev = nameList.get(nameList.size() - 1);
						tagpname = dev;
					}
				}

				String tagid = String.valueOf(tag.get("ID"));
				String tagpid = String.valueOf(tag.get("PID") == null ? "" : tag.get("PID"));
				String unitCode = String.valueOf(tag.get("UNITID"));
				String NAME = String.valueOf(tag.get("NAME") == null ? "" : tag.get("NAME"));
				String TAGNUMBER = String.valueOf(tag.get("TAGNUMBER") == null ? "" : tag.get("TAGNUMBER"));
				String DATASOURCE = String.valueOf(tag.get("DATASOURCE") == null ? "" : tag.get("DATASOURCE"));
				String SDUNIT = String.valueOf(tag.get("SDUNIT") == null ? "" : tag.get("SDUNIT"));
				String SOURCEYPE = String.valueOf(tag.get("SOURCEYPE") == null ? "" : tag.get("SOURCEYPE"));
				String CTYPE = String.valueOf(tag.get("CTYPE"));// 只用到2和3 ,同步过来的可能有其他字符

				// PROCESSUNIT, PRODUCTNAME, SAMPLINGPOINT, ANALYSISNAME, ITEMNAME
				String PROCESSUNITNAME = String
						.valueOf(tag.get("PROCESSUNITNAME") == null ? "" : tag.get("PROCESSUNITNAME"));
				String PRODUCTNAME = String.valueOf(tag.get("PRODUCTNAME") == null ? "" : tag.get("PRODUCTNAME"));
				String SAMPLINGPOINT = String.valueOf(tag.get("SAMPLINGPOINT") == null ? "" : tag.get("SAMPLINGPOINT"));
				String ANALYSISNAME = String.valueOf(tag.get("ANALYSISNAME") == null ? "" : tag.get("ANALYSISNAME"));
				String ANALYSISSUBNAME = String
						.valueOf(tag.get("ANALYSISSUBNAME") == null ? "" : tag.get("ANALYSISSUBNAME"));

				Double upLimit = null;// tag.get("uplimit");
				Double lowerLimit = null;// String.valueOf(tag.get("lowlimit"));
				Integer decimalDegit = null;// String.valueOf(tag.get("bit"));

				String upstr = String.valueOf(tag.get("INDEXRANGEUPPER"));
				String lowstr = String.valueOf(tag.get("INDEXRANGELOWER"));
				String bitstr = String.valueOf(tag.get("POINTCOUNTLEDGER"));
				if (Coms.judgeDouble(upstr)) {
					upLimit = Double.parseDouble(upstr);
				}
				if (Coms.judgeDouble(lowstr)) {
					lowerLimit = Double.parseDouble(lowstr);
				}
				if (Coms.judgeDouble(bitstr)) {
					decimalDegit = Integer.parseInt(bitstr);
				}
				String infux = String.valueOf(tag.get("ISWRITEBACKINFLUXDB"));

				Integer ISWRITEINPUT = tag.get("ISWRITEINPUT") == null ? null
						: Integer.parseInt(String.valueOf(tag.get("ISWRITEINPUT")));// 手工填写 1是
				Integer CONTROLTYPE = tag.get("CONTROLTYPE") == null ? null
						: Integer.parseInt(String.valueOf(tag.get("CONTROLTYPE")));// 组件类型
				String DEFAULTVAL = String.valueOf(tag.get("DEFAULTVAL") == null ? "" : tag.get("DEFAULTVAL"));// 默认值
				String COMBINITKEY = String.valueOf(tag.get("COMBINITKEY") == null ? "" : tag.get("COMBINITKEY"));// 下拉key
				String COMBINITVAL = String.valueOf(tag.get("COMBINITVAL") == null ? "" : tag.get("COMBINITVAL"));// 下拉内容

				// 设备默认值,手动输入控件类型
				String deviceDefaultVal = String
						.valueOf(tag.get("DEVICEDEFAULTVAL") == null ? "" : tag.get("DEVICEDEFAULTVAL"));
				String defaultVals = String
						.valueOf(tag.get("DEFAULTVALS") == null ? "" : tag.get("DEFAULTVALS"));
				String multiSelectDisplayMode = String
						.valueOf(tag.get("MULTISELECTDISPLAYMODE") == null ? "" : tag.get("MULTISELECTDISPLAYMODE"));
				String copyAddDefaultMode = String
						.valueOf(tag.get("COPYADDDEFAULTMODE") == null ? "" : tag.get("COPYADDDEFAULTMODE"));

				TdsAccountTag vo = new TdsAccountTag();
				vo.setId(TMUID.getUID());
				vo.setUnitCode(unitCode);
				vo.setUnitName(unitName);
				vo.setBelongZone(zone);
				vo.setBelongDev(dev);
				vo.setBelongTag(tname);
				vo.setVermark(verMark);
				vo.setTagid(tagid);
				vo.setTagpid(tagpid);
				vo.setTagpname(tagpname);
				vo.setTagname(NAME);
				vo.setTagnumber(TAGNUMBER);
				vo.setDatasource(DATASOURCE);
				vo.setSourceype(SOURCEYPE);
				vo.setCtype(CTYPE);// 仪表类型
				vo.setSdunit(SDUNIT);
				vo.setUpLimit(upLimit);
				vo.setLowerLimit(lowerLimit);
				vo.setDecimalDegit(decimalDegit);
				vo.setRq(currDay);
				vo.setWidth(getDefaultWidth(vo));
				vo.setIsWriteBackInfluxdb("1".equals(infux) ? 1 : 0);// 回写infuxdb

				vo.setProcessUnitName(PROCESSUNITNAME);// 5个用于limis数据的属性
				vo.setProductName(PRODUCTNAME);
				vo.setSamplingPoint(SAMPLINGPOINT);
				vo.setAnalysisName(ANALYSISNAME);
				vo.setAnalysisSubName(ANALYSISSUBNAME);

				vo.setEditMark(0);// 不可编辑
				vo.setAlign("right");// 右对齐
				vo.setTmused(1);
				vo.setTmsort(this.sortno++);

				vo.setIswriteinput(ISWRITEINPUT);
				vo.setControltype(CONTROLTYPE);
				vo.setDefaultval(DEFAULTVAL);
				vo.setCombinitkey(COMBINITKEY);
				vo.setCombinitval(COMBINITVAL);

				// 设备默认值,手动输入控件类型
				vo.setDeviceDefaultVal(deviceDefaultVal);
				vo.setDefaultVals(defaultVals);
				vo.setMultiSelectDisplayMode(multiSelectDisplayMode);
				vo.setCopyAddDefaultMode(copyAddDefaultMode);

				if (nameList.size() > 0) {
					for (int i = 0, il = nameList.size(); i < il; i++) {
						if (i == 0) {
							vo.setClass1(nameList.get(i));
						} else if (i == 1) {
							vo.setClass2(nameList.get(i));
						} else if (i == 2) {
							vo.setClass3(nameList.get(i));
						} else if (i == 3) {
							vo.setClass4(nameList.get(i));
						} else if (i == 4) {
							vo.setClass5(nameList.get(i));
						} else if (i == 5) {
							vo.setClass6(nameList.get(i));
						} else if (i == 6) {
							vo.setClass7(nameList.get(i));
						} else if (i == 7) {
							vo.setClass8(nameList.get(i));
						} else if (i == 8) {
							vo.setClass9(nameList.get(i));
						} else if (i == 9) {
							vo.setClass10(nameList.get(i));
						}
					}
				}

				addList.add(vo);
			}
		}
	}

	// 根据上下限计算台账仪表默认宽度
	public int getDefaultWidth(TdsAccountTag vo) {
		Boolean havePot = false;
		Integer bit = vo.getDecimalDegit();
		Double up = vo.getUpLimit();
		Double low = vo.getLowerLimit();

		int len = 0;
		if (up != null) {
			String s = Coms.formatNumber(up, "###########################0.###############################");
			int l = s.length();// String.valueOf((int) Math.floor(up)).length();
			if (l > len) {
				len = l;
			}
		}
		if (low != null) {
			String s = Coms.formatNumber(low, "###########################0.###############################");
			int l = s.length();
			String.valueOf((int) Math.floor(low)).length();
			if (l > len) {
				len = l;
			}
		}
		// if(bit!=null) {
		// if(bit > 0) {
		// len+=bit;
		// havePot = true;
		// }
		// }
		if (len > 0) {// 没有上下限，默认0
			Double pxWidth = 20d;// 一个数字大概多少像素 12 -> 15
			len = (int) (len * pxWidth);
			if (havePot) {
				len += 5;// 小数点大概多少像素
			}
		}

		return len;
	}

	private TdsAccountTagVersion accuntCurrVer(String unitCode, String currDay) {

		TdsAccountTagVersion ver = null;

		// 获取全部版本
		Where wherever = Where.create();
		wherever.eq(TdsAccountTagVersion::getUnitcode, unitCode);
		Order order = Order.create();
		order.order(TdsAccountTagVersion::getRq);
		List<TdsAccountTagVersion> tagVerList = srv.queryData(TdsAccountTagVersion.class, wherever, order, null);

		if (StringUtils.isNotEmpty(tagVerList)) {
			// 根据日期获取对应版本
			for (TdsAccountTagVersion v : tagVerList) {
				if (v.getRq().equals(currDay)) {
					ver = v;
					break;
				}
			}
		}
		return ver;
	}

	@Override
	public String getAaccuntVerRq(String unitCode, String sendRq) {

		TdsAccountTagVersion ver = null;

		// 获取全部版本
		Where wherever = Where.create();
		wherever.eq(TdsAccountTagVersion::getUnitcode, unitCode);
		Order order = Order.create();
		order.order(TdsAccountTagVersion::getRq);
		List<TdsAccountTagVersion> tagVerList = srv.queryData(TdsAccountTagVersion.class, wherever, order, null);

		if (StringUtils.isNotEmpty(tagVerList)) {
			for (TdsAccountTagVersion v : tagVerList) {
				if (sendRq.compareTo(v.getRq()) >= 0) {
					ver = v;
				} else {
					break;
				}
			}
		}
		return ver == null ? null : ver.getRq();
	}

	/**
	 * @category 根据核算对象（活动）获取功能模式 1采集点 or 0电子台账模型
	 *           采集点：由移动端获取数据，不自动生成时间点，可添加、修改、删除时间点数据（暂无确认）
	 *           电子台账模型：类似巡检，不自动生成时间点，由接口获取并生成，未到时间点不显示，有确认，确认时调用接口（同步数据确认）
	 */
	@Override
	public Map<String, String> getUnitAccountConf(String unitCode, String tdsAlias) {
		Map<String, String> rmap = new HashMap<String, String>();
		rmap.put("mode", "0");

		// 查询数据源模式，时间点通过核算对象设置判断；外部数据模式通过班组精益核算配置判断
		TdsAccountTime timeConf = accountSrv.getTimeConf(tdsAlias);// 时间配置
		if (timeConf != null) {
			if ("synchronize".equals(timeConf.getShowMode())) {// 移动端同步模式，（辽兴）时间点
				String sql = "select * from COSTUINT where id=?";
				List<Object> param = new ArrayList<Object>();
				param.add(unitCode);
				List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
				if (StringUtils.isNotEmpty(list)) {
					String mode = String.valueOf(list.get(0).get("isOntime"));// 定期
					rmap.put("mode", "1".equals(mode) ? "0" : "1");
				}
			} else if ("mobile".equals(timeConf.getShowMode())) {// 外部数据
				// 根据核算对象代码获取应用模式（只考虑一个核算对象情况），班组精益核算项目由调度生成采集点时间项目，直接读取数据，无添加删除功能
				String sql = "select recordType,recordid from JOBLIST_ACTIVITYPROPERTIES where id=?";
				List<Object> param = new ArrayList<Object>();
				param.add(unitCode);
				List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
				if (StringUtils.isNotEmpty(list)) {
					String mode = String.valueOf(list.get(0).get("recordid"));
					rmap.put("mode", "3".equals(mode) ? "1" : "0");
				}
			}
		}

		/*
		 * recordType
		 * map.put("1", "内置功能");
		 * map.put("2", "外部数据");
		 * map.put("0", "无");
		 * recordid
		 * map.put("1", "电子台账模型");
		 * map.put("2", "表单");
		 * map.put("3", "采集点");
		 */
		// //根据核算对象代码获取应用模式（只考虑一个核算对象情况），班组精益核算项目由调度生成采集点时间项目，直接读取数据，无添加删除功能
		// String sql = "select recordType,recordid from JOBLIST_ACTIVITYPROPERTIES
		// where id=?";
		// List<Object> param = new ArrayList<Object>();
		// param.add(unitCode);
		// List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
		// if(StringUtils.isNotEmpty(list)) {
		// String mode = String.valueOf(list.get(0).get("recordid"));
		// rmap.put("mode", "3".equals(mode)?"1":"0");
		// }
		// 查区域应用情况
		// param.clear();
		// sql = "";
		// list = srv.queryListMap(sql, param.toArray());
		// if(StringUtils.isNotEmpty(list)) {
		//
		// }
		return rmap;
	}

	/**
	 * @category 从外部接口获取采集点
	 *           张晋桐提供，查询已保存的数据，如果未保存过，直接获取核算对象（活动）采集点
	 * @param param
	 * @return
	 */
	@Override
	public List<TdsAccountMeter> getExtPotList(ApplyParams param) {
		List<TdsAccountMeter> rlist = new ArrayList<TdsAccountMeter>();

		String rq = param.getRq();
		String unitCode = param.getUnitCode();
		String itemCode = param.getItemCode();
		String bc = param.getBc();
		String sbsj = param.getSbsj();
		String xbsj = param.getXbsj();
		//在辽化现场不同机构使用相同台账模型时，需要使用机构编码进行数据隔离
		String teamId = param.getTeamId();

		if (!unitCode.equals(itemCode) || itemCode.indexOf(",") != -1) {// 多设备模式
			// List<String> deviceIds = Coms.StrToList(itemCode, ",");//多设备情况，暂时没有，后期完善，//接口
			// TODO
			String deviceIds = "'" + itemCode.replace(",", "','") + "'";
			String sql = "select COLLECT_POINT_ID, COLLECT_POINT, IPT_ID, input_comp_type " +
					" from ACCTOBJ_INPUTMX where TMUSED=1 and exists (select ID from ACCTOBJ_INPUT where BA_ID in("
					+ deviceIds + ") and INPUT_TIME between ? and ? and team_id = ? and ID=ACCTOBJ_INPUTMX.IPT_ID) " +
					" order by INPUT_TIME desc";
			List<Object> pobj = new ArrayList<Object>();
			pobj.add(DateTimeUtils.parseDateTime(sbsj));
			pobj.add(DateTimeUtils.parseDateTime(xbsj));
			pobj.add(teamId);
			List<Map<String, Object>> tlist = srv.queryListMap(sql, pobj.toArray());
			if (StringUtils.isNotEmpty(tlist)) {
				if (StringUtils.isNotEmpty(tlist)) {
					Set<String> tidlist = new HashSet<String>();
					for (Map<String, Object> map : tlist) {
						String tagid = String.valueOf(map.get("COLLECT_POINT_ID"));
						if (tidlist.contains(tagid)) {
							continue;
						}
						String showName = String.valueOf(map.get("COLLECT_POINT"));
						String type = String.valueOf(map.get("input_comp_type"));
						Integer itype = getType(type);

						TdsAccountMeter obj = new TdsAccountMeter();
						obj.setTagid(tagid);
						obj.setShowName(showName);
						obj.setControltype(itype);
						rlist.add(obj);

						tidlist.add(tagid);
					}
				}
			}

		} else {// 单活动模式
				// 接口 TODO
			String sql = "select COLLECT_POINT_ID, COLLECT_POINT, IPT_ID, input_comp_type,input_options " +
					" from ACCTOBJ_INPUTMX where TMUSED=1 and exists (select ID from ACCTOBJ_INPUT where TMUSED=1 and BA_ID =? and INPUT_TIME between ? and ? and team_id = ? and ID=ACCTOBJ_INPUTMX.IPT_ID) "
					+
					" order by INPUT_TIME desc";
			List<Object> pobj = new ArrayList<Object>();
			pobj.add(itemCode);
			pobj.add(DateTimeUtils.parseDateTime(sbsj));
			pobj.add(DateTimeUtils.parseDateTime(xbsj));
			pobj.add(teamId);
			List<Map<String, Object>> tlist = srv.queryListMap(sql, pobj.toArray());
			if (StringUtils.isNotEmpty(tlist)) {
				List<String> collectPointIds = tlist.stream().map(map -> String.valueOf(map.get("COLLECT_POINT_ID")))
						.collect(Collectors.toList());
				// 使用id列表查询 避免 in 方式
				StringBuffer dotSqlString = new StringBuffer();
				dotSqlString.append(
						"SELECT id,name,tmsort,combinitkey,combinitval FROM COSTUNITSAMPLEDOT A WHERE EXISTS (SELECT" +
								" 1 FROM " +
								"(VALUES ");
				int index = 0;
				for (String collectPointId : collectPointIds) {
					dotSqlString.append("('")
							.append(collectPointId)
							.append("')");
					if (index < collectPointIds.size() - 1) {
						dotSqlString.append(",");
					}
					index++;
				}
				dotSqlString.append(") AS ids(id) WHERE A.id = ids.id);");
				List<Map<String, Object>> dotList = srv.queryListMap(dotSqlString.toString(), null);
				Map<String, String> dotMap = dotList.stream().collect(Collectors.toMap(
						item -> String.valueOf(item.getOrDefault("id", "")),
						item -> String.valueOf(item.getOrDefault("name", "")),
						(v1, v2) -> v1));
				Map<String, String> dotSortMap = dotList.stream().collect(Collectors.toMap(
						item -> String.valueOf(item.getOrDefault("id", "")),
						item -> String.valueOf(item.getOrDefault("tmsort", 0)),
						(v1, v2) -> v1));
				// 采集点手工录入下拉框key
				Map<String, String> dotComboKeyMap = dotList.stream()
						// 过滤掉没有combinitkey的点
						.filter(item -> StringUtils.isNotEmpty(String.valueOf(item.get("combinitkey"))))
						.collect(Collectors.toMap(
								item -> String.valueOf(item.getOrDefault("id", "")),
								item -> {
									Object key = item.get("combinitkey");
									if (key != null) {
										String arrStr = String.valueOf(key);
										String[] split = arrStr.split(",");
										return JSONArray.toJSONString(Arrays.asList(split));
									} else {
										return "";
									}
								},
								(v1, v2) -> v1));
				// 采集点下拉框label
				Map<String, String> dotComboLabelMap = dotList.stream()
						// 过滤掉没有combinitkey的点
						.filter(item -> StringUtils.isNotEmpty(String.valueOf(item.get("combinitval"))))
						.collect(Collectors.toMap(
								item -> String.valueOf(item.getOrDefault("id", "")),
								item -> {
									Object key = item.get("combinitval");
									if (key != null) {
										String arrStr = String.valueOf(key);
										String[] split = arrStr.split(",");
										return JSONArray.toJSONString(Arrays.asList(split));
									} else {
										return "";
									}
								},
								(v1, v2) -> v1));
				Set<String> tidlist = new HashSet<String>();
				for (Map<String, Object> map : tlist) {
					String tagid = String.valueOf(map.get("COLLECT_POINT_ID"));
					if (tidlist.contains(tagid)) {
						continue;
					}
					String showName = String.valueOf(map.get("COLLECT_POINT"));
					String type = String.valueOf(map.get("input_comp_type"));
					Integer itype = getType(type);
					String ops = String.valueOf(map.get("input_options") == null ? "" : map.get("input_options"));
					String ks = "", vs = "";

					if (ops.length() > 0) {
						try {
							JSONArray arr = JSONArray.parseArray(ops);
							for (int i = 0; i < arr.size(); i++) {
								JSONObject obj = arr.getJSONObject(i);
								ks += "," + obj.getString("value");
								vs += "," + obj.getString("text");
							}
						} catch (Exception e) {
						}
						if (ks.length() > 0) {
							ks = ks.substring(1);
							vs = vs.substring(1);
						}
					}

					TdsAccountMeter obj = new TdsAccountMeter();
					obj.setTagid(tagid);
					obj.setShowName(dotMap.getOrDefault(tagid, ""));
					obj.setTagnumber(showName);
					obj.setControltype(itype);
					obj.setCombinitkey(dotComboKeyMap.get(tagid));
					obj.setCombinitval(dotComboLabelMap.get(tagid));
					obj.setTmsort(Integer.valueOf(dotSortMap.getOrDefault(tagid, "0")));
					rlist.add(obj);

					tidlist.add(tagid);
				}
			}
		}

		if (StringUtils.isEmpty(rlist)) {// 如果未保存过，直接获取核算对象（活动）采集点
			rlist = accountSrv.getDefaultAccountUnitTagList(itemCode, rq);
		} else {// 保存过，获得采集点ID、名称信息，补全其他属性
			getMeterProp(rlist);
		}
		if (StringUtils.isNotEmpty(rlist)) {
			// 重新排序
			rlist = rlist.stream().sorted(Comparator.comparing(TdsAccountMeter::getTmsort))
					.collect(Collectors.toList());
		}
		return rlist;
	}

	private Integer getType(String confType) {
		Integer rv = null;
		if ("textfield".equals(confType)) {
			rv = 0;
		} else if ("numberfield".equals(confType)) {
			rv = 1;
		} else if ("datetimefield".equals(confType)) {
			rv = 2;
		} else if ("checkbox".equals(confType)) {
			rv = 3;
		} else if ("combobox".equals(confType)) {
			rv = 4;
		} else if ("selectUserSingle".equals(confType)) {
			rv = 8;
		} else if ("selectUserMulti".equals(confType)) {
			rv = 9;
		} else if (("datefield").equals(confType)) {
			rv = 10;
		} else  if (("timefield").equals(confType)) {
			rv = 11;
		} else if (("uploadImg").equals(confType)) {
			rv = 12;
		} else if (("orgfield").equals(confType)) {
			rv = 13;
		}else if (("not_have").equals(confType)) {
			rv = 14;
		}else if (("comboboxMulti").equals(confType)) {
			rv = 15;
		}
		return rv;
	}

	/**
	 * @category 补全所有仪表相关信息
	 *           根据采集点id，从tds_account_tag表中获取全部数据，根据日期倒序，补全数据取第一个即可
	 * @param list
	 * @return
	 */
	public List<TdsAccountMeter> getMeterProp(List<TdsAccountMeter> list) {
		List<String> idList = list.stream().map(TdsAccountMeter::getTagid).collect(Collectors.toList());

		Where where = Where.create();
		// where.eq(TdsAccountTag::getTmused, 1);
		where.in(TdsAccountTag::getTagid, idList.toArray());
		Order order = Order.create().order(TdsAccountTag::getTagid).orderByDesc(TdsAccountTag::getRq);
		List<TdsAccountTag> tlist = srv.queryData(TdsAccountTag.class, where, order, null);
		if (StringUtils.isNotEmpty(tlist)) {
			Map<String, TdsAccountTag> map = new HashMap<String, TdsAccountTag>();
			for (TdsAccountTag obj : tlist) {
				if (!map.containsKey(obj.getTagid())) {
					map.put(obj.getTagid(), obj);
				}
			}
			for (TdsAccountMeter obj : list) {
				TdsAccountTag tag = map.get(obj.getTagid());
				if (tag != null) {
					// 表头
					obj.setUnitCode(tag.getUnitCode());
					obj.setBelongZone(tag.getBelongZone());
					obj.setBelongDev(tag.getBelongDev());
					obj.setBelongTag(tag.getBelongTag());
					obj.setSdunit(tag.getSdunit());
					obj.setUpLimit(tag.getUpLimit());
					obj.setLowerLimit(tag.getLowerLimit());
					// 属性
					obj.setDecimalDegit(tag.getDecimalDegit());
					obj.setAlign(tag.getAlign());
					obj.setIswriteinput(tag.getIswriteinput());
					// obj.setCombinitkey(tag.getCombinitkey());
					// obj.setCombinitval(tag.getCombinitval());
					obj.setDefaultval(tag.getDefaultval());
					// obj.setControltype(tag.getControltype());
					obj.setShowName(tag.getTagname());
					obj.setTagnumber(tag.getTagnumber());
					obj.setIsWriteBackInfluxdb(obj.getIsWriteBackInfluxdb());
					obj.setDatasource(tag.getDatasource());
				}
			}
		}

		return list;
	}

	/**
	 * @category 从外部接口获取时间点
	 * @param param
	 * @return
	 */
	@Override
	public List<Map<String, Object>> getExtTimeList(ApplyParams param) {
		List<Map<String, Object>> rlist = new ArrayList<Map<String, Object>>();

		// String rq = param.getRq();
		String unitCode = param.getUnitCode();
		// String itemCode = param.getItemCode();
		String bc = param.getBc();
		String sbsj = param.getSbsj();
		String xbsj = param.getXbsj();
		String mode = param.getApplyMode();// 传入时间模式
		String teamId = param.getTeamId();

		// 接口 TODO
		/*
		 * -- ACTIVITY_DATE 一定是时间 jobNo 编号 FREQUENCY_TYPE 0 时间 1是数字编号
		 * select ACTIVITY_DATE,JOB_NO,FREQUENCY_TYPE,ID
		 * from JOBLIST_ACTIVITYEXAMPLE
		 * where SHIFT_CLASS_CODE = '班次id' and IS_PARENT = 0 and SBSJ<= '下班时间' and
		 * SBSJ>='上班时间'
		 * and ACTIVITY_ID='核算对象id'
		 *
		 * select a.ID,b.PERSON_ID,(case when (a.FREQUENCY_TYPE is null or
		 * a.FREQUENCY_TYPE!=0) then a.BEGIN_DATE else a.ACTIVITY_DATE end) as
		 * 'ACTIVITY_DATE'
		 * from JOBLIST_ACTIVITYEXAMPLE a
		 * left join JOBLIST_EXAMPLE_DUTYPERSON b on a.id = b.ACTIVITY_EXAMPLE_ID
		 * where a.PID='ZQQEAH2MP07YSOB2BI7669'
		 */

		if ("synchronize".equals(mode)) {
			// 辽兴模式，未使用班组精益核算活动功能，从移动端已存数据中获取
			StringBuffer whereUnit = new StringBuffer();
			String uid = param.getItemCode();
			if (uid.indexOf(",") == -1) {
				whereUnit.append("='");
				whereUnit.append(unitCode);
				whereUnit.append("'");
			} else {
				whereUnit.append(" in(");
				List<String> ulist = Coms.StrToList(unitCode, ",");
				for (String u : ulist) {
					if (u.length() > 3) {
						whereUnit.append(",");
					}
					whereUnit.append("'");
					whereUnit.append(u);
					whereUnit.append("'");
				}
				whereUnit.append(")");
			}

			List<Object> p = new ArrayList<Object>();
			Date sbsjD = StringUtils.isEmpty(sbsj) ? null : DateTimeUtils.parseDateTime(sbsj);
			String sql = "select ID, INPUT_TIME, JOB_INPUT_TIME from ACCTOBJ_INPUT where tmused=1 and BA_ID ='" + unitCode
					+ "' and bcdm=? and sbsj=? and team_id = ? order by input_time";
			p.add(bc);
			p.add(sbsjD);
			p.add(teamId);

			List<Map<String, Object>> list = srv.queryListMap(sql, p.toArray());
			if (StringUtils.isNotEmpty(list)) {
				List<String> sjlist = new ArrayList<String>();
				for (Map<String, Object> map : list) {
					Object sjobj = map.get("INPUT_TIME");
					Object jobInputTime = map.get("JOB_INPUT_TIME");

					String sj = String.valueOf(sjobj);
					String jit = String.valueOf(jobInputTime);
					if (sjlist.contains(sj)) {
						continue;
					}

					map.put("id", unitCode);
					map.put("sj", sj);
					map.put("jobInputTime", jit);
					map.put("edit", 1);// 默认不能编辑
					rlist.add(map);

					sjlist.add(sj);
				}
			}
		} else {
			String sql = "select ACTIVITY_DATE,JOB_NO,FREQUENCY_TYPE,ID,BEGIN_DATE  from JOBLIST_ACTIVITYEXAMPLE "
					+ "where SHIFT_CLASS_CODE =? and IS_PARENT = 0 and SBSJ<= ? and SBSJ>=? and ACTIVITY_ID=?";
			List<Object> pobj = new ArrayList<Object>();
			pobj.add(bc);
			pobj.add(xbsj);
			pobj.add(sbsj);
			pobj.add(unitCode);
			List<Map<String, Object>> tlist = srv.queryListMap(sql, pobj.toArray());
			if (StringUtils.isNotEmpty(tlist)) {
				List<String> idList = new ArrayList<String>();
				for (Map<String, Object> map : tlist) {
					Object sjobj = null;
					Object typeobj = map.get("FREQUENCY_TYPE");
					if (typeobj == null || !"0".equals(String.valueOf(typeobj))) {
						sjobj = map.get("BEGIN_DATE");
					} else {
						sjobj = map.get("ACTIVITY_DATE");
					}
					if (sjobj == null || String.valueOf(sjobj).length() < 19) {
						continue;
					}

					String sj = String.valueOf(sjobj);
					String id = String.valueOf(map.get("ID"));

					// Map<String, Object> tmap = new HashMap<String, Object>();
					map.put("id", id);
					map.put("sj", sj);
					map.put("edit", 0);// 默认不能编辑
					rlist.add(map);

					idList.add(id);
				}

				if (StringUtils.isNotEmpty(idList)) {
					SysUser user = SysUserHolder.getCurrentUser();
					String ids = "'" + Coms.listToString(idList, "','") + "'";
					sql = "select ACTIVITY_EXAMPLE_ID,PERSON_ID,PERSON_TYPE from JOBLIST_EXAMPLE_DUTYPERSON where ACTIVITY_EXAMPLE_ID in ("
							+ ids + ") order by ACTIVITY_EXAMPLE_ID,PERSON_TYPE desc";
					tlist = srv.queryListMap(sql, null);
					if (StringUtils.isNotEmpty(tlist)) {
						Map<String, Set<String>> tmap = new HashMap<String, Set<String>>();
						for (Map<String, Object> map : tlist) {
							String fid = String.valueOf(map.get("ACTIVITY_EXAMPLE_ID"));
							String uid = String.valueOf(map.get("PERSON_ID"));
							String type = String.valueOf(map.get("PERSON_TYPE"));

							if (tmap.containsKey(fid)) {
								if ("1".equals(type)) {
									tmap.get(fid).add(uid);
								} else {
									tmap.get(fid).add(uid);
								}
							} else {
								Set<String> ulist = new HashSet<String>();
								ulist.add(uid);
								tmap.put(fid, ulist);
							}
						}

						for (Map<String, Object> map : rlist) {
							String fid = String.valueOf(map.get("id"));

							Set<String> ulist = tmap.get(fid);
							if (StringUtils.isNotEmpty(ulist) && ulist.contains(user.getId())) {
								map.put("edit", 1);// 对应人员可编辑
							}
						}
					}
				}

			}
		}
		return rlist;
	}

	public Boolean extDataSave(String sendUnitCode, String shiftCode, String dataId, List<Date> sjlist,
			Map<Date, String> infoTimeIdMap, String sbsj, String xbsj,
			List<String> colList, Map<String, TdsAccountMeter> tagIdObjMap, Map<String, Map<Date, String>> map,
			Map<String, Map<Date, String>> dataList, Map<Date, String> jobInputTimeIdMap, String  teamId) {
		Boolean flag = true;

		List<AcctobjInputVo> clist = new ArrayList<AcctobjInputVo>();// 相当于行数据（时间点）
		String sendUnitName = "";
		String shiftName = "";
		List<Object> param = new ArrayList<Object>();
		String sql = "select name from costuint where id=?";
		param.add(sendUnitCode);
		List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
		if (StringUtils.isNotEmpty(list)) {
			sendUnitName = String.valueOf(list.get(0).get("name"));
		}
		sql = "select SHIFTNAME from SHIFT_MODEL_CLASS where ID=?";
		param.clear();
		param.add(shiftCode);
		list = srv.queryListMap(sql, param.toArray());
		if (StringUtils.isNotEmpty(list)) {
			shiftName = String.valueOf(list.get(0).get("SHIFTNAME"));
		}

		Map<String, String> haveDataMap = new HashMap<String, String>();
		if (dataList != null && dataList.size() > 0) {
			for (String tagId : dataList.keySet()) {
				Map<Date, String> hmap = dataList.get(tagId);// 已存获取数据
				for (Date d : hmap.keySet()) {
					haveDataMap.put(tagId + "_" + DateTimeUtils.formatDateTime(d), hmap.get(d));
				}
			}
		}

		for (Date date : sjlist) {
			String dateStr = DateTimeUtils.formatDateTime(date);
			String taskId = infoTimeIdMap == null ? null : infoTimeIdMap.get(date);
			taskId = StringUtils.isEmpty(taskId) ? sendUnitCode : taskId;

			AcctobjInputVo vo = new AcctobjInputVo();// 行数据对象
			vo.setRowFlag(0);
			vo.setInputTime(date);
			vo.setBaId(sendUnitCode);// sendUnitCode
			vo.setBaName(sendUnitName);// sendUnitName
			vo.setBcdm(shiftCode);
			vo.setBcmc(shiftName);
			vo.setSbsj(DateTimeUtils.parseDateTime(sbsj));
			vo.setXbsj(DateTimeUtils.parseDateTime(xbsj));
			vo.setTaskId(taskId);
			vo.setTmused(1);
			// 从jobInputTimeIdMap获取采集时间
			if (jobInputTimeIdMap != null && jobInputTimeIdMap.containsKey(date)) {
				vo.setJobInputTime(DateTimeUtils.parseDateTime(jobInputTimeIdMap.get(date)));
			}
			vo.setTeamId(teamId);

			List<AcctobjInputFlVo> flList = new ArrayList<AcctobjInputFlVo>();// 按分类汇总数据
			vo.setFlData(flList);

			Map<String, AcctobjInputVo> umap = new LinkedHashMap<String, AcctobjInputVo>();
			Map<String, AcctobjInputFlVo> tempmap = new LinkedHashMap<String, AcctobjInputFlVo>();

			for (String tagId : colList) {

				TdsAccountMeter atag = tagIdObjMap.get(tagId);
				String ucode = atag.getUnitCode();
				String tagpid = atag.getTagpid();
				String comType = null, txt = null, ops = null, tagval = null;

				tagval = haveDataMap.get(tagId + "_" + dateStr);// 历史数据
				if (tagval == null) {
					Map<Date, String> _map = map.get(atag.getDatasource());// map.get(tagId);
					if (_map != null && _map.containsKey(date)) {
						tagval = _map.get(date);// 采集数据
					}
				}

				if (atag != null) {
					int icom = -1;
					if (atag.getControltype() != null) {
						icom = atag.getControltype();
					}
					Object ct = ControlTypeConverter.convertToString(icom);
					String ks = atag.getCombinitkey();
					String vs = atag.getCombinitval();

					comType = ct == null ? null : String.valueOf(ct);
					if (StringUtils.isNotEmpty(ks) && StringUtils.isNotEmpty(vs)) {
						// [{"text":"选项1","value":"选项1"},{"text":"选项2","value":"选项2"},{"text":"选项3","value":"选项3"}]
						List<String> klist = Coms.StrToList(ks, ",");
						List<String> vlist = Coms.StrToList(vs, ",");
						StringBuffer sb = new StringBuffer();
						for (int j = 0; j < klist.size(); j++) {
							if (klist.get(j).equals(tagval)) {
								txt = vlist.get(j);
							}
							String key = klist.get(j).replace("\"", "\\\"");
							String val = vlist.get(j).replace("\"", "\\\"");
							sb.append(",{\"text\":\"");
							sb.append(val);
							sb.append("\",\"value\":\"");
							sb.append(key);
							sb.append("\"}");

						}
						if (sb.length() > 0) {
							ops = "[" + sb.substring(1) + "]";
						}
					}
				}

				if (umap.containsKey(ucode)) {
					if (tempmap.containsKey(tagpid)) {
						AcctobjInputFlVo flvo = tempmap.get(tagpid);
						AcctobjInputmxVo mx = new AcctobjInputmxVo();
						mx.setCollectPoint(atag.getTagnumber());
						mx.setCollectPointId(tagId);
						mx.setCollectPointVal(tagval);
						mx.setTagNo(atag.getDatasource());
						mx.setSn(atag.getTmsort());
						mx.setInputCompType(comType);
						mx.setCollectPointText(txt);
						mx.setInputOptions(ops);
						mx.setTmused(1);
						mx.setIsWriteBackInfluxdb(atag.getIsWriteBackInfluxdb());
						flvo.getMxData().add(mx);
					} else {
						AcctobjInputVo uobj = umap.get(ucode);

						AcctobjInputFlVo flvo = ObjUtils.copyTo(uobj, AcctobjInputFlVo.class);
						flvo.setFlId(tagpid);
						flvo.setFlName(atag.getTagpname());
						List<AcctobjInputmxVo> mxData = new ArrayList<AcctobjInputmxVo>();
						flvo.setMxData(mxData);

						AcctobjInputmxVo mx = new AcctobjInputmxVo();
						mx.setCollectPoint(atag.getTagnumber());
						mx.setCollectPointId(tagId);
						mx.setCollectPointVal(tagval);
						mx.setTagNo(atag.getDatasource());
						mx.setSn(atag.getTmsort());
						mx.setInputCompType(comType);
						mx.setCollectPointText(txt);
						mx.setInputOptions(ops);
						mx.setTmused(1);
						mx.setIsWriteBackInfluxdb(atag.getIsWriteBackInfluxdb());
						mxData.add(mx);
						tempmap.put(tagpid, flvo);
						uobj.getFlData().add(flvo);
					}
				} else {
					AcctobjInputVo uobj = ObjUtils.copyTo(vo, AcctobjInputVo.class);
					uobj.setAcctobjId(ucode);
					uobj.setAcctobjName(atag.getUnitName());

					AcctobjInputFlVo flvo = ObjUtils.copyTo(uobj, AcctobjInputFlVo.class);
					flvo.setFlId(tagpid);
					flvo.setFlName(atag.getTagpname());
					List<AcctobjInputmxVo> mxData = new ArrayList<AcctobjInputmxVo>();
					flvo.setMxData(mxData);

					AcctobjInputmxVo mx = new AcctobjInputmxVo();
					mx.setCollectPoint(atag.getTagnumber());
					mx.setCollectPointId(tagId);
					mx.setCollectPointVal(tagval);
					mx.setTagNo(atag.getDatasource());
					mx.setSn(atag.getTmsort());
					mx.setInputCompType(comType);
					mx.setCollectPointText(txt);
					mx.setInputOptions(ops);
					mx.setTmused(1);
					mx.setIsWriteBackInfluxdb(atag.getIsWriteBackInfluxdb());
					mxData.add(mx);

					tempmap.put(tagpid, flvo);

					uobj.getFlData().add(flvo);
					umap.put(ucode, uobj);
				}

			}

			for (AcctobjInputVo ai : umap.values()) {// 多设备情况处理
				clist.add(ai);
			}
		}

		if (StringUtils.isNotEmpty(clist)) {// 生成事件发布
			String tid = "";
			sql = "select PID  from JOBLIST_ACTIVITYEXAMPLE where ID=?";
			param.clear();
			param.add(clist.get(0).getTaskId());
			List<Map<String, Object>> tlist = srv.queryListMap(sql, param.toArray());
			if (StringUtils.isNotEmpty(tlist)) {
				tid = String.valueOf(tlist.get(0).get("PID"));
			}

			AccountSaveDto obj = new AccountSaveDto();
			obj.setType("collectionPoint");// extOp?"collectionPoint":"routingInspection"
			obj.setCollectionPointInputData(clist);
			obj.setOperType("");
			obj.setFormDataId(dataId);
			obj.setTaskId(tid);
			AccountSaveEvent event = new AccountSaveEvent(obj);
			context.publishEvent(event);
		}

		return flag;
	}

	@Override
	/**
	 * @category 获取班次时间点列表
	 * @param sbsj
	 * @param xbsj
	 * @return
	 */
	public List<String> getUnitOntimeList(String sbsj, String xbsj) {
		List<String> rlist = new ArrayList<String>();

		// 指定数据源别名
		ApplyParams dto = new ApplyParams();
		dto.setApplyAlias("account_default_tdsAalias");
		String tdsAalias = getApplyConfValue(dto);

		TdsAccountConf conf = accountSrv.getAccountConf(tdsAalias);// 配置信息
		TdsAccountTime timeConf = accountSrv.getTimeConf(tdsAalias);// 时间配置

		Date startDate = null;
		String startRq = null;
		String startBind = timeConf.getStartBingDay();
		String startFix = timeConf.getStartFixed();
		Boolean haveStart = new Integer(1).equals(timeConf.getStartRound());
		int istartfix = 0;

		Date endDate = null;
		String endRq = null;
		String endBind = timeConf.getEndBingDay();
		String endFix = timeConf.getEndFixed();
		Boolean haveEnd = new Integer(1).equals(timeConf.getEndRound());
		int iendfix = 0;
		if (Coms.judgeLong(startFix)) {
			istartfix = Integer.parseInt(startFix);
		}
		if (Coms.judgeLong(endFix)) {
			iendfix = Integer.parseInt(endFix);
		}
		// 开始时间获取
		if (StringUtils.isNotEmpty(startBind)) {
			// 绑定台账内部绑定
			startRq = sbsj;
			Object o = startRq;
			if (DateTimeUtils.parseDate(o) == null) {
				startRq = null;
			} else {
				startRq = startRq.substring(0, 19);
			}
		}
		if (StringUtils.isEmpty(startRq)) {
			if (istartfix != 0) {
				startRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " " + timeConf.getStartBingTime() + ":00";
				startDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(startRq), istartfix);
			} else {
				startRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " " + timeConf.getStartBingTime() + ":00";
				startDate = DateTimeUtils.parseDateTime(startRq);
			}
		} else {
			startDate = DateTimeUtils.parseDateTime(startRq);
			if (conf.getBcDiff() != null) {// 上班时间偏差修正
				int bcdiff = conf.getBcDiff();
				startDate = DateTimeUtils.doMinute(startDate, bcdiff);
			}
		}
		// 截止时间获取
		if (StringUtils.isNotEmpty(endBind)) {
			// 绑定台账内部绑定
			endRq = xbsj;
			Object o = endRq;
			if (DateTimeUtils.parseDate(o) == null) {
				endRq = null;
			} else {
				endRq = endRq.substring(0, 19);
			}
		}
		if (StringUtils.isEmpty(endRq)) {
			if (iendfix != 0) {
				endRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " " + timeConf.getEndBingTime() + ":00";
				endDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(endRq), iendfix);
			} else {
				endRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " " + timeConf.getEndBingTime() + ":00";
				endDate = DateTimeUtils.parseDateTime(endRq);
			}
		} else {
			endDate = DateTimeUtils.parseDateTime(endRq);
			if (conf.getBcDiff() != null) {// 下班时间偏差修正
				int bcdiff = conf.getBcDiff();
				endDate = DateTimeUtils.doMinute(endDate, bcdiff);
			}
		}

		List<Date> timeList = new ArrayList<Date>();
		if (DateTimeUtils.bjDate(startDate, endDate) == 1) {// 如果时间不对，不获取数据
		} else {

			Date tempDate = startDate;
			String timeStep = timeConf.getTimeStep();
			Integer step = 60;
			if (Coms.judgeLong(timeStep)) {
				step = Integer.parseInt(timeStep);
			}
			if (haveStart) {
				timeList.add(startDate);
				tempDate = DateTimeUtils.doMinute(tempDate, step);
			}

			for (int i = 0, il = 50; i < il && DateTimeUtils.bjDate(endDate, tempDate) > 0; i++) {// 时间对比，增加循环数，避免死循环
				timeList.add((Date) tempDate.clone());
				tempDate = DateTimeUtils.doMinute(tempDate, step);
			}

			if (DateTimeUtils.bjDate(tempDate, endDate) == 1) {
				if (haveEnd) {
					if (!timeList.contains(endDate)) {
						timeList.add(endDate);
					}
				}
			} else if (DateTimeUtils.bjDate(tempDate, endDate) == 0) {
				// 间隔固定
				if (haveEnd) {
					timeList.add(endDate);
				}
			}

			for (Date date : timeList) {
				rlist.add(DateTimeUtils.formatDateTime(date));
			}
		}

		return rlist;
	}

	@Override
	public String transferUnitCode(String unitCode) {
		// 查询核算单元是否有指向，如果有，转换核算单元代码，否则，直接返回本核算单元
		try {
			String sql = "select * from COSTUINT where ID='" + unitCode + "'";
			List<Map<String, Object>> list = srv.queryListMap(sql);
			if (StringUtils.isNotEmpty(list)) {
				String unitId = list.get(0).get("DEVICEIDS") == null ? null
						: String.valueOf(list.get(0).get("DEVICEIDS"));
				if (StringUtils.isNotEmpty(unitId)) {
					return unitId;
				}
			}
		} catch (Exception e) {
		}

		return unitCode;
	}
}
