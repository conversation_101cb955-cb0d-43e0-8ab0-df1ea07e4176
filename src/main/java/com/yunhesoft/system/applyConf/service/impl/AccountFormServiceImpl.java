package com.yunhesoft.system.applyConf.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.checkerframework.common.value.qual.StringVal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.applyConf.entity.dto.FormParam;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountForm;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormManage;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormMeter;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormManageVo;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormMeterVo;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormVo;
import com.yunhesoft.system.applyConf.service.IAccountFormService;
import com.yunhesoft.system.applyConf.service.IApplyConfService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.mongodb.service.MongoDBService;
import com.yunhesoft.system.tds.entity.po.TdsAccountTag;
import com.yunhesoft.system.tds.entity.po.TdsAccountTagVersion;
import com.yunhesoft.system.tds.service.IDataSourceAccountService;


@Service
public class AccountFormServiceImpl implements IAccountFormService {
	
	@Autowired
	EntityService srv;
	
	@Autowired
    private IDataSourceAccountService accountServ;
	
	@Autowired
	private MongoDBService mongoDBServ;

	@Autowired
	private IApplyConfService applySrv;
	@Autowired
	private RedisUtil redis;
	
	private String accountTypeId = "account";
	
	private final String accountTabName = "accountTab";
	
	private final String accountKey = "TDS:ACCOUUNT:TITLE";//

	@Override
	public List<TdsAccountFormVo> getFormManageList(FormParam dto) {
		initDefaultAccount(null);
		
		Where where = Where.create();
		where.eq(TdsAccountForm::getTmused, 1);
		Order order = Order.create();
		order.order(TdsAccountForm::getTmsort);
		order.order(TdsAccountForm::getId);
		List<TdsAccountForm> list = srv.queryData(TdsAccountForm.class, where, order, null);
		
		List<TdsAccountFormVo> rlist = new ArrayList<TdsAccountFormVo>();
		if(StringUtils.isNotEmpty(list)) {
			for (TdsAccountForm obj : list) {
				TdsAccountFormVo vo = ObjUtils.copyTo(obj, TdsAccountFormVo.class);
				if(!new Integer(0).equals(obj.getTypeCode())) {
					vo.setTagConf("管理");
					vo.setManageConf("管理");
				}
				rlist.add(vo);
			}
		}
		
		return rlist;
	}

	@Override
	public List<TdsAccountFormVo> saveFormList(FormParam dto) {
		List<TdsAccountFormVo> rlist = new ArrayList<TdsAccountFormVo>();
		List<TdsAccountFormVo> data = dto.getData();
		if(StringUtils.isNotEmpty(data)) {
			List<TdsAccountForm> addList = new ArrayList<TdsAccountForm>();
			List<TdsAccountForm> updList = new ArrayList<TdsAccountForm>();
			for (TdsAccountFormVo vo : data) {
				TdsAccountForm obj = ObjUtils.copyTo(vo, TdsAccountForm.class);
				if(StringUtils.isEmpty(obj.getId())) {//添加
					obj.setId(TMUID.getUID());
					obj.setTypeCode(1);
					obj.setTmused(1);
					addList.add(obj);
				}else {//修改
					updList.add(obj);
				}
			}
			
			if(StringUtils.isNotEmpty(addList)) {
				srv.insertBatch(addList);
				for (TdsAccountForm obj : addList) {
					TdsAccountFormVo vo = ObjUtils.copyTo(obj, TdsAccountFormVo.class);
					rlist.add(vo);
				}
			}
			if(StringUtils.isNotEmpty(updList)) {
				srv.updateBatch(updList);
				for (TdsAccountForm obj : updList) {
					TdsAccountFormVo vo = ObjUtils.copyTo(obj, TdsAccountFormVo.class);
					rlist.add(vo);
				}
			}
		}
		return rlist;
	}

	@Override
	public Boolean deleteFormList(FormParam dto) {
		Boolean flag = true;
		String ids = dto.getDelids();
		if(StringUtils.isNotEmpty(ids)) {
			List<String> idlist = Coms.StrToList(ids, ",");
			Where where = Where.create();
			where.eq(TdsAccountForm::getTmused, 1);
			where.in(TdsAccountForm::getId, idlist.toArray());
			List<TdsAccountForm> list = srv.queryData(TdsAccountForm.class, where, null, null);
			if(StringUtils.isNotEmpty(list)) {
				for (TdsAccountForm obj : list) {
					obj.setTmused(0);
				}
				flag = flag && srv.updateBatch(list) == 1;
			}
		}
		return flag;
	}

	@Override
	public List<TdsAccountFormManageVo> getFormPermList(FormParam dto) {
		String formid = dto.getFormCode();
		
		Where where = Where.create();
		where.eq(TdsAccountFormManage::getTmused, 1);
		where.eq(TdsAccountFormManage::getAccountid, formid);
		List<TdsAccountFormManage> list = srv.queryData(TdsAccountFormManage.class, where, null, null);
		
		List<TdsAccountFormManageVo> rlist = new ArrayList<TdsAccountFormManageVo>();
		if(StringUtils.isNotEmpty(list)) {
			for (TdsAccountFormManage obj : list) {
				TdsAccountFormManageVo vo = ObjUtils.copyTo(obj, TdsAccountFormManageVo.class);
				vo.setName(obj.getManageName());
				vo.setLabel(obj.getManageName());
				vo.setExpr("'"+obj.getManageCode()+"'");
				vo.setCode(obj.getManageCode());
				
				if("1".equals(obj.getModeType())) {
					vo.setType("user");
				}else if("2".equals(obj.getModeType())) {
					vo.setType("org-post");
				}else if("3".equals(obj.getModeType())) {
					vo.setType("org");
					vo.setExpr(obj.getManageCode());
				}
				rlist.add(vo);
			}
		}
		
		return rlist;
	}

	@Override
	public Boolean saveFormPermList(FormParam dto) {
		Boolean flag = true;
		
		//每次保存，先删除原子表信息，再添加
		List<TdsAccountFormManageVo> dlist = getFormPermList(dto);
		
		List<TdsAccountFormManageVo> mlist = dto.getMlist();
		
		List<TdsAccountFormManage> addList = new ArrayList<TdsAccountFormManage>();
		
		if(StringUtils.isNotEmpty(mlist)) {
			for (TdsAccountFormManageVo vo : mlist) {
				TdsAccountFormManage obj = ObjUtils.copyTo(vo, TdsAccountFormManage.class);
				obj.setManageCode(vo.getCode());
				obj.setManageName(vo.getName());
				String mt = "1";
				if("org".equals(vo.getType())) {
					mt = "3";
				}else if("org-post".equals(vo.getType())) {
					mt = "2";
					String[] ss = obj.getManageCode().split("_");
					obj.setOrgCode(ss[0]);
					if(ss.length > 1) {
						obj.setPostCode(ss[1]);
					}
				}
				obj.setModeType(mt);
				
//				if(StringUtils.isNotEmpty(obj.getId())) {//更新
//					obj.setTmused(1);
//					updList.add(obj);
//				}else {//添加
//					obj.setId(TMUID.getUID());
//					obj.setFormid(dto.getFormCode());
//					obj.setTmused(1);
//					addList.add(obj);
//				}
				obj.setId(TMUID.getUID());
				obj.setAccountid(dto.getFormCode());
				obj.setTmused(1);
				addList.add(obj);
			}
			if(StringUtils.isNotEmpty(addList)) {
				flag = flag && srv.insertBatch(addList) == 1;
			}
			if(StringUtils.isNotEmpty(dlist)) {
				List<TdsAccountFormManage> dellist = new ArrayList<TdsAccountFormManage>();
				for (TdsAccountFormManageVo vo : dlist) {
					TdsAccountFormManage obj = ObjUtils.copyTo(vo, TdsAccountFormManage.class);
					dellist.add(obj);
				}
				flag = flag && srv.deleteByIdBatch(dellist) == 1;
			}
		}else {
			if(StringUtils.isNotEmpty(dlist)) {
				List<TdsAccountFormManage> dellist = new ArrayList<TdsAccountFormManage>();
				for (TdsAccountFormManageVo vo : dlist) {
					TdsAccountFormManage obj = ObjUtils.copyTo(vo, TdsAccountFormManage.class);
					dellist.add(obj);
				}
				flag = flag && srv.deleteByIdBatch(dellist) == 1;
			}
		}
		
		return flag;
	}

	@Override
	public List<TdsAccountFormMeterVo> geAccountTagManage(FormParam dto) {
		String unitCode = dto.getUnitCode();
		String formCode = dto.getFormCode();
		String tagType = dto.getTagType();
		String searchName = dto.getSearchName();
		Pagination<?> page = dto.getPage();
		List<TdsAccountFormMeterVo> rlist = new ArrayList<TdsAccountFormMeterVo>();
		
		//条件中需去除自定义台账同核算对象已设置的仪表
		List<TdsAccountFormMeter> haveMeterList = getHaveUnitMeterList(unitCode, formCode, tagType);
		List<String> haveTagList = new ArrayList<String>();
		if(StringUtils.isNotEmpty(haveMeterList)) {
			for (TdsAccountFormMeter meter : haveMeterList) {
				haveTagList.add(meter.getTagid());
			}
		}
		
		//核算对象仪表数据
		List<TdsAccountTag> tagList = getUnitTagList(null, unitCode, formCode, haveTagList, tagType, page, searchName);//
		//已定制的仪表数据
		List<TdsAccountFormMeter> mlist = getFormMeterList(unitCode, formCode, tagType);
		if(StringUtils.isNotEmpty(mlist)) {
//			List<String> midlist = new ArrayList<String>();//后期仪表删除用
			Map<String, TdsAccountFormMeter> mmap = new HashMap<String, TdsAccountFormMeter>();
			for (TdsAccountFormMeter meter : mlist) {
				mmap.put(meter.getTagid(), meter);
//				midlist.add(meter.getTagid());
			}
			
			for (TdsAccountTag obj : tagList) {
				TdsAccountFormMeterVo vo = ObjUtils.copyTo(obj, TdsAccountFormMeterVo.class);
				TdsAccountFormMeter meter = mmap.get(obj.getTagid());
				vo.setShowName(obj.getTagname());
				if(meter == null) {
					vo.setShowMark(false);
					vo.setUsedMark(false);
				}else {
					vo.setShowMark(true);
					vo.setUsedMark(true);
					vo.setMeterId(meter.getId());
					vo.setAlign(meter.getAlign());
					vo.setEditMark(meter.getEditMark());
					vo.setWidth(meter.getWidth());
//					midlist.remove(obj.getTagid());
				}
				rlist.add(vo);
			}
//			if(StringUtils.isNotEmpty(midlist)) {//有已设置，但是后期版本被删除的仪表
//				for (String tagid : midlist) {
//					TdsAccountFormMeter meter = mmap.get(tagid);
//					TdsAccountFormMeterVo vo = ObjUtils.copyTo(meter, TdsAccountFormMeterVo.class);
//					vo.setShowMark(true);
//					vo.setUsedMark(true);
//					vo.setCtype("del");
//					vo.setMeterId(meter.getId());
//					rlist.add(vo);
//				}
//			}
		}else {
			if(tagList!=null) {
				for (TdsAccountTag obj : tagList) {
					TdsAccountFormMeterVo vo = ObjUtils.copyTo(obj, TdsAccountFormMeterVo.class);
					vo.setShowName(obj.getTagname());
					vo.setShowMark(true);
					vo.setUsedMark(false);
					rlist.add(vo);
				}
			}
		}
		
		return rlist;
	}

	@Override
	public Boolean saveAccountTagManage(FormParam dto) {
		Boolean flag = true;
		String formCode = dto.getFormCode();
		List<TdsAccountFormMeterVo> meterList = dto.getMeterList();
		
		if(StringUtils.isNotEmpty(meterList)) {
			
			List<TdsAccountFormMeter> addMeterList = new ArrayList<TdsAccountFormMeter>();
			List<TdsAccountFormMeter> updMeterList = new ArrayList<TdsAccountFormMeter>();
			List<TdsAccountFormMeter> delMeterList = new ArrayList<TdsAccountFormMeter>();
			List<TdsAccountTag> updTagList = new ArrayList<TdsAccountTag>();
			
			Map<String, TdsAccountFormMeterVo> addMeterMap = new LinkedHashMap<String, TdsAccountFormMeterVo>();
			Map<String, TdsAccountFormMeterVo> updMeterMap = new HashMap<String, TdsAccountFormMeterVo>();
			Map<String, TdsAccountFormMeterVo> delMeterMap = new HashMap<String, TdsAccountFormMeterVo>();
			Map<String, TdsAccountFormMeterVo> updTagMap = new HashMap<String, TdsAccountFormMeterVo>();
			
			
			for (TdsAccountFormMeterVo vo : meterList) {
				if(StringUtils.isNotEmpty(vo.getMeterId())) {
					if(vo.getUsedMark()) {//继续使用，更新属性即可
						updMeterMap.put(vo.getMeterId(), vo);
						updTagMap.put(vo.getId(), vo);//需要同步更新仪表的设置
					}else {//不再使用，删除仪表信息数据
						delMeterMap.put(vo.getMeterId(), vo);
					}
				}else {
					if(vo.getUsedMark()) {//新增使用，增加仪表信息
						addMeterMap.put(vo.getId(), vo);
					}else {//只修改属性，更新版本的仪表信息数据
						updTagMap.put(vo.getId(), vo);
						
					}
				}
			}
			
			if(addMeterMap.size() > 0) {
				for (TdsAccountFormMeterVo vo : addMeterMap.values()) {
					TdsAccountFormMeter obj = ObjUtils.copyTo(vo, TdsAccountFormMeter.class);
					obj.setId(TMUID.getUID());
					obj.setTypeCode(1);
					if("2".equals(vo.getCtype())) {
						obj.setTagType(2);
					}else if("3".equals(vo.getCtype())) {
						obj.setTagType(3);
					}
					obj.setAccountid(formCode);
					obj.setTmused(1);
					addMeterList.add(obj);
				}
			}
			if(updMeterMap.size() > 0) {
				List<String> idlist = new ArrayList<String>();
				for (String id : updMeterMap.keySet()) {
					idlist.add(id);
				}
				List<TdsAccountFormMeter> list = getFormMeterByIds(idlist);
				if(StringUtils.isNotEmpty(list)) {
					for (TdsAccountFormMeter meter : list) {
						TdsAccountFormMeterVo vo = updMeterMap.get(meter.getId());
						meter.setWidth(vo.getWidth());
						meter.setEditMark(vo.getEditMark());
						meter.setAlign(vo.getAlign());
						updMeterList.add(meter);
					}
				}
			}
			if(delMeterMap.size() > 0) {
				List<String> idlist = new ArrayList<String>();
				for (String id : delMeterMap.keySet()) {
					idlist.add(id);
				}
				List<TdsAccountFormMeter> list = getFormMeterByIds(idlist);
				if(StringUtils.isNotEmpty(list)) {
					delMeterList.addAll(list);
				}
			}
			if(updTagMap.size() > 0) {
				List<String> idlist = new ArrayList<String>();
				for (String id : updTagMap.keySet()) {
					idlist.add(id);
				}
				List<TdsAccountTag> list = getTagByIds(idlist);
				if(StringUtils.isNotEmpty(list)) {
					for (TdsAccountTag tag : list) {
						TdsAccountFormMeterVo vo = updTagMap.get(tag.getId());
						tag.setWidth(vo.getWidth());
						tag.setEditMark(vo.getEditMark());
						tag.setAlign(vo.getAlign());
						updTagList.add(tag);
					}
				}
			}
			
			if(StringUtils.isNotEmpty(addMeterList)) {
				flag = flag && 1 == srv.insertBatch(addMeterList);
			}
			if(StringUtils.isNotEmpty(updMeterList)) {
				flag = flag && 1 == srv.updateBatch(updMeterList);
			}
			if(StringUtils.isNotEmpty(delMeterList)) {
				flag = flag && 1 == srv.deleteByIdBatch(delMeterList);
			}
			if(StringUtils.isNotEmpty(updTagList)) {
				flag = flag && 1 == srv.updateBatch(updTagList);
			}
			deleteRedit(formCode);
		}
		
		return flag;
	}
	private Boolean deleteRedit(String formId) {
		Boolean flag = true;
		//判断是否有redis,有就清除，需要查一下相关的自定义台账，如果没有，只清除默认台账的
		TdsAccountForm form = getFormObj(formId);
		if(form!=null && StringUtils.isNotEmpty(form.getFormCode())) {
			List<String> listKey = new ArrayList<String>();
			String accountId = form.getId()==null?"":form.getId();
			String unitCode = form.getUnitCode().replace("\"", "").replace("[", "").replace("]", "");
			
			//获取全部版本
			Where wherever = Where.create();
			wherever.eq(TdsAccountTagVersion::getUnitcode, unitCode);
			Order order = Order.create();
			order.order(TdsAccountTagVersion::getRq);
			List<TdsAccountTagVersion> tagVerList = srv.queryData(TdsAccountTagVersion.class, wherever, order, null);
			
			if(StringUtils.isNotEmpty(tagVerList)) {
				for (TdsAccountTagVersion ver : tagVerList) {
					String rq = ver.getRq();
					String key = unitCode+"_"+rq+"_"+accountId+"_2";
					String key2 = unitCode+"_"+rq+"_"+accountId+"_3";
					listKey.add(key);
					listKey.add(key2);
				}
			}
			if(StringUtils.isNotEmpty(listKey)) {
				redis.hDelete(accountKey, listKey.toArray());
			}
		}
		
		return true;
	}
	private TdsAccountForm getFormObj(String formId) {
		TdsAccountForm obj = srv.queryObjectById(TdsAccountForm.class, formId);
		return obj;
	}
	private List<TdsAccountTag> getTagByIds(List<String> idlist) {
		Where where = Where.create();
		where.eq(TdsAccountTag::getTmused, 1);
		where.in(TdsAccountTag::getId, idlist.toArray());
		List<TdsAccountTag> list = srv.queryList(TdsAccountTag.class, where, null);
		return list;
	}
	private List<TdsAccountFormMeter> getFormMeterByIds(List<String> idlist) {
		Where where = Where.create();
		where.eq(TdsAccountFormMeter::getTmused, 1);
		where.in(TdsAccountFormMeter::getId, idlist.toArray());
		where.eq(TdsAccountFormMeter::getTypeCode, 1);
		List<TdsAccountFormMeter> list = srv.queryList(TdsAccountFormMeter.class, where, null);
		return list;
	}

	@Override
	/**
	 * @category 获取指定配置的仪表
	 * @param unitCode
	 * @param formCode
	 * @return
	 */
	public List<TdsAccountFormMeter> getHaveUnitMeterList(String unitCode, String formCode, String tagType) {
		List<TdsAccountFormMeter> rlist = null;
		
		List<String> accountIdList = new ArrayList<String>();
		Where where = Where.create();
		where.eq(TdsAccountForm::getTmused, 1);
		where.like(TdsAccountForm::getUnitCode, unitCode);
		where.notEmpty(TdsAccountForm::getFormCode);
		where.ne(TdsAccountForm::getId, formCode);
		List<TdsAccountForm> list = srv.queryData(TdsAccountForm.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			for (TdsAccountForm form : list) {
				accountIdList.add(form.getId());
			}
			
			Where wherem = Where.create();
			wherem.eq(TdsAccountFormMeter::getTmused, 1);
			wherem.in(TdsAccountFormMeter::getAccountid, accountIdList.toArray());
			if("3".equals(tagType) || "2".equals(tagType)) {
				where.eq(TdsAccountFormMeter::getTagType, "3".equals(tagType)?3:2);
			}
			wherem.eq(TdsAccountFormMeter::getUnitCode, unitCode);
			wherem.eq(TdsAccountFormMeter::getTypeCode, 1);
			rlist = srv.queryData(TdsAccountFormMeter.class, wherem, null, null);
			
		}
		return rlist;
	}
	@Override
	/**
	 * @category 获取当前日期最近版本核算单元的仪表信息列表
	 * @param unitCode
	 * @return
	 */
	public List<TdsAccountTag> getUnitTagList(String unitCode, String formCode, List<String> haveTagList, String tagType, Pagination<?> page) {
		return getUnitTagList(null, unitCode, formCode, haveTagList, tagType, page);
	}
	public List<TdsAccountTag> getUnitTagList(String rq, String unitCode, String formCode, List<String> haveTagList, String tagType, Pagination<?> page) {
		return getUnitTagList(rq, unitCode, formCode, haveTagList, tagType, page, null);
	}
	public List<TdsAccountTag> getUnitTagList(String rq, String unitCode, String formCode, List<String> haveTagList, String tagType, Pagination<?> page, String searchName) {
		List<TdsAccountTag> taglist = null;
		
		Where wherev = Where.create();
		wherev.eq(TdsAccountTagVersion::getTmused, 1);
		wherev.eq(TdsAccountTagVersion::getUnitcode, unitCode);
		Order orderv = Order.create();
		orderv.orderByDesc(TdsAccountTagVersion::getRq);
		List<TdsAccountTagVersion> vlist = srv.queryList(TdsAccountTagVersion.class, wherev, orderv);
		
		String vid = "";
		String version = "";
		Date nd = DateTimeUtils.getND();
		if(StringUtils.isNotEmpty(rq)) {
			nd = DateTimeUtils.parseDate(rq);
		}
		//从大到小日期
		if(StringUtils.isNotEmpty(vlist)) {
			for (TdsAccountTagVersion ver : vlist) {
				String t = ver.getRq();
				Date dt = DateTimeUtils.parseD(t, DateTimeUtils.DateFormat_YMD);
				if(DateTimeUtils.bjDate(dt, nd) <= 0) {
					vid = ver.getId();
					version = DateTimeUtils.formatDate(dt, DateTimeUtils.DateFormat_YMD);
					break;
				}
			}
			if("".equals(version)) {
				if(StringUtils.isNotEmpty(vlist)) {
					vid = vlist.get(0).getId();
					version = vlist.get(0).getRq();
				}
			}
			
//			//条件中需去除自定义台账同核算对象已设置的仪表
//			List<TdsAccountFormMeter> haveMeterList = getHaveUnitMeterList(unitCode, formCode);
//			List<String> haveTagList = new ArrayList<String>();
//			if(StringUtils.isNotEmpty(haveMeterList)) {
//				for (TdsAccountFormMeter meter : haveMeterList) {
//					haveTagList.add(meter.getTagid());
//				}
//			}
			
			if(haveTagList.size() > 600) {
				//避免not in过长，先取所有数据，取差，获取in的数据，再用in取
				List<TdsAccountTag> intaglist = new ArrayList<TdsAccountTag>();
				List<TdsAccountTag> alltaglist = new ArrayList<TdsAccountTag>();
				
				//all仪表数据
				Where whereall = Where.create();
				whereall.eq(TdsAccountTag::getTmused, 1);
				whereall.eq(TdsAccountTag::getUnitCode, unitCode);
				whereall.eq(TdsAccountTag::getVermark, vid);
				if(StringUtils.isNotEmpty(tagType) && Coms.judgeLong(tagType)) {
					whereall.eq(TdsAccountTag::getCtype, Integer.parseInt(tagType));
				}
				if(StringUtils.isNotEmpty(searchName)) {
					whereall.and().lb();
					whereall.like(TdsAccountTag::getClass1, searchName);
					whereall.or().like(TdsAccountTag::getTagname, searchName);
					whereall.or().like(TdsAccountTag::getTagnumber, searchName);
					whereall.rb();
				}
				alltaglist = srv.queryData(TdsAccountTag.class, whereall, null, null);
				
				List<String> inIdList = new ArrayList<String>();
				if(StringUtils.isNotEmpty(alltaglist)) {
					for (TdsAccountTag tag : alltaglist) {
						if(!haveTagList.contains(tag.getTagid())) {
							inIdList.add(tag.getTagid());
						}
					}
					
					if(inIdList.size() > 600) {
						List<String> tids = new ArrayList<String>();
						for (String tagId : inIdList) {
							tids.add(tagId);
							if(tids.size() >= 600) {
								Where where = Where.create();
								where.eq(TdsAccountTag::getTmused, 1);
								where.eq(TdsAccountTag::getUnitCode, unitCode);
								where.eq(TdsAccountTag::getVermark, vid);
								if(StringUtils.isNotEmpty(tagType) && Coms.judgeLong(tagType)) {
									where.eq(TdsAccountTag::getCtype, Integer.parseInt(tagType));
								}
								where.in(TdsAccountTag::getTagid, tids.toArray());
								if(StringUtils.isNotEmpty(searchName)) {
									where.and().lb();
									where.like(TdsAccountTag::getClass1, searchName);
									where.or().like(TdsAccountTag::getTagname, searchName);
									where.or().like(TdsAccountTag::getTagnumber, searchName);
									where.rb();
								}
								intaglist.addAll(srv.queryData(TdsAccountTag.class, where, null, null));
								tids.clear();
							}
						}
						if(tids.size() > 0) {
							Where where = Where.create();
							where.eq(TdsAccountTag::getTmused, 1);
							where.eq(TdsAccountTag::getUnitCode, unitCode);
							where.eq(TdsAccountTag::getVermark, vid);
							if(StringUtils.isNotEmpty(tagType) && Coms.judgeLong(tagType)) {
								where.eq(TdsAccountTag::getCtype, Integer.parseInt(tagType));
							}
							where.in(TdsAccountTag::getTagid, tids.toArray());
							if(StringUtils.isNotEmpty(searchName)) {
								where.and().lb();
								where.like(TdsAccountTag::getClass1, searchName);
								where.or().like(TdsAccountTag::getTagname, searchName);
								where.or().like(TdsAccountTag::getTagnumber, searchName);
								where.rb();
							}
							intaglist.addAll(srv.queryData(TdsAccountTag.class, where, null, null));
						}
						
						Collections.sort(intaglist, (t1, t2) -> t1.getTmsort()==null || t2.getTmsort()==null ? -1 : t1.getTmsort() - t2.getTmsort());//重新排序 
						
						if(page!=null) {
							taglist = new ArrayList<TdsAccountTag>();
							page.setTotal(intaglist.size());
							int size = page.getSize();
							int no = page.getPage();
							int si = (no-1)*size;
							int ei = no*size;
							
							int i=0, lj = 0;
							for (TdsAccountTag tag : intaglist) {
								if(i >= si && i < ei) {
									taglist.add(tag);
									lj++;
									if(lj >= size) {
										break;
									}
								}
								i++;
							}
						}else {
							taglist = intaglist;
						}
						
					}else {
						if(StringUtils.isNotEmpty(inIdList)) {
							Where where = Where.create();
							where.eq(TdsAccountTag::getTmused, 1);
							where.eq(TdsAccountTag::getUnitCode, unitCode);
							where.eq(TdsAccountTag::getVermark, vid);
							if(StringUtils.isNotEmpty(inIdList)) {
								where.in(TdsAccountTag::getTagid, inIdList.toArray());
							}
							if(StringUtils.isNotEmpty(tagType)) {
								where.eq(TdsAccountTag::getCtype, tagType);
							}
							if(StringUtils.isNotEmpty(searchName)) {
								where.and().lb();
								where.like(TdsAccountTag::getClass1, searchName);
								where.or().like(TdsAccountTag::getTagname, searchName);
								where.or().like(TdsAccountTag::getTagnumber, searchName);
								where.rb();
							}
							Order order = Order.create();
							order.order(TdsAccountTag::getTmsort);
							taglist = srv.queryData(TdsAccountTag.class, where, order, page);
						}else {
							taglist = new ArrayList<TdsAccountTag>();
						}
					}
				}
				
//				Where where = Where.create();
//				where.eq(TdsAccountTag::getTmused, 1);
//				where.eq(TdsAccountTag::getUnitCode, unitCode);
//				where.eq(TdsAccountTag::getVermark, vid);
//				if(StringUtils.isNotEmpty(haveTagList)) {
//					List<String> tagIds = new ArrayList<String>();
//					for (String tagId : haveTagList) {
//						tagIds.add(tagId);
//						if(tagIds.size() >= 500) {
//							where.notIn(TdsAccountTag::getTagid, tagIds.toArray());
//							tagIds.clear();
//						}
//					}
//					if(tagIds.size() > 0) {
//						where.notIn(TdsAccountTag::getTagid, haveTagList.toArray());
//					}
//				}
//				if(StringUtils.isNotEmpty(tagType)) {
//					where.eq(TdsAccountTag::getCtype, tagType);
//				}
//				if(StringUtils.isNotEmpty(searchName)) {
//					where.and().lb();
//					where.like(TdsAccountTag::getClass1, searchName);
//					where.or().like(TdsAccountTag::getTagname, searchName);
//					where.or().like(TdsAccountTag::getTagnumber, searchName);
//					where.rb();
//				}
//				Order order = Order.create();
//				order.order(TdsAccountTag::getTmsort);
//				taglist = srv.queryData(TdsAccountTag.class, where, order, page);
				
//				Collections.sort(taglist, (t1, t2) -> t1.getTmsort() - t2.getTmsort());//重新排序 
				
			}else {
				
				Where where = Where.create();
				where.eq(TdsAccountTag::getTmused, 1);
				where.eq(TdsAccountTag::getUnitCode, unitCode);
				where.eq(TdsAccountTag::getVermark, vid);
				if(StringUtils.isNotEmpty(haveTagList)) {
					where.notIn(TdsAccountTag::getTagid, haveTagList.toArray());
				}
				if(StringUtils.isNotEmpty(tagType)) {
					where.eq(TdsAccountTag::getCtype, tagType);
				}
				if(StringUtils.isNotEmpty(searchName)) {
					where.and().lb();
					where.like(TdsAccountTag::getClass1, searchName);
					where.or().like(TdsAccountTag::getTagname, searchName);
					where.or().like(TdsAccountTag::getTagnumber, searchName);
					where.rb();
				}
				Order order = Order.create();
				order.order(TdsAccountTag::getTmsort);
				taglist = srv.queryData(TdsAccountTag.class, where, order, page);
			}
			
		}
		
		return taglist;
	}
	
	private List<TdsAccountFormMeter> getFormMeterList(String unitCode, String formCode, String tagType) {
		Where where = Where.create();
		where.eq(TdsAccountFormMeter::getTmused, 1);
		where.eq(TdsAccountFormMeter::getTypeCode, 1);
		where.eq(TdsAccountFormMeter::getUnitCode, unitCode);
		where.eq(TdsAccountFormMeter::getAccountid, formCode);
		if(StringUtils.isNotEmpty(tagType)) {
			where.eq(TdsAccountFormMeter::getTagType, "3".equals(tagType)?3:2);
		}
		List<TdsAccountFormMeter> list = srv.queryList(TdsAccountFormMeter.class, where, null);
		return list;
	}

	@Override
	public Boolean initDefaultAccount(FormParam dto) {
		TdsAccountForm defaultForm = null;
		
		Where wheref = Where.create();
		wheref.in(TdsAccountForm::getTypeCode, 0);
		List<TdsAccountForm> rlist = srv.queryData(TdsAccountForm.class, wheref, null, null);
		if(StringUtils.isNotEmpty(rlist)) {
		}else {
			//初始化后，关联表单为空
			defaultForm = new TdsAccountForm();
			defaultForm.setId(TMUID.getUID());
			defaultForm.setTypeCode(0);
			defaultForm.setAccountName("交接班录入");
			defaultForm.setTmsort(0);
			defaultForm.setTmused(1);
			
			srv.insert(defaultForm);
		}
		return true;
	}

	@Override
	public Boolean saveAccountTagByClass1(FormParam dto) {
		Boolean flag = true;
		
		String unitCode = dto.getUnitCode();
		String formCode = dto.getFormCode();
		String tagType = dto.getTagType();
		String class1 = dto.getClass1();
		
		//条件中需去除自定义台账同核算对象已设置的仪表
		List<TdsAccountFormMeter> haveMeterList = getHaveUnitMeterList(unitCode, formCode, tagType);
		List<String> haveTagList = new ArrayList<String>();
		if(StringUtils.isNotEmpty(haveMeterList)) {
			for (TdsAccountFormMeter meter : haveMeterList) {
				haveTagList.add(meter.getTagid());
			}
		}
		
		//核算对象仪表数据
		List<TdsAccountTag> tagList = getUnitTagList(unitCode, formCode, haveTagList, tagType, null);//
		List<TdsAccountTag> updtagList = new ArrayList<TdsAccountTag>();
		//已定制的仪表数据
		List<TdsAccountFormMeter> mlist = getFormMeterList(unitCode, formCode, tagType);
		List<String> hlist = new ArrayList<String>();
		if(StringUtils.isNotEmpty(mlist)) {
			for (TdsAccountFormMeter meter : mlist) {
				hlist.add(meter.getTagid());
			}
		}
		
		List<TdsAccountFormMeter> addMeterList = new ArrayList<TdsAccountFormMeter>();
		
		if(StringUtils.isNotEmpty(class1)) {//按一级分类设置仪表
			List<String> clist = Coms.StrToList(class1, ",,,");
			for (TdsAccountTag tag : tagList) {
				boolean is = false;
				for (String c1 : clist) {
					if(c1.equals(tag.getClass1())) {
						is = true;
						break;
					}
				}
				if(is) {
					TdsAccountFormMeter obj = ObjUtils.copyTo(tag, TdsAccountFormMeter.class);
					obj.setId(TMUID.getUID());
					obj.setTypeCode(1);
					if("2".equals(tag.getCtype())) {
						obj.setTagType(2);
					}else if("3".equals(tag.getCtype())) {
						obj.setTagType(3);
					}
					obj.setAccountid(formCode);
					obj.setTmused(1);
					addMeterList.add(obj);
					
					tag.setCustomcode(formCode);
					updtagList.add(tag);
				}else if(hlist.contains(tag.getTagid())) {
					tag.setCustomcode(null);
					updtagList.add(tag);
				}
			}
		}
		
		if(StringUtils.isNotEmpty(mlist)) {
			flag = flag && 1 == srv.deleteByIdBatch(mlist);
		}
		
		if(StringUtils.isNotEmpty(addMeterList)) {
			flag = flag && 1 == srv.insertBatch(addMeterList);
			srv.updateBatch(updtagList, 200);
		}
		
		if(StringUtils.isNotEmpty(mlist) || StringUtils.isNotEmpty(addMeterList)) {
			deleteRedit(formCode);
		}
		
		return flag;
	}

	@Override
	public List<TdsAccountTag> getAccountClass1(FormParam dto) {
		List<TdsAccountTag> rlist = new ArrayList<TdsAccountTag>();
		
		String unitCode = dto.getUnitCode();
		String formCode = dto.getFormCode();
		String tagType = dto.getTagType();
		
		//条件中需去除自定义台账同核算对象已设置的仪表
		List<TdsAccountFormMeter> haveMeterList = getHaveUnitMeterList(unitCode, formCode, tagType);
		List<String> haveTagList = new ArrayList<String>();
		if(StringUtils.isNotEmpty(haveMeterList)) {
			for (TdsAccountFormMeter meter : haveMeterList) {
				haveTagList.add(meter.getTagid());
			}
		}
		
		//核算对象仪表数据
		List<TdsAccountTag> tagList = getUnitTagList(unitCode, formCode, haveTagList, tagType, null);//
		
		if(StringUtils.isNotEmpty(tagList)) {
			List<String> clist = new ArrayList<String>();
			for (TdsAccountTag tag : tagList) {
				if(!clist.contains(tag.getClass1())) {
					clist.add(tag.getClass1());
				}
			}
			
			for (int i = 0,il=clist.size(); i < il; i++) {
				TdsAccountTag tag = new TdsAccountTag();
				tag.setId(String.valueOf(i));
				tag.setClass1(clist.get(i));
				rlist.add(tag);
			}
		}
		return rlist;
	}

}
