package com.yunhesoft.system.applyConf.service;

import java.util.List;

import com.yunhesoft.system.applyConf.entity.dto.FormParam;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormManage;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormMeter;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormManageVo;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormMeterVo;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormVo;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.tds.entity.po.TdsAccountTag;

public interface IAccountFormService {
	
	/**
	 * @category 初始化默认台账
	 * @param dto
	 * @return
	 */
	Boolean initDefaultAccount(FormParam dto);
	/**
	 * @category 获取台账管理数据
	 * @param dto
	 * @return
	 */
	List<TdsAccountFormVo> getFormManageList(FormParam dto);
	/**
	 * @category 保存台账管理数据
	 * @param dto
	 * @return
	 */
	List<TdsAccountFormVo> saveFormList(FormParam dto);
	/**
	 * @category 删除台账管理数据
	 * @param dto
	 * @return
	 */
	Boolean deleteFormList(FormParam dto);
	/**
	 * @category 获取台账管理权限数据
	 * @param dto
	 * @return
	 */
	List<TdsAccountFormManageVo> getFormPermList(FormParam dto);
	/**
	 * @category 保存台账管理权限数据
	 * @param dto
	 * @return
	 */
	Boolean saveFormPermList(FormParam dto);
	
	/**
	 * @category 获取台账仪表管理数据
	 * @param dto
	 * @return
	 */
	List<TdsAccountFormMeterVo> geAccountTagManage(FormParam dto);
	/**
	 * @category 保存台账仪表管理数据
	 * @param dto
	 * @return
	 */
	Boolean saveAccountTagManage(FormParam dto);
	/**
	 * @category 获取自定义台账其他相同核算对象已设置的仪表
	 * @param unitCode
	 * @param formCode
	 * @return
	 */
	List<TdsAccountFormMeter> getHaveUnitMeterList(String unitCode, String formCode, String tagType);
	/**
	 * @category 获取当前日期最近版本核算单元的仪表信息列表
	 * @param unitCode
	 * @return
	 */
	List<TdsAccountTag> getUnitTagList(String unitCode, String formCode, List<String> haveTagList, String tagType, Pagination<?> page);
	/**
	 * @category 获取指定日期最近版本核算单元的仪表信息列表
	*/
	List<TdsAccountTag> getUnitTagList(String rq, String unitCode, String formCode, List<String> haveTagList, String tagType, Pagination<?> page);
	
	/**
	 * @category 保存台账仪表管理数据
	 * @param dto
	 * @return
	 */
	Boolean saveAccountTagByClass1(FormParam dto);
	/**
	 * @category 获取仪表一级仪分类列表
	 * @param dto
	 * @return
	 */
	List<TdsAccountTag> getAccountClass1(FormParam dto);
}
