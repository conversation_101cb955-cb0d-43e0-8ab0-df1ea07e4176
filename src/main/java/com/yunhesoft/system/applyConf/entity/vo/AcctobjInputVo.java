package com.yunhesoft.system.applyConf.entity.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AcctobjInputVo {

//	@ApiModelProperty("采集点分类数据")
    private List<AcctobjInputFlVo> flData;

//    @ApiModelProperty("数据保存标识：1=添加，-1=删除，其它=修改")
    private Integer rowFlag;

//    @ApiModelProperty("智能化岗位责任清单实例任务 ID")
    private String taskId;

	private String id;
//	@ApiModelProperty(value = "业务活动ID", example = "BA：Business Activity")
    private String baId;

//    @ApiModelProperty(value = "业务活动名称", example = "BA：Business Activity")
    private String baName;

//    @ApiModelProperty(value = "核算对象ID", example = "核算对象ID1")
    private String acctobjId;

//    @ApiModelProperty(value = "核算对象名称", example = "核算对象名称1")
    private String acctobjName;

//    @ApiModelProperty(value = "录入时间", example = "2024-01-14 11:34:24")
    private Date inputTime;

//    @ApiModelProperty(value = "录入时所在位置经度", example = "123")
    private Double inputLongitude;

//    @ApiModelProperty(value = "录入时所在位置纬度", example = "123")
    private Double inputLatitude;

//    @ApiModelProperty(value = "作业半径", example = "123")
    private Double operatingRadius;

//    @ApiModelProperty(value = "班次代码", example = "班次代码1")
    private String bcdm;

//    @ApiModelProperty(value = "班次名称", example = "班次名称1")
    private String bcmc;

//    @ApiModelProperty(value = "上班时间", example = "2024-01-15 11:34:24")
    private Date sbsj;

//    @ApiModelProperty(value = "下班时间", example = "2024-01-16 11:34:24")
    private Date xbsj;

//    @ApiModelProperty(value = "班组ID", example = "班组ID1")
    private String teamId;

//    @ApiModelProperty(value = "班组名称", example = "班组名称1")
    private String teamName;

//    @ApiModelProperty(value = "可用标识", example = "1=可用，0=无效")
    private Integer tmused;

    //    @ApiModelProperty(value = "活动录入时间", example = "2024-01-14 11:34:24")
    private Date jobInputTime;

}
