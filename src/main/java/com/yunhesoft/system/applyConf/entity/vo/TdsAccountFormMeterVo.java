package com.yunhesoft.system.applyConf.entity.vo;

import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormMeter;

import lombok.Getter;
import lombok.Setter;
@Setter
@Getter
public class TdsAccountFormMeterVo extends TdsAccountFormMeter{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String ctype;//仪表类型
	private Boolean showMark;//指定使用标识
	private Integer showMarkInt;//指定使用标识
	private String meterId;//指定保存的数据ID
	private Boolean usedMark;//手工选择是否使用
	private Integer usedMarkInt;//手工选择是否使用
}
