package com.yunhesoft.system.applyConf.entity.vo;

import java.util.List;

import lombok.Data;

@Data
public class AccountSaveDto {

//	@ApiModelProperty("业务类型，collectionPoint = 采集点，routingInspection = 巡检")
    String type;
    
//    @ApiModelProperty("操作类型，confirm = 确认")
    private String operType;

//    @ApiModelProperty("采集点录入数据")
    List<AcctobjInputVo> collectionPointInputData;
    
//    @ApiModelProperty("行云流表单数据 ID")
    private String formDataId;
    
    private String taskId;
}
