package com.yunhesoft.system.applyConf.entity.dto;

import java.util.List;

import com.yunhesoft.system.applyConf.entity.po.TdsAccountForm;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormManageVo;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormMeterVo;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormVo;
import com.yunhesoft.system.kernel.service.model.Pagination;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class FormParam extends TdsAccountForm{

	String delids;
	
	String tagType;
	
	/** 分页当前页数 */
	Integer pageNum;

	/** 分页每页记录数 */
	Integer pageSize;
	
	Pagination<?> page; //分页
	
	List<TdsAccountFormVo> data;
	
	List<TdsAccountFormManageVo> mlist;
	
	List<TdsAccountFormMeterVo> meterList;
	
	String class1;//一级分类名
	
	String searchName;//查询名称
	
}
