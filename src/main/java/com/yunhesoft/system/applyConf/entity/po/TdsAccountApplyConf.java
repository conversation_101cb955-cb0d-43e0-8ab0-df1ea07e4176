package com.yunhesoft.system.applyConf.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 台账应用配置表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_ACCOUNT_APPLY_CONF")
public class TdsAccountApplyConf extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 应用模型account */
    @Column(name="APPLY_MODE", length=50)
    private String applyMode;
    
    /** 应用机构默认空 */
    @Column(name="APPLY_ORG", length=50)
    private String applyOrg;
    
    /** 参数名称 */
    @Column(name="APPLY_NAME", length=100)
    private String applyName;
    
    /** 参数别名 */
    @Column(name="APPLY_ALIAS", length=100)
    private String applyAlias;
    
    /** 参数值 */
    @Column(name="APPLY_VALUE", length=2000)
    private String applyValue;
    
    /** 参数描述 */
    @Column(name="APPLY_DESC", length=2000)
    private String applyDesc;
    
    /** 参数设置组件 */
    @Column(name="APPLY_COM", length=50)
    private String applyCom;
    
    /** 备选值内容 */
    @Column(name="APPLY_KEYS", length=2000)
    private String applyKeys;
    
    /** 备选文本内容 */
    @Column(name="APPLY_VALUES", length=2000)
    private String applyValues;
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** 使用 */
    @Column(name="TMUSED")
    private Integer tmused;
    

}

