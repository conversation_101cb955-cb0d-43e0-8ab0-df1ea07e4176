package com.yunhesoft.system.applyConf.entity.dto;

import java.util.Date;
import java.util.List;

import com.yunhesoft.system.tds.entity.vo.TdsAccountTagVo;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class TagObj {

	private String kssj;//开始时间
	private String jzsj;//截止时间
	private Date kssjd;//开始时间对象 可选
	private Date jzsjd;//截止时间对象 可选
	
	private String userId;//人员ID
    private String userName;//人员名称
	
    private List<TdsAccountTagVo> list;//仪表信息
}
