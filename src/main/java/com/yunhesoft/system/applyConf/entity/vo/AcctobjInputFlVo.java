package com.yunhesoft.system.applyConf.entity.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;


//@ApiModel("采集点分类数据")
@Data
public class AcctobjInputFlVo {
//    @ApiModelProperty("明细数据")
    private List<AcctobjInputmxVo> mxData;
    
//    @ApiModelProperty(value = "业务活动ID", example = "业务活动ID1")
    private String baId;

//    @ApiModelProperty(value = "核算对象ID", example = "核算对象ID1")
    private String acctobjId;

//    @ApiModelProperty(value = "录入主表ID", example = "8533dedc4aff57c5")
    private String iptId;

//    @ApiModelProperty(value = "分类ID", example = "分类ID1")
    private String flId;

//    @ApiModelProperty(value = "分类名称", example = "分类名称1")
    private String flName;

//    @ApiModelProperty(value = "可用标识", example = "123")
    private Integer tmused;

//    @ApiModelProperty(value = "序号", example = "123")
    private Integer sn;

//    @ApiModelProperty(value = "录入时间", example = "2024-01-14 11:34:24")
    private Date inputTime;
}
