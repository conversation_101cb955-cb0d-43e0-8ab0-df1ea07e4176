package com.yunhesoft.system.applyConf.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 台账修改日志表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_ACCOUNT_LOG")
public class TdsAccountLog extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 数据源别名 */
    @Column(name="TDSALIAS", length=50)
    private String tdsalias;
    
    /** 核算对象代码 */
    @Column(name="UNIT_CODE", length=100)
    private String unitCode;
    
    /** 核算对象名称 */
    @Column(name="UNIT_NAME", length=100)
    private String unitName;
    
    /** 班次代码 */
    @Column(name="SHIFT_CODE", length=100)
    private String shiftCode;
    
    /** 班次名称 */
    @Column(name="SHIFT_NAME", length=100)
    private String shiftName;
    
    /** 上班时间字符串 */
    @Column(name="SBSJSTR", length=30)
    private String sbsjstr;
    
    /** 下班时间字符串 */
    @Column(name="XBSJSTR", length=30)
    private String xbsjstr;
    
    /** 机构代码 */
    @Column(name="ORG_CODE", length=50)
    private String orgCode;
    
    /** 机构名称 */
    @Column(name="ORG_NAME", length=100)
    private String orgName;
    
    /** 仪表ID（采集点ID） */
    @Column(name="TAG_ID", length=50)
    private String tagId;
    
    /** 仪表名称 */
    @Column(name="TAG_NAME", length=100)
    private String tagName;
    
    /** 时间字符串 */
    @Column(name="SJSTR", length=50)
    private String sjstr;
    
    /** 修改描述 */
    @Column(name="MEMO", length=2000)
    private String memo;
    
    /** 修改人 */
    @Column(name="UPD_USER_NAME", length=50)
    private String updUserName;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}
