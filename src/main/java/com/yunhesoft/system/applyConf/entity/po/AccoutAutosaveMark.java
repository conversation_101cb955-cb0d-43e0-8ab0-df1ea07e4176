package com.yunhesoft.system.applyConf.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 台账仪表自动保存时间记录
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "ACCOUT_AUTOSAVE_MARK")
public class AccoutAutosaveMark extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 应用类型MongoDB或relation */
    @Column(name="APPLY_TYPE", length=50)
    private String applyType;
    
    /** 最后更新实际 */
    @Column(name="UPD_TIME")
    private Date updTime;
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** 使用 */
    @Column(name="TMUSED")
    private Integer tmused;
    
}
