package com.yunhesoft.system.applyConf.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 台账表单管理范围表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_ACCOUNT_FORM_MANAGE")
public class TdsAccountFormManage extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 类型1人2岗3机构 */
    @Column(name="MODE_TYPE", length=50)
    private String modeType;
    
    /** 台账id */
    @Column(name="ACCOUNTID", length=50)
    private String accountid;
    
    /** 管理编码 */
    @Column(name="MANAGE_CODE", length=100)
    private String manageCode;
    
    /** 管理名 */
    @Column(name="MANAGE_NAME", length=100)
    private String manageName;
    
    /** 机构代码，用于机构岗位记录 */
    @Column(name="ORG_CODE", length=100)
    private String orgCode;
    
    /** 岗位代码，用于机构岗位记录 */
    @Column(name="POST_CODE", length=100)
    private String postCode;
    
    /** 录入标识 */
    @Column(name="ADD_MARK")
    private Integer addMark;
    
    /** 补录标识 */
    @Column(name="MANAGE_MARK")
    private Integer manageMark;
    
    /** 查询标识 */
    @Column(name="SEARCH_MARK")
    private Integer searchMark;
    
    /** 管理范围 */
    @Column(name="MANAGE_BOUND", length=20)
    private String manageBound;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}
