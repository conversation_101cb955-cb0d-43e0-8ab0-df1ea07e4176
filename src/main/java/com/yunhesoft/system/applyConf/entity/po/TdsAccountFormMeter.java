package com.yunhesoft.system.applyConf.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 台账表单仪表配置表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_ACCOUNT_FORM_METER")
public class TdsAccountFormMeter extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 类型1仪表2设备 */
    @Column(name="TYPE_CODE")
    private Integer typeCode;
    
    /** 设备编码 */
    @Column(name="DEV_CODE", length=50)
    private String devCode;
    
    /** 设备名称 */
    @Column(name="DEV_NAME", length=50)
    private String devName;
    
    /** 台账id */
    @Column(name="ACCOUNTID", length=50)
    private String accountid;
    
    /** 日期版本标识 */
    @Column(name="VERMARK", length=50)
    private String vermark;
    
    /** 日期 */
    @Column(name="RQ", length=50)
    private String rq;
    
    /** 数据源别名 */
    @Column(name="TDSALIAS", length=50)
    private String tdsalias;
    
    /** 设置类型1班组2核算单元 */
    @Column(name="CONF_TYPE")
    private Integer confType;
    
    /** 单元代码班组代码或核算单元代码 */
    @Column(name="CONF_CODE", length=100)
    private String confCode;
    
    /** 核算单元代码 */
    @Column(name="UNIT_CODE", length=100)
    private String unitCode;
    
    /** 所属区域 */
    @Column(name="BELONG_ZONE", length=100)
    private String belongZone;
    
    /** 所属设备 */
    @Column(name="BELONG_DEV", length=100)
    private String belongDev;
    
    /** 所属名称 */
    @Column(name="BELONG_TAG", length=100)
    private String belongTag;
    
    /** 显示名称 */
    @Column(name="SHOW_NAME", length=100)
    private String showName;
    
    /** 仪表位号 */
    @Column(name="TAGNUMBER", length=200)
    private String tagnumber;
    
    /** 单位 */
    @Column(name="SDUNIT", length=100)
    private String sdunit;
    
    /** 小数位数 */
    @Column(name="DECIMAL_DEGIT")
    private Integer decimalDegit;
    
    /** 数据来源 */
    @Column(name="DATASOURCE", length=1000)
    private String datasource;
    
    /** 仪表类型 */
    @Column(name="TAG_TYPE")
    private Integer tagType;
    
    /** 仪表标识 */
    @Column(name="TAGID", length=100)
    private String tagid;
    
    /** 对齐方式 */
    @Column(name="ALIGN", length=10)
    private String align;
    
    /** 可编辑标识，默认不可编辑 */
    @Column(name="EDIT_MARK")
    private Integer editMark;
    
    /** 宽度 */
    @Column(name="WIDTH")
    private Integer width;
    
    /** 上限 */
    @Column(name="UP_LIMIT")
    private Double upLimit;
    
    /** 下限 */
    @Column(name="LOWER_LIMIT")
    private Double lowerLimit;
    
    /** 1级分类 */
    @Column(name="CLASS1", length=100)
    private String class1;
    
    /** 2级分类 */
    @Column(name="CLASS2", length=100)
    private String class2;
    
    /** 3级分类 */
    @Column(name="CLASS3", length=100)
    private String class3;
    
    /** 4级分类 */
    @Column(name="CLASS5", length=100)
    private String class5;
    
    /** 5级分类 */
    @Column(name="CLASS6", length=100)
    private String class6;
    
    /** 6级分类 */
    @Column(name="CLASS4", length=100)
    private String class4;
    
    /** 7级分类 */
    @Column(name="CLASS7", length=100)
    private String class7;
    
    /** 8级分类 */
    @Column(name="CLASS8", length=100)
    private String class8;
    
    /** 9级分类 */
    @Column(name="CLASS9", length=100)
    private String class9;
    
    /** 10级分类 */
    @Column(name="CLASS10", length=100)
    private String class10;
    
  //附加属性，用于limis取数，属性同步源于采集点
    /** 装置名称 */
	@Column(name = "PROCESSUNITNAME", length = 200)
	private String processUnitName;
	
	/** 产品名称 */
	@Column(name = "PRODUCTNAME", length = 200)
	private String productName;
	
	/** 采样点 */
	@Column(name = "SAMPLINGPOINT", length = 200)
	private String samplingPoint;
	
	/** 分析名称 */
	@Column(name = "ANALYSISNAME", length = 200)
	private String analysisName;
	
	/** 分析分项名称 */
	@Column(name = "ANALYSISSUBNAME", length = 200)
	private String analysisSubName;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}
