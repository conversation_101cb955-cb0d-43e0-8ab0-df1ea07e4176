package com.yunhesoft.system.applyConf.entity.dto;


import com.yunhesoft.system.applyConf.entity.vo.ApplyConfVo;
import com.yunhesoft.system.kernel.service.model.Pagination;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class ApplyParams {

	/** 应用模型account */
    String applyMode;

    /** 应用机构默认空 */
    String applyOrg;

    /** 参数名称 */
    String applyName;

    /** 参数别名 */
    String applyAlias;

    /** 参数描述 */
    String applyDesc;

    /** 数据 */
    List<ApplyConfVo> list;

    /** 分页对象 */
	Pagination page;

	/** 核算对象（活动） */
	String unitCode;

	/** 子活动（设备）如果无设备，代码同核算对象 */
	String itemCode;

	/** 日期 */
	String rq;

	/** 班次 */
	String bc;

	/** 上班时间 */
	String sbsj;

	/** 下班时间 */
	String xbsj;

	String teamId;

}
