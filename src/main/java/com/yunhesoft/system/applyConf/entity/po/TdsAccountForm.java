package com.yunhesoft.system.applyConf.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 台账数据源保存数据主表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_ACCOUNT_FORM")
public class TdsAccountForm extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 类别标识0默认 1自定义 2动态默认 */
    @Column(name="TYPE_CODE")
    private Integer typeCode;
    
    /** 日期 */
    @Column(name="RQ", length=50)
    private String rq;
    
    /** 台账名称 */
    @Column(name="ACCOUNT_NAME", length=50)
    private String accountName;
    
    /** 表单代码 */
    @Column(name="FORM_CODE", length=100)
    private String formCode;
    
    /** 表单名称 */
    @Column(name="FORM_NAME", length=100)
    private String formName;
    
    /** 核算单元代码 */
    @Column(name="UNIT_CODE", length=500)
    private String unitCode;
    
    /** 核算对象名称 */
    @Column(name="UNIT_NAME", length=500)
    private String unitName;
    
    /** 管理范围名称 */
    @Column(name="MANAGE_ROUND", length=2000)
    private String manageRound;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;

    
    @Transient
    private String qx;
    /** 录入标识 */
    @Transient
    private Integer addMark;
    
    /** 补录标识 */
    @Transient
    private Integer manageMark;
    
    /** 查询标识 */
    @Transient
    private Integer searchMark;
}
