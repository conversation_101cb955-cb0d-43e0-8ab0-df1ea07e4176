package com.yunhesoft.system.applyConf.entity.vo;

import lombok.Data;

import java.util.Date;

@Data
public class AcctobjInputmxVo {

//	@ApiModelProperty("数据保存标识：1=添加，-1=删除，其它=修改")
    private Integer rowFlag;

//    @ApiModelProperty(value = "业务活动ID", example = "BA：Business Activity")
//    @Column(name = "BA_ID", length = 200)
    private String baId;

//    @ApiModelProperty(value = "核算对象ID", example = "核算对象ID1")
//    @Column(name = "ACCTOBJ_ID", length = 200)
    private String acctobjId;

//    @ApiModelProperty(value = "录入主表ID", example = "ACCTOBJ_INPUT 表的 ID")
//    @Column(name = "IPT_ID", length = 50)
    private String iptId;

//    @ApiModelProperty(value="录入采集点分类表ID", example="4427671afa3379e4")
//    @Column(name = "IPT_FL_ID", length = 50)
    private String iptFlId;

//    @ApiModelProperty(value="仪表位号", example="仪表位号1")
//    @Column(name="TAG_NO", length=200)
    private String tagNo;

//    @ApiModelProperty(value="录入时间", example="2024-01-20 07:59:36")
//    @Column(name="INPUT_TIME")
    private Date inputTime;

//    @ApiModelProperty(value="采集点ID", example="采集点ID1")
//    @Column(name="COLLECT_POINT_ID", length=50)
    private String collectPointId;

//    @ApiModelProperty(value = "采集点", example = "采集点1")
//    @Column(name = "COLLECT_POINT", length = 200)
    private String collectPoint;

//    @ApiModelProperty(value = "采集点录入值", example = "")
//    @Column(name = "COLLECT_POINT_VAL", length = 4000)
    private String collectPointVal;

//    @ApiModelProperty(value = "下拉框选择值对应的显示值", example = "")
//    @Column(name = "COLLECT_POINT_TEXT", length = 200)
    private String collectPointText;

//    @ApiModelProperty(value="序号", example="123")
    private Integer sn;

//    @ApiModelProperty(value = "可用标识", example = "1=可用，0=无效")
    private Integer tmused;

//    @ApiModelProperty(value = "录入组件类型", example = "空=文本输入框，textfield=文本输入框，numberfield=数值输入框，datetimefield=日期时间选择框，combobox=下拉选择框，checkbox=复选框")
//    @Column(name = "INPUT_COMP_TYPE", length = 50)
    private String inputCompType;

//    @ApiModelProperty(value = "录入组件备选数据", example = "下拉选择框的备选数据，格式为JSON数组：[{value:\"\",text:\"\"}]")
//    @Column(name = "INPUT_OPTIONS", length = 4000)
    private String inputOptions;

//    @ApiModelProperty(value = "设备维保备注信息", example = "对应设备维护保养表格中的记录列")
//    @Column(name = "EQUIP_MT_REMARK", length = 4000)
    private String equipMtRemark;

//    @ApiModelProperty(value="设备维保业务标识", example="1=是设备维保，0=不是")
//    @Column(name="IS_EQUIP_MAINTENANCE")
    private Integer isEquipMaintenance;

//    @ApiModelProperty(value = "采集点上传图片id", example = "")
//    @Column(name = "COLLECT_POINT_IMG_ID", length = 100)
    private String collectPointImgId;

//    @ApiModelProperty(value = "数据是否回写到influxdb：1、是（默认）；其他不是", example = "")
//    @Column(name = "IS_WRITE_BACK_INFLUXDB")
    private Integer isWriteBackInfluxdb;

    //    @ApiModelProperty(value = "活动录入时间", example = "2024-01-14 11:34:24")
    private Date jobInputTime;
}
