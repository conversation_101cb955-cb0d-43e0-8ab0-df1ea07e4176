package com.yunhesoft.system.applyConf.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.system.applyConf.entity.dto.ApplyParams;
import com.yunhesoft.system.applyConf.entity.dto.FormParam;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormMeterVo;
import com.yunhesoft.system.applyConf.service.IAccountFormService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 台账表单配置关系用相关函数
 * 
 * <AUTHOR>
 *
 */
@Api(tags = "台账数据源用相关函数")
@RestController
@RequestMapping("/accountForm")
public class AccountFormController extends BaseRestController {

	@Autowired
	private IAccountFormService serv; // 服务类

	
	/**配置部分*******************************************************************************************************/
	
	/**
	 * 初始化默认台账数据
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "初始化默认台账数据")
	@RequestMapping(value = "/initDefaultAccount", method = { RequestMethod.POST })
	public Res<?> initDefaultAccount(@RequestBody FormParam dto) {
		return Res.OK(serv.initDefaultAccount(dto));
	}
	/**
	 * 获取台账管理数据
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "获取台账管理数据")
	@RequestMapping(value = "/getFormManageList", method = { RequestMethod.POST })
	public Res<?> getFormManageList(@RequestBody FormParam dto) {
		return Res.OK(serv.getFormManageList(dto));
	}
	
	/**
	 * 保存台账管理数据
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "保存台账管理数据")
	@RequestMapping(value = "/saveFormList", method = { RequestMethod.POST })
	public Res<?> saveFormList(@RequestBody FormParam dto) {
		return Res.OK(serv.saveFormList(dto));
	}
	
	/**
	 * 删除台账管理数据
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "删除台账管理数据")
	@RequestMapping(value = "/deleteFormList", method = { RequestMethod.POST })
	public Res<?> deleteFormList(@RequestBody FormParam dto) {
		return Res.OK(serv.deleteFormList(dto));
	}
	
	
	/**
	 * 获取台账管理权限数据
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "获取台账管理权限数据")
	@RequestMapping(value = "/getFormPermList", method = { RequestMethod.POST })
	public Res<?> getFormPermList(@RequestBody FormParam dto) {
		return Res.OK(serv.getFormPermList(dto));
	}
	
	/**
	 * 保存台账管理权限数据
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "保存台账管理权限数据")
	@RequestMapping(value = "/saveFormPermList", method = { RequestMethod.POST })
	public Res<?> saveFormPermList(@RequestBody FormParam dto) {
		return Res.OK(serv.saveFormPermList(dto));
	}
	
	/**
	 * 获取台账仪表管理数据
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "获取台账仪表管理数据")
	@RequestMapping(value = "/geAccountTagManage", method = { RequestMethod.POST })
	public Res<?> geAccountTagManage(@RequestBody FormParam dto) {
		Res<List<TdsAccountFormMeterVo>> res = new Res<List<TdsAccountFormMeterVo>>();
		Pagination<?> page = null;
		if (dto.getPageSize() != null && dto.getPageSize() > 0) {
			page = Pagination.create(dto.getPageNum() == null ? 1 : dto.getPageNum(), dto.getPageSize());
			dto.setPage(page);
		}
		res.setResult(serv.geAccountTagManage(dto));
		if(page!=null) {
			res.setTotal(dto.getPage().getTotal());
		}
		return res;
	}
	
	/**
	 * 保存台账仪表管理数据
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "保存台账仪表管理数据")
	@RequestMapping(value = "/saveAccountTagManage", method = { RequestMethod.POST })
	public Res<?> saveAccountTagManage(@RequestBody FormParam dto) {
		return Res.OK(serv.saveAccountTagManage(dto));
	}
	
	/**
	 * 按一级分类名称更新配置仪表管理数据
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "按一级分类名称更新配置仪表管理数据")
	@RequestMapping(value = "/saveAccountTagByClass1", method = { RequestMethod.POST })
	public Res saveAccountTagByClass1(@RequestBody FormParam dto) {
		return Res.OK(serv.saveAccountTagByClass1(dto));
	}
	
	/**
	 * 获取仪表一级仪分类列表
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "获取仪表一级仪分类列表")
	@RequestMapping(value = "/getAccountClass1", method = { RequestMethod.POST })
	public Res getAccountClass1(@RequestBody FormParam dto) {
		return Res.OK(serv.getAccountClass1(dto));
	}
	
	/**应用部分*******************************************************************************************************/
	
}
