package com.yunhesoft.system.applyConf.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.applyConf.entity.dto.ApplyParams;
import com.yunhesoft.system.applyConf.service.IApplyConfService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Pagination;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 台账应用参数管理Controller
 *
 */
@Log4j2
@RestController
@Api(tags = "应用配置管理")
@RequestMapping("/system/applyConf")
public class AccountApplyConfController extends BaseRestController {
	@Autowired
	private IApplyConfService srv;
	
	@ApiOperation(value = "查询数据列表")
	@RequestMapping(value = "/queryList", method = { RequestMethod.POST })
	public Res queryDataList(@RequestBody ApplyParams dto) {
		Pagination<?> page = getRequestPagination();
		return Res.OK(srv.queryList(dto));
	}


	@ApiOperation(value = "保存应用配置")
	@RequestMapping(value = "/saveConf", method = { RequestMethod.POST })
	public Res saveFolder(@RequestBody ApplyParams dto) {
		return Res.OK(srv.saveConf(dto));
	}


	@ApiOperation(value = "批量删除")
	@RequestMapping(value = "/deleteDataByIdList", method = { RequestMethod.POST })
	public Res deleteDataByIdList(@RequestBody List<String> idList) {
		return Res.OK(srv.deleteDataByIdList(idList));
	}
	
	@ApiOperation(value = "判断是否管理员")
	@RequestMapping(value = "/isAdmin", method = { RequestMethod.POST })
	public Res isAdmin(@RequestBody ApplyParams dto) {
		return Res.OK(SysUserUtil.isAdmin());
	}
	
	@ApiOperation(value = "获取核算对象（活动）配置，包括模式及区域列表")
	@RequestMapping(value = "/getUnitAccountConf", method = { RequestMethod.POST })
	public Res getUnitAccountInfo(@RequestBody ApplyParams dto) {
		return Res.OK(srv.getUnitAccountConf(dto.getUnitCode(), dto.getApplyAlias()));
	}
}
