package com.yunhesoft.system.applyConf.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.applyConf.entity.dto.FormParam;
import com.yunhesoft.system.applyConf.entity.dto.TagObj;
import com.yunhesoft.system.applyConf.service.IAccountSynService;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 台账数据同步相关函数
 * 
 * <AUTHOR>
 *
 */
@Api(tags = "台账数据同步相关函数")
@RestController
@RequestMapping("/accountSyn")
public class AccountSynController extends BaseRestController {

	@Autowired
	private IAccountSynService serv; // 服务类

	
	
	/**
	 * 台账移动端数据修改
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "台账移动端数据修改")
	@RequestMapping(value = "/mobileUpd", method = { RequestMethod.POST })
	public Res<?> mobileUpd(@RequestBody TagObj param) {
		return Res.OK(serv.mobileUpd(param));
	}
	
	/**
	 * 台账自动保存
	 * 
	 * @param dto
	 * @return
	 */
	@ApiOperation(value = "台账自动保存")
	@RequestMapping(value = "/accountAutoSave", method = { RequestMethod.POST })
	public Res<?> dbSyn(@RequestBody TagObj param) {
		serv.accountAutoSave(param);
		return Res.OK();
	}
}
