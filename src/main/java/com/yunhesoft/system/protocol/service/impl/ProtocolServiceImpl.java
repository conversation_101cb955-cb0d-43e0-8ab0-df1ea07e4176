package com.yunhesoft.system.protocol.service.impl;

import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.protocol.entity.po.BProtocol;
import com.yunhesoft.system.protocol.service.IProtocolService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class ProtocolServiceImpl implements IProtocolService {
    @Autowired
    private EntityService entityService;

    /**
     * 通过编码获取协议
     * @param code
     * @return
     */
    @Override
    public BProtocol byCode(String code) {
        Where where = Where.create();
        where.eq(BProtocol::getProtocolCode, code);
        where.eq(BProtocol::getTmused, 1);
        return entityService.queryObject(BProtocol.class, where);
    }
}
