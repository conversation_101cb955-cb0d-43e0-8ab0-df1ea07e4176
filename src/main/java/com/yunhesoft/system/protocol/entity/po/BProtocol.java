package com.yunhesoft.system.protocol.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ApiModel(value = "协议对象")
@Data
@Entity
@Table(name = "B_PROTOCOL")
public class BProtocol extends BaseEntity {
    @ApiModelProperty(value="PROTOCOL_NAME", example="协议名称")
    @Column(name="PROTOCOL_NAME", length=200)
    private String protocolName;

    @ApiModelProperty(value="PROTOCOL_CODE", example="协议编码")
    @Column(name="PROTOCOL_CODE", length=50)
    private String protocolCode;

    @ApiModelProperty(value="PROTOCOL_CONTENT", example="协议内容")
    @Column(name="PROTOCOL_CONTENT", length=4000)
    private String protocolContent;

    @ApiModelProperty(value="TMUSED", example="1=可用，0=不可用")
    @Column(name="TMUSED")
    private Integer tmused;
}
