package com.yunhesoft.system.protocol.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.protocol.service.IProtocolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "协议相关接口（用户服务协议、隐私协议...）")
@RequestMapping("/system/protocol")
public class ProtocolController extends BaseRestController {

    @Autowired
    IProtocolService iProtocolService;

    @ApiOperation(value = "通过编码获取协议")
    @PostMapping("/byCode")
    public Res<?> byCode(String code) {
        return Res.OK(iProtocolService.byCode(code));
    }

}
