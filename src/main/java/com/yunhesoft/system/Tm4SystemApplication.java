package com.yunhesoft.system;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.web.server.ConfigurableWebServerFactory;
import org.springframework.boot.web.server.ErrorPage;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.http.HttpStatus;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.transaction.annotation.EnableTransactionManagement;

//import com.yunhesoft.ssl.CertificateLoad;

@EnableEurekaClient
@EnableFeignClients(basePackages = {"com.yunhesoft"})
@EnableAspectJAutoProxy
@EntityScan(basePackages = { "com.yunhesoft" })
@ComponentScan("com.yunhesoft")
@EnableTransactionManagement
@EnableGlobalMethodSecurity(prePostEnabled = true)
@SpringBootApplication(exclude = { SecurityAutoConfiguration.class })
public class Tm4SystemApplication {
	public static void main(String[] args) {
//		CertificateLoad.loadAll();// 加载所有https证书
		SpringApplication.run(Tm4SystemApplication.class, args);
	}

	@Bean
	public WebServerFactoryCustomizer<ConfigurableWebServerFactory> containerCustomizer() {
		return new WebServerFactoryCustomizer<ConfigurableWebServerFactory>() {
			@Override
			public void customize(ConfigurableWebServerFactory container) {
				ErrorPage error404Page = new ErrorPage(HttpStatus.NOT_FOUND, "/index.html");
				container.addErrorPages(error404Page);
			}
		};
	}
}
