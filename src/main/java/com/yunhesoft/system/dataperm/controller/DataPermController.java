package com.yunhesoft.system.dataperm.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.system.dataperm.entity.dto.DataPermDto;
import com.yunhesoft.system.dataperm.entity.dto.DataPermSaveDto;
import com.yunhesoft.system.dataperm.service.ISysDataPermService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * TM4 数据权限
 * 
 * <AUTHOR>
 * 
 */
@Log4j2
@Api(tags = "系统数据权限")
@RestController
@RequestMapping("/system/dataperm")
public class DataPermController extends BaseRestController {

	@Autowired
	private ISysDataPermService srv; // 数据权限服务

	/**
	 * 获取数据权限对象列表树
	 * 
	 * @return
	 */
	@RequestMapping(value = "/getDataPermCfgTree", method = RequestMethod.POST)
	@ApiOperation(value = "获取数据权限对象列表树")
	public Res<?> getDataPermCfgTree() {
		return Res.OK(srv.getDataPermCfgTree());
	}

	/**
	 * 获取数据权限备选树
	 * 
	 * @param dataPermCode 权限对象编码
	 * @return
	 */
	@RequestMapping(value = "/getDataPermDataTree", method = RequestMethod.POST)
	@ApiOperation(value = "获取数据权限备选树")
	public Res<?> getDataPermCfgTree(@RequestParam String dataPermCode, @RequestParam String cfgCode) {
		return Res.OK(srv.getDataPermDataTree(dataPermCode, cfgCode));
	}

	/**
	 * 获取数据权限
	 * 
	 * @param dataPermCode 权限对象编码
	 * @return
	 */
	@RequestMapping(value = "/getDataPermValueByUserid", method = RequestMethod.POST)
	@ApiOperation(value = "通过人员id获取数据权限")
	public Res<?> getDataPerm(@RequestParam String userId, @RequestParam String dataPermCode) {
		return Res.OK(srv.getDataPerm(userId, dataPermCode));
	}

	/**
	 * 获取数据权限
	 * 
	 * @param dataPermCode 权限对象编码
	 * @return
	 */
	@RequestMapping(value = "/getDataPermValue", method = RequestMethod.POST)
	@ApiOperation(value = "获取数据权限")
	public Res<?> getDataPerm(@RequestParam String dataPermCode) {
		SysUser user = SysUserHolder.getCurrentUser();
		return Res.OK(srv.getDataPerm(user, dataPermCode));
	}

	/**
	 * 获取数据权限
	 * 
	 * @param dataPermCode 权限对象编码
	 * @return
	 */
	@RequestMapping(value = "/getDataPermByObjid", method = RequestMethod.POST)
	@ApiOperation(value = "通过对象id获取数据权限")
	public Res<?> getDataPermByObjid(@RequestBody DataPermDto param) {
		if (param != null) {
			return Res.OK(srv.getDataPermByObjid(param.getDatapermCode(), param.getObjtype(), param.getObjid()));
		} else {
			return Res.FAIL("参数传递错误！");
		}
	}

	@RequestMapping(value = "/saveDataPerm", method = RequestMethod.POST)
	@ApiOperation(value = "保存数据权限")
	public Res<?> saveDataPerm(@RequestBody DataPermSaveDto param) {
		if (param != null) {
			try {
				srv.saveDataPerm(param);
				return Res.OK();
			} catch (Exception e) {
				log.error("", e);
				return Res.FAIL(e.getMessage());
			}

		} else {
			return Res.FAIL("参数传递错误！");
		}
	}

	@RequestMapping(value = "/getDataPermData", method = RequestMethod.POST)
	@ApiOperation(value = "获取数据权限")
	public Res<?> getDataPermData(@RequestBody DataPermDto param) {
		if (param != null) {
			try {
				return Res.OK(srv.getDataPermData(param));
			} catch (Exception e) {
				log.error("", e);
				return Res.FAIL(e.getMessage());
			}
		} else {
			return Res.FAIL("参数传递错误！");
		}
	}

}
