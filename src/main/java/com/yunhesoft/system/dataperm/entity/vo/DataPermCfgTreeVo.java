package com.yunhesoft.system.dataperm.entity.vo;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据权限列表对象
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class DataPermCfgTreeVo {

	private String cfgCode;//配置信息编码，用于左侧树形展开后，能取到对应配置信息
	
	private String code;

	private String name;

	private Integer level;

	private Integer sort;

	private Integer datatype; // 数据类型，1：自定义；0：组织机构

	private List<DataPermCfgTreeVo> children = new ArrayList<DataPermCfgTreeVo>();

}
