package com.yunhesoft.system.dataperm.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据权限表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_DATAPERM")
public class SysDataperm extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 数据权限编码 */
	@Column(name = "DATAPERM_CODE", length = 100)
	private String datapermCode;

	/** 拥有数据权限的对象id */
	@Column(name = "OBJID", length = 100)
	private String objid;

	/** 拥有数据权限的对象类型 */
	@Column(name = "OBJTYPE")
	private Integer objtype;// 0:角色；1：人员；2：其他

	/** 数据权限值 */
	@Column(name = "DATACODE", length = 255)
	private String datacode;

}