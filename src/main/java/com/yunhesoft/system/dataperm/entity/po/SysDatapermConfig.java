package com.yunhesoft.system.dataperm.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据权限配置表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_DATAPERM_CONFIG")
public class SysDatapermConfig extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 模块编码 */
	@Column(name = "MODULECODE", length = 50)
	private String modulecode;

	/** 数据权限对象编码 */
	@Column(name = "CODE", length = 100)
	private String code;

	/** 数据权限对象名称 */
	@Column(name = "NAME", length = 50)
	private String name;

	/** 排序 */
	@Column(name = "TMSORT")
	private Integer tmsort;

	/** 数据权限类型 */
	@Column(name = "DATATYPE")
	private Integer datatype;// 数据类型，1：自定义；0：组织机构

	/** 数据备选数据加载类名 */
	@Column(name = "TREECLASSNAME", length = 255)
	private String treeClassName;

	/** 数据备选数据获取函数 */
	@Column(name = "TREEGETDATAFUN", length = 255)
	private String treeGetDataFun;
	
	/** 数据树形展开函数，有该函数则树形可以展开 */
	@Column(name = "TREEEXPANDDATAFUN", length = 255)
	private String treeExpandDataFun;
	
    /** 服务名 */
    @Column(name="SERVICE_NAME", length=500)
    private String serviceName;
    
    /** 树形获取服务 */
    @Column(name="TREEGETDATAURL", length=500)
    private String treeGetDataUrl;
    
    /** 树形展开服务 */
    @Column(name="TREEEXPANDDATAURL", length=500)
    private String treeExpandDataUrl;
}
