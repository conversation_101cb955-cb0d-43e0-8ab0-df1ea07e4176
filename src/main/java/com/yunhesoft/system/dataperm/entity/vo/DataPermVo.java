package com.yunhesoft.system.dataperm.entity.vo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.system.org.entity.vo.SysOrgTreeData;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DataPermVo {

	/** 数据权限编码 */
	private String datapermCode;

	/** 数据对象类型 */
	private Integer objtype;

	/** 数据对象id */
	private String objid;

	/** 数据范围 */
	// 1：全部；2:仅本人；30：本部门；40：本部门及以下；-1：自定义
	// 1,2 两种类型 datacodeList 不返回任何值
	private Integer datatype;

	/** 数据权限值 */
	private List<String> datacodeList = new ArrayList<String>();
	/*数据范围下拉框*/
	private JSONArray combo;

	/*已失效但还残留的权限数据*/
	private  List<String> invaildList;
	private  List<SysOrgTreeData> invaildListTree;
}
