package com.yunhesoft.system.dataperm.entity.dto;

import java.util.List;

import lombok.Data;

/**
 * 数据权限查询
 * 
 * <AUTHOR>
 *
 */
@Data
public class DataPermSaveDto {

	// 权限对象编码
	private String datapermCode;

	// 对象类型（0:角色；1：人员；2：其他）
	private Integer objtype;

	// 对象编码
	private String objid;

	// 数据范围 // 1：全部；2:仅本人；30：本部门；40：本部门及以下；-1：自定义
	private Integer datatype;

	// 数据权限值
	private List<String> datacodeList;

}
