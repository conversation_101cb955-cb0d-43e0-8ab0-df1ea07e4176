package com.yunhesoft.system.dataperm.entity.vo;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据权限-备选数据
 * 
 * <AUTHOR>
 *
 */

@Getter
@Setter
public class DataPermTreeVo {

	//代码
	private String orgcode;
	//名称
	private String orgname;
	//排序
	private Integer tmSort;
	//级别
	private Integer orglevel;

	private List<DataPermTreeVo> children = new ArrayList<DataPermTreeVo>();

}
