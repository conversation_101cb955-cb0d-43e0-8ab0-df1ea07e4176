package com.yunhesoft.system.dataperm.entity.dto;

import com.yunhesoft.system.dataperm.entity.po.SysDatapermConfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据权限操作条件
 * 
 * @Description:
 * <AUTHOR>
 * @date 2021年11月17日
 */
@Data
@ApiModel(value="数据权限操作 DTO类",description="数据权限操作 DTO类")
public class DataPermOperateDto {

	@ApiModelProperty(value = "要加载的树形根节点编码")
	private String dataPermCode;
	@ApiModelProperty(value = "数据权限配置信息")
	private SysDatapermConfig cfg;

}
