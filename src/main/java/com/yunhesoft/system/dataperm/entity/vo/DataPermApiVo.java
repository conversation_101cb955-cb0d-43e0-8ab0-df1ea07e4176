package com.yunhesoft.system.dataperm.entity.vo;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据权限返回VO类
 *
 * <AUTHOR>
 * @Description MtmFormulaApiVo
 * @date 2024年12月6日
 */
@Data
@ApiModel(value = "数据权限返回VO类", description = "数据权限返回VO类")
public class DataPermApiVo {
    @ApiModelProperty(value = "数据权限配置信息列表")
	private List<DataPermCfgTreeVo> cfgList = new ArrayList<DataPermCfgTreeVo>();
    @ApiModelProperty(value = "数据权限树返回列表")
	private List<DataPermTreeVo> treeList = new ArrayList<DataPermTreeVo>();
}