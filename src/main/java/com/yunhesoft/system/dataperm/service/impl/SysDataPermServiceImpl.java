package com.yunhesoft.system.dataperm.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.ClassExecute;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.dataperm.entity.dto.DataPermCopyDto;
import com.yunhesoft.system.dataperm.entity.dto.DataPermDto;
import com.yunhesoft.system.dataperm.entity.dto.DataPermOperateDto;
import com.yunhesoft.system.dataperm.entity.dto.DataPermSaveDto;
import com.yunhesoft.system.dataperm.entity.po.SysDataperm;
import com.yunhesoft.system.dataperm.entity.po.SysDatapermConfig;
import com.yunhesoft.system.dataperm.entity.po.SysDatapermDatatype;
import com.yunhesoft.system.dataperm.entity.vo.DataPermApiVo;
import com.yunhesoft.system.dataperm.entity.vo.DataPermCfgTreeVo;
import com.yunhesoft.system.dataperm.entity.vo.DataPermTreeVo;
import com.yunhesoft.system.dataperm.entity.vo.DataPermVo;
import com.yunhesoft.system.dataperm.service.ISysDataPermService;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.menu.entity.po.SysModule;
import com.yunhesoft.system.menu.service.ISysMenuLibService;
import com.yunhesoft.system.org.entity.dto.SysOrgSelect;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.entity.vo.SysOrgTreeData;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.role.entity.po.SysRole;
import com.yunhesoft.system.role.service.ISysRoleLevelService;
import com.yunhesoft.system.role.service.ISysUserRoleService;
import com.yunhesoft.system.role.utils.IPermUtils;

import lombok.extern.log4j.Log4j2;

/**
 * 数据权限实现类
 *
 * <AUTHOR>
 *
 */
@Log4j2
@Service
public class SysDataPermServiceImpl implements ISysDataPermService {

	@Autowired
	private EntityService dao;

	@Autowired
	private ISysMenuLibService menuLibServ;
	// 机构
	@Autowired
	private ISysOrgService orgServ;
	@Autowired
	private ISysEmployeeOrgService userOrgService;
	// 权限
	@Autowired
	private IPermUtils perUtils;
	// 人员
	@Autowired
	private AuthService authServ;
	@Autowired
	private ISysUserRoleService userRoleService;
	@Autowired
	private ISysRoleLevelService roleLevelService;

    @Resource(name = "CustomRestTemplate")
	private RestTemplate restTemplate;

    private String sys_org_gx="sys_org_gx";
	// 数据范围
//	public static final int DATATYPE_ALL = 1; // 所有
//	public static final int DATATYPE_USER = 2; // 仅本人
//	public static final int DATATYPE_DEP = 30;// 本部门
//	public static final int DATATYPE_DEPSUB = 40;// 本部门及以下
//	public static final int DATATYPE_DIY = -1;// 自定义

	/**
	 * 获取当前登录人的数据权限(默认：组织机构数据权限)
	 */
	@Override
	public DataPermVo getDataPerm() {
		return this.getDataPerm(SysUserHolder.getCurrentUser(), sys_org_gx);
	}

	/**
	 * 获取当前登录人的数据权限
	 */
	@Override
	public DataPermVo getDataPerm(String datapermCode) {
		return this.getDataPerm(SysUserHolder.getCurrentUser(), datapermCode);
	}

	/**
	 * 获取某个人的数据权限
	 *
	 * @param userId
	 * @param datapermCode
	 * @return
	 */
	@Override
	public DataPermVo getDataPerm(String userId, String datapermCode) {
		SysUser user = SysUserHolder.getCurrentUser();
		if (!user.getId().equals(userId)) {
			user = authServ.getUser(userId); // 根据id获得user对象
		}
		return this.getDataPerm(user, datapermCode);
	}
	/**
	 * 获取某个人的数据权限
	 *
	 * @param datapermCode 数据权限编码
	 * @return
	 */
	@Override
	public DataPermVo getDataPerm(SysUser user, String datapermCode) {
		return this.getDataPerm(user, datapermCode,"");
	}
	/**
	 * 获取某个人的数据权限
	 *
	 * @param dataPermCode  数据权限编码。具体编码在各模块的自定义权限树形类中查找（SYS_DATAPERM_CONFIG表treeClassName对应的类）
	 * @param cfgCode  数据权限配置信息编码。 当前有assess_themes，具体查找编码在SYS_DATAPERM_CONFIG表 不给时默认为datapermCode
	 * @return
	 */
	@Override
	public DataPermVo getDataPerm(SysUser user, String datapermCode,String cfgCode) {
		DataPermVo vo = new DataPermVo();
		vo.setDatapermCode(datapermCode);
		List<String> list = new ArrayList<String>();
		if (user == null) {
			user = SysUserHolder.getCurrentUser();
		}
		if (user != null) {
			if (perUtils.isAdmin(user.getId()) || perUtils.isTenantAdmin(user)) {// 超级管理员
				vo.setDatatype(DATATYPE_ALL);
				return vo;
			}
			if(StringUtils.isEmpty(cfgCode)) {//如果未指定配置表编码，则使用datapermCode
				cfgCode=datapermCode;
			}
			int dataType=1;//数据角色类型 0组织机构 1自定义
			List<SysDatapermConfig> cfgList = this.getSysDatapermConfig(cfgCode);//获取配置信息
			if (StringUtils.isNotEmpty(cfgList)) {
				SysDatapermConfig cfg = cfgList.get(0);
				if (cfg.getDatatype() == null || cfg.getDatatype() == 0) {// 组织机构的数据权限
					dataType=0;//组织机构
				}
			}
//			vo.setDatapermCode(datapermCode);
			vo.setDatatype(DATATYPE_NONE);
			List<String> roleIdList = new ArrayList<String>();
			String empId = "";
//			if (cfg.getDatatype() == null || cfg.getDatatype() == 0) {// 组织机构的数据权限
				if (StringUtils.isNotEmpty(user.getRoles())) {
					// 角色的数据权限
					Map<String,SysDatapermDatatype>  datapermMap =null;
					List<SysDatapermDatatype> listDatatype = this.getDataTypeList(datapermCode, 0, user.getRoles());
					if (StringUtils.isNotEmpty(listDatatype)) {
							datapermMap = listDatatype.stream().collect(Collectors.toMap(SysDatapermDatatype::getObjid,
									SysDatapermDatatype -> SysDatapermDatatype, (key1, key2) -> key1));// 将list转换为map
//							for (SysDatapermDatatype datatype : listDatatype) {
//								int type = datatype.getDatatype() == null ? 0 : datatype.getDatatype();
//								if (type == DATATYPE_ALL) {// 全部
//									vo.setDatatype(type);
//									return vo;
//								} else if (type == DATATYPE_DIY) {// 自定义
//									roleIdList.add(datatype.getObjid());
//								} else {
//									if (type > vo.getDatatype()) {
//										vo.setDatatype(type);
//									}
//								}
//							}					
					}
					if(datapermMap==null) {
						datapermMap = new HashMap<String,SysDatapermDatatype>();
					}
					for(String role:user.getRoles()){
						SysDatapermDatatype datatype = datapermMap.get(role);
						if(datatype!=null) {
							int type = datatype.getDatatype() == null ? 0 : datatype.getDatatype();
							if (type == DATATYPE_ALL) {// 全部
								vo.setDatatype(type);
								return vo;
							} else if (type == DATATYPE_DIY) {// 自定义
								roleIdList.add(datatype.getObjid());
								if (type > vo.getDatatype()) {
									vo.setDatatype(type);
								}
							} else {
								if (type > vo.getDatatype()) {
									vo.setDatatype(type);
								}
							}
						}else{//未设置角色的数据权限，给默认
							if(sys_org_gx.equals(datapermCode)) {//只有机构管辖，才对角色权限进行默认，非机构关系，不需要有默认级别（无默认级别可以使自定义单独生效，有默认级别则自定义会和默认级别进行权限混合）
								vo.setDatatype(DATATYPE_DEPSUB);//默认为当前及以下
							}
						}
					}
				}
				// 个人权限
				DataPermSaveDto dto = new DataPermSaveDto();
				dto.setDatapermCode(datapermCode);
				dto.setObjid(user.getId());
				dto.setObjtype(1);
				SysDatapermDatatype userDatatype = this.getDataType(dto);
				if (userDatatype != null) {
					int type = userDatatype.getDatatype() == null ? 0 : userDatatype.getDatatype();
					if (type == DATATYPE_ALL) {// 全部
						vo.setDatatype(type);
						return vo;
					} else if (type == DATATYPE_DIY) {// 自定义
						empId = user.getId();// roleIdList.add(datatype.getObjid());
						if (type > vo.getDatatype()) {
							vo.setDatatype(type);
						}
					} else {
						if (type > vo.getDatatype()) {
							vo.setDatatype(type);
						}
					}
				}else {//未设置人员的数据权限，给默认
					if(sys_org_gx.equals(datapermCode)) {//只有机构管辖，才对个人权限进行默认，非机构关系，不需要有默认级别（无默认级别可以使自定义单独生效，有默认级别则自定义会和默认级别进行权限混合）
						vo.setDatatype(DATATYPE_DEPSUB);//默认为当前及以下
					}
				}
				if(vo.getDatatype()==DATATYPE_NONE) {//角色、个人权限都没有设置权限类型 那么给当前及以下 	// 1：全部；2:仅本人；30：本部门；40：本部门及以下；-1：自定义
					vo.setDatatype(DATATYPE_DEPSUB);//默认为当前及以下
				}
				if(dataType==0) {//组织机构的，把机构合并到数据权限中
					// 1：全部；2:仅本人；30：本部门；40：本部门及以下；-1：自定义
					getUserPermissByDataPerConfig(user, vo, list);
				}
			// 1.获取角色的数据权限
			if (StringUtils.isNotEmpty(roleIdList)) {
				List<String> tempList = this.getDataPermByObjid(datapermCode, 0, roleIdList);
				this.addDataCode(list, tempList);
			}
			// 2.获取个人的数据权限
			if (StringUtils.isNotEmpty(empId)) {
				List<String> tempList = this.getDataPermByObjid(datapermCode, 1, empId);
				this.addDataCode(list, tempList);
			}
		}
		vo.setDatacodeList(list);
		return vo;
	}
	
	/**
	 * 根据权限类型获取机构管辖权限
	 * @category 
	 * <AUTHOR> 
	 * @param user
	 * @param vo.dataType 2个人 30本部门 40本部门及以下
	 * @param list
	 */
	private void getUserPermissByDataPerConfig(SysUser user, DataPermVo vo, List<String> list) {
		String orgId = user.getOrgId();
		if (vo.getDatatype() == DATATYPE_DEP) {// 本部门
			//查验当前用户的角色层级
			SysRole currentUserRoleLevel = roleLevelService.getCurrentUserRoleLevel();
			//根据角色层级 进行所属机构的提升
			orgId = getOrgByLevel(currentUserRoleLevel, orgId);
			if (!list.contains(orgId)) {
				list.add(orgId);
			}
		} else if (vo.getDatatype() == DATATYPE_DEPSUB) {// 本部门及以下
			//查验当前用户的角色层级
			SysRole currentUserRoleLevel = roleLevelService.getCurrentUserRoleLevel();
			//根据角色层级 进行所属机构的提升
			orgId = getOrgByLevel(currentUserRoleLevel, orgId);
			List<SysOrg> orgList = orgServ.getOrgList(orgId);
			if (StringUtils.isNotEmpty(orgList)) {
				for (SysOrg sysOrg : orgList) {
					if (!list.contains(sysOrg.getId())) {
						list.add(sysOrg.getId());
					}
				}
			}
		}else if (vo.getDatatype() == DATATYPE_USER) {// 个人
			if (!list.contains(orgId)) {
				list.add(orgId);
			}
		}
	}

	private String getOrgByLevel(SysRole currentUserRoleLevel, String orgId) {
		if(currentUserRoleLevel !=null){
			String orgTypeByLevel = roleLevelService.getOrgTypeByLevel(currentUserRoleLevel.getRoleLevel());
			if(StringUtils.isNotEmpty(orgTypeByLevel)){
				List<String> list1 = Arrays.asList(orgTypeByLevel.split(","));
				List<SysOrg> orgList = orgServ.getFullParentOrgList(orgId,null);
				for (SysOrg sysOrg : orgList) {
					if(list1.contains(sysOrg.getOrgType())){
						return sysOrg.getOrgcode();
					}
				}
			}
		}
		return null;
	}

	private void addDataCode(List<String> rtnList, List<String> dataList) {
		if (rtnList == null) {
			rtnList = new ArrayList<String>();
		}
		if (StringUtils.isNotEmpty(dataList)) {
			for (String code : dataList) {
				if (!rtnList.contains(code)) {
					rtnList.add(code);
				}
			}
		}
	}

	/**
	 * 通过对象类型获取数据id
	 *
	 * @param datapermCode 数据权限编码
	 * @param objType      对象类型（0:角色；1：人员；2：其他）
	 * @param objIds       对象id（人员id or 角色id）
	 * @return
	 */
	@Override
	public List<String> getDataPermByObjid(String datapermCode, Integer objType, List<String> objIds) {
		List<String> list = new ArrayList<String>();
		List<SysDataperm> permList = this.getSysDataperm(datapermCode, objType, objIds);
		if (StringUtils.isNotEmpty(permList)) {
			for (SysDataperm e : permList) {
				String code = e.getDatacode();
				if (StringUtils.isNotEmpty(code) && !list.contains(code)) {
					list.add(code);
				}
			}
		}
		return list;
	}

	/**
	 * 获取数据权限数据
	 *
	 * @param datapermCode
	 * @param objType
	 * @param objIds
	 * @return
	 */
	private List<SysDataperm> getSysDataperm(String datapermCode, Integer objType, List<String> objIds) {
		Where where = Where.create();
		if (datapermCode != null) {
			where.eq(SysDataperm::getDatapermCode, datapermCode);
		}
		where.eq(SysDataperm::getObjtype, objType);
		if (StringUtils.isNotEmpty(objIds)) {
			if (objIds.size() == 1) {
				where.eq(SysDataperm::getObjid, objIds.get(0));
			} else {
				where.in(SysDataperm::getObjid, objIds.toArray());
			}
		}
		return dao.queryList(SysDataperm.class, where);
	}

	/**
	 * 通过对象类型获取数据id
	 *
	 * @param datapermCode 数据权限编码
	 * @param objType      对象类型（0:角色；1：人员；2：其他）
	 * @param objId        对象id（人员id or 角色id）
	 * @return
	 */
	@Override
	public List<String> getDataPermByObjid(String datapermCode, Integer objType, String objId) {
		List<String> list = new ArrayList<String>();
		list.add(objId);
		return this.getDataPermByObjid(datapermCode, objType, list);
	}

	/**
	 * 获取数据权限配置信息
	 *
	 * @return
	 */
	private List<SysDatapermConfig> getSysDatapermConfig(String code) {
		Where where = Where.create();
		if (StringUtils.isNotEmpty(code)) {
			where.eq(SysDatapermConfig::getCode, code);
		}
		Order order = Order.create();
		order.orderByAsc(SysDatapermConfig::getModulecode);
		order.orderByAsc(SysDatapermConfig::getTmsort);
		// 不考虑租户信息
		return dao.queryListDisableTenant(SysDatapermConfig.class, where, order);
	}

	/**
	 * 获取数据权限项目列表数据
	 *
	 * @return
	 */
	@Override
	public List<DataPermCfgTreeVo> getDataPermCfgTree() {
		List<DataPermCfgTreeVo> voList = new ArrayList<DataPermCfgTreeVo>();
		List<SysDatapermConfig> list = getSysDatapermConfig(null);
		if (StringUtils.isNotEmpty(list)) {
			Map<String, SysModule> moduleMap = this.getMobuleMap();
			Map<String, List<SysDatapermConfig>> cfgMap = new HashMap<String, List<SysDatapermConfig>>();
			for (SysDatapermConfig e : list) {
				String moduleCode = e.getModulecode();
				if (cfgMap.containsKey(moduleCode)) {
					cfgMap.get(moduleCode).add(e);
				} else {
					List<SysDatapermConfig> dataList = new ArrayList<SysDatapermConfig>();
					dataList.add(e);
					cfgMap.put(moduleCode, dataList);
					SysModule sysModule = moduleMap.get(moduleCode);
					if (sysModule != null) {
						DataPermCfgTreeVo vo = new DataPermCfgTreeVo();
						vo.setCode(moduleCode);
						vo.setName(sysModule.getModuleName());
						vo.setSort(sysModule.getTmsort());
						vo.setLevel(1);
						vo.setDatatype(0);
						voList.add(vo);
					}
				}
			}
			if (voList.size() > 0) {
				// 按照sort排序
				voList = voList.stream().sorted(Comparator.comparing(DataPermCfgTreeVo::getSort))
						.collect(Collectors.toList());
				for (DataPermCfgTreeVo e : voList) {
					List<SysDatapermConfig> dataList = cfgMap.get(e.getCode());
					if (StringUtils.isNotEmpty(dataList)) {
						List<DataPermCfgTreeVo> child = new ArrayList<DataPermCfgTreeVo>();
						for (SysDatapermConfig cfg : dataList) {
							DataPermCfgTreeVo vo = new DataPermCfgTreeVo();
							vo.setCode(cfg.getCode());
							vo.setName(cfg.getName());
							vo.setSort(cfg.getTmsort());
							if (cfg.getDatatype() == null) {
								vo.setDatatype(0);
							} else {
								vo.setDatatype(cfg.getDatatype());
							}
							if (StringUtils.isNotEmpty(cfg.getTreeExpandDataFun())) {//需要展开节点
								vo.setLevel(1);//这个节点不作为数据权限的选择节点
								List<DataPermCfgTreeVo> childList = execClassExpand(cfg);
								if(StringUtils.isNotEmpty(childList)) {
									vo.setChildren(childList);//添加到子节点
								}
							}else {
								vo.setLevel(2);
							}
							child.add(vo);
						}
						e.setChildren(child);
					}
				}
			}
		}
		return voList;
	}

	/**
	 * 获取数据权限备选数据
	 * @param dataPermCode 数据权限树形节点id
	 * @param cfgCode 数据权限树形节点对应的配置信息id
	 * @return
	 */
	@Override
	public Object getDataPermDataTree(String dataPermCode,String cfgCode) {
		if (StringUtils.isEmpty(cfgCode)) {//如果节点没有配置信息编码，则获得数据权限树形节点id
			cfgCode=dataPermCode;
		}
		List<SysDatapermConfig> cfgList = getSysDatapermConfig(cfgCode);
		if (StringUtils.isNotEmpty(cfgList)) {
			SysDatapermConfig cfg = cfgList.get(0);
			if (cfg.getDatatype() == null || cfg.getDatatype() == 0) {// 返回组织机构树
				// 当前用户的可见范围，不包含目标原本所拥有的
				return this.getAllOrg();
			} else {// 返回自定义数据
				return this.execClass(cfg,dataPermCode);
			}
		}
		return null;
	}

	/**
	 * 获取机构树形数据
	 *
	 * @return
	 */
	private List<SysOrgTreeData> getAllOrg() {
		SysOrgSelect param = new SysOrgSelect();
		param.setPorgcode("");
		param.setIncludeNode("true");
		param.setDatatype("all");
		param.setShowRootNode("false");
		return orgServ.listDatas(param);
	}

	/**
	 * 获取模块map
	 *
	 * @return
	 */
	private Map<String, SysModule> getMobuleMap() {
		return menuLibServ.getMobuleMap();
	}

	/**
	 * java类 计算备选项数据
	 *
	 * @param SysDatapermConfig 配置信息
	 * @param dataPermCode 节点ID
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private List<DataPermTreeVo> execClass(SysDatapermConfig cfg,String dataPermCode) {
		List<DataPermTreeVo> list = null;
		String execContent = "";
		String className = "";
		try {
			if (cfg != null && cfg.getDatatype() == 1) {// 自定义，根据后台类配置返回
				if (StringUtils.isNotEmpty(cfg.getServiceName()) && StringUtils.isNotEmpty(cfg.getTreeGetDataUrl())) {//是微服务模式
					DataPermOperateDto dto = new DataPermOperateDto();
					dto.setCfg(cfg);
					dto.setDataPermCode(dataPermCode);
					DataPermApiVo vo = execRestApi(cfg.getServiceName(),cfg.getTreeGetDataUrl(),dto);
					if(vo!=null) {
						list = vo.getTreeList();
					}
				}else {
					if (cfg.getTreeGetDataFun() != null && cfg.getTreeClassName() != null) {
						execContent = cfg.getTreeGetDataFun();
						className = cfg.getTreeClassName();
						if (StringUtils.isNotEmpty(execContent)) {
							ClassExecute classExec = new ClassExecute();
							Object val = null;
							if(execContent.indexOf("@")>=0) {//这个函数带有参数
								int index = execContent.indexOf("(");
								if(index>=0) {
									execContent=execContent.substring(0,index);////这里把函数名称取一下，带参数名称的没法直接反射
								}
								val = classExec.execClassByName(className, execContent, new Class[] {String.class} , new Object[]{dataPermCode});//只有一个String类型的参数可以传递进反射
							}else {
								val = classExec.execClassByName(className, execContent);
							}
							if (val != null) {
								list = (List<DataPermTreeVo>) val;
							}
						}
					}
				}
			}
		} catch (Exception e) {
			log.error("数据权限执行类出错," + className + "." + execContent, e);
		}
		return list;
	}
	/**
	 * java类 计算备选项数据
	 *
	 * @param SysDatapermConfig 配置信息
	 * @param dataPermCode 节点ID
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private List<DataPermCfgTreeVo> execClassExpand(SysDatapermConfig cfg) {
		List<DataPermCfgTreeVo> result = null;
		String execContent=null;
		String className=null;
		try {
			if (cfg != null && cfg.getDatatype() == 1) {// 自定义，根据后台类配置返回
				if (StringUtils.isNotEmpty(cfg.getServiceName()) && StringUtils.isNotEmpty(cfg.getTreeExpandDataUrl())) {//是微服务模式
					DataPermOperateDto dto = new DataPermOperateDto();
					dto.setCfg(cfg);
					DataPermApiVo vo = execRestApi(cfg.getServiceName(),cfg.getTreeExpandDataUrl(),dto);
					if(vo!=null) {
						result = vo.getCfgList();
					}
				}else {
					if (cfg.getTreeExpandDataFun() != null && cfg.getTreeClassName() != null) {
						execContent = cfg.getTreeExpandDataFun();
						className = cfg.getTreeClassName();
						if (StringUtils.isNotEmpty(execContent)) {
							ClassExecute classExec = new ClassExecute();
							Object val = null;
							if(execContent.indexOf("@")>=0) {//这个函数带有参数
								int index = execContent.indexOf("(");
								if(index>=0) {
									execContent=execContent.substring(0,index);////这里把函数名称取一下，带参数名称的没法直接反射
								}
								val = classExec.execClassByName(className, execContent , new Class[] {SysDatapermConfig.class} , new Object[]{cfg});//只有一个SysDatapermConfig类型的参数可以传递进反射
							}else {
								val = classExec.execClassByName(className, execContent);
							}
							if (val != null) {
								result = (List<DataPermCfgTreeVo>) val;
							}
						}
					}
				}
			}
		} catch (Exception e1) {
			log.error("数据权限执行类出错," + className + "." + execContent, e1);
		}
		return result;
	}
	/**
	 * 删除数据
	 *
	 * @param dto
	 * @return
	 */
	private boolean deleteDataPerm(DataPermSaveDto dto) {
		boolean bln = false;
		Where where = Where.create();
		if (StringUtils.isNotEmpty(dto.getDatapermCode())) {
			where.eq(SysDataperm::getDatapermCode, dto.getDatapermCode());
		}
		if (dto.getObjtype() != null) {
			where.eq(SysDataperm::getObjtype, dto.getObjtype());
		}
		where.eq(SysDataperm::getObjid, dto.getObjid());
		int i = dao.delete(SysDataperm.class, where);

		if (i > 0) {
			bln = true;
		}
		return bln;
	}

	/**
	 * 通过对象id删除数据权限
	 *
	 * @param objids
	 * @return
	 */
	private boolean deleteDataPermByIds(List<String> objids) {
		boolean bln = false;
		int i = 0;
		if (StringUtils.isNotEmpty(objids)) {
			Where where = Where.create();
			if (objids.size() == 1) {
				where.eq(SysDataperm::getObjid, objids.get(0));
			} else {
				where.eq(SysDataperm::getObjid, objids.toArray());
			}
			i = dao.delete(SysDataperm.class, where);
		}
		if (i > 0) {
			bln = true;
		}
		return bln;
	}

	/**
	 * 添加数据范围数据
	 *
	 * @param dto
	 * @return
	 */
	private boolean insertDataPerm(DataPermSaveDto dto) {
		boolean bln = false;
		List<SysDataperm> list = new ArrayList<SysDataperm>();
		if (dto != null && StringUtils.isNotEmpty(dto.getDatacodeList())) {
			for (String code : dto.getDatacodeList()) {
				if (!"root".equalsIgnoreCase(code)) {
					SysDataperm bean = new SysDataperm();
					bean.setDatacode(code);
					bean.setDatapermCode(dto.getDatapermCode());
					bean.setId(TMUID.getUID());
					bean.setObjid(dto.getObjid());
					bean.setObjtype(dto.getObjtype());
					list.add(bean);
				}
			}
			if (list.size() > 0) {
				bln = this.insertDataPerm(list);
			}
		}
		return bln;
	}

	private boolean insertDataPerm(List<SysDataperm> list) {
		boolean bln = false;
		if (dao.insertBatch(list) > 0) {
			bln = true;
		}
		return bln;
	}

	/**
	 * 获取数据权限-数据范围
	 *
	 * @param dto
	 * @return
	 */
	private SysDatapermDatatype getDataType(DataPermSaveDto dto) {
		List<String> objids = new ArrayList<String>();
		objids.add(dto.getObjid());
		List<SysDatapermDatatype> list = this.getDataTypeList(dto.getDatapermCode(), dto.getObjtype(), objids);
		if (StringUtils.isNotEmpty(list)) {
			return list.get(0);
		} else {
			return null;
		}
	}

	/**
	 * 获取数据权限-数据范围
	 *
	 * @param datapermCode
	 * @param objtype
	 * @param objids
	 * @return
	 */
	private List<SysDatapermDatatype> getDataTypeList(String datapermCode, Integer objtype, List<String> objids) {
		Where where = Where.create();
		if (datapermCode != null) {
			where.eq(SysDatapermDatatype::getDatapermCode, datapermCode);
		}
		where.eq(SysDatapermDatatype::getObjtype, objtype);
		if (StringUtils.isNotEmpty(objids)) {
			if (objids.size() == 1) {
				where.eq(SysDatapermDatatype::getObjid, objids.get(0));
			} else {
				where.in(SysDatapermDatatype::getObjid, objids.toArray());
			}
		}
		List<SysDatapermDatatype> list = dao.queryList(SysDatapermDatatype.class, where);
		return list;
	}

	private boolean insertDataType(SysDatapermDatatype bean) {
		int i = dao.insert(bean);
		return i > 0 ? true : false;
	}

	private boolean insertDataType(List<SysDatapermDatatype> list) {
		int i = dao.insertBatch(list);
		return i > 0 ? true : false;
	}

	public boolean deleteDataType(DataPermSaveDto dto) {
		int i = 0;
		SysDatapermDatatype bean = this.getDataType(dto);
		if (bean != null) {
			i = dao.rawDeleteById(bean);
		}
		return i > 0 ? true : false;
	}

	/**
	 * 删除 通过 对象id dataType
	 *
	 * @param objid
	 * @return
	 */
	public boolean deleteDataType(List<String> objids) {
		if (StringUtils.isNotEmpty(objids)) {
			Where where = Where.create();
			if (objids.size() == 1) {
				where.eq(SysDatapermDatatype::getObjid, objids.get(0));
			} else {
				where.eq(SysDatapermDatatype::getObjid, objids.toArray());
			}
			int i = dao.delete(SysDatapermDatatype.class, where);
			return i > 0 ? true : false;
		} else {
			return false;
		}
	}

	private boolean updateDataType(SysDatapermDatatype bean) {
		int i = dao.rawUpdateById(bean);
		return i > 0 ? true : false;
	}

	public void saveDataType(DataPermSaveDto dto) {
		if (dto.getDatatype() == null) {
			this.deleteDataType(dto);
		} else {
			SysDatapermDatatype bean = this.getDataType(dto);
			if (bean == null) {
				bean = new SysDatapermDatatype();
				bean.setId(TMUID.getUID());
				bean.setDatapermCode(dto.getDatapermCode());
				bean.setObjid(dto.getObjid());
				bean.setObjtype(dto.getObjtype());
				bean.setDatatype(dto.getDatatype());
				this.insertDataType(bean);
			} else {
				bean.setDatatype(dto.getDatatype());
				this.updateDataType(bean);
			}
		}
	}

	/**
	 * 数据权限数据保存
	 */
	@Override
	public void saveDataPerm(DataPermSaveDto dto) {
		if (dto != null) {
			this.deleteDataPerm(dto);// 先删除，后插入
			this.saveDataType(dto);// 保存数据范围
			if (dto.getDatatype() != null && dto.getDatatype() == -1) {
				this.insertDataPerm(dto);// 保存明细数据
			}
		}
	}

	/**
	 * 删除数据权限
	 *
	 * @param objid
	 */

	@Override
	public void deleteDataPerm(List<String> objids) {
		this.deleteDataType(objids);
		this.deleteDataPermByIds(objids);
	}

	/**
	 * 删除数据权限
	 *
	 * @param objid
	 */
	@Override
	public void deleteDataPerm(String objid) {
		List<String> objids = new ArrayList<String>();
		objids.add(objid);
		this.deleteDataPerm(objids);
	}

	/**
	 * 通过范围代码获取数据范围下拉框
	 *
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	private JSONObject getComboByDataType(DataPermDto param) {
//		查看当前登录用户是否有自定义权限
//		Boolean bln = false;
//		// 个人
//		DataPermSaveDto dto = new DataPermSaveDto();
//		dto.setDatapermCode(param.getDatapermCode());
//		dto.setObjid(SysUserUtil.getCurrentUser().getId());
//		dto.setObjtype(1);
//		SysDatapermDatatype bean = this.getDataType(dto);
//		if (ObjUtils.notEmpty(bean) && bean.getDatatype() == -1) {
//			bln = true;
//		} else {
//			// 角色
//			List<String> roles = SysUserUtil.getCurrentUser().getRoles();
//			if (StringUtils.isNotEmpty(roles)) {
//				for (String role : roles) {
//					DataPermSaveDto dto1 = new DataPermSaveDto();
//					dto1.setDatapermCode(param.getDatapermCode());
//					dto1.setObjid(role);
//					dto1.setObjtype(0);
//					// 当前对象保存的
//					SysDatapermDatatype bean1 = this.getDataType(dto1);
//					if (ObjUtils.notEmpty(bean1) && bean1.getDatatype() == -1) {
//						bln = true;
//						break;
//					}
//				}
//			}
//		}

		// 组建下拉框内容map
		JSONArray array = new JSONArray();
		JSONObject jsonObject1 = new JSONObject();
		jsonObject1.put("value", "全部权限");
		jsonObject1.put("key", 1);
		array.add(jsonObject1);

		JSONObject jsonObject4 = new JSONObject();
		jsonObject4.put("value", "本部门及以下级别数据");
		jsonObject4.put("key", 40);
		array.add(jsonObject4);

		JSONObject jsonObject3 = new JSONObject();
		jsonObject3.put("value", "本部门数据");
		jsonObject3.put("key", 30);
		array.add(jsonObject3);

		JSONObject jsonObject2 = new JSONObject();
		jsonObject2.put("value", "仅本人数据");
		jsonObject2.put("key", 2);
		array.add(jsonObject2);

		JSONObject jsonObject5 = new JSONObject();
		jsonObject5.put("value", "自定义数据权限");
		jsonObject5.put("key", -1);
		array.add(jsonObject5);

		JSONObject jsonObject = new JSONObject();
		JSONArray jsonArray1 = new JSONArray();
		jsonArray1.add(jsonObject1);
		jsonArray1.add(jsonObject4);
		jsonArray1.add(jsonObject3);
		jsonArray1.add(jsonObject2);
		jsonArray1.add(jsonObject5);
		jsonObject.put("1", jsonArray1);

		JSONArray jsonArray4 = new JSONArray();
		jsonArray4.add(jsonObject4);
		jsonArray4.add(jsonObject3);
		jsonArray4.add(jsonObject2);
//		if (bln) {
			jsonArray4.add(jsonObject5);
//		}
		jsonObject.put("40", jsonArray4);

		JSONArray jsonArray3 = new JSONArray();
		jsonArray3.add(jsonObject3);
		jsonArray3.add(jsonObject2);
//		if (bln) {
			jsonArray3.add(jsonObject5);
//		}
		jsonObject.put("30", jsonArray3);

		JSONArray jsonArray2 = new JSONArray();
		jsonArray2.add(jsonObject2);
//		if (bln) {
			jsonArray2.add(jsonObject5);
//		}
		jsonObject.put("2", jsonArray2);

		JSONArray jsonArray5 = new JSONArray();
		jsonArray5.add(jsonObject5);
		jsonObject.put("-1", jsonArray5);
		return jsonObject;
	}

	/**
	 * 制定权限优先规则 大权限覆盖小权限
	 *
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	private List<Integer> getPrimary() {
		// 优先级规则
		List<Integer> primary = new ArrayList<>();
		primary.add(1);
		primary.add(40);
		primary.add(30);
		primary.add(2);
		primary.add(-1);
		return primary;
	}

	/**
	 * 获取数据权限值
	 */
	@Override
	public DataPermVo getDataPermData(DataPermDto param) {
		JSONObject jsonObject = this.getComboByDataType(param);
		List<Integer> primary = this.getPrimary();
		if (param == null) {
			return null;
		}
		DataPermVo vo = new DataPermVo();
		String datapermCode = param.getDatapermCode();
		String objid = param.getObjid();
		Integer objtype = param.getObjtype();
		vo.setDatapermCode(datapermCode);
		vo.setObjid(objid);
		vo.setObjtype(objtype);
		vo.setDatatype(40);//默认为本部门及以下
		DataPermSaveDto dto = new DataPermSaveDto();
		dto.setDatapermCode(datapermCode);
		dto.setObjid(objid);
		dto.setObjtype(objtype);
		// 当前对象保存的
		SysDatapermDatatype bean = this.getDataType(dto);
//		// 当前用户的角色范围
//		SysDatapermDatatype beanRole = null;
//		// 当前登录用户的个人范围
//		DataPermSaveDto permSaveDto = ObjUtils.copyTo(dto, DataPermSaveDto.class);
//		permSaveDto.setObjid(SysUserUtil.getCurrentUser().getId());
//		permSaveDto.setObjtype(1);
//		SysDatapermDatatype beanPerson = this.getDataType(permSaveDto);
//		if (dto.getObjtype() == 1 && (!SysUserUtil.isAdmin())) {
//			UserRoleDto userRoleDto = new UserRoleDto();
//			userRoleDto.setUserid(permSaveDto.getObjid());
//			List<SysUserRole> roles = userRoleService.getUserRole(userRoleDto);
//			List<String> idList = new ArrayList<>();
//			for (SysUserRole role : roles) {
//				// 按角色查
//				dto.setObjtype(0);
//				dto.setObjid(role.getRoleid());
//				beanRole = (this.getDataType(dto));
//				if (beanRole != null && beanRole.getDatatype() != null) {
//					int roleIndex = primary.indexOf(beanRole.getDatatype());
//					int personIndex = primary.indexOf(
//							beanPerson != null && beanPerson.getDatatype() != null ? beanPerson.getDatatype() : 40);
//					if (roleIndex < personIndex) {
//						if (ObjUtils.notEmpty(beanRole)) {
//							vo.setCombo(jsonObject.getJSONArray(String.valueOf(beanRole.getDatatype())));
//						} else {
//							vo.setCombo(jsonObject.getJSONArray(String.valueOf(40)));
//						}
//					} else {
//						if (ObjUtils.notEmpty(beanPerson)) {
//							vo.setCombo(jsonObject.getJSONArray(String.valueOf(beanPerson.getDatatype())));
//						} else {
//							vo.setCombo(jsonObject.getJSONArray(String.valueOf(40)));
//						}
//					}
//				}
//			}
//		}else{
//			//是管理员
//			vo.setCombo(jsonObject.getJSONArray("1"));
////			vo.setDatatype(1);
//		}
//		if (bean != null && bean.getDatatype() != null) {
//			if (SysUserUtil.isAdmin()) {
//				vo.setCombo(jsonObject.getJSONArray("1"));
//			} else {
//				if (ObjUtils.isEmpty(vo.getCombo())) {
//					vo.setCombo(jsonObject.getJSONArray("40"));
//				}
//			}
//			vo.setDatatype(bean.getDatatype());
//			if (bean.getDatatype() == -1) {// 填充自定义数据
//				List<String> dataList = this.getDataPermByObjid(datapermCode, objtype, objid);
//				if (dataList != null) {
//					vo.setDatacodeList(dataList);
//				}
//			}
//		} else {
//			if(sys_org_gx.equals(param.getDatapermCode())) {
//				if (!SysUserUtil.isAdmin()) {
//					// 非管理员
//					if (ObjUtils.isEmpty(vo.getCombo())) {
//						vo.setCombo(jsonObject.getJSONArray("40"));
//					}
//					vo.setDatatype(40);
//				} else {
//					// 管理员
//					vo.setCombo(jsonObject.getJSONArray("1"));
//					vo.setDatatype(40);
//				}
//			}
//			return vo;
//		}
//		if(sys_org_gx.equals(param.getDatapermCode())) {
//			// 获取失效数据
//			// 将填充的自定义数据 以有效的数据为准进行过滤 分为有效与无效两种数据
//			// 获取有效数据
//			List<SysOrgTreeData> extraTree = new ArrayList<>();
//			if (vo.getDatatype() == -1 && (!SysUserUtil.isAdmin())) {
//				List<String> invailidList = new ArrayList<>();
//				// 目标用户的权限范围
//				List<String> dataList = this.getDataPermByObjid(datapermCode, objtype, objid);
//				for (String s : dataList) {
//					invailidList.add(s);
//				}
//				// 获取当前树形
//				SysOrgSelect orgSelect = new SysOrgSelect();
//				orgSelect.setIncludeNode("true");
//				orgSelect.setDatatype("all");
//				orgSelect.setShowRootNode("false");
//				// 当前登录用户可见范围
//				List<SysOrgTreeData> datatreeData = orgServ.listDatas(orgSelect);
//				vo.setInvaildListTree(datatreeData);
//				// 生成新树
//				if (StringUtils.isNotEmpty(invailidList)) {
//					List<SysOrg> orgListById = orgServ.getOrgListById(invailidList);
//					List<SysOrgTreeData> treeData = new ArrayList<>();
//					if (StringUtils.isNotEmpty(orgListById)) {
//						for (SysOrg sysOrg : orgListById) {
//							SysOrgTreeData sysOrgTreeData = ObjUtils.copyTo(sysOrg, SysOrgTreeData.class);
//							treeData.add(sysOrgTreeData);
//						}
//
//						// 去重
//						datatreeData = datatreeData.stream().filter(i -> !invailidList.contains(i.getId()))
//								.collect(Collectors.toList());
//						datatreeData.addAll(treeData);
//						vo.setInvaildListTree(datatreeData);
//					}
//					// 拥有的权限，包含原本可见范围不包括
//					if (vo.getDatacodeList() != null) {
//						vo.setDatacodeList(vo.getDatacodeList().stream().filter(i -> !invailidList.contains(i))
//								.collect(Collectors.toList()));
//						vo.getDatacodeList().addAll(invailidList);
//					}
//				}
//			} else {
//				// 获取当前树形
//				SysOrgSelect orgSelect = new SysOrgSelect();
//				orgSelect.setIncludeNode("true");
//				orgSelect.setDatatype("all");
//				orgSelect.setShowRootNode("false");
//				// 当前管理员用户可见范围
//				//List<SysOrgTreeData> datatreeData = orgServ.listDatas(orgSelect);
//				//vo.setInvaildListTree(datatreeData);
//			}
//		}
		if (bean != null && bean.getDatatype() != null) {
			vo.setDatatype(bean.getDatatype());
			if(bean.getDatatype() == -1){// 填充自定义数据
				List<String> dataList = this.getDataPermByObjid(datapermCode, objtype, objid);
				if (dataList != null) {
					vo.setDatacodeList(dataList);
				}
			}
		}		
		DataPermVo dp = getDataPerm();//获取当前用户机构管辖范围（这里必须用管辖机构，不能用数据权限范围，正常机构管辖大权限的人，数据权限范围可能很小，如果用数据权限，则可选范围会很小）
		if(dp!=null) {
			vo.setCombo(jsonObject.getJSONArray(String.valueOf(dp.getDatatype())));//控制当前用户设置权限时的可选范围
		}
		return vo;
	}

	/**
	 * 获取配置信息map
	 *
	 * @param cfgList
	 * @return
	 */
	private Map<String, SysDatapermConfig> getCfgMap(List<SysDatapermConfig> cfgList) {
		Map<String, SysDatapermConfig> map = new HashMap<String, SysDatapermConfig>();
		if (StringUtils.isNotEmpty(cfgList)) {
			for (SysDatapermConfig e : cfgList) {
				map.put(e.getCode(), e);
			}
		}
		return map;
	}

	/**
	 * 数据权限配置表添加
	 *
	 * @param insertList
	 * @return
	 */
	private boolean insertDatapermConfig(List<SysDatapermConfig> insertList) {
		if (StringUtils.isNotEmpty(insertList)) {
			int i = dao.insertBatch(insertList);
			return i > 0 ? true : false;
		}
		return false;
	}

	/**
	 * 数据权限初始化
	 *
	 * @param list
	 */
	@Override
	public void initDataPerm(List<SysDatapermConfig> list) {
		if (StringUtils.isNotEmpty(list)) {
			List<SysDatapermConfig> cfgList = getSysDatapermConfig(null);
			Map<String, SysDatapermConfig> cfgMap = getCfgMap(cfgList);
			List<SysDatapermConfig> insertList = new ArrayList<SysDatapermConfig>();
			int sort = cfgMap.size();
			for (SysDatapermConfig e : list) {
				if (!cfgMap.containsKey(e.getCode())) {
					sort++;
					e.setTmsort(sort);
					insertList.add(e);
				}
			}
			this.insertDatapermConfig(insertList);// 插入数据库
		}
	}

	/**
	 * 数据权限拷贝
	 *
	 * @param dto
	 */
	@Override
	public void copyDataPerm(DataPermCopyDto dto) {
		List<String> ids = new ArrayList<String>();
		ids.add(dto.getOldObjid());

		List<SysDatapermDatatype> datatypeList = this.getDataTypeList(null, dto.getObjtype(), ids);
		if (StringUtils.isNotEmpty(datatypeList)) {
			List<SysDatapermDatatype> insertList = new ArrayList<SysDatapermDatatype>();
			for (SysDatapermDatatype e : datatypeList) {
				e.setId(TMUID.getUID());
				e.setObjid(dto.getNewObjid());
				insertList.add(e);
			}
			this.insertDataType(insertList);
		}

		List<SysDataperm> permList = this.getSysDataperm(null, dto.getObjtype(), ids);
		if (StringUtils.isNotEmpty(permList)) {
			List<SysDataperm> insertList = new ArrayList<SysDataperm>();
			for (SysDataperm e : permList) {
				e.setId(TMUID.getUID());
				e.setObjid(dto.getNewObjid());
				insertList.add(e);
			}
			this.insertDataPerm(insertList);
		}
	}
	/**
	 * 执行微服务调用
	 * @category
	 * <AUTHOR>
	 * @param serviceName 服务名
	 * @param requestPath 链接地址
	 * @param param 参数
	 * @return
	 */
	private DataPermApiVo execRestApi(String serviceName,String requestPath,DataPermOperateDto dto) {
		DataPermApiVo result = null;
		if(StringUtils.isNotEmpty(serviceName) && StringUtils.isNotEmpty(requestPath)) {
			try {
				String url = "http://" + serviceName + "/" + requestPath;
				HttpHeaders headers = new HttpHeaders();
				headers.setContentType(MediaType.APPLICATION_JSON);
				String currentToken = SysUserHolder.getCurrentToken();
				headers.set("Authorization", currentToken);
				HttpEntity<Object> entity = new HttpEntity<>(dto, headers);
				ParameterizedTypeReference<Res<DataPermApiVo>> responseType = new ParameterizedTypeReference<Res<DataPermApiVo>>(){};
				Res<DataPermApiVo> sysRes = restTemplate.exchange(url, HttpMethod.POST, entity, responseType).getBody();
				if (sysRes != null && sysRes.getResult()!=null) {
					result = sysRes.getResult();
				}
			}catch(Exception e){
				log.error(e);
			}
		}
		return result;
	}
}
