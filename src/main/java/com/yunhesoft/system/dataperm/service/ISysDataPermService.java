package com.yunhesoft.system.dataperm.service;

import java.util.List;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.system.dataperm.entity.dto.DataPermCopyDto;
import com.yunhesoft.system.dataperm.entity.dto.DataPermDto;
import com.yunhesoft.system.dataperm.entity.dto.DataPermSaveDto;
import com.yunhesoft.system.dataperm.entity.po.SysDatapermConfig;
import com.yunhesoft.system.dataperm.entity.vo.DataPermCfgTreeVo;
import com.yunhesoft.system.dataperm.entity.vo.DataPermVo;

/**
 * 数据权限服务类
 * 
 * <AUTHOR>
 */
public interface ISysDataPermService {

	// 数据范围
	public static final int DATATYPE_ALL = 1; // 所有
	public static final int DATATYPE_USER = 2; // 仅本人
	public static final int DATATYPE_DEP = 30;// 本部门
	public static final int DATATYPE_DEPSUB = 40;// 本部门及以下
	public static final int DATATYPE_DIY = -1;// 自定义
	public static final int DATATYPE_NONE = -100;// 未设置


	/**
	 * 获取当前登录人的数据权限(默认：组织机构数据权限)
	 * 
	 * @param datapermCode 数据权限编码
	 * @return
	 */
	DataPermVo getDataPerm();
	
	/**
	 * 获取当前登录人的数据权限
	 * 
	 * @param datapermCode 数据权限编码
	 * @return
	 */
	DataPermVo getDataPerm(String datapermCode);

	/**
	 * 获取数据权限项目列表数据
	 * 
	 * @return
	 */
	List<DataPermCfgTreeVo> getDataPermCfgTree();

	/**
	 * 获取数据权限备选数据
	 * 
	 * @return
	 */
	Object getDataPermDataTree(String dataPermCode,String cfgCode);

	/**
	 * 获取某个人的数据权限
	 * 
	 * @param userId       人员ID
	 * @param datapermCode 数据权限编码
	 * @return
	 */
	DataPermVo getDataPerm(String userId, String datapermCode);

	/**
	 * 获取某个人的数据权限
	 * 
	 * @param user         用户对象
	 * @param datapermCode 数据权限编码
	 * @return
	 */
	DataPermVo getDataPerm(SysUser user, String datapermCode);
	
	/**
	 * 获取某个人的数据权限
	 * 
	 * @param user         用户对象
	 * @param dataPermCode  数据权限编码。具体编码在各模块的自定义权限树形类中查找（SYS_DATAPERM_CONFIG表treeClassName对应的类）
	 * @param cfgCode  数据权限配置信息编码 当前有assess_themes，具体查找编码在SYS_DATAPERM_CONFIG表 不给时默认为datapermCode
	 * @return
	 */
	DataPermVo getDataPerm(SysUser user, String datapermCode,String cfgCode);

	/**
	 * 通过对象类型获取数据id
	 * 
	 * @param datapermCode 数据权限编码
	 * @param objType      对象类型（0:角色；1：人员；2：其他）
	 * @param objId        对象id（人员id or 角色id）
	 * @return
	 */
	List<String> getDataPermByObjid(String datapermCode, Integer objType, String objId);

	/**
	 * 通过对象类型获取数据id
	 * 
	 * @param datapermCode 数据权限编码
	 * @param objType      对象类型（0:角色；1：人员；2：其他）
	 * @param objId        对象id（人员id or 角色id）
	 * @return
	 */
	List<String> getDataPermByObjid(String datapermCode, Integer objType, List<String> objIds);

	/**
	 * 数据权限数据保存
	 * 
	 * @param dto
	 */

	void saveDataPerm(DataPermSaveDto dto);

	/**
	 * 获取数据权限值
	 * 
	 * @param datapermCode 数据权限编码
	 * @param objType      对象类型（0:角色；1：人员；2：其他）
	 * @return
	 */
	DataPermVo getDataPermData(DataPermDto param);

	/**
	 * 删除数据权限
	 * 
	 * @param objid 对象id
	 */
	void deleteDataPerm(String objid);

	/**
	 * 删除数据权限
	 * 
	 * @param objids 对象id列表
	 */
	void deleteDataPerm(List<String> objids);

	/**
	 * 数据权限初始化
	 * 
	 * @param list
	 */
	void initDataPerm(List<SysDatapermConfig> list);

	/**
	 * 数据权限拷贝
	 * 
	 * @param dto
	 */
	void copyDataPerm(DataPermCopyDto dto);

}
