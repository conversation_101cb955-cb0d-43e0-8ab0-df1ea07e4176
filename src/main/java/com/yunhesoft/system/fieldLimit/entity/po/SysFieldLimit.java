package com.yunhesoft.system.fieldLimit.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * 
 * @category 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_FIELD_LIMIT")
public class SysFieldLimit extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型  1：数据源、2：自定义页面")
    @Column(name = "TMTYPE")
    private Integer tmType;
    
    @ApiModelProperty(value = "标识")
    @Column(name = "CODE", length = 200)
    private String code;
    
    @ApiModelProperty(value = "参数名称")
    @Column(name = "PARAMNAME", length = 200)
    private String paramName;
    
    @ApiModelProperty(value = "参数编码")
    @Column(name = "PARAMCODE", length = 200)
    private String paramCode;

    @ApiModelProperty(value = "设置子数据list")
    @Transient
    private List<SysFieldLimitSet> setList = new ArrayList<>();
    
}
