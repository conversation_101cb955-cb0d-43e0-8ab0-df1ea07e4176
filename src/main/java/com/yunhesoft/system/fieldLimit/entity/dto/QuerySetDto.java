package com.yunhesoft.system.fieldLimit.entity.dto;

import com.yunhesoft.core.common.dto.BaseQueryDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "查询", description = "查询")
public class QuerySetDto extends BaseQueryDto {
	
	/** type 1：数据源、2：自定义页面 */
    @ApiModelProperty(value = "type")
    private String type;
    
    /** params json数据对象 */
    @ApiModelProperty(value = "params")
    private String params;
	
	/** code */
    @ApiModelProperty(value = "code")
    private String code;
	
    /** pid */
    @ApiModelProperty(value = "pid")
    private String pid;

}
