package com.yunhesoft.system.fieldLimit.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SysFieldLimitDataVo {

    @ApiModelProperty("全部允许编辑")
    private boolean allCanEdit = false;

    @ApiModelProperty("隐藏列")
    private List<String> hiddenCols;

    @ApiModelProperty("只读列")
    private List<String> readOnlyCols;

    @ApiModelProperty("行数据字段编辑属性")
    private Map<String, Map<String, Boolean>> rowCannotEditCols;


}
