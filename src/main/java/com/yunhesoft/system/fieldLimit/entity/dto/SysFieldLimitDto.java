package com.yunhesoft.system.fieldLimit.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
public class SysFieldLimitDto {

    @ApiModelProperty(value = "类型  1：数据源、2：自定义页面")
    private int type = 2;

    @ApiModelProperty(value = "标识(数据源别名或表名)")
    private String code;

    @ApiModelProperty(value = "是否查询数据")
    private boolean queryRowData = true;

    @ApiModelProperty(value = "数据id")
    private List<String> dataIdList;
}
