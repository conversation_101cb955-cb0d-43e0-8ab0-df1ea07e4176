package com.yunhesoft.system.fieldLimit.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * 
 * @category 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_FIELD_LIMIT_SET")
public class SysFieldLimitSet extends BaseEntity {

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "父id")
    @Column(name = "PID", length = 200)
    private String pid;
    
    @ApiModelProperty(value = "对象类型  1、人员、2、岗位、3、角色、4、机构")
    @Column(name = "TMTYPE")
    private Integer tmType;
    
    @ApiModelProperty(value = "对象编码")
    @Column(name = "CODE", length = 4000)
    private String code;
    
    @ApiModelProperty(value = "对象名称")
    @Column(name = "NAME", length = 2000)
    private String name;
    
    @ApiModelProperty(value = "只读")
    @Column(name = "ISREADONLY", length = 200)
    private Integer isReadOnly;
    
    @ApiModelProperty(value = "显隐")
    @Column(name = "ISSHOW", length = 200)
    private Integer isShow;
    
    @ApiModelProperty(value = "编辑次数")
    @Column(name = "EDITTIMES", length = 200)
    private Integer editTimes;
}