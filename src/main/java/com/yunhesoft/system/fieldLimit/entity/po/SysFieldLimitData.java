package com.yunhesoft.system.fieldLimit.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * 
 * @category 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_FIELD_LIMIT_DATA")
public class SysFieldLimitData extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型  1、人员、2、岗位、3、角色、4、机构")
    @Column(name = "TMTYPE")
    private Integer tmType;
    
    @ApiModelProperty(value = "标识")
    @Column(name = "CODE", length = 200)
    private String code;
    
    @ApiModelProperty(value = "数据id")
    @Column(name = "PID", length = 200)
    private String pid;
    
    @ApiModelProperty(value = "参数名称")
    @Column(name = "PARAMNAME", length = 200)
    private String paramName;
    
    @ApiModelProperty(value = "参数编码")
    @Column(name = "PARAMCODE", length = 200)
    private String paramCode;
    
    @ApiModelProperty(value = "人员id")
    @Column(name = "ZYID", length = 200)
    private String zyid;
    
    @ApiModelProperty(value = "人员名称")
    @Column(name = "ZYXM", length = 200)
    private String zyxm;
    
    @ApiModelProperty(value = "岗位id")
    @Column(name = "GWID", length = 200)
    private String gwid;
    
    @ApiModelProperty(value = "岗位名称")
    @Column(name = "GWMC", length = 200)
    private String gwmc;
    
    @ApiModelProperty(value = "机构编码")
    @Column(name = "ORGBM", length = 200)
    private String orgbm;
    
    @ApiModelProperty(value = "机构名称")
    @Column(name = "ORGMC", length = 200)
    private String orgmc;
    
    @ApiModelProperty(value = "角色id")
    @Column(name = "JSID", length = 200)
    private String jsid;
    
    @ApiModelProperty(value = "角色名称")
    @Column(name = "JSMC", length = 200)
    private String jsmc;
    
}
