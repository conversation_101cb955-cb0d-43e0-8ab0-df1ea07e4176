package com.yunhesoft.system.fieldLimit.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.fieldLimit.entity.dto.QuerySetDto;
import com.yunhesoft.system.fieldLimit.entity.dto.SaveSetDto;
import com.yunhesoft.system.fieldLimit.entity.po.SysFieldLimit;
import com.yunhesoft.system.fieldLimit.entity.po.SysFieldLimitSet;
import com.yunhesoft.system.fieldLimit.service.FieldLimitSetService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Api(tags = "字段权限设置接口")
@RestController
@RequestMapping("/system/fieldLimitSet")
public class FieldLimitSetController {
	 
	@Autowired
    private FieldLimitSetService baseServ;
	
	/**
     * 查询所有替换字符列表
     * 
     * @param param 查询条件
     * 
     */
    @RequestMapping(value = "/queryList", method = RequestMethod.POST)
    @ApiOperation("查询列表")
    public Res<?> queryList(@RequestBody QuerySetDto param) {
        Res<List<SysFieldLimit>> res = new Res<List<SysFieldLimit>>();
        if (param != null) {
            List<SysFieldLimit> list = baseServ.queryList(param);// 检索数据
            res.setResult(list);
        }
        return res;
    }
	

    /**
     * 查询所有替换字符列表
     * 
     * @param param 查询条件
     * 
     */
    @RequestMapping(value = "/querySetList", method = RequestMethod.POST)
    @ApiOperation("查询")
    public Res<?> querySetList(@RequestBody QuerySetDto param) {
        Res<List<SysFieldLimitSet>> res = new Res<List<SysFieldLimitSet>>();
        if (param != null) {
            List<SysFieldLimitSet> list = baseServ.querySetList(param, null);// 检索数据
            res.setResult(list);
        }
        return res;
    }

    /**
     * 保存替换字符
     * 
     * @param param 保存数据（json）
     * 
     */
    @RequestMapping(value = "/saveSetData", method = RequestMethod.POST)
    @ApiOperation("保存")
    public Res<?> saveSetData(@RequestBody SaveSetDto param) {
        return Res.OK(baseServ.saveSetData(param));
    }
}
