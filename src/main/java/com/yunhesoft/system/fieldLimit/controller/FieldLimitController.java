package com.yunhesoft.system.fieldLimit.controller;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.fieldLimit.entity.dto.SysFieldLimitDto;
import com.yunhesoft.system.fieldLimit.service.FieldLimitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Log4j2
@Api(tags = "字段限制接口")
@RestController
@RequestMapping("/system/fieldLimit")
public class FieldLimitController {

    @Autowired
    private FieldLimitService srv;

    @ResponseBody
    @RequestMapping(value = "/queryFieldLimitData", method = { RequestMethod.POST })
    @ApiOperation(value = "获取字段限制数据")
    public Res queryFieldLimitData(@RequestBody SysFieldLimitDto dto) {
        return Res.OK(srv.queryLimitDataByCode(dto.getType(), dto.getCode(), dto.isQueryRowData(), dto.getDataIdList()));
    }

    @ResponseBody
    @RequestMapping(value = "/saveLimitData", method = { RequestMethod.POST })
    @ApiOperation(value = "获取字段限制数据")
    public Res saveLimitData(@RequestBody JSONObject dto) {
        return Res.OK(srv.saveLimitData(dto.getInteger("type"), dto.getString("code"), dto.getJSONArray("data")));
    }

}
