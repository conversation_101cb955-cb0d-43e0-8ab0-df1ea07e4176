package com.yunhesoft.system.fieldLimit.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.system.fieldLimit.entity.vo.SysFieldLimitDataVo;

import java.util.List;

public interface FieldLimitService {

    SysFieldLimitDataVo queryLimitDataByCode (int type, String code, boolean queryData);

    SysFieldLimitDataVo queryLimitDataByCode (int type, String code);

    SysFieldLimitDataVo queryLimitDataByCode (int type, String code, List<String> dataIdList);
    SysFieldLimitDataVo queryLimitDataByCode (int type, String code, boolean queryRowData, List<String> dataIdList);

    String saveLimitData (int type, String code, JSONArray data);
}
