package com.yunhesoft.system.fieldLimit.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.fieldLimit.entity.dto.QuerySetDto;
import com.yunhesoft.system.fieldLimit.entity.dto.SaveSetDto;
import com.yunhesoft.system.fieldLimit.entity.po.SysFieldLimit;
import com.yunhesoft.system.fieldLimit.entity.po.SysFieldLimitSet;
import com.yunhesoft.system.fieldLimit.service.FieldLimitSetService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class FieldLimitSetServiceImpl implements FieldLimitSetService {
	 
	@Autowired
    private EntityService entityService;
	
	/**
	 * 边侧查询
	 */
	@Override
    public List<SysFieldLimit> queryList(QuerySetDto queryParam) {
        // where 条件
        Where where = Where.create();
        // where.eq(SysFieldLimitSet::getUsed, 1);
        boolean isZdy = false;
        if (queryParam != null) {
            if (StringUtils.isNotEmpty(queryParam.getType())) {
                where.eq(SysFieldLimit::getTmType, queryParam.getType());
                if("2".equals(queryParam.getType())) {
                	isZdy = true;
                }
            }
            if (StringUtils.isNotEmpty(queryParam.getCode())) {
                where.eq(SysFieldLimit::getCode, queryParam.getCode());
            }
        }
        List<SysFieldLimit> list = entityService.queryData(SysFieldLimit.class, where, null, null);
        
        if(isZdy) {
        	//自定义页面同步
        	this.zdyFun(list, queryParam);
        }else {
        	//数据源同步
        	this.tdsFun(list, queryParam);
        }
        
        return entityService.queryData(SysFieldLimit.class, where, null, null);
    }
    
    /**
     * 数据源页面同步
     * @param list
     * @param queryParam
     */
    private void tdsFun(List<SysFieldLimit> list,QuerySetDto queryParam) {
    	
    }
    
    /**
     * 自定义页面同步
     * @param list
     * @param queryParam
     */
    private void zdyFun(List<SysFieldLimit> list,QuerySetDto queryParam) {
    	if (queryParam != null && StringUtils.isNotEmpty(queryParam.getParams())) {
	    	JSONArray datas = JSONArray.parseArray(queryParam.getParams());
	    	List<SysFieldLimit> insertList = new ArrayList<SysFieldLimit>();  //添加的数据
	    	List<SysFieldLimit> upateList = new ArrayList<SysFieldLimit>();   //修改的数据
	    	List<SysFieldLimit> deleteList = new ArrayList<SysFieldLimit>();  //删除的数据
	    	if(list!=null&&list.size()>0) {  
	    		for (int i = 0; i < datas.size(); i++) {
	                JSONObject row = datas.getJSONObject(i);
	                SysFieldLimit bean = new SysFieldLimit();
	                boolean isHave = false;
	                for (int l = 0; l < list.size(); l++) {
	                	if(row.getString("alias").equals(list.get(l).getParamCode())) {
	                		bean = list.get(l);
	                		bean.setParamName(row.getString("header"));
	                		upateList.add(bean);
	                		isHave = true;
	                		break;
	                	}
	                }
	                
	                if(!isHave) {
	                	 bean.setId(TMUID.getUID());
	                     bean.setTmType(2);
	                     bean.setCode(queryParam.getCode());
	                     bean.setParamCode(row.getString("alias"));
	                     bean.setParamName(row.getString("header"));
	                     insertList.add(bean);
	                }
	            }
	    		
	    		for (int l = 0; l < list.size(); l++) {
	    			boolean isHave = false;
	    			for (int i = 0; i < datas.size(); i++) {
	                    JSONObject row = datas.getJSONObject(i);
	                    if(row.getString("alias").equals(list.get(l).getParamCode())) {
	                    	isHave = true;
	                    	break;
	                    }
	    			}
	    			if(!isHave) {
	    				deleteList.add(list.get(l));
	    			}
	            }
	        }else {
	        	if (datas != null) {
	                for (int i = 0; i < datas.size(); i++) {
	                    JSONObject row = datas.getJSONObject(i);
	                    SysFieldLimit bean = new SysFieldLimit();
	                    bean.setId(TMUID.getUID());
	                    bean.setTmType(2);
	                    bean.setCode(queryParam.getCode());
	                    bean.setParamCode(row.getString("alias"));
	                    bean.setParamName(row.getString("header"));
	                    insertList.add(bean);
	                }
	        	}
	        }
	    	if (StringUtils.isNotEmpty(insertList)) {
	            this.insertData(insertList);// 添加
	        }
	        if (StringUtils.isNotEmpty(upateList)) {
	            this.updateData(upateList); // 更新
	        }
	        if (StringUtils.isNotEmpty(deleteList)) {
	            this.deleteData(deleteList);// 删除
	        }
    	}
    }
	
    /**
     * 替换字符列表查询
     * 
     * @param queryParam 检索条件
     * @param page       分页信息
     * @return
     */
    @Override
    public List<SysFieldLimitSet> querySetList(QuerySetDto queryParam, Pagination<?> page) {
        // where 条件
        Where where = Where.create();
        // where.eq(SysFieldLimitSet::getUsed, 1);
        if (queryParam != null) {
            if (StringUtils.isNotEmpty(queryParam.getPid())) {
                where.eq(SysFieldLimitSet::getPid, queryParam.getPid());
            }
        }
        // 排序
        Order order = Order.create();
        order.orderByDesc(SysFieldLimitSet::getUpdateTime);
        return entityService.queryData(SysFieldLimitSet.class, where, order, page);
    }

    /**
     * 保存替换数据
     */
    @Override
    public boolean saveSetData(SaveSetDto param) {
        boolean bln = false;
        if (param != null && StringUtils.isNotEmpty(param.getData())) {
            List<SysFieldLimitSet> insertList = new ArrayList<SysFieldLimitSet>();
            List<SysFieldLimitSet> upateList = new ArrayList<SysFieldLimitSet>();
            List<SysFieldLimitSet> deleteList = new ArrayList<SysFieldLimitSet>();
            JSONArray datas = JSONArray.parseArray(param.getData());
            if (datas != null) {
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject row = datas.getJSONObject(i);
                    Integer rowFlag = row.getInteger("TDSROW_rowFlag"); // 标识字段
                    SysFieldLimitSet bean = new SysFieldLimitSet();
                    String id = row.getString("id");// 主键
                    bean.setId(id);
                    bean.setTmType(row.getInteger("tmType"));
                    bean.setPid(row.getString("pid"));
                    bean.setCode(row.getString("code"));
                    bean.setName(row.getString("name"));
                    bean.setIsReadOnly(row.getInteger("isReadOnly"));
                    bean.setIsShow(row.getInteger("isShow"));
                    bean.setEditTimes(row.getInteger("editTimes"));
                    if (rowFlag == null || rowFlag == 0) {// 添加
                        bean.setId(TMUID.getUID());
                        insertList.add(bean);
                    } else if (rowFlag == 1) {// 修改
                        upateList.add(bean);
                    } else {// 删除
                        if (StringUtils.isNotEmpty(id)) {
                            deleteList.add(bean);
                        }
                    }
                }
            }
            if (StringUtils.isNotEmpty(insertList)) {
                bln = this.insertSetData(insertList);// 添加
            }
            if (StringUtils.isNotEmpty(upateList)) {
                bln = this.updateSetData(upateList);// 更新
            }
            if (StringUtils.isNotEmpty(deleteList)) {
                bln = this.deleteSetData(deleteList);// 删除
            }
        }
        return bln;

    }

    /**
     * 批量添加数据
     * 
     * @param list
     * @return
     */
    private boolean insertSetData(List<SysFieldLimitSet> list) {
        int row = entityService.insertBatch(list);
        return row > 0 ? true : false;
    }

    /**
     * 批量更新数据
     * 
     * @param list
     * @return
     */
    private boolean updateSetData(List<SysFieldLimitSet> list) {
        int row = entityService.updateByIdBatch(list);
        return row > 0 ? true : false;
    }

    /**
     * 批量删除数据
     * 
     * @param list
     * @return
     */
    private boolean deleteSetData(List<SysFieldLimitSet> list) {
        int row = entityService.deleteByIdBatch(list);
        return row > 0 ? true : false;
    }
    
    /**
     * 批量添加数据
     * 
     * @param list
     * @return
     */
    private boolean insertData(List<SysFieldLimit> list) {
        int row = entityService.insertBatch(list);
        return row > 0 ? true : false;
    }

    /**
     * 批量更新数据
     * 
     * @param list
     * @return
     */
    private boolean updateData(List<SysFieldLimit> list) {
        int row = entityService.updateByIdBatch(list);
        return row > 0 ? true : false;
    }

    /**
     * 批量删除数据
     * 
     * @param list
     * @return
     */
    private boolean deleteData(List<SysFieldLimit> list) {
        int row = entityService.deleteByIdBatch(list);
        return row > 0 ? true : false;
    }
}
