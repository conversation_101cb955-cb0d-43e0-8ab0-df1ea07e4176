package com.yunhesoft.system.fieldLimit.service;

import java.util.List;

import com.yunhesoft.system.fieldLimit.entity.dto.QuerySetDto;
import com.yunhesoft.system.fieldLimit.entity.dto.SaveSetDto;
import com.yunhesoft.system.fieldLimit.entity.po.SysFieldLimit;
import com.yunhesoft.system.fieldLimit.entity.po.SysFieldLimitSet;
import com.yunhesoft.system.kernel.service.model.Pagination;

public interface FieldLimitSetService {

	List<SysFieldLimitSet> querySetList(QuerySetDto queryParam, Pagination<?> page);

	boolean saveSetData(SaveSetDto param);

	List<SysFieldLimit> queryList(QuerySetDto queryParam);

}
