package com.yunhesoft.system.fieldLimit.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.fieldLimit.entity.po.SysFieldLimit;
import com.yunhesoft.system.fieldLimit.entity.po.SysFieldLimitData;
import com.yunhesoft.system.fieldLimit.entity.po.SysFieldLimitSet;
import com.yunhesoft.system.fieldLimit.entity.vo.SysFieldLimitDataVo;
import com.yunhesoft.system.fieldLimit.service.FieldLimitService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class FieldLimitServiceImpl implements FieldLimitService {

    @Autowired
    private EntityService dao;

    /**
     * 获取限制设置最终结果
     * @param type 1数据源 2自定义页面
     * @param alias 标识（数据源别名或表名）
     * @return
     */
    private Map<String, SysFieldLimitSet> getLimitSetResult(int type, String alias) {
        List<SysFieldLimit> limitList = this.getLimitSetList(type, alias);
        if (StringUtils.isEmpty(limitList)) {
            return null;
        }
        SysUser currentUser = SysUserHolder.getCurrentUser();
        String userId = Optional.of(currentUser).map(SysUser::getId).orElse("");
        String postId = Optional.of(currentUser).map(SysUser::getPostId).orElse("");
        List<String> roles = Optional.of(currentUser).map(SysUser::getRoles).orElse(null);
        String orgId = Optional.of(currentUser).map(SysUser::getOrgId).orElse("");
        Map<String, SysFieldLimitSet> limitResult = new HashMap<>();

        for (SysFieldLimit limit : limitList) {
            //字段别名
            String paraCode = limit.getParamCode();
            List<SysFieldLimitSet> setList = limit.getSetList();
            if (StringUtils.isEmpty(setList)) {
                limitResult.putIfAbsent(paraCode, null);
                continue;
            }
            //按优先级排序
            setList.sort(Comparator.comparing(SysFieldLimitSet::getTmType));

            //遍历设置，查找匹配项
            for (SysFieldLimitSet set : setList) {
                Integer tmType = set.getTmType();
                int isReadOnly = Optional.ofNullable(set.getIsReadOnly()).orElse(0);
                int isShow = Optional.ofNullable(set.getIsShow()).orElse(0);
                Integer editTimes = set.getEditTimes();
                String objCode = set.getCode();
                if (tmType == null || StringUtils.isEmpty(objCode)) {
                	limitResult.putIfAbsent(paraCode, null);
                    continue;
                }
//                if (isReadOnly != 0 && isShow != 0 && (editTimes == null || editTimes <= 0)) {
//                    //非只读且显示时，必须指定次数，否则跳过
//                	limitResult.putIfAbsent(paraCode, null);
//                    continue;
//                }
                //有可能有多个逗号间隔，拆分成数组
                List<String> codes = new ArrayList<>(Arrays.asList(objCode.split(",")));
                if (tmType == 1 && userId != null && codes.contains(userId)) {  //1、人员、2、岗位、3、角色、4、机构
                    //人员
                } else if (tmType == 2 && postId != null && codes.contains(postId)) {
                    //岗位
                } else if (tmType == 3 && roles != null && !Collections.disjoint(codes, roles)) {
                    //角色
                } else if (tmType == 4 && orgId != null && codes.contains(orgId)) {
                    //机构
                } else {
                    //都不匹配跳过
                	limitResult.putIfAbsent(paraCode, null);
                    continue;
                }
//                columnWhereMap.putIfAbsent(paraCode, tmType);
                limitResult.putIfAbsent(paraCode, set);
            }
        }
        return limitResult;
    }

    /**
     * 查询限制数据
     * @param type 1数据源 2自定义页面
     * @param code 标识（数据源别名或表名）
     * @return
     */
    @Override
    public SysFieldLimitDataVo queryLimitDataByCode(int type, String code) {
        return queryLimitDataByCode(type, code, true, null);
    }

    /**
     * 查询限制数据
     * @param type 1数据源 2自定义页面
     * @param code 标识（数据源别名或表名）
     * @param queryRowData 是否查询行数据
     * @return
     */
    @Override
    public SysFieldLimitDataVo queryLimitDataByCode (int type, String code, boolean queryData) {
        return queryLimitDataByCode(type, code, queryData, null);
    }

    /**
     * 查询指定行id的限制数据
     * @param type 1数据源 2自定义页面
     * @param code 标识（数据源别名或表名）
     * @param dataIdList 数据id（queryRowData为true才生效）
     * @return
     */
    @Override
    public SysFieldLimitDataVo queryLimitDataByCode(int type, String code, List<String> dataIdList) {
        return queryLimitDataByCode(type, code, true, null);
    }

    /**
     * 根据数据源别名查询限制数据
     * @param type 1数据源 2自定义页面
     * @param code 标识（数据源别名或表名）
     * @param queryRowData 是否查询行数据
     * @param dataIdList 数据id（queryRowData为true才生效）
     * @return
     */
    @Override
    public SysFieldLimitDataVo queryLimitDataByCode(int type, String code, boolean queryRowData, List<String> dataIdList) {
        SysFieldLimitDataVo result = new SysFieldLimitDataVo();
        SysUser currentUser = SysUserHolder.getCurrentUser();
        if (currentUser == null) {
            return result;
        }
        Map<String, SysFieldLimitSet> limitSetResult = this.getLimitSetResult(type, code);
        if (limitSetResult == null) {
            //字段为空时全允许编辑
            result.setAllCanEdit(true);
            return result;
        }

        String userId = Optional.of(currentUser).map(SysUser::getId).orElse("");
        String postId = Optional.of(currentUser).map(SysUser::getPostId).orElse("");
        List<String> roles = Optional.of(currentUser).map(SysUser::getRoles).orElse(null);
        String orgId = Optional.of(currentUser).map(SysUser::getOrgId).orElse("");

        Where where = Where.create().eq(SysFieldLimitData::getCode, code);
        if (StringUtils.isNotEmpty(dataIdList)) {
            where.in(SysFieldLimitData::getPid, dataIdList.toArray());
        }
        
        List<Map.Entry<String, SysFieldLimitSet>> setlist = new ArrayList<>();
        for (Map.Entry<String, SysFieldLimitSet> entry : limitSetResult.entrySet()) {
        	SysFieldLimitSet set = entry.getValue();
            if (set == null) {
                continue;
            }
            setlist.add(entry);
        }
        
        if (StringUtils.isNotEmpty(setlist)) {
        	where.and();
        	where.lb();
        	int index = 0;
        	for (Map.Entry<String, SysFieldLimitSet> entry : setlist) {
        		String paraCode = entry.getKey();
        		SysFieldLimitSet set = entry.getValue();
        		int tmType = Optional.ofNullable(set.getTmType()).orElse(0);
        		if (index > 0) {
        			where.or();
        		}
        		where.lb();
        		where.eq(SysFieldLimitData::getParamCode, paraCode);
        		if (tmType == 1) {
        			where.eq(SysFieldLimitData::getZyid, userId);
        		} else if (tmType == 2) {
        			where.eq(SysFieldLimitData::getGwid, postId);
        		} else if (tmType == 3 && StringUtils.isNotEmpty(roles)) {
        			where.lb();
        			for (int i = 0; i < roles.size(); i++) {
        				String s = roles.get(i);
        				if (i > 0) {
        					where.or();
        				}
        				where.like(SysFieldLimitData::getJsid, s);
        			}
        			where.rb();
        		} else if (tmType == 4) {
        			where.eq(SysFieldLimitData::getOrgbm, orgId);
        		}
        		where.rb();
        		index++;
        	}
        	where.rb();
        }

        List<SysFieldLimitData> limitDataList = !queryRowData ? null : dao.queryData(SysFieldLimitData.class, where, null, null);

        //计算计数map，{字段别名: {行id: 修改次数}}
        Map<String, Map<String, Integer>> countMap = new HashMap<>();
        if (StringUtils.isNotEmpty(limitDataList)) {
            for (SysFieldLimitData data : limitDataList) {
                String dataId = data.getPid();
                String paramCode = data.getParamCode();
                countMap.computeIfAbsent(dataId, k -> new HashMap<>()).compute(paramCode, (k, v) -> (v == null ? 0 : v) + 1);
            }
        }

        //{readOnlyCols: [], hiddenCols: [], rowCannotEditCols: {dataId: [col]}}
//        Map<String, Long> countMap = list.stream().collect(Collectors.groupingBy(v -> v.getParamCode() + "____" + v.getPid(), Collectors.counting()));
        List<String> hiddenCols = new ArrayList<>();    //隐藏的字段list
        List<String> readOnlyCols = new ArrayList<>();  //只读的字段list
        Map<String, Map<String, Boolean>> rowCannotEditCols = new HashMap<>();

        //判断只读和隐藏
        for (Map.Entry<String, SysFieldLimitSet> entry : limitSetResult.entrySet()) {
            String paraCode = entry.getKey();
            SysFieldLimitSet set = entry.getValue();
            if (set == null) {
                continue;
            }
            int isShow = Optional.ofNullable(set.getIsShow()).orElse(0);
            int isReadOnly = Optional.ofNullable(set.getIsReadOnly()).orElse(0);
            if (isShow == 0) {
                //隐藏字段
                hiddenCols.add(paraCode);
            } else if (isReadOnly == 1) {
                //只读字段
                readOnlyCols.add(paraCode);
            }
        }

        result.setHiddenCols(hiddenCols);
        result.setReadOnlyCols(readOnlyCols);
        if (!queryRowData) {
            return result;
        }

        //组装数据
        for (String dataId : dataIdList) {
            Map<String, Integer> paramCountMap = countMap.get(dataId);
            for (Map.Entry<String, SysFieldLimitSet> entry : limitSetResult.entrySet()) {
                String paraCode = entry.getKey();
                SysFieldLimitSet set = entry.getValue();
                if (set == null || set.getEditTimes() == null) {
                    //没有设置项默认不能编辑
                    rowCannotEditCols.computeIfAbsent(dataId, k -> new HashMap<>()).put(paraCode, false);
                    continue;
                }
                int editTimes = set.getEditTimes();
                if (editTimes <= 0) {
                    //编辑次数小于等于0为无限
                    rowCannotEditCols.computeIfAbsent(dataId, k -> new HashMap<>()).put(paraCode, true);
                    continue;
                }
                int count = paramCountMap == null ? 0 : paramCountMap.getOrDefault(paraCode, 0);
                rowCannotEditCols.computeIfAbsent(dataId, k -> new HashMap<>()).put(paraCode, count < set.getEditTimes());
            }
        }

//        for (Map.Entry<String, SysFieldLimitSet> entry : limitSetResult.entrySet()) {
//            String paraCode = entry.getKey();
//            SysFieldLimitSet set = entry.getValue();
//
//            int isShow = Optional.ofNullable(set.getIsShow()).orElse(0);
//            int isReadOnly = Optional.ofNullable(set.getIsReadOnly()).orElse(0);
//            if (isShow == 0) {
//                //隐藏字段
//                hiddenCols.add(paraCode);
//            } else if (isReadOnly == 1) {
//                //只读字段
//                readOnlyCols.add(paraCode);
//            } else if (countMap.containsKey(paraCode)) {
//                //其余查询剩余次数
//                Map<String, Integer> dataIdCountMap = countMap.get(paraCode);
//                for (String dataId : dataIdList) {
//
//                }
//
//                for (Map.Entry<String, Integer> dataEntry : dataIdCountMap.entrySet()) {
//                    String dataId = dataEntry.getKey();
//                    int count = dataEntry.getValue();
//                    if (count >= set.getEditTimes()) {
//                        //到次数了
//                        rowCannotEditCols.computeIfAbsent(dataId, k -> new ArrayList<>()).add(paraCode);
//                    }
//                }
//            }
//        }
        result.setRowCannotEditCols(rowCannotEditCols);
        return result;
    }


    /**
     * 保存限制数据
     *
     * @param type 1数据源 2自定义页面
     * @param code 标识（数据源别名或表名）
     * @param data
     * @return
     */
    @Override
    public String saveLimitData(int type, String code, JSONArray data) {
        //[{a：1，b：2，old：{a:1,b:2}}]
        if (StringUtils.isEmpty(data)) {
            return "没有要保存的数据";
        }
        Map<String, List<String>> rowColMap = new HashMap<>();
//        List<String> dataIdList = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JSONObject jobj = data.getJSONObject(i);
            String id = type == 1 ? jobj.getString("ID") : jobj.getString("id");
            if (StringUtils.isEmpty(id)) {
                continue;
            }
//            dataIdList.add(id);
            JSONObject oldJobj = jobj.getJSONObject("TDSROW_originalValue");
            if (StringUtils.isEmpty(oldJobj)) {
                continue;
            }
            for (Map.Entry<String, Object> entry : oldJobj.entrySet()) {
                String key = entry.getKey();
                Object oldValue = entry.getValue();
                Object newValue = jobj.get(key);
                String oldV = oldValue == null ? "" : String.valueOf(oldValue);
                String newV = newValue == null ? "" : String.valueOf(newValue);
                if (!newV.equals(oldV)) {
                    //新旧值不等，则表示修改了该字段
                    rowColMap.computeIfAbsent(id, k -> new ArrayList<>()).add(key);
                }
            }
        }
        if (StringUtils.isEmpty(rowColMap)) {
            return null;
        }

        Map<String, SysFieldLimitSet> limitSetResult = this.getLimitSetResult(type, code);
        if (StringUtils.isEmpty(limitSetResult)) {
            return null;
        }
        List<SysFieldLimitData> insertList = new ArrayList<>();
        SysUser currentUser = SysUserHolder.getCurrentUser();
        String userId = currentUser.getId();
        String postId = currentUser.getPostId();
        List<String> roles = currentUser.getRoles();
        String orgId = currentUser.getOrgId();
        for (Map.Entry<String, List<String>> entry : rowColMap.entrySet()) {
            String id = entry.getKey();
            List<String> colList = entry.getValue();
            if (StringUtils.isEmpty(colList)) {
                continue;
            }
            for (String col : colList) {
                if (!limitSetResult.containsKey(col)) {
                    //设置里没有该字段，跳过
                    continue;
                }
                SysFieldLimitSet set = limitSetResult.get(col);
                int tmType = Optional.ofNullable(set.getTmType()).orElse(0);
                if (tmType < 1 || tmType > 4) {
                    //类型  1、人员、2、岗位、3、角色、4、机构
                    continue;
                }
                SysFieldLimitData limitData = new SysFieldLimitData();
                limitData.setId(TMUID.getUID());
                limitData.setParamCode(col);
                limitData.setCode(code);
                limitData.setTmType(tmType);
                limitData.setPid(id);
                limitData.setZyid(userId);
                limitData.setZyxm(currentUser.getRealName());
                limitData.setGwid(postId);
                limitData.setGwmc(currentUser.getPostName());
                limitData.setJsid(roles==null?null:StringUtils.join(roles, ","));
                limitData.setJsmc(currentUser.getRoleName());
                limitData.setOrgbm(orgId);
                limitData.setOrgmc(currentUser.getOrgName());
                insertList.add(limitData);
            }
        }
        if (StringUtils.isNotEmpty(insertList)) {
            dao.insertBatch(insertList);
        }
        return null;
    }


    /**
     * 获取限制设置
     * @param type
     * @param alias
     * @return
     */
    private List<SysFieldLimit> getLimitSetList(int type, String alias) {
        List<SysFieldLimit> sysFieldLimits = dao.queryData(SysFieldLimit.class, Where.create().eq(SysFieldLimit::getTmType, type).eq(SysFieldLimit::getCode, alias), null, null);
        if (StringUtils.isEmpty(sysFieldLimits)) {
            return null;
        }
        List<String> collect = sysFieldLimits.stream().map(SysFieldLimit::getId).collect(Collectors.toList());
        List<SysFieldLimitSet> sysFieldLimitSets = dao.queryData(SysFieldLimitSet.class, Where.create().in(SysFieldLimitSet::getPid, collect.toArray()), null, null);
        if (StringUtils.isEmpty(sysFieldLimitSets)) {
            return sysFieldLimits;
        }
        Map<String, List<SysFieldLimitSet>> group = sysFieldLimitSets.stream().collect(Collectors.groupingBy(SysFieldLimitSet::getPid));
        for (SysFieldLimit limit : sysFieldLimits) {
            limit.setSetList(group.get(limit.getId()));
        }
        return sysFieldLimits;
    }


//    private List<JSONObject> addLimitKey (Integer type, String code, List<T> list) {
//        if (StringUtils.isEmpty(list)) {
//            return null;
//        }
//        List<JSONObject> result = new ArrayList<>();
//        List<JSONObject> jsonObjects = ObjUtils.convertToList(JSONObject.class, list);
//        List<String> idList = new ArrayList<>();
//        for (T t : list) {
//            JSONObject obj = ObjUtils.convertTo(t, JSONObject.class);
//            if (obj == null) {
//                continue;
//            }
//            String id = obj.getString("id");
//            idList.add(id);
//            obj.put("TDSROW_originalValue", ObjUtils.convertTo(t, JSONObject.class));
//            result.add(obj);
//        }
//
//        SysFieldLimitDataVo limitData = this.queryLimitDataByCode(type, code, idList);
//        if (limitData == null) {
//            return result;
//        }
//        List<String> hiddenCols = limitData.getHiddenCols();
//        List<String> readOnlyCols = limitData.getReadOnlyCols();
//        Map<String, List<String>> rowCannotEditCols = limitData.getRowCannotEditCols();
//        if (StringUtils.isEmpty(hiddenCols) && StringUtils.isEmpty(readOnlyCols) && StringUtils.isEmpty(rowCannotEditCols)) {
//            return result;
//        }
//        result.forEach(item -> {
//            String id = item.getString("id");
//            List<String> cannotEiditCols = StringUtils.isNotEmpty(rowCannotEditCols) ? rowCannotEditCols.get(id) : null;
//            if (StringUtils.isNotEmpty(readOnlyCols)) {
//                cannotEiditCols = cannotEiditCols == null ? new ArrayList<>() : cannotEiditCols;
//                cannotEiditCols.addAll(readOnlyCols);
//            }
//            if (StringUtils.isNotEmpty(cannotEiditCols)) {
//                item.put("TDSROW_limitSet_cannotEditCols", cannotEiditCols);
//            }
//        });
//        return result;
//
//    }


    public static void main(String[] args) {
        String str = "ceshixiaoxi";
        str = new String(str.getBytes(), StandardCharsets.UTF_16);
        str = new String(str.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        System.out.println(str);
    }

}
