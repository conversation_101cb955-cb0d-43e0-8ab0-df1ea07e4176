package com.yunhesoft.system.settings.controller;

import java.util.List;

import com.yunhesoft.system.settings.entity.dto.SysOpenInterFaceWhiteDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.settings.entity.SysOpenInterfaceWhite;
import com.yunhesoft.system.settings.service.IOpenInterfaceWhiteService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/system/settings/open-white")
@Api(tags = "开放接口白名单")
public class OpenInterfaceWhiteController extends BaseRestController {
	@Autowired
	private IOpenInterfaceWhiteService openService;

	@ApiOperation(value = "保存开放接口白名单")
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public Res<String> save(@RequestBody List<SysOpenInterFaceWhiteDto> entities) {
		openService.save(entities);
		return new Res<String>().ok("保存完成");
	}

}
