package com.yunhesoft.system.settings.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @category controller层级的白名单
 * <AUTHOR>
 * @since 2023.3.28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@ApiModel(value = "开发接口白名单", description = "controller层级的开放接口白名单")
@Table(name = "SYS_OPEN_INTERFACE_WHITE", indexes = { @Index(columnList = "CTRL_API") })
public class SysOpenInterfaceWhite extends BaseEntity {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty("模块编码")
	@Column(name = "MODULE_CODE")
	private String moduleCode;
	@ApiModelProperty("开放接口")
	@Column(name = "CTRL_API")
	private String ctrlApi;
	@ApiModelProperty("授权token")
	@Column(name = "TOKEN")
	private String token;
	@ApiModelProperty(value = "登录名称/授权名称", notes = "anonymous = 匿名用户，指定给指定用户，请自行定义用户名称")
	@Column(name = "USER_NAME")
	private String userName;
	@ApiModelProperty("到期日期")
	@Column(name = "EXPIRE_DATE")
	private Date expireDate;
	@ApiModelProperty("启用")
	@Column(name = "ENABLED")
	Integer enabled;
	@ApiModelProperty("是否删除")
	@Column(name = "USED")
	Integer used;
}
