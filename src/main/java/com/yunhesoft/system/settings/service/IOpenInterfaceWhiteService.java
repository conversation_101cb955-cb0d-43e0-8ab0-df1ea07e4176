package com.yunhesoft.system.settings.service;

import java.util.List;

import com.yunhesoft.system.settings.entity.SysOpenInterfaceWhite;
import com.yunhesoft.system.settings.entity.dto.SysOpenInterFaceWhiteDto;

public interface IOpenInterfaceWhiteService {

	List<SysOpenInterfaceWhite> read();

	List<SysOpenInterfaceWhite> read(SysOpenInterfaceWhite param);

	void save(List<SysOpenInterFaceWhiteDto> entities);

}
