package com.yunhesoft.system.settings.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.settings.entity.SysOpenInterfaceWhite;
import com.yunhesoft.system.settings.entity.dto.SysOpenInterFaceWhiteDto;
import com.yunhesoft.system.settings.service.IOpenInterfaceWhiteService;
import com.yunhesoft.tmtools.JwtUser;
import com.yunhesoft.tmtools.TokenUtils;

import lombok.extern.log4j.Log4j2;

/**
 * @category 开放ctrl Api 服务类
 * <AUTHOR>
 *
 */
@Log4j2
@Service
public class OpenInterfaceWhiteServiceImpl implements IOpenInterfaceWhiteService {
	@Autowired
	private EntityService entityService;

	private static List<SysOpenInterfaceWhite> whiteList = null;

	/**
	 * 表格操作
	 * 
	 * @category 保存白名单
	 */
	@Override
	public void save(List<SysOpenInterFaceWhiteDto> entities) {
		try {
			List<SysOpenInterfaceWhite> updates = new ArrayList<>();
			List<SysOpenInterfaceWhite> inserts = new ArrayList<>();
			Map<String, SysOpenInterfaceWhite> map = new HashMap<>();
			// 读取所有的表中数据
			List<SysOpenInterfaceWhite> recs = entityService.queryList(SysOpenInterfaceWhite.class);
			for (SysOpenInterfaceWhite rec : recs) {
				String key = rec.getCtrlApi() + "." + rec.getUserName();
				map.put(key, rec);
			}
			for (SysOpenInterFaceWhiteDto entity : entities) {
				SysOpenInterfaceWhite beanEntity = ObjUtils.copyTo(entity, SysOpenInterfaceWhite.class);
				beanEntity.setUsed(1);
				JwtUser juser = new JwtUser();
				juser.setUserName(beanEntity.getUserName());
				juser.setIat(new Date());
				juser.setExpire(beanEntity.getExpireDate().getTime() - System.currentTimeMillis());
				if (StringUtils.isEmpty(beanEntity.getToken()) || (StringUtils.isNotEmpty(beanEntity.getToken())
						&& DateTimeUtils.bjDate(new Date(), beanEntity.getExpireDate()) == 1)) {
					// token为空 或者 token不为空并且当前时间已经过期时重新生成token
					beanEntity.setToken(TokenUtils.createToken(juser));
				}
				String key = beanEntity.getCtrlApi() + "." + beanEntity.getUserName();
				if (map.containsKey(key)) {
					// 数据库中已经存在
					SysOpenInterfaceWhite sqlBean = map.get(key);
					beanEntity.setId(sqlBean.getId());
					if (!beanEntity.getUserName().equals(sqlBean.getUserName())) {
						// 用户名不相等时 重新生成token
						beanEntity.setToken(TokenUtils.createToken(juser));
					}
					if (beanEntity.getExpireDate().compareTo(sqlBean.getExpireDate()) != 0) {
						// 日期被修改不相等时重新生成token
						beanEntity.setToken(TokenUtils.createToken(juser));
					}
					if (entity.getTDSROW_rowFlag() != null && entity.getTDSROW_rowFlag() != 0
							&& entity.getTDSROW_rowFlag() != 1) {
						// 删除
						beanEntity.setUsed(0);
					}
					updates.add(beanEntity);
				} else {
					// 数据库不存在时为纪录生成token
					beanEntity.setToken(TokenUtils.createToken(juser));
					if (entity.getTDSROW_rowFlag() == null || entity.getTDSROW_rowFlag() == 0) {
						// 表中的新增操作
						beanEntity.setId(TMUID.getUID());
						inserts.add(beanEntity);
					} else if (entity.getTDSROW_rowFlag() == 1) {
						// 表中的更新操作
						updates.add(beanEntity);
					}
				}
			}
			if (updates.size() > 0) {
				entityService.rawUpdateByIdBatch(updates);
			}
			if (inserts.size() > 0) {
				entityService.insertBatch(inserts);
			}
		} catch (Exception e) {
			log.error("", e);
		} finally {
			this.clearWhiteList();
			this.read();
		}

	}

	/**
	 * @category 表格 读取白名单
	 * @param ctrlApi
	 * @return
	 */
	@Override
	public List<SysOpenInterfaceWhite> read(SysOpenInterfaceWhite param) {
		Where where = Where.create();
		where.eq(SysOpenInterfaceWhite::getUsed, 1);
		if (param.getEnabled() != null) {
			where.eq(SysOpenInterfaceWhite::getEnabled, param.getEnabled());
		}
		if (StringUtils.isNotEmpty(param.getCtrlApi())) {
			where.eq(SysOpenInterfaceWhite::getCtrlApi, param.getCtrlApi());
		}
		if (StringUtils.isNotEmpty(param.getUserName())) {
			where.eq(SysOpenInterfaceWhite::getUserName, param.getUserName());
		}
		return entityService.queryList(SysOpenInterfaceWhite.class, where);
	}

	/**
	 * @category 读取白名单
	 * @return
	 */
	@Override
	public List<SysOpenInterfaceWhite> read() {
		if (whiteList == null) {
//			System.out.println("数据库中白名单读取====================");
			Where where = Where.create();
			where.eq(SysOpenInterfaceWhite::getUsed, 1);
			// where.eq(SysOpenInterfaceWhite::getEnabled, 1);
			whiteList = entityService.queryList(SysOpenInterfaceWhite.class, where);
			if (whiteList == null) {
				whiteList = new ArrayList<SysOpenInterfaceWhite>();
			}
		}
		return whiteList;
	}

	private void clearWhiteList() {
		whiteList = null;
	}
}
