package com.yunhesoft.system.r3dbInterface.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.r3dbInterface.entity.po.InterfaceError;
import com.yunhesoft.system.r3dbInterface.entity.po.InterfaceReg;
import com.yunhesoft.system.r3dbInterface.service.IInterfaceService;

import lombok.extern.log4j.Log4j2;

/**
 * 接口操作类实现
 * 
 * <AUTHOR>
 *
 */
@Log4j2
@Service
public class IInterfaceServiceImpl implements IInterfaceService {

	@Autowired
	private EntityService dao;

	/**
	 * 读取接口注册信息
	 */
	@Override
	public List<InterfaceReg> getInterfaceReg() {
		return dao.queryList(InterfaceReg.class
				, Where.create().eq(InterfaceReg::getIsuse, 1).eq(InterfaceReg::getUsed, 1).eq(InterfaceReg::getApptype, 1)
				, Order.create().orderByAsc(InterfaceReg::getTmsort));
	}

	/**
	 * 读取接口错误信息
	 */
	@Override
	public List<InterfaceError> getInterfaceError() {
		// WHERE ISUSE=1 and USED=1 AND ERRTYPE=1 ORDER BY TMSORT
		return dao.queryList(InterfaceError.class
				, Where.create().eq(InterfaceError::getIsuse, 1).eq(InterfaceError::getUsed, 1).eq(InterfaceError::getErrtype, 1)
				, Order.create().orderByAsc(InterfaceError::getTmsort));
	}

}
