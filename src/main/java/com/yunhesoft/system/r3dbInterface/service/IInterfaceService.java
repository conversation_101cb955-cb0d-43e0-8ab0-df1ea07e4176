package com.yunhesoft.system.r3dbInterface.service;

import java.util.List;

import com.yunhesoft.system.r3dbInterface.entity.po.InterfaceError;
import com.yunhesoft.system.r3dbInterface.entity.po.InterfaceReg;

/**
 * 接口操作类
 * 
 * <AUTHOR>
 *
 */
public interface IInterfaceService {


    /**
     * 读取接口注册信息
     */
	List<InterfaceReg> getInterfaceReg();



    /**
     * 读取接口错误信息
     */
	List<InterfaceError> getInterfaceError();
}
