package com.yunhesoft.system.r3dbInterface.entity.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * 仪表对象
 * 2021.08.29
 * z<PERSON><PERSON>
 */
public class Tag {

    /**
     * 接口认证码
     */
    public String authId=null;
    
	/**
	 * 仪表位号
	 */
	private String tagCode=null;

	/**
	 * 仪表数据
	 */
	public List<TagData> datas = null;

	/**
	 * 未来仪表其它属性预留
	 */
	//String other;

	public String getAuthId() {
		return authId;
	}

	public void setAuthId(String authId) {
		this.authId = authId;
	}
	
	public String getTagCode() {
		return tagCode;
	}

	public void setTagCode(String tagCode) {
		this.tagCode = tagCode;
	}

	public List<TagData> getDatas() {
		return datas;
	}

	public void setDatas(List<TagData> datas) {
		this.datas = datas;
	}
	
	/**
	 * 构造函数
	 */
	public Tag(){}
	
	/**
	 * 构造函数
	 * @param authId 接口认证码
	 * @param tagCode 仪表位号
	 */
    public Tag(String authId, String tagCode){
        if (tagCode != null && !"".equals(tagCode.trim())){
            this.tagCode = tagCode.trim();
            this.authId = authId;
            this.datas = new ArrayList<TagData>();
        }else{
        	System.out.println("com.yunhe.tools.Interface.Tag.调用Tag构造函数时，tagCode不允许为空。");
        }
    }

    /**
     * 添加数据
     * @param data
     * @return
     */
    public TagData addData(TagData data)
    {
        TagData tagData = null;
        if (this.tagCode != null && !"".equals(this.tagCode.trim()) || this.datas != null){
            //赋值仪表位号
            data.setTagCode(this.tagCode);
            this.datas.add(data);

            //生成返回值
            tagData = data;
        }else{
        	System.out.println("com.yunhe.tools.Interface.Tag.调用Tag.addData(TagData data)函数时，tagCode不允许为空或者datas不允许为空。");
        }
        return tagData;
    }
    
    /**
     * 添加数据
     * @param datetime 时间：传空为当前时间
     * @param value 值
     * @param quality 信任值
     * @param errcode 错误编码
     * @param error 错误信息
     * @return
     */
    public TagData addData(String datetime, String value, String quality, String errcode, String error){
        TagData tagData = null;
        if (this.tagCode != null && !"".equals(this.tagCode.trim()) || this.datas != null){
            tagData = new TagData(this.tagCode, datetime, value, quality, errcode, error);
            this.datas.add(tagData);
        }else{
            System.out.println("com.yunhe.tools.Interface.Tag.调用Tag.addData(String datetime, String value, String quality, String errcode, String error)函数时，tagCode不允许为空或者datas不允许为空。");
        }
        return tagData;
    }
    

    /**
     * 添加数据
     * @param datetime 时间：传空为当前时间
     * @param value 值
     * @param quality 信任值
     * @return
     */
    public TagData addData(String datetime, String value, String quality){
        return this.addData(datetime, value, quality, null, null);
    }
    
    
}