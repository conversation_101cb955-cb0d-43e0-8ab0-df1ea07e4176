package com.yunhesoft.system.r3dbInterface.entity.dto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.alibaba.fastjson.JSONArray;

/**
 * 接口仪表数据对象
 * 2021.08.29
 * z<PERSON><PERSON>
 */
public class TagDatas {
	
    /**
     * 接口认证码
     */
    public String authId=null;
    
	/**
	 * 全量数据
	 */
	private List<TagData> datas = null;
	
	/**
	 * 全量仪表及数据
	 */
	private List<Tag> tags = null;
	
	/**
	 * 全量仪表及数据MAP
	 */
	private HashMap<String,Tag> mapTags = null;
	
	/**
	 * 接口认证码
	 */
	public String getAuthId() {
		return authId;
	}

	/**
	 * 接口认证码
	 */
	public void setAuthId(String authId) {
		this.authId = authId;
	}
	
	/**
	 * 全量数据
	 */
	public List<TagData> getDatas() {
		return datas;
	}
	/**
	 * 全量数据
	 */
	public void setDatas(List<TagData> datas) {
		this.datas = datas;
	}
	/**
	 * 全量仪表及数据
	 */
	public List<Tag> getTags() {
		return tags;
	}
	/**
	 * 全量仪表及数据
	 */
	public void setTags(List<Tag> tags) {
		this.tags = tags;
	}
	/**
	 * 全量仪表及数据MAP
	 */
	public HashMap<String, Tag> getMapTags() {
		return mapTags;
	}
	/**
	 * 全量仪表及数据MAP
	 */
	public void setMapTags(HashMap<String, Tag> mapTags) {
		this.mapTags = mapTags;
	}
	
	/**
	 * 构造函数
	 */
	public TagDatas(){}
	
    /**
     * 构造函数
     * @param authId 接口认证码
     */
    public TagDatas(String authId){
        this.authId = authId;
        this.tags = new ArrayList<Tag>();
        this.datas = new ArrayList<TagData>();
        this.mapTags = new HashMap<String, Tag>();
    }

    
    /**
     * 添加仪表数据
     * @param tagCode 仪表位号
     * @param datetime 时间：传空为当前时间
     * @param value 值
     * @param quality 信任值
     * @param errcode 错误编码
     * @param error 错误信息
     * @return
     */
    public Tag addData(String tagCode, String datetime, String value, String quality, String errcode, String error){
        Tag tag = null;
        try{
            if (this.mapTags != null && tagCode != null && !"".equals(tagCode.trim())){
                tagCode = tagCode.trim();
                TagData tagData = null;
                if (this.mapTags.containsKey(tagCode.toUpperCase())){
                    //存在此仪表(仪表位号转换成大写用于MAP查找)
                    tag = this.mapTags.get(tagCode.toUpperCase());
                    //添加数据
                    tagData = tag.addData(datetime, value, quality, errcode, error);
                }else{
                    //不存在此仪表，则创建
                    tag = new Tag(this.authId, tagCode);
                    //添加数据
                    tagData = tag.addData(datetime, value, quality, errcode, error);
                    //将仪表加入MAP(仪表位号转换成大写用于MAP查找)
                    this.mapTags.put(tagCode.toUpperCase(), tag);
                    //将仪表加入列表
                    if (this.tags != null) this.tags.add(tag);
                }

                //加入全量数据列表
                if (this.datas != null && tagData != null) this.datas.add(tagData);
            }
        }catch (Exception ex){
        	ex.printStackTrace();
        }
        return tag;
    }
    /**
     * 添加仪表数据
     * @param tagCode 仪表位号
     * @param datetime 时间：传空为当前时间
     * @param value 值
     * @param quality 信任值
     * @param errcode 错误编码
     * @param error 错误信息
     * @return
     */
    public Tag addData(String tagCode, String datetime, String value, String quality){
    	return this.addData(tagCode, datetime, value, quality, null, null);
    }

    /// <summary>
    /// 将数据转换成JSON字符串
    /// </summary>
    /// <returns></returns>
    public String toJsonData(){
        String jsonData = null;
        try{
            if (this.tags != null && this.tags.size() > 0){
                jsonData = JSONArray.toJSONString(this.tags);
            }
        }catch (Exception ex){
        	ex.printStackTrace();
        }
        return jsonData;
    }
    
    /// <summary>
    /// 将数据转换成JSON数组
    /// </summary>
    /// <returns></returns>
    public JSONArray toJsonArray(){
    	JSONArray jsonData = null;
        try{
            if (this.tags != null && this.tags.size() > 0){
                jsonData = JSONArray.parseArray(this.toJsonData());
            }
        }catch (Exception ex){
        	ex.printStackTrace();
        }
        return jsonData;
    }
    
}
