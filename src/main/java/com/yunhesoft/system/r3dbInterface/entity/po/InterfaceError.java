package com.yunhesoft.system.r3dbInterface.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * InterfaceError
 */

@Entity
@Getter
@Setter
@Table(name = "I_INTERFACE_ERROR")
public class InterfaceError extends BaseEntity {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(generator = "assigned")
	@GenericGenerator(name = "assigned", strategy = "assigned")
	@Column(name = "ID", length = 100)
	private String id;

	@ApiModelProperty(value = "错误编码", example = "错误编码1")
	@Column(name = "ERRCODE", length = 200)
	private String errcode;

	@ApiModelProperty(value = "错误名称", example = "错误名称1")
	@Column(name = "ERRNAME", length = 500)
	private String errname;

	@ApiModelProperty(value = "错误解决方案地址", example = "错误解决方案地址1")
	@Column(name = "ERRURL", length = 500)
	private String errurl;

	@ApiModelProperty(value = "错误类型，1：接口预警功能 0：其它", example = "错误类型，1：接口预警功能 0：其它1")
	@Column(name = "ERRTYPE", length = 500)
	private String errtype;

	@ApiModelProperty(value = "备注信息", example = "备注信息1")
	@Column(name = "MEMO", length = 3000)
	private String memo;

	@ApiModelProperty(value = "排序", example = "1")
	@Column(name = "TMSORT")
	private Integer tmsort;

	@ApiModelProperty(value = "是否使用", example = "1")
	@Column(name = "ISUSE")
	private Integer isuse;

	@ApiModelProperty(value = "是否删除", example = "1")
	@Column(name = "USED")
	private Integer used;

}