package com.yunhesoft.system.r3dbInterface.entity.dto;

import java.util.Date;

/**
 * 仪表数据点对象
 * 2021.08.29
 * z<PERSON><PERSON>
 */
public class TagData {
	
	/**
	 * 构造函数
	 */
	public TagData(){}
	
	/**
	 * 构造函数
	 * @param tagCode 仪表位号
	 * @param datetime 时间：传空为当前时间
	 * @param value 值
	 * @param quality 信任值
	 * @param errcode 错误编码
	 * @param error 错误信息
	 */
    public TagData(String tagCode, String datetime, String value, String quality){
        this.initTagData(tagCode, datetime, value, quality, null, null);
    }
    
	/**
	 * 构造函数
	 * @param tagCode 仪表位号
	 * @param datetime 时间：传空为当前时间
	 * @param value 值
	 * @param quality 信任值
	 * @param errcode 错误编码
	 * @param error 错误信息
	 */
    public TagData(String tagCode, String datetime, String value, String quality, String errcode, String error){
        this.initTagData(tagCode, datetime, value, quality, errcode, error);
    }

	/**
	 * 初始化函数
	 * @param tagCode 仪表位号
	 * @param datetime 时间：传空为当前时间
	 * @param value 值
	 * @param quality 信任值
	 * @param errcode 错误编码
	 * @param error 错误信息
	 */
    private void initTagData(String tagCode, String datetime, String value, String quality, String errcode, String error){
        if (tagCode != null && !"".equals(tagCode.trim())){
            this.tagCode = tagCode.trim();
            this.datetime = datetime;
            this.value = value;
            this.quality = quality;
            this.errcode = errcode;
            this.error = error;
        }
    }
    
	/**
	 * 仪表位号
	 */
	private String tagCode;
	/**
	 * 时间戳
	 */
	private Date time;
	/**
	 * 数据时间(格式yyyy-MM-dd HH:mm:ss.SSS)与时间戳至少有一个有值
	 */
	private String datetime;
	/**
	 * 质量戳 String : -1 仪表错误 0 = 不合格 50 = 一般合格 100 = 合格
	 */
	private String quality;
	/**
	 * 错误编码
	 */
	private String errcode;
	/**
	 * 错误描述
	 */
	private String error;
	/**
	 * 数据值
	 */
	private String value;

	public String getTagCode() {
		return tagCode;
	}
	public void setTagCode(String tagCode) {
		this.tagCode = tagCode;
	}
	public Date getTime() {
		return time;
	}
	public void setTime(Date time) {
		this.time = time;
	}
	public String getDatetime() {
		return datetime;
	}
	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}
	public String getQuality() {
		return quality;
	}
	public void setQuality(String quality) {
		this.quality = quality;
	}
	public String getErrcode() {
		return errcode;
	}
	public void setErrcode(String errcode) {
		this.errcode = errcode;
	}
	public String getError() {
		return error;
	}
	public void setError(String error) {
		this.error = error;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
}