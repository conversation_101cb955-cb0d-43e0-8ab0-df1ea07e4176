package com.yunhesoft.system.r3dbInterface.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * InterfaceReg
 */

@Entity
@Getter
@Setter
@Table(name = "I_INTERFACE_REG")
public class InterfaceReg extends BaseEntity {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(generator = "assigned")
	@GenericGenerator(name = "assigned", strategy = "assigned")
	@Column(name = "ID", length = 100)
	private String id;

	@ApiModelProperty(value = "父ID", example = "1223013741d39bd1")
	@Column(name = "PID", length = 100)
	private String pid;

	@ApiModelProperty(value = "接口分类", example = "接口分类1")
	@Column(name = "APPCLASS", length = 500)
	private String appclass;

	@ApiModelProperty(value = "接口分类编码", example = "接口分类编码1")
	@Column(name = "APPCLASSCODE", length = 500)
	private String appclasscode;

	@ApiModelProperty(value = "接口名称", example = "接口名称1")
	@Column(name = "APPNAME", length = 500)
	private String appname;

	@ApiModelProperty(value = "接口编码", example = "接口编码1")
	@Column(name = "APPCODE", length = 200)
	private String appcode;

	@ApiModelProperty(value = "接口类型，1：WEB端检测页面，0：其它", example = "接口类型，1：WEB端检测页面，0：其它1")
	@Column(name = "APPTYPE", length = 200)
	private String apptype;

	@ApiModelProperty(value = "超时报警时间(秒)", example = "超时报警时间(秒)1")
	@Column(name = "OUTTIME", length = 50)
	private String outtime;

	@ApiModelProperty(value = "备注信息", example = "备注信息1")
	@Column(name = "MEMO", length = 3000)
	private String memo;

	@ApiModelProperty(value = "排序", example = "1")
	@Column(name = "TMSORT")
	private Integer tmsort;

	@ApiModelProperty(value = "是否使用", example = "1")
	@Column(name = "ISUSE")
	private Integer isuse;

	@ApiModelProperty(value = "是否删除", example = "1")
	@Column(name = "USED")
	private Integer used;

}