package com.yunhesoft.system.r3dbInterface.entity.dto;


/**
 * 接口状态检测BEAN
 * 2021.08.28
 * zouhao
 */
public class InterfaceStatusCheckBean {
	
	/**
	 * 分类
	 */
	private String appclass;
	/**
	 * 分类编码
	 */
	private String appclasscode;
	/**
	 * 接口名称
	 */
	private String appname;
	/**
	 * 接口名称编码(仪表位号)
	 */
	private String appcode;
	/**
	 * 接口类型，1：WEB端检测页面，0：其它
	 */
	private String apptype;
	/**
	 * 超时报警时间(秒)
	 */
	private String outtime;
	/**
	 * 备注信息
	 */
	private String memo;
	/**
	 * 排序
	 */
	private Integer tmsort;
	
	/***
	 * 检查状态 0:异常 1:正常
	 */
	private int checkStatus = 1;
	
	/**
	 * 检查后信息
	 */
	private String checkInfo = "正常";
	
	/**
	 * 错误编码
	 */
	private String errCode = "";
	
	/**
	 * 错误编码
	 */
	private String errName = "";
	
	/**
	 * 错误解决方案地址
	 */
	private String errUrl = "";
	
	/**
	 * 错误解决方案描述
	 */
	private String errMemo = "";
	
	

	public String getAppclass() {
		return appclass;
	}

	public void setAppclass(String appclass) {
		this.appclass = appclass;
	}

	public String getAppclasscode() {
		return appclasscode;
	}

	public void setAppclasscode(String appclasscode) {
		this.appclasscode = appclasscode;
	}

	public String getAppname() {
		return appname;
	}

	public void setAppname(String appname) {
		this.appname = appname;
	}

	public String getAppcode() {
		return appcode;
	}

	public void setAppcode(String appcode) {
		this.appcode = appcode;
	}

	public String getApptype() {
		return apptype;
	}

	public void setApptype(String apptype) {
		this.apptype = apptype;
	}

	public String getOuttime() {
		return outtime;
	}

	public void setOuttime(String outtime) {
		this.outtime = outtime;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getTmsort() {
		return tmsort;
	}

	public void setTmsort(Integer tmsort) {
		this.tmsort = tmsort;
	}

	public int getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(int checkStatus) {
		this.checkStatus = checkStatus;
	}

	public String getCheckInfo() {
		return checkInfo;
	}

	public void setCheckInfo(String checkInfo) {
		this.checkInfo = checkInfo;
	}

	public String getErrCode() {
		return errCode;
	}

	public void setErrCode(String errCode) {
		this.errCode = errCode;
	}

	public String getErrUrl() {
		return errUrl;
	}

	public void setErrUrl(String errUrl) {
		this.errUrl = errUrl;
	}

	public String getErrName() {
		return errName;
	}

	public void setErrName(String errName) {
		this.errName = errName;
	}

	public String getErrMemo() {
		return errMemo;
	}

	public void setErrMemo(String errMemo) {
		this.errMemo = errMemo;
	}
}

