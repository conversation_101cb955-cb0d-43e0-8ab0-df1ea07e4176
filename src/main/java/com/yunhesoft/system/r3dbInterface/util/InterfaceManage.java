package com.yunhesoft.system.r3dbInterface.util;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.system.r3dbInterface.entity.dto.InterfaceStatusCheckBean;
import com.yunhesoft.system.r3dbInterface.entity.dto.Tag;
import com.yunhesoft.system.r3dbInterface.entity.dto.TagData;
import com.yunhesoft.system.r3dbInterface.entity.dto.TagDatas;
import com.yunhesoft.system.r3dbInterface.entity.po.InterfaceError;
import com.yunhesoft.system.r3dbInterface.entity.po.InterfaceReg;
import com.yunhesoft.system.r3dbInterface.service.IInterfaceService;

/**
 * 外部数据接口管理类（非R3DB读取类） 2021.08.28
 * <AUTHOR>
 */
public class InterfaceManage {

	/**
	 * 链接失败(异常)
	 */
	private static final String ERR_0001 = "ERR_0001";
	
	/**
	 * 接口注册信息
	 */
	private List<InterfaceReg> listReg = null;
	
	/**
	 * 接口错误信息
	 */
	private List<InterfaceError> listErr = null;
	
	/**
	 * 接口错误信息Map
	 */
	private HashMap<String,InterfaceError> mapErr = null;
	
	/**
	 * 接口服务
	 */
	@Autowired
	private IInterfaceService ifaceSer;
	
	/**
	 * 构造
	 */
	public InterfaceManage(){
		//初始化接口信息
		this.initInterfaceInfo();
	}
	
	/**
	 * 初始化接口信息
	 */
	private boolean initInterfaceInfo(){
		boolean b = true;
		try{
			this.listReg = this.getInterfaceReg();//接口注册信息
			this.listErr = this.getInterfaceError();//接口错误信息
			this.mapErr = this.getInterfaceErrorMap(this.listErr);//接口错误信息MAP
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return b;
	}
	
    /**
     * 读取接口注册信息
     */
	private List<InterfaceReg> getInterfaceReg(){
    	List<InterfaceReg> retValue = null;
    	try{
    		List<InterfaceReg> listData = ifaceSer.getInterfaceReg();
    		if(listData!=null && listData.size()>0){
    			retValue = listData;
    		}
    	}catch(Exception ex){
    		ex.printStackTrace();
    	}
    	return retValue;
    }

    /**
     * 读取接口错误信息
     */
	private List<InterfaceError> getInterfaceError(){
    	List<InterfaceError> retValue = null;
    	try{
    		List<InterfaceError> listData = ifaceSer.getInterfaceError();
    		if(listData!=null && listData.size()>0){
    			retValue = listData;
    		}
    	}catch(Exception ex){
    		ex.printStackTrace();
    	}
    	return retValue;
    }
    
	/**
	 * 生成接口错误信息MAP
	 * @param list 错误信息数据集
	 * @return
	 */
	private HashMap<String,InterfaceError> getInterfaceErrorMap(List<InterfaceError> list){
		HashMap<String,InterfaceError> retValue = new HashMap<String, InterfaceError>();
		try{
			if(list!=null && list.size()>0){
				for(InterfaceError item : list){
					if(!retValue.containsKey(item.getErrcode())){
						retValue.put(item.getErrcode(), item);
					}
				}
			}
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return retValue;
	}
   
    
    /**
     * 调用 R3db 服务(单仪表)
     * @param url 地址
     * @param authId 令牌
     * @param tagCodes 仪表位号
     * @param stime 开始时间
     * @param etime 截止时间
     * @param rdbid 实时数据库(目前传空)
     * @return
     */
    public static TagDatas callRtdbServer(boolean isDebug, String url,String authId,String tagCode,String stime,String etime,String rdbid){
    	List<String> tagCodes = new ArrayList<String>();
    	tagCodes.add(tagCode);
    	return InterfaceManage.callRtdbServer(isDebug, url, authId, tagCodes, stime, etime, rdbid);
    }
    
    /**
     * 调用 R3db 服务（多仪表）
     * @param url 地址
     * @param authId 令牌
     * @param tagCodes 仪表位号
     * @param stime 开始时间
     * @param etime 截止时间
     * @param rdbid 实时数据库(目前传空)
     * @return
     */
    public static TagDatas callRtdbServer(boolean isDebug, String url,String authId,List<String> tagCodes,String stime,String etime,String rdbid){
    	R3dbManage rdbSer = R3dbManage.getServer(isDebug, url, authId);
    	Date stimeDt = DateTimeUtils.parseDateTime(stime);
    	Date etimeDt = DateTimeUtils.parseDateTime(etime);
    	return rdbSer.getData(tagCodes, stimeDt, etimeDt);
    }
    

    /**
	 * post请求
	 * @param url 地址
	 * @param header 头
	 * @param json 体
	 * @return
	 */
	public static String doPost(boolean DEBUG ,String url, Map<String, String> header, JSONObject json) {
		return doPost( DEBUG , url, header, json.toString());
	}
	
    /**
	 * post请求
	 * @param url 地址
	 * @param header 头
	 * @param json 体
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public static String doPost(boolean DEBUG ,String url, Map<String, String> header, String json) {
		return R3dbManage.doPost(DEBUG, url, header, json);
	}
	

    /**
     * 设置接口错误状态
     * @param mapErr 错误编码库
     * @param errCode 错误编码
     * @param statusObj 状态对象
     */
    private static InterfaceStatusCheckBean setInterfaceErrStatus(HashMap<String, InterfaceError> mapErr, String errCode,InterfaceStatusCheckBean statusObj){
    	try{
    		if(statusObj!=null){
	    		statusObj.setErrCode(errCode);
	    		statusObj.setErrName("");
	    		statusObj.setErrUrl("");
	    		statusObj.setErrMemo("");
				statusObj.setCheckStatus(0);//检查状态 0:异常 1:正常
				statusObj.setCheckInfo("");
				if(mapErr!=null && mapErr.size()>0){
					if(mapErr.containsKey(errCode)){
						InterfaceError errOjb = mapErr.get(errCode);
						statusObj.setErrName(errOjb.getErrname());
						statusObj.setErrUrl(errOjb.getErrurl());
						statusObj.setErrMemo(errOjb.getMemo());
						
						//累加错误信息
						StringBuffer checkInfo = new StringBuffer();
						//分类
						checkInfo.append(statusObj.getAppclass());
						//名称
						checkInfo.append(",");checkInfo.append(statusObj.getAppname());checkInfo.append("(");checkInfo.append(statusObj.getAppcode());checkInfo.append(")");
						//错误编码
						checkInfo.append(",");checkInfo.append(statusObj.getErrName());checkInfo.append("(");checkInfo.append(statusObj.getErrCode());checkInfo.append(")");
						//如果有帮助地址，增加帮助地址
						if(statusObj.getErrUrl()!=null && !"".equals(statusObj.getErrUrl())){
							checkInfo.append(",【详见】");checkInfo.append(statusObj.getErrUrl());
						}
						statusObj.setCheckInfo(checkInfo.toString());
					}else{
			    		statusObj.setErrName("未知错误编码");
						statusObj.setCheckInfo(statusObj.getErrName());
					}
				}else{
					statusObj.setCheckInfo("未查找到错误编码库(InterfaceError表未初始化错误信息)。");
				}
    		}
    	}catch(Exception ex){
    		ex.printStackTrace();
    	}
    	return statusObj;
    }
    
	 /**
     * 检测各接口运行状态
     * 
     * @param isDebug 调试模式 1调试模式，0非调试模式
     * @param url 服务地址
     * @param token 调用R3db服务接口的令牌
     * @param showType 显示类型：0 or null查询所有接口，1只查询异常接口
     * @return
     */
    public static List<InterfaceStatusCheckBean> check(boolean isDebug,String url,String token,String showType){
    	List<InterfaceStatusCheckBean> list = new ArrayList<InterfaceStatusCheckBean>();
    	try{
    		
    		if(url==null || "".equals(url.trim())){
    			//(系统参数)R3db系统地址
        		//url = "http://127.0.0.1:8887/tm4main";
    			//url = SystemOptionTools.configParam("R3dbUrl");
    		}
    		if(token==null || "".equals(token.trim())){
    			//(系统参数)R3db系统服务授权码
        		//token = "994E90184E56C2160833B8BD7D7F25B3";
    			//token = SystemOptionTools.configParam("R3dbSerAuth");
    		}
    		
    		if(url==null || "".equals(url.trim())){
    			System.out.println("(系统参数)R3db Url服务地址为null");
    			return list;
    		}else{
    			url = url.trim();
    		}
    		
    		if(token==null || "".equals(token.trim())){
    			System.out.println("(系统参数)R3db系统服务授权码为null");
    			return list;
    		}else{
    			token = token.trim();
    		}
    		
    		//接口地址
    		String path = "rtdb-api/v1/read-tagdata-list";

			//生成调用地址
			String sendAdd = url;
			if(!"/".equals(url.substring(url.length()-1))){
				sendAdd += "/";
			}
			sendAdd += path;
			
    		//初始化接口检测对象
    		InterfaceManage im = new InterfaceManage();
    		if(im.listReg!=null && im.listReg.size()>0){
    			for(int i=0,iCount=im.listReg.size();i<iCount;i++){
    				InterfaceReg item = im.listReg.get(i);
					String APPCODE = item.getAppcode();//接口编码（仪表位号）
					String OUTTIME = item.getOuttime();//超时时间（秒）
    				TagDatas retTagDatas = null;
    				try{
    					
    					//默认超时5分钟
    					int second = 60 * 5;
    					try{second = Integer.parseInt(OUTTIME.trim());}catch(Exception ex){}
    					
    					//当前时间（结束时间）
    					Date nowDate = DateTimeUtils.getNowDate();
    					String nowDateStr = DateTimeUtils.formatDateTime(nowDate);
    					
    					//当前时间-超时时间（开始时间）
    					Date startDate = DateTimeUtils.doSecond(nowDate, -second);
    					String startDateStr = DateTimeUtils.formatDateTime(startDate);
    					
//    					//调用 R3db 服务 获取数据(多仪表调用)
//    					List<String> tagCodes = new ArrayList<String>();
//    			    	tagCodes.add("PING_PHDAPI");
//    			    	tagCodes.add("PING_PHDDA");
//    					retTagDatas = InterfaceManage.callRtdbServer(isDebug, sendAdd, token, tagCodes, startDateStr, nowDateStr, null);
    					
    					//调用 R3db 服务 获取数据(单仪表调用)
    					retTagDatas = InterfaceManage.callRtdbServer(isDebug, sendAdd, token, APPCODE, startDateStr, nowDateStr, null);
		    			
    				}catch(Exception ex){
    					ex.printStackTrace();
    				}
    				
    				//创建检测对象用于返回数据
    				InterfaceStatusCheckBean tempOjb = new InterfaceStatusCheckBean();
    				ObjUtils.copyTo(item, tempOjb);
    				
    				//判断接口是否正常运行(读取实时数据返回值)
    				if(retTagDatas!=null){
    					HashMap<String,Tag> mapTags = retTagDatas.getMapTags();
    					if(mapTags!=null && mapTags.size()>0 && mapTags.containsKey(APPCODE.trim().toUpperCase())){
    						Tag tag = mapTags.get(APPCODE.trim().toUpperCase());
    						if(tag!=null && tag.datas!=null && tag.datas.size()>0){
    							//取出最新一条记录判断状态
    							TagData td = tag.datas.get(tag.datas.size()-1);
    							if(td!=null){
	    							if(td.getErrcode()!=null && !"".equals(td.getErrcode())){
	    								//有错误信息
	    								tempOjb = InterfaceManage.setInterfaceErrStatus(im.mapErr, td.getErrcode(), tempOjb);
	    							}else{
	    								//无错误信息，接口正常运行。
	    							}
    							}else{
        	    					System.out.println("TagData对象为null。正常不应该出现此情况。[需要跟踪代码]");
    							}
    						}else{
    							//接口未返回数据(无心跳数据)
    	    					tempOjb = InterfaceManage.setInterfaceErrStatus(im.mapErr, InterfaceManage.ERR_0001, tempOjb);
    						}
    					}else{
    						//接口未返回数据(无心跳数据)
        					tempOjb = InterfaceManage.setInterfaceErrStatus(im.mapErr, InterfaceManage.ERR_0001, tempOjb);
    					}
    				}else{
    					//接口未返回数据（可能是远程InfluxDB服务器未连接成功，此处不发短信）
    					tempOjb = null;
    					//tempOjb = InterfaceManage.setInterfaceErrStatus(im.mapErr, InterfaceManage.ERR_0001, tempOjb);
    				}
    				
    				//检测对象不为空时，返回数据。
    				if(tempOjb!=null){
	    				//显示类型：0 or null查询所有接口，1只查询异常接口
	    				if(showType!=null && "1".equals(showType.trim())){
	    					//只显示异常接口
	    					if(tempOjb!=null && tempOjb.getCheckStatus()!=1){
	    						//接口异常
	            				list.add(tempOjb);
	    					}
	    				}else{
	        				//显示所有接口信息
	        				list.add(tempOjb);
	    				}
    				}
    			}
    		}
    	}catch(Exception ex){
    		ex.printStackTrace();
    	}
    	return list;
    }
    
    
    /**
     * 调试入口
     * @param args
     */
    /*public static void main(String[] args){
//    	List<InterfaceStatusCheckBean> list = InterfaceManage.check(false, "http://127.0.0.1:8887/tm4main", "994E90184E56C2160833B8BD7D7F25B3", "0");
    	List<InterfaceStatusCheckBean> list = InterfaceManage.check(false, null, null, null);
    	System.out.println(JSONArray.toJSONString(list));
    	
    }*/
    
}
