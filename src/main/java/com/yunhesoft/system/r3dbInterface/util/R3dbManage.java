package com.yunhesoft.system.r3dbInterface.util;


import java.net.ConnectException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.r3dbInterface.entity.dto.TagDatas;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;

/**
 * TM4实时数据管理类（R3DB读取类） 2021.11.12
 * <AUTHOR>
 */
public class R3dbManage {

	/**
	 * 调试模式 1调试模式，0非调试模式
	 */
	private boolean isDebug = false;
	
	/**
	 * 服务地址,例如：http://127.0.0.1:8887/tm4main
	 */
	private String url = "";
    
    /**
     * 调用R3db服务接口的令牌,例如：994E90184E56C2160833B8BD7D7F25B3
     */
	private String token = "";
    
	public boolean isDebug() {
		return isDebug;
	}
	
	/**
	 * 设置调试模式
	 * @param isDebug true:调试模式 ，false:非调试模式
	 */
	public void setDebug(boolean isDebug) {
		this.isDebug = isDebug;
	}

	/**
	 * 构造函数
	 * @param isDebug true:调试模式 ，false:非调试模式
	 * @param url 服务地址
     * @param token 调用R3db服务接口的令牌
	 */
	public R3dbManage(boolean isDebug ,String url ,String token){
		if(url==null || "".equals(url.trim())){
			//(系统参数)R3db系统地址
			// TM4工况识别系统地址
			this.url = SpringUtils.getBean(ISysConfigService.class).getSysConfig("TM4OperativeModeUrl");
		}else {
			this.url =url;
		}
		
		if(token==null || "".equals(token.trim())){
			//(系统参数)R3db系统服务授权码
			this.token = SpringUtils.getBean(ISysConfigService.class).getSysConfig("TM4RtdbSerAuth");
		}else {
			this.token =token;
		}
		this.isDebug = isDebug;
	}
    
	/**
	 * 获取TM4实时数据服务
	 * @return
	 */
	public static R3dbManage getServer(){
		return R3dbManage.getServer(false,null,null);
	}
	
	/**
	 * 获取TM4实时数据服务
	 * @param isDebug true:调试模式 ，false:非调试模式
	 * @return
	 */
	public static R3dbManage getServer(boolean isDebug){
		return R3dbManage.getServer(isDebug,null,null);
	}
	
	/**
	 * 获取TM4实时数据服务
	 * @param isDebug true:调试模式 ，false:非调试模式
	 * @param url 服务地址
     * @param token 调用R3db服务接口的令牌
	 * @return
	 */
	public static R3dbManage getServer(boolean isDebug ,String url ,String token){
		R3dbManage R3dbObj = null;
		try{
			R3dbObj = new R3dbManage(isDebug,url,token);
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return R3dbObj;
	}
	
    /**
     * 调用 R3db 服务(单仪表)
     * @param url 地址
     * @param authId 令牌
     * @param tagCodes 仪表位号
     * @param stime 开始时间
     * @param etime 截止时间
     * @param rdbid 实时数据库(目前传空)
     * @return
     */
	private TagDatas callRtdbServer(boolean isDebug, String url,String authId,String tagCode,String stime,String etime,String rdbid){
    	List<String> tagCodes = new ArrayList<String>();
    	tagCodes.add(tagCode);
    	return this.callRtdbServer(isDebug, url, authId, tagCodes, stime, etime, rdbid);
    }
    
    /**
     * 调用 R3db 服务（多仪表）
     * @param url 地址
     * @param authId 令牌
     * @param tagCodes 仪表位号
     * @param stime 开始时间
     * @param etime 截止时间
     * @param rdbid 实时数据库(目前传空)
     * @return
     */
    private TagDatas callRtdbServer(boolean isDebug, String url,String authId,List<String> tagCodes,String stime,String etime,String rdbid){
    	
    	//创建返回值对象
    	TagDatas retObj = null;
    	
    	try{

			/**
			    接口文档
			  http://192.168.139.1:8887/tm4main/doc.html#/%E5%AE%9E%E6%97%B6%E6%95%B0%E6%8D%AE%E6%9C%8D%E5%8A%A1-%E6%A0%B8%E5%BF%83%E5%8C%85-api/Rtdb-Server-api/readTagsDatasUsingPOST
			
			    请求示例：
			  {
				  "authId": "", //读取数据认证标识(暂时未启用)
				  "stime": "", //读取数据的开始时间
				  "etime": "",//读取数据的结束时间
				  "rdbid": "",//实时数据来源(未来会弃用)
				  "tagCodes": []//读取的仪表位号列表
			  }
			 */
    		
    		// -- 固定参数 --------------------------------
			//创建JSON对象
			JSONObject jsonObj = new JSONObject();
			
			//初始化仪表位号数组
			jsonObj.put("tagCodes", JSONArray.toJSON(tagCodes));
			
			//初始化其它参数
			jsonObj.put("authId", authId);
			jsonObj.put("stime", stime);
			jsonObj.put("etime", etime);
			jsonObj.put("rdbid", rdbid==null?"":rdbid);
			
			// header
			//Map<String, String> header = new HashMap<String, String>();
			//header.put("token", token);
			
			//POST方式调用
			String info = R3dbManage.doPost(isDebug ,url, null, jsonObj);
			
			/**
			 * 相应数据：
			 {
				"code": 200,
				"message": "操作成功！",
				"result": [
					{
						"datas": [],
						"tagCode": "PING_PHDAPI"
					},
					{
						"datas": [],
						"tagCode": "PING_PHDAPI2"
					}
				],
				"success": true,
				"timestamp": 1630140568201,
				"total": 0
			}
			 */
			
			//解析返回的，JSON对象
			JSONObject jsonRetInfo = null;
			try{
				if(info!=null && !"".equals(info)){
					
			    	//初始化返回值对象
					retObj = new TagDatas(authId);
			    	
					//----------------------------------------------------
			    	
					jsonRetInfo = JSONObject.parseObject(info);
					if(jsonRetInfo!=null && jsonRetInfo.size()>0){
						String success = jsonRetInfo.getString("success");
						if(success!=null && "true".equals(success.trim().toLowerCase())){
							//请求数据成功
							String result = jsonRetInfo.getString("result");
							JSONArray jsonArrTags = JSONArray.parseArray(result);
							//遍历所有仪表
							if(jsonArrTags!=null && jsonArrTags.size()>0){
								for(int i=0,iCount=jsonArrTags.size();i<iCount;i++){
									try{
										JSONObject jsonObjTag = jsonArrTags.getJSONObject(i);
										String tagCodeStr = jsonObjTag.getString("tagCode");
										if(tagCodeStr!=null && !"".equals(tagCodeStr.trim())){
											JSONArray jsonObjDatas = jsonObjTag.getJSONArray("datas");
											//遍历所有仪表数据
											if(jsonObjDatas!=null && jsonObjDatas.size()>0){
												//遍历仪表数据
												for(int j=0,jCount=jsonObjDatas.size();j<jCount;j++){
													try{
														JSONObject jsonObjData =jsonObjDatas.getJSONObject(j);
														//时间戳
														//Date time = jsonObjData.containsKey("time")?jsonObjData.getTimestamp("time"):null;
														
														//数据时间(格式yyyy-MM-dd HH:mm:ss.SSS)与时间戳至少有一个有值
														String datetime = jsonObjData.containsKey("datetime")?jsonObjData.getString("datetime"):"";
														
														//质量戳 String : -1 仪表错误 0 = 不合格 50 = 一般合格 100 = 合格
														String quality = jsonObjData.containsKey("quality")?jsonObjData.getString("quality"):"";
														
														//错误编码
														String errcode = jsonObjData.containsKey("errcode")?jsonObjData.getString("errcode"):"";
														
														//错误描述
														String error = jsonObjData.containsKey("error")?jsonObjData.getString("error"):"";
														
														//数据值
														String value = jsonObjData.containsKey("value")?jsonObjData.getString("value"):"";

														//创建数据对象，仪表位号(仪表位号转成大写，方便后面比对)
														retObj.addData(tagCodeStr.trim().toUpperCase(), datetime, value, quality, errcode, error);
														
													}catch(Exception ex){
														System.out.println("【"+tagCodeStr.trim().toUpperCase()+"】当前仪表某条数据报错，不影响下一条数据的采集，错误信息如下：");
														ex.printStackTrace();
													}
												}
											}
										}
									}catch(Exception ex){
										System.out.println("【某块仪表报错】，不影响下一块仪表的采集，错误信息如下：");
										ex.printStackTrace();
									}
								}
							}
						}else{
							//请求数据失败
							System.out.println("调用实时数据库服务器错误。错误信息：" + info);
						}
					}
				}else{
					System.out.println("调用实时数据库服务器错误。错误信息：远程服务端无响应(链接超时等情况)。InterfaceManage.doPost(isDebug ,url, null, jsonObj) ，返回值为空。");
				}
			}catch(Exception ex){
				ex.printStackTrace();
			}
    	}catch(Exception ex){
    		ex.printStackTrace();
    	}
    	return retObj;
    }
    
    
    /**
	 * post请求
	 * @param url 地址
	 * @param header 头
	 * @param json 体
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public static String doPost(boolean DEBUG ,String url, Map<String, String> header, JSONObject json) {
		String jsonStr = "";
		if(json!=null){
			jsonStr = json.toString();
		}
		return R3dbManage.doPost(DEBUG, url, header, jsonStr);
	}
	
	
    /**
	 * post请求
	 * @param url 地址
	 * @param header 头
	 * @param json 体
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public static String doPost(boolean DEBUG ,String url, Map<String, String> header, String json) {
		//编码类型
		String ENCODE_TYPE = "utf-8";
		String result = null;
		DefaultHttpClient client = new DefaultHttpClient();

		try {

			if (DEBUG) {
				System.out.println();
				System.out.println("----------------------------------------");
				System.out.println("com.yunhe.tools.InterfaceManage.doPost()");
				System.out.println("----------------------------------------");
				System.out.println("url（通信地址）：" + url);
			}

			HttpPost post = new HttpPost(url);
			post.addHeader("Content-type", "application/json; charset="+ENCODE_TYPE);
			
			StringEntity s = new StringEntity(json,Charset.forName(ENCODE_TYPE));
			s.setContentEncoding(ENCODE_TYPE);
			s.setContentType("application/json"); // 发送json数据需要设置contentType

			// header
			if (header != null && header.size() > 0) {
				Set<String> headerKeys = header.keySet();

				if (DEBUG) {
					System.out.println();
					System.out.println("header：");
				}

				for (String hK : headerKeys) {
					post.addHeader(hK, header.get(hK));

					if (DEBUG) {
						System.out.println();
						System.out.println("key：" + hK);
						System.out.println("value：" + header.get(hK));
					}
				}
			}

			if (DEBUG) {
				System.out.println();
				System.out.println("请求字符串：" +json);
				System.out.println();
				System.out.println("json（body）：" + s);
			}

			// json body
			post.setEntity(s);

			// request
			HttpResponse res = client.execute(post);
			if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				// HttpEntity entity = res.getEntity();
				result = EntityUtils.toString(res.getEntity());// 返回json格式：
				// response = JSONObject.fromObject(result);
				
				if (DEBUG) {
					System.out.println();
					System.out.println("result（应答）：" + result);
				}
			} else {
				// result = "POST通信状态码：" + res.getStatusLine().getStatusCode() + "\n";
				// result += "应答信息：" + EntityUtils.toString(res.getEntity());
				
				System.out.println("\n【错误信息】【通信】【" + DateTimeUtils.getNowDateTimeStr() + "】：\n状态码：" + res.getStatusLine().getStatusCode() + "\n" + "应答信息：" + EntityUtils.toString(res.getEntity()));
			}
			
			client.close();
		}catch(ConnectException e){
			e.printStackTrace();
			client.close();
		}catch (Exception e) {
			e.printStackTrace();
			client.close();
		}
		
		return result;
	}

	 /**
     * 获取实时数据
     * 
     * @param tagCode 仪表位号
     * @param stime 开始日期时间 2021-01-01 00:00:00
     * @param etime 结束日期时间 2021-01-02 00:00:00
     * @return
     */
    public TagDatas getData(List<String> tagCodes ,Date stime ,Date etime){
    	return getData(tagCodes ,stime ,etime,null);
    }
	 /**
     * 获取实时数据
     * 
     * @param tagCode 仪表位号
     * @param stime 开始日期时间 2021-01-01 00:00:00
     * @param etime 结束日期时间 2021-01-02 00:00:00
     * @return
     */
    public TagDatas getData(List<String> tagCodes ,Date stime ,Date etime,String path){
    	TagDatas tagDatas = null;
    	try{
    		if(url==null || "".equals(url.trim())){
    			System.out.println("(系统参数)R3db Url服务地址为null");
    			return tagDatas;
    		}else{
    			url = url.trim();
    		}
    		
    		if(token==null || "".equals(token.trim())){
    			System.out.println("(系统参数)R3db系统服务授权码为null");
    			return tagDatas;
    		}else{
    			token = token.trim();
    		}
    		
    		//接口地址
//    		String path = "rtdb-api/v1/read-tagdata-list";
    		if(path==null||"".equals(path)) {
    			path = "rtdb-api/v1/read-tagdata-list";
    		}
			//生成调用地址
			String sendAdd = url;
			if(!"/".equals(url.substring(url.length()-1))){
				sendAdd += "/";
			}
			sendAdd += path;
			
			//当前时间（结束时间）
			String stimeStr = DateTimeUtils.formatDateTime(stime);
			
			//（开始时间）
			String etimeStr = DateTimeUtils.formatDateTime(etime);
			
			//调用 R3db 服务 获取数据(多仪表调用)
//			List<String> tagCodes = new ArrayList<String>();
//	    	tagCodes.add("PING_PHDAPI");
//	    	tagCodes.add("PING_PHDDA");
			tagDatas = this.callRtdbServer(this.isDebug, sendAdd, this.token, tagCodes, stimeStr, etimeStr, null);
			
    	}catch(Exception ex){
    		ex.printStackTrace();
    	}
    	return tagDatas;
    }
    
    
    /**
     * 写入实时数据库服务(单条数据)
     * @param tagCode 仪表位号
     * @param datetime 时间，传入null为当前时间
     * @param value 值
     * @param quality 信任值
     * @param errcode 错误编码
     * @param error 错误内容
     * @return
     */
    public String setData(String tagCode, String datetime, String value, String quality, String errcode, String error){
        String retValue = null;
        try{
    		if(url==null || "".equals(url.trim())){
    			System.out.println("(系统参数)R3db Url服务地址为null");
    			return retValue;
    		}else{
    			url = url.trim();
    		}
    		
    		if(token==null || "".equals(token.trim())){
    			System.out.println("(系统参数)R3db系统服务授权码为null");
    			return retValue;
    		}else{
    			token = token.trim();
    		}

            if (url != null && !"".equals(url.trim())){
                //API地址
                String path = "rtdb-api/v1/post-tagdata-batch";
                
    			//生成调用地址
    			String sendAdd = url;
    			if(!"/".equals(url.substring(url.length()-1))){
    				sendAdd += "/";
    			}
    			sendAdd += path;

                //创建实时数据管理对象
                TagDatas tagDatas = new TagDatas(token);
                //if (datetime == null || "".Equals(datetime.Trim())) { datetime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"); }
                //创建仪表数据
                tagDatas.addData(tagCode, datetime, value, quality, errcode, error);
                //生成JSON数据
                String jsonData = tagDatas.toJsonData();
                
    			//POST方式调用
                R3dbManage.doPost(isDebug ,sendAdd, null, jsonData);
    			
            }else{
                System.out.println("调用写入实时数据库服务（R3dbManage.setData）出错：传入的url地址为空。");
            }
        }catch (Exception ex){
        	ex.printStackTrace();
        }
        return retValue;
    }
    
    /**
     * 调试入口
     * @param args
     */
//    public static void main(String[] args){
//    	try {
//	    	R3dbManage rdbSer = R3dbManage.getServer(true, "http://192.168.0.32:6670/tm4main", "994E90184E56C2160833B8BD7D7F25B3");
//			List<String> tagCodes = new ArrayList<String>();
//	    	tagCodes.add("PEU1_64SFK0921.PV");
//	    	tagCodes.add("PEU1_64SFK0922.PV");
//	    	tagCodes.add("PEU1_64SFK0301.PV");
//	    	tagCodes.add("PEU1_64SFK0302.PV");
//	    	String kssj="2022-09-30 08:00:00";
//	    	String jzsj="2022-09-30 08:30:00";
//	    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//	    	Date stimeDt =sdf.parse(kssj);
//	    	Date etimeDt =sdf.parse(jzsj);
//	    	TagDatas tagDatas = rdbSer.getData(tagCodes, stimeDt, etimeDt);
//	    	
//	    	System.out.println(JSONArray.toJSONString(tagDatas));
//	    } catch (Exception e) {
//			e.printStackTrace();
//		}
//    }
    
}
