package com.yunhesoft.system.simpleFlow.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * 自定义流程步骤表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "CUSTOM_FLOW_TYPE")
public class CustomFlowType extends BaseEntity {
	
    private static final long serialVersionUID = 1L;

    /** 流程模块id */
    @Column(name="MODULE_CODE", length=50)
    private String moduleCode;
    
    /** 类型编码 */
    @Column(name = "FLOW_TYPE_CODE", length = 200)
    private String flowTypeCode;
    
    /** 类型名称 */
    @Column(name="FLOW_TYPE_NAME", length=200)
    private String flowTypeName;

    /** pc待办组件地址 */
    @Column(name="WEB_TODO_INFO_URL", length=100)
    private String webTodoInfoUrl;

    /** 手机端待办组件地址 */
    @Column(name="APP_TODO_INFO_URL", length=100)
    private String appTodoInfoUrl;

    @ApiModelProperty(value = "不可否决标识，1为不能否决处理")
    @Column(name="NO_VOTE")
	private Integer noVote;
//    /** TMSORT */
//    @Column(name="CUSTOMTYPE")
//    private Integer customType;
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    

}

