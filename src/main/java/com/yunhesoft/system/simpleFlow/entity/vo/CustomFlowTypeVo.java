package com.yunhesoft.system.simpleFlow.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CustomFlowTypeVo {
	
	@ApiModelProperty(value = "类型编码")
	private String flowTypeCode;
	
	@ApiModelProperty(value = "类型名称")
	private String flowTypeName;
	
	@ApiModelProperty(value = "电脑端跳转地址")
	private String webTodoInfoUrl;
	
	@ApiModelProperty(value = "手机端跳转地址")
	private String appTodoInfoUrl;
	
	@ApiModelProperty(value = "不可否决标识，1为不能否决处理")
	private int noVote = 0;
	
}
