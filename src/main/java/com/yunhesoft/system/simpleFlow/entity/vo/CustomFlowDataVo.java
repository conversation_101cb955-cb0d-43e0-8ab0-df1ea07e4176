package com.yunhesoft.system.simpleFlow.entity.vo;

import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowData;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowDataAcc;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class CustomFlowDataVo extends CustomFlowData {

	/** 流程模块 */
    private String flowModuleName;
    
    /** 功能 */
    private String flowFunName;
    private String name;
	List<CustomFlowStepVo> stepList;//步骤设置数据
	List<CustomFlowDataAcc> degreeList;//处理数据
	List<CustomFlowStepVo> stepDataList;//流程数据步骤

	private Boolean applyPc = false;//pc应用
	private String urlPc;//pc应用地址
	private Boolean applyApp = false;//移动端可应用
	private String urlApp;//移动端可应用
	
	private Boolean afterDegreeResult=true;//审核后操作结果
	private String errorInfo;//审核后操作结果错误信息（批量审核使用，或审核失败（afterDegreeFlow操作导致的（包括库存不足，逻辑不通等）））
	
	
	private Boolean flowDegreeResult = true;//流程处理结果
	private String flowErrorInfo;//流程处理错误信息
}
