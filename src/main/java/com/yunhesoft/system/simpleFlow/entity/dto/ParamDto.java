package com.yunhesoft.system.simpleFlow.entity.dto;

import java.util.List;

import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowDataVo;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowStepVo;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ParamDto {
	
	private String id;//数据ID
	private String userId;//穿纳入的用户ID
	
	private String degreeMark;//处理标识 1通过 -1否决
	private String degreeDesc;//处理信息
	
	private String idstr;
	
	private String moduleCode;//模块编码
	
	private String funCode;//功能编码
	
	private String funName;//功能编码
	
	private String flowTypeCode;//类型编码
	
	private Integer customType;//是否为开放式工作流 1为开放式工作流 开放式工作流给id如果未查到流程设置，则会自动创建
	
	/** 业务名称 */
	private String businessDataName;
	/** 业务编码 */
	private String businessCode;
	/** 业务实例编码 */
	private String businessDataCode;
	/** 自定义人员获取编码 */
	private String customUserCode;
	/** 流程名称 */
	private String flowName;
	
	/** 流程模板列表 */
	private List<CustomFlowDataVo> tplList;
	
	/** 流程步骤列表 */
	private List<CustomFlowStepVo> tplStepList;

	/**
	 * 开始日期
	 */
	private String startDate;
	/**
	 * 截止日期
	 */
	private String endDate;
}
