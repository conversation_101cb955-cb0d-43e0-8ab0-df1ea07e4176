package com.yunhesoft.system.simpleFlow.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CustomUserVo{
	@ApiModelProperty(value = "对象类型 1人员 2岗位")
	private Integer type;
	@ApiModelProperty(value = "岗位机构代码")
	private String postOrgCode;
	@ApiModelProperty(value = "岗位ID")
	private String postId;
	@ApiModelProperty(value = "岗位名称")
	private String postName;
	@ApiModelProperty(value = "人员ID")
	private String userId;
	@ApiModelProperty(value = "人员姓名")
	private String userName;
}
