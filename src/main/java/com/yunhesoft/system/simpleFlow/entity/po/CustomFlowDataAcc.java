package com.yunhesoft.system.simpleFlow.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 自定义流程数据处理表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "CUSTOM_FLOW_DATA_ACC")
public class CustomFlowDataAcc extends BaseEntity {
	
    private static final long serialVersionUID = 1L;

    /** 流程模块id */
    @Column(name="FLOW_MODULE_CODE", length=50)
    private String flowModuleCode;
    
    /** 功能id */
    @Column(name="FLOW_FUN_CODE", length=50)
    private String flowFunCode;

    /** 功能名称 */
    @Column(name="FLOW_FUN_NAME", length=100)
    private String flowFunName;

    /** 类型编码 */
    @Column(name = "FLOW_TYPE_CODE", length = 200)
    private String flowTypeCode;
    
    /** 业务编码 */
    @Column(name="BUSINESS_CODE", length=200)
    private String businessCode;
    
    /** 流程数据ID */
    @Column(name="FLOW_DATA_ID", length=50)
    private String flowDataId;
    
    /** 流程ID */
    @Column(name="FLOW_ID", length=50)
    private String flowId;
    
    /** 步骤ID */
    @Column(name="STEP_ID", length=50)
    private String stepId;
    
    /** 步骤顺序号 */
    @Column(name="STEP_NO", length=50)
    private Integer stepNo;
    
    /** 当前应用状态1应用0不用 */
    @Column(name="CURR_MARK")
    private Integer currMark;
    
    /** 处理标识1处理0未处理 */
    @Column(name="DEGREE_MARK")
    private Integer degreeMark;

    /** 处理结果1通过0未处理-1否决*/
    @Column(name="DEGREE_RESULT")
    private Integer degreeResult;
    
    /** 处理时间 */
    @Column(name="DEGREE_TIME")
    private Date degreeTime;
    
    /** 处理人名 */
    @Column(name="DEGREE_USER_NAME")
    private String degreeUserName;

    /** 处理人id */
    @Column(name="DEGREE_USER_ID")
    private String degreeUserId;
    
    /** 接收来源ID，表CUSTOM_FLOW_TPL_STEP_ACC */
    @Column(name="ACC_SOURCE_ID", length=50)
    private String accSourceId;
    
    /** 接收类型1人2岗位9自定义 */
    @Column(name="ACC_TYPE")
    private Integer accType;
    
    /** 接收代码 */
    @Column(name="ACC_CODE", length=50)
    private String accCode;
    
    /** 接收名称 */
    @Column(name="ACC_NAME", length=500)
    private String accName;
    
    /** 机构代码 */
    @Column(name="ORG_CODE", length=50)
    private String orgCode;
    
    /** 岗位代码 */
    @Column(name="JOB_CODE", length=50)
    private String jobCode;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}
