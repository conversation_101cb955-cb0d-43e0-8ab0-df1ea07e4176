package com.yunhesoft.system.simpleFlow.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 自定义流程步骤表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "CUSTOM_FLOW_MODULE")
public class CustomFlowMondule extends BaseEntity {
	

    /** 模块编码 */
    @Column(name="MODULE_CODE", length=50)
    private String moduleCode;

    /** 模块名称 */
    @Column(name="MODULE_NAME", length=50)
    private String moduleName;

    /** 待办图标 */
    @Column(name="TODO_ICON", length=50)
    private String todoIcon;

    /** 待办名称 */
    @Column(name="TODO_NAME", length=50)
    private String todoName;

    /** 回调函数api地址 */
    @Column(name="CALLBACK_API", length=100)
    private String callbackApi;

    /** 系统地址 */
    @Column(name="SYS_ADDRESS", length=100)
    private String sysAddress;

    /** 待办页面tbar栏扩展按钮组件路径 */
    @Column(name="TODO_TBAR_EXT_BTN_URL", length=100)
    private String todoTbarExtBtnUrl;

    /**
     * 微服务名
     */
    @Column(name="SERVICE_NAME", length=100)
    private String serviceName;

}

