package com.yunhesoft.system.simpleFlow.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * 自定义流程数据表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "CUSTOM_FLOW_DATA")
public class CustomFlowData extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 流程模块id */
    @Column(name="FLOW_MODULE_CODE", length=50)
    private String flowModuleCode;
    
    /** 功能id */
    @Column(name="FLOW_FUN_CODE", length=50)
    private String flowFunCode;

    /** 功能名称 */
    @Column(name="FLOW_FUN_NAME", length=100)
    private String flowFunName;

    /** 类型编码 */
    @Column(name = "FLOW_TYPE_CODE", length = 200)
    private String flowTypeCode;
    
    /** 原业务编码 */
    @Column(name="SOURCE_BUSINESS_CODE", length=200)
    private String sourceBusinessCode;
    
    /** 业务编码 */
    @Column(name="BUSINESS_CODE", length=200)
    private String businessCode;
    
	/** 自定义人员获取编码 */
    @Column(name="CUSTOM_USER_CODE", length=1000)
	private String customUserCode;
    
    
    /** 业务数据编码 */
    @Column(name="BUSINESS_DATA_CODE", length=4000)
    private String businessDataCode;
    
    /** 业务数据名称 */
    @Column(name="BUSINESS_DATA_NAME", length=4000)
    private String businessDataName;
    
    /** 当前步骤 */
    @Column(name="CURR_STEP_ID", length=50)
    private String currStepId;
    
    /** 运行状态0开始1进行中2完成-1否决-2销项 -3发起失败退回删除*/
    @Column(name="RUN_STATUS")
    private Integer runStatus;
    
    /** 否决信息 */
    @Column(name="BACK_DESC", length=2000)
    private String backDesc;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    
    @Transient
    private Boolean noVote;
    

}
