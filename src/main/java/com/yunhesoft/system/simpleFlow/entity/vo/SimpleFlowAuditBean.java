package com.yunhesoft.system.simpleFlow.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class SimpleFlowAuditBean{
	//以下为调用审核流程的输入参数
//	@ApiModelProperty(value = "模块编码")
//	private String moduleCode;
//	@ApiModelProperty(value = "功能编码")
//	private String funCode;
//	@ApiModelProperty(value = "业务编码(比如机构代)")
//	private String businessCode;
//	@ApiModelProperty(value = "数据编码 对应记录的数据")
//	private String businessDataCode;
	@ApiModelProperty(value = "流程实例ID")
	private String flowId;
	@ApiModelProperty(value = "审核状态 1通过 -1否决")
	private String auditStatus;
	@ApiModelProperty(value = "审核描述")
	private String auditDesc;
//	//以下为审核结果
//	@ApiModelProperty(value = "0、待审核； 1、审核中； 2、审核通过； -1、审核否决；")
//	private Integer auditResult;
}
