package com.yunhesoft.system.simpleFlow.entity.vo;

import java.util.List;

import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowStep;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowStepAcc;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CustomFlowStepVo extends CustomFlowStep {

	List<CustomFlowStepAcc> accList;
	
	Boolean active;//活动状态 true当前节点
	Integer degreeStatus;//处理状态1已处理0未处理
	Integer degreeResult;//处理结果 1通过 -1否决
	String degreeUserName;//处理人
	String degreeTime;//处理时间
	String toDegree;//待处理人
}
