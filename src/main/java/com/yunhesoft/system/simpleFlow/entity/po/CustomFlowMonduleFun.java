package com.yunhesoft.system.simpleFlow.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 自定义流程步骤表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "CUSTOM_FLOW_MODULE_FUN")
public class CustomFlowMonduleFun extends BaseEntity {
	

    /** 模块编码 */
    @Column(name="MODULE_CODE", length=50)
    private String moduleCode;

    /** 业务编码(机构代码或其他) */
    @Column(name="BUSINESS_CODE", length=200)
    private String businessCode;
    
    /** 类型编码 */
    @Column(name = "FLOW_TYPE_CODE", length = 200)
    private String flowTypeCode;
    
    /** 名称 */
    @Column(name="FUN_NAME", length=200)
    private String funName;

    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    /** 开放式工作流 1=开放式 */
 	@Column(name="CUSTOMTYPE")
  	private Integer customType;

}

