package com.yunhesoft.system.simpleFlow.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CustomApplicationVo{
	
	@ApiModelProperty(value = "应用代码")
	private String applicationCode;
	
	@ApiModelProperty(value = "应用名称")
	private String applicationName;
	
	@ApiModelProperty(value = "电脑端跳转地址")
	private String todoUrl;
	
	@ApiModelProperty(value = "手机端跳转地址")
	private String appUrl;
	
	@ApiModelProperty(value = "不可否决标识，true为不能否决处理")
	private Boolean noVote;
	
}
