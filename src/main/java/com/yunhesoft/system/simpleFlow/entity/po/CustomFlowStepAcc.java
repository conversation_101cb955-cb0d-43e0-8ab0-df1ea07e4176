package com.yunhesoft.system.simpleFlow.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 自定义流程步骤接收表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "CUSTOM_FLOW_STEP_ACC")
public class CustomFlowStepAcc extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 流程模块id */
    @Column(name="FLOW_MODULE_CODE", length=50)
    private String flowModuleCode;
    
    /** 功能id */
    @Column(name="FLOW_FUN_CODE", length=50)
    private String flowFunCode;

    /** 类型编码 */
    @Column(name = "FLOW_TYPE_CODE", length = 200)
    private String flowTypeCode;
    
    /** 业务编码 */
    @Column(name="BUSINESS_CODE", length=200)
    private String businessCode;
    
    /** 步骤顺序 */
    @Column(name="STEP_ID", length=50)
    private String stepId;
    
    /** 接收类型1人2岗位9自定义 */
    @Column(name="ACC_TYPE")
    private Integer accType;
    
    /** 接收代码 */
    @Column(name="ACC_CODE", length=50)
    private String accCode;
    
    /** 接收名称 */
    @Column(name="ACC_NAME", length=500)
    private String accName;
    
    /** 机构代码 */
    @Column(name="ORG_CODE", length=50)
    private String orgCode;
    
    /** 岗位代码 */
    @Column(name="JOB_CODE", length=50)
    private String jobCode;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}
