package com.yunhesoft.system.simpleFlow.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 自定义流程步骤表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "CUSTOM_FLOW_STEP")
public class CustomFlowStep extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 流程模块id */
    @Column(name="FLOW_MODULE_CODE", length=50)
    private String flowModuleCode;
    
    /** 功能id */
    @Column(name="FLOW_FUN_CODE", length=50)
    private String flowFunCode;

    /** 类型编码 */
    @Column(name = "FLOW_TYPE_CODE", length = 200)
    private String flowTypeCode;
    
    /** 业务编码(机构代码或其他) */
    @Column(name="BUSINESS_CODE", length=200)
    private String businessCode;
    
    /** 步骤顺序 */
    @Column(name="STEP_NO")
    private Integer stepNo;
    
    /** 步骤名称 */
    @Column(name="STEP_NAME", length=100)
    private String stepName;
    
    /** 步骤类型编码 */
    @Column(name="STEP_TYPE", length=50)
    private String stepType;
    
    /** 接收类型1人2岗位9自定义 */
    @Column(name="ACC_TYPE")
    private Integer accType;
    
    /** 接收信息逗号分割 */
    @Column(name="ACC_SHOW", length=2000)
    private String accShow;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;


}

