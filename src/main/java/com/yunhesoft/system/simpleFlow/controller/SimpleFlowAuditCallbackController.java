package com.yunhesoft.system.simpleFlow.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowDataVo;
import com.yunhesoft.system.simpleFlow.service.impl.SimpleFlowAuditCallbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "简单流程")
@RestController
@RequestMapping("/system/simpleFlow/callback")
public class SimpleFlowAuditCallbackController {

    @Autowired
    private SimpleFlowAuditCallbackService srv;

    @RequestMapping(value = "/auditCallback", method = RequestMethod.POST)
    @ApiOperation("审核后回调函数")
	public Res<Boolean> auditCallbackApi(@RequestBody CustomFlowDataVo vo) {
        return Res.OK(srv.auditCallback(vo));
    }

}
