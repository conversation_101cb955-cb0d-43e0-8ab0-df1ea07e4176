package com.yunhesoft.system.simpleFlow.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.simpleFlow.entity.dto.BatchAuditDto;
import com.yunhesoft.system.simpleFlow.entity.dto.ParamDto;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowMonduleFun;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowDataVo;
import com.yunhesoft.system.simpleFlow.service.ISimpleFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 简单流程Controller
 * 
 * <AUTHOR> 2024-5-15
 */
@Log4j2
@Api(tags = "简单流程")
@RestController
@RequestMapping("/system/simpleFlow")
public class SimpleFlowServiceController {

	/**
	 * 简单流程服务对象
	 */
	@Autowired
	private ISimpleFlowService sfsSrv;



	/**
	 * @category 获取模块
	 * @param
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/queryFlowMonduleByModuleCode", method = { RequestMethod.POST })
	@ApiOperation(value = "获取模块")
	public Res<?> queryFlowMonduleByModuleCode(@RequestBody ParamDto param) {
		return Res.OK(sfsSrv.queryFlowMonduleByModuleCode(param.getModuleCode()));
	}

	/**
	 * @category 获取模块功能列表
	 * @param
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getModuleFunList", method = { RequestMethod.POST })
	@ApiOperation(value = "获取模块功能列表")
	public Res getModuleFunList(@RequestBody ParamDto param) {
		String moduleCode = param.getModuleCode();
		return Res.OK(sfsSrv.getModuleFunList(moduleCode, param.getBusinessCode(), param.getFlowTypeCode()));
	}

	@ResponseBody
	@RequestMapping(value = "/saveFlowModuleFunList", method = { RequestMethod.POST })
	@ApiOperation(value = "保存功能")
	public Res<?> saveFlowModuleFunList(@RequestBody List<CustomFlowMonduleFun> list) {
		return Res.OK(sfsSrv.saveFlowModuleFunList(list));
	}

	@ResponseBody
	@RequestMapping(value = "/deleteFlowModuleFunList", method = { RequestMethod.POST })
	@ApiOperation(value = "删除功能")
	public Res<?> deleteFlowModuleFunList(@RequestBody List<CustomFlowMonduleFun> list) {
		return Res.OK(sfsSrv.deleteFlowModuleFunList(list));
	}

	@ResponseBody
	@RequestMapping(value = "/getFlowTypeList", method = { RequestMethod.POST })
	@ApiOperation(value = "获取类型列表")
	public Res getFlowTypeList(@RequestBody ParamDto param) {
		String moduleCode = param.getModuleCode();
		return Res.OK(sfsSrv.getFlowTypeList(moduleCode));
	}

	/**
	 * @category 获取流程模板信息
	 * @param
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getFlowStepList", method = { RequestMethod.POST })
	@ApiOperation(value = "获取流程模板步骤列表")
	public Res getFlowStepList(@RequestBody ParamDto param) {
		return Res.OK(sfsSrv.getFlowStepList(param));
	}

	/**
	 * @category 保存流程模板步骤
	 * @param
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/saveFlowStep", method = { RequestMethod.POST })
	@ApiOperation(value = "保存流程模板步骤")
	public Res<Boolean> saveFlowStep(@RequestBody ParamDto param) {
		Res<Boolean> res = new Res<>();
		Boolean rv = sfsSrv.saveFlowStep(param);
		res.setResult(rv);
		return res;
	}

	/**
	 * @category 删除流程模板步骤
	 * @param
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/deleteFlowStep", method = { RequestMethod.POST })
	@ApiOperation(value = "删除流程模板步骤")
	public Res<Boolean> deleteFlowStep(@RequestBody ParamDto param) {
		return Res.OK(sfsSrv.delFlowTplStep(param));
	}

	
	/**
	 * @category 获取流程模板信息
	 * @param 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getZoneList", method = { RequestMethod.POST })
	@ApiOperation(value = "获取区域经理列表")
	public Res<List<Map<String, String>>> getZoneList(@RequestBody ParamDto param) {
		Res<List<Map<String, String>>> res = new Res<List<Map<String, String>>>();
		List<Map<String, String>> rlist = sfsSrv.getZoneList(param);
		res.setResult(rlist);
		return res;
	}
	



	/**
	 * @category 启动流程
	 * @param 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/startFlow", method = { RequestMethod.POST })
	@ApiOperation(value = "启动流程")
	public Res<CustomFlowDataVo> startFlow(@RequestBody ParamDto param) {
		Res<CustomFlowDataVo> res = new Res<>();
		CustomFlowDataVo rv = sfsSrv.startFlow(param);
		res.setResult(rv);
		return res;
	}
	
	/**
	 * @category 处理流程
	 * @param 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/degreeFlowStep", method = { RequestMethod.POST })
	@ApiOperation(value = "处理流程")
	public Res<List<CustomFlowDataVo>> degreeFlowStep(@RequestBody ParamDto param) {
		Res<List<CustomFlowDataVo>> res = new Res<List<CustomFlowDataVo>>();
		List<CustomFlowDataVo> rv = sfsSrv.degreeFlowStep(param);
		res.setResult(rv);
		return res;
	}
	/**
	 * @category 流程返回上一步
	 * @param 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/toUpStepFlow", method = { RequestMethod.POST })
	@ApiOperation(value = "流程返回上一步")
	public Res<Integer> toUpStepFlow(@RequestBody ParamDto param) {
		Res<Integer> res = new Res<Integer>();
		Integer rv = sfsSrv.toUpStepFlow(param.getId());
		res.setResult(rv);
		return res;
	}
	/**
	 * @category 删除流程
	 * @param 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/delFlow", method = { RequestMethod.POST })
	@ApiOperation(value = "删除流程")
	public Res<List<CustomFlowDataVo>> delFlow(@RequestBody ParamDto param) {
		Res<List<CustomFlowDataVo>> res = new Res<List<CustomFlowDataVo>>();
		List<CustomFlowDataVo> rv = sfsSrv.delFlow(param);
		res.setResult(rv);
		return res;
	}
	
	/**
	 * @category 获取人员流程待办数
	 * @param 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getFlowTodo", method = { RequestMethod.POST })
	@ApiOperation(value = "获取人员流程待办数，按功能模块区分")
	public Res<Integer> getFlowTodo(@RequestBody ParamDto param) {
		Res<Integer> res = new Res<Integer>();
		String userId = null;
		SysUser sysUser = SysUserUtil.getCurrentUser();
		if(sysUser!=null) {
			userId = sysUser.getId();
		}
		String moduleCode = param.getModuleCode();
		Integer rv = sfsSrv.getFlowTodo(userId, moduleCode);
		res.setResult(rv);
		return res;
	}
	
	/**
	 * @category 获取人员流程待办列表
	 * @param 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getFlowTodoList", method = { RequestMethod.POST })
	@ApiOperation(value = "获取人员流程待办列表，按功能模块区分")
	public Res<List<CustomFlowDataVo>> getFlowTodoList(@RequestBody ParamDto param) {
		Res<List<CustomFlowDataVo>> res = new Res<List<CustomFlowDataVo>>();
		String userId = null;
		SysUser sysUser = SysUserUtil.getCurrentUser();
		if(sysUser!=null) {
			userId = sysUser.getId();
		}
		String moduleCode = param.getModuleCode();
		String funCode = param.getFunCode();
		List<CustomFlowDataVo> rv = sfsSrv.getFlowTodoList(userId, moduleCode, funCode);
		res.setResult(rv);
		return res;
	}
	

	/**
	 * @category 根据参数加载执行流程信息
	 * @param 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/loadFlowDataInfo", method = { RequestMethod.POST })
	@ApiOperation(value = "根据参数加载执行流程信息")
	public Res<CustomFlowDataVo> loadFlowDataInfo(@RequestBody ParamDto param) {
		Res<CustomFlowDataVo> res = new Res<CustomFlowDataVo>();
		CustomFlowDataVo rv = sfsSrv.loadFlowDataInfo(param);
		res.setResult(rv);
		return res;
	}

	@RequestMapping(value = "/auditFlowBatch", method = RequestMethod.POST)
    @ApiOperation("批量审核")
    public Res<?> auditFlowBatch(@RequestBody BatchAuditDto auditDto) {
        return Res.OK(sfsSrv.auditFlowBatch(auditDto.getAuditList(), auditDto.getModuleCode()));
    }


	@RequestMapping(value = "/queryMyDegreeFlowDataList", method = RequestMethod.POST)
    @ApiOperation("查询我参与处理的流程实例数据")
    public Res<?> queryMyDegreeFlowDataList(@RequestBody ParamDto param) {
        return Res.OK(sfsSrv.queryMyDegreeFlowDataList(param.getStartDate(), param.getEndDate()));
    }


//	/**
//	 * 审核后回调
//	 * @param vo
//	 * @return
//	 */
//	@RequestMapping(value = "/auditCallbackApi", method = RequestMethod.POST)
//    @ApiOperation("auditCallbackApi")
//    public Res<?> auditCallbackApi(@RequestBody CustomFlowDataVo vo) {
//		String flowModuleCode = vo.getFlowModuleCode();
//		SimpleFlowModel modelInstance = flowFactory.getModelInstance(flowModuleCode);
//		return Res.OK(modelInstance.afterDegreeFlow(vo));
//    }

}
