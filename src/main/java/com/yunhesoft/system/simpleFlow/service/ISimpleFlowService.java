package com.yunhesoft.system.simpleFlow.service;

import java.util.List;
import java.util.Map;

import com.yunhesoft.system.simpleFlow.entity.dto.ParamDto;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowData;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowMondule;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowMonduleFun;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowType;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowDataVo;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowStepVo;
import com.yunhesoft.system.simpleFlow.entity.vo.SimpleFlowAuditBean;

/**
 * @category 简单流程信息服务
 * 
 * <AUTHOR> 2024-5-15
 */
public interface ISimpleFlowService {

	CustomFlowMondule queryFlowMonduleByModuleCode (String moduleCode);

	String saveFlowModuleFunList (List<CustomFlowMonduleFun> list);

	String deleteFlowModuleFunList (List<CustomFlowMonduleFun> list);
	
	/**
	 * 获取开方式工作流(没有时会自动创建)
	 * @category 
	 * <AUTHOR> 
	 * @param funData
	 * @return
	 */
	void createCustomFlowModuleFun (CustomFlowMonduleFun funData);
	/**
	 * @category 获取区域经理列表（设置用）
	 * @param param
	 * @return
	 */
	public List<Map<String, String>> getZoneList(ParamDto param);
	/**
	 * @category 获取流程模板步骤列表
	 * @param param param.tplId
	 * @return
	 */
	public List<CustomFlowStepVo> getFlowStepList(ParamDto param);
	
	/**
	 * @category 保存流程模板步骤
	 * @param param
	 * @return
	 */
	Boolean saveFlowStep(ParamDto param);
	
	/**
	 * @category 删除流程模板步骤
	 * @param param
	 * @return
	 */
	public boolean delFlowTplStep(ParamDto param);
	
	/**
	 * @category 启动流程
	 * @param param
	 * @return
	 */
	public CustomFlowDataVo startFlow(ParamDto param);

	List<CustomFlowDataVo> auditFlowBatch(List<SimpleFlowAuditBean> auditList, String moduleCode);

	/**
	 * @category 处理流程
	 * @param param
	 * @return
	 */
	public List<CustomFlowDataVo> degreeFlowStep(ParamDto param);
	
	/**
	 * @category 流程返回上一步（用于处理流程后业务处理出错后，流程数据还原）
	 * @param param
	 * @return 1成功 0还原失败
	 */
	public Integer toUpStepFlow(String flowId);
	public Integer toUpStepFlow(List<String> flowIdList);
	
	/**
	 * @category 撤回流程
	 * @param param
	 * @return 1成功 0已进行无法撤回 -1程序出错未撤回
	 */
	public Integer backFlow(String flowId);
	
	/**
	 * @category 清理流程
	 * @param param
	 * @return
	 */
	public List<CustomFlowDataVo> delFlow(ParamDto param);

	/**
	 * @category 根据业务数据id清除流程数据
	 * @param param
	 * @return
	 */
	String clearFlowDataByBusinessDataCode(List<String> businessDataCodeList);

	/**
	 * @category 获取人员流程待办数
	 * @return
	 */
	public Integer getFlowTodo(String moduleCode);
	public Integer getFlowTodo(String userId, String moduleCode);
	
	/**
	 * @category 获取人员流程待办列表
	 * @param userId
	 * @return
	 */
	public List<CustomFlowDataVo> getFlowTodoList(String moduleCode);
	public List<CustomFlowDataVo> getFlowTodoList(String userId, String moduleCode, String funCode);

	/**
	 * @category 获取模块功能列表
	 * @return
	 */
	List<CustomFlowMonduleFun> getModuleFunList(String moduleCode, String businessCode);

	List<CustomFlowMonduleFun> getModuleFunList(String moduleCode, String businessCode, String flowTypeCode);
	/**
	 * @category 获取类型列表
	 * @return
	 */
	List<CustomFlowType> getFlowTypeList(String moduleCode);

	/**
	 * @category 根据参数加载执行流程信息
	 * @param param id or businessDataCode
	 * @return
	 */
	public CustomFlowDataVo loadFlowDataInfo(ParamDto param);

	CustomFlowDataVo degreeFlow(SimpleFlowAuditBean param);

	/**
	 * 查询我参与的流程实例
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	List<CustomFlowData> queryMyDegreeFlowDataList (String startDate, String endDate);
}
