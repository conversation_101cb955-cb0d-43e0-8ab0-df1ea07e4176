package com.yunhesoft.system.simpleFlow.service.impl;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowDataVo;
import com.yunhesoft.system.simpleFlow.util.SimpleFlowModel;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 *  @category 简单流程类
 * 
 * <AUTHOR> 2024-5-15
 */
@Log4j2
@Service
public class SimpleFlowAuditCallbackService {

	@Autowired
	private Map<String, SimpleFlowModel> modelInitMap = new HashMap<>();

	public SimpleFlowModel getModelInstance(String moduleCode) {
		if (StringUtils.isEmpty(moduleCode)) {
			return null;
		}
		for (Map.Entry<String, SimpleFlowModel> entry : modelInitMap.entrySet()) {
			SimpleFlowModel value = entry.getValue();
			if (moduleCode.equals(value.getModuleCode())) {
				return value;
			}
		}
		return null;
	}

	public boolean auditCallback(CustomFlowDataVo vo) {
		SimpleFlowModel model = this.getModelInstance(vo.getFlowModuleCode());
		if (model == null) {
			return false;
		}
		return model.afterDegreeFlow(vo);
	}
}
