package com.yunhesoft.system.simpleFlow.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.HttpClientService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Update;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.simpleFlow.entity.dto.ParamDto;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowData;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowDataAcc;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowMondule;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowMonduleFun;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowStep;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowStepAcc;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowType;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowDataVo;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowStepVo;
import com.yunhesoft.system.simpleFlow.entity.vo.SimpleFlowAuditBean;
import com.yunhesoft.system.simpleFlow.service.ISimpleFlowService;
import com.yunhesoft.system.simpleFlow.util.SimpleFlowModel;
import com.yunhesoft.system.tools.todo.service.TodoService;

import lombok.extern.log4j.Log4j2;

/**
 *  @category 简单流程类
 * 
 * <AUTHOR> 2024-5-15
 */
@Log4j2
@Service
public class SimpleFlowServiceImpl implements ISimpleFlowService {

	/**
	 * 配置文件服务类
	 */
	@Autowired
	private EntityService srv;
	
	@Autowired
	private TodoService todosrv; // 待办服务

//	@Autowired
//	private SimpleFlowModelFactory modelFactory;

	@Autowired
    private HttpClientService httpSrv;

	@Autowired
	private SimpleFlowAuditCallbackService callbackService;

	@Autowired
    private RedisUtil redisUtil;

	@Autowired
	private Environment env;

	@Resource(name = "CustomRestTemplate")
	private RestTemplate restTemplate;

	private final int redisTimeOut = 60000;
	private String todoModuleCode = "simpleFlow";
	private String todoFunCode = "simpleFlow_";

	public String saveFlowModuleFunList (List<CustomFlowMonduleFun> list) {
		List<CustomFlowMonduleFun> insertList = new ArrayList<>();
		List<CustomFlowMonduleFun> updateList = new ArrayList<>();
		for (CustomFlowMonduleFun moduleFun : list) {
			moduleFun.setTmused(1);
			if (StringUtils.isEmpty(moduleFun.getId())) {
				//新增
				moduleFun.setId(TMUID.getUID());
				insertList.add(moduleFun);
			} else {
				//修改
				updateList.add(moduleFun);
			}
		}
		if (StringUtils.isNotEmpty(insertList)) {
			srv.insertBatch(insertList);
		}
		if (StringUtils.isNotEmpty(updateList)) {
			srv.updateByIdBatch(updateList);
		}
		return null;
	}

	public String deleteFlowModuleFunList (List<CustomFlowMonduleFun> list) {
		srv.deleteByIdBatch(list);
		return null;
	}

	/**
	 * 获取开方式工作流
	 * @category 
	 * <AUTHOR> 
	 * @param funData
	 * @return
	 */
	@Override
	public void createCustomFlowModuleFun(CustomFlowMonduleFun funData) {
		if(funData!=null && StringUtils.isNotEmpty(funData.getId())) {//传入了id
			CustomFlowMonduleFun obj = srv.queryObjectById(CustomFlowMonduleFun.class, funData.getId());//查找工作流设置
			if(obj==null) {//数据库中没有则新建
				funData.setTmsort(1);
				funData.setTmused(1);
				funData.setCustomType(1);//开放式工作流
				srv.insert(funData);//插入数据
			}
		}
	}
	
	/**
	 * @category 获取区域经理列表（设置用）调用接口 TODO
	 * @param param
	 * @return
	 */
	public List<Map<String, String>> getZoneList(ParamDto param) {
		
		List<Map<String, String>> rlist = new ArrayList<Map<String,String>>();
		
		String moduleCode = param.getModuleCode();
		
//		SimpleFlowModel sfm = SimpleFlowModelFactory.getInstanceByModuleCode(moduleCode);
//		SimpleFlowModel sfm = this.getInstanceByModuleCode(moduleCode, param.getFlowTypeCode());
//		SimpleFlowModel sfm = modelFactory.getModelInstance(moduleCode);
//		if(sfm!=null) {
//			LinkedHashMap<String, String> cmap = sfm.getCustomUserMap();
//			for (String code : cmap.keySet()) {
//				String name = cmap.get(code);
//				Map<String, String> map = new HashMap<String, String>();
//				map.put("code", code);
//				map.put("lable", name);
//				rlist.add(map);
//			}
//		}
		return rlist;
	}
	
	/**
	 * @category 获取流程模板步骤列表
	 * @param param
	 * @return
	 */
	@Override
	public List<CustomFlowStepVo> getFlowStepList(ParamDto param) {
		List<CustomFlowStepVo> rlist = new ArrayList<>();
		String moduleCode = param.getModuleCode();
		String funCode = param.getFunCode();
//		String businessCode = businessCodeChange(moduleCode, param.getFlowTypeCode(), param.getBusinessCode());
		String businessCode = param.getBusinessCode();
		List<CustomFlowStep> slist = getFlowStepList(moduleCode, funCode, businessCode);
		if(StringUtils.isEmpty(slist)) {//没有查到步骤信息
			if(param.getCustomType()!=null && param.getCustomType().intValue()==1) {//开放式工作流
				CustomFlowMonduleFun funData = new CustomFlowMonduleFun();
				funData.setId(param.getFunCode());//
				funData.setFunName(param.getFunName());//名称
				funData.setModuleCode(param.getModuleCode());//模块编码
				funData.setBusinessCode(param.getBusinessCode());//业务编码
				funData.setFlowTypeCode(param.getFlowTypeCode());//类型编码
				funData.setCustomType(param.getCustomType());//开放式工作流
				this.createCustomFlowModuleFun(funData);//自动创建
			}
		}
		if(StringUtils.isNotEmpty(slist)) {//查到了步骤信息
			List<CustomFlowStepAcc> alist = getFlowStepAccList(moduleCode, funCode, businessCode);
			Map<String, List<CustomFlowStepAcc>> amap = new HashMap<>();
			for (CustomFlowStepAcc obj : alist) {
				String stepId = obj.getStepId();
				if(amap.containsKey(stepId)) {
					amap.get(stepId).add(obj);
				}else {
					List<CustomFlowStepAcc> tlist = new ArrayList<>();
					tlist.add(obj);
					amap.put(stepId, tlist);
				}
			}
			for (CustomFlowStep obj : slist) {
				CustomFlowStepVo vo = ObjUtils.copyTo(obj, CustomFlowStepVo.class);
				List<CustomFlowStepAcc> tlist = amap.get(obj.getId());
				if(StringUtils.isNotEmpty(tlist)) {
					vo.setAccList(tlist);
				}else {
					vo.setAccList(new ArrayList<CustomFlowStepAcc>());
				}
				rlist.add(vo);
			}
		}
		return rlist;
	}
//	private String businessCodeChange(String moduleCode, String typeCode, String businessCode) {
//		SimpleFlowModel sfm = this.getInstanceByModuleCode(moduleCode, typeCode);
//		if(sfm!=null) {
//			String code = sfm.businessCodeChange(businessCode, typeCode);
//			if(code!=null) {
//				businessCode = code;
//			}
//		}
//		return businessCode;
//	}
	/*
	 * 获取流程模板步骤信息
	 */
	private List<CustomFlowStep> getFlowStepList(String moduleCode, String funCode, String businessCode) {
		Where where = Where.create().eq(CustomFlowStep::getTmused, 1);
		if(StringUtils.isNotEmpty(moduleCode)) {
			where.eq(CustomFlowStep::getFlowModuleCode, moduleCode);
		}
		if(StringUtils.isNotEmpty(funCode)) {
			where.eq(CustomFlowStep::getFlowFunCode, funCode);
		}
		if(StringUtils.isNotEmpty(businessCode)) {
			where.eq(CustomFlowStep::getBusinessCode, businessCode);
		}
		Order order = Order.create(CustomFlowStep::getTmsort);
		List<CustomFlowStep> list = srv.queryData(CustomFlowStep.class, where, order, null);
		return list;
	}
	/*
	 * 获取流程模板接收对象信息
	 */
	private List<CustomFlowStepAcc> getFlowStepAccList(String moduleCode, String funCode, String businessCode) {
		Where where = Where.create().eq(CustomFlowStepAcc::getTmused, 1);
		if(StringUtils.isNotEmpty(moduleCode)) {
			where.eq(CustomFlowStepAcc::getFlowModuleCode, moduleCode);
		}
		if(StringUtils.isNotEmpty(funCode)) {
			where.eq(CustomFlowStepAcc::getFlowFunCode, funCode);
		}
		if(StringUtils.isNotEmpty(businessCode)) {
			where.eq(CustomFlowStepAcc::getBusinessCode, businessCode);
		}
		Order order = Order.create(CustomFlowStepAcc::getStepId).orderByAsc(CustomFlowStepAcc::getTmsort);
		List<CustomFlowStepAcc> list = srv.queryData(CustomFlowStepAcc.class, where, order, null);
		return list;
	}
	/**
	 * @category 保存流程模板步骤
	 * @param param
	 * @return
	 */
	@Override
	public Boolean saveFlowStep(ParamDto param) {
		Boolean flag = true;
		List<CustomFlowStepVo> list = param.getTplStepList();
		if (StringUtils.isEmpty(list)) {
			return false;
		}
		String funCode = list.get(0).getFlowFunCode();

		List<String> stepIdList = new ArrayList<String>();
		for (CustomFlowStepVo vo : list) {
			if(StringUtils.isNotEmpty(vo.getId())) {
				stepIdList.add(vo.getId());
			}
		}

		String flowTypeCode = this.getFlowTypeCodeByFunCode(funCode);

		if(StringUtils.isNotEmpty(stepIdList)) {
			//原子表信息全部删除
			Where whereacc = Where.create().eq(CustomFlowStepAcc::getTmused, 1).in(CustomFlowStepAcc::getStepId, stepIdList.toArray());
			List<CustomFlowStepAcc> delAccList = srv.queryData(CustomFlowStepAcc.class, whereacc, null, null);
			if(StringUtils.isNotEmpty(delAccList)) {
				srv.deleteByIdBatch(delAccList);
			}
		}
		
		List<CustomFlowStep> addList = new ArrayList<>();
		List<CustomFlowStep> updList = new ArrayList<>();
		List<CustomFlowStepAcc> addAccList = new ArrayList<>();
		
		for (CustomFlowStepVo vo : list) {
			CustomFlowStep step = ObjUtils.copyTo(vo, CustomFlowStep.class);
			List<CustomFlowStepAcc> accList = vo.getAccList();
			if(StringUtils.isNotEmpty(step.getId())) {
				updList.add(step);
			}else {
//				String businessCode = businessCodeChange(step.getFlowModuleCode(), flowTypeCode, step.getBusinessCode());
				String businessCode = step.getBusinessCode();
				step.setId(TMUID.getUID());
				step.setStepNo(step.getTmsort());
				step.setTmused(1);
				step.setBusinessCode(businessCode);//编码转换
				addList.add(step);
			}
			if(StringUtils.isNotEmpty(accList)) {
				for (CustomFlowStepAcc acc : accList) {
					acc.setId(TMUID.getUID());
					acc.setStepId(step.getId());
					acc.setBusinessCode(step.getBusinessCode());
					acc.setTmused(1);
					addAccList.add(acc);
				}
			}
		}
		
		if(StringUtils.isNotEmpty(addList)) {
			flag = flag && 1 == srv.insertBatch(addList);
		}
		if(StringUtils.isNotEmpty(updList)) {
			flag = flag && 1 == srv.updateBatch(updList);
		}
		if(StringUtils.isNotEmpty(addAccList)) {
			flag = flag && 1 == srv.insertBatch(addAccList);
		}
		//如果有添加，重新排序
		if (StringUtils.isNotEmpty(addList)) {
			CustomFlowStep step = addList.get(0);
			String moduleCode = step.getFlowModuleCode();
//			String funCode = step.getFlowFunCode();
			String businessCode = step.getBusinessCode();
			reSortStep(moduleCode, funCode, businessCode);
		}
		
		return flag;
	}

	/**
	 * @category 删除流程模板步骤
	 * @param param
	 * @return
	 */
	@Override
	public boolean delFlowTplStep(ParamDto param) {
		boolean flag = true;
		
		List<CustomFlowStepVo> list = param.getTplStepList();
		
		List<CustomFlowStep> updList = new ArrayList<CustomFlowStep>();
		List<CustomFlowStepAcc> updAccList = new ArrayList<CustomFlowStepAcc>();
		for (CustomFlowStepVo vo : list) {
			CustomFlowStep step = ObjUtils.copyTo(vo, CustomFlowStep.class);
			if(StringUtils.isNotEmpty(step.getId())) {
				step.setTmused(0);
				updList.add(step);
			}
			List<CustomFlowStepAcc> accList = vo.getAccList();
			if(StringUtils.isNotEmpty(accList)) {
				for (CustomFlowStepAcc acc : accList) {
					if(StringUtils.isNotEmpty(acc.getId())) {
						acc.setTmused(0);
						updAccList.add(acc);
					}
				}
			}
		}
		
		if(StringUtils.isNotEmpty(updList)) {
			flag = flag && 1 == srv.updateBatch(updList);
		}
		if(StringUtils.isNotEmpty(updAccList)) {
			flag = flag && 1 == srv.updateBatch(updAccList);
		}
		
		//如果有删除，重新排序
		if(StringUtils.isNotEmpty(updList)) {
			CustomFlowStep step = updList.get(0);
			String moduleCode = step.getFlowModuleCode();
			String funCode = step.getFlowFunCode();
			String businessCode = step.getBusinessCode();
			reSortStep(moduleCode, funCode, businessCode);
		}
		return flag;
	}
	//重新排序
	private void reSortStep(String moduleCode, String funCode, String businessCode) {
		List<CustomFlowStep> list = getFlowStepList(moduleCode, funCode, businessCode);
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0,il=list.size(); i < il; i++) {
				CustomFlowStep step = list.get(i);
				step.setStepNo(i+1);
				step.setTmsort(i+1);
			}
			srv.updateBatch(list);
		}
	}
	/**
	 * @category 启动流程
	 * @param param
	 * @return
	 */
	@Override
	public CustomFlowDataVo startFlow(ParamDto param) {
		
		CustomFlowDataVo rv = new CustomFlowDataVo();

		Where where = Where.create().between(CustomFlowData::getRunStatus, 0, 1);
		where.eq(CustomFlowData::getBusinessDataCode, param.getBusinessDataCode());
		List<CustomFlowData> list = srv.queryData(CustomFlowData.class, where, null, null);
		if (StringUtils.isNotEmpty(list)) {
			//存在待审核或审核中的数据
			rv.setFlowDegreeResult(false);
			rv.setFlowErrorInfo("该记录已经处于流程中，不能重复提交");
			return rv;
		}

		String moduleCode = param.getModuleCode();
		String funCode = param.getFunCode();
		CustomFlowMonduleFun customFlowMonduleFun = this.queryFlowMonduleFunById(funCode);
		String businessCode = param.getBusinessCode();
		String businessDataCode = param.getBusinessDataCode();
		String businessDataName = param.getBusinessDataName();
		String customUserCode = param.getCustomUserCode();
		String flowTypeCode = this.getFlowTypeCodeByFunCode(funCode);
		
		try {
//			String sourceBusinessCode = businessCodeChange(moduleCode, flowTypeCode, businessCode);
			String sourceBusinessCode = businessCode;

			//获取流程配置
			List<CustomFlowStepVo> stepList = getFlowStepList(param);//使用tplid
			
			if(StringUtils.isNotEmpty(stepList)) {
				
				//写入流程实例数据
				CustomFlowData data = new CustomFlowData();
				data.setId(TMUID.getUID());
				data.setFlowModuleCode(moduleCode);
				data.setFlowFunCode(funCode);
				data.setFlowFunName(customFlowMonduleFun == null ? null : customFlowMonduleFun.getFunName());
				data.setFlowTypeCode(flowTypeCode);
				data.setBusinessCode(businessCode);
				data.setCustomUserCode(customUserCode);
				data.setSourceBusinessCode(sourceBusinessCode);
				data.setBusinessDataCode(businessDataCode);
				data.setBusinessDataName(businessDataName);
				data.setCurrStepId(stepList.get(0).getId());
				data.setRunStatus(0);
				data.setTmused(1);
				//接收对象数据写入
				List<CustomFlowDataAcc> accAddList = new ArrayList<>();
				for (CustomFlowStepVo step : stepList) {
					List<CustomFlowDataAcc> stepAccAddList = new ArrayList<>();
					List<String> hlist = new ArrayList<>();//记录已有人员，岗位，避免同一步骤重复
					List<CustomFlowStepAcc> accList = step.getAccList();
					if(StringUtils.isNotEmpty(accList)) {
						
						List<String> customIds = new ArrayList<>();
						
						for (CustomFlowStepAcc acc : accList) {
							CustomFlowDataAcc obj = new CustomFlowDataAcc();
							obj.setId(TMUID.getUID());
							obj.setFlowModuleCode(acc.getFlowModuleCode());
							obj.setFlowFunCode(data.getFlowFunCode());
							obj.setFlowFunName(data.getFlowFunName());
							obj.setFlowTypeCode(acc.getFlowTypeCode());
							obj.setBusinessCode(businessCode);
							obj.setFlowDataId(data.getId());
							obj.setStepId(step.getId());
							obj.setStepNo(step.getStepNo());
							obj.setAccSourceId(acc.getId());
							obj.setDegreeMark(0);
							obj.setDegreeResult(0);
							if(new Integer(1).equals(step.getStepNo())) {//启动流程后，第一步相关接收对象为当前可用对象
								obj.setCurrMark(1);
							}else {
								obj.setCurrMark(0);
							}
							
							if(new Integer(1).equals(acc.getAccType()) || new Integer(2).equals(acc.getAccType())) {//个人或岗位
								if(hlist.contains(acc.getAccCode())) {
									continue;
								}
								
								obj.setAccType(acc.getAccType());
								obj.setAccCode(acc.getAccCode());
								obj.setAccName(acc.getAccName());
								obj.setOrgCode(acc.getOrgCode());
								obj.setJobCode(acc.getJobCode());
								
								hlist.add(acc.getAccCode());
							}else if(new Integer(9).equals(acc.getAccType())) {
								customIds.add(acc.getAccCode());
								continue;
							}
							obj.setTmused(1);
							
							accAddList.add(obj);
							stepAccAddList.add(obj);
						}
						
						if(StringUtils.isNotEmpty(customIds)) {//根据接口获取区域经理相关人员或岗位

							/*
							SimpleFlowModel sfm = modelFactory.getModelInstance(moduleCode);
							if(sfm==null) {
								return null;
							}
							List<CustomUserVo> clist = sfm.getCustomUserByCode(customIds, businessCode,customUserCode);
							if(StringUtils.isNotEmpty(clist)) {
								for (CustomUserVo vo : clist) {
									CustomFlowDataAcc obj = new CustomFlowDataAcc();
									obj.setId(TMUID.getUID());
									obj.setFlowModuleCode(moduleCode);
									obj.setFlowFunCode(funCode);
									obj.setFlowTypeCode(flowTypeCode);
									obj.setBusinessCode(businessCode);
									obj.setFlowDataId(data.getId());
									obj.setStepId(step.getId());
									obj.setStepNo(step.getStepNo());
									obj.setAccSourceId(null);
									obj.setDegreeMark(0);
									obj.setDegreeResult(0);
									obj.setTmused(1);
									if(new Integer(1).equals(step.getStepNo())) {//启动流程后，第一步相关接收对象为当前可用对象
										obj.setCurrMark(1);
									}else {
										obj.setCurrMark(0);
									}
									
									String code = null, name = null, org=null, job=null;
									if(new Integer(1).equals(vo.getType())) {//
										code = vo.getUserId();
										name = vo.getUserName();
									}else {
										code = vo.getPostOrgCode()+"_"+vo.getPostId();
										name = vo.getPostName();
										org = vo.getPostOrgCode();
										job = vo.getPostId();
									}
									if(StringUtils.isEmpty(code)) {
										continue;
									}
									if(hlist.contains(code)) {
										continue;
									}
									obj.setAccType(vo.getType());
									obj.setAccCode(code);
									obj.setAccName(name);
									obj.setOrgCode(org);
									obj.setJobCode(job);
									
									hlist.add(code);
									accAddList.add(obj);
									stepAccAddList.add(obj);
								}
							}
							*/
						}
					}
					
					//如果某一步骤没有接收信息存入，直接返回错误
					if(StringUtils.isEmpty(stepAccAddList)) {
						rv.setFlowDegreeResult(false);
						rv.setFlowErrorInfo("工作流程步骤("+step.getAccShow()+"),未获取到审核人员");
						return rv;
					}
				}
				
				if(StringUtils.isNotEmpty(accAddList)) {
					srv.insert(data);
					srv.insertBatch(accAddList);
					
					//启动不自动向下走
//				SysUser sysUser = SysUserUtil.getCurrentUser();
//				String userId = sysUser.getId();
//				String userJob = sysUser.getOrgId()+"_"+sysUser.getPostId();
//				
//				//启动流程后，判断这个流程是否当前人员可进行处理，依次进行
//				Map<Integer, List<CustomFlowDataAcc>> smap = new HashMap<Integer,List<CustomFlowDataAcc>>();
//				for (CustomFlowDataAcc acc : accAddList) {
//					Integer stepNo = acc.getStepNo();
//					if(smap.containsKey(stepNo)) {
//						smap.get(stepNo).add(acc);
//					}else {
//						List<CustomFlowDataAcc> tlist = smap.get(stepNo);
//						if(StringUtils.isEmpty(tlist)) {
//							tlist = new ArrayList<CustomFlowDataAcc>();
//							tlist.add(acc);
//						}
//						smap.put(stepNo, tlist);
//					}
//				}
//				List<CustomFlowDataAcc> updList = new ArrayList<CustomFlowDataAcc>();
//				for (int i = 0; i < 5; i++) {
//					List<CustomFlowDataAcc> alist = smap.get(i+1);
//					if(StringUtils.isNotEmpty(alist)) {
//						Boolean haveDegree = false;
//						for (CustomFlowDataAcc acc : alist) {
//							if(userId.equals(acc.getAccCode()) || userJob.equals(acc.getAccCode())) {
//								acc.setDegreeMark(1);
//								acc.setDegreeUserName(sysUser.getRealName());
//								haveDegree = true;
//								break;
//							}
//						}
//						if(haveDegree) {
//							for (CustomFlowDataAcc acc : alist) {
//								acc.setCurrMark(0);
//								updList.add(acc);
//							}
//						}else {//没有同处理人
//							if(i > 0) {
//								for (CustomFlowDataAcc acc : alist) {
//									acc.setCurrMark(1);
//									updList.add(acc);
//								}
//								data.setCurrStepId(alist.get(0).getStepId());
//								data.setRunStatus(1);
//								srv.updateById(data);
//							}
//							break;
//						}
//					}else {
//						data.setCurrStepId(null);
//						data.setRunStatus(2);
//						srv.updateById(data);
//						break;
//					}
//				}
//				if(StringUtils.isNotEmpty(updList)) {
//					srv.updateBatch(updList, 50);
//				}
					
					rv = ObjUtils.copyTo(data, CustomFlowDataVo.class);
//				//清理redis
//				SimpleFlowModel sfm = SimpleFlowModelFactory.getInstanceByModuleCode(moduleCode);
					todosrv.clearTodoCached(todoModuleCode, todoFunCode+moduleCode);
				}
			}else {
				rv.setFlowDegreeResult(false);
				rv.setFlowErrorInfo("未设置工作处理流程");
			}
		} catch (Exception e) {
			log.error(e);
			rv.setFlowDegreeResult(false);
			rv.setFlowErrorInfo("工作流程创建错误");
		}
		
		
		return rv;
	}

	/**
	 *	批量审核接口
	 * @param auditList
	 * @param moduleCode
	 * @return
	 */
	@Override
	public List<CustomFlowDataVo> auditFlowBatch(List<SimpleFlowAuditBean> auditList, String moduleCode) {
//		List<CustomFlowDataVo> result = new ArrayList<>();
//		//创建工作流控制类
//		SimpleFlowModel flowMode = modelFactory.getModelInstance(moduleCode);
//		if(flowMode!=null) {
//			result = flowMode.degreeFlowBatch(auditList);
//		}
//		return result;
		List<CustomFlowDataVo> result = new ArrayList<>();;
		if (StringUtils.isNotEmpty(auditList)) {
			for(SimpleFlowAuditBean temp:auditList) {//批量审核时，需要按记录单次审核，避免库存计算并发
				result.add(degreeFlow(temp));//这里不会返回空，直接返回信息即可
//				if(auditResult==null) {
//					CustomFlowDataVo errBean = new CustomFlowDataVo();
//					errBean.setBusinessDataCode(param.getFlowId());
//					errBean.setAfterDegreeResult(false);
//					errBean.setErrorInfo("审核操作出现异常，未能生成审核数据！");
//				}else {
//					result.add(e)
//				}
			}
		}
		return result;
	}

	public CustomFlowDataVo degreeFlow(SimpleFlowAuditBean param) {
		CustomFlowDataVo result = null;
		ParamDto auditparam = new ParamDto();
		auditparam.setIdstr(param.getFlowId());////流程实例ID，批量用逗号分割
		auditparam.setDegreeMark(param.getAuditStatus());//处理标识 1通过 -1否决
		auditparam.setDegreeDesc(param.getAuditDesc());//审核描述，前台给定
		List<CustomFlowDataVo> auditList = degreeFlowStep(auditparam);
		if (StringUtils.isNotEmpty(auditList)) {
			result=auditList.get(0);
			try {
				String moduleCode = result.getFlowModuleCode();
				String eurekaEnabled = env.getProperty("eureka.client.enabled");
				if ("true".equals(eurekaEnabled)) {
					//微服务启动
					CustomFlowMondule customFlowMondule = this.queryFlowMonduleByModuleCode(moduleCode);
					String callbackApi = customFlowMondule == null ? null : customFlowMondule.getCallbackApi();
					String sysAddress = customFlowMondule == null ? null : customFlowMondule.getSysAddress();
					String serviceName = customFlowMondule == null ? null : customFlowMondule.getServiceName();
					if (StringUtils.isNotEmpty(serviceName)) {
						//有服务名
//						Map<String, String> headers = new HashMap<>();
//						headers.put("Authorization", "Bearer "+ SysUserHolder.getCurrentToken());
//						String apiUrl = sysAddress+"/system/simpleFlow/callback/auditCallback";
//						String res = httpSrv.post(apiUrl, headers, result);

						String http = "http";
						if ("true".equalsIgnoreCase(env.getProperty("server.ssl.enabled"))) {
							http = "https";
						}
						String context = env.getProperty("server.servlet.context-path");
						if (StringUtils.isEmpty(context)) {
							context = "";
						} else {
							if (!context.startsWith("/")) {
								context = "/" + context;
							}
						}
						String url = "http://" + serviceName + context + "/system/simpleFlow/callback/auditCallback";
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.APPLICATION_JSON);
						String currentToken = SysUserHolder.getCurrentToken();
						headers.set("Authorization", currentToken);
						HttpEntity<Object> entity = new HttpEntity<>(result, headers);
						Res sysRes = restTemplate.exchange(url, HttpMethod.POST, entity, Res.class).getBody();

						Boolean res = (Boolean) sysRes.getResult();
						if (res != null && !res) {
							//回调失败
							Integer jg = toUpStepFlow(param.getFlowId());//流程返回上一步
							result.setAfterDegreeResult(false);
							result.setErrorInfo(result.getErrorInfo());//错误信息
							if(jg!=1) {
								result.setErrorInfo("审核异常终止，请联系管理员！");//错误信息
							}
						}
					}
				} else {
					//单服务启动
					SimpleFlowModel moduleInstance = callbackService.getModelInstance(moduleCode);
					if (moduleInstance != null) {
						boolean rv = moduleInstance.afterDegreeFlow(result);//这里考虑执行后出错的情况
						if(!rv) {
							Integer jg = toUpStepFlow(param.getFlowId());//流程返回上一步
							result.setAfterDegreeResult(false);
							result.setErrorInfo(result.getErrorInfo());//错误信息
							if(jg!=1) {
								result.setErrorInfo("审核异常终止，请联系管理员！");//错误信息
							}
						}
					}
				}
			}catch(Exception e){
				result.setAfterDegreeResult(false);
				result.setErrorInfo("审核异常终止，请联系管理员！");//错误信息
				log.error("", e);
			}
		}else {
			result = new CustomFlowDataVo();
			result.setBusinessDataCode(param.getFlowId());
			result.setAfterDegreeResult(false);
			result.setRunStatus(null);//审核异常，没有运行状态！！
			result.setErrorInfo("审核操作出现异常，未能生成审核数据！");
		}
		return result;
	}

	/**
	 * @category 处理流程
	 * @param param
	 * @return 返回null出错，否则返回流程主数据数组
	 */
	@Override
	public List<CustomFlowDataVo> degreeFlowStep(ParamDto param) {
		
		List<CustomFlowDataVo> rlist = null;
		
		String ids = param.getIdstr();//流程实例ID，可批量处理
		String userId = param.getUserId();//
		String degreeMark = param.getDegreeMark();//处理标识 1通过 -1否决
		String degreeDesc = param.getDegreeDesc();//处理信息
		List<String> idList = Coms.StrToList(ids, ",");
		if(StringUtils.isEmpty(ids)) {//需有传入标识
			return null;
		}
		
		SysUser sysUser = SysUserUtil.getCurrentUser();
		if(StringUtils.isEmpty(userId)) {
			userId = sysUser.getId();
		}
		String userJob = sysUser.getOrgId()+"_"+sysUser.getPostId();
		
		Where wheredata = Where.create().in(CustomFlowData::getId, idList.toArray());
		List<CustomFlowData> dataList = srv.queryData(CustomFlowData.class, wheredata, null, null);
		
		
		if(StringUtils.isNotEmpty(dataList)) {
			Where where = Where.create().eq(CustomFlowDataAcc::getTmused, 1).in(CustomFlowDataAcc::getFlowDataId, idList.toArray());
			Order order  = Order.create(CustomFlowDataAcc::getFlowDataId).orderByAsc(CustomFlowDataAcc::getStepNo);
			List<CustomFlowDataAcc> accList = srv.queryData(CustomFlowDataAcc.class, where, order, null);
			
			Map<String, Map<Integer, List<CustomFlowDataAcc>>> amap = new HashMap<String, Map<Integer,List<CustomFlowDataAcc>>>();
			for (CustomFlowDataAcc acc : accList) {
				String fid = acc.getFlowDataId();
				Integer stepNo = acc.getStepNo();
				if(amap.containsKey(fid)) {
					Map<Integer, List<CustomFlowDataAcc>> smap = amap.get(fid);
					if(smap.containsKey(stepNo)) {
						smap.get(stepNo).add(acc);
					}else {
						List<CustomFlowDataAcc> tlist = smap.get(stepNo);
						if(StringUtils.isEmpty(tlist)) {
							tlist = new ArrayList<CustomFlowDataAcc>();
							tlist.add(acc);
						}
						smap.put(stepNo, tlist);
					}
				}else {
					List<CustomFlowDataAcc> tlist = new ArrayList<CustomFlowDataAcc>();
					tlist.add(acc);
					Map<Integer, List<CustomFlowDataAcc>> smap = new HashMap<Integer, List<CustomFlowDataAcc>>();
					smap.put(stepNo, tlist);
					amap.put(fid, smap);
				}
			}
			
			List<CustomFlowDataAcc> updList = new ArrayList<CustomFlowDataAcc>();
			
			for (CustomFlowData obj : dataList) {
				Map<Integer, List<CustomFlowDataAcc>> smap = amap.get(obj.getId());
				if(smap!=null) {
					Integer currNo = null;//当前接收对象列表
					List<CustomFlowDataAcc> currAccList = null;
					for (List<CustomFlowDataAcc> alist : smap.values()) {
						if(StringUtils.isNotEmpty(alist)) {
							CustomFlowDataAcc aobj = alist.get(0);
							if(new Integer(1).equals(aobj.getCurrMark())) {//当前处理状态
								currNo = aobj.getStepNo();
								currAccList = alist;
								break;
							}
						}
					}
					
					if(StringUtils.isNotEmpty(currAccList)) {
						Boolean degree = false;
						for (CustomFlowDataAcc acc : currAccList) {//处理当前记录
							acc.setCurrMark(0);
							if(!degree && (userId.equals(acc.getAccCode()) || userJob.equals(acc.getAccCode()))) {
								acc.setDegreeMark(1);
								acc.setDegreeUserName(sysUser.getRealName());
								acc.setDegreeUserId(sysUser.getId());
								if (Coms.judgeInt(degreeMark)) {
									acc.setDegreeResult(Integer.parseInt(degreeMark));
								}
								degree = true;
							}
						}
						if("1".equals(degreeMark)) {//通过
							List<CustomFlowDataAcc> nextList = smap.get(currNo==null?null:currNo+1);
							if(StringUtils.isNotEmpty(nextList)) {//下一步骤
								//判断是否可以处理下一步骤处理
								for (int i = 0; i < 5; i++) {//相同处理人，向下一步进行处理，默认5步相同，最多向下处理5步
									Boolean haveDegree = false;
									if(StringUtils.isNotEmpty(nextList)) {//下一步骤
										for (CustomFlowDataAcc acc : nextList) {
											if(userId.equals(acc.getAccCode()) || userJob.equals(acc.getAccCode())) {
												acc.setDegreeMark(1);
												acc.setDegreeUserName(sysUser.getRealName());
												acc.setDegreeUserId(sysUser.getId());
												acc.setDegreeResult(1);
												haveDegree = true;
												break;
											}
										}
										if(haveDegree) {
											for (CustomFlowDataAcc acc : nextList) {
												updList.add(acc);
											}
											nextList = smap.get(currNo==null?null:currNo+i+2);
										}else {//没有同处理人
											break;
										}
									}
								}
								
								if(StringUtils.isNotEmpty(nextList)) {//有下一步骤
									for (CustomFlowDataAcc acc : nextList) {
										acc.setCurrMark(1);
									}
									updList.addAll(nextList);
									obj.setCurrStepId(nextList.get(0).getStepId());
									obj.setRunStatus(1);
								}else {
//									obj.setCurrStepId(null);
									obj.setRunStatus(2);
								}
							}else {//结束
//								obj.setCurrStepId(null);
								obj.setRunStatus(2);
							}
							
							updList.addAll(currAccList);
						}else if("-1".equals(degreeMark)) {//否决，流程终止
//							obj.setCurrStepId(null);
							obj.setRunStatus(-1);
							obj.setBackDesc(degreeDesc);
							updList.addAll(currAccList);
						}
					}else {
						log.error("流程数据错误，未获取到当前流程步骤数据，流程数据ID："+obj.getId());
					}
				}else {
					log.error("流程数据错误，流程数据ID："+obj.getId());
				}
			}
			
			if(StringUtils.isNotEmpty(updList)) {
				srv.updateBatch(dataList);
				srv.updateBatch(updList, 50);
				todosrv.clearTodoCached(todoModuleCode, todoFunCode+dataList.get(0).getFlowModuleCode());
			}
			
			rlist = new ArrayList<>();
			for (CustomFlowData fd : dataList) {
				CustomFlowDataVo vo = ObjUtils.copyTo(fd, CustomFlowDataVo.class);
				rlist.add(vo);
			}
		}
		
		return rlist;
	}
	/**
	 * @category 流程返回上一步（用于处理流程后业务处理出错后，流程数据还原）
	 * @param
	 * @return 1成功 0还原失败 -1流程已删除
	 */
	@Override
	public Integer toUpStepFlow(String flowId) {
		Integer rv = 0;
		if(StringUtils.isNotEmpty(flowId)) {
			CustomFlowData data = srv.queryObjectById(CustomFlowData.class, flowId);
			if(new Integer(0).equals(data.getTmused())) {//流程已删除，还原失败
				log.error("流程返回上一步失败，流程已删除，流程ID:"+data.getId());
				return -1;
			}
			//查流程数据
			Where where = Where.create().eq(CustomFlowDataAcc::getTmused, 1).eq(CustomFlowDataAcc::getFlowDataId, flowId);
			Order order  = Order.create().orderByAsc(CustomFlowDataAcc::getStepNo);
			List<CustomFlowDataAcc> accList = srv.queryData(CustomFlowDataAcc.class, where, order, null);
			//整理流程各步骤数据
			Map<Integer, List<CustomFlowDataAcc>> smap = new HashMap<Integer, List<CustomFlowDataAcc>>();
			Integer currStep = null;//当前活动流程序号
			Integer maxNo = null;//最大流程序号
			for (CustomFlowDataAcc acc : accList) {
				Integer stepNo = acc.getStepNo();
				if(smap.containsKey(stepNo)) {
					smap.get(stepNo).add(acc);
				}else {
					List<CustomFlowDataAcc> tlist = new ArrayList<CustomFlowDataAcc>();
					tlist.add(acc);
					smap.put(stepNo, tlist);
				}
				if(new Integer(1).equals(acc.getCurrMark())) {
					currStep = acc.getStepNo();
				}
				if(maxNo==null || stepNo > maxNo) {
					maxNo = stepNo;
				}
			}
			List<CustomFlowDataAcc> updateList = new ArrayList<CustomFlowDataAcc>();
			if(currStep == null) {//流程结束了
//				currStep = maxNo + (maxNo == 1 ? 0 : 1);//只有一步的情况，无上一步
				//判断流程是，否决结束还是终止结束；否决结束，找停止的位置；终止结束，最后一个位置启动
				if(new Integer(-1).equals(data.getRunStatus())) {//否决结束
					String cstep = data.getCurrStepId();
					Integer pos = -1;
					for (CustomFlowDataAcc acc : accList) {
						if(acc.getStepId().equals(cstep)) {
							acc.setCurrMark(1);
							acc.setDegreeMark(0);
							updateList.add(acc);
							pos = acc.getStepNo();
						}
					}
					data.setRunStatus(1==pos?0:1);
				}else if(new Integer(2).equals(data.getRunStatus())){//终止结束，退回到最后一步
					String currStepId = null;
					List<CustomFlowDataAcc> tlist = smap.get(maxNo);
					if(maxNo!=null) {
						for (CustomFlowDataAcc acc : tlist) {
							acc.setCurrMark(1);
							acc.setDegreeMark(0);
							updateList.add(acc);
							currStepId = acc.getStepId();
						}
					}
					data.setCurrStepId(currStepId);
					data.setRunStatus(1==maxNo?0:1);
				}
				
			}else {//当前活动步骤改为不活动
				List<CustomFlowDataAcc> clist = smap.get(currStep);
				for (CustomFlowDataAcc acc : clist) {
					acc.setCurrMark(0);
					acc.setDegreeMark(0);
					updateList.add(acc);
				}
			}
			
			if(currStep == null) {//流程结束了
				if(StringUtils.isNotEmpty(updateList)) {
					srv.updateById(data);
					rv = srv.updateBatch(updateList);
					todosrv.clearTodoCached(todoModuleCode, todoFunCode+data.getFlowModuleCode());
				}else {
					log.error("流程返回上一步失败，流程数据错误，流程ID:"+data.getId());
					rv = 0;//内部错误
				}
			}else {
				List<CustomFlowDataAcc> ulist = smap.get(currStep - 1);//步骤的上一步
				if(StringUtils.isNotEmpty(ulist)) {
					for (CustomFlowDataAcc acc : ulist) {
						acc.setCurrMark(1);
						acc.setDegreeMark(0);
						updateList.add(acc);
						data.setCurrStepId(acc.getStepId());
					}
					if(StringUtils.isNotEmpty(updateList)) {
						if(new Integer(2).equals(currStep)) {
							data.setRunStatus(0);
						}else {
							data.setRunStatus(1);
						}
						srv.updateById(data);
						rv = srv.updateBatch(updateList);
						todosrv.clearTodoCached(todoModuleCode, todoFunCode+data.getFlowModuleCode());
					}
				}else {//没有上一步，删除流程（对应发起失败）
					data.setRunStatus(-3);//发起失败，退回删除
					data.setTmused(0);
					srv.updateById(data);
					rv = srv.updateBatch(updateList);
					todosrv.clearTodoCached(todoModuleCode, todoFunCode+data.getFlowModuleCode());
				}
				if(rv!=1) {
					log.error("流程返回上一步失败，程序更新错误，流程ID:"+data.getId());
				}
			}
			
		}
		return rv;
	}

	@Override
	public Integer toUpStepFlow(List<String> flowIdList) {
		Integer rv = 0;
		if(StringUtils.isEmpty(flowIdList)) {
			return rv;
		}
		Where wheredata = Where.create().in(CustomFlowData::getId, flowIdList.toArray());
		List<CustomFlowData> dataList = srv.queryData(CustomFlowData.class, wheredata, null, null);
		
		
		if(StringUtils.isNotEmpty(dataList)) {
			Where where = Where.create().eq(CustomFlowDataAcc::getTmused, 1).in(CustomFlowDataAcc::getFlowDataId, flowIdList.toArray());
			Order order  = Order.create(CustomFlowDataAcc::getFlowDataId).orderByAsc(CustomFlowDataAcc::getStepNo);
			List<CustomFlowDataAcc> accList = srv.queryData(CustomFlowDataAcc.class, where, order, null);
			
			Map<String, Map<Integer, List<CustomFlowDataAcc>>> amap = new HashMap<String, Map<Integer,List<CustomFlowDataAcc>>>();
			for (CustomFlowDataAcc acc : accList) {
				String fid = acc.getFlowDataId();
				Integer stepNo = acc.getStepNo();
				if(amap.containsKey(fid)) {
					Map<Integer, List<CustomFlowDataAcc>> smap = amap.get(fid);
					if(smap.containsKey(stepNo)) {
						smap.get(stepNo).add(acc);
					}else {
						List<CustomFlowDataAcc> tlist = smap.get(stepNo);
						if(StringUtils.isEmpty(tlist)) {
							tlist = new ArrayList<CustomFlowDataAcc>();
							tlist.add(acc);
						}
						smap.put(stepNo, tlist);
					}
				}else {
					List<CustomFlowDataAcc> tlist = new ArrayList<CustomFlowDataAcc>();
					tlist.add(acc);
					Map<Integer, List<CustomFlowDataAcc>> smap = new HashMap<Integer, List<CustomFlowDataAcc>>();
					smap.put(stepNo, tlist);
					amap.put(fid, smap);
				}
			}
			
			Set<String> funSet = new HashSet<String>();
			List<CustomFlowDataAcc> updList = new ArrayList<CustomFlowDataAcc>();
			for (CustomFlowData obj : dataList) {
				if(new Integer(0).equals(obj.getTmused())) {
					continue;
				}
				Integer currStep = null;
				Integer maxNo = null;
				Map<Integer, List<CustomFlowDataAcc>> smap = amap.get(obj.getId());
				if(smap!=null) {
					if(!funSet.contains(obj.getFlowFunCode())) {
						funSet.add(obj.getFlowFunCode());
					}
					for (Integer stepNo : smap.keySet()) {
						List<CustomFlowDataAcc> tlist = smap.get(stepNo);
						for (CustomFlowDataAcc acc : tlist) {
							if(new Integer(1).equals(acc.getCurrMark())) {
								currStep = stepNo;
								acc.setCurrMark(0);
								updList.add(acc);
							}
						}
						if(maxNo==null || stepNo > maxNo) {
							maxNo = stepNo;
						}
					}
					if(currStep == null) {
						currStep = maxNo + (maxNo == 1 ? 0 : 1);//只有一步的情况
					}
					if(currStep!=null && currStep > 1) {
						List<CustomFlowDataAcc> tlist = smap.get(currStep - 1);
						if(tlist!=null) {
							for (CustomFlowDataAcc acc : tlist) {
								if(new Integer(1).equals(acc.getCurrMark())) {
									acc.setCurrMark(1);
									acc.setDegreeMark(0);
									updList.add(acc);
									obj.setCurrStepId(acc.getStepId());
								}
							}
						}
						if(new Integer(2).equals(currStep)) {
							obj.setRunStatus(0);
						}else {
							obj.setRunStatus(1);
						}
					}else {//第一步删除流程
//						return rv;
						obj.setRunStatus(0);
						obj.setTmused(0);
					}
				}
			}
			if(StringUtils.isNotEmpty(updList)) {
				srv.updateBatch(dataList);
				rv = srv.updateBatch(updList, 100);
				for (String fun : funSet) {
					todosrv.clearTodoCached(todoModuleCode, todoFunCode+fun);//清理功能缓存
				}
			}
		}
		return rv;
	}
	/**
	 * @category 撤回流程
	 * @param
	 * @return 1成功 0已进行无法撤回 -1程序出错未撤回
	 */
	public Integer backFlow(String flowId) {
		CustomFlowData data = srv.queryObjectById(CustomFlowData.class, flowId);
		if(data==null) {
			return -1;
		}else {
			if(new Integer(0).equals(data.getRunStatus())) {//可以撤回
				Where where = Where.create().eq(CustomFlowDataAcc::getTmused, 1).eq(CustomFlowDataAcc::getFlowDataId, flowId).eq(CustomFlowDataAcc::getCurrMark, 1);
				List<CustomFlowDataAcc> accList = srv.queryData(CustomFlowDataAcc.class, where, null, null);
				if(StringUtils.isEmpty(accList)) {
					return -1;
				}else {
					for (CustomFlowDataAcc acc : accList) {
						acc.setCurrMark(0);
					}
					data.setCurrStepId(null);
					data.setRunStatus(-2);
					Boolean flag = 1 == srv.updateById(data);
					flag = flag && 1 == srv.updateBatch(accList, 50);
					todosrv.clearTodoCached(todoModuleCode, todoFunCode+data.getFlowModuleCode());
					if(flag) {
						return 1;
					}else {
						return -1;
					}
				}
			}else {
				return 0;
			}
		}
		
	}
	/**
	 * @category 清理流程
	 * @param param
	 * @return 返回null出错，否则返回删除的流程主数据数组
	 */
	@Override
	public List<CustomFlowDataVo> delFlow(ParamDto param) {
		
		List<CustomFlowDataVo> rlist = null;
		
		String ids = param.getIdstr();//流程实例ID，可批量处理
		List<String> idList = Coms.StrToList(ids, ",");
		if(StringUtils.isEmpty(ids)) {//需有传入标识
			return null;
		}
		
		Where wheredata = Where.create().in(CustomFlowData::getId, idList.toArray());
		List<CustomFlowData> dataList = srv.queryData(CustomFlowData.class, wheredata, null, null);
		if(StringUtils.isNotEmpty(dataList)) {
			
			Set<String> mlist = new HashSet<String>();
			
			Where where = Where.create().eq(CustomFlowDataAcc::getTmused, 1).in(CustomFlowDataAcc::getFlowDataId, idList.toArray()).eq(CustomFlowDataAcc::getCurrMark, 1);
			List<CustomFlowDataAcc> accList = srv.queryData(CustomFlowDataAcc.class, where, null, null);
			
			for (CustomFlowData data : dataList) {
				data.setCurrStepId(null);
				data.setRunStatus(-2);
				if(!mlist.contains(data.getFlowModuleCode())) {
					mlist.add(data.getFlowModuleCode());
				}
			}
			for (CustomFlowDataAcc acc : accList) {
				acc.setCurrMark(0);
			}
			
			srv.updateBatch(dataList);
			if(StringUtils.isNotEmpty(accList)) {
				srv.updateBatch(accList, 50);
			}
			
			if(StringUtils.isNotEmpty(mlist)) {
				for (String mcode : mlist) {
					todosrv.clearTodoCached(todoModuleCode, todoFunCode+mcode);
				}
			}
			
			rlist = new ArrayList<CustomFlowDataVo>();
			for (CustomFlowData fd : dataList) {
				CustomFlowDataVo vo = ObjUtils.copyTo(fd, CustomFlowDataVo.class);
				rlist.add(vo);
			}
		}
		
		return rlist;
	}

	/**
	 * @category 删除流程数据
	 * @param param
	 * @return 返回null出错，否则返回删除的流程主数据数组
	 */
	@Override
	public String clearFlowDataByBusinessDataCode(List<String> businessDataCodeList) {

		Where wheredata = Where.create().in(CustomFlowData::getBusinessDataCode, businessDataCodeList.toArray());
		List<CustomFlowData> dataList = srv.queryData(CustomFlowData.class, wheredata, null, null);
		if (StringUtils.isEmpty(dataList)) {
			return null;
		}

		Set<String> mlist = new HashSet<>();

		List<String> dataIdList = new ArrayList<>();
		for (CustomFlowData data : dataList) {
			dataIdList.add(data.getId());
			mlist.add(data.getFlowModuleCode());
		}

//		Where where = Where.create().eq(CustomFlowDataAcc::getTmused, 1).in(CustomFlowDataAcc::getFlowDataId, dataIdList.toArray());
//		List<CustomFlowDataAcc> accList = srv.queryData(CustomFlowDataAcc.class, where, null, null);

		srv.rawUpdate(CustomFlowData.class, Update.create(CustomFlowData::getTmused, 0).update(CustomFlowData::getRunStatus, -2).update(CustomFlowData::getCurrStepId, ""), Where.create().in(CustomFlowData::getId, dataIdList.toArray()));

		srv.rawUpdate(CustomFlowDataAcc.class, Update.create(CustomFlowDataAcc::getTmused, 0).update(CustomFlowDataAcc::getCurrMark, 0), Where.create().in(CustomFlowDataAcc::getFlowDataId, dataIdList.toArray()));


		if (StringUtils.isNotEmpty(mlist)) {
			for (String mcode : mlist) {
				todosrv.clearTodoCached(todoModuleCode, todoFunCode+mcode);
			}
		}

		return null;
	}
	/**
	 * @category 获取人员流程待办数
	 * @return
	 */
	@Override
	public Integer getFlowTodo(String moduleCode) {
		return getFlowTodo(null, moduleCode);
	}
	@Override
	public Integer getFlowTodo(String userId, String moduleCode) {
		int rv = 0;
		
		SysUser sysUser = SysUserUtil.getCurrentUser();
		if(StringUtils.isEmpty(userId)) {
			userId = sysUser.getId();
		}
		String userJob = sysUser.getOrgId()+"_"+sysUser.getPostId();
		
		Where where = Where.create().eq(CustomFlowDataAcc::getTmused, 1).eq(CustomFlowDataAcc::getCurrMark, 1).eq(CustomFlowDataAcc::getFlowModuleCode, moduleCode).in(CustomFlowDataAcc::getAccCode, userId, userJob);
		List<CustomFlowDataAcc> list = srv.queryData(CustomFlowDataAcc.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			Set<String> set = new HashSet<String>(); 
			for (CustomFlowDataAcc obj : list) {
				if(!set.contains(obj.getFlowDataId())) {
					set.add(obj.getFlowDataId());
				}
			}
			rv = set.size();
		}
//		Long l = srv.queryCount(CustomFlowDataAcc.class, where);
		return rv;
	}

	/**
	 * @category 获取人员流程待办列表
	 * @param
	 * @return
	 */
	@Override
	public List<CustomFlowDataVo> getFlowTodoList(String moduleCode) {
		return getFlowTodoList(null, moduleCode, null);
	}
	@Override
	public List<CustomFlowDataVo> getFlowTodoList(String userId, String moduleCode, String funCode) {
		List<CustomFlowDataVo> rlist = new ArrayList<CustomFlowDataVo>();
		//参数
		SysUser sysUser = SysUserUtil.getCurrentUser();
		if(StringUtils.isEmpty(userId)) {
			userId = sysUser.getId();
		}
		String userJob = sysUser.getOrgId()+"_"+sysUser.getPostId();
		//待办项数据
		Where where = Where.create().eq(CustomFlowDataAcc::getTmused, 1).eq(CustomFlowDataAcc::getCurrMark, 1).eq(CustomFlowDataAcc::getFlowModuleCode, moduleCode).in(CustomFlowDataAcc::getAccCode, userId, userJob);
		if(StringUtils.isNotEmpty(funCode)) {
			where.eq(CustomFlowDataAcc::getFlowFunCode, funCode);
		}
		List<CustomFlowDataAcc> list = srv.queryData(CustomFlowDataAcc.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			
			List<String> dataIdList = new ArrayList<String>();//主数据ID
			for (CustomFlowDataAcc obj : list) {
				if(!dataIdList.contains(obj.getFlowDataId())) {
					dataIdList.add(obj.getFlowDataId());
				}
			}
			
			//获取主数据
			Where wheredata = Where.create().in(CustomFlowData::getId, dataIdList.toArray());
			List<CustomFlowData> dataList = srv.queryData(CustomFlowData.class, wheredata, null, null);

//			String flowTypeCode = this.getFlowTypeCodeByFunCode(funCode);

//			SimpleFlowModel sfm = modelFactory.getModelInstance(moduleCode);
			CustomFlowMondule moduleBean = queryFlowMonduleByModuleCode(moduleCode);

			List<CustomFlowType> flowTypeList = getFlowTypeList(moduleCode);
			Map<String, CustomFlowType> flowTypeMap = StringUtils.isEmpty(flowTypeList) ? null : flowTypeList.stream().collect(Collectors.toMap(CustomFlowType::getFlowTypeCode, v->v, (k1, k2) -> k1));

			//补全相关属性
//			SimpleFlowModel sfm = this.getInstanceByModuleCode(moduleCode, flowTypeCode);

			String moduleName = null;
//			LinkedHashMap<String,CustomApplicationVo> funMap = null;
			if(moduleBean!=null) {
				moduleName = moduleBean.getModuleName();
//				funMap = sfm.getApplicationMap();//获取功能map
			}
			List<CustomFlowMonduleFun> moduleFunList = this.getModuleFunList(moduleCode, null);
			Map<String, CustomFlowMonduleFun> funMap = StringUtils.isEmpty(moduleFunList) ? null : moduleFunList.stream().collect(Collectors.toMap(CustomFlowMonduleFun::getId, v->v, (k1, k2) -> k1));

			for (CustomFlowData fd : dataList) {
				CustomFlowDataVo vo = ObjUtils.copyTo(fd, CustomFlowDataVo.class);
//				CustomApplicationVo obj = funMap==null?null:funMap.get(fd.getFlowFunCode());
//				CustomFlowMonduleFun fun = funMap == null ? null : funMap.get(fd.getFlowFunCode());
//				CustomFlowTypeVo flowType = fun == null ? null : modelFactory.getFlowType(moduleCode, fun.getFlowTypeCode());
				vo.setFlowModuleName(moduleName);
//				vo.setFlowFunName(obj==null?"":obj.getApplicationName());
				if (StringUtils.isEmpty(vo.getFlowFunName())) {
					CustomFlowMonduleFun fun = funMap == null ? null : funMap.get(fd.getFlowFunCode());
					vo.setFlowFunName(fun==null?"":fun.getFunName());
				}
//				vo.setFlowFunName(fun==null?"":fun.getFunName());
				vo.setName(fd.getBusinessDataName());
//				if(obj!=null) {
//					vo.setUrlPc(obj.getTodoUrl());
//					vo.setApplyPc(StringUtils.isNotEmpty(vo.getUrlPc()));
//					vo.setUrlApp(obj.getAppUrl());
//					vo.setApplyApp(StringUtils.isNotEmpty(vo.getUrlApp()));
//					vo.setNoVote(obj.getNoVote());
//				}else {
//					vo.setNoVote(false);
//				}
				CustomFlowType flowType = flowTypeMap == null ? null : flowTypeMap.get(fd.getFlowTypeCode());
				if (flowType != null) {
					vo.setUrlPc(flowType.getWebTodoInfoUrl());
					vo.setApplyPc(StringUtils.isNotEmpty(vo.getUrlPc()));
					vo.setUrlApp(flowType.getAppTodoInfoUrl());
					vo.setApplyApp(StringUtils.isNotEmpty(vo.getUrlApp()));
					vo.setNoVote(flowType.getNoVote() != null && flowType.getNoVote() == 1);
				}else {
					vo.setNoVote(false);
				}
				rlist.add(vo);
			}
		}
		
		
		return rlist;
	}

	/**
	 * @category 获取模块功能列表
	 * @return
	 */
	@Override
	public List<CustomFlowMonduleFun> getModuleFunList(String moduleCode, String businessCode) {
		return getModuleFunList(moduleCode, businessCode, null);
	}
	
	/**
	 * @category 获取模块功能列表
	 * @return
	 */
	@Override
	public List<CustomFlowMonduleFun> getModuleFunList(String moduleCode, String businessCode, String flowTypeCode) {
		if (StringUtils.isEmpty(moduleCode)) {
			return null;
		}
		Where where = Where.create().eq(CustomFlowMonduleFun::getModuleCode, moduleCode);
		if (StringUtils.isNotEmpty(businessCode)) {
			where.eq(CustomFlowMonduleFun::getBusinessCode, businessCode);
			//按业务代码查时，只查询非开放式的功能
			where.isNull(CustomFlowMonduleFun::getCustomType);
		}
		if (StringUtils.isNotEmpty(flowTypeCode)) {
			where.eq(CustomFlowMonduleFun::getFlowTypeCode, flowTypeCode);
		}
		where.eq(CustomFlowMonduleFun::getTmused, 1);
		Order order = Order.create().orderByAsc(CustomFlowMonduleFun::getTmsort);
		order.orderByAsc(CustomFlowMonduleFun::getId);
		return srv.queryData(CustomFlowMonduleFun.class, where, order, null);
//		List<Map<String,String>> rlist = new ArrayList<Map<String,String>>();
//
//		SimpleFlowModel sfm = SimpleFlowModelFactory.getInstanceByModuleCode(moduleCode);
//		if(sfm!=null) {
//			LinkedHashMap<String,CustomApplicationVo> cmap = sfm.getApplicationMap();
//			for (String code : cmap.keySet()) {
//				CustomApplicationVo name = cmap.get(code);
//				Map<String, String> map = new HashMap<String, String>();
//				map.put("code", name.getApplicationCode());
//				map.put("funName", name.getApplicationName());
//				rlist.add(map);
//			}
//		}
//
//		return rlist;
	}


	/**
	 * @category 根据参数加载执行流程信息
	 * @param param id or businessDataCode
	 * @return
	 */
	@Override
	public CustomFlowDataVo loadFlowDataInfo(ParamDto param) {
		CustomFlowData data = null;
		
		String id = param.getId();
		if(StringUtils.isEmpty(id)) {
			String bid = param.getBusinessDataCode();
			if(StringUtils.isNotEmpty(bid)) {
				Where where = Where.create().eq(CustomFlowData::getTmused, 1);
				where.eq(CustomFlowData::getBusinessDataCode, bid);
				Order order = Order.create();
				order.orderByDesc(CustomFlowData::getCreateTime);
				order.orderByDesc(CustomFlowData::getId);
				List<CustomFlowData> list = srv.queryData(CustomFlowData.class, where, order, null);
				//时间倒序，取最新一次的记录
				if(StringUtils.isNotEmpty(list)) {
					data = list.get(0);
				}
			}
		}else {
			data = srv.queryObjectById(CustomFlowData.class, id);
		}
		if(data!=null) {
			List<CustomFlowStepVo> stepList = new ArrayList<>();
			CustomFlowDataVo vo = ObjUtils.copyTo(data, CustomFlowDataVo.class);
			List<CustomFlowStep> slist = getFlowStepList(data.getFlowModuleCode(), data.getFlowFunCode(), data.getSourceBusinessCode());
			
			Where wheresd = Where.create().eq(CustomFlowDataAcc::getTmused, 1).eq(CustomFlowDataAcc::getFlowDataId, data.getId());
			Order order = Order.create().orderByAsc(CustomFlowDataAcc::getStepNo).orderByAsc(CustomFlowDataAcc::getId);
			List<CustomFlowDataAcc> dataAccListAll = srv.queryData(CustomFlowDataAcc.class, wheresd, order, null);
			if (StringUtils.isEmpty(dataAccListAll)) {
				return null;
			}
			List<CustomFlowDataAcc> dalist = new ArrayList<>();
			Map<String, CustomFlowDataAcc> smap = new HashMap<>();
			Map<Integer, CustomFlowDataAcc> degreeMap = new HashMap<>();
			Integer currentStepNo = null;
			String curStepId = null;

			for (CustomFlowDataAcc acc : dataAccListAll) {
				Integer stepNo = acc.getStepNo();
				if (acc.getDegreeMark() != null && acc.getDegreeMark() == 1) {
					//已处理
					dalist.add(acc);
					smap.put(acc.getStepId(), acc);
					if (stepNo != null) {
						degreeMap.put(stepNo, acc);
					}
				}
				if (acc.getCurrMark() != null && acc.getCurrMark() == 1) {
					curStepId = acc.getStepId();
					currentStepNo = stepNo;
				}
			}
			for (CustomFlowStep step : slist) {
				CustomFlowStepVo svo = ObjUtils.copyTo(step, CustomFlowStepVo.class);
				CustomFlowDataAcc acc = smap.get(step.getId());
				if(acc!=null) {
					//已处理节点
					svo.setDegreeStatus(1);
					svo.setDegreeResult(acc.getDegreeResult());
					svo.setDegreeTime(DateTimeUtils.formatDateTime(acc.getUpdateTime()));
					svo.setDegreeUserName(acc.getDegreeUserName());
					svo.setActive(false);
				} else {
					svo.setDegreeStatus(0);
					svo.setToDegree(step.getAccShow());
                    svo.setActive(step.getId().equals(curStepId));
				}
				//两个属性不知道哪个有用，都赋值
				if (StringUtils.isEmpty(svo.getStepName()) && StringUtils.isNotEmpty(svo.getAccShow())) {
					svo.setStepName(svo.getAccShow());
				}
				if (StringUtils.isEmpty(svo.getAccShow()) && StringUtils.isNotEmpty(svo.getStepName())) {
					svo.setAccShow(svo.getStepName());
				}
				stepList.add(svo);
			}

			LinkedHashMap<Integer, CustomFlowStepVo> stepDataMap = new LinkedHashMap<>();
			for (CustomFlowDataAcc acc : dataAccListAll) {
				Integer stepNo = acc.getStepNo();
				CustomFlowDataAcc degreeAcc = degreeMap.get(stepNo);
				if (stepNo != null && degreeAcc != null) {
					//已处理的步骤节点
					CustomFlowStepVo customFlowStepVo = stepDataMap.get(stepNo);
					if (customFlowStepVo == null) {
						customFlowStepVo = new CustomFlowStepVo();
						customFlowStepVo.setStepNo(stepNo);
						if (degreeAcc.getAccType() != null) {
							customFlowStepVo.setStepType(String.valueOf(degreeAcc.getAccType()));
						}
						customFlowStepVo.setAccType(degreeAcc.getAccType());
						customFlowStepVo.setDegreeStatus(1);
						if (StringUtils.isNotEmpty(degreeAcc.getDegreeUserName())) {
							customFlowStepVo.setDegreeUserName(degreeAcc.getDegreeUserName());
						}
						customFlowStepVo.setDegreeResult(degreeAcc.getDegreeResult());
						customFlowStepVo.setDegreeTime(DateTimeUtils.formatDateTime(degreeAcc.getUpdateTime()));
						stepDataMap.put(stepNo, customFlowStepVo);
					}
					continue;
				}

				CustomFlowStepVo customFlowStepVo = stepDataMap.get(stepNo);
				if (customFlowStepVo == null) {
					customFlowStepVo = new CustomFlowStepVo();
					customFlowStepVo.setStepNo(stepNo);
					if (acc.getAccType() != null) {
						customFlowStepVo.setStepType(String.valueOf(acc.getAccType()));
					}
					customFlowStepVo.setAccType(acc.getAccType());
					stepDataMap.put(stepNo, customFlowStepVo);
//					customFlowStepVo = ObjUtils.copyTo(acc, CustomFlowStepVo.class);
				}
				String accName = acc.getAccName();
				String oldAccShow = customFlowStepVo.getAccShow();
				String newAccShow = (StringUtils.isNotEmpty(oldAccShow) ? oldAccShow+"," : "") + accName;
				customFlowStepVo.setAccShow(newAccShow);
				customFlowStepVo.setStepName(newAccShow);
//				if (acc.getDegreeMark() != null && acc.getDegreeMark() == 1) {
//					//已处理
//					customFlowStepVo.setDegreeStatus(1);
//					if (StringUtils.isNotEmpty(acc.getDegreeUserName())) {
//						customFlowStepVo.setDegreeUserName(acc.getDegreeUserName());
//						customFlowStepVo.setDegreeResult(acc.getDegreeResult());
//						customFlowStepVo.setDegreeTime(DateTimeUtils.formatDateTime(acc.getUpdateTime()));
//					}
//				} else {
					customFlowStepVo.setDegreeStatus(0);
//				}
//				if (acc.getCurrMark() != null && acc.getCurrMark() == 1) {
//					//当前处理节点
//					customFlowStepVo.setDegreeStatus(0);
//					customFlowStepVo.setActive(true);
//				}
				if (currentStepNo != null && currentStepNo.equals(stepNo)) {
					//当前处理节点
//					customFlowStepVo.setDegreeStatus(0);
					customFlowStepVo.setActive(true);
				}
			}

			vo.setStepList(stepList);
			vo.setDegreeList(dalist);
			vo.setStepDataList(new ArrayList<>(stepDataMap.values()));
			return vo;
		}
		return null;
	}

	/**
	 * @category 获取类型列表
	 * @return
	 */
	@Override
	public List<CustomFlowType> getFlowTypeList(String moduleCode) {
		if (StringUtils.isEmpty(moduleCode)) {
			return null;
		}
		String redisKey = "SYSTEM:SIMPLEFLOW:FLOWTYPE:"+moduleCode;
		List<CustomFlowType> result = redisUtil.getClassList(CustomFlowType.class, redisKey);
		if (result == null) {
			Where where = Where.create().eq(CustomFlowType::getModuleCode, moduleCode);
			Order order = Order.create().orderByAsc(CustomFlowType::getTmsort);
			order.orderByAsc(CustomFlowType::getId);
			result = srv.queryData(CustomFlowType.class, where, order, null);
			if (result != null) {
				redisUtil.setObject(redisKey, result, redisTimeOut);
			}
		}
		return result;
//		Where where = Where.create().eq(CustomFlowType::getModuleCode, moduleCode);
//		Order order = Order.create().orderByAsc(CustomFlowType::getTmsort);
//		order.orderByAsc(CustomFlowType::getId);
//		return srv.queryData(CustomFlowType.class, where, order, null);

	}

	@Override
	public CustomFlowMondule queryFlowMonduleByModuleCode (String moduleCode) {
		if (StringUtils.isEmpty(moduleCode)) {
			return null;
		}
		String redisKey = "SYSTEM:SIMPLEFLOW:MODULE:"+moduleCode;
		CustomFlowMondule result = redisUtil.getClassObject(CustomFlowMondule.class, redisKey);
		if (result == null) {
			Where where = Where.create().eq(CustomFlowMondule::getModuleCode, moduleCode);
			List<CustomFlowMondule> list = srv.queryData(CustomFlowMondule.class, where, null, null);
			if (StringUtils.isNotEmpty(list)) {
				result = list.get(0);
				redisUtil.setObject(redisKey, result, redisTimeOut);
			}
		}
		return result;
	}

	private CustomFlowMonduleFun queryFlowMonduleFunById (String funCode) {
		if (StringUtils.isEmpty(funCode)) {
			return null;
		}
		return srv.queryObjectById(CustomFlowMonduleFun.class, funCode);
	}

	private String getFlowTypeCodeByFunCode (String funCode) {
		CustomFlowMonduleFun fun = queryFlowMonduleFunById(funCode);
		if (fun == null) {
			return null;
		}
		return fun.getFlowTypeCode();
	}

	/**
	 * 查询我参与的流程实例
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<CustomFlowData> queryMyDegreeFlowDataList (String startDate, String endDate) {

		SysUser user = SysUserHolder.getCurrentUser();
		if (user == null) {
			return null;
		}
		if (StringUtils.isAnyEmpty(startDate, endDate)) {
			return null;
		}
		Date sdate = DateTimeUtils.parseDate(startDate+" 00:00:00");
		Date edate = DateTimeUtils.parseDate(endDate+" 23:59:59");
		String sql = "select * from CUSTOM_FLOW_DATA where TMUSED=1 and (CREATE_TIME between ? and ?) " +
				"and ID in (select distinct FLOW_DATA_ID from CUSTOM_FLOW_DATA_ACC where TMUSED=1 and DEGREE_MARK=1 " +
				"and DEGREE_USER_ID=?)";
		List<Object> vars = new ArrayList<>();
		vars.add(sdate);
		vars.add(edate);
		vars.add(user.getId());
		List<CustomFlowData> result = srv.rawQueryList(CustomFlowData.class, sql, vars.toArray());


		return null;
	}


//	//获取模块map TODO
//	private Map<String, String> getFunMap() {
//		Map<String, String> rmap = new HashMap<String, String>();
//		return rmap;
//	}
//	//获取功能map TODO
//	private Map<String, String> getModuleMap() {
//		Map<String, String> rmap = new HashMap<String, String>();
//		return rmap;
//	}

}
