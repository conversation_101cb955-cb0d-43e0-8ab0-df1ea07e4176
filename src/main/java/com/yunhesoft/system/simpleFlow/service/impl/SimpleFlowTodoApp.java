package com.yunhesoft.system.simpleFlow.service.impl;

import com.yunhesoft.core.common.model.BaseTodoApp;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.simpleFlow.service.ISimpleFlowService;

public class SimpleFlowTodoApp extends BaseTodoApp {

	/**
	 *	获取待办项数量
	 * @return
	 */
	public Integer getTodoInfoCount(String moduleCode) {
		int count = SpringUtils.getBean(ISimpleFlowService.class).getFlowTodo(moduleCode);
		return count;
	}
	public Integer getTodoInfoCount() {
		int count = SpringUtils.getBean(ISimpleFlowService.class).getFlowTodo(null);
		return count;
	}

}

