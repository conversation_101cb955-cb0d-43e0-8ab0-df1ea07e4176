package com.yunhesoft.system.simpleFlow.util;

import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowDataVo;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowTypeVo;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomUserVo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Service
public class DemoFlow extends SimpleFlowModel {

	@Override
	public String getTodoIcon() {
		// TODO Auto-generated method stub
		return "el-icon-document";
	}

	@Override
	public String getTodoName() {
		return "测试审核";
	}

	@Override
	public String getModuleCode() {
		// TODO Auto-generated method stub
		return "demo";
	}

	@Override
	public String getModuleName() {
		// TODO Auto-generated method stub
		return "测试流程";
	}

//	@Override
//	public String getCallbackApi() {
//		return "";
//	}

	@Override
	public List<CustomFlowTypeVo> getFlowTypeList() {

		List<CustomFlowTypeVo> list = new ArrayList<>();
		CustomFlowTypeVo type1 = new CustomFlowTypeVo();
		type1.setFlowTypeCode("type1");
		type1.setFlowTypeName("类型1");
		type1.setWebTodoInfoUrl("/df/dfsf/sdf");
		type1.setAppTodoInfoUrl("/a/b/c");
		CustomFlowTypeVo type2 = new CustomFlowTypeVo();
		type2.setFlowTypeCode("type2");
		type2.setFlowTypeName("类型2");
		type2.setWebTodoInfoUrl("/3423/dfs234f/s2df");
		type2.setAppTodoInfoUrl("/123/345/345");
		list.add(type1);
		list.add(type2);
		return list;
	}

	@Override
	public LinkedHashMap<String, String> getCustomUserMap() {
		// TODO Auto-generated method stub
		LinkedHashMap<String, String> result = new LinkedHashMap<String, String>();
		result.put("AAA", "区域经理");
		result.put("BBB", "单元主管");
		result.put("CCC", "库管员");
		return result;
	}

	@Override
	public List<CustomUserVo> getCustomUserByCode(List<String> customCodeList,String businessCode,String customUserCode) {
		// TODO Auto-generated method stub
		List<CustomUserVo> result = new ArrayList<CustomUserVo>();
		if(customCodeList!=null && customCodeList.size()>0){
			for(String temp:customCodeList) {
				if("AAA".equals(temp)) {
					CustomUserVo bean = new CustomUserVo();
					bean.setType(1);
					bean.setUserId("AAA");
					bean.setUserName("张三"+businessCode);
					result.add(bean);
				}else if("BBB".equals(temp)) {
					CustomUserVo bean = new CustomUserVo();
					bean.setType(2);
					bean.setPostOrgCode("012000121");
					bean.setPostId("BBB");
					bean.setPostName("班长"+businessCode);
					result.add(bean);
				}else if("CCC".equals(temp)) {
					CustomUserVo bean = new CustomUserVo();
					bean.setType(1);
					bean.setUserId("AAA");
					bean.setUserName("张三"+businessCode);
					result.add(bean);
					CustomUserVo bean2 = new CustomUserVo();
					bean2.setType(2);
					bean2.setPostOrgCode("012000121");
					bean2.setPostId("BBB");
					bean2.setPostName("班长"+businessCode);
					result.add(bean2);
				}
			}
		}
		return result;
	}


	@Override
	public boolean afterDegreeFlow(CustomFlowDataVo auditResult) {
		// TODO Auto-generated method stub
		boolean result = true;
		if("SH1".equals(auditResult.getFlowFunCode())) {
			//serv.audit(auidBean.getBusinessCode(),auidBean.getBusinessDataCode())
		}else if("SH1".equals(auditResult.getFlowFunCode())) {
			
		}
		return result;
	}

}
