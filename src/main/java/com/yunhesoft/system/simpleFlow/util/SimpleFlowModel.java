package com.yunhesoft.system.simpleFlow.util;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.menu.service.ISysMenuLibInitService;
import com.yunhesoft.system.simpleFlow.entity.dto.ParamDto;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowMondule;
import com.yunhesoft.system.simpleFlow.entity.po.CustomFlowType;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowDataVo;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomFlowTypeVo;
import com.yunhesoft.system.simpleFlow.entity.vo.CustomUserVo;
import com.yunhesoft.system.simpleFlow.entity.vo.SimpleFlowAuditBean;
import com.yunhesoft.system.simpleFlow.service.ISimpleFlowService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.util.*;

/**
 * 简单工作流抽象类
 * 每个模块继承本类，实现抽象方法
 * 在菜单初始化init方法中调用实现类的register()方法完成注册
 * 如SpringUtils.getBean(PrizeAuditModel.class).register();
 * <AUTHOR>
@Log4j2
@Service
public abstract class SimpleFlowModel{

	@Autowired
	private EntityService entityServ;
	@Autowired
	private ISysMenuLibInitService menuServ; // 菜单库
	@Autowired
	private ISimpleFlowService flowServ;

	@Autowired
    private RedisUtil redisUtil;

	@Autowired
	private Environment env;
	
	private static String execType="simpleFlow";//接口类型
	
	private static String execName="工作流（简化模式）";//接口类型

	/**
	 * 模块注册
	 * @category 
	 * <AUTHOR> 
	 * @param
	 * @param
	 */
	public final void register() {
		//注册待办
		String moduleCode = getModuleCode();
		String moduleName = getModuleName();
		String todokey = execType+"_"+moduleCode+"_todo";//待办编码
		String todoIcon = getTodoIcon();
		String todoName = getTodoName();

		String todoUrl = "system/simpleFlow/flowModuleTodo.vue?moduleCode="+moduleCode;//电脑端地址
//		String appUrl = getAppUrl();//"xxxxxApp?moduleCode="+getModuleCode();//手机端地址
		menuServ.initTodoModuleAndModule(execType, execName, execType+"_"+moduleCode, moduleName, "com.yunhesoft.system.simpleFlow.service.impl.SimpleFlowTodoApp");
		Map<String, String> dataMap = new HashMap<>();
		dataMap.put("icoClass", todoIcon);
		dataMap.put("todoComData", "[{name:\""+todokey+"\",url:\""+todoUrl+"\",span:24}]");
		dataMap.put("appComData", "");
		dataMap.put("todoColorData", "");
		dataMap.put("todoUrl", todoUrl);
//			dataMap.put("appURL", appUrl);
		menuServ.initTodo(todokey, todoName, "getTodoInfoCount(\""+moduleCode+"\")", "", "", dataMap);

		List<CustomFlowMondule> moduleList = entityServ.queryData(CustomFlowMondule.class, Where.create().in(CustomFlowMondule::getModuleCode, moduleCode), null, null);
		CustomFlowMondule moduleBean = StringUtils.isEmpty(moduleList) ? null : moduleList.get(0);
		if (moduleBean == null) {
			moduleBean = new CustomFlowMondule();
			moduleBean.setId(TMUID.getUID());
			moduleBean.setModuleCode(moduleCode);
			moduleBean.setModuleName(moduleName);
			moduleBean.setTodoIcon(todoIcon);
			moduleBean.setTodoName(todoName);
//			moduleBean.setCallbackApi(getCallbackApi());
			moduleBean.setSysAddress(getSystemAddress());
			moduleBean.setServiceName(getServiceName());
			moduleBean.setTodoTbarExtBtnUrl(getTodoTbarExtBtnUrl());
			entityServ.insert(moduleBean);
		} else {
			moduleBean.setModuleCode(moduleCode);
			moduleBean.setModuleName(moduleName);
			moduleBean.setTodoIcon(todoIcon);
			moduleBean.setTodoName(todoName);
			moduleBean.setSysAddress(getSystemAddress());
			moduleBean.setTodoTbarExtBtnUrl(getTodoTbarExtBtnUrl());
			String eurekaEnabled = env.getProperty("eureka.client.enabled");
			if ("true".equals(eurekaEnabled)) {
				//微服务启动时才更新服务名
				moduleBean.setServiceName(getServiceName());
			}
			entityServ.updateById(moduleBean);
		}
		int typeSortMax = 0;
		Map<String, CustomFlowType> oldTypeMap = new HashMap<>();
		List<CustomFlowType> oldTypeList = entityServ.queryData(CustomFlowType.class, Where.create().in(CustomFlowType::getModuleCode, moduleCode), null, null);
		if (StringUtils.isNotEmpty(oldTypeList)) {
			for (CustomFlowType type : oldTypeList) {
				oldTypeMap.put(type.getFlowTypeCode(), type);
				typeSortMax = Math.max(typeSortMax, type.getTmsort());
			}
		}
		List<CustomFlowType> insertTypeList = new ArrayList<>();
		List<CustomFlowType> updateTypeList = new ArrayList<>();
		List<CustomFlowTypeVo> flowTypeList = getFlowTypeList();
		if (StringUtils.isNotEmpty(flowTypeList)) {
			for (CustomFlowTypeVo flowType : flowTypeList) {
				String flowTypeCode = flowType.getFlowTypeCode();
				if (StringUtils.isEmpty(flowTypeCode)) {
					continue;
				}
				CustomFlowType typeBean = oldTypeMap.get(flowTypeCode);
				if (typeBean == null) {
					typeBean = new CustomFlowType();
					typeBean.setId(TMUID.getUID());
					typeBean.setModuleCode(moduleCode);
					typeBean.setFlowTypeCode(flowTypeCode);
					typeBean.setTmsort(++typeSortMax);
					insertTypeList.add(typeBean);
				} else {
					updateTypeList.add(typeBean);
				}
				typeBean.setFlowTypeName(flowType.getFlowTypeName());
				typeBean.setWebTodoInfoUrl(flowType.getWebTodoInfoUrl());
				typeBean.setAppTodoInfoUrl(flowType.getAppTodoInfoUrl());
			}
		}

		if (StringUtils.isNotEmpty(insertTypeList)) {
			entityServ.insertBatch(insertTypeList);
		}
		if (StringUtils.isNotEmpty(updateTypeList)) {
			entityServ.updateByIdBatch(updateTypeList);
		}
		redisUtil.delete("SYSTEM:SIMPLEFLOW:FLOWTYPE:"+moduleCode);
		redisUtil.delete("SYSTEM:SIMPLEFLOW:MODULE:"+moduleCode);
	}

	public final String getServiceName() {
		return env.getProperty("spring.application.name");
	}

	public final String getSystemAddress() {
		String ip = env.getProperty("server.ip");
		if (StringUtils.isEmpty(ip)) {
            try {
                ip = InetAddress.getLocalHost().getHostAddress();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
		String port = env.getProperty("server.port");
		if (StringUtils.isEmpty(port)) {
			port = "8080";
		}
		String context = env.getProperty("server.servlet.context-path");
		if (StringUtils.isEmpty(context)) {
			context = "";
		} else {
			if (!context.startsWith("/")) {
				context = "/" + context;
			}
		}
		String http = "http";
		if ("true".equalsIgnoreCase(env.getProperty("server.ssl.enabled"))) {
			http = "https";
		}
		String url = http + "://" + ip + ":" + port + context;
		return url;
	}

	/**
	 * 获取待办列表图标样式 https://element.eleme.cn/#/zh-CN/component/icon中查找即可
	 * @category 
	 * <AUTHOR> 
	 * @return
	 */
	public abstract String getTodoIcon();

	/**
	 * 获取待办名称 必须有返回值
	 * @category
	 * <AUTHOR> @return
	 */
	public abstract String getTodoName();


	/**
	 * 获取待办页面tbar扩展按钮组件路径
	 * @category
	 * <AUTHOR> @return
	 */
	public String getTodoTbarExtBtnUrl() {
		return "";
	}

	/**
	 * 获取模块编码 必须有返回值
	 * @category 
	 * <AUTHOR> 
	 * @return
	 */
	public abstract String getModuleCode();
	/**
	 * 获取模块名称 必须有返回值
	 * @category 
	 * <AUTHOR> 
	 * @return
	 */
	public abstract String getModuleName();

	/**
	 * 回调函数api 必须有返回值，controller接口地址(post请求)，controller接收参数可继承SimpleFlowCallbackController接口
	 * 返回值需是完整路径，https://xxx.xxx.x.xx:8887/abc/def
	 * 可通过本类中的getSystemAddress获取当前系统地址 后面直接拼接接口相对路径即可
	 * @return
	 */
//	public abstract String getCallbackApi();

	/**
	 * 模块下的流程类型集合
	 * 至少返回一个，需指定PC及手机待办地址，todoUrl和appUrl
	 * @category
	 * @return
	 */
	public abstract List<CustomFlowTypeVo> getFlowTypeList();


	/**
	 * 获取自定义人员列表
	 * @category 获取自定义人员列表 （比如区域经理、地区主管等）
	 * <AUTHOR> 
	 * @return LinkedHashMap key customUserCode value customUserName
	 */
	public abstract LinkedHashMap<String,String> getCustomUserMap();
	/**
	 * 根据自定义人员编码和应用编码获取真实的人员或岗位
	 * @category 
	 * <AUTHOR> 
	 * @param customCodeList 自定义人员编码列表
	 * @param businessCode 业务编码（比如机构代码等，用于获取对应的人员或岗位）
	 * @param customUserCode 自定义人员获取编码（特殊的编码，格式自定义，配合businessCode获取人员或岗位）
	 * @return List<CustomUserVo>
	 */
	public abstract List<CustomUserVo> getCustomUserByCode(List<String> customCodeList,String businessCode,String customUserCode);

	/**
	 * 启动流程
	 * @category 
	 * <AUTHOR> 
	 * @param param ParamDto param = new ParamDto();
					param.setModuleCode(xxxxxx);//模块编码
					param.setFunCode(xxxxxx);//功能编码
					param.setBusinessCode(xxxxxx);//业务编码 例如：xx机构
					param.setBusinessDataCode(xxxxxx);//数据 对应记录的数据
					param.setBusinessDataName(xxxxxx);//待办业务名 例如：小队出库
	 * @return CustomFlowDataVo 如果返回空值，则代表流程为设置，需要提示设置流程
	 */
	public final CustomFlowDataVo startFlow(ParamDto param) {
		CustomFlowDataVo result = null;
		if(param!=null) {
			param.setModuleCode(getModuleCode());//启动时，只能启动本模块的类，不能启动其他模块的
			result = flowServ.startFlow(param);
		}
		return result;
	}

	/**
	 * 运行流程（单条审核）
	 * @category 
	 * <AUTHOR> 
	 * @param param
	 * @return CustomFlowDataVo /返回bean为null 或 bean.RunStatus为null：未成功，CustomFlowDataVo.getRunStatus()//运行状态0、待审核； 1、审核中； 2、审核通过； -1、审核否决；
	 */
	public final CustomFlowDataVo degreeFlow(SimpleFlowAuditBean param) {
		return flowServ.degreeFlow(param);
//		CustomFlowDataVo reuslt = null;
//		ParamDto auditparam = new ParamDto();
//		auditparam.setIdstr(param.getFlowId());////流程实例ID，批量用逗号分割
//		auditparam.setDegreeMark(param.getAuditStatus());//处理标识 1通过 -1否决
//		auditparam.setDegreeDesc(param.getAuditDesc());//审核描述，前台给定
//		List<CustomFlowDataVo> auditList = flowServ.degreeFlowStep(auditparam);
//		if (StringUtils.isNotEmpty(auditList)) {
//			reuslt=auditList.get(0);
//			try {
//				Map<String, String> headers = new HashMap<>();
//				headers.put("Authorization", "Bearer "+SysUserHolder.getCurrentToken());
//				httpSrv.post("asdf", headers, reuslt);
//				boolean rv = afterDegreeFlow(reuslt);//这里考虑执行后出错的情况
//				if(!rv) {
//					Integer jg = flowServ.toUpStepFlow(param.getFlowId());//流程返回上一步
//					reuslt.setAfterDegreeResult(false);
//					reuslt.setErrorInfo(reuslt.getErrorInfo());//错误信息
//					if(jg!=1) {
//						reuslt.setErrorInfo("审核异常终止，请联系管理员！");//错误信息
//					}
//				}
//			}catch(Exception e){
//				reuslt.setAfterDegreeResult(false);
//				reuslt.setErrorInfo("审核异常终止，请联系管理员！");//错误信息
//				log.error("", e);
//			}
//		}else {
//			reuslt = new CustomFlowDataVo();
//			reuslt.setBusinessDataCode(param.getFlowId());
//			reuslt.setAfterDegreeResult(false);
//			reuslt.setRunStatus(null);//审核异常，没有运行状态！！
//			reuslt.setErrorInfo("审核操作出现异常，未能生成审核数据！");
//		}
//		return reuslt;
	}

	/**
	 * @category 撤回流程
	 * @param
	 * @return 1成功 0已进行无法撤回 -1程序出错未撤回
	 */
	public final Integer backFlow(String flowId) {
		return flowServ.backFlow(flowId);
	}
	/**
	 * 批量运行流程（批量审核）
	 * @category 
	 * <AUTHOR> 
	 * @param auditList
	 */
	public final List<CustomFlowDataVo> degreeFlowBatch(List<SimpleFlowAuditBean> auditList) {
//		List<CustomFlowDataVo> result = new ArrayList<CustomFlowDataVo>();;
//		if (StringUtils.isNotEmpty(auditList)) {
//			for(SimpleFlowAuditBean temp:auditList) {//批量审核时，需要按记录单次审核，避免库存计算并发
//				result.add(degreeFlow(temp));//这里不会返回空，直接返回信息即可
//			}
//		}
//		return result;
		return flowServ.auditFlowBatch(auditList, getModuleCode());
	}
	
	
	/**
	 * 审核后的数据处理
	 * @category 
	 * <AUTHOR> 
	 * @param
	 *
	 * @return
	 * 数据id
	 * String dataId = auditResult.getBusinessDataCode();
	 * 数据流程类型
	 * String flowType = auditResult.getFlowTypeCode();
	 * 运行状态：1、审核中； 2、审核通过； -1、审核否决；
	 * Integer runStatus = auditResult.getRunStatus();
	 */
	public abstract boolean afterDegreeFlow(CustomFlowDataVo auditResult);

}

