package com.yunhesoft.system.professional.service;

import java.util.List;

import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.professional.entity.dto.SysProfessionalInfoQueryDto;

import com.yunhesoft.system.professional.entity.dto.SysProfessionalInfoSaveDto;

import com.yunhesoft.system.professional.entity.vo.SysProfessionalInfoVo;


/**
 * 接口
 * <AUTHOR>
 *
 */
public interface ISysProfessionalInfoService {
	 /**
	 * 查询数据
	 * 
	 * @param dto
	 * @return
	 */
	public List<SysProfessionalInfoVo> getData(SysProfessionalInfoQueryDto dto);
	 /**
	 * 通过ID查询
	 * 
	 * @param id
	 * @return
	 */
	public SysProfessionalInfoVo getBean(String id);
	
	/**
	 * 保存全部数据
	 * @param beans
	 * @return
	 */
	public String save(List<SysProfessionalInfoSaveDto> beans);
	 /**
	 * 删除数据
	 * 
	 * @param bean
	 * @return
	 */
	public String delete(List<SysProfessionalInfoSaveDto> beans);
	
	
	/**
	 * 查询人员变动后，专业查询，如果人员有专业，不在查询，如果没有专业，查询岗位
	 * @param changeDt
	 * @param userId
	 * @return
	 */
	public EmployeeVo getUserChange(String changeDt, String userId);
	
	/**
	 * @category 根据变动查询人员专业，返回人员对象和对应专业，取professionalInfoId，多个专业用逗号分割
	 * @param changeDt
	 * @param userId
	 * @return
	 */
	public EmployeeVo getUserChangeProfessionalObj(String changeDt, String userId);
	
	
}
