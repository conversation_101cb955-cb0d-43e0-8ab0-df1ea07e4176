package com.yunhesoft.system.professional.service.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.druid.util.StringUtils;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.ISysEmployeeChangeInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.professional.entity.dto.SysProfessionalInfoQueryDto;
import com.yunhesoft.system.professional.entity.dto.SysProfessionalInfoSaveDto;
import com.yunhesoft.system.professional.entity.po.SysProfessionalInfo;
import com.yunhesoft.system.professional.entity.vo.SysProfessionalInfoVo;
import com.yunhesoft.system.professional.service.ISysProfessionalInfoService;

@Service
public class SysProfessionalInfoServiceImpl implements ISysProfessionalInfoService {
	@Autowired
	private EntityService entityService;

	@Autowired
	private ISysEmployeeChangeInfoService SysProfessionalInfoServiceImpl;

	@Override
	public List<SysProfessionalInfoVo> getData(SysProfessionalInfoQueryDto dto) {
		Where where = Where.create();
		if (dto != null) {
			if (!StringUtils.isEmpty(dto.getId())) {
				where.eq(SysProfessionalInfo::getId, dto.getId());
			}
			if (!StringUtils.isEmpty(dto.getProfessionalName())) {
				where.like(SysProfessionalInfo::getProfessionalName, dto.getProfessionalName());
			}
		}
		Order order = Order.create();
		List<SysProfessionalInfo> configs = entityService.queryList(SysProfessionalInfo.class, where, order);
		List<SysProfessionalInfoVo> _list = new ArrayList<SysProfessionalInfoVo>();
		for (SysProfessionalInfo saveDto : configs) {
			// 数据
			SysProfessionalInfoVo saveObj = new SysProfessionalInfoVo();
			// 复制
			ObjUtils.copyTo(saveDto, saveObj);
			_list.add(saveObj);
		}
		return _list;
	}

	@Override
	public SysProfessionalInfoVo getBean(String id) {
		SysProfessionalInfo obj = entityService.queryObjectById(SysProfessionalInfo.class, id);
		// 数据
		SysProfessionalInfoVo saveObj = new SysProfessionalInfoVo();
		// 复制
		ObjUtils.copyTo(obj, saveObj);
		return saveObj;
	}

	@Override
	public String save(List<SysProfessionalInfoSaveDto> beans) {
		Map<String, String> idMap = new LinkedHashMap<String, String>();
		for (SysProfessionalInfoSaveDto saveDto : beans) {
			String professionalName = saveDto.getProfessionalName();
			String id = saveDto.getId();
			if (idMap.containsKey(professionalName)) {
				return "[" + professionalName + "]名称重复";
			} else {
				idMap.put(professionalName, id);
			}
		}
		SysProfessionalInfoQueryDto dto = new SysProfessionalInfoQueryDto();
		List<SysProfessionalInfoVo> list2 = getData(dto);
		for (SysProfessionalInfoVo bean : list2) {
			String professionalName = bean.getProfessionalName();
			String id = bean.getId();
			if (idMap.containsKey(professionalName)) {
				String _id = idMap.get(professionalName);
				if (!_id.equals(id)) {// id不等
					return "[" + professionalName + "]名称重复";
				}
			} else {
				idMap.put(professionalName, id);
			}
		}

//		List<SysProfessionalInfo> infos = new ArrayList<SysProfessionalInfo>();
//		for (SysProfessionalInfoSaveDto saveBean : beans) {
//			// 数据
//			SysProfessionalInfo saveObj = new SysProfessionalInfo();
//			ObjUtils.copyTo(saveBean, saveObj);
//			saveObj.setId(TMUID.getUID());
//			infos.add(saveObj);
//		}

		List<SysProfessionalInfo> saveList = new ArrayList<SysProfessionalInfo>();
		List<SysProfessionalInfo> updataList = new ArrayList<SysProfessionalInfo>();
		for (SysProfessionalInfoSaveDto bean : beans) {
			SysProfessionalInfo saveObj = new SysProfessionalInfo();
			ObjUtils.copyTo(bean, saveObj);
			if (!StringUtils.isEmpty(bean.getId())) {
				updataList.add(saveObj);
			} else {
				saveObj.setId(TMUID.getUID());
				saveList.add(saveObj);
			}
		}
		if (saveList.size() > 0) {
			entityService.insertBatch(saveList);
		} else if (updataList.size() > 0) {
			entityService.updateBatch(updataList);
		}
		return "";
	}

	@Override
	public String delete(List<SysProfessionalInfoSaveDto> bean) {
		List<SysProfessionalInfo> infos = new ArrayList<SysProfessionalInfo>();
		for (SysProfessionalInfoSaveDto saveBean : bean) {
			// 数据
			SysProfessionalInfo saveObj = new SysProfessionalInfo();
			ObjUtils.copyTo(saveBean, saveObj);
			infos.add(saveObj);
		}
		entityService.deleteByIdBatch(infos);
		return "";
	}

	@Override
	public EmployeeVo getUserChange(String changeDt, String userId) {
		EmployeeVo employeeVo = SysProfessionalInfoServiceImpl.getUserChange(changeDt, userId, true);
		//当人员在查询时间范围之外返回null
		if(employeeVo!=null) {
			String professionalInfoIds = employeeVo.getProfessionalInfoId();
			if (professionalInfoIds == null || professionalInfoIds.length() == 0) {
				String postId = employeeVo.getPostTmuid();
				SysPost post = entityService.queryObjectById(SysPost.class, postId);
				if (post != null) {
					String _professionalInfoIds = post.getProfessionalInfoId();
					if (_professionalInfoIds != null && _professionalInfoIds.length() > 0) {
						employeeVo.setProfessionalInfoId(_professionalInfoIds);
					}
				}
			}
		}
		return employeeVo;
	}

	@Override
	public EmployeeVo getUserChangeProfessionalObj(String changeDt, String userId) {
		EmployeeVo employeeVo = SysProfessionalInfoServiceImpl.getUserChange(changeDt, userId, true);
		//当人员在查询时间范围之外返回null
		if(employeeVo!=null) {
			if(com.yunhesoft.core.utils.StringUtils.isNotEmpty(employeeVo.getProfessionalInfoId())) {//使用原个人专业
			}else if(com.yunhesoft.core.utils.StringUtils.isNotEmpty(employeeVo.getPostProfessionalInfoId())) {//使用岗位专业
				employeeVo.setProfessionalInfoId(employeeVo.getPostProfessionalInfoId());
			}
		}
		return employeeVo;
	}
}
