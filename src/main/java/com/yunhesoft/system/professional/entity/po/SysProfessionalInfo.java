package com.yunhesoft.system.professional.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * 
 * @category 专业设置
 * <AUTHOR>
 */
@Entity
@Setter
@Getter
@Api(tags = "专业设置")
@Table(name = "SYS_PROFESSIONAL_INFO")
public class SysProfessionalInfo extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	
	@ApiModelProperty(value = "专业名称")
	@Column(name = "PROFESSIONAL_NAME" , length = 200)
	private String professionalName;
	
	@ApiModelProperty(value = "描述")
	@Column(name = "MEMO", length = 2000)
	private String memo;

	@ApiModelProperty(value = "排序")
	@Column(name = "TM_SORT")
	private Integer tmSort;

	@ApiModelProperty(value = "是否使用")
	@Column(name = "TM_USED")
	private Integer tmused;
	
	
}
