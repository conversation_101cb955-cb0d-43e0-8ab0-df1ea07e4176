package com.yunhesoft.system.professional.controller;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.professional.entity.dto.SysProfessionalInfoQueryDto;
import com.yunhesoft.system.professional.entity.dto.SysProfessionalInfoSaveDto;
import com.yunhesoft.system.professional.entity.vo.SysProfessionalInfoVo;
import com.yunhesoft.system.professional.service.impl.SysProfessionalInfoServiceImpl;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("system/professional")
@Api(tags = "专业设置")
public class SysProfessionalInfoController {

	@Autowired
	SysProfessionalInfoServiceImpl _sysProfessionalInfoService;

	@ApiOperation(value = "人员信息下拉选择框")
	@RequestMapping(value = "/professionalDataAll", method = RequestMethod.POST)
	public Res<?> professionalDataAll() {
		Res<List<Map<String, String>>> res = new Res<List<Map<String, String>>>();
		List<Map<String, String>> arrayList = new ArrayList<Map<String, String>>();
		List<SysProfessionalInfoVo> list = _sysProfessionalInfoService.getData(null);
		for (SysProfessionalInfoVo bean : list) {
			Map<String, String> map = new LinkedHashMap<String, String>();
			String id = bean.getId();
			String professionalName = bean.getProfessionalName();
			map.put("label", professionalName);
			map.put("value", id);
			arrayList.add(map);
			res.setResult(arrayList);
		}
		return res;
	}

	@RequestMapping(value = "/getData", method = RequestMethod.POST)
	@ApiOperation(value = "获取数据")
	public Res<?> getData(@RequestBody SysProfessionalInfoQueryDto dto) {
		Res<List<SysProfessionalInfoVo>> res = new Res<List<SysProfessionalInfoVo>>();
		List<SysProfessionalInfoVo> list = _sysProfessionalInfoService.getData(dto);
		res.setResult(list);
		return res;
	}

	@RequestMapping(value = "/saveData", method = RequestMethod.POST)
	@ApiOperation(value = "保存数据")
	public Res<?> saveData(@RequestBody List<SysProfessionalInfoSaveDto> beans) {
		String message = _sysProfessionalInfoService.save(beans);
		Res<String> res = new Res<String>();
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}

	@RequestMapping(value = "/deleteDatas", method = RequestMethod.POST)
	@ApiOperation(value = "删除数据")
	public Res<?> deleteDatas(@RequestBody List<SysProfessionalInfoSaveDto> beans) {
		_sysProfessionalInfoService.delete(beans);
		Res<String> res = new Res<String>();
		return res.ok();
	}
}
