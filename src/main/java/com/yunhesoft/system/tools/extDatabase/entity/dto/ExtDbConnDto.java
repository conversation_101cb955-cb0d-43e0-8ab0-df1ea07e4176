package com.yunhesoft.system.tools.extDatabase.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 外部数据源连接
 * 
 * <AUTHOR>
 *
 */
@Data
public class ExtDbConnDto {

	@ApiModelProperty(value = "连接字符串")
	private String url;

	/** 用户名 */
	@ApiModelProperty(value = "用户名")
	private String username;

	/** 密码 */
	@ApiModelProperty(value = "密码")
	private String password;

}
