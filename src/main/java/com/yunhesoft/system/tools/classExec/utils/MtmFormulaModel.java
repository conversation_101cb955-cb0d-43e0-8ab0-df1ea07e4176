package com.yunhesoft.system.tools.classExec.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.tools.classExec.entry.po.MtmLookUpReg;
import com.yunhesoft.system.tools.classExec.entry.po.SYSClassExecConfig;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaTreeVo;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaValueVo;
import com.yunhesoft.system.tools.classExec.service.SysClassExecService;

import lombok.extern.log4j.Log4j2;

/**
 * 非系统包类注册和执行抽象类
 * <AUTHOR>
 */
@Log4j2
public abstract class MtmFormulaModel{
//	@Autowired
//	private SysClassExecService serv;	
	protected SYSClassExecConfig sysConfig = null;
	public static String execType="mtm_formula";//接口类型
	protected String treeParam;//树形加载参数
//	protected String moduleCode;//外部模块编码
//	protected String moduleName;//外部模块名称
	private static SysClassExecService getSysClassExecService() {
		SysClassExecService result = null;
		try{			
			result =  (SysClassExecService) SpringUtils.getBean(SysClassExecService.class);
		}catch(Exception e){
			log.error("", e);
		}
		return result;
	}
	/**
	 * 根据模块编码获取类路径
	 * @category 
	 * <AUTHOR> 
	 * @param moduleCode 模块编码
	 * @return
	 */
	public static String getClassPathByModuleCode(String moduleCode) {
		String result = null;
		try{			
			SysClassExecService ser = getSysClassExecService();
			if(ser!=null) {
				SYSClassExecConfig cfg = ser.getConfigData(execType, moduleCode);
				if(cfg!=null) {
					result=cfg.getClassPath();
				}
			}
		}catch(Exception e){
			log.error("", e);
		}
		return result;
	}
	/**
	 * 根据模块编码获取配置信息
	 * @category 
	 * <AUTHOR> 
	 * @param moduleCode 模块编码
	 * @return
	 */
	public static SYSClassExecConfig getConfig(String moduleCode) {
		SYSClassExecConfig result = null;
		if (StringUtils.isNotEmpty(moduleCode)) {
			try{			
				SysClassExecService ser = getSysClassExecService();
				if(ser!=null) {
					result = ser.getConfigData(execType, moduleCode);
				}
			}catch(Exception e){
				log.error("", e);
			}
		}
		return result;
	}
	
	
	/**
	 * 根据查表表格编码获取类路径
	 * @category 
	 * <AUTHOR> 
	 * @param tabelCode 查表表格编码
	 * @return
	 */
	public static String getClassByLookUpTabelCode(String tabelCode){
		String result = null;
		try{			
			SysClassExecService ser = getSysClassExecService();
			if(ser!=null) {
				MtmLookUpReg reg = ser.getLookUpRegByTabelCode(tabelCode);
				if(reg!=null) {
					SYSClassExecConfig cfg = ser.getConfigData(execType, reg.getFormulaModelCode());
					if(cfg!=null) {
						result=cfg.getClassPath();
					}
				}
			}
		}catch(Exception e){
			log.error("", e);
		}
		return result;
	}
	/**
	 * 获取目标传导公式节点列表
	 * @deprecated
	 * @category 
	 * <AUTHOR> 
	 * @return
	 */
	public static List<MtmFormulaTreeVo> getMtmFormulaTreeList(){
		return getMtmFormulaTreeRootList(null);
	}
	/**
	 * 获取目标传导公式节点列表
	 * @category 
	 * @param pId 树形父ID
	 * <AUTHOR> 
	 * @return
	 */
	public static List<MtmFormulaTreeVo> getMtmFormulaTreeRootList(String orgCodes){
		List<MtmFormulaTreeVo> result = new ArrayList<MtmFormulaTreeVo>();
		try{			
			SysClassExecService ser = getSysClassExecService();
			if(ser!=null) {
				List<SYSClassExecConfig> cfgList = ser.getRegisterList(execType);
				if (StringUtils.isNotEmpty(cfgList)) {
					for(SYSClassExecConfig temp:cfgList) {
						MtmFormulaModel model = MtmFormulaModelFactory.getInstanceByCfg(temp);//.getInstance(temp.getClassPath());
						if(model!=null) {
							List<MtmFormulaTreeVo> treeNodeList = model.getAllFormulaTreeByPid(orgCodes,true);//获取树形根节点
							if (StringUtils.isNotEmpty(treeNodeList)) {
								result.addAll(treeNodeList);
							}
						}
					}
				}
			}
		}catch(Exception e){
			log.error("", e);
		}
		return result;
	}
	/**
	 * 获取已注册的公式列表
	 * @category 
	 * <AUTHOR> 
	 * @return
	 */
	public static List<MtmFormulaModel> getRegisterList(){
		List<MtmFormulaModel> result = new ArrayList<MtmFormulaModel>();
		try{			
			SysClassExecService ser = getSysClassExecService();
			if(ser!=null) {
				List<SYSClassExecConfig> cfgList = ser.getRegisterList(execType);
				if (StringUtils.isNotEmpty(cfgList)) {
					for(SYSClassExecConfig temp:cfgList) {
//						MtmFormulaModel model = MtmFormulaModelFactory.getInstance(temp.getClassPath());
						MtmFormulaModel model = MtmFormulaModelFactory.getInstanceByCfg(temp);
						if(model!=null) {
							result.add(model);
						}
					}
				}
			}
		}catch(Exception e){
			log.error("", e);
		}
		return result;
	}
//	public String getClassPathByModuleCode(String moduleCode) {
//		String result = null;
//		SYSClassExecConfig cfg = serv.getConfigData(this.execType, moduleCode);
//		if(cfg!=null) {
//			result=cfg.getClassPath();
//		}
//		return result;
//	}
	/**
	 * 模块注册
	 * @category 
	 * <AUTHOR> 
	 * @param moduleCode 模块编码
	 * @param moduleName 模块名称
	 */
	public void register() {
		SysClassExecService serv = getSysClassExecService();
		if(serv!=null) {
			serv.register(execType, getModuleCode(), getModuleName(), this.getClass().getName());
			init();//初始化数据
		}
	}

	/**
	 * 模块微服务注册
	 * @category 
	 * <AUTHOR> 
	 * @param execType 执行的类型 比如目标传导公式用，可以传入mtm_formula
	 * @param moduleCode 模块编码
	 * @param moduleName 模块名称
	 * @param serviceName  服务名
	 * @param getFormulaTree 树形获取服务
	 * @param getFormulaValue 公式解析服务
	 * @param getJsonData 获取json服务
	 * @param saveJsonData 保存json服务
	 * @param init 初始化公式服务
	 */
	public void register(SYSClassExecConfig cfg) {
		SysClassExecService serv = getSysClassExecService();
		if(serv!=null) {
			serv.register(cfg);
			this.sysConfig =cfg;
			init();//初始化数据
		}
	}
	/**
	 * 获取公式树形节点
	 * @category 
	 * <AUTHOR> 
	 * @param pId 父ID 加载根节点时会传入机构代码(为了扩展性，这里可能传入多个机构代码，用逗号分隔)
	 * @param isRootLoad 是否在加载根节点
	 * @return
	 */
	public List<MtmFormulaTreeVo> getAllFormulaTreeByPid(String pId,boolean isRootLoad) {
		List<MtmFormulaTreeVo> result = new ArrayList<MtmFormulaTreeVo>();
		String moduleCode=getModuleCode()+".";//模块编码，这个是作为公式前缀的
		MtmFormulaTreeVo root = new MtmFormulaTreeVo();
		root.setTreeNodeId("root");
		root.setFormulaCode(getModuleCode());
		root.setFormulaName(getModuleName());
		root.setDynamicLoad(1);//动态加载		
		if(isRootLoad) {			
			changeTree(moduleCode,root);//公式上增加模块前缀
			result.add(root);
		}else {
			if(StringUtils.isNotEmpty(pId)){//给定了ID
				if(pId.startsWith(moduleCode)) {
					pId=pId.substring(moduleCode.length());//去掉ID前缀
				}else{
					isRootLoad=true;//没有模块前缀，则代表传入机构代码加载机构根节点
				}
				List<MtmFormulaTreeVo> nodeList = getFormulaTree(pId,isRootLoad);
				if (StringUtils.isNotEmpty(nodeList)) {
					root.setChildren(nodeList);
					changeTree(moduleCode,root);//公式上增加模块前缀
				}
				result.addAll(root.getChildren());//只返回一层的节点
			}
		}
		return result;
	}
	/**
	 * 递归处理树形
	 * @category 递归处理树形
	 * <AUTHOR> 
	 * @param node 树节点
	 */
	private void changeTree(String moduleCode,MtmFormulaTreeVo node){	
		node.setModuleCode(this.getModuleCode());//指定模块编码，用于动态加载树形
		if(StringUtils.isNotEmpty(node.getTreeNodeId())){//给定了ID
			node.setTreeNodeId(moduleCode+node.getTreeNodeId());//加上模块编码前缀防止多模块重复
		}
		boolean isLeaf=false;
		if(node.getIsLeaf()!=null && node.getIsLeaf().intValue()==1) {
			node.setFormulaCode("["+moduleCode+node.getFormulaCode()+"]");
			isLeaf=true;
		}
		if (StringUtils.isNotEmpty(node.getChildren())) {
			node.setDynamicLoad(null);//有子节点，就不用动态加载了
			for(MtmFormulaTreeVo temp:node.getChildren()) {
				changeTree(moduleCode,temp);
			}
		}else {
			if(node.getDynamicLoad()==null) {//未指定加载方式
				if(!isLeaf && StringUtils.isNotEmpty(node.getTreeNodeId())) {//有节点Id，非叶子节点
					node.setDynamicLoad(1);//非叶子节点默认为动态加载下级数据，除非指定了加载模式
				}	
			}else {
				//指定了加载方式的，就不进行加载方式判断了
			}
		}
	}
	/**
	 * 获取公式值
	 * @category 获取公式值
	 * @deprecated
	 * <AUTHOR> 
	 * @param startDt 开始日期
	 * @param endDt 截止日期
	 * @param formulaTextList 公式列表（去重复）
	 * @return
	 */
	public HashMap<String,HashMap<String,MtmFormulaValueVo>> getAllFormulaValue(String startDt,String endDt,List<String> formulaTextList){
		return this.getAllFormulaValue(startDt, endDt, formulaTextList, null);
	}
	/**
	 * 获取公式值
	 * @category 
	 * <AUTHOR> 
	 * @param startDt 开始日期
	 * @param endDt 截止日期
	 * @param formulaTextList 公式列表（去重复）
	 * @param formulaTextObjList 公式列表(按对象存储，不去除重复)
	 * @return
	 */
	public HashMap<String,HashMap<String,MtmFormulaValueVo>> getAllFormulaValue(String startDt,String endDt,List<String> formulaTextList,List<MtmFormulaValueVo> formulaTextObjList){
		HashMap<String,HashMap<String,MtmFormulaValueVo>> result = new HashMap<String,HashMap<String,MtmFormulaValueVo>>();		
		if (StringUtils.isNotEmpty(formulaTextList)) {
			String moduleCode=getModuleCode()+".";//模块编码，这个是作为公式前缀的
			List<String> realformulaList = new ArrayList<String>();
			for(String temp:formulaTextList) {
				if(temp!=null ) {
					temp=temp.trim().replaceFirst(moduleCode, "");//去除空格和模块前缀
					realformulaList.add(temp);//加到要解析的公式里
				}
			}
			for(MtmFormulaValueVo temp:formulaTextObjList) {
				if(temp.getFormulaText()!=null ) {
					temp.setFormulaText(temp.getFormulaText().trim().replaceFirst(moduleCode, ""));//去除空格和模块前缀
//					realformulaList.add(temp);//加到要解析的公式里
				}else {
					temp.setFormulaText("");//容错
				}
			}
			List<MtmFormulaValueVo> vauleList = getFormulaValue(startDt,endDt,realformulaList,formulaTextObjList);//其他功能接口解析公式内容
			if (StringUtils.isNotEmpty(vauleList)) {
				for(MtmFormulaValueVo temp:vauleList) {
					String formulaKey = moduleCode+temp.getFormulaText();
					HashMap<String,MtmFormulaValueVo> valueMap = result.get(formulaKey);
					if(valueMap==null) {
						valueMap= new HashMap<String,MtmFormulaValueVo>();
						result.put(formulaKey, valueMap);
					}
					valueMap.put((temp.getObjType()==null?"":String.valueOf(temp.getObjType()))+"_"+
							(temp.getObjCode()==null?"":String.valueOf(temp.getObjCode())), temp);
				}
			}		
		}
		return result;
	}	
	
	public String getTreeParam() {
		return treeParam;
	}
	public void setTreeParam(String treeParam) {
		this.treeParam = treeParam;
	}
	/**
	 * 获取模块编码（必须有）
	 * @category 
	 * <AUTHOR> 
	 * @return
	 */
	public abstract String getModuleCode();
	/**
	 * 获取模块名称（必须有）
	 * @category 
	 * <AUTHOR> 
	 * @return
	 */
	public abstract String getModuleName();
	/**
	 * 获取公式tree
	 * @category 获取公式tree
	 * <AUTHOR> 
	 * @param pId 父ID 加载根节点时会传入机构代码(为了扩展性，这里可能传入多个机构代码，用逗号分隔)
	 * @param isRootLoad 是否在加载根节点
	 * @return
	 */
	protected abstract List<MtmFormulaTreeVo> getFormulaTree(String pId,boolean isRootLoad);
	/**
	 * 获取公式解析结果
	 * @category 获取公式解析结果
	 * @param startDt 开始日期
	 * @param endDt 截止日期
	 * @param formulaTextList 公式列表（去重复）
	 * @param formulaTextObjList 公式列表(按对象存储，不去除重复)
	 * <AUTHOR> 
	 * @return
	 */
	protected abstract List<MtmFormulaValueVo> getFormulaValue(String startDt,String endDt,List<String> formulaTextList,List<MtmFormulaValueVo> formulaTextObjList);
	
	/**
	 * 根据条件获取模块内部数据
	 * @category 根据条件获取模块内部数据 
	 * <AUTHOR> 
	 * @param startDt 开始日期
	 * @param endDt 截止日期
	 * @param queryList 查询条件列表 MtmFormulaValueVo.objType 类型（机构、岗位、人员） MtmFormulaValueVo.objCode 对应类型的代码 MtmFormulaValueVo.paramValue存放要取数据的表
	 * @return 查询结果转为 json存入MtmFormulaValueVo.formulaValue中
	 */
	public abstract void getJsonData(String startDt, String endDt,List<MtmFormulaValueVo> queryList);
	/**
	 * 保存模块数据
	 * @category 保存模块数据
	 * <AUTHOR> 
	 * @param saveList 要保存的数据列表，json格式，数据存储在MtmFormulaValueVo.formulaValue字段中，对应表存储在MtmFormulaValueVo.paramValue中
	 * @return
	 */
	public abstract boolean saveJsonData(List<MtmFormulaValueVo> saveList);
	/**
	 * 公式模块数据初始化
	 * @category 
	 * <AUTHOR>
	 */
	protected abstract void init(); 
}
