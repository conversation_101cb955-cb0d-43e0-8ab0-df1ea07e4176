package com.yunhesoft.system.tools.files.entity.dto;

import com.yunhesoft.core.common.dto.BaseQueryDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据检索条件
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "查询", description = "查询对象")
public class WordQueryDto extends BaseQueryDto {

	/** 模板ID */
    @ApiModelProperty(value = "tplId")
    private String tplId;
    
    /** 类型名 */
    @ApiModelProperty(value = "typeName")
    private String typeName;

    /** 别名 */
    @ApiModelProperty(value = "aliasName")
    private String aliasName;
    
    /** 名称 */
    @ApiModelProperty(value = "name")
    private String name;
    
    /** 使用，默认查1，有值时查对应值 */
    @ApiModelProperty(value = "used")
    private String used;
    

}
