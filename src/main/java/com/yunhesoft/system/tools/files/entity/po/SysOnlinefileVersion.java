package com.yunhesoft.system.tools.files.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 在线文档版本
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_ONLINEFILE_VERSION")
public class SysOnlinefileVersion extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 文件id（sys_file_manage） */
	@Column(name = "FILE_ID", length = 50)
	private String fileId;

	/** 当前活动状态1发布3存档 */
	@Column(name = "CURR_MARK")
	private Integer currMark;

	/** 当前最新应用版本标识 */
	@Column(name = "ACTIVE_MARK")
	private int activeMark;

	/** 当前最新存档版本标识 */
	@Column(name = "ACTIVE_ARCHIVE_MARK")
	private int activeArchiveMark;

	/** 发布时间 */
	@Column(name = "PUBLISH_TIME")
	private Date publishTime;

	/** 存档时间 */
	@Column(name = "ARCHIVE_TIME")
	private Date archiveTime;

	/** 发布人ID */
	@Column(name = "PUBLISH_USER_ID", length = 50)
	private String publishUserId;

	/** 发布人姓名 */
	@Column(name = "PUBLISH_USER_NAME", length = 50)
	private String publishUserName;

	/** 排序 */
	@Column(name = "TMSORT")
	private Integer tmsort;

	/** 使用 */
	@Column(name = "TMUSED")
	private Integer tmused;

}
