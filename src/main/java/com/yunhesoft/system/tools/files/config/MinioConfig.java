package com.yunhesoft.system.tools.files.config;

import com.yunhesoft.core.utils.http.SslUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;



import io.minio.MinioClient;
import lombok.Data;
import okhttp3.OkHttpClient;

/**
 * Minio 配置信息
 *
 */
@Configuration(proxyBeanMethods = true)
@Data
@ConfigurationProperties(prefix = "minio")
public class MinioConfig {
	/**
	 * 服务地址
	 */
	private String url;

	/**
	 * 用户名
	 */
	private String accessKey;

	/**
	 * 密码
	 */
	private String secretKey;

	/**
	 * 存储桶名称
	 */
	private String bucketName;

	/**
	 * 公共存储桶名称
	 */
	private String bucketNamePublic;

	@Autowired
	private Environment spbenv;

	@Bean
	public MinioClient getMinioClient() throws Exception {
		if (StringUtils.isAnyEmpty(url, accessKey, secretKey)) {
			return null;
		}
		if (this.isHttps(url)) {// https 模式
			OkHttpClient okHttpClient = SslUtils.getUnsafeOkHttpClent();
//			return MinioClient.builder().endpoint("192.168.0.40", 9090,true).httpClient(okHttpClient).region("eu-west-1").credentials(accessKey, secretKey).build();
			return MinioClient.builder().endpoint(url, getPort(url), true).httpClient(okHttpClient)
					.credentials(accessKey, secretKey).build();
		} else {
			return MinioClient.builder().endpoint(url).credentials(accessKey, secretKey).build();
		}

	}

	/**
	 * 判断是否为https
	 * 
	 * @param url
	 * @return
	 */
	private boolean isHttps(String url) {
		if (url.toLowerCase().startsWith("https:")) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 获得端口
	 * 
	 * @param url
	 * @return
	 * @throws Exception
	 */
	private int getPort(String url) throws Exception {
		int port = 9000;
		String[] urls = url.split("\\:");
		if (urls.length >= 2) {
			port = Integer.parseInt(urls[urls.length - 1]);
		}
		return port;
	}
}
