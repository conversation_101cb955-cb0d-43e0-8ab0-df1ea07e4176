package com.yunhesoft.system.tools.formulaParam.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 扩展公式树形数据表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "FORMULA_PARAM_EXT_TREE")
public class FormulaParamExtTree extends BaseEntity {
	
    private static final long serialVersionUID = 1L;

    /** 模型编码 */
    @Column(name="MODULE_CODE", length=50)
    private String moduleCode;
    
    /** 根节点类型，扩展 ext、核算 cost、日考核 oec等 */
    @Column(name="ROOT_TYPE", length=50)
    private String rootType;
    
    /** 节点类型，2分类、1数据 */
    @Column(name="NODE_TYPE")
    private Integer nodeType;
    
    /** 父节点标识 */
    @Column(name="PID", length=50)
    private String pid;
    
    /** 节点名称 */
    @Column(name="NODE_NAME", length=100)
    private String nodeName;
    
    /** 说明 */
    @Column(name="NODE_DESC", length=1000)
    private String nodeDesc;
    
    /** 公式内容 */
    @Column(name="FORMULA_CONTENT", length=4000)
    private String formulaContent;
    
    /** 有公式1有0无 */
    @Column(name="HAVE_FORMULA")
    private Integer haveFormula;
    
    /** 树形路径信息，逗号分割 */
    @Column(name="PATH_INFO", length=2000)
    private String pathInfo;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}
