package com.yunhesoft.system.tools.code.controller;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@RestController
@RequestMapping("/system/tools/syscode")
@Api(tags = "系统工具")

/**
 * 统计代码量
 * 
 * <AUTHOR>
 *
 */
public class SysCodeController extends BaseRestController {

	/** 空行 */
	private long nullLines = 0;
	/** 注释行 */
	private long annoLines = 0;
	/** 代码行 */
	private long codeLines = 0;
	/** 配置文件行 */
	private long configLines = 0;
	/** 总行 */
	private long allLines = 0;

	/** java 文件数 */
	private long javaCounts = 0;

	/** java 配置文件数 */
	private long configCounts = 0;

	@ApiOperation(value = "获取代码行数", notes = "获取代码行数")
	@RequestMapping(value = "/getLineCount", method = { RequestMethod.POST })
	public Res<?> getLineCount(@RequestParam @ApiParam(value = "程序路径") String codepath,
			@RequestParam @ApiParam(value = "程序文件扩展名") String extname) {
		if (StringUtils.isEmpty(extname)) {
			extname = ".java";
		}
		String[] ary = extname.split(",");
		List<String> extList = new ArrayList<String>();
		for (String s : ary) {
			extList.add(s);
		}
		this.clear();
		this.listFile(codepath, extList);// 开始统计
		LinkedHashMap<String, Object> map = new LinkedHashMap<String, Object>();
		map.put("配置文件数：", configCounts);
		map.put("代码扩展名：", extname);
		map.put("代码文件数：", javaCounts);
		map.put("空行数：", nullLines);
		map.put("注释行：", annoLines);
		map.put("配置文件行：", configLines);
		map.put("代码行数：", codeLines);
		map.put("总行数：", allLines);
		return Res.OK(map);
	}

	private void clear() {
		nullLines = 0;
		annoLines = 0;/** 注释行 */
		codeLines = 0;/** 代码行 */
		configLines = 0;/** 配置文件数 */
		allLines = 0;/** 总行 */
		javaCounts = 0;
		configCounts = 0;
	}

	/**
	 * 循环文件夹统计
	 * 
	 * @param filePath 程序路径
	 * @param extname  程序扩展名
	 */
	private void listFile(String filePath, List<String> extList) {
		File f = new File(filePath);
		File[] childs = f.listFiles();
		for (int i = 0; i < childs.length; i++) {
			if (!childs[i].isDirectory()) {
				if (this.isCodeFile(childs[i].getName(), extList)) {
					sumCode(childs[i], 0);
				} else if (childs[i].getName().endsWith(".yml") || childs[i].getName().endsWith(".xml")
						|| childs[i].getName().endsWith(".properties")) {
					sumCode(childs[i], 1);
				}
			} else {
				listFile(childs[i].getPath(), extList);
			}
		}
	}

	/**
	 * 判断是否为代码文件
	 * 
	 * @param filename
	 * @param extList
	 * @return
	 */
	private boolean isCodeFile(String filename, List<String> extList) {
		for (String ext : extList) {
			if (filename.endsWith(ext)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 统计代码行数
	 * 
	 * @param file
	 */
	private void sumCode(File file, int type) {
		BufferedReader br = null;
		try {
			br = new BufferedReader(new FileReader(file));
			String line = "";
			boolean isConfigFile = false;
			boolean isJavaFile = false;
			if (type == 0) {
				this.javaCounts++;
				isJavaFile = true;
			} else if (type == 1) {
				isConfigFile = true;
				this.configCounts++;
			}
			while ((line = br.readLine()) != null) {
				allLines++;
				if (isConfigFile) {// 配置文件
					configLines++;
				} else if (isJavaFile) {// java文件
					String trimStr = line.trim();
					if (trimStr.length() == 0) {// 空行
						nullLines++;
					} else if (trimStr.startsWith("//") || trimStr.startsWith("/**") || trimStr.startsWith("*")
							|| trimStr.startsWith("*/") || trimStr.startsWith("/*")) {
						annoLines++;
					} else {
						codeLines++;
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				br.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
}