package com.yunhesoft.system.multiTenant.model;

import com.yunhesoft.system.employee.entity.dto.EmployeeDto;
import com.yunhesoft.system.multiTenant.entity.dto.SysMultiTenantDto;

/**
 * 初始化多租户模块数据抽象基类
 * 
 * <AUTHOR>
 * @date 2023/06/05 17:15
 */
public abstract class BaseInitTenantData {

	/**
	 * 企业注册 - 初始化数据方法
	 * 
	 * @param tenantId     租户ID
	 * @param type         qy（企业） 或者 gr（个人）
	 * @param registerBean 企业注册信息
	 * @param empBean      人员信息（企业注册时为住户管理员，个人注册时为普通用户）
	 */
	public abstract void initDataEnterprise(String tenantId, String type, SysMultiTenantDto registerBean,
			EmployeeDto empBean);

	/**
	 * 个人注册 - 初始化数据方法
	 * 
	 * @param tenantId     租户ID
	 * @param type         qy（企业） 或者 gr（个人）
	 * @param registerBean 企业注册信息
	 * @param empBean      人员信息（企业注册时为住户管理员，个人注册时为普通用户）
	 */
	public abstract void initDataPerson(String tenantId, String type, SysMultiTenantDto registerBean,
			EmployeeDto empBean);

}
