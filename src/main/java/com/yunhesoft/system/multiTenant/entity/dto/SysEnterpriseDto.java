package com.yunhesoft.system.multiTenant.entity.dto;

import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * @category 行业类型
 * <AUTHOR>
 * @since 2022/12/16
 * @update 2022/12/16
 */
@ApiModel(value = "行业类型")
@Setter
@Getter
public class SysEnterpriseDto {

	private String id;//id
	private String name;//名称
	private String value;//值
	private String pid;//父id
	private String assessTypeid;//调查表id
	private Integer isleaf;//是否有子节点
	private List<SysEnterpriseDto> children;//子节点集合
}
