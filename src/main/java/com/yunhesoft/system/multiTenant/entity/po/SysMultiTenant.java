package com.yunhesoft.system.multiTenant.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * @category 企业注册表
 * <AUTHOR>
 * @since 2022/12/16
 * @update 2022/12/16
 */
@ApiModel(value = "企业注册表")
@Entity
@Setter
@Getter
@Table(name = "SYS_MULTI_TENANT")
public class SysMultiTenant extends BaseEntity {

	private static final long serialVersionUID = 1L;
	/** 企业识别码-唯一编码 */
	@Column(name = "CORPORATEIDENTITY", length = 255)
	private String corporateIdentity;
	/** 企业码 */
	@Column(name = "ENTERPRISENUM", length = 255)
	private String enterpriseNum;
	/** 企业名 */
	@Column(name = "ENTERPRISENAME", length = 255)
	private String enterpriseName;
	/** 企业类型 */
	@Column(name = "ENTERPRISETYPEID", length = 50)
	private String enterpriseTypeId;
	/** 人员规模 选择（0-100,100-500,500-1000,1000-5000,万人以上）*/
	@Column(name = "STAFFSIZE")
	private Integer staffSize;
//	/** 所属地区 */
//	@Column(name = "LOCATION", length = 1000)
//	private String location;
	/** 信用码 */
	@Column(name = "CREDITCODE", length = 255)
	private String creditCode;
	/** 法人代表 */
	@Column(name = "CORPORATEREPRESENTATIVE", length = 255)
	private String corporateRepresentative;
	/**  */
	@Column(name = "IDENTITYCARD", length = 50)
	private String identityCard;

	/** 租户id **/
	@Column(name = "TENANT_ID", columnDefinition = "varchar(50) default '1000'", length = 50)
	private String tenant_id = "1000";

	@Column(name = "TMUSED")
	private Integer tmused;

	/** 省 */
	@Column(name = "PROVINCE", length = 255)
	private String province;
	/** 市 */
	@Column(name = "CITY", length = 255)
	private String city;
	/** 区 */
	@Column(name = "AREA", length = 255)
	private String area;
	
//	主营产品：录入
	@Column(name = "PRODUCT", length = 2000)
	private String product;
//	市场份额：1%-100%
	@Column(name = "MARKET_SHARES")
	private Double market_shares;
//	上年度营收：选择1-100，100-500,500-1000,1000-5000,5000-1亿,1亿以上
	@Column(name = "REVENUE")
	private Integer revenue;
	
//	生产方式：1离散型，2流程型
	@Column(name = "PRODUCTION_MODE")
	private Integer production_mode;
	
//	工艺流程：录入 
	@Column(name = "TECHNOLOGICAL_PROCESS", length = 2000)
	private String technological_process;
	
//	技术水平：录入
	@Column(name = "TECHNICAL_MERIT", length = 2000)
	private String technical_merit;
	

	@Column(name = "ENTERPRISE_TYPEID_PATH", length=200)
	private String enterpriseTypeIdPath;
}
