package com.yunhesoft.system.multiTenant.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * @category 企业注册表
 * <AUTHOR>
 * @since 2022/12/16
 * @update 2022/12/16
 */
@ApiModel(value = "行业类别")
@Entity
@Setter
@Getter
@Table(name = "SYS_ENTERPRISE_TYPE")
public class SysEnterpriseType  extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/** 名称 */
	@Column(name = "NAME", length = 500)
	private String name;
	/** 父id */
	@Column(name = "PID", length = 255)
	private String pid;
	/** 级别 */
	@Column(name = "ENTERPRISELEVEL")
	private Integer EnterpriseLevel;
	/** 调查表类型id */
	@Column(name = "ASSESSTYPEID", length = 255)
	private String assessTypeid;
	/** 是否使用 */
	@Column(name = "TMUSED")
	private Integer tmused;
	/** 描述 */
	@Column(name = "DETAIL_DESCRIBE" , length = 2000)
	private String describe;
	
}
