package com.yunhesoft.system.multiTenant.entity.vo;


import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * @category 企业注册表
 * <AUTHOR>
 * @since 2022/12/16
 * @update 2022/12/16
 */
@ApiModel(value = "注册表信息")
@Setter
@Getter
public class SysMultiTenantVo {

	/** 租户id */
	private String tenant_id;

	/** 企业码 */
	private String enterpriseNum;
	/** 企业名 */
	private String enterpriseName;
	/** 企业类型 */
	private String enterpriseType;
	/** 人员规模 */
	private Integer staffSize;
	/** 所属地区 */
	private String location;
	/** 手机号 */
	private String cellPhoneNum;
	/** 密码 */
	private String password;
	/** 验证码 */
	private String verificationCode;
	/** 姓名 */
	private String name;
	
//	主营产品：录入
	private String product;
//	市场份额：1%-100%
	private Double market_shares;
//	上年度营收：选择1-100，100-500,500-1000,1000-5000,5000-1亿,1亿以上
	private Integer revenue;
	
//	生产方式：1离散型，2流程型
	private Integer production_mode;
	
//	工艺流程：录入 
	private String technological_process;
	
//	技术水平：录入
	private String technical_merit;
}
