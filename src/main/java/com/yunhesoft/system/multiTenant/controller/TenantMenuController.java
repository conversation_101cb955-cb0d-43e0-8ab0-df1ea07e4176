package com.yunhesoft.system.multiTenant.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.menu.entity.po.SysMenuTenantDefault;
import com.yunhesoft.system.multiTenant.entity.dto.TenantMenuDto;
import com.yunhesoft.system.multiTenant.service.ISysTenantMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TenantMenuController
 * 
 * @category 租户菜单接口
 * <AUTHOR> @date
 */
@Api(tags = "租户菜单接口")
@RestController
@RequestMapping("/system/multiTenant/tenantMenu")
@Log4j2
public class TenantMenuController extends BaseRestController {

	@Autowired
	ISysTenantMenuService srv;
	
	/**
	 * 获取租户列表
	 * 
	 * @category 获取租户列表
	 * @param
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getTenantList", method = { RequestMethod.GET })
	@ApiOperation(value = "获取租户列表")
	public Res<?> getTenantList() {
		return Res.OK(srv.getTenantList());
	}

	@ResponseBody
	@RequestMapping(value = "/getTenantDefaultMenuList", method = { RequestMethod.GET })
	@ApiOperation(value = "获取租户默认菜单列表")
	public Res<?> getTenantDefaultMenuList() {
		return Res.OK(srv.getTenantDefaultMenuList());
	}

	@ResponseBody
	@RequestMapping(value = "/saveTenantDefaultMenuList", method = { RequestMethod.POST })
	@ApiOperation(value = "保存租户默认菜单列表")
	public Res<?> saveTenantDefaultMenuList(@RequestBody List<SysMenuTenantDefault> list) {
		return Res.OK(srv.saveTenantDefaultMenuList(list));
	}

	/**
	 * 获取租户菜单列表
	 * 
	 * @category 获取租户菜单列表
	 * @param
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getTenantMenuList/{tenant_id}", method = { RequestMethod.GET })
	@ApiOperation(value = "获取租户菜单列表")
	public Res<?> getTenantMenuList(@PathVariable String tenant_id) {
		return Res.OK(srv.getTenantMenuList(tenant_id));
	}


}
