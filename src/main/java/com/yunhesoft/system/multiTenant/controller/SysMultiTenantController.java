package com.yunhesoft.system.multiTenant.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.multiTenant.entity.dto.SysEnterpriseDto;
import com.yunhesoft.system.multiTenant.entity.dto.SysMultiTenantDto;
import com.yunhesoft.system.multiTenant.entity.po.SysMultiTenant;
import com.yunhesoft.system.multiTenant.service.ISysMultiTenantService;
import com.yunhesoft.system.multiTenant.service.impl.SysMultiTenantImpl;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

/**
 * SysOrgController
 *
 * @category SysOrgController接口
 * <AUTHOR>
 * @date 2022/12/16
 */
@Api(tags = "注册接口")
@RestController
@RequestMapping("/system/multiTenant/sysMultiTenant")
public class SysMultiTenantController  extends BaseRestController {

	@Autowired
	ISysMultiTenantService iss;
	@Autowired
	RedisUtil redisUtil;

	/**
	 * 注册信息
	 *
	 * @category 保存注册信息
	 * @param MeterStatusDto
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/saveLogin", method = { RequestMethod.POST })
	@ApiOperation(value = "保存注册信息")
	@ApiImplicitParam(name = "saveLogin", value = "保存注册信息", required = true, paramType = "body", dataType = "SysMultiTenantDto")
	public Res<?> saveLogin(@RequestBody SysMultiTenantDto bean){
		boolean b = false;
		try {
			b = iss.saveLogin(bean);
		}catch (DuplicateKeyException e){
			return Res.FAIL(500,e.getMessage());
		}
		return Res.OK(b);
	}
	/**
	 * 同步机构信息
	 *
	 * @category 校验注册信息
	 * @param MeterStatusDto
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/verifyData", method = { RequestMethod.POST })
	@ApiOperation(value = "校验注册信息")
	@ApiImplicitParam(name = "verifyData", value = "保存注册信息", required = true, paramType = "body", dataType = "SysMultiTenantDto")
	public Res<?> verifyData(@RequestBody SysMultiTenantDto bean) {
		return Res.OK(iss.verifyData(bean));
	}

	/**
	 * 注册信息
	 *
	 * @category 获取行业信息
	 * @param MeterStatusDto
	 * @return
	 */
	@ApiOperation(value = "获取行业信息")
	@RequestMapping(value = "/getEnterprise", method = { RequestMethod.GET })
	public Res<?> getEnterprise(String pid) {
		List<SysEnterpriseDto> list=iss.getEnterprise(pid);
		return Res.OK(list);
	}

	/**
	 * 校验企业信息
	 *
	 * @category 获企业更多信息
	 * @param MeterStatusDto
	 * @return
	 */
	@ApiOperation(value = "获企业更多信息")
	@RequestMapping(value = "/getMultiTenantInfo", method = { RequestMethod.GET })
	public Res<?> getMultiTenantInfo() {
		SysMultiTenant bean =iss.getSysMultiTenant();
		return Res.OK(bean);
	}

	/**
	 * 保存企业信息
	 *
	 * @category 保存企业信息
	 * @param MeterStatusDto
	 * @return
	 */
	@ApiOperation(value = "获企业更多信息")
	@RequestMapping(value = "/saveMultiTenantData", method = { RequestMethod.POST })
	public Res<?> saveMultiTenantData(@RequestBody SysMultiTenant bean) {
		return Res.OK(iss.saveMultiTenantData(bean));
	}
	/**
	 * 校验企业邀请信息时限
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	@ApiOperation(value = "校验企业邀请信息")
	@RequestMapping(value = "/enterpriseSecurityCheck", method = { RequestMethod.POST })
	public Res<?> enterpriseSecurityCheck(@RequestParam("inviteToken") String token) {
		String inviteToken = token;
		if(SysMultiTenantImpl.USECACHE_INVITE) {
			//从缓存中获取
			inviteToken = iss.getInviteInfoByCache(token);
		}
		return Res.OK(iss.enterpriseSecurityCheck(inviteToken));
	}
	/**
	 * 查询是否是多租户模式
	 * <AUTHOR>
	 * @return
	 * @params
	*/
	@ApiOperation(value = "查询是否是多租户模式")
	@RequestMapping(value = "/getIsMultiTenant", method = { RequestMethod.POST })
	public Res<?> getIsMultiTenant() {
		return Res.OK(MultiTenantUtils.enalbe());
	}

}
