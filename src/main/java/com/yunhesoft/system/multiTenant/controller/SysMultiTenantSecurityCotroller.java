package com.yunhesoft.system.multiTenant.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.multiTenant.service.ISysMultiTenantService;
import com.yunhesoft.system.multiTenant.service.impl.SysMultiTenantImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/system/multiTenant/security")
@Api(tags = "多租户功能安全接口")
public class SysMultiTenantSecurityCotroller extends BaseRestController {

    @Autowired
    ISysMultiTenantService iss;

    /**
     *
     * 企业邀请信息
     * <AUTHOR>
     * @return
     * @params
     */
    @ApiOperation(value = "企业邀请信息")
    @RequestMapping(value = "/getEnterpriseInvite", method = { RequestMethod.POST })
    public Res<?> getEnterpriseInvite() {
        String enterpriseInviteToken = iss.getEnterpriseInvite();
        if(SysMultiTenantImpl.USECACHE_INVITE) {
            enterpriseInviteToken = iss.cacheInviteInfo(enterpriseInviteToken);
        }
        return Res.OK(enterpriseInviteToken);
    }
}
