package com.yunhesoft.system.multiTenant.service;

import com.yunhesoft.system.menu.entity.po.SysMenu;
import com.yunhesoft.system.menu.entity.po.SysMenuTenantDefault;
import com.yunhesoft.system.multiTenant.entity.vo.SysMultiTenantVo;

import java.util.List;

public interface ISysTenantMenuService {

    List<SysMultiTenantVo> getTenantList();

    List<SysMenuTenantDefault> getTenantDefaultMenuList();

    List<SysMenu> initTenantMenuListByDefault(String tenant_id);

    String saveTenantDefaultMenuList(List<SysMenuTenantDefault> list);

    List<SysMenu> getTenantMenuList(String tenant_id);


}
