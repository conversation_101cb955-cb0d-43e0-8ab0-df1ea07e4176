package com.yunhesoft.system.multiTenant.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.entity.BaseEntity;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.menu.entity.po.SysMenu;
import com.yunhesoft.system.menu.entity.po.SysMenuTenantDefault;
import com.yunhesoft.system.menu.service.ISysMenuLibService;
import com.yunhesoft.system.menu.service.SysMenuService;
import com.yunhesoft.system.multiTenant.entity.po.SysMultiTenant;
import com.yunhesoft.system.multiTenant.entity.vo.SysMultiTenantVo;
import com.yunhesoft.system.multiTenant.service.ISysTenantMenuService;

@Service
public class SysTenantMenuServiceImpl implements ISysTenantMenuService {

    @Autowired
    private EntityService dao;

    @Autowired
    private ISysMenuLibService menuLibService;

    @Autowired
    private SysMenuService menuService;

    @Override
    public List<SysMultiTenantVo> getTenantList() {
        List<SysMultiTenant> list = dao.queryListDisableTenant(SysMultiTenant.class,  Where.create().eq(SysMultiTenant::getTmused, 1), Order.create().orderByDesc(SysMultiTenant::getCreateTime));
        if (StringUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(item -> ObjUtils.copyTo(item, SysMultiTenantVo.class)).collect(Collectors.toList());
    }

    @Override
    public List<SysMenuTenantDefault> getTenantDefaultMenuList() {
        return dao.queryDataDisableTenant(SysMenuTenantDefault.class, Where.create(), null, null);
    }

    @Override
    public List<SysMenu> initTenantMenuListByDefault(String tenant_id) {
        List<SysMenuTenantDefault> tenantDefaultMenuList = getTenantDefaultMenuList();
        List<SysMenu> result = null;
        if (StringUtils.isNotEmpty(tenantDefaultMenuList)) {
            List<String> idList = tenantDefaultMenuList.stream().map(BaseEntity::getId).collect(Collectors.toList());
            SysMenu menu = new SysMenu();
            menu.setId(StringUtils.join(idList, ","));
            menu.setTenant_id(tenant_id);
            menuLibService.syncMenuLib(menu);
            result = menuService.getMenuList(false, false, false, false);
        }
        return result;
    }

    @Override
    public String saveTenantDefaultMenuList(List<SysMenuTenantDefault> list) {
        dao.rawDeleteByWhere(SysMenuTenantDefault.class, Where.create());
        dao.insertBatch(list);
        return null;
    }

    @Override
    public List<SysMenu> getTenantMenuList(String tenant_id) {
        return dao.rawQueryListByWhereWithTenant(tenant_id, SysMenu.class, null, null);
    }



}
