package com.yunhesoft.system.multiTenant.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.ClassExecute;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.auth.entity.po.SysLoginUser;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.employee.entity.dto.EmployeeDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.service.IEmployeeBasicOperationService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.menu.entity.po.SysMenu;
import com.yunhesoft.system.menu.entity.po.SysModule;
import com.yunhesoft.system.menu.service.ISysMenuLibInitService;
import com.yunhesoft.system.menu.service.SysMenuService;
import com.yunhesoft.system.multiTenant.entity.dto.SysEnterpriseDto;
import com.yunhesoft.system.multiTenant.entity.dto.SysMultiTenantDto;
import com.yunhesoft.system.multiTenant.entity.po.SysEnterpriseType;
import com.yunhesoft.system.multiTenant.entity.po.SysMultiTenant;
import com.yunhesoft.system.multiTenant.service.ISysMultiTenantService;
import com.yunhesoft.system.multiTenant.service.ISysTenantMenuService;
import com.yunhesoft.system.org.entity.dto.SysOrgAdd;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.post.entity.dto.PostDto;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.post.entity.vo.PostVo;
import com.yunhesoft.system.post.service.IPostBasicOperationService;
import com.yunhesoft.system.post.service.ISysPostService;
import com.yunhesoft.system.role.entity.po.SysRolePerm;
import com.yunhesoft.system.role.entity.po.SysUserRole;
import com.yunhesoft.system.role.service.ISysRoleService;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.SignatureException;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class SysMultiTenantImpl implements ISysMultiTenantService {

	/* 是否启用缓存邀请信息 */
	public static Boolean USECACHE_INVITE = false;
	/**
	 * 登录信息服务接口
	 */
	@Autowired
	private AuthService loginServ;

	@Autowired
	private EntityService dao;
	@Autowired
	private RedisUtil redisUtil;

	private final static String RED_KEY_TENANT = "SYSTEM:TENANT_INFO";

	/* 二维码时效时间 单位：分钟 */
	private static int VALID_TIME = 480;// 默认8小时失效时间

//	@Autowired
//	private SysMenuService sysMenuSer; // 菜单

	@Autowired
	private ISysRoleService sysRoleService; // 角色信息
	@Autowired
	private ISysOrgService sysOrgService;// 机构信息
	@Autowired
	private ISysPostService sysPostService;// 岗位信息
	@Autowired
	private IPostBasicOperationService sysPostBscOperSrv;// 岗位信息
	@Autowired
	private IEmployeeBasicOperationService employeeService;// 人员信息

	@Autowired
	private ISysTenantMenuService tenantMenuSrv; // 租户菜单

	@Autowired
	private ISysMenuLibInitService serv; // 菜单库

	/**
	 * 校验
	 */

	@Override
	public String verifyData(SysMultiTenantDto bean) {

		String str = "";
		String cellPhoneNum = bean.getCellPhoneNum();// 手机号
		String enterpriseName = bean.getEnterpriseName();// 企业名
		String enterpriseNum = bean.getEnterpriseNum();// 企业码

		Where where = Where.create();
//		where.eq(SysMultiTenant::getTmused, 1);
		where.eq(SysMultiTenant::getEnterpriseName, enterpriseName);
		List<SysMultiTenant> listmt = dao.rawQueryListByWhereDisableTenant(SysMultiTenant.class, where);

		if (listmt != null && listmt.size() > 0) {
			str = "企业名已经存在,请更换企业名称!";
		}

		if (enterpriseNum != null && !"".equals(enterpriseNum)) {
			where = Where.create();
			where.eq(SysMultiTenant::getEnterpriseNum, enterpriseNum);
			listmt = dao.rawQueryListByWhereDisableTenant(SysMultiTenant.class, where);

			if (listmt == null || listmt.size() <= 0) {
				str = "企业码不存在，请输入正确企业码!";
			}
		}

		where = Where.create();
		where.eq(SysLoginUser::getUserName, cellPhoneNum);
		List<SysLoginUser> list = dao.rawQueryListByWhereDisableTenant(SysLoginUser.class, where);

		if (list != null && list.size() > 0) {
			str = "该手机号已经注册过，请更换手机号，或直接登录!";
		}
		return str;
	}

	/**
	 * 注册
	 */
	@Override
	public boolean saveLogin(SysMultiTenantDto bean) throws DuplicateKeyException {

		String tenantId = "";
		// ====未验证短信验证码=====//
		if (bean != null && bean.getEnterpriseNum() != null && !"".equals(bean.getEnterpriseNum())) {
			// 有企业码，个人注册
			// 根据企业码，获取租户id
			tenantId = bean.getEnterpriseNum();
			try {
				initData(bean, tenantId, "gr");
			} catch (DuplicateKeyException e) {
				throw e;
			}
		} else {
			// 没有企业码，注册企业
			tenantId = createMultiTenant(bean);
			// 只有注册企业的时候，初始化一次
			try {
				initData(bean, tenantId, "qy");
			} catch (DuplicateKeyException e) {
				throw e;
			}
		}

		return true;
	}

	/**
	 * 根据企业码，获取租户id
	 *
	 * @param enterpriseNum
	 * @return
	 */
	public String getTenantId(String enterpriseNum) {
		String tenant_id = "";
		Where where = Where.create();
		where.eq(SysMultiTenant::getEnterpriseNum, enterpriseNum);
		where.eq(SysMultiTenant::getTmused, 1);
		List<SysMultiTenant> list = dao.queryListDisableTenant(SysMultiTenant.class, where);
		if (list != null && list.size() > 0) {
			SysMultiTenant e = list.get(0);
			tenant_id = e.getTenant_id();
		}
		return tenant_id;
	}

	/**
	 * 创建企业信息
	 *
	 * @param bean
	 * @return
	 */
	public String createMultiTenant(SysMultiTenantDto bean) {
		List<SysMultiTenant> list = new ArrayList<SysMultiTenant>();
		SysMultiTenant e = new SysMultiTenant();
		e.setId(TMUID.getUID());
		e.setEnterpriseTypeIdPath(bean.getEnterpriseTypeIdPath());
		e.setEnterpriseNum(getEnterpriseNum());/** 企业码 */
		e.setEnterpriseName(bean.getEnterpriseName());/** 企业名 */
		e.setEnterpriseTypeId(bean.getEnterpriseTypeId());/** 企业类型 */
		e.setStaffSize(bean.getStaffSize());/** 人员规模 */
//		e.setLocation(bean.getLocation());/** 所属地区 */
		e.setProvince(bean.getProvince());/** 省 */
		e.setCity(bean.getCity());/** 市 */
		e.setArea(bean.getArea());/** 区 */
		e.setCreditCode("");/** 信用码 */
		e.setCorporateRepresentative("");/** 法人代表 */
		e.setIdentityCard("");/** 身份证 */
		e.setTmused(1);
		e.setCorporateIdentity(bean.getCorporateIdentity());// 企业识别码
		// e.setAssessTypeid(bean.getAssessTypeid());
		String tenant_id = TMUID.getUID();
		e.setTenant_id(tenant_id);/** 租户id **/
		list.add(e);
		int rs = dao.insertBatchDisableTenant(list);// 保存企业注册表
		if (rs <= 0) {
			return "";
		} else {
			return tenant_id;
		}

	}

	/**
	 * 创建登录表信息
	 *
	 * @param bean
	 * @param tenant_id
	 * @return
	 */
	public SysLoginUser createLogin(SysMultiTenantDto bean, String tenant_id, String id) throws DuplicateKeyException {

		List<SysLoginUser> list = new ArrayList<SysLoginUser>();
		SysLoginUser user = new SysLoginUser();
		user = new SysLoginUser();
		user.setId(id);
		user.setTenant_id(tenant_id);
		user.setPassword(loginServ.encryptPassword(bean.getPassword()));
		user.setRealName(bean.getName());
		user.setNickName(bean.getName());
		user.setPhone(bean.getCellPhoneNum());
		user.setStatus(1);
		user.setUserName(bean.getCellPhoneNum());
		list.add(user);
		int rs = 0;// 保存企业注册表
		try {
			rs = dao.insertBatchDisableTenant(list);
		} catch (Exception e) {
			if (e instanceof DuplicateKeyException) {
				throw new DuplicateKeyException("此手机号已经被注册！");
			}
		}
		if (rs <= 0) {
			return null;

		} else {
			return user;
		}
	}

	/**
	 * 创建人员信息
	 *
	 * @param bean
	 * @return
	 */
	public EmployeeDto createEmployee(SysMultiTenantDto bean, String tenantId, String orgid, String postid,
			String roleid) {
		List<EmployeeDto> listDto = new ArrayList<EmployeeDto>();
		EmployeeDto e = new EmployeeDto();
//		String id = TMUID.getUID();
//		e.setEmpTmuid(id);// 人员id
		e.setTenantId(tenantId);
		e.setOrgcode(orgid);
		if (StringUtils.isNotEmpty(postid)) {
			e.setPostid(postid);
		}
		e.setRoleid(roleid);
		e.setEmpname(bean.getName());/** 用户姓名 */
		e.setEntryDate(new Date());/** 入职日期 */
		e.setSex("1");/** 性别 */
//		e.setStaffNo("1000001");/** 工号 */
		e.setMobile(bean.getCellPhoneNum());/** 手机号 */
		e.setStaffType(1);/** 员工类型（1全职、2兼职、3实习、4外派、5其它） */
		e.setTmSort(1);/** 人员排序 */
		e.setStatus(1);/** 人员状态 - 在职(1)、离职(0)、退休(-1) */
		e.setOrgStatus(1); /** 是否主机构，1主机构 2兼机构 */
		e.setOrgSort(1);/** 机构排序 */
		e.setPostStatus(1);/** 是否主岗位，1主岗位2兼岗位3借调岗位 */
		e.setPostSort(1);/** 岗位排序 */
		e.setUsed(1);/** 是否使用 */
		e.setSys(1);/** 是否为系统内置人员（1：是，0：否） */
		listDto.add(e);
		String err = employeeService.addEmployee(listDto);
		if (err == null || "".equals(err)) {
			if (listDto != null && listDto.size() > 0) {
				EmployeeDto em = listDto.get(0);
				return em;
			} else {
				return e;
			}
		} else {
			return null;
		}
	}

	/**
	 * 创建机构信息
	 *
	 * @param bean
	 * @return
	 */
	public SysOrgAdd createOrg(SysMultiTenantDto bean, String tenantId) {
		String orgCode = "";
		// 初始化跟节点
		SysOrg o = sysOrgService.initOrg("机构", tenantId);
		// 初始化公司节点
		List<SysOrgAdd> list = new ArrayList<SysOrgAdd>();
		SysOrgAdd e = new SysOrgAdd();
		orgCode = "gs:" + tenantId;
		e.setId(orgCode);// id
		e.setOrgname(bean.getEnterpriseName());// 公司名
		e.setOrgNumber("");// 机构编码
		e.setOrgType("company");// 机构类型
		e.setPorgcode(o.getId());// 父级节点
		e.setOrgHead(bean.getName());// 部门责任人
		e.setOrgpath(o.getOrgpath() + "/" + orgCode);
		e.setOrglevel(1);
		e.setTmSort(1);
		e.setPhone(bean.getCellPhoneNum());
		e.setTenantId(tenantId);
		list.add(e);
		sysOrgService.insertData(list);
		return e;
	}

	/**
	 * 创建岗位信息
	 *
	 * @param bean
	 * @return
	 */
	public List<PostVo> createPost(SysMultiTenantDto bean, String tenantId) {
		SysPost sp = sysPostService.initPost("岗位", tenantId);
		String pid = sp.getId();

		List<PostDto> listDto = new ArrayList<PostDto>();

		PostDto e = new PostDto();
		e.setId("generalManager:" + tenantId);
//		e.setTenant_id(tenantId);
		e.setPcode(pid);
		e.setName("总经理");
		e.setUsed(1);
		listDto.add(e);

		PostDto e1 = new PostDto();
		e1.setId("manager:" + tenantId);
//		e1.setTenant_id(tenantId);
		e1.setPcode(pid);
		e1.setName("经理");
		e1.setUsed(1);
		listDto.add(e1);

		PostDto e2 = new PostDto();
		e2.setId("zy:" + tenantId);
//		e2.setTenant_id(tenantId);
		e2.setPcode(pid);
		e2.setName("职员");
		e2.setUsed(1);
		listDto.add(e2);
//		sysPostService.addBatch(listPo);

		List<PostVo> r = sysPostBscOperSrv.addPost(tenantId, listDto);

//		dao.rawInsertBatchWithTenant(tenantId, listPo);
//		sysPostService.updateReids(listPo);//保存redis
		return r;
	}

	/**
	 * 绑定角色id和权限id
	 *
	 * @param permid
	 * @param roleid
	 */
	public boolean createRolePerm(List<SysMenu> list, String roleid, String tenant_id) {
		List<SysRolePerm> listRp = new ArrayList<SysRolePerm>();
		if (list != null && list.size() > 0) {
			for (int i = 0; i < list.size(); i++) {
				String permid = list.get(i).getId();
				SysRolePerm e = new SysRolePerm();
				e.setId(TMUID.getUID());
				e.setPermid(permid);
				e.setRoleid(roleid);
//				e.setTenant_id(tenant_id);
				listRp.add(e);
			}
		}
		try {
			dao.rawInsertBatchWithTenant(tenant_id, listRp);
		} catch (Exception ex) {
			return false;
		}
		return true;
	}

	public int cs = 0;

	/**
	 * 获取行业类型
	 *
	 * @param bean
	 */
	@Override
	public List<SysEnterpriseDto> getEnterprise(String pid) {
		List<SysEnterpriseDto> list = new ArrayList<SysEnterpriseDto>();

		Where where = Where.create();
		where.eq(SysEnterpriseType::getTmused, 1);
//		if (pid != null && !"".equals(pid)) {
//			where.eq(SysEnterpriseType::getPid, pid);
//		} else {
//			// 获取第一层节点
//			where.eq(SysEnterpriseType::getEnterpriseLevel, 1);
//		}
		Order order = Order.create().orderByAsc(SysEnterpriseType::getId);
		List<SysEnterpriseType> listmt = dao.rawQueryListByWhereDisableTenant(SysEnterpriseType.class, where, order);
		if (listmt != null && listmt.size() > 0) {
			int m = listmt.size();
			for (int i = 0; i < m; i++) {
				SysEnterpriseType se = listmt.get(i);
				if (se.getEnterpriseLevel() == 1) {
					SysEnterpriseDto e = new SysEnterpriseDto();
					e.setId(se.getId());
					e.setName(se.getId() + ":" + se.getName());
					e.setValue(se.getId() + "_" + se.getAssessTypeid());
					e.setPid(pid);
					e.setAssessTypeid(se.getAssessTypeid());
					List<SysEnterpriseDto> list0 = getEnterpriseChild(listmt, se.getId());
					if (list0 != null && list0.size() > 0) {
						e.setIsleaf(1);
					} else {
						e.setIsleaf(0);
					}
					e.setChildren(list0);
					list.add(e);
				}

			}
		}
		return list;
	}

	public List<SysEnterpriseDto> getEnterpriseChild(List<SysEnterpriseType> listent, String pid) {
		List<SysEnterpriseDto> list = new ArrayList<SysEnterpriseDto>();

		if (listent != null && listent.size() > 0) {
			int m = listent.size();
			for (int i = 0; i < m; i++) {
				SysEnterpriseType se = listent.get(i);

				if (pid.equals(se.getPid())) {
					SysEnterpriseDto e = new SysEnterpriseDto();
					e.setId(se.getId());
					e.setName(se.getId() + ":" + se.getName());
					e.setValue(se.getId() + "_" + se.getAssessTypeid());
					e.setPid(pid);
					e.setAssessTypeid(se.getAssessTypeid());
					List<SysEnterpriseDto> list0 = getEnterpriseChild(listent, se.getId());
					if (list0 != null && list0.size() > 0) {
						e.setIsleaf(1);
					} else {
						e.setIsleaf(0);
					}
					e.setChildren(list0);
					list.add(e);
				}
			}
		}
		return list;
	}

	/**
	 * 获取企业码
	 *
	 * @return
	 */
	public String getEnterpriseNum() {

		Date d = new Date();
		String num = String.valueOf(d.getTime());
		return num;
	}

	/**
	 * 多租户菜单初始化
	 *
	 * @param iAdmin true：超级管理员
	 * @return
	 */
	public List<SysMenu> initMenuTenant(String tenantId, String type) {
		List<SysMenu> listMenu = new ArrayList<SysMenu>();
		String MODEL_ID = SysMenuService.MODEL_ID;
		String ROOT_ID = SysMenuService.ROOT_ID;

		Where where = Where.create();
//		where.eq(SysMenu::getTenant_id, tenantId);
		// 查菜单是否初始化过
		List<SysMenu> listmu = dao.rawQueryListByWhereWithTenant(tenantId, SysMenu.class, where);
		if (listmu != null && listmu.size() > 0) {
			// 不需要初始化，直接查出来用，
			int m = listmu.size();
			for (int i = 0; i < m; i++) {
				SysMenu e = listmu.get(i);
				Integer isEnterprise = e.getIsEnterprise() == null ? 0 : e.getIsEnterprise();
				Integer isUser = e.getIsUser() == null ? 0 : e.getIsUser();
				if ("qy".equals(type) && isEnterprise == 1) {// 企业
					listMenu.add(e);
				} else if ("gr".equals(type) && isUser == 1) {// 个人
					listMenu.add(e);
				}
			}
		} else {
			// 初始化菜单

			// 1.初始化root
			int lft = 0;
			int rgt = 999;
			// List<SysMenu> list = new ArrayList<SysMenu>();

			lft++;
			rgt--;
			// 系统设置
			SysMenu root = getSysMenu(tenantId, type, lft, rgt, MODEL_ID, ROOT_ID, "#", "/root", "根菜单", "",
					SysMenu.TYPE_ROOT);
			// 先手动添加根节点
			dao.rawInsertWithTenant(tenantId, root);
			// 调用同步租户默认菜单
			listMenu = tenantMenuSrv.initTenantMenuListByDefault(tenantId);

			/*
			 * list.add(root);
			 * 
			 * 
			 * // 2.系统设置 lft++; rgt--; //系统设置 SysMenu menu_xtsz= getSysMenu(tenantId, type,
			 * lft, rgt, MODEL_ID, root.getId(), "monitor", "/id", "系统设置",
			 * "system",SysMenu.TYPE_DIR); list.add(menu_xtsz); listMenu.add(menu_xtsz);
			 * 
			 * 
			 * // 手机端===========.系统评测 lft++; rgt--; //系统设置 SysMenu menu_pc=
			 * getSysMenu(tenantId, type, lft, rgt, MODEL_ID, root.getId(), "documentation",
			 * "/ZZVU4UO002CZX4E4FL0160", "中小企业数字化转型", "",SysMenu.TYPE_DIR);
			 * menu_pc.setComponent("Layout"); menu_pc.setApp_icon(null);
			 * menu_pc.setApp_menuName(null); menu_pc.setApp_path(null);
			 * menu_pc.setApplyRange("2"); list.add(menu_pc); listMenu.add(menu_pc); //
			 * 手机端===========数字化水平评测 lft++; rgt--; //系统设置 SysMenu menu_szpc=
			 * getSysMenu(tenantId, type, lft, rgt, MODEL_ID, menu_pc.getId(),
			 * "documentation", "/pages/assesstable/index", "数字化水平评测",
			 * "pages",SysMenu.TYPE_MENU);
			 * menu_szpc.setComponent("pages/assesstable/index");
			 * menu_szpc.setApp_icon("star-filled"); menu_szpc.setApp_menuName("数字化水平评测");
			 * menu_szpc.setApp_path("/pages/assesstable/index");
			 * menu_szpc.setApplyRange("2"); list.add(menu_szpc); listMenu.add(menu_szpc);
			 * // 手机端===========服务商满意度调查 lft++; rgt--; //系统设置 SysMenu menu_fwdc=
			 * getSysMenu(tenantId, type, lft, rgt, MODEL_ID, menu_pc.getId(),
			 * "documentation", "/pages/assesstable/index?tableId=myd", "服务商满意度调查",
			 * "pages",SysMenu.TYPE_MENU);
			 * menu_fwdc.setComponent("pages/assesstable/index");
			 * menu_fwdc.setApp_icon("paperplane-filled");
			 * menu_fwdc.setApp_menuName("服务商满意度调查");
			 * menu_fwdc.setApp_path("/pages/assesstable/index?tableId=myd");
			 * menu_fwdc.setApplyRange("2"); list.add(menu_fwdc); listMenu.add(menu_fwdc);
			 * 
			 * 
			 * 
			 * lft++; rgt--; //菜单管理 SysMenu menu_cdgl= getSysMenu(tenantId, type, lft, rgt,
			 * MODEL_ID, menu_xtsz.getId(), "table", "/system/menu/index", "菜单管理",
			 * "system",SysMenu.TYPE_MENU); list.add(menu_cdgl); listMenu.add(menu_cdgl);
			 * 
			 * lft++; rgt--; //机构设置 SysMenu menu_jggl= getSysMenu(tenantId, type, lft, rgt,
			 * MODEL_ID, menu_xtsz.getId(), "tree", "/system/org/index", "机构设置",
			 * "system",SysMenu.TYPE_MENU); list.add(menu_jggl); listMenu.add(menu_jggl);
			 * lft++; rgt--; //岗位设置 SysMenu menu_gwgl= getSysMenu(tenantId, type, lft, rgt,
			 * MODEL_ID, menu_xtsz.getId(), "post", "/system/post/index", "岗位管理",
			 * "system",SysMenu.TYPE_MENU); list.add(menu_gwgl); listMenu.add(menu_gwgl);
			 * lft++; rgt--; //人员设置 SysMenu menu_rygl= getSysMenu(tenantId, type, lft, rgt,
			 * MODEL_ID, menu_xtsz.getId(), "user", "/system/employee/index", "人员管理",
			 * "system",SysMenu.TYPE_MENU); list.add(menu_rygl); listMenu.add(menu_rygl);
			 * lft++; rgt--; //角色设置 SysMenu menu_jsgl= getSysMenu(tenantId, type, lft, rgt,
			 * MODEL_ID, menu_xtsz.getId(), "peoples", "/system/role/index", "角色管理",
			 * "system",SysMenu.TYPE_MENU); list.add(menu_jsgl); listMenu.add(menu_jsgl);
			 * 
			 * try { dao.rawInsertBatchWithTenant(tenantId,list); }catch(Exception ex) {
			 * listMenu=new ArrayList<SysMenu>(); }
			 */

		}
		return listMenu;
	}

	/**
	 * 初始化当前租户各模块信息
	 * 
	 * @param tenantId     租户ID
	 * @param type         qy（企业） 或者 gr（个人）
	 * @param registerBean 企业注册信息
	 * @param empBean      人员信息（企业注册时为住户管理员，个人注册时为普通用户）
	 */
	@SuppressWarnings("rawtypes")
	public void initAllModuleInfo(String tenantId, String type, SysMultiTenantDto registerBean, EmployeeDto empBean) {
		try {
			List<SysModule> modulelist = serv.getModuleList();
			if (modulelist != null) {
				Class[] argsClass = new Class[] { String.class, String.class, SysMultiTenantDto.class,
						EmployeeDto.class };
				Object[] argsValue = new Object[] { tenantId, type, registerBean, empBean };
				log.info("\r\n********初始化当前租户各模块信息-开始********租户ID：" + tenantId);
				for (SysModule item : modulelist) {
					try {
						StringBuffer sb = new StringBuffer();
						sb.append("正在初始化「");
						sb.append(item.getModuleName());
						sb.append("」( ");
						sb.append(item.getModuleCode());
						sb.append(")模块，包路径： ");
						sb.append(StringUtils.isEmpty(item.getModulePackPath()) ? "无" : item.getModulePackPath());
						log.info(sb.toString());
						String modulePackPath = item.getModulePackPath();// 微服务初始化数据包路径
						if (StringUtils.isNotEmpty(modulePackPath)) {
							modulePackPath = modulePackPath.trim() + ".InitTenantData";
							ClassExecute classExec = new ClassExecute();
							if ("qy".equals(type)) {// 企业注册
								classExec.execClassByName(modulePackPath, "initDataEnterprise", argsClass, argsValue);
							} else if ("gr".equals(type)) {// 个人注册
								classExec.execClassByName(modulePackPath, "initDataPerson", argsClass, argsValue);
							}
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}
				}
				log.info("\r\n********初始化当前租户各模块信息-结束********租户ID：" + tenantId);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}

	/**
	 * @param tenantId   多租户id
	 * @param type       类型（企业，个人）
	 * @param lft
	 * @param rgt
	 * @param MODEL_ID（）
	 * @param pid        父id
	 * @param icon       图标
	 * @param path       路径
	 * @param menuName   菜单名
	 * @param moduleCode
	 * @param menuType   菜单类型
	 * @return
	 */
	public SysMenu getSysMenu(String tenantId, String type, Integer lft, Integer rgt, String MODEL_ID, String pid,
			String icon, String path, String menuName, String moduleCode, String menuType) {
		SysMenu menu0 = new SysMenu();
		menu0.setModuleCode("system");// 模块编码
		menu0.setId(TMUID.getUID());
		menu0.setDelFlag(0);
		menu0.setLft(lft);
		menu0.setRgt(rgt);
		menu0.setModelId(MODEL_ID);
		menu0.setPid(pid);// menu1.getId()
		menu0.setIcon(icon);
		if ("/id".equals(path)) {
			path = "/" + menu0.getId();
		}
		if (menuType != null && menuType.equals(SysMenu.TYPE_MENU)) {
			menu0.setComponent(path);
		}
		menu0.setPath(path);
		menu0.setIsCache("1");
		menu0.setIsFrame("1");
		menu0.setApplyRange("1");
//		menu0.setApp_icon("");
//		menu0.setApp_menuName("");
//		menu0.setApp_path("");
//		menu0.setApp_query("");
		menu0.setShowType("0");
		menu0.setMenuName(menuName);// 人员管理
		menu0.setMenuType(menuType);
		menu0.setModuleCode(moduleCode);
		menu0.setStatus("0");
		menu0.setVisible("1");
		if ("qy".equals(type)) {// 企业
			menu0.setIsEnterprise(1);
			menu0.setIsUser(0);
		} else if ("gr".equals(type)) {// 个人
			menu0.setIsEnterprise(0);
			menu0.setIsUser(1);
		}
//		menu0.setTenant_id(tenantId);
		return menu0;
	}

	/**
	 * 租户数据初始化(启用注册成功时调用)
	 *
	 * @param tenantId
	 */
	@Override
	public void initData(SysMultiTenantDto bean, String tenantId, String type) throws DuplicateKeyException {
		if (StringUtils.isEmpty(tenantId)) {
			tenantId = MultiTenantUtils.getTenantId();
		}
		String roleid = "";
		String orgid = "";
		String postid = "";
		if (type != null && "gr".equals(type)) {// type ["gr","qy"] gr个人，qy企业
			roleid = "user:" + tenantId;// 初始化角色--一般用户user_
			orgid = "gs:" + tenantId;// 初始化机构--公司parentCompany_
			postid = "zy:" + tenantId;// --初始化岗位--职员officeClerk_
		} else {
			roleid = "admin:" + tenantId;// 初始化角色--管理员admin_
			orgid = "gs:" + tenantId;// 初始化机构--公司
			// postid = "generalManager_" + tenantId;// --初始化岗位--总经理
			postid = null;
		}

		if (type != null && "qy".equals(type)) {
			// 1.菜单初始化
			List<SysMenu> permidList = initMenuTenant(tenantId, type);// 菜单id
			// 2.角色初始化
			sysRoleService.initTenantData(tenantId);// "admin_" + tenantId 管理权限 ,"user_" + tenantId 普通权限
			// 3.初始化菜单和角色
			createRolePerm(permidList, roleid, tenantId);
			// 4.初始化组织机构
			createOrg(bean, tenantId);// "parentCompany_"+tenantId 为公司名称对应的id

			// 5.初始化岗位信息
			createPost(bean, tenantId);// generalManager_"+tenantId 总经理 , "manager_"+tenantId 经理 ,
			// "officeClerk_"+tenantId 职员

		}
		// 6.初始化人员信息
		EmployeeDto e = createEmployee(bean, tenantId, orgid, postid, roleid);

		// 注册用户登录表
		try {
			createLogin(bean, tenantId, e.getEmpTmuid());
		} catch (DuplicateKeyException ex) {
			throw ex;
		}
		// 99.调用各模块需初始化的内容
		initAllModuleInfo(tenantId, type, bean, e);

	}

	/**
	 * 根据租户id获取企业信息
	 *
	 * @param tenantId
	 * @return
	 */
	public SysMultiTenant getSysMultiTenant(String tenantId) {
		SysMultiTenant bean = this.getSysMultiTenantFromRedis(tenantId); // 优先从redis中获取
		if (bean == null) {
			Where where = Where.create();
			where.eq(SysMultiTenant::getTenant_id, tenantId);
			where.eq(SysMultiTenant::getTmused, 1);
			List<SysMultiTenant> list = dao.rawQueryListByWhereDisableTenant(SysMultiTenant.class, where);
			if (list != null && list.size() > 0) {
				bean = list.get(0);
				this.setSysMultiTenantToRedis(bean);// 存入redis
			}
		}
		return bean;
	}

	private SysMultiTenant getSysMultiTenantFromRedis(String tenantId) {
		return redisUtil.getMapValue(RED_KEY_TENANT, tenantId, SysMultiTenant.class);
	}

	private void setSysMultiTenantToRedis(SysMultiTenant bean) {
		redisUtil.setMapValue(RED_KEY_TENANT, bean.getTenant_id(), bean);
	}

	/**
	 * 根据行业id获取行业信息
	 *
	 * @param id
	 * @return
	 */
	public SysEnterpriseType getSysEnterpriseType(String id) {
		SysEnterpriseType bean = null;
		Where where = Where.create();
		where.eq(SysEnterpriseType::getId, id);
		where.eq(SysEnterpriseType::getTmused, 1);
		List<SysEnterpriseType> list = dao.rawQueryListByWhereDisableTenant(SysEnterpriseType.class, where);
		if (list != null && list.size() > 0) {
			bean = list.get(0);
		}
		return bean;
	}

	/**
	 * 获企业更多信息
	 *
	 * @return
	 */
	@Override
	public SysMultiTenant getSysMultiTenant() {
		SysMultiTenant bean = new SysMultiTenant();
		SysUser user = SysUserHolder.getCurrentUser();
		// String id = user.getId();// 人员id
		String tenantId = user.getTenant_id();// 租户id

//    	根据人员id获取人员表中的多租户id
//    	Where where = Where.create();
//		where.eq(SysEmployeeInfo::getUsed, 1);
//		where.eq(SysEmployeeInfo::getId, id);
//		List<SysEmployeeInfo> list = dao.rawQueryListByWhereDisableTenant(SysEmployeeInfo.class, where);
//    	if(list!=null&&list.size()>0) {
//    		SysEmployeeInfo e=list.get(0);
//    		tenantId=e.getTenant_id();
//    	}

		// 获取企业信息
		bean = getSysMultiTenant(tenantId);

		return bean;
	}

	/**
	 * 保存企业信息
	 *
	 * @param bean
	 * @return
	 */
	@Override
	public boolean saveMultiTenantData(SysMultiTenant bean) {
		boolean bool = false;
		int i = dao.updateById(bean);
		if (i > 0) {
			bool = true;
		}
		return bool;
	}

	/**
	 * 获取当前登录企业的企业码
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */

	@Override
	public String getCurrentEnterpriseNum() {
		SysUser currentUser = SysUserUtil.getCurrentUser();
		boolean tenantAdmin = SysUserUtil.isTenantAdmin(currentUser);
		if (tenantAdmin) {
			SysMultiTenant sysMultiTenant = getSysMultiTenant(currentUser.getTenant_id());
			return sysMultiTenant.getEnterpriseNum();
		} else {
			return "";
		}
	}

	/**
	 * 企业邀请信息
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public String getEnterpriseInvite() {
		JSONObject json = new JSONObject();
		json.put("token", "");
		json.put("endDate", "");

		SysUser currentUser = SysUserUtil.getCurrentUser();
		if (StringUtils.isNotEmpty(currentUser.getTenant_id())) {
			// 有效时间间隔
			Long limitTime = VALID_TIME * 60 * 1000L;
			// 生成邀请信息token
			HashMap<String, Object> claims = new HashMap<String, Object>(5);
			claims.put("id", currentUser.getId());
			claims.put("name", currentUser.getUserName());
			claims.put("tenant_id", currentUser.getTenant_id());
			claims.put("productTime", new Date());
			Date endDate = new Date(System.currentTimeMillis() + limitTime);// token失效截止时间
			// 生成token
			String token = Jwts.builder().signWith(SignatureAlgorithm.HS512, "yhznkj-tm4").setClaims(claims)
					.setIssuer("YHZNKJ").setId(currentUser.getId()).setIssuedAt(new Date()).setExpiration(endDate)
					.compact();

			json.put("token", token);
			json.put("tenant_id", currentUser.getTenant_id());
			json.put("endDate", DateTimeUtils.formatDate(endDate));
		} else {
			// 无企业码
		}
		return json.toString();
	}

	/**
	 * 企业码安全性校验
	 *
	 * <AUTHOR>
	 * @return true 通过 false 不通过
	 * @params
	 */
	@Override
	public JSONObject enterpriseSecurityCheck(String token) {
		JSONObject result = new JSONObject();
		if (StringUtils.isNotEmpty(token)) {
			try {
				Claims claims = (Claims) Jwts.parser().setSigningKey("yhznkj-tm4").parseClaimsJws(token).getBody();
				// String id = claims.get("id", String.class);
				// String name = claims.get("name", String.class);
				String tid = claims.get("tenant_id", String.class);
				// Date productTime = claims.get("productTime", Date.class);
				result.put("flag", 1);
				result.put("msg", "认证成功");
				result.put("tenant_id", tid);
				SysMultiTenant mt = this.getSysMultiTenant(tid);
				result.put("enterprise_name", mt.getEnterpriseName());
				result.put("enterprise_typeid", mt.getEnterpriseTypeId());
				result.put("province", mt.getProvince());// 省
				result.put("city", mt.getCity());// 市
				result.put("area", mt.getArea());// 区
				result.put("enterpriseTypeIdPath", mt.getEnterpriseTypeIdPath());

				return result;
			} catch (SignatureException exception) {
				// 令牌无效
				result.put("flag", -1);
				result.put("msg", "邀请令牌无效");
				return result;
			} catch (ExpiredJwtException exception) {
				result.put("flag", 0);
				result.put("msg", "邀请令牌过期");
				return result;
			}
		}
		result.put("flag", -1);
		result.put("msg", "未获取到邀请令牌");
		return result;
	}

	/**
	 * 缓存邀请信息
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public String cacheInviteInfo(String enterpriseInviteToken) {
		String tenantId = SysUserUtil.getCurrentUser().getTenant_id();
		// 删除旧的信息
		redisUtil.delete("SYSTEM:MULTI:INVITE:" + tenantId);
		// 存入redis 每个租户只存在一个邀请信息
		redisUtil.setJSONString("SYSTEM:MULTI:INVITE:" + tenantId, enterpriseInviteToken);
		return tenantId;
	}

	/**
	 * 从缓存中获取邀请信息
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public String getInviteInfoByCache(String id) {
		String string = redisUtil.getString("SYSTEM:MULTI:INVITE:" + id);
		return string;
	}

	/**
	 * 根据租户id获取其租户管理员 tips: 此方法使用的查询方法均是不使用租户id
	 * 
	 * @param tenantId
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public SysEmployeeInfo getAdminInfoByTenantId(String tenantId) {
		// 根据租户id 获取其管理员人员id
		if (MultiTenantUtils.enalbe() && (!"0".equals(tenantId))) {
			Where where = Where.create();
			where.eq(SysUserRole::getRoleid, "admin:" + tenantId);
			List<SysUserRole> sysUserRoles = dao.rawQueryListByWhereDisableTenant(SysUserRole.class, where);
			if (StringUtils.isNotEmpty(sysUserRoles)) {
				SysUserRole sysUserRole = sysUserRoles.get(0);
				// 通过人员id 查找人员信息
				SysEmployeeInfo sysEmployeeInfo = dao.queryObjectByIdDisableTenant(SysEmployeeInfo.class,
						sysUserRole.getUserid());
				return sysEmployeeInfo;
			}
		} else {
			return dao.queryObjectByIdDisableTenant(SysEmployeeInfo.class, "administrator");
		}
		return null;
	}

	/**
	 * 初始化企业类型
	 * 
	 * @return
	 */
	@Override
	public void initEnterpriseType() {
		long c = dao.queryCountDisableTenant(SysEnterpriseType.class, null);
		if (c <= 0) {
			String json = getEnterpriseType();
			JSONArray ary = JSONArray.parseArray(json);
			List<SysEnterpriseType> list = new ArrayList<SysEnterpriseType>();
			for (int i = 0; i < ary.size(); i++) {
				SysEnterpriseType bean = new SysEnterpriseType();
				JSONObject obj = ary.getJSONObject(i);
				bean.setId(obj.getString("ID"));
				bean.setEnterpriseLevel(obj.getInteger("ENTERPRISELEVEL"));
				bean.setAssessTypeid(obj.getString("ASSESSTYPEID"));
				bean.setName(obj.getString("NAME"));
				bean.setPid(obj.getString("PID"));
				bean.setTmused(1);
				list.add(bean);
			}
			dao.insertBatch(list, 500);
		}
	}

	private String getEnterpriseType() {
		StringBuffer buff = new StringBuffer();
		buff.append(
				"[{\"ID\": \"01\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"农业\",\"PID\": \"A\"}");
		buff.append(
				", {\"ID\": \"011\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"谷物种植\",\"PID\": \"01\"}");
		buff.append(
				", {\"ID\": \"0111\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"稻谷种植\",\"PID\": \"011\"}");
		buff.append(
				", {\"ID\": \"0112\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"小麦种植\",\"PID\": \"011\"}");
		buff.append(
				", {\"ID\": \"0113\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"玉米种植\",\"PID\": \"011\"}");
		buff.append(
				", {\"ID\": \"0119\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他谷物种植\",\"PID\": \"011\"}");
		buff.append(
				", {\"ID\": \"012\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"豆类、油料和薯类种植\",\"PID\": \"01\"}");
		buff.append(
				", {\"ID\": \"0121\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"豆类种植\",\"PID\": \"012\"}");
		buff.append(
				", {\"ID\": \"0122\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"油料种植\",\"PID\": \"012\"}");
		buff.append(
				", {\"ID\": \"0123\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"薯类种植\",\"PID\": \"012\"}");
		buff.append(
				", {\"ID\": \"013\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"棉、麻、糖、烟草种植\",\"PID\": \"01\"}");
		buff.append(
				", {\"ID\": \"0131\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"棉花种植\",\"PID\": \"013\"}");
		buff.append(
				", {\"ID\": \"0132\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"麻类种植\",\"PID\": \"013\"}");
		buff.append(
				", {\"ID\": \"0133\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"糖料种植\",\"PID\": \"013\"}");
		buff.append(
				", {\"ID\": \"0134\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"烟草种植\",\"PID\": \"013\"}");
		buff.append(
				", {\"ID\": \"014\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"蔬菜、食用菌及园艺作物种植\",\"PID\": \"01\"}");
		buff.append(
				", {\"ID\": \"0141\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"蔬菜种植\",\"PID\": \"014\"}");
		buff.append(
				", {\"ID\": \"0142\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"食用菌种植\",\"PID\": \"014\"}");
		buff.append(
				", {\"ID\": \"0143\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"花卉种植\",\"PID\": \"014\"}");
		buff.append(
				", {\"ID\": \"0149\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他园艺作物种植\",\"PID\": \"014\"}");
		buff.append(
				", {\"ID\": \"015\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水果种植\",\"PID\": \"01\"}");
		buff.append(
				", {\"ID\": \"0151\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"仁果类和核果类水果种植\",\"PID\": \"015\"}");
		buff.append(
				", {\"ID\": \"0152\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"葡萄种植\",\"PID\": \"015\"}");
		buff.append(
				", {\"ID\": \"0153\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"柑橘类种植\",\"PID\": \"015\"}");
		buff.append(
				", {\"ID\": \"0154\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"香蕉等亚热带水果种植\",\"PID\": \"015\"}");
		buff.append(
				", {\"ID\": \"0159\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他水果种植\",\"PID\": \"015\"}");
		buff.append(
				", {\"ID\": \"016\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"坚果、含油果、香料和饮料作物种植\",\"PID\": \"01\"}");
		buff.append(
				", {\"ID\": \"0161\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"坚果种植\",\"PID\": \"016\"}");
		buff.append(
				", {\"ID\": \"0162\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"含油果种植\",\"PID\": \"016\"}");
		buff.append(
				", {\"ID\": \"0163\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"香料作物种植\",\"PID\": \"016\"}");
		buff.append(
				", {\"ID\": \"0164\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"茶叶种植\",\"PID\": \"016\"}");
		buff.append(
				", {\"ID\": \"0169\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他饮料作物种植\",\"PID\": \"016\"}");
		buff.append(
				", {\"ID\": \"017\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"中药材种植\",\"PID\": \"01\"}");
		buff.append(
				", {\"ID\": \"0171\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"中草药种植\",\"PID\": \"017\"}");
		buff.append(
				", {\"ID\": \"0179\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他中药材种植\",\"PID\": \"017\"}");
		buff.append(
				", {\"ID\": \"018\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"草种植及割草\",\"PID\": \"01\"}");
		buff.append(
				", {\"ID\": \"0181\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"草种植\",\"PID\": \"018\"}");
		buff.append(
				", {\"ID\": \"0182\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"天然草原割草\",\"PID\": \"018\"}");
		buff.append(
				", {\"ID\": \"0190\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他农业\",\"PID\": \"01\"}");
		buff.append(
				", {\"ID\": \"02\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"林业\",\"PID\": \"A\"}");
		buff.append(
				", {\"ID\": \"021\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"林木育种和育苗\",\"PID\": \"02\"}");
		buff.append(
				", {\"ID\": \"0211\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"林木育种\",\"PID\": \"021\"}");
		buff.append(
				", {\"ID\": \"0212\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"林木育苗\",\"PID\": \"021\"}");
		buff.append(
				", {\"ID\": \"0220\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"造林和更新\",\"PID\": \"02\"}");
		buff.append(
				", {\"ID\": \"023\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"森林经营、管护和改培\",\"PID\": \"02\"}");
		buff.append(
				", {\"ID\": \"0231\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"森林经营和管护\",\"PID\": \"023\"}");
		buff.append(
				", {\"ID\": \"0232\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"森林改培\",\"PID\": \"023\"}");
		buff.append(
				", {\"ID\": \"024\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"木材和竹材采运\",\"PID\": \"02\"}");
		buff.append(
				", {\"ID\": \"0241\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"木材采运\",\"PID\": \"024\"}");
		buff.append(
				", {\"ID\": \"0242\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"竹材采运\",\"PID\": \"024\"}");
		buff.append(
				", {\"ID\": \"025\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"林产品采集\",\"PID\": \"02\"}");
		buff.append(
				", {\"ID\": \"0251\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"木竹材林产品采集\",\"PID\": \"025\"}");
		buff.append(
				", {\"ID\": \"0252\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"非木竹材林产品采集\",\"PID\": \"025\"}");
		buff.append(
				", {\"ID\": \"03\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"畜牧业\",\"PID\": \"A\"}");
		buff.append(
				", {\"ID\": \"031\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"牲畜饲养\",\"PID\": \"03\"}");
		buff.append(
				", {\"ID\": \"0311\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"牛的饲养\",\"PID\": \"031\"}");
		buff.append(
				", {\"ID\": \"0312\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"马的饲养\",\"PID\": \"031\"}");
		buff.append(
				", {\"ID\": \"0313\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"猪的饲养\",\"PID\": \"031\"}");
		buff.append(
				", {\"ID\": \"0314\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"羊的饲养\",\"PID\": \"031\"}");
		buff.append(
				", {\"ID\": \"0315\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"骆驼饲养\",\"PID\": \"031\"}");
		buff.append(
				", {\"ID\": \"0319\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他牲畜饲养\",\"PID\": \"031\"}");
		buff.append(
				", {\"ID\": \"032\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"家禽饲养\",\"PID\": \"03\"}");
		buff.append(
				", {\"ID\": \"0321\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"鸡的饲养\",\"PID\": \"032\"}");
		buff.append(
				", {\"ID\": \"0322\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"鸭的饲养\",\"PID\": \"032\"}");
		buff.append(
				", {\"ID\": \"0323\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"鹅的饲养\",\"PID\": \"032\"}");
		buff.append(
				", {\"ID\": \"0329\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他家禽饲养\",\"PID\": \"032\"}");
		buff.append(
				", {\"ID\": \"0330\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"狩猎和捕捉动物\",\"PID\": \"03\"}");
		buff.append(
				", {\"ID\": \"039\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他畜牧业\",\"PID\": \"03\"}");
		buff.append(
				", {\"ID\": \"0391\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"兔的饲养\",\"PID\": \"039\"}");
		buff.append(
				", {\"ID\": \"0392\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"蜜蜂饲养\",\"PID\": \"039\"}");
		buff.append(
				", {\"ID\": \"0399\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他未列明畜牧业\",\"PID\": \"039\"}");
		buff.append(
				", {\"ID\": \"04\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"渔业\",\"PID\": \"A\"}");
		buff.append(
				", {\"ID\": \"041\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水产养殖\",\"PID\": \"04\"}");
		buff.append(
				", {\"ID\": \"0411\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"海水养殖\",\"PID\": \"041\"}");
		buff.append(
				", {\"ID\": \"0412\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"内陆养殖\",\"PID\": \"041\"}");
		buff.append(
				", {\"ID\": \"042\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水产捕捞\",\"PID\": \"04\"}");
		buff.append(
				", {\"ID\": \"0421\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"海水捕捞\",\"PID\": \"042\"}");
		buff.append(
				", {\"ID\": \"0422\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"内陆捕捞\",\"PID\": \"042\"}");
		buff.append(
				", {\"ID\": \"05\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农、林、牧、渔专业及辅助性活动\",\"PID\": \"A\"}");
		buff.append(
				", {\"ID\": \"051\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农业专业及辅助性活动\",\"PID\": \"05\"}");
		buff.append(
				", {\"ID\": \"0511\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"种子种苗培育活动\",\"PID\": \"051\"}");
		buff.append(
				", {\"ID\": \"0512\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农业机械活动\",\"PID\": \"051\"}");
		buff.append(
				", {\"ID\": \"0513\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"灌溉活动\",\"PID\": \"051\"}");
		buff.append(
				", {\"ID\": \"0514\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农产品初加工活动\",\"PID\": \"051\"}");
		buff.append(
				", {\"ID\": \"0515\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农作物病虫害防治活动\",\"PID\": \"051\"}");
		buff.append(
				", {\"ID\": \"0519\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他农业专业及辅助性活动\",\"PID\": \"051\"}");
		buff.append(
				", {\"ID\": \"052\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"林业专业及辅助性活动\",\"PID\": \"05\"}");
		buff.append(
				", {\"ID\": \"0521\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"林业有害生物防治活动\",\"PID\": \"052\"}");
		buff.append(
				", {\"ID\": \"0522\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"森林防火活动\",\"PID\": \"052\"}");
		buff.append(
				", {\"ID\": \"0523\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"林产品初级加工活动\",\"PID\": \"052\"}");
		buff.append(
				", {\"ID\": \"0529\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他林业专业及辅助性活动\",\"PID\": \"052\"}");
		buff.append(
				", {\"ID\": \"053\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"畜牧专业及辅助性活动\",\"PID\": \"05\"}");
		buff.append(
				", {\"ID\": \"0531\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"畜牧良种繁殖活动\",\"PID\": \"053\"}");
		buff.append(
				", {\"ID\": \"0532\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"畜禽粪污处理活动\",\"PID\": \"053\"}");
		buff.append(
				", {\"ID\": \"0539\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他畜牧专业及辅助性活动\",\"PID\": \"053\"}");
		buff.append(
				", {\"ID\": \"054\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"渔业专业及辅助性活动\",\"PID\": \"05\"}");
		buff.append(
				", {\"ID\": \"0541\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"鱼苗及鱼种场活动\",\"PID\": \"054\"}");
		buff.append(
				", {\"ID\": \"0549\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他渔业专业及辅助性活动\",\"PID\": \"054\"}");
		buff.append(
				", {\"ID\": \"06\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"煤炭开采和洗选业\",\"PID\": \"B\"}");
		buff.append(
				", {\"ID\": \"0610\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"烟煤和无烟煤开采洗选\",\"PID\": \"06\"}");
		buff.append(
				", {\"ID\": \"0620\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"褐煤开采洗选\",\"PID\": \"06\"}");
		buff.append(
				", {\"ID\": \"0690\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他煤炭采选\",\"PID\": \"06\"}");
		buff.append(
				", {\"ID\": \"07\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"石油和天然气开采业\",\"PID\": \"B\"}");
		buff.append(
				", {\"ID\": \"071\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"石油开采\",\"PID\": \"07\"}");
		buff.append(
				", {\"ID\": \"0711\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"陆地石油开采\",\"PID\": \"071\"}");
		buff.append(
				", {\"ID\": \"0712\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"海洋石油开采\",\"PID\": \"071\"}");
		buff.append(
				", {\"ID\": \"072\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"天然气开采\",\"PID\": \"07\"}");
		buff.append(
				", {\"ID\": \"0721\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"陆地天然气开采\",\"PID\": \"072\"}");
		buff.append(
				", {\"ID\": \"0722\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"海洋天然气及可燃冰开采\",\"PID\": \"072\"}");
		buff.append(
				", {\"ID\": \"08\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"黑色金属矿采选业\",\"PID\": \"B\"}");
		buff.append(
				", {\"ID\": \"0810\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"铁矿采选\",\"PID\": \"08\"}");
		buff.append(
				", {\"ID\": \"0820\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"锰矿、铬矿采选\",\"PID\": \"08\"}");
		buff.append(
				", {\"ID\": \"0890\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他黑色金属矿采选\",\"PID\": \"08\"}");
		buff.append(
				", {\"ID\": \"09\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"有色金属矿采选业\",\"PID\": \"B\"}");
		buff.append(
				", {\"ID\": \"091\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"常用有色金属矿采选\",\"PID\": \"09\"}");
		buff.append(
				", {\"ID\": \"0911\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"铜矿采选\",\"PID\": \"091\"}");
		buff.append(
				", {\"ID\": \"0912\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"铅锌矿采选\",\"PID\": \"091\"}");
		buff.append(
				", {\"ID\": \"0913\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"镍钴矿采选\",\"PID\": \"091\"}");
		buff.append(
				", {\"ID\": \"0914\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"锡矿采选\",\"PID\": \"091\"}");
		buff.append(
				", {\"ID\": \"0915\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"锑矿采选\",\"PID\": \"091\"}");
		buff.append(
				", {\"ID\": \"0916\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"铝矿采选\",\"PID\": \"091\"}");
		buff.append(
				", {\"ID\": \"0917\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"镁矿采选\",\"PID\": \"091\"}");
		buff.append(
				", {\"ID\": \"0919\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他常用有色金属矿采选\",\"PID\": \"091\"}");
		buff.append(
				", {\"ID\": \"092\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"贵金属矿采选\",\"PID\": \"09\"}");
		buff.append(
				", {\"ID\": \"0921\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"金矿采选\",\"PID\": \"092\"}");
		buff.append(
				", {\"ID\": \"0922\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"银矿采选\",\"PID\": \"092\"}");
		buff.append(
				", {\"ID\": \"0929\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他贵金属矿采选\",\"PID\": \"092\"}");
		buff.append(
				", {\"ID\": \"093\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"稀有稀土金属矿采选\",\"PID\": \"09\"}");
		buff.append(
				", {\"ID\": \"0931\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"钨钼矿采选\",\"PID\": \"093\"}");
		buff.append(
				", {\"ID\": \"0932\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"稀土金属矿采选\",\"PID\": \"093\"}");
		buff.append(
				", {\"ID\": \"0933\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"放射性金属矿采选\",\"PID\": \"093\"}");
		buff.append(
				", {\"ID\": \"0939\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他稀有金属矿采选\",\"PID\": \"093\"}");
		buff.append(
				", {\"ID\": \"10\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"非金属矿采选业\",\"PID\": \"B\"}");
		buff.append(
				", {\"ID\": \"101\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"土砂石开采\",\"PID\": \"10\"}");
		buff.append(
				", {\"ID\": \"1011\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"石灰石、石膏开采\",\"PID\": \"101\"}");
		buff.append(
				", {\"ID\": \"1012\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"建筑装饰用石开采\",\"PID\": \"101\"}");
		buff.append(
				", {\"ID\": \"1013\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"耐火土石开采\",\"PID\": \"101\"}");
		buff.append(
				", {\"ID\": \"1019\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"粘土及其他土砂石开采\",\"PID\": \"101\"}");
		buff.append(
				", {\"ID\": \"1020\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"化学矿开采\",\"PID\": \"10\"}");
		buff.append(
				", {\"ID\": \"1030\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"采盐\",\"PID\": \"10\"}");
		buff.append(
				", {\"ID\": \"109\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"石棉及其他非金属矿采选\",\"PID\": \"10\"}");
		buff.append(
				", {\"ID\": \"1091\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"石棉、云母矿采选\",\"PID\": \"109\"}");
		buff.append(
				", {\"ID\": \"1092\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"石墨、滑石采选\",\"PID\": \"109\"}");
		buff.append(
				", {\"ID\": \"1093\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"宝石、玉石采选\",\"PID\": \"109\"}");
		buff.append(
				", {\"ID\": \"1099\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他未列明非金属矿采选\",\"PID\": \"109\"}");
		buff.append(
				", {\"ID\": \"11\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"开采专业及辅助性活动\",\"PID\": \"B\"}");
		buff.append(
				", {\"ID\": \"1110\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"煤炭开采和洗选专业及辅助性活动\",\"PID\": \"11\"}");
		buff.append(
				", {\"ID\": \"1120\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"石油和天然气开采专业及辅助性活动\",\"PID\": \"11\"}");
		buff.append(
				", {\"ID\": \"1190\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他开采专业及辅助性活动\",\"PID\": \"11\"}");
		buff.append(
				", {\"ID\": \"12\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他采矿业\",\"PID\": \"B\"}");
		buff.append(
				", {\"ID\": \"1200\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他采矿业\",\"PID\": \"12\"}");
		buff.append(
				", {\"ID\": \"13\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"农副食品加工业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"131\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"谷物磨制\",\"PID\": \"13\"}");
		buff.append(
				", {\"ID\": \"1311\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"稻谷加工\",\"PID\": \"131\"}");
		buff.append(
				", {\"ID\": \"1312\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"小麦加工\",\"PID\": \"131\"}");
		buff.append(
				", {\"ID\": \"1313\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"玉米加工\",\"PID\": \"131\"}");
		buff.append(
				", {\"ID\": \"1314\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"杂粮加工\",\"PID\": \"131\"}");
		buff.append(
				", {\"ID\": \"1319\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他谷物磨制\",\"PID\": \"131\"}");
		buff.append(
				", {\"ID\": \"132\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"饲料加工 \",\"PID\": \"13\"}");
		buff.append(
				", {\"ID\": \"1321\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"宠物饲料加工\",\"PID\": \"132\"}");
		buff.append(
				", {\"ID\": \"1329\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他饲料加工\",\"PID\": \"132\"}");
		buff.append(
				", {\"ID\": \"133\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"植物油加工\",\"PID\": \"13\"}");
		buff.append(
				", {\"ID\": \"1331\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"食用植物油加工\",\"PID\": \"133\"}");
		buff.append(
				", {\"ID\": \"1332\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"非食用植物油加工\",\"PID\": \"133\"}");
		buff.append(
				", {\"ID\": \"1340\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"制糖业\",\"PID\": \"13\"}");
		buff.append(
				", {\"ID\": \"135\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"屠宰及肉类加工\",\"PID\": \"13\"}");
		buff.append(
				", {\"ID\": \"1351\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"牲畜屠宰\",\"PID\": \"135\"}");
		buff.append(
				", {\"ID\": \"1352\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"禽类屠宰\",\"PID\": \"135\"}");
		buff.append(
				", {\"ID\": \"1353\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"肉制品及副产品加工\",\"PID\": \"135\"}");
		buff.append(
				", {\"ID\": \"136\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"水产品加工\",\"PID\": \"13\"}");
		buff.append(
				", {\"ID\": \"1361\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"水产品冷冻加工\",\"PID\": \"136\"}");
		buff.append(
				", {\"ID\": \"1362\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"鱼糜制品及水产品干腌制加工\",\"PID\": \"136\"}");
		buff.append(
				", {\"ID\": \"1363\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"鱼油提取及制品制造\",\"PID\": \"136\"}");
		buff.append(
				", {\"ID\": \"1369\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他水产品加工\",\"PID\": \"136\"}");
		buff.append(
				", {\"ID\": \"137\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"蔬菜、菌类、水果和坚果加工\",\"PID\": \"13\"}");
		buff.append(
				", {\"ID\": \"1371\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"蔬菜加工\",\"PID\": \"137\"}");
		buff.append(
				", {\"ID\": \"1372\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"食用菌加工\",\"PID\": \"137\"}");
		buff.append(
				", {\"ID\": \"1373\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"水果和坚果加工\",\"PID\": \"137\"}");
		buff.append(
				", {\"ID\": \"139\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他农副食品加工\",\"PID\": \"13\"}");
		buff.append(
				", {\"ID\": \"1391\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"淀粉及淀粉制品制造\",\"PID\": \"139\"}");
		buff.append(
				", {\"ID\": \"1392\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"豆制品制造\",\"PID\": \"139\"}");
		buff.append(
				", {\"ID\": \"1393\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"蛋品加工\",\"PID\": \"139\"}");
		buff.append(
				", {\"ID\": \"1399\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他未列明农副食品加工\",\"PID\": \"139\"}");
		buff.append(
				", {\"ID\": \"14\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"食品制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"141\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"焙烤食品制造\",\"PID\": \"14\"}");
		buff.append(
				", {\"ID\": \"1411\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"糕点、面包制造\",\"PID\": \"141\"}");
		buff.append(
				", {\"ID\": \"1419\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"饼干及其他焙烤食品制造\",\"PID\": \"141\"}");
		buff.append(
				", {\"ID\": \"142\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"糖果、巧克力及蜜饯制造\",\"PID\": \"14\"}");
		buff.append(
				", {\"ID\": \"1421\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"糖果、巧克力制造\",\"PID\": \"142\"}");
		buff.append(
				", {\"ID\": \"1422\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"蜜饯制作\",\"PID\": \"142\"}");
		buff.append(
				", {\"ID\": \"143\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"方便食品制造\",\"PID\": \"14\"}");
		buff.append(
				", {\"ID\": \"1431\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"米、面制品制造\",\"PID\": \"143\"}");
		buff.append(
				", {\"ID\": \"1432\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"速冻食品制造\",\"PID\": \"143\"}");
		buff.append(
				", {\"ID\": \"1433\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"方便面制造\",\"PID\": \"143\"}");
		buff.append(
				", {\"ID\": \"1439\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他方便食品制造\",\"PID\": \"143\"}");
		buff.append(
				", {\"ID\": \"144\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"乳制品制造\",\"PID\": \"14\"}");
		buff.append(
				", {\"ID\": \"1441\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"液体乳制造\",\"PID\": \"144\"}");
		buff.append(
				", {\"ID\": \"1442\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"乳粉制造\",\"PID\": \"144\"}");
		buff.append(
				", {\"ID\": \"1449\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他乳制品制造\",\"PID\": \"144\"}");
		buff.append(
				", {\"ID\": \"145\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"罐头食品制造\",\"PID\": \"14\"}");
		buff.append(
				", {\"ID\": \"1451\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"肉、禽类罐头制造\",\"PID\": \"145\"}");
		buff.append(
				", {\"ID\": \"1452\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"水产品罐头制造\",\"PID\": \"145\"}");
		buff.append(
				", {\"ID\": \"1453\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"蔬菜、水果罐头制造\",\"PID\": \"145\"}");
		buff.append(
				", {\"ID\": \"1459\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他罐头食品制造\",\"PID\": \"145\"}");
		buff.append(
				", {\"ID\": \"146\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"调味品、发酵制品制造\",\"PID\": \"14\"}");
		buff.append(
				", {\"ID\": \"1461\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"味精制造\",\"PID\": \"146\"}");
		buff.append(
				", {\"ID\": \"1462\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"酱油、食醋及类似制品制造\",\"PID\": \"146\"}");
		buff.append(
				", {\"ID\": \"1469\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他调味品、发酵制品制造\",\"PID\": \"146\"}");
		buff.append(
				", {\"ID\": \"149\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他食品制造\",\"PID\": \"14\"}");
		buff.append(
				", {\"ID\": \"1491\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"营养食品制造\",\"PID\": \"149\"}");
		buff.append(
				", {\"ID\": \"1492\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"保健食品制造\",\"PID\": \"149\"}");
		buff.append(
				", {\"ID\": \"1493\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"冷冻饮品及食用冰制造\",\"PID\": \"149\"}");
		buff.append(
				", {\"ID\": \"1494\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"盐加工 \",\"PID\": \"149\"}");
		buff.append(
				", {\"ID\": \"1495\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"食品及饲料添加剂制造\",\"PID\": \"149\"}");
		buff.append(
				", {\"ID\": \"1499\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他未列明食品制造\",\"PID\": \"149\"}");
		buff.append(
				", {\"ID\": \"15\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"酒、饮料及精制茶制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"151\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"酒的制造\",\"PID\": \"15\"}");
		buff.append(
				", {\"ID\": \"1511\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"酒精制造\",\"PID\": \"151\"}");
		buff.append(
				", {\"ID\": \"1512\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"白酒制造\",\"PID\": \"151\"}");
		buff.append(
				", {\"ID\": \"1513\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"啤酒制造\",\"PID\": \"151\"}");
		buff.append(
				", {\"ID\": \"1514\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"黄酒制造\",\"PID\": \"151\"}");
		buff.append(
				", {\"ID\": \"1515\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"葡萄酒制造\",\"PID\": \"151\"}");
		buff.append(
				", {\"ID\": \"1519\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他酒制造\",\"PID\": \"151\"}");
		buff.append(
				", {\"ID\": \"152\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"饮料制造\",\"PID\": \"15\"}");
		buff.append(
				", {\"ID\": \"1521\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"碳酸饮料制造\",\"PID\": \"152\"}");
		buff.append(
				", {\"ID\": \"1522\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"瓶（罐）装饮用水制造\",\"PID\": \"152\"}");
		buff.append(
				", {\"ID\": \"1523\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"果菜汁及果菜汁饮料制造\",\"PID\": \"152\"}");
		buff.append(
				", {\"ID\": \"1524\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"含乳饮料和植物蛋白饮料制造\",\"PID\": \"152\"}");
		buff.append(
				", {\"ID\": \"1525\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"固体饮料制造\",\"PID\": \"152\"}");
		buff.append(
				", {\"ID\": \"1529\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"茶饮料及其他饮料制造\",\"PID\": \"152\"}");
		buff.append(
				", {\"ID\": \"1530\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"精制茶加工\",\"PID\": \"15\"}");
		buff.append(
				", {\"ID\": \"16\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"烟草制品业 \",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"1610\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"烟叶复烤\",\"PID\": \"16\"}");
		buff.append(
				", {\"ID\": \"1620\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"卷烟制造\",\"PID\": \"16\"}");
		buff.append(
				", {\"ID\": \"1690\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他烟草制品制造\",\"PID\": \"16\"}");
		buff.append(
				", {\"ID\": \"17\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"纺织业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"171\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"棉纺织及印染精加工\",\"PID\": \"17\"}");
		buff.append(
				", {\"ID\": \"1711\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"棉纺纱加工\",\"PID\": \"171\"}");
		buff.append(
				", {\"ID\": \"1712\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"棉织造加工\",\"PID\": \"171\"}");
		buff.append(
				", {\"ID\": \"1713\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"棉印染精加工\",\"PID\": \"171\"}");
		buff.append(
				", {\"ID\": \"172\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"毛纺织及染整精加工\",\"PID\": \"17\"}");
		buff.append(
				", {\"ID\": \"1721\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"毛条和毛纱线加工\",\"PID\": \"172\"}");
		buff.append(
				", {\"ID\": \"1722\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"毛织造加工\",\"PID\": \"172\"}");
		buff.append(
				", {\"ID\": \"1723\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"毛染整精加工\",\"PID\": \"172\"}");
		buff.append(
				", {\"ID\": \"173\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"麻纺织及染整精加工\",\"PID\": \"17\"}");
		buff.append(
				", {\"ID\": \"1731\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"麻纤维纺前加工和纺纱\",\"PID\": \"173\"}");
		buff.append(
				", {\"ID\": \"1732\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"麻织造加工\",\"PID\": \"173\"}");
		buff.append(
				", {\"ID\": \"1733\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"麻染整精加工\",\"PID\": \"173\"}");
		buff.append(
				", {\"ID\": \"174\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"丝绢纺织及印染精加工\",\"PID\": \"17\"}");
		buff.append(
				", {\"ID\": \"1741\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"缫丝加工\",\"PID\": \"174\"}");
		buff.append(
				", {\"ID\": \"1742\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"绢纺和丝织加工\",\"PID\": \"174\"}");
		buff.append(
				", {\"ID\": \"1743\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"丝印染精加工\",\"PID\": \"174\"}");
		buff.append(
				", {\"ID\": \"175\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化纤织造及印染精加工\",\"PID\": \"17\"}");
		buff.append(
				", {\"ID\": \"1751\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化纤织造加工\",\"PID\": \"175\"}");
		buff.append(
				", {\"ID\": \"1752\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化纤织物染整精加工\",\"PID\": \"175\"}");
		buff.append(
				", {\"ID\": \"176\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"针织或钩针编织物及其制品制造\",\"PID\": \"17\"}");
		buff.append(
				", {\"ID\": \"1761\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"针织或钩针编织物织造\",\"PID\": \"176\"}");
		buff.append(
				", {\"ID\": \"1762\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"针织或钩针编织物印染精加工\",\"PID\": \"176\"}");
		buff.append(
				", {\"ID\": \"1763\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"针织或钩针编织品制造\",\"PID\": \"176\"}");
		buff.append(
				", {\"ID\": \"177\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"家用纺织制成品制造\",\"PID\": \"17\"}");
		buff.append(
				", {\"ID\": \"1771\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"床上用品制造\",\"PID\": \"177\"}");
		buff.append(
				", {\"ID\": \"1772\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"毛巾类制品制造\",\"PID\": \"177\"}");
		buff.append(
				", {\"ID\": \"1773\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"窗帘、布艺类产品制造\",\"PID\": \"177\"}");
		buff.append(
				", {\"ID\": \"1779\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他家用纺织制成品制造\",\"PID\": \"177\"}");
		buff.append(
				", {\"ID\": \"178\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"产业用纺织制成品制造\",\"PID\": \"17\"}");
		buff.append(
				", {\"ID\": \"1781\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"非织造布制造\",\"PID\": \"178\"}");
		buff.append(
				", {\"ID\": \"1782\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"绳、索、缆制造\",\"PID\": \"178\"}");
		buff.append(
				", {\"ID\": \"1783\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"纺织带和帘子布制造\",\"PID\": \"178\"}");
		buff.append(
				", {\"ID\": \"1784\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"篷、帆布制造\",\"PID\": \"178\"}");
		buff.append(
				", {\"ID\": \"1789\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他产业用纺织制成品制造\",\"PID\": \"178\"}");
		buff.append(
				", {\"ID\": \"18\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"纺织服装、服饰业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"181\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"机织服装制造\",\"PID\": \"18\"}");
		buff.append(
				", {\"ID\": \"1811\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"运动机织服装制造\",\"PID\": \"181\"}");
		buff.append(
				", {\"ID\": \"1819\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他机织服装制造\",\"PID\": \"181\"}");
		buff.append(
				", {\"ID\": \"182\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"针织或钩针编织服装制造\",\"PID\": \"18\"}");
		buff.append(
				", {\"ID\": \"1821\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"运动休闲针织服装制造\",\"PID\": \"182\"}");
		buff.append(
				", {\"ID\": \"1829\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他针织或钩针编织服装制造\",\"PID\": \"182\"}");
		buff.append(
				", {\"ID\": \"1830\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"服饰制造\",\"PID\": \"18\"}");
		buff.append(
				", {\"ID\": \"19\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"皮革、毛皮、羽毛及其制品和制鞋业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"1910\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"皮革鞣制加工\",\"PID\": \"19\"}");
		buff.append(
				", {\"ID\": \"192\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"皮革制品制造\",\"PID\": \"19\"}");
		buff.append(
				", {\"ID\": \"1921\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"皮革服装制造\",\"PID\": \"192\"}");
		buff.append(
				", {\"ID\": \"1922\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"皮箱、包(袋)制造\",\"PID\": \"192\"}");
		buff.append(
				", {\"ID\": \"1923\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"皮手套及皮装饰制品制造 \",\"PID\": \"192\"}");
		buff.append(
				", {\"ID\": \"1929\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他皮革制品制造\",\"PID\": \"192\"}");
		buff.append(
				", {\"ID\": \"193\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"毛皮鞣制及制品加工\",\"PID\": \"19\"}");
		buff.append(
				", {\"ID\": \"1931\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"毛皮鞣制加工\",\"PID\": \"193\"}");
		buff.append(
				", {\"ID\": \"1932\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"毛皮服装加工\",\"PID\": \"193\"}");
		buff.append(
				", {\"ID\": \"1939\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他毛皮制品加工\",\"PID\": \"193\"}");
		buff.append(
				", {\"ID\": \"194\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"羽毛(绒)加工及制品制造\",\"PID\": \"19\"}");
		buff.append(
				", {\"ID\": \"1941\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"羽毛(绒)加工\",\"PID\": \"194\"}");
		buff.append(
				", {\"ID\": \"1942\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"羽毛(绒)制品加工\",\"PID\": \"194\"}");
		buff.append(
				", {\"ID\": \"195\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"制鞋业\",\"PID\": \"19\"}");
		buff.append(
				", {\"ID\": \"1951\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"纺织面料鞋制造\",\"PID\": \"195\"}");
		buff.append(
				", {\"ID\": \"1952\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"皮鞋制造\",\"PID\": \"195\"}");
		buff.append(
				", {\"ID\": \"1953\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"塑料鞋制造\",\"PID\": \"195\"}");
		buff.append(
				", {\"ID\": \"1954\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"橡胶鞋制造\",\"PID\": \"195\"}");
		buff.append(
				", {\"ID\": \"1959\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他制鞋业\",\"PID\": \"195\"}");
		buff.append(
				", {\"ID\": \"20\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"木材加工和木、竹、藤、棕、草制品业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"201\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"木材加工\",\"PID\": \"20\"}");
		buff.append(
				", {\"ID\": \"2011\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"锯材加工\",\"PID\": \"201\"}");
		buff.append(
				", {\"ID\": \"2012\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"木片加工\",\"PID\": \"201\"}");
		buff.append(
				", {\"ID\": \"2013\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"单板加工\",\"PID\": \"201\"}");
		buff.append(
				", {\"ID\": \"2019\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他木材加工\",\"PID\": \"201\"}");
		buff.append(
				", {\"ID\": \"202\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"人造板制造\",\"PID\": \"20\"}");
		buff.append(
				", {\"ID\": \"2021\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"胶合板制造\",\"PID\": \"202\"}");
		buff.append(
				", {\"ID\": \"2022\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"纤维板制造\",\"PID\": \"202\"}");
		buff.append(
				", {\"ID\": \"2023\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"刨花板制造\",\"PID\": \"202\"}");
		buff.append(
				", {\"ID\": \"2029\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他人造板制造\",\"PID\": \"202\"}");
		buff.append(
				", {\"ID\": \"203\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"木制品制造\",\"PID\": \"20\"}");
		buff.append(
				", {\"ID\": \"2031\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"建筑用木料及木材组件加工\",\"PID\": \"203\"}");
		buff.append(
				", {\"ID\": \"2032\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"木门窗制造\",\"PID\": \"203\"}");
		buff.append(
				", {\"ID\": \"2033\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"木楼梯制造\",\"PID\": \"203\"}");
		buff.append(
				", {\"ID\": \"2034\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"木地板制造\",\"PID\": \"203\"}");
		buff.append(
				", {\"ID\": \"2035\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"木制容器制造\",\"PID\": \"203\"}");
		buff.append(
				", {\"ID\": \"2039\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"软木制品及其他木制品制造\",\"PID\": \"203\"}");
		buff.append(
				", {\"ID\": \"204\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"竹、藤、棕、草制品制造\",\"PID\": \"20\"}");
		buff.append(
				", {\"ID\": \"2041\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"竹制品制造\",\"PID\": \"204\"}");
		buff.append(
				", {\"ID\": \"2042\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"藤制品制造\",\"PID\": \"204\"}");
		buff.append(
				", {\"ID\": \"2043\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"棕制品制造\",\"PID\": \"204\"}");
		buff.append(
				", {\"ID\": \"2049\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"草及其他制品制造\",\"PID\": \"204\"}");
		buff.append(
				", {\"ID\": \"21\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"家具制造业 \",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"2110\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"木质家具制造\",\"PID\": \"21\"}");
		buff.append(
				", {\"ID\": \"2120\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"竹、藤家具制造\",\"PID\": \"21\"}");
		buff.append(
				", {\"ID\": \"2130\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属家具制造\",\"PID\": \"21\"}");
		buff.append(
				", {\"ID\": \"2140\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"塑料家具制造\",\"PID\": \"21\"}");
		buff.append(
				", {\"ID\": \"2190\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他家具制造\",\"PID\": \"21\"}");
		buff.append(
				", {\"ID\": \"22\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"造纸和纸制品业 \",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"221\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"纸浆制造\",\"PID\": \"22\"}");
		buff.append(
				", {\"ID\": \"2211\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"木竹浆制造\",\"PID\": \"221\"}");
		buff.append(
				", {\"ID\": \"2212\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"非木竹浆制造\",\"PID\": \"221\"}");
		buff.append(
				", {\"ID\": \"222\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"造纸\",\"PID\": \"22\"}");
		buff.append(
				", {\"ID\": \"2221\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"机制纸及纸板制造\",\"PID\": \"222\"}");
		buff.append(
				", {\"ID\": \"2222\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"手工纸制造\",\"PID\": \"222\"}");
		buff.append(
				", {\"ID\": \"2223\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"加工纸制造\",\"PID\": \"222\"}");
		buff.append(
				", {\"ID\": \"223\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"纸制品制造\",\"PID\": \"22\"}");
		buff.append(
				", {\"ID\": \"2231\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"纸和纸板容器制造\",\"PID\": \"223\"}");
		buff.append(
				", {\"ID\": \"2239\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他纸制品制造\",\"PID\": \"223\"}");
		buff.append(
				", {\"ID\": \"23\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"印刷和记录媒介复制业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"231\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"印刷\",\"PID\": \"23\"}");
		buff.append(
				", {\"ID\": \"2311\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"书、报刊印刷\",\"PID\": \"231\"}");
		buff.append(
				", {\"ID\": \"2312\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"本册印制\",\"PID\": \"231\"}");
		buff.append(
				", {\"ID\": \"2319\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"包装装潢及其他印刷\",\"PID\": \"231\"}");
		buff.append(
				", {\"ID\": \"2320\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"装订及印刷相关服务\",\"PID\": \"23\"}");
		buff.append(
				", {\"ID\": \"2330\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"记录媒介复制\",\"PID\": \"23\"}");
		buff.append(
				", {\"ID\": \"24\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"文教、工美、体育和娱乐用品制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"241\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"文教办公用品制造\",\"PID\": \"24\"}");
		buff.append(
				", {\"ID\": \"2411\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"文具制造\",\"PID\": \"241\"}");
		buff.append(
				", {\"ID\": \"2412\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"笔的制造\",\"PID\": \"241\"}");
		buff.append(
				", {\"ID\": \"2413\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"教学用模型及教具制造\",\"PID\": \"241\"}");
		buff.append(
				", {\"ID\": \"2414\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"墨水、墨汁制造\",\"PID\": \"241\"}");
		buff.append(
				", {\"ID\": \"2419\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他文教办公用品制造\",\"PID\": \"241\"}");
		buff.append(
				", {\"ID\": \"242\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"乐器制造\",\"PID\": \"24\"}");
		buff.append(
				", {\"ID\": \"2421\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"中乐器制造\",\"PID\": \"242\"}");
		buff.append(
				", {\"ID\": \"2422\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"西乐器制造\",\"PID\": \"242\"}");
		buff.append(
				", {\"ID\": \"2423\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电子乐器制造\",\"PID\": \"242\"}");
		buff.append(
				", {\"ID\": \"2429\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他乐器及零件制造\",\"PID\": \"242\"}");
		buff.append(
				", {\"ID\": \"243\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"工艺美术及礼仪用品制造\",\"PID\": \"24\"}");
		buff.append(
				", {\"ID\": \"2431\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"雕塑工艺品制造\",\"PID\": \"243\"}");
		buff.append(
				", {\"ID\": \"2432\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属工艺品制造\",\"PID\": \"243\"}");
		buff.append(
				", {\"ID\": \"2433\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"漆器工艺品制造\",\"PID\": \"243\"}");
		buff.append(
				", {\"ID\": \"2434\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"花画工艺品制造\",\"PID\": \"243\"}");
		buff.append(
				", {\"ID\": \"2435\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"天然植物纤维编织工艺品制造\",\"PID\": \"243\"}");
		buff.append(
				", {\"ID\": \"2436\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"抽纱刺绣工艺品制造\",\"PID\": \"243\"}");
		buff.append(
				", {\"ID\": \"2437\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"地毯、挂毯制造\",\"PID\": \"243\"}");
		buff.append(
				", {\"ID\": \"2438\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"珠宝首饰及有关物品制造\",\"PID\": \"243\"}");
		buff.append(
				", {\"ID\": \"2439\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他工艺美术及礼仪用品制造\",\"PID\": \"243\"}");
		buff.append(
				", {\"ID\": \"244\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"体育用品制造\",\"PID\": \"24\"}");
		buff.append(
				", {\"ID\": \"2441\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"球类制造\",\"PID\": \"244\"}");
		buff.append(
				", {\"ID\": \"2442\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"专项运动器材及配件制造\",\"PID\": \"244\"}");
		buff.append(
				", {\"ID\": \"2443\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"健身器材制造\",\"PID\": \"244\"}");
		buff.append(
				", {\"ID\": \"2444\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"运动防护用具制造\",\"PID\": \"244\"}");
		buff.append(
				", {\"ID\": \"2449\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他体育用品制造\",\"PID\": \"244\"}");
		buff.append(
				", {\"ID\": \"245\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"玩具制造\",\"PID\": \"24\"}");
		buff.append(
				", {\"ID\": \"2451\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电玩具制造\",\"PID\": \"245\"}");
		buff.append(
				", {\"ID\": \"2452\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"塑胶玩具制造\",\"PID\": \"245\"}");
		buff.append(
				", {\"ID\": \"2453\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属玩具制造\",\"PID\": \"245\"}");
		buff.append(
				", {\"ID\": \"2454\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"弹射玩具制造\",\"PID\": \"245\"}");
		buff.append(
				", {\"ID\": \"2455\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"娃娃玩具制造\",\"PID\": \"245\"}");
		buff.append(
				", {\"ID\": \"2456\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"儿童乘骑玩耍的童车类产品制造\",\"PID\": \"245\"}");
		buff.append(
				", {\"ID\": \"2459\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他玩具制造\",\"PID\": \"245\"}");
		buff.append(
				", {\"ID\": \"246\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"游艺器材及娱乐用品制造\",\"PID\": \"24\"}");
		buff.append(
				", {\"ID\": \"2461\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"露天游乐场所游乐设备制造\",\"PID\": \"246\"}");
		buff.append(
				", {\"ID\": \"2462\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"游艺用品及室内游艺器材制造\",\"PID\": \"246\"}");
		buff.append(
				", {\"ID\": \"2469\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他娱乐用品制造\",\"PID\": \"246\"}");
		buff.append(
				", {\"ID\": \"25\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"石油、煤炭及其他燃料加工业 \",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"251\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"精炼石油产品制造\",\"PID\": \"25\"}");
		buff.append(
				", {\"ID\": \"2511\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"原油加工及石油制品制造\",\"PID\": \"251\"}");
		buff.append(
				", {\"ID\": \"2519\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他原油制造\",\"PID\": \"251\"}");
		buff.append(
				", {\"ID\": \"252\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"煤炭加工\",\"PID\": \"25\"}");
		buff.append(
				", {\"ID\": \"2521\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"炼焦\",\"PID\": \"252\"}");
		buff.append(
				", {\"ID\": \"2522\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"煤制合成气生产\",\"PID\": \"252\"}");
		buff.append(
				", {\"ID\": \"2523\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"煤制液体燃料生产\",\"PID\": \"252\"}");
		buff.append(
				", {\"ID\": \"2524\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"煤制品制造\",\"PID\": \"252\"}");
		buff.append(
				", {\"ID\": \"2529\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他煤炭加工\",\"PID\": \"252\"}");
		buff.append(
				", {\"ID\": \"2530\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"核燃料加工\",\"PID\": \"25\"}");
		buff.append(
				", {\"ID\": \"254\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生物质燃料加工\",\"PID\": \"25\"}");
		buff.append(
				", {\"ID\": \"2541\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生物质液体燃料生产\",\"PID\": \"254\"}");
		buff.append(
				", {\"ID\": \"2542\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生物质致密成型燃料加工\",\"PID\": \"254\"}");
		buff.append(
				", {\"ID\": \"26\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化学原料和化学制品制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"261\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"基础化学原料制造\",\"PID\": \"26\"}");
		buff.append(
				", {\"ID\": \"2611\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"无机酸制造\",\"PID\": \"261\"}");
		buff.append(
				", {\"ID\": \"2612\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"无机碱制造\",\"PID\": \"261\"}");
		buff.append(
				", {\"ID\": \"2613\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"无机盐制造\",\"PID\": \"261\"}");
		buff.append(
				", {\"ID\": \"2614\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"有机化学原料制造\",\"PID\": \"261\"}");
		buff.append(
				", {\"ID\": \"2619\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他基础化学原料制造\",\"PID\": \"261\"}");
		buff.append(
				", {\"ID\": \"262\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"肥料制造\",\"PID\": \"26\"}");
		buff.append(
				", {\"ID\": \"2621\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"氮肥制造\",\"PID\": \"262\"}");
		buff.append(
				", {\"ID\": \"2622\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"磷肥制造\",\"PID\": \"262\"}");
		buff.append(
				", {\"ID\": \"2623\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"钾肥制造\",\"PID\": \"262\"}");
		buff.append(
				", {\"ID\": \"2624\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"复混肥料制造\",\"PID\": \"262\"}");
		buff.append(
				", {\"ID\": \"2625\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"有机肥料及微生物肥料制造\",\"PID\": \"262\"}");
		buff.append(
				", {\"ID\": \"2629\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他肥料制造\",\"PID\": \"262\"}");
		buff.append(
				", {\"ID\": \"263\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"农药制造\",\"PID\": \"26\"}");
		buff.append(
				", {\"ID\": \"2631\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化学农药制造\",\"PID\": \"263\"}");
		buff.append(
				", {\"ID\": \"2632\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生物化学农药及微生物农药制造\",\"PID\": \"263\"}");
		buff.append(
				", {\"ID\": \"264\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"涂料、油墨、颜料及类似产品制造\",\"PID\": \"26\"}");
		buff.append(
				", {\"ID\": \"2641\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"涂料制造\",\"PID\": \"264\"}");
		buff.append(
				", {\"ID\": \"2642\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"油墨及类似产品制造\",\"PID\": \"264\"}");
		buff.append(
				", {\"ID\": \"2643\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"工业颜料制造\",\"PID\": \"264\"}");
		buff.append(
				", {\"ID\": \"2644\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"工艺美术颜料制造\",\"PID\": \"264\"}");
		buff.append(
				", {\"ID\": \"2645\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"染料制造\",\"PID\": \"264\"}");
		buff.append(
				", {\"ID\": \"2646\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"密封用填料及类似品制造\",\"PID\": \"264\"}");
		buff.append(
				", {\"ID\": \"265\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"合成材料制造\",\"PID\": \"26\"}");
		buff.append(
				", {\"ID\": \"2651\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"初级形态塑料及合成树脂制造\",\"PID\": \"265\"}");
		buff.append(
				", {\"ID\": \"2652\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"合成橡胶制造\",\"PID\": \"265\"}");
		buff.append(
				", {\"ID\": \"2653\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"合成纤维单(聚合)体制造\",\"PID\": \"265\"}");
		buff.append(
				", {\"ID\": \"2659\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他合成材料制造\",\"PID\": \"265\"}");
		buff.append(
				", {\"ID\": \"266\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"专用化学产品制造\",\"PID\": \"26\"}");
		buff.append(
				", {\"ID\": \"2661\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化学试剂和助剂制造\",\"PID\": \"266\"}");
		buff.append(
				", {\"ID\": \"2662\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"专项化学用品制造\",\"PID\": \"266\"}");
		buff.append(
				", {\"ID\": \"2663\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"林产化学产品制造\",\"PID\": \"266\"}");
		buff.append(
				", {\"ID\": \"2664\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"文化用信息化学品制造\",\"PID\": \"266\"}");
		buff.append(
				", {\"ID\": \"2665\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"医学生产用信息化学品制造\",\"PID\": \"266\"}");
		buff.append(
				", {\"ID\": \"2666\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"环境污染处理专用药剂材料制造\",\"PID\": \"266\"}");
		buff.append(
				", {\"ID\": \"2667\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"动物胶制造\",\"PID\": \"266\"}");
		buff.append(
				", {\"ID\": \"2669\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他专用化学产品制造\",\"PID\": \"266\"}");
		buff.append(
				", {\"ID\": \"267\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"炸药、火工及焰火产品制造\",\"PID\": \"26\"}");
		buff.append(
				", {\"ID\": \"2671\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"炸药及火工产品制造\",\"PID\": \"267\"}");
		buff.append(
				", {\"ID\": \"2672\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"焰火、鞭炮产品制造 \",\"PID\": \"267\"}");
		buff.append(
				", {\"ID\": \"268\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"日用化学产品制造\",\"PID\": \"26\"}");
		buff.append(
				", {\"ID\": \"2681\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"肥皂及洗涤剂制造\",\"PID\": \"268\"}");
		buff.append(
				", {\"ID\": \"2682\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化妆品制造\",\"PID\": \"268\"}");
		buff.append(
				", {\"ID\": \"2683\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"口腔清洁用品制造\",\"PID\": \"268\"}");
		buff.append(
				", {\"ID\": \"2684\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"香料、香精制造\",\"PID\": \"268\"}");
		buff.append(
				", {\"ID\": \"2689\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他日用化学产品制造\",\"PID\": \"268\"}");
		buff.append(
				", {\"ID\": \"27\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"医药制造业 \",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"2710\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化学药品原料药制造\",\"PID\": \"27\"}");
		buff.append(
				", {\"ID\": \"2720\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化学药品制剂制造\",\"PID\": \"27\"}");
		buff.append(
				", {\"ID\": \"2730\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"中药饮片加工\",\"PID\": \"27\"}");
		buff.append(
				", {\"ID\": \"2740\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"中成药生产\",\"PID\": \"27\"}");
		buff.append(
				", {\"ID\": \"2750\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"兽用药品制造\",\"PID\": \"27\"}");
		buff.append(
				", {\"ID\": \"276\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生物药品制品制造\",\"PID\": \"27\"}");
		buff.append(
				", {\"ID\": \"2761\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生物药品制造\",\"PID\": \"276\"}");
		buff.append(
				", {\"ID\": \"2762\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"基因工程药物和疫苗制造\",\"PID\": \"276\"}");
		buff.append(
				", {\"ID\": \"2770\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"卫生材料及医药用品制造\",\"PID\": \"27\"}");
		buff.append(
				", {\"ID\": \"2780\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"药用辅料及包装材料\",\"PID\": \"27\"}");
		buff.append(
				", {\"ID\": \"28\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化学纤维制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"281\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"纤维素纤维原料及纤维制造\",\"PID\": \"28\"}");
		buff.append(
				", {\"ID\": \"2811\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化纤浆粕制造\",\"PID\": \"281\"}");
		buff.append(
				", {\"ID\": \"2812\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"人造纤维（纤维素纤维）制造\",\"PID\": \"281\"}");
		buff.append(
				", {\"ID\": \"282\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"合成纤维制造\",\"PID\": \"28\"}");
		buff.append(
				", {\"ID\": \"2821\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"锦纶纤维制造\",\"PID\": \"282\"}");
		buff.append(
				", {\"ID\": \"2822\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"涤纶纤维制造\",\"PID\": \"282\"}");
		buff.append(
				", {\"ID\": \"2823\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"腈纶纤维制造\",\"PID\": \"282\"}");
		buff.append(
				", {\"ID\": \"2824\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"维纶纤维制造\",\"PID\": \"282\"}");
		buff.append(
				", {\"ID\": \"2825\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"丙纶纤维制造\",\"PID\": \"282\"}");
		buff.append(
				", {\"ID\": \"2826\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"氨纶纤维制造\",\"PID\": \"282\"}");
		buff.append(
				", {\"ID\": \"2829\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他合成纤维制造\",\"PID\": \"282\"}");
		buff.append(
				", {\"ID\": \"283\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生物基材料制造\",\"PID\": \"28\"}");
		buff.append(
				", {\"ID\": \"2831\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生物基化学纤维制造\",\"PID\": \"283\"}");
		buff.append(
				", {\"ID\": \"2832\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生物基、淀粉基新材料制造\",\"PID\": \"283\"}");
		buff.append(
				", {\"ID\": \"29\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"橡胶和塑料制品业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"291\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"橡胶制品业\",\"PID\": \"29\"}");
		buff.append(
				", {\"ID\": \"2911\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"轮胎制造\",\"PID\": \"291\"}");
		buff.append(
				", {\"ID\": \"2912\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"橡胶板、管、带制造\",\"PID\": \"291\"}");
		buff.append(
				", {\"ID\": \"2913\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"橡胶零件制造\",\"PID\": \"291\"}");
		buff.append(
				", {\"ID\": \"2914\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"再生橡胶制造\",\"PID\": \"291\"}");
		buff.append(
				", {\"ID\": \"2915\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"日用及医用橡胶制品制造\",\"PID\": \"291\"}");
		buff.append(
				", {\"ID\": \"2916\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"运动场地用塑胶制造\",\"PID\": \"291\"}");
		buff.append(
				", {\"ID\": \"2919\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他橡胶制品制造\",\"PID\": \"291\"}");
		buff.append(
				", {\"ID\": \"292\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"塑料制品业\",\"PID\": \"29\"}");
		buff.append(
				", {\"ID\": \"2921\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"塑料薄膜制造\",\"PID\": \"292\"}");
		buff.append(
				", {\"ID\": \"2922\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"塑料板、管、型材制造\",\"PID\": \"292\"}");
		buff.append(
				", {\"ID\": \"2923\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"塑料丝、绳及编织品制造\",\"PID\": \"292\"}");
		buff.append(
				", {\"ID\": \"2924\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"泡沫塑料制造\",\"PID\": \"292\"}");
		buff.append(
				", {\"ID\": \"2925\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"塑料人造革、合成革制造\",\"PID\": \"292\"}");
		buff.append(
				", {\"ID\": \"2926\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"塑料包装箱及容器制造\",\"PID\": \"292\"}");
		buff.append(
				", {\"ID\": \"2927\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"日用塑料制品制造\",\"PID\": \"292\"}");
		buff.append(
				", {\"ID\": \"2928\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"人造草坪制造\",\"PID\": \"292\"}");
		buff.append(
				", {\"ID\": \"2929\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"塑料零件及其他塑料制品制造\",\"PID\": \"292\"}");
		buff.append(
				", {\"ID\": \"30\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"非金属矿物制品业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"301\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"水泥、石灰和石膏制造\",\"PID\": \"30\"}");
		buff.append(
				", {\"ID\": \"3011\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"水泥制造\",\"PID\": \"301\"}");
		buff.append(
				", {\"ID\": \"3012\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"石灰和石膏制造\",\"PID\": \"301\"}");
		buff.append(
				", {\"ID\": \"302\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"石膏、水泥制品及类似制品制造\",\"PID\": \"30\"}");
		buff.append(
				", {\"ID\": \"3021\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"水泥制品制造\",\"PID\": \"302\"}");
		buff.append(
				", {\"ID\": \"3022\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"砼结构构件制造\",\"PID\": \"302\"}");
		buff.append(
				", {\"ID\": \"3023\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"石棉水泥制品制造\",\"PID\": \"302\"}");
		buff.append(
				", {\"ID\": \"3024\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"轻质建筑材料制造\",\"PID\": \"302\"}");
		buff.append(
				", {\"ID\": \"3029\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他水泥类似制品制造\",\"PID\": \"302\"}");
		buff.append(
				", {\"ID\": \"303\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"砖瓦、石材等建筑材料制造\",\"PID\": \"30\"}");
		buff.append(
				", {\"ID\": \"3031\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"粘土砖瓦及建筑砌块制造\",\"PID\": \"303\"}");
		buff.append(
				", {\"ID\": \"3032\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"建筑用石加工\",\"PID\": \"303\"}");
		buff.append(
				", {\"ID\": \"3033\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"防水建筑材料制造\",\"PID\": \"303\"}");
		buff.append(
				", {\"ID\": \"3034\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"隔热和隔音材料制造\",\"PID\": \"303\"}");
		buff.append(
				", {\"ID\": \"3039\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他建筑材料制造\",\"PID\": \"303\"}");
		buff.append(
				", {\"ID\": \"304\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"玻璃制造\",\"PID\": \"30\"}");
		buff.append(
				", {\"ID\": \"3041\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"平板玻璃制造\",\"PID\": \"304\"}");
		buff.append(
				", {\"ID\": \"3042\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"特种玻璃制造\",\"PID\": \"304\"}");
		buff.append(
				", {\"ID\": \"3049\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他玻璃制造\",\"PID\": \"304\"}");
		buff.append(
				", {\"ID\": \"305\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"玻璃制品制造\",\"PID\": \"30\"}");
		buff.append(
				", {\"ID\": \"3051\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"技术玻璃制品制造\",\"PID\": \"305\"}");
		buff.append(
				", {\"ID\": \"3052\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"光学玻璃制造\",\"PID\": \"305\"}");
		buff.append(
				", {\"ID\": \"3053\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"玻璃仪器制造\",\"PID\": \"305\"}");
		buff.append(
				", {\"ID\": \"3054\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"日用玻璃制品制造\",\"PID\": \"305\"}");
		buff.append(
				", {\"ID\": \"3055\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"玻璃包装容器制造\",\"PID\": \"305\"}");
		buff.append(
				", {\"ID\": \"3056\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"玻璃保温容器制造\",\"PID\": \"305\"}");
		buff.append(
				", {\"ID\": \"3057\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"制镜及类似品加工\",\"PID\": \"305\"}");
		buff.append(
				", {\"ID\": \"3059\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他玻璃制品制造\",\"PID\": \"305\"}");
		buff.append(
				", {\"ID\": \"306\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"玻璃纤维和玻璃纤维增强塑料制品制造\",\"PID\": \"30\"}");
		buff.append(
				", {\"ID\": \"3061\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"玻璃纤维及制品制造\",\"PID\": \"306\"}");
		buff.append(
				", {\"ID\": \"3062\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"玻璃纤维增强塑料制品制造\",\"PID\": \"306\"}");
		buff.append(
				", {\"ID\": \"307\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"陶瓷制品制造\",\"PID\": \"30\"}");
		buff.append(
				", {\"ID\": \"3071\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"建筑陶瓷制品制造\",\"PID\": \"307\"}");
		buff.append(
				", {\"ID\": \"3072\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"卫生陶瓷制品制造\",\"PID\": \"307\"}");
		buff.append(
				", {\"ID\": \"3073\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"特种陶瓷制品制造\",\"PID\": \"307\"}");
		buff.append(
				", {\"ID\": \"3074\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"日用陶瓷制品制造\",\"PID\": \"307\"}");
		buff.append(
				", {\"ID\": \"3075\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"陈设艺术陶瓷制造\",\"PID\": \"307\"}");
		buff.append(
				", {\"ID\": \"3076\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"园艺陶瓷制造\",\"PID\": \"307\"}");
		buff.append(
				", {\"ID\": \"3079\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他陶瓷制品制造\",\"PID\": \"307\"}");
		buff.append(
				", {\"ID\": \"308\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"耐火材料制品制造\",\"PID\": \"30\"}");
		buff.append(
				", {\"ID\": \"3081\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"石棉制品制造\",\"PID\": \"308\"}");
		buff.append(
				", {\"ID\": \"3082\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"云母制品制造\",\"PID\": \"308\"}");
		buff.append(
				", {\"ID\": \"3089\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"耐火陶瓷制品及其他耐火材料制造\",\"PID\": \"308\"}");
		buff.append(
				", {\"ID\": \"309\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"石墨及其他非金属矿物制品制造\",\"PID\": \"30\"}");
		buff.append(
				", {\"ID\": \"3091\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"石墨及碳素制品制造\",\"PID\": \"309\"}");
		buff.append(
				", {\"ID\": \"3099\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他非金属矿物制品制造\",\"PID\": \"309\"}");
		buff.append(
				", {\"ID\": \"31\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"黑色金属冶炼和压延加工业 \",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"3110\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"炼铁\",\"PID\": \"31\"}");
		buff.append(
				", {\"ID\": \"3120\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"炼钢\",\"PID\": \"31\"}");
		buff.append(
				", {\"ID\": \"3130\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"钢压延加工\",\"PID\": \"31\"}");
		buff.append(
				", {\"ID\": \"3140\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铁合金冶炼\",\"PID\": \"31\"}");
		buff.append(
				", {\"ID\": \"32\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"有色金属冶炼和压延加工业 \",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"321\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"常用有色金属冶炼\",\"PID\": \"32\"}");
		buff.append(
				", {\"ID\": \"3211\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铜冶炼\",\"PID\": \"321\"}");
		buff.append(
				", {\"ID\": \"3212\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铅锌冶炼\",\"PID\": \"321\"}");
		buff.append(
				", {\"ID\": \"3213\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"镍钴冶炼\",\"PID\": \"321\"}");
		buff.append(
				", {\"ID\": \"3214\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"锡冶炼\",\"PID\": \"321\"}");
		buff.append(
				", {\"ID\": \"3215\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"锑冶炼\",\"PID\": \"321\"}");
		buff.append(
				", {\"ID\": \"3216\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铝冶炼\",\"PID\": \"321\"}");
		buff.append(
				", {\"ID\": \"3217\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"镁冶炼\",\"PID\": \"321\"}");
		buff.append(
				", {\"ID\": \"3218\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"硅冶炼\",\"PID\": \"321\"}");
		buff.append(
				", {\"ID\": \"3219\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他常用有色金属冶炼\",\"PID\": \"321\"}");
		buff.append(
				", {\"ID\": \"322\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"贵金属冶炼\",\"PID\": \"32\"}");
		buff.append(
				", {\"ID\": \"3221\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金冶炼\",\"PID\": \"322\"}");
		buff.append(
				", {\"ID\": \"3222\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"银冶炼\",\"PID\": \"322\"}");
		buff.append(
				", {\"ID\": \"3229\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他贵金属冶炼\",\"PID\": \"322\"}");
		buff.append(
				", {\"ID\": \"323\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"稀有稀土金属冶炼\",\"PID\": \"32\"}");
		buff.append(
				", {\"ID\": \"3231\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"钨钼冶炼\",\"PID\": \"323\"}");
		buff.append(
				", {\"ID\": \"3232\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"稀土金属冶炼\",\"PID\": \"323\"}");
		buff.append(
				", {\"ID\": \"3239\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他稀有金属冶炼\",\"PID\": \"323\"}");
		buff.append(
				", {\"ID\": \"3240\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"有色金属合金制造\",\"PID\": \"32\"}");
		buff.append(
				", {\"ID\": \"325\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"有色金属压延加工\",\"PID\": \"32\"}");
		buff.append(
				", {\"ID\": \"3251\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铜压延加工\",\"PID\": \"325\"}");
		buff.append(
				", {\"ID\": \"3252\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铝压延加工\",\"PID\": \"325\"}");
		buff.append(
				", {\"ID\": \"3253\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"贵金属压延加工\",\"PID\": \"325\"}");
		buff.append(
				", {\"ID\": \"3254\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"稀有稀土金属压延加工\",\"PID\": \"325\"}");
		buff.append(
				", {\"ID\": \"3259\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他有色金属压延加工\",\"PID\": \"325\"}");
		buff.append(
				", {\"ID\": \"33\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属制品业 \",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"331\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"结构性金属制品制造\",\"PID\": \"33\"}");
		buff.append(
				", {\"ID\": \"3311\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属结构制造\",\"PID\": \"331\"}");
		buff.append(
				", {\"ID\": \"3312\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属门窗制造\",\"PID\": \"331\"}");
		buff.append(
				", {\"ID\": \"332\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属工具制造\",\"PID\": \"33\"}");
		buff.append(
				", {\"ID\": \"3321\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"切削工具制造\",\"PID\": \"332\"}");
		buff.append(
				", {\"ID\": \"3322\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"手工具制造\",\"PID\": \"332\"}");
		buff.append(
				", {\"ID\": \"3323\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"农用及园林用金属工具制造\",\"PID\": \"332\"}");
		buff.append(
				", {\"ID\": \"3324\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"刀剪及类似日用金属工具制造\",\"PID\": \"332\"}");
		buff.append(
				", {\"ID\": \"3329\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他金属工具制造\",\"PID\": \"332\"}");
		buff.append(
				", {\"ID\": \"333\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"集装箱及金属包装容器制造\",\"PID\": \"33\"}");
		buff.append(
				", {\"ID\": \"3331\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"集装箱制造\",\"PID\": \"333\"}");
		buff.append(
				", {\"ID\": \"3332\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属压力容器制造\",\"PID\": \"333\"}");
		buff.append(
				", {\"ID\": \"3333\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属包装容器及材料制造\",\"PID\": \"333\"}");
		buff.append(
				", {\"ID\": \"3340\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属丝绳及其制品制造\",\"PID\": \"33\"}");
		buff.append(
				", {\"ID\": \"335\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"建筑、安全用金属制品制造\",\"PID\": \"33\"}");
		buff.append(
				", {\"ID\": \"3351\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"建筑、家具用金属配件制造\",\"PID\": \"335\"}");
		buff.append(
				", {\"ID\": \"3352\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"建筑装饰及水暖管道零件制造\",\"PID\": \"335\"}");
		buff.append(
				", {\"ID\": \"3353\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"安全、消防用金属制品制造\",\"PID\": \"335\"}");
		buff.append(
				", {\"ID\": \"3359\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他建筑、安全用金属制品制造\",\"PID\": \"335\"}");
		buff.append(
				", {\"ID\": \"3360\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属表面处理及热处理加工\",\"PID\": \"33\"}");
		buff.append(
				", {\"ID\": \"337\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"搪瓷制品制造\",\"PID\": \"33\"}");
		buff.append(
				", {\"ID\": \"3371\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生产专用搪瓷制品制造\",\"PID\": \"337\"}");
		buff.append(
				", {\"ID\": \"3372\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"建筑装饰搪瓷制品制造\",\"PID\": \"337\"}");
		buff.append(
				", {\"ID\": \"3373\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"搪瓷卫生洁具制造\",\"PID\": \"337\"}");
		buff.append(
				", {\"ID\": \"3379\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"搪瓷日用品及其他搪瓷制品制造\",\"PID\": \"337\"}");
		buff.append(
				", {\"ID\": \"338\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属制日用品制造\",\"PID\": \"33\"}");
		buff.append(
				", {\"ID\": \"3381\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属制厨房用器具制造\",\"PID\": \"338\"}");
		buff.append(
				", {\"ID\": \"3382\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属制餐具和器皿制造\",\"PID\": \"338\"}");
		buff.append(
				", {\"ID\": \"3383\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属制卫生器具制造\",\"PID\": \"338\"}");
		buff.append(
				", {\"ID\": \"3389\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他金属制日用品制造\",\"PID\": \"338\"}");
		buff.append(
				", {\"ID\": \"339\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他金属制品制造\",\"PID\": \"33\"}");
		buff.append(
				", {\"ID\": \"3391\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"黑色金属铸造\",\"PID\": \"339\"}");
		buff.append(
				", {\"ID\": \"3392\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"有色金属铸造\",\"PID\": \"339\"}");
		buff.append(
				", {\"ID\": \"3393\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"锻件及粉末冶金制品制造\",\"PID\": \"339\"}");
		buff.append(
				", {\"ID\": \"3394\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"交通及公共管理用金属标牌制造\",\"PID\": \"339\"}");
		buff.append(
				", {\"ID\": \"3399\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他未列明金属制品制造\",\"PID\": \"339\"}");
		buff.append(
				", {\"ID\": \"34\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"通用设备制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"341\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"锅炉及原动设备制造\",\"PID\": \"34\"}");
		buff.append(
				", {\"ID\": \"3411\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"锅炉及辅助设备制造\",\"PID\": \"341\"}");
		buff.append(
				", {\"ID\": \"3412\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"内燃机及配件制造\",\"PID\": \"341\"}");
		buff.append(
				", {\"ID\": \"3413\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"汽轮机及辅机制造\",\"PID\": \"341\"}");
		buff.append(
				", {\"ID\": \"3414\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"水轮机及辅机制造\",\"PID\": \"341\"}");
		buff.append(
				", {\"ID\": \"3415\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"风能原动设备制造\",\"PID\": \"341\"}");
		buff.append(
				", {\"ID\": \"3419\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他原动设备制造\",\"PID\": \"341\"}");
		buff.append(
				", {\"ID\": \"342\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属加工机械制造\",\"PID\": \"34\"}");
		buff.append(
				", {\"ID\": \"3421\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属切削机床制造\",\"PID\": \"342\"}");
		buff.append(
				", {\"ID\": \"3422\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属成形机床制造\",\"PID\": \"342\"}");
		buff.append(
				", {\"ID\": \"3423\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铸造机械制造\",\"PID\": \"342\"}");
		buff.append(
				", {\"ID\": \"3424\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属切割及焊接设备制造\",\"PID\": \"342\"}");
		buff.append(
				", {\"ID\": \"3425\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"机床功能部件及附件制造\",\"PID\": \"342\"}");
		buff.append(
				", {\"ID\": \"3429\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他金属加工机械制造\",\"PID\": \"342\"}");
		buff.append(
				", {\"ID\": \"343\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"物料搬运设备制造\",\"PID\": \"34\"}");
		buff.append(
				", {\"ID\": \"3431\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"轻小型起重设备制造\",\"PID\": \"343\"}");
		buff.append(
				", {\"ID\": \"3432\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生产专用起重机制造\",\"PID\": \"343\"}");
		buff.append(
				", {\"ID\": \"3433\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"生产专用车辆制造\",\"PID\": \"343\"}");
		buff.append(
				", {\"ID\": \"3434\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"连续搬运设备制造\",\"PID\": \"343\"}");
		buff.append(
				", {\"ID\": \"3435\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电梯、自动扶梯及升降机制造\",\"PID\": \"343\"}");
		buff.append(
				", {\"ID\": \"3436\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"客运索道制造\",\"PID\": \"343\"}");
		buff.append(
				", {\"ID\": \"3437\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"机械式停车设备制造\",\"PID\": \"343\"}");
		buff.append(
				", {\"ID\": \"3439\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他物料搬运设备制造\",\"PID\": \"343\"}");
		buff.append(
				", {\"ID\": \"344\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"泵、阀门、压缩机及类似机械制造\",\"PID\": \"34\"}");
		buff.append(
				", {\"ID\": \"3441\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"泵及真空设备制造\",\"PID\": \"344\"}");
		buff.append(
				", {\"ID\": \"3442\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"气体压缩机械制造\",\"PID\": \"344\"}");
		buff.append(
				", {\"ID\": \"3443\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"阀门和旋塞制造\",\"PID\": \"344\"}");
		buff.append(
				", {\"ID\": \"3444\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"液压动力机械及元件制造\",\"PID\": \"344\"}");
		buff.append(
				", {\"ID\": \"3445\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"液力动力机械元件制造\",\"PID\": \"344\"}");
		buff.append(
				", {\"ID\": \"3446\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \" 气压动力机械及元件制造\",\"PID\": \"344\"}");
		buff.append(
				", {\"ID\": \"345\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"轴承、齿轮和传动部件制造\",\"PID\": \"34\"}");
		buff.append(
				", {\"ID\": \"3451\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"滚动轴承制造\",\"PID\": \"345\"}");
		buff.append(
				", {\"ID\": \"3452\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"滑动轴承制造\",\"PID\": \"345\"}");
		buff.append(
				", {\"ID\": \"3453\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"齿轮及齿轮减、变速箱制造\",\"PID\": \"345\"}");
		buff.append(
				", {\"ID\": \"3459\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他传动部件制造\",\"PID\": \"345\"}");
		buff.append(
				", {\"ID\": \"346\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"烘炉、风机、包装等设备制造\",\"PID\": \"34\"}");
		buff.append(
				", {\"ID\": \"3461\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"烘炉、熔炉及电炉制造\",\"PID\": \"346\"}");
		buff.append(
				", {\"ID\": \"3462\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"风机、风扇制造\",\"PID\": \"346\"}");
		buff.append(
				", {\"ID\": \"3463\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"气体、液体分离及纯净设备制造\",\"PID\": \"346\"}");
		buff.append(
				", {\"ID\": \"3464\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"制冷、空调设备制造\",\"PID\": \"346\"}");
		buff.append(
				", {\"ID\": \"3465\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"风动和电动工具制造\",\"PID\": \"346\"}");
		buff.append(
				", {\"ID\": \"3466\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"喷枪及类似器具制造 \",\"PID\": \"346\"}");
		buff.append(
				", {\"ID\": \"3467\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"包装专用设备制造\",\"PID\": \"346\"}");
		buff.append(
				", {\"ID\": \"347\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"文化、办公用机械制造\",\"PID\": \"34\"}");
		buff.append(
				", {\"ID\": \"3471\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电影机械制造\",\"PID\": \"347\"}");
		buff.append(
				", {\"ID\": \"3472\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"幻灯及投影设备制造\",\"PID\": \"347\"}");
		buff.append(
				", {\"ID\": \"3473\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"照相机及器材制造\",\"PID\": \"347\"}");
		buff.append(
				", {\"ID\": \"3474\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"复印和胶印设备制造\",\"PID\": \"347\"}");
		buff.append(
				", {\"ID\": \"3475\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"计算器及货币专用设备制造\",\"PID\": \"347\"}");
		buff.append(
				", {\"ID\": \"3479\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他文化、办公用机械制造\",\"PID\": \"347\"}");
		buff.append(
				", {\"ID\": \"348\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"通用零部件制造\",\"PID\": \"34\"}");
		buff.append(
				", {\"ID\": \"3481\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属密封件制造\",\"PID\": \"348\"}");
		buff.append(
				", {\"ID\": \"3482\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"紧固件制造\",\"PID\": \"348\"}");
		buff.append(
				", {\"ID\": \"3483\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"弹簧制造\",\"PID\": \"348\"}");
		buff.append(
				", {\"ID\": \"3484\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"机械零部件加工\",\"PID\": \"348\"}");
		buff.append(
				", {\"ID\": \"3489\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他通用零部件制造\",\"PID\": \"348\"}");
		buff.append(
				", {\"ID\": \"349\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他通用设备制造\",\"PID\": \"34\"}");
		buff.append(
				", {\"ID\": \"3491\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"工业机器人制造\",\"PID\": \"349\"}");
		buff.append(
				", {\"ID\": \"3492\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"特殊作业机器人制造\",\"PID\": \"349\"}");
		buff.append(
				", {\"ID\": \"3493\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"增材制造装备制造\",\"PID\": \"349\"}");
		buff.append(
				", {\"ID\": \"3499\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他未列明通用设备制造业\",\"PID\": \"349\"}");
		buff.append(
				", {\"ID\": \"35\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"专用设备制造业 \",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"351\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"采矿、冶金、建筑专用设备制造\",\"PID\": \"35\"}");
		buff.append(
				", {\"ID\": \"3511\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"矿山机械制造\",\"PID\": \"351\"}");
		buff.append(
				", {\"ID\": \"3512\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"石油钻采专用设备制造\",\"PID\": \"351\"}");
		buff.append(
				", {\"ID\": \"3513\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"深海石油钻探设备制造\",\"PID\": \"351\"}");
		buff.append(
				", {\"ID\": \"3514\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"建筑工程用机械制造\",\"PID\": \"351\"}");
		buff.append(
				", {\"ID\": \"3515\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"建筑材料生产专用机械制造\",\"PID\": \"351\"}");
		buff.append(
				", {\"ID\": \"3516\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"冶金专用设备制造\",\"PID\": \"351\"}");
		buff.append(
				", {\"ID\": \"3517\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"隧道施工专用机械制造\",\"PID\": \"351\"}");
		buff.append(
				", {\"ID\": \"352\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"化工、木材、非金属加工专用设备制造\",\"PID\": \"35\"}");
		buff.append(
				", {\"ID\": \"3521\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"炼油、化工生产专用设备制造\",\"PID\": \"352\"}");
		buff.append(
				", {\"ID\": \"3522\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"橡胶加工专用设备制造\",\"PID\": \"352\"}");
		buff.append(
				", {\"ID\": \"3523\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"塑料加工专用设备制造\",\"PID\": \"352\"}");
		buff.append(
				", {\"ID\": \"3524\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"木竹材加工机械制造\",\"PID\": \"352\"}");
		buff.append(
				", {\"ID\": \"3525\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"模具制造\",\"PID\": \"352\"}");
		buff.append(
				", {\"ID\": \"3529\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他非金属加工专用设备制造\",\"PID\": \"352\"}");
		buff.append(
				", {\"ID\": \"353\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"食品、饮料、烟草及饲料生产专用设备制造　　　\",\"PID\": \"35\"}");
		buff.append(
				", {\"ID\": \"3531\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"食品、酒、饮料及茶生产专用设备制造\",\"PID\": \"353\"}");
		buff.append(
				", {\"ID\": \"3532\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"农副食品加工专用设备制造\",\"PID\": \"353\"}");
		buff.append(
				", {\"ID\": \"3533\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"烟草生产专用设备制造\",\"PID\": \"353\"}");
		buff.append(
				", {\"ID\": \"3534\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"饲料生产专用设备制造\",\"PID\": \"353\"}");
		buff.append(
				", {\"ID\": \"354\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"印刷、制药、日化及日用品生产专用设备制造\",\"PID\": \"35\"}");
		buff.append(
				", {\"ID\": \"3541\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"制浆和造纸专用设备制造\",\"PID\": \"354\"}");
		buff.append(
				", {\"ID\": \"3542\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"印刷专用设备制造\",\"PID\": \"354\"}");
		buff.append(
				", {\"ID\": \"3543\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"日用化工专用设备制造\",\"PID\": \"354\"}");
		buff.append(
				", {\"ID\": \"3544\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"制药专用设备制造\",\"PID\": \"354\"}");
		buff.append(
				", {\"ID\": \"3545\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"照明器具生产专用设备制造\",\"PID\": \"354\"}");
		buff.append(
				", {\"ID\": \"3546\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"玻璃、陶瓷和搪瓷制品生产专用设备制造\",\"PID\": \"354\"}");
		buff.append(
				", {\"ID\": \"3549\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他日用品生产专用设备制造\",\"PID\": \"354\"}");
		buff.append(
				", {\"ID\": \"355\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"纺织、服装和皮革加工专用设备制造\",\"PID\": \"35\"}");
		buff.append(
				", {\"ID\": \"3551\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"纺织专用设备制造\",\"PID\": \"355\"}");
		buff.append(
				", {\"ID\": \"3552\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"皮革、毛皮及其制品加工专用设备制造\",\"PID\": \"355\"}");
		buff.append(
				", {\"ID\": \"3553\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"缝制机械制造\",\"PID\": \"355\"}");
		buff.append(
				", {\"ID\": \"3554\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"洗涤机械制造\",\"PID\": \"355\"}");
		buff.append(
				", {\"ID\": \"356\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电子和电工机械专用设备制造\",\"PID\": \"35\"}");
		buff.append(
				", {\"ID\": \"3561\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电工机械专用设备制造\",\"PID\": \"356\"}");
		buff.append(
				", {\"ID\": \"3562\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"半导体器件专用设备制造\",\"PID\": \"356\"}");
		buff.append(
				", {\"ID\": \"3563\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电子元器件与机电组件设备制造\",\"PID\": \"356\"}");
		buff.append(
				", {\"ID\": \"3569\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他电子专用设备制造\",\"PID\": \"356\"}");
		buff.append(
				", {\"ID\": \"357\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"农、林、牧、渔专用机械制造\",\"PID\": \"35\"}");
		buff.append(
				", {\"ID\": \"3571\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"拖拉机制造\",\"PID\": \"357\"}");
		buff.append(
				", {\"ID\": \"3572\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"机械化农业及园艺机具制造\",\"PID\": \"357\"}");
		buff.append(
				", {\"ID\": \"3573\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"营林及木竹采伐机械制造\",\"PID\": \"357\"}");
		buff.append(
				", {\"ID\": \"3574\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"畜牧机械制造\",\"PID\": \"357\"}");
		buff.append(
				", {\"ID\": \"3575\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"渔业机械制造\",\"PID\": \"357\"}");
		buff.append(
				", {\"ID\": \"3576\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"农林牧渔机械配件制造\",\"PID\": \"357\"}");
		buff.append(
				", {\"ID\": \"3577\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"棉花加工机械制造\",\"PID\": \"357\"}");
		buff.append(
				", {\"ID\": \"3579\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他农、林、牧、渔业机械制造\",\"PID\": \"357\"}");
		buff.append(
				", {\"ID\": \"358\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"医疗仪器设备及器械制造\",\"PID\": \"35\"}");
		buff.append(
				", {\"ID\": \"3581\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"医疗诊断、监护及治疗设备制造\",\"PID\": \"358\"}");
		buff.append(
				", {\"ID\": \"3582\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"口腔科用设备及器具制造\",\"PID\": \"358\"}");
		buff.append(
				", {\"ID\": \"3583\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"医疗实验室及医用消毒设备和器具制造\",\"PID\": \"358\"}");
		buff.append(
				", {\"ID\": \"3584\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"医疗、外科及兽医用器械制造\",\"PID\": \"358\"}");
		buff.append(
				", {\"ID\": \"3585\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"机械治疗及病房护理设备制造\",\"PID\": \"358\"}");
		buff.append(
				", {\"ID\": \"3586\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"康复辅具制造\",\"PID\": \"358\"}");
		buff.append(
				", {\"ID\": \"3587\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"眼镜制造\",\"PID\": \"358\"}");
		buff.append(
				", {\"ID\": \"3589\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他医疗设备及器械制造\",\"PID\": \"358\"}");
		buff.append(
				", {\"ID\": \"359\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"环保、邮政、社会公共服务及其他专用设备制造\",\"PID\": \"35\"}");
		buff.append(
				", {\"ID\": \"3591\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"环境保护专用设备制造\",\"PID\": \"359\"}");
		buff.append(
				", {\"ID\": \"3592\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"地质勘查专用设备制造\",\"PID\": \"359\"}");
		buff.append(
				", {\"ID\": \"3593\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"邮政专用机械及器材制造\",\"PID\": \"359\"}");
		buff.append(
				", {\"ID\": \"3594\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"商业、饮食、服务专用设备制造\",\"PID\": \"359\"}");
		buff.append(
				", {\"ID\": \"3595\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"社会公共安全设备及器材制造\",\"PID\": \"359\"}");
		buff.append(
				", {\"ID\": \"3596\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"交通安全、管制及类似专用设备制造\",\"PID\": \"359\"}");
		buff.append(
				", {\"ID\": \"3597\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"水资源专用机械制造\",\"PID\": \"359\"}");
		buff.append(
				", {\"ID\": \"3599\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他专用设备制造\",\"PID\": \"359\"}");
		buff.append(
				", {\"ID\": \"36\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"汽车制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"361\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"汽车整车制造\",\"PID\": \"36\"}");
		buff.append(
				", {\"ID\": \"3611\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"汽柴油车整车制造\",\"PID\": \"361\"}");
		buff.append(
				", {\"ID\": \"3612\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"新能源车整车制造\",\"PID\": \"361\"}");
		buff.append(
				", {\"ID\": \"3620\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"汽车用发动机制造\",\"PID\": \"36\"}");
		buff.append(
				", {\"ID\": \"3630\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"改装汽车制造\",\"PID\": \"36\"}");
		buff.append(
				", {\"ID\": \"3640\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"低速汽车制造\",\"PID\": \"36\"}");
		buff.append(
				", {\"ID\": \"3650\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电车制造\",\"PID\": \"36\"}");
		buff.append(
				", {\"ID\": \"3660\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"汽车车身、挂车制造\",\"PID\": \"36\"}");
		buff.append(
				", {\"ID\": \"3670\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"汽车零部件及配件制造\",\"PID\": \"36\"}");
		buff.append(
				", {\"ID\": \"37\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铁路、船舶、航空航天和其他运输设备制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"371\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铁路运输设备制造\",\"PID\": \"37\"}");
		buff.append(
				", {\"ID\": \"3711\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"高铁车组制造\",\"PID\": \"371\"}");
		buff.append(
				", {\"ID\": \"3712\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铁路机车车辆制造\",\"PID\": \"371\"}");
		buff.append(
				", {\"ID\": \"3713\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"窄轨机车车辆制造\",\"PID\": \"371\"}");
		buff.append(
				", {\"ID\": \"3714\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"高铁设备、配件制造\",\"PID\": \"371\"}");
		buff.append(
				", {\"ID\": \"3715\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铁路机车车辆配件制造\",\"PID\": \"371\"}");
		buff.append(
				", {\"ID\": \"3716\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铁路专用设备及器材、配件制造\",\"PID\": \"371\"}");
		buff.append(
				", {\"ID\": \"3719\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他铁路运输设备制造\",\"PID\": \"371\"}");
		buff.append(
				", {\"ID\": \"3720\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"城市轨道交通设备制造\",\"PID\": \"37\"}");
		buff.append(
				", {\"ID\": \"373\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"船舶及相关装置制造\",\"PID\": \"37\"}");
		buff.append(
				", {\"ID\": \"3731\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属船舶制造\",\"PID\": \"373\"}");
		buff.append(
				", {\"ID\": \"3732\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"非金属船舶制造\",\"PID\": \"373\"}");
		buff.append(
				", {\"ID\": \"3733\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"娱乐船和运动船制造\",\"PID\": \"373\"}");
		buff.append(
				", {\"ID\": \"3734\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"船用配套设备制造\",\"PID\": \"373\"}");
		buff.append(
				", {\"ID\": \"3735\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"船舶改装\",\"PID\": \"373\"}");
		buff.append(
				", {\"ID\": \"3736\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"船舶拆除\",\"PID\": \"373\"}");
		buff.append(
				", {\"ID\": \"3737\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"海洋工程装备制造\",\"PID\": \"373\"}");
		buff.append(
				", {\"ID\": \"3739\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"航标器材及其他相关装置制造\",\"PID\": \"373\"}");
		buff.append(
				", {\"ID\": \"374\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"航空、航天器及设备制造\",\"PID\": \"37\"}");
		buff.append(
				", {\"ID\": \"3741\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"飞机制造\",\"PID\": \"374\"}");
		buff.append(
				", {\"ID\": \"3742\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"航天器及运载火箭制造\",\"PID\": \"374\"}");
		buff.append(
				", {\"ID\": \"3743\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"航天相关设备制造\",\"PID\": \"374\"}");
		buff.append(
				", {\"ID\": \"3744\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"航空相关设备制造\",\"PID\": \"374\"}");
		buff.append(
				", {\"ID\": \"3749\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他航空航天器制造\",\"PID\": \"374\"}");
		buff.append(
				", {\"ID\": \"375\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"摩托车制造\",\"PID\": \"37\"}");
		buff.append(
				", {\"ID\": \"3751\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"摩托车整车制造\",\"PID\": \"375\"}");
		buff.append(
				", {\"ID\": \"3752\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"摩托车零部件及配件制造\",\"PID\": \"375\"}");
		buff.append(
				", {\"ID\": \"376\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"自行车和残疾人座车制造\",\"PID\": \"37\"}");
		buff.append(
				", {\"ID\": \"3761\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"自行车制造\",\"PID\": \"376\"}");
		buff.append(
				", {\"ID\": \"3762\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"残疾人座车制造\",\"PID\": \"376\"}");
		buff.append(
				", {\"ID\": \"3770\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"助动车制造\",\"PID\": \"37\"}");
		buff.append(
				", {\"ID\": \"3780\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"非公路休闲车及零配件制造\",\"PID\": \"37\"}");
		buff.append(
				", {\"ID\": \"379\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"潜水救捞及其他未列明运输设备制造\",\"PID\": \"37\"}");
		buff.append(
				", {\"ID\": \"3791\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"潜水装备制造\",\"PID\": \"379\"}");
		buff.append(
				", {\"ID\": \"3792\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"水下救捞装备制造\",\"PID\": \"379\"}");
		buff.append(
				", {\"ID\": \"3799\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他未列明运输设备制造\",\"PID\": \"379\"}");
		buff.append(
				", {\"ID\": \"38\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电气机械和器材制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"381\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电机制造\",\"PID\": \"38\"}");
		buff.append(
				", {\"ID\": \"3811\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"发电机及发电机组制造\",\"PID\": \"381\"}");
		buff.append(
				", {\"ID\": \"3812\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电动机制造\",\"PID\": \"381\"}");
		buff.append(
				", {\"ID\": \"3813\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"微特电机及组件制造\",\"PID\": \"381\"}");
		buff.append(
				", {\"ID\": \"3819\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他电机制造\",\"PID\": \"381\"}");
		buff.append(
				", {\"ID\": \"382\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"输配电及控制设备制造\",\"PID\": \"38\"}");
		buff.append(
				", {\"ID\": \"3821\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"变压器、整流器和电感器制造\",\"PID\": \"382\"}");
		buff.append(
				", {\"ID\": \"3822\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电容器及其配套设备制造\",\"PID\": \"382\"}");
		buff.append(
				", {\"ID\": \"3823\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"配电开关控制设备制造\",\"PID\": \"382\"}");
		buff.append(
				", {\"ID\": \"3824\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电力电子元器件制造\",\"PID\": \"382\"}");
		buff.append(
				", {\"ID\": \"3825\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"光伏设备及元器件制造\",\"PID\": \"382\"}");
		buff.append(
				", {\"ID\": \"3829\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他输配电及控制设备制造\",\"PID\": \"382\"}");
		buff.append(
				", {\"ID\": \"383\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电线、电缆、光缆及电工器材制造\",\"PID\": \"38\"}");
		buff.append(
				", {\"ID\": \"3831\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电线、电缆制造\",\"PID\": \"383\"}");
		buff.append(
				", {\"ID\": \"3832\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"光纤制造\",\"PID\": \"383\"}");
		buff.append(
				", {\"ID\": \"3833\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"光缆制造\",\"PID\": \"383\"}");
		buff.append(
				", {\"ID\": \"3834\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"绝缘制品制造\",\"PID\": \"383\"}");
		buff.append(
				", {\"ID\": \"3839\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他电工器材制造\",\"PID\": \"383\"}");
		buff.append(
				", {\"ID\": \"384\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电池制造\",\"PID\": \"38\"}");
		buff.append(
				", {\"ID\": \"3841\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"锂离子电池制造\",\"PID\": \"384\"}");
		buff.append(
				", {\"ID\": \"3842\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"镍氢电池制造\",\"PID\": \"384\"}");
		buff.append(
				", {\"ID\": \"3843\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铅蓄电池制造\",\"PID\": \"384\"}");
		buff.append(
				", {\"ID\": \"3844\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"锌锰电池制造\",\"PID\": \"384\"}");
		buff.append(
				", {\"ID\": \"3849\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他电池制造\",\"PID\": \"384\"}");
		buff.append(
				", {\"ID\": \"385\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"家用电力器具制造\",\"PID\": \"38\"}");
		buff.append(
				", {\"ID\": \"3851\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"家用制冷电器具制造\",\"PID\": \"385\"}");
		buff.append(
				", {\"ID\": \"3852\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"家用空气调节器制造\",\"PID\": \"385\"}");
		buff.append(
				", {\"ID\": \"3853\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"家用通风电器具制造\",\"PID\": \"385\"}");
		buff.append(
				", {\"ID\": \"3854\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"家用厨房电器具制造\",\"PID\": \"385\"}");
		buff.append(
				", {\"ID\": \"3855\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"家用清洁卫生电器具制造\",\"PID\": \"385\"}");
		buff.append(
				", {\"ID\": \"3856\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"家用美容、保健护理电器具制造\",\"PID\": \"385\"}");
		buff.append(
				", {\"ID\": \"3857\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"家用电力器具专用配件制造\",\"PID\": \"385\"}");
		buff.append(
				", {\"ID\": \"3859\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他家用电力器具制造\",\"PID\": \"385\"}");
		buff.append(
				", {\"ID\": \"386\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"非电力家用器具制造\",\"PID\": \"38\"}");
		buff.append(
				", {\"ID\": \"3861\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"燃气及类似能源家用器具制造\",\"PID\": \"386\"}");
		buff.append(
				", {\"ID\": \"3862\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"太阳能器具制造 \",\"PID\": \"386\"}");
		buff.append(
				", {\"ID\": \"3869\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他非电力家用器具制造\",\"PID\": \"386\"}");
		buff.append(
				", {\"ID\": \"387\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"照明器具制造\",\"PID\": \"38\"}");
		buff.append(
				", {\"ID\": \"3871\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电光源制造\",\"PID\": \"387\"}");
		buff.append(
				", {\"ID\": \"3872\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"照明灯具制造\",\"PID\": \"387\"}");
		buff.append(
				", {\"ID\": \"3873\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"舞台及场地用灯制造\",\"PID\": \"387\"}");
		buff.append(
				", {\"ID\": \"3874\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"智能照明器具制造\",\"PID\": \"387\"}");
		buff.append(
				", {\"ID\": \"3879\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"灯用电器附件及其他照明器具制造\",\"PID\": \"387\"}");
		buff.append(
				", {\"ID\": \"389\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他电气机械及器材制造\",\"PID\": \"38\"}");
		buff.append(
				", {\"ID\": \"3891\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电气信号设备装置制造\",\"PID\": \"389\"}");
		buff.append(
				", {\"ID\": \"3899\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他未列明电气机械及器材制造\",\"PID\": \"389\"}");
		buff.append(
				", {\"ID\": \"39\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"计算机、通信和其他电子设备制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"391\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"计算机制造\",\"PID\": \"39\"}");
		buff.append(
				", {\"ID\": \"3911\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"计算机整机制造\",\"PID\": \"391\"}");
		buff.append(
				", {\"ID\": \"3912\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"计算机零部件制造\",\"PID\": \"391\"}");
		buff.append(
				", {\"ID\": \"3913\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"计算机外围设备制造\",\"PID\": \"391\"}");
		buff.append(
				", {\"ID\": \"3914\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"工业控制计算机及系统制造\",\"PID\": \"391\"}");
		buff.append(
				", {\"ID\": \"3915\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"信息安全设备制造\",\"PID\": \"391\"}");
		buff.append(
				", {\"ID\": \"3919\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他计算机制造\",\"PID\": \"391\"}");
		buff.append(
				", {\"ID\": \"392\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"通信设备制造\",\"PID\": \"39\"}");
		buff.append(
				", {\"ID\": \"3921\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"通信系统设备制造\",\"PID\": \"392\"}");
		buff.append(
				", {\"ID\": \"3922\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"通信终端设备制造\",\"PID\": \"392\"}");
		buff.append(
				", {\"ID\": \"393\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"广播电视设备制造\",\"PID\": \"39\"}");
		buff.append(
				", {\"ID\": \"3931\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"广播电视节目制作及发射设备制造\",\"PID\": \"393\"}");
		buff.append(
				", {\"ID\": \"3932\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"广播电视接收设备制造\",\"PID\": \"393\"}");
		buff.append(
				", {\"ID\": \"3933\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"广播电视专用配件制造\",\"PID\": \"393\"}");
		buff.append(
				", {\"ID\": \"3934\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"专业音响设备制造\",\"PID\": \"393\"}");
		buff.append(
				", {\"ID\": \"3939\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"应用电视设备及其他广播电视设备制造\",\"PID\": \"393\"}");
		buff.append(
				", {\"ID\": \"3940\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"雷达及配套设备制造\",\"PID\": \"39\"}");
		buff.append(
				", {\"ID\": \"395\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"视听设备制造\",\"PID\": \"39\"}");
		buff.append(
				", {\"ID\": \"3951\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电视机制造\",\"PID\": \"395\"}");
		buff.append(
				", {\"ID\": \"3952\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"音响设备制造\",\"PID\": \"395\"}");
		buff.append(
				", {\"ID\": \"3953\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"影视录放设备制造\",\"PID\": \"395\"}");
		buff.append(
				", {\"ID\": \"396\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"智能消费设备制造\",\"PID\": \"39\"}");
		buff.append(
				", {\"ID\": \"3961\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"可穿戴智能设备制造\",\"PID\": \"396\"}");
		buff.append(
				", {\"ID\": \"3962\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"智能车载设备制造\",\"PID\": \"396\"}");
		buff.append(
				", {\"ID\": \"3963\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"智能无人飞行器制造\",\"PID\": \"396\"}");
		buff.append(
				", {\"ID\": \"3964\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"服务消费机器人制造\",\"PID\": \"396\"}");
		buff.append(
				", {\"ID\": \"3969\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他智能消费设备制造\",\"PID\": \"396\"}");
		buff.append(
				", {\"ID\": \"397\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电子器件制造\",\"PID\": \"39\"}");
		buff.append(
				", {\"ID\": \"3971\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电子真空器件制造\",\"PID\": \"397\"}");
		buff.append(
				", {\"ID\": \"3972\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"半导体分立器件制造\",\"PID\": \"397\"}");
		buff.append(
				", {\"ID\": \"3973\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"集成电路制造\",\"PID\": \"397\"}");
		buff.append(
				", {\"ID\": \"3974\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"显示器件制造\",\"PID\": \"397\"}");
		buff.append(
				", {\"ID\": \"3975\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"半导体照明器件制造\",\"PID\": \"397\"}");
		buff.append(
				", {\"ID\": \"3976\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"光电子器件制造\",\"PID\": \"397\"}");
		buff.append(
				", {\"ID\": \"3979\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他电子器件制造\",\"PID\": \"397\"}");
		buff.append(
				", {\"ID\": \"398\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电子元件及电子专用材料制造\",\"PID\": \"39\"}");
		buff.append(
				", {\"ID\": \"3981\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电阻电容电感元件制造\",\"PID\": \"398\"}");
		buff.append(
				", {\"ID\": \"3982\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电子电路制造\",\"PID\": \"398\"}");
		buff.append(
				", {\"ID\": \"3983\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"敏感元件及传感器制造\",\"PID\": \"398\"}");
		buff.append(
				", {\"ID\": \"3984\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电声器件及零件制造\",\"PID\": \"398\"}");
		buff.append(
				", {\"ID\": \"3985\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电子专用材料制造\",\"PID\": \"398\"}");
		buff.append(
				", {\"ID\": \"3989\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他电子元件制造\",\"PID\": \"398\"}");
		buff.append(
				", {\"ID\": \"3990\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他电子设备制造\",\"PID\": \"39\"}");
		buff.append(
				", {\"ID\": \"40\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"仪器仪表制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"401\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"通用仪器仪表制造\",\"PID\": \"40\"}");
		buff.append(
				", {\"ID\": \"4011\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"工业自动控制系统装置制造\",\"PID\": \"401\"}");
		buff.append(
				", {\"ID\": \"4012\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电工仪器仪表制造\",\"PID\": \"401\"}");
		buff.append(
				", {\"ID\": \"4013\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"绘图、计算及测量仪器制造\",\"PID\": \"401\"}");
		buff.append(
				", {\"ID\": \"4014\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"实验分析仪器制造\",\"PID\": \"401\"}");
		buff.append(
				", {\"ID\": \"4015\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"试验机制造\",\"PID\": \"401\"}");
		buff.append(
				", {\"ID\": \"4016\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"供应用仪器仪表制造\",\"PID\": \"401\"}");
		buff.append(
				", {\"ID\": \"4019\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他通用仪器制造\",\"PID\": \"401\"}");
		buff.append(
				", {\"ID\": \"402\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"专用仪器仪表制造\",\"PID\": \"40\"}");
		buff.append(
				", {\"ID\": \"4021\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"环境监测专用仪器仪表制造\",\"PID\": \"402\"}");
		buff.append(
				", {\"ID\": \"4022\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"运输设备及生产用计数仪表制造\",\"PID\": \"402\"}");
		buff.append(
				", {\"ID\": \"4023\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"导航、测绘、气象及海洋专用仪器制造\",\"PID\": \"402\"}");
		buff.append(
				", {\"ID\": \"4024\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"农林牧渔专用仪器仪表制造\",\"PID\": \"402\"}");
		buff.append(
				", {\"ID\": \"4025\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"地质勘探和地震专用仪器制造\",\"PID\": \"402\"}");
		buff.append(
				", {\"ID\": \"4026\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"教学专用仪器制造\",\"PID\": \"402\"}");
		buff.append(
				", {\"ID\": \"4027\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"核子及核辐射测量仪器制造\",\"PID\": \"402\"}");
		buff.append(
				", {\"ID\": \"4028\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电子测量仪器制造 \",\"PID\": \"402\"}");
		buff.append(
				", {\"ID\": \"4029\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他专用仪器制造\",\"PID\": \"402\"}");
		buff.append(
				", {\"ID\": \"4030\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"钟表与计时仪器制造\",\"PID\": \"40\"}");
		buff.append(
				", {\"ID\": \"4040\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"光学仪器制造\",\"PID\": \"40\"}");
		buff.append(
				", {\"ID\": \"4050\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"衡器制造\",\"PID\": \"40\"}");
		buff.append(
				", {\"ID\": \"4090\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他仪器仪表制造业\",\"PID\": \"40\"}");
		buff.append(
				", {\"ID\": \"41\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他制造业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"411\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"日用杂品制造\",\"PID\": \"41\"}");
		buff.append(
				", {\"ID\": \"4111\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"鬃毛加工、制刷及清扫工具制造\",\"PID\": \"411\"}");
		buff.append(
				", {\"ID\": \"4119\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他日用杂品制造\",\"PID\": \"411\"}");
		buff.append(
				", {\"ID\": \"4120\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"核辐射加工\",\"PID\": \"41\"}");
		buff.append(
				", {\"ID\": \"4190\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他未列明制造业\",\"PID\": \"41\"}");
		buff.append(
				", {\"ID\": \"42\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"废弃资源综合利用业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"4210\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属废料和碎屑加工处理\",\"PID\": \"42\"}");
		buff.append(
				", {\"ID\": \"4220\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"非金属废料和碎屑加工处理\",\"PID\": \"42\"}");
		buff.append(
				", {\"ID\": \"43\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属制品、机械和设备修理业\",\"PID\": \"C\"}");
		buff.append(
				", {\"ID\": \"4310\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"金属制品修理\",\"PID\": \"43\"}");
		buff.append(
				", {\"ID\": \"4320\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"通用设备修理\",\"PID\": \"43\"}");
		buff.append(
				", {\"ID\": \"4330\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"专用设备修理\",\"PID\": \"43\"}");
		buff.append(
				", {\"ID\": \"434\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铁路、船舶、航空航天等运输设备修理\",\"PID\": \"43\"}");
		buff.append(
				", {\"ID\": \"4341\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"铁路运输设备修理\",\"PID\": \"434\"}");
		buff.append(
				", {\"ID\": \"4342\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"船舶修理\",\"PID\": \"434\"}");
		buff.append(
				", {\"ID\": \"4343\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"航空航天器修理\",\"PID\": \"434\"}");
		buff.append(
				", {\"ID\": \"4349\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他运输设备修理\",\"PID\": \"434\"}");
		buff.append(
				", {\"ID\": \"4350\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"电气设备修理\",\"PID\": \"43\"}");
		buff.append(
				", {\"ID\": \"4360\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"仪器仪表修理\",\"PID\": \"43\"}");
		buff.append(
				", {\"ID\": \"4390\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"fact\",\"NAME\": \"其他机械和设备修理业\",\"PID\": \"43\"}");
		buff.append(
				", {\"ID\": \"44\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电力、热力生产和供应业\",\"PID\": \"D\"}");
		buff.append(
				", {\"ID\": \"441\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电力生产\",\"PID\": \"44\"}");
		buff.append(
				", {\"ID\": \"4411\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"火力发电\",\"PID\": \"441\"}");
		buff.append(
				", {\"ID\": \"4412\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"热电联产\",\"PID\": \"441\"}");
		buff.append(
				", {\"ID\": \"4413\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水力发电\",\"PID\": \"441\"}");
		buff.append(
				", {\"ID\": \"4414\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"核力发电\",\"PID\": \"441\"}");
		buff.append(
				", {\"ID\": \"4415\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"风力发电\",\"PID\": \"441\"}");
		buff.append(
				", {\"ID\": \"4416\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"太阳能发电\",\"PID\": \"441\"}");
		buff.append(
				", {\"ID\": \"4417\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"生物质能发电\",\"PID\": \"441\"}");
		buff.append(
				", {\"ID\": \"4419\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他电力生产\",\"PID\": \"441\"}");
		buff.append(
				", {\"ID\": \"4420\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电力供应\",\"PID\": \"44\"}");
		buff.append(
				", {\"ID\": \"4430\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"热力生产和供应\",\"PID\": \"44\"}");
		buff.append(
				", {\"ID\": \"45\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"燃气生产和供应业\",\"PID\": \"D\"}");
		buff.append(
				", {\"ID\": \"451\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"燃气生产和供应业\",\"PID\": \"45\"}");
		buff.append(
				", {\"ID\": \"4511\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"天然气生产和供应业\",\"PID\": \"451\"}");
		buff.append(
				", {\"ID\": \"4512\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"液化石油气生产和供应业\",\"PID\": \"451\"}");
		buff.append(
				", {\"ID\": \"4513\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"煤气生产和供应业\",\"PID\": \"451\"}");
		buff.append(
				", {\"ID\": \"4520\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"生物质燃气生产和供应业\",\"PID\": \"45\"}");
		buff.append(
				", {\"ID\": \"46\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水的生产和供应业\",\"PID\": \"D\"}");
		buff.append(
				", {\"ID\": \"4610\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"自来水生产和供应\",\"PID\": \"46\"}");
		buff.append(
				", {\"ID\": \"4620\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"?污水处理及其再生利用\",\"PID\": \"46\"}");
		buff.append(
				", {\"ID\": \"4630\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"海水淡化处理\",\"PID\": \"46\"}");
		buff.append(
				", {\"ID\": \"4690\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他水的处理、利用与分配\",\"PID\": \"46\"}");
		buff.append(
				", {\"ID\": \"47\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"房屋建筑业\",\"PID\": \"E\"}");
		buff.append(
				", {\"ID\": \"4710\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"住宅房屋建筑\",\"PID\": \"47\"}");
		buff.append(
				", {\"ID\": \"4720\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育场馆建筑\",\"PID\": \"47\"}");
		buff.append(
				", {\"ID\": \"4790\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他房屋建筑业\",\"PID\": \"47\"}");
		buff.append(
				", {\"ID\": \"48\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"土木工程建筑业\",\"PID\": \"E\"}");
		buff.append(
				", {\"ID\": \"481\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"铁路、道路、隧道和桥梁工程建筑\",\"PID\": \"48\"}");
		buff.append(
				", {\"ID\": \"4811\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"铁路工程建筑\",\"PID\": \"481\"}");
		buff.append(
				", {\"ID\": \"4812\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"公路工程建筑\",\"PID\": \"481\"}");
		buff.append(
				", {\"ID\": \"4813\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"市政道路工程建筑 \",\"PID\": \"481\"}");
		buff.append(
				", {\"ID\": \"4814\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"城市轨道交通工程建筑\",\"PID\": \"481\"}");
		buff.append(
				", {\"ID\": \"4819\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他道路、隧道和桥梁工程建筑 \",\"PID\": \"481\"}");
		buff.append(
				", {\"ID\": \"482\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水利和水运工程建筑\",\"PID\": \"48\"}");
		buff.append(
				", {\"ID\": \"4821\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水源及供水设施工程建筑\",\"PID\": \"482\"}");
		buff.append(
				", {\"ID\": \"4822\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"河湖治理及防洪设施工程建筑\",\"PID\": \"482\"}");
		buff.append(
				", {\"ID\": \"4823\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"港口及航运设施工程建筑\",\"PID\": \"482\"}");
		buff.append(
				", {\"ID\": \"483\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"海洋工程建筑\",\"PID\": \"48\"}");
		buff.append(
				", {\"ID\": \"4831\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"海洋油气资源开发利用工程建筑\",\"PID\": \"483\"}");
		buff.append(
				", {\"ID\": \"4832\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"海洋能源开发利用工程建筑\",\"PID\": \"483\"}");
		buff.append(
				", {\"ID\": \"4833\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"海底隧道工程建筑\",\"PID\": \"483\"}");
		buff.append(
				", {\"ID\": \"4834\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"海底设施铺设工程建筑\",\"PID\": \"483\"}");
		buff.append(
				", {\"ID\": \"4839\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他海洋工程建筑\",\"PID\": \"483\"}");
		buff.append(
				", {\"ID\": \"4840\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"工矿工程建筑\",\"PID\": \"48\"}");
		buff.append(
				", {\"ID\": \"485\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"架线和管道工程建筑\",\"PID\": \"48\"}");
		buff.append(
				", {\"ID\": \"4851\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"架线及设备工程建筑\",\"PID\": \"485\"}");
		buff.append(
				", {\"ID\": \"4852\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"管道工程建筑\",\"PID\": \"485\"}");
		buff.append(
				", {\"ID\": \"4853\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"地下综合管廊工程建筑\",\"PID\": \"485\"}");
		buff.append(
				", {\"ID\": \"486\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"节能环保工程施工\",\"PID\": \"48\"}");
		buff.append(
				", {\"ID\": \"4861\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"节能工程施工\",\"PID\": \"486\"}");
		buff.append(
				", {\"ID\": \"4862\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"环保工程施工\",\"PID\": \"486\"}");
		buff.append(
				", {\"ID\": \"4863\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"生态保护工程施工\",\"PID\": \"486\"}");
		buff.append(
				", {\"ID\": \"487\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电力工程施工\",\"PID\": \"48\"}");
		buff.append(
				", {\"ID\": \"4871\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"火力发电工程施工\",\"PID\": \"487\"}");
		buff.append(
				", {\"ID\": \"4872\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水力发电工程施工\",\"PID\": \"487\"}");
		buff.append(
				", {\"ID\": \"4873\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"核电工程施工\",\"PID\": \"487\"}");
		buff.append(
				", {\"ID\": \"4874\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"风能发电工程施工\",\"PID\": \"487\"}");
		buff.append(
				", {\"ID\": \"4875\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"太阳能发电工程施工\",\"PID\": \"487\"}");
		buff.append(
				", {\"ID\": \"4879\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他电力工程施工\",\"PID\": \"487\"}");
		buff.append(
				", {\"ID\": \"489\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他土木工程建筑\",\"PID\": \"48\"}");
		buff.append(
				", {\"ID\": \"4891\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"园林绿化工程施工\",\"PID\": \"489\"}");
		buff.append(
				", {\"ID\": \"4892\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育场地设施工程施工\",\"PID\": \"489\"}");
		buff.append(
				", {\"ID\": \"4893\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"游乐设施工程施工\",\"PID\": \"489\"}");
		buff.append(
				", {\"ID\": \"4899\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他土木工程建筑施工\",\"PID\": \"489\"}");
		buff.append(
				", {\"ID\": \"49\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"建筑安装业\",\"PID\": \"E\"}");
		buff.append(
				", {\"ID\": \"4910\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电气安装\",\"PID\": \"49\"}");
		buff.append(
				", {\"ID\": \"4920\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"管道和设备安装\",\"PID\": \"49\"}");
		buff.append(
				", {\"ID\": \"499\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他建筑安装业\",\"PID\": \"49\"}");
		buff.append(
				", {\"ID\": \"4991\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育场地设施安装\",\"PID\": \"499\"}");
		buff.append(
				", {\"ID\": \"4999\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他建筑安装\",\"PID\": \"499\"}");
		buff.append(
				", {\"ID\": \"50\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"建筑装饰、装修和其他建筑业\",\"PID\": \"E\"}");
		buff.append(
				", {\"ID\": \"501\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"建筑装饰和装修业\",\"PID\": \"50\"}");
		buff.append(
				", {\"ID\": \"5011\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"公共建筑装饰和装修\",\"PID\": \"501\"}");
		buff.append(
				", {\"ID\": \"5012\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"住宅装饰和装修\",\"PID\": \"501\"}");
		buff.append(
				", {\"ID\": \"5013\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"建筑幕墙装饰和装修\",\"PID\": \"501\"}");
		buff.append(
				", {\"ID\": \"502\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"建筑物拆除和场地准备活动\",\"PID\": \"50\"}");
		buff.append(
				", {\"ID\": \"5021\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"建筑物拆除活动\",\"PID\": \"502\"}");
		buff.append(
				", {\"ID\": \"5022\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"场地准备活动\",\"PID\": \"502\"}");
		buff.append(
				", {\"ID\": \"5030\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"提供施工设备服务\",\"PID\": \"50\"}");
		buff.append(
				", {\"ID\": \"5090\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他未列明建筑业\",\"PID\": \"50\"}");
		buff.append(
				", {\"ID\": \"51\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"批发业\",\"PID\": \"F\"}");
		buff.append(
				", {\"ID\": \"511\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农、林、牧、渔产品批发\",\"PID\": \"51\"}");
		buff.append(
				", {\"ID\": \"5111\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"谷物、豆及薯类批发\",\"PID\": \"511\"}");
		buff.append(
				", {\"ID\": \"5112\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"种子批发\",\"PID\": \"511\"}");
		buff.append(
				", {\"ID\": \"5113\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"畜牧渔业饲料批发\",\"PID\": \"511\"}");
		buff.append(
				", {\"ID\": \"5114\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"棉、麻批发\",\"PID\": \"511\"}");
		buff.append(
				", {\"ID\": \"5115\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"林业产品批发\",\"PID\": \"511\"}");
		buff.append(
				", {\"ID\": \"5116\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"牲畜批发\",\"PID\": \"511\"}");
		buff.append(
				", {\"ID\": \"5117\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"渔业产品批发\",\"PID\": \"511\"}");
		buff.append(
				", {\"ID\": \"5119\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他农牧产品批发\",\"PID\": \"511\"}");
		buff.append(
				", {\"ID\": \"512\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"食品、饮料及烟草制品批发\",\"PID\": \"51\"}");
		buff.append(
				", {\"ID\": \"5121\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"米、面制品及食用油批发\",\"PID\": \"512\"}");
		buff.append(
				", {\"ID\": \"5122\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"糕点、糖果及糖批发\",\"PID\": \"512\"}");
		buff.append(
				", {\"ID\": \"5123\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"果品、蔬菜批发\",\"PID\": \"512\"}");
		buff.append(
				", {\"ID\": \"5124\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"肉、禽、蛋、奶及水产品批发\",\"PID\": \"512\"}");
		buff.append(
				", {\"ID\": \"5125\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"盐及调味品批发\",\"PID\": \"512\"}");
		buff.append(
				", {\"ID\": \"5126\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"营养和保健品批发\",\"PID\": \"512\"}");
		buff.append(
				", {\"ID\": \"5127\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"酒、饮料及茶叶批发\",\"PID\": \"512\"}");
		buff.append(
				", {\"ID\": \"5128\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"烟草制品批发\",\"PID\": \"512\"}");
		buff.append(
				", {\"ID\": \"5129\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他食品批发\",\"PID\": \"512\"}");
		buff.append(
				", {\"ID\": \"513\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"纺织、服装及家庭用品批发\",\"PID\": \"51\"}");
		buff.append(
				", {\"ID\": \"5131\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"纺织品、针织品及原料批发\",\"PID\": \"513\"}");
		buff.append(
				", {\"ID\": \"5132\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"服装批发\",\"PID\": \"513\"}");
		buff.append(
				", {\"ID\": \"5133\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"鞋帽批发\",\"PID\": \"513\"}");
		buff.append(
				", {\"ID\": \"5134\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"化妆品及卫生用品批发\",\"PID\": \"513\"}");
		buff.append(
				", {\"ID\": \"5135\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"厨具卫具及日用杂品批发\",\"PID\": \"513\"}");
		buff.append(
				", {\"ID\": \"5136\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"灯具、装饰物品批发\",\"PID\": \"513\"}");
		buff.append(
				", {\"ID\": \"5137\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"家用视听设备批发\",\"PID\": \"513\"}");
		buff.append(
				", {\"ID\": \"5138\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"日用家电批发\",\"PID\": \"513\"}");
		buff.append(
				", {\"ID\": \"5139\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他家庭用品批发\",\"PID\": \"513\"}");
		buff.append(
				", {\"ID\": \"514\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"文化、体育用品及器材批发\",\"PID\": \"51\"}");
		buff.append(
				", {\"ID\": \"5141\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"文具用品批发\",\"PID\": \"514\"}");
		buff.append(
				", {\"ID\": \"5142\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"体育用品及器材批发\",\"PID\": \"514\"}");
		buff.append(
				", {\"ID\": \"5143\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"图书批发\",\"PID\": \"514\"}");
		buff.append(
				", {\"ID\": \"5144\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"报刊批发\",\"PID\": \"514\"}");
		buff.append(
				", {\"ID\": \"5145\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"音像制品、电子和数字出版物批发\",\"PID\": \"514\"}");
		buff.append(
				", {\"ID\": \"5146\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"首饰、工艺品及收藏品批发\",\"PID\": \"514\"}");
		buff.append(
				", {\"ID\": \"5147\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"乐器批发\",\"PID\": \"514\"}");
		buff.append(
				", {\"ID\": \"5149\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他文化用品批发\",\"PID\": \"514\"}");
		buff.append(
				", {\"ID\": \"515\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"医药及医疗器材批发\",\"PID\": \"51\"}");
		buff.append(
				", {\"ID\": \"5151\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"西药批发\",\"PID\": \"515\"}");
		buff.append(
				", {\"ID\": \"5152\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"中药批发\",\"PID\": \"515\"}");
		buff.append(
				", {\"ID\": \"5153\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"动物用药品批发\",\"PID\": \"515\"}");
		buff.append(
				", {\"ID\": \"5154\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"医疗用品及器材批发\",\"PID\": \"515\"}");
		buff.append(
				", {\"ID\": \"516\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"矿产品、建材及化工产品批发\",\"PID\": \"51\"}");
		buff.append(
				", {\"ID\": \"5161\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"煤炭及制品批发\",\"PID\": \"516\"}");
		buff.append(
				", {\"ID\": \"5162\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"石油及制品批发\",\"PID\": \"516\"}");
		buff.append(
				", {\"ID\": \"5163\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"非金属矿及制品批发\",\"PID\": \"516\"}");
		buff.append(
				", {\"ID\": \"5164\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"金属及金属矿批发\",\"PID\": \"516\"}");
		buff.append(
				", {\"ID\": \"5165\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"建材批发\",\"PID\": \"516\"}");
		buff.append(
				", {\"ID\": \"5166\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"化肥批发\",\"PID\": \"516\"}");
		buff.append(
				", {\"ID\": \"5167\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农药批发\",\"PID\": \"516\"}");
		buff.append(
				", {\"ID\": \"5168\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农用薄膜批发\",\"PID\": \"516\"}");
		buff.append(
				", {\"ID\": \"5169\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他化工产品批发\",\"PID\": \"516\"}");
		buff.append(
				", {\"ID\": \"517\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"机械设备、五金产品及电子产品批发\",\"PID\": \"51\"}");
		buff.append(
				", {\"ID\": \"5171\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农业机械批发\",\"PID\": \"517\"}");
		buff.append(
				", {\"ID\": \"5172\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"汽车及零配件批发\",\"PID\": \"517\"}");
		buff.append(
				", {\"ID\": \"5173\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"摩托车及零配件批发\",\"PID\": \"517\"}");
		buff.append(
				", {\"ID\": \"5174\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"五金产品批发\",\"PID\": \"517\"}");
		buff.append(
				", {\"ID\": \"5175\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"电气设备批发\",\"PID\": \"517\"}");
		buff.append(
				", {\"ID\": \"5176\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"计算机、软件及辅助设备批发\",\"PID\": \"517\"}");
		buff.append(
				", {\"ID\": \"5177\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"通讯设备批发\",\"PID\": \"517\"}");
		buff.append(
				", {\"ID\": \"5178\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"广播影视设备批发\",\"PID\": \"517\"}");
		buff.append(
				", {\"ID\": \"5179\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他机械设备及电子产品批发\",\"PID\": \"517\"}");
		buff.append(
				", {\"ID\": \"518\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"贸易经纪与代理\",\"PID\": \"51\"}");
		buff.append(
				", {\"ID\": \"5181\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"贸易代理\",\"PID\": \"518\"}");
		buff.append(
				", {\"ID\": \"5182\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"一般物品拍卖 \",\"PID\": \"518\"}");
		buff.append(
				", {\"ID\": \"5183\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"艺术品、收藏品拍卖\",\"PID\": \"518\"}");
		buff.append(
				", {\"ID\": \"5184\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"艺术品代理\",\"PID\": \"518\"}");
		buff.append(
				", {\"ID\": \"5189\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他贸易经纪与代理\",\"PID\": \"518\"}");
		buff.append(
				", {\"ID\": \"519\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他批发业\",\"PID\": \"51\"}");
		buff.append(
				", {\"ID\": \"5191\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"再生物资回收与批发\",\"PID\": \"519\"}");
		buff.append(
				", {\"ID\": \"5192\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"宠物食品用品批发\",\"PID\": \"519\"}");
		buff.append(
				", {\"ID\": \"5193\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网批发\",\"PID\": \"519\"}");
		buff.append(
				", {\"ID\": \"5199\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他未列明批发业\",\"PID\": \"519\"}");
		buff.append(
				", {\"ID\": \"52\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"零售业\",\"PID\": \"F\"}");
		buff.append(
				", {\"ID\": \"521\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"综合零售\",\"PID\": \"52\"}");
		buff.append(
				", {\"ID\": \"5211\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"百货零售\",\"PID\": \"521\"}");
		buff.append(
				", {\"ID\": \"5212\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"超级市场零售\",\"PID\": \"521\"}");
		buff.append(
				", {\"ID\": \"5213\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"便利店零售\",\"PID\": \"521\"}");
		buff.append(
				", {\"ID\": \"5219\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他综合零售\",\"PID\": \"521\"}");
		buff.append(
				", {\"ID\": \"522\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"食品、饮料及烟草制品专门零售\",\"PID\": \"52\"}");
		buff.append(
				", {\"ID\": \"5221\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"粮油零售\",\"PID\": \"522\"}");
		buff.append(
				", {\"ID\": \"5222\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"糕点、面包零售\",\"PID\": \"522\"}");
		buff.append(
				", {\"ID\": \"5223\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"果品、蔬菜零售\",\"PID\": \"522\"}");
		buff.append(
				", {\"ID\": \"5224\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"肉、禽、蛋、奶及水产品零售\",\"PID\": \"522\"}");
		buff.append(
				", {\"ID\": \"5225\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"营养和保健品零售\",\"PID\": \"522\"}");
		buff.append(
				", {\"ID\": \"5226\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"酒、饮料及茶叶零售\",\"PID\": \"522\"}");
		buff.append(
				", {\"ID\": \"5227\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"烟草制品零售\",\"PID\": \"522\"}");
		buff.append(
				", {\"ID\": \"5229\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他食品零售\",\"PID\": \"522\"}");
		buff.append(
				", {\"ID\": \"523\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"纺织、服装及日用品专门零售\",\"PID\": \"52\"}");
		buff.append(
				", {\"ID\": \"5231\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"纺织品及针织品零售\",\"PID\": \"523\"}");
		buff.append(
				", {\"ID\": \"5232\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"服装零售\",\"PID\": \"523\"}");
		buff.append(
				", {\"ID\": \"5233\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"鞋帽零售\",\"PID\": \"523\"}");
		buff.append(
				", {\"ID\": \"5234\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"化妆品及卫生用品零售\",\"PID\": \"523\"}");
		buff.append(
				", {\"ID\": \"5235\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"厨具卫具及日用杂品零售\",\"PID\": \"523\"}");
		buff.append(
				", {\"ID\": \"5236\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"钟表、眼镜零售\",\"PID\": \"523\"}");
		buff.append(
				", {\"ID\": \"5237\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"箱包零售\",\"PID\": \"523\"}");
		buff.append(
				", {\"ID\": \"5238\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"自行车等代步设备零售\",\"PID\": \"523\"}");
		buff.append(
				", {\"ID\": \"5239\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他日用品零售\",\"PID\": \"523\"}");
		buff.append(
				", {\"ID\": \"524\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文化、体育用品及器材专门零售\",\"PID\": \"52\"}");
		buff.append(
				", {\"ID\": \"5241\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文具用品零售\",\"PID\": \"524\"}");
		buff.append(
				", {\"ID\": \"5242\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育用品及器材零售\",\"PID\": \"524\"}");
		buff.append(
				", {\"ID\": \"5243\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"图书、报刊零售\",\"PID\": \"524\"}");
		buff.append(
				", {\"ID\": \"5244\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"音像制品、电子和数字出版物零售\",\"PID\": \"524\"}");
		buff.append(
				", {\"ID\": \"5245\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"珠宝首饰零售\",\"PID\": \"524\"}");
		buff.append(
				", {\"ID\": \"5246\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"工艺美术品及收藏品零售\",\"PID\": \"524\"}");
		buff.append(
				", {\"ID\": \"5247\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"乐器零售\",\"PID\": \"524\"}");
		buff.append(
				", {\"ID\": \"5248\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"照相器材零售\",\"PID\": \"524\"}");
		buff.append(
				", {\"ID\": \"5249\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他文化用品零售\",\"PID\": \"524\"}");
		buff.append(
				", {\"ID\": \"525\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"医药及医疗器材专门零售\",\"PID\": \"52\"}");
		buff.append(
				", {\"ID\": \"5251\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"西药零售\",\"PID\": \"525\"}");
		buff.append(
				", {\"ID\": \"5252\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"中药零售\",\"PID\": \"525\"}");
		buff.append(
				", {\"ID\": \"5253\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"动物用药品零售\",\"PID\": \"525\"}");
		buff.append(
				", {\"ID\": \"5254\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"医疗用品及器材零售\",\"PID\": \"525\"}");
		buff.append(
				", {\"ID\": \"5255\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"保健辅助治疗器材零售\",\"PID\": \"525\"}");
		buff.append(
				", {\"ID\": \"526\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"汽车、摩托车、零配件和燃料及其他动力销售\",\"PID\": \"52\"}");
		buff.append(
				", {\"ID\": \"5261\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"汽车新车零售\",\"PID\": \"526\"}");
		buff.append(
				", {\"ID\": \"5262\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"汽车旧车零售\",\"PID\": \"526\"}");
		buff.append(
				", {\"ID\": \"5263\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"汽车零配件零售\",\"PID\": \"526\"}");
		buff.append(
				", {\"ID\": \"5264\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"摩托车及零配件零售\",\"PID\": \"526\"}");
		buff.append(
				", {\"ID\": \"5265\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"机动车燃油零售\",\"PID\": \"526\"}");
		buff.append(
				", {\"ID\": \"5266\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"机动车燃气零售\",\"PID\": \"526\"}");
		buff.append(
				", {\"ID\": \"5267\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"机动车充电销售\",\"PID\": \"526\"}");
		buff.append(
				", {\"ID\": \"527\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"家用电器及电子产品专门零售 \",\"PID\": \"52\"}");
		buff.append(
				", {\"ID\": \"5271\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"家用视听设备零售\",\"PID\": \"527\"}");
		buff.append(
				", {\"ID\": \"5272\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"日用家电零售\",\"PID\": \"527\"}");
		buff.append(
				", {\"ID\": \"5273\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"计算机、软件及辅助设备零售\",\"PID\": \"527\"}");
		buff.append(
				", {\"ID\": \"5274\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"通信设备零售\",\"PID\": \"527\"}");
		buff.append(
				", {\"ID\": \"5279\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他电子产品零售\",\"PID\": \"527\"}");
		buff.append(
				", {\"ID\": \"528\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"五金、家具及室内装饰材料专门零售\",\"PID\": \"52\"}");
		buff.append(
				", {\"ID\": \"5281\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"五金零售\",\"PID\": \"528\"}");
		buff.append(
				", {\"ID\": \"5282\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"灯具零售\",\"PID\": \"528\"}");
		buff.append(
				", {\"ID\": \"5283\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"家具零售\",\"PID\": \"528\"}");
		buff.append(
				", {\"ID\": \"5284\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"涂料零售\",\"PID\": \"528\"}");
		buff.append(
				", {\"ID\": \"5285\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"卫生洁具零售\",\"PID\": \"528\"}");
		buff.append(
				", {\"ID\": \"5286\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"木质装饰材料零售\",\"PID\": \"528\"}");
		buff.append(
				", {\"ID\": \"5287\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"陶瓷、石材装饰材料零售\",\"PID\": \"528\"}");
		buff.append(
				", {\"ID\": \"5289\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他室内装饰材料零售\",\"PID\": \"528\"}");
		buff.append(
				", {\"ID\": \"529\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"货摊、无店铺及其他零售业\",\"PID\": \"52\"}");
		buff.append(
				", {\"ID\": \"5291\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"流动货摊零售\",\"PID\": \"529\"}");
		buff.append(
				", {\"ID\": \"5292\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网零售\",\"PID\": \"529\"}");
		buff.append(
				", {\"ID\": \"5293\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"邮购及电视、电话零售\",\"PID\": \"529\"}");
		buff.append(
				", {\"ID\": \"5294\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"自动售货机零售\",\"PID\": \"529\"}");
		buff.append(
				", {\"ID\": \"5295\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"旧货零售\",\"PID\": \"529\"}");
		buff.append(
				", {\"ID\": \"5296\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"生活用燃料零售\",\"PID\": \"529\"}");
		buff.append(
				", {\"ID\": \"5297\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"宠物食品用品零售\",\"PID\": \"529\"}");
		buff.append(
				", {\"ID\": \"5299\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他未列明零售业\",\"PID\": \"529\"}");
		buff.append(
				", {\"ID\": \"53\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"铁路运输业\",\"PID\": \"G\"}");
		buff.append(
				", {\"ID\": \"531\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"铁路旅客运输\",\"PID\": \"53\"}");
		buff.append(
				", {\"ID\": \"5311\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"高速铁路旅客运输\",\"PID\": \"531\"}");
		buff.append(
				", {\"ID\": \"5312\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"城际铁路旅客运输\",\"PID\": \"531\"}");
		buff.append(
				", {\"ID\": \"5313\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"普通铁路旅客运输\",\"PID\": \"531\"}");
		buff.append(
				", {\"ID\": \"5320\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"铁路货物运输\",\"PID\": \"53\"}");
		buff.append(
				", {\"ID\": \"533\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"铁路运输辅助活动\",\"PID\": \"53\"}");
		buff.append(
				", {\"ID\": \"5331\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"客运火车站\",\"PID\": \"533\"}");
		buff.append(
				", {\"ID\": \"5332\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"货运火车站（场）\",\"PID\": \"533\"}");
		buff.append(
				", {\"ID\": \"5333\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"铁路运输维护活动\",\"PID\": \"533\"}");
		buff.append(
				", {\"ID\": \"5339\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他铁路运输辅助活动\",\"PID\": \"533\"}");
		buff.append(
				", {\"ID\": \"54\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"道路运输业\",\"PID\": \"G\"}");
		buff.append(
				", {\"ID\": \"541\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"城市公共交通运输\",\"PID\": \"54\"}");
		buff.append(
				", {\"ID\": \"5411\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"公共电汽车客运\",\"PID\": \"541\"}");
		buff.append(
				", {\"ID\": \"5412\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"城市轨道交通\",\"PID\": \"541\"}");
		buff.append(
				", {\"ID\": \"5413\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"出租车客运\",\"PID\": \"541\"}");
		buff.append(
				", {\"ID\": \"5414\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"公共自行车服务\",\"PID\": \"541\"}");
		buff.append(
				", {\"ID\": \"5419\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他城市公共交通运输 \",\"PID\": \"541\"}");
		buff.append(
				", {\"ID\": \"542\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"公路旅客运输\",\"PID\": \"54\"}");
		buff.append(
				", {\"ID\": \"5421\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"长途客运\",\"PID\": \"542\"}");
		buff.append(
				", {\"ID\": \"5422\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"旅游客运\",\"PID\": \"542\"}");
		buff.append(
				", {\"ID\": \"5429\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他公路客运\",\"PID\": \"542\"}");
		buff.append(
				", {\"ID\": \"543\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"道路货物运输\",\"PID\": \"54\"}");
		buff.append(
				", {\"ID\": \"5431\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"普通货物道路运输\",\"PID\": \"543\"}");
		buff.append(
				", {\"ID\": \"5432\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"冷藏车道路运输\",\"PID\": \"543\"}");
		buff.append(
				", {\"ID\": \"5433\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"集装箱道路运输\",\"PID\": \"543\"}");
		buff.append(
				", {\"ID\": \"5434\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"大型货物道路运输\",\"PID\": \"543\"}");
		buff.append(
				", {\"ID\": \"5435\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"危险货物道路运输\",\"PID\": \"543\"}");
		buff.append(
				", {\"ID\": \"5436\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"邮件包裹道路运输\",\"PID\": \"543\"}");
		buff.append(
				", {\"ID\": \"5437\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"城市配送\",\"PID\": \"543\"}");
		buff.append(
				", {\"ID\": \"5438\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"搬家运输\",\"PID\": \"543\"}");
		buff.append(
				", {\"ID\": \"5439\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他道路货物运输\",\"PID\": \"543\"}");
		buff.append(
				", {\"ID\": \"544\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"道路运输辅助活动\",\"PID\": \"54\"}");
		buff.append(
				", {\"ID\": \"5441\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"客运汽车站\",\"PID\": \"544\"}");
		buff.append(
				", {\"ID\": \"5442\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"货运枢纽（站）\",\"PID\": \"544\"}");
		buff.append(
				", {\"ID\": \"5443\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"公路管理与养护\",\"PID\": \"544\"}");
		buff.append(
				", {\"ID\": \"5449\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他道路运输辅助活动\",\"PID\": \"544\"}");
		buff.append(
				", {\"ID\": \"55\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"水上运输业\",\"PID\": \"G\"}");
		buff.append(
				", {\"ID\": \"551\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"水上旅客运输\",\"PID\": \"55\"}");
		buff.append(
				", {\"ID\": \"5511\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"海上旅客运输\",\"PID\": \"551\"}");
		buff.append(
				", {\"ID\": \"5512\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"内河旅客运输\",\"PID\": \"551\"}");
		buff.append(
				", {\"ID\": \"5513\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"客运轮渡运输\",\"PID\": \"551\"}");
		buff.append(
				", {\"ID\": \"552\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"水上货物运输\",\"PID\": \"55\"}");
		buff.append(
				", {\"ID\": \"5521\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"远洋货物运输\",\"PID\": \"552\"}");
		buff.append(
				", {\"ID\": \"5522\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"沿海货物运输\",\"PID\": \"552\"}");
		buff.append(
				", {\"ID\": \"5523\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"内河货物运输\",\"PID\": \"552\"}");
		buff.append(
				", {\"ID\": \"553\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"水上运输辅助活动\",\"PID\": \"55\"}");
		buff.append(
				", {\"ID\": \"5531\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"客运港口\",\"PID\": \"553\"}");
		buff.append(
				", {\"ID\": \"5532\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"货运港口\",\"PID\": \"553\"}");
		buff.append(
				", {\"ID\": \"5539\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他水上运输辅助活动\",\"PID\": \"553\"}");
		buff.append(
				", {\"ID\": \"56\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"航空运输业 \",\"PID\": \"G\"}");
		buff.append(
				", {\"ID\": \"561\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"航空客货运输\",\"PID\": \"56\"}");
		buff.append(
				", {\"ID\": \"5611\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"航空旅客运输\",\"PID\": \"561\"}");
		buff.append(
				", {\"ID\": \"5612\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"航空货物运输\",\"PID\": \"561\"}");
		buff.append(
				", {\"ID\": \"562\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"通用航空服务\",\"PID\": \"56\"}");
		buff.append(
				", {\"ID\": \"5621\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"通用航空生产服务\",\"PID\": \"562\"}");
		buff.append(
				", {\"ID\": \"5622\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"观光游览航空服务\",\"PID\": \"562\"}");
		buff.append(
				", {\"ID\": \"5623\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"体育航空运动服务\",\"PID\": \"562\"}");
		buff.append(
				", {\"ID\": \"5629\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他通用航空服务\",\"PID\": \"562\"}");
		buff.append(
				", {\"ID\": \"563\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"航空运输辅助活动\",\"PID\": \"56\"}");
		buff.append(
				", {\"ID\": \"5631\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"机场\",\"PID\": \"563\"}");
		buff.append(
				", {\"ID\": \"5632\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"空中交通管理\",\"PID\": \"563\"}");
		buff.append(
				", {\"ID\": \"5639\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他航空运输辅助活动\",\"PID\": \"563\"}");
		buff.append(
				", {\"ID\": \"57\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"管道运输业 \",\"PID\": \"G\"}");
		buff.append(
				", {\"ID\": \"5710\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"海底管道运输\",\"PID\": \"57\"}");
		buff.append(
				", {\"ID\": \"5720\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"陆地管道运输\",\"PID\": \"57\"}");
		buff.append(
				", {\"ID\": \"58\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"多式联运和运输代理业\",\"PID\": \"G\"}");
		buff.append(
				", {\"ID\": \"5810\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"多式联运\",\"PID\": \"58\"}");
		buff.append(
				", {\"ID\": \"582\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"运输代理业\",\"PID\": \"58\"}");
		buff.append(
				", {\"ID\": \"5821\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"货物运输代理\",\"PID\": \"582\"}");
		buff.append(
				", {\"ID\": \"5822\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"旅客票务代理\",\"PID\": \"582\"}");
		buff.append(
				", {\"ID\": \"5829\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他运输代理业\",\"PID\": \"582\"}");
		buff.append(
				", {\"ID\": \"59\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"装卸搬运和仓储业 \",\"PID\": \"G\"}");
		buff.append(
				", {\"ID\": \"5910\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"装卸搬运\",\"PID\": \"59\"}");
		buff.append(
				", {\"ID\": \"5920\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"通用仓储\",\"PID\": \"59\"}");
		buff.append(
				", {\"ID\": \"5930\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"低温仓储\",\"PID\": \"59\"}");
		buff.append(
				", {\"ID\": \"594\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"危险品仓储\",\"PID\": \"59\"}");
		buff.append(
				", {\"ID\": \"5941\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"油气仓储\",\"PID\": \"594\"}");
		buff.append(
				", {\"ID\": \"5942\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"危险化学品仓储\",\"PID\": \"594\"}");
		buff.append(
				", {\"ID\": \"5949\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他危险品仓储\",\"PID\": \"594\"}");
		buff.append(
				", {\"ID\": \"595\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"谷物、棉花等农产品仓储\",\"PID\": \"59\"}");
		buff.append(
				", {\"ID\": \"5951\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"谷物仓储\",\"PID\": \"595\"}");
		buff.append(
				", {\"ID\": \"5952\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"棉花仓储\",\"PID\": \"595\"}");
		buff.append(
				", {\"ID\": \"5959\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他农产品仓储\",\"PID\": \"595\"}");
		buff.append(
				", {\"ID\": \"5960\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"中药材仓储\",\"PID\": \"59\"}");
		buff.append(
				", {\"ID\": \"5990\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他仓储业\",\"PID\": \"59\"}");
		buff.append(
				", {\"ID\": \"60\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"邮政业\",\"PID\": \"G\"}");
		buff.append(
				", {\"ID\": \"6010\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"邮政基本服务\",\"PID\": \"60\"}");
		buff.append(
				", {\"ID\": \"6020\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"快递服务\",\"PID\": \"60\"}");
		buff.append(
				", {\"ID\": \"6090\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他寄递服务\",\"PID\": \"60\"}");
		buff.append(
				", {\"ID\": \"61\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"住宿业\",\"PID\": \"H\"}");
		buff.append(
				", {\"ID\": \"6110\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"旅游饭店\",\"PID\": \"61\"}");
		buff.append(
				", {\"ID\": \"612\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"一般旅馆\",\"PID\": \"61\"}");
		buff.append(
				", {\"ID\": \"6121\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"经济型连锁酒店\",\"PID\": \"612\"}");
		buff.append(
				", {\"ID\": \"6129\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他一般旅馆\",\"PID\": \"612\"}");
		buff.append(
				", {\"ID\": \"6130\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"民宿服务\",\"PID\": \"61\"}");
		buff.append(
				", {\"ID\": \"6140\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"露营地服务\",\"PID\": \"61\"}");
		buff.append(
				", {\"ID\": \"6190\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"其他住宿业\",\"PID\": \"61\"}");
		buff.append(
				", {\"ID\": \"62\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"餐饮业\",\"PID\": \"H\"}");
		buff.append(
				", {\"ID\": \"6210\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"正餐服务\",\"PID\": \"62\"}");
		buff.append(
				", {\"ID\": \"6220\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"快餐服务\",\"PID\": \"62\"}");
		buff.append(
				", {\"ID\": \"623\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"饮料及冷饮服务\",\"PID\": \"62\"}");
		buff.append(
				", {\"ID\": \"6231\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"茶馆服务\",\"PID\": \"623\"}");
		buff.append(
				", {\"ID\": \"6232\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"咖啡馆服务\",\"PID\": \"623\"}");
		buff.append(
				", {\"ID\": \"6233\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"酒吧服务 \",\"PID\": \"623\"}");
		buff.append(
				", {\"ID\": \"6239\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他饮料及冷饮服务\",\"PID\": \"623\"}");
		buff.append(
				", {\"ID\": \"624\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"餐饮配送及外卖送餐服务\",\"PID\": \"62\"}");
		buff.append(
				", {\"ID\": \"6241\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"餐饮配送服务\",\"PID\": \"624\"}");
		buff.append(
				", {\"ID\": \"6242\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"外卖送餐服务\",\"PID\": \"624\"}");
		buff.append(
				", {\"ID\": \"629\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他餐饮业\",\"PID\": \"62\"}");
		buff.append(
				", {\"ID\": \"6291\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"小吃服务\",\"PID\": \"629\"}");
		buff.append(
				", {\"ID\": \"6299\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他未列明餐饮业\",\"PID\": \"629\"}");
		buff.append(
				", {\"ID\": \"63\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电信、广播电视和卫星传输服务\",\"PID\": \"I\"}");
		buff.append(
				", {\"ID\": \"631\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"电信\",\"PID\": \"63\"}");
		buff.append(
				", {\"ID\": \"6311\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"固定电信服务\",\"PID\": \"631\"}");
		buff.append(
				", {\"ID\": \"6312\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"移动电信服务\",\"PID\": \"631\"}");
		buff.append(
				", {\"ID\": \"6319\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他电信服务\",\"PID\": \"631\"}");
		buff.append(
				", {\"ID\": \"632\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"广播电视传输服务\",\"PID\": \"63\"}");
		buff.append(
				", {\"ID\": \"6321\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"有线广播电视传输服务\",\"PID\": \"632\"}");
		buff.append(
				", {\"ID\": \"6322\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"无线广播电视传输服务\",\"PID\": \"632\"}");
		buff.append(
				", {\"ID\": \"633\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"卫星传输服务\",\"PID\": \"63\"}");
		buff.append(
				", {\"ID\": \"6331\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"广播电视卫星传输服务\",\"PID\": \"633\"}");
		buff.append(
				", {\"ID\": \"6339\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"其他卫星传输服务\",\"PID\": \"633\"}");
		buff.append(
				", {\"ID\": \"64\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网和相关服务\",\"PID\": \"I\"}");
		buff.append(
				", {\"ID\": \"6410\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网接入及相关服务\",\"PID\": \"64\"}");
		buff.append(
				", {\"ID\": \"642\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网信息服务\",\"PID\": \"64\"}");
		buff.append(
				", {\"ID\": \"6421\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网搜索服务\",\"PID\": \"642\"}");
		buff.append(
				", {\"ID\": \"6422\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网游戏服务\",\"PID\": \"642\"}");
		buff.append(
				", {\"ID\": \"6429\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网其他信息服务\",\"PID\": \"642\"}");
		buff.append(
				", {\"ID\": \"643\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网平台\",\"PID\": \"64\"}");
		buff.append(
				", {\"ID\": \"6431\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网生产服务平台\",\"PID\": \"643\"}");
		buff.append(
				", {\"ID\": \"6432\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网生活服务平台\",\"PID\": \"643\"}");
		buff.append(
				", {\"ID\": \"6433\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网科技创新平台\",\"PID\": \"643\"}");
		buff.append(
				", {\"ID\": \"6434\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网公共服务平台\",\"PID\": \"643\"}");
		buff.append(
				", {\"ID\": \"6439\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他互联网平台\",\"PID\": \"643\"}");
		buff.append(
				", {\"ID\": \"6440\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网安全服务\",\"PID\": \"64\"}");
		buff.append(
				", {\"ID\": \"6450\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网数据服务\",\"PID\": \"64\"}");
		buff.append(
				", {\"ID\": \"6490\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他互联网服务\",\"PID\": \"64\"}");
		buff.append(
				", {\"ID\": \"65\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"软件和信息技术服务业\",\"PID\": \"I\"}");
		buff.append(
				", {\"ID\": \"651\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"软件开发\",\"PID\": \"65\"}");
		buff.append(
				", {\"ID\": \"6511\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"基础软件开发\",\"PID\": \"651\"}");
		buff.append(
				", {\"ID\": \"6512\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"支撑软件开发\",\"PID\": \"651\"}");
		buff.append(
				", {\"ID\": \"6513\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"应用软件开发\",\"PID\": \"651\"}");
		buff.append(
				", {\"ID\": \"6519\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他软件开发\",\"PID\": \"651\"}");
		buff.append(
				", {\"ID\": \"6520\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"集成电路设计\",\"PID\": \"65\"}");
		buff.append(
				", {\"ID\": \"653\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"信息系统集成和物联网技术服务\",\"PID\": \"65\"}");
		buff.append(
				", {\"ID\": \"6531\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"信息系统集成服务\",\"PID\": \"653\"}");
		buff.append(
				", {\"ID\": \"6532\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"物联网技术服务\",\"PID\": \"653\"}");
		buff.append(
				", {\"ID\": \"6540\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"运行维护服务\",\"PID\": \"65\"}");
		buff.append(
				", {\"ID\": \"6550\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"信息处理和存储支持服务\",\"PID\": \"65\"}");
		buff.append(
				", {\"ID\": \"6560\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"信息技术咨询服务\",\"PID\": \"65\"}");
		buff.append(
				", {\"ID\": \"657\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"数字内容服务\",\"PID\": \"65\"}");
		buff.append(
				", {\"ID\": \"6571\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"地理遥感信息服务\",\"PID\": \"657\"}");
		buff.append(
				", {\"ID\": \"6572\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"动漫、游戏数字内容服务\",\"PID\": \"657\"}");
		buff.append(
				", {\"ID\": \"6579\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他数字内容服务\",\"PID\": \"657\"}");
		buff.append(
				", {\"ID\": \"659\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他信息技术服务业\",\"PID\": \"65\"}");
		buff.append(
				", {\"ID\": \"6591\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"呼叫中心\",\"PID\": \"659\"}");
		buff.append(
				", {\"ID\": \"6599\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他未列明信息技术服务业\",\"PID\": \"659\"}");
		buff.append(
				", {\"ID\": \"66\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"货币金融服务\",\"PID\": \"J\"}");
		buff.append(
				", {\"ID\": \"6610\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"中央银行服务\",\"PID\": \"66\"}");
		buff.append(
				", {\"ID\": \"662\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"货币银行服务\",\"PID\": \"66\"}");
		buff.append(
				", {\"ID\": \"6621\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"商业银行服务\",\"PID\": \"662\"}");
		buff.append(
				", {\"ID\": \"6622\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"政策性银行服务\",\"PID\": \"662\"}");
		buff.append(
				", {\"ID\": \"6623\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"信用合作社服务\",\"PID\": \"662\"}");
		buff.append(
				", {\"ID\": \"6624\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"农村资金互助社服务\",\"PID\": \"662\"}");
		buff.append(
				", {\"ID\": \"6629\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"其他货币银行服务\",\"PID\": \"662\"}");
		buff.append(
				", {\"ID\": \"663\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"非货币银行服务\",\"PID\": \"66\"}");
		buff.append(
				", {\"ID\": \"6631\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"融资租赁服务\",\"PID\": \"663\"}");
		buff.append(
				", {\"ID\": \"6632\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"财务公司服务 \",\"PID\": \"663\"}");
		buff.append(
				", {\"ID\": \"6633\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"典当\",\"PID\": \"663\"}");
		buff.append(
				", {\"ID\": \"6634\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"汽车金融公司服务\",\"PID\": \"663\"}");
		buff.append(
				", {\"ID\": \"6635\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"小额贷款公司服务 \",\"PID\": \"663\"}");
		buff.append(
				", {\"ID\": \"6636\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"消费金融公司服务 \",\"PID\": \"663\"}");
		buff.append(
				", {\"ID\": \"6637\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"网络借贷服务\",\"PID\": \"663\"}");
		buff.append(
				", {\"ID\": \"6639\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他非货币银行服务\",\"PID\": \"663\"}");
		buff.append(
				", {\"ID\": \"6640\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"银行理财服务\",\"PID\": \"66\"}");
		buff.append(
				", {\"ID\": \"6650\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"银行监管服务\",\"PID\": \"66\"}");
		buff.append(
				", {\"ID\": \"67\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"资本市场服务\",\"PID\": \"J\"}");
		buff.append(
				", {\"ID\": \"671\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"证券市场服务\",\"PID\": \"67\"}");
		buff.append(
				", {\"ID\": \"6711\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"证券市场管理服务\",\"PID\": \"671\"}");
		buff.append(
				", {\"ID\": \"6712\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"证券经纪交易服务\",\"PID\": \"671\"}");
		buff.append(
				", {\"ID\": \"6720\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"公开募集证券投资基金\",\"PID\": \"67\"}");
		buff.append(
				", {\"ID\": \"673\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"非公开募集证券投资基金\",\"PID\": \"67\"}");
		buff.append(
				", {\"ID\": \"6731\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"创业投资基金\",\"PID\": \"673\"}");
		buff.append(
				", {\"ID\": \"6732\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"天使投资\",\"PID\": \"673\"}");
		buff.append(
				", {\"ID\": \"6739\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他非公开募集证券投资基金\",\"PID\": \"673\"}");
		buff.append(
				", {\"ID\": \"674\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"期货市场服务\",\"PID\": \"67\"}");
		buff.append(
				", {\"ID\": \"6741\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"期货市场管理服务\",\"PID\": \"674\"}");
		buff.append(
				", {\"ID\": \"6749\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他期货市场服务\",\"PID\": \"674\"}");
		buff.append(
				", {\"ID\": \"6750\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"证券期货监管服务\",\"PID\": \"67\"}");
		buff.append(
				", {\"ID\": \"6760\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"资本投资服务\",\"PID\": \"67\"}");
		buff.append(
				", {\"ID\": \"6790\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他资本市场服务\",\"PID\": \"67\"}");
		buff.append(
				", {\"ID\": \"68\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"保险业\",\"PID\": \"J\"}");
		buff.append(
				", {\"ID\": \"681\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"人身保险\",\"PID\": \"68\"}");
		buff.append(
				", {\"ID\": \"6811\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"人寿保险\",\"PID\": \"681\"}");
		buff.append(
				", {\"ID\": \"6812\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"年金保险\",\"PID\": \"681\"}");
		buff.append(
				", {\"ID\": \"6813\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"健康保险\",\"PID\": \"681\"}");
		buff.append(
				", {\"ID\": \"6814\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"意外伤害保险\",\"PID\": \"681\"}");
		buff.append(
				", {\"ID\": \"6820\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"财产保险\",\"PID\": \"68\"}");
		buff.append(
				", {\"ID\": \"6830\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"再保险\",\"PID\": \"68\"}");
		buff.append(
				", {\"ID\": \"6840\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"商业养老金\",\"PID\": \"68\"}");
		buff.append(
				", {\"ID\": \"685\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"保险中介服务\",\"PID\": \"68\"}");
		buff.append(
				", {\"ID\": \"6851\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"保险经纪服务\",\"PID\": \"685\"}");
		buff.append(
				", {\"ID\": \"6852\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"保险代理服务\",\"PID\": \"685\"}");
		buff.append(
				", {\"ID\": \"6853\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"保险公估服务\",\"PID\": \"685\"}");
		buff.append(
				", {\"ID\": \"6860\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"保险资产管理\",\"PID\": \"68\"}");
		buff.append(
				", {\"ID\": \"6870\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"保险监管服务\",\"PID\": \"68\"}");
		buff.append(
				", {\"ID\": \"6890\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他保险活动\",\"PID\": \"68\"}");
		buff.append(
				", {\"ID\": \"69\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他金融业\",\"PID\": \"J\"}");
		buff.append(
				", {\"ID\": \"691\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"金融信托与管理服务\",\"PID\": \"69\"}");
		buff.append(
				", {\"ID\": \"6911\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"信托公司\",\"PID\": \"691\"}");
		buff.append(
				", {\"ID\": \"6919\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他金融信托与管理服务\",\"PID\": \"691\"}");
		buff.append(
				", {\"ID\": \"6920\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"控股公司服务\",\"PID\": \"69\"}");
		buff.append(
				", {\"ID\": \"6930\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"非金融机构支付服务\",\"PID\": \"69\"}");
		buff.append(
				", {\"ID\": \"6940\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"金融信息服务\",\"PID\": \"69\"}");
		buff.append(
				", {\"ID\": \"6950\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"金融资产管理公司\",\"PID\": \"69\"}");
		buff.append(
				", {\"ID\": \"699\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他未列明金融业\",\"PID\": \"69\"}");
		buff.append(
				", {\"ID\": \"6991\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"货币经纪公司服务\",\"PID\": \"699\"}");
		buff.append(
				", {\"ID\": \"6999\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他未包括金融业\",\"PID\": \"699\"}");
		buff.append(
				", {\"ID\": \"70\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"房地产业\",\"PID\": \"K\"}");
		buff.append(
				", {\"ID\": \"7010\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"房地产开发经营\",\"PID\": \"70\"}");
		buff.append(
				", {\"ID\": \"7020\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"物业管理\",\"PID\": \"70\"}");
		buff.append(
				", {\"ID\": \"7030\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"房地产中介服务\",\"PID\": \"70\"}");
		buff.append(
				", {\"ID\": \"7040\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"房地产租赁经营\",\"PID\": \"70\"}");
		buff.append(
				", {\"ID\": \"7090\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他房地产业\",\"PID\": \"70\"}");
		buff.append(
				", {\"ID\": \"71\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"租赁业\",\"PID\": \"L\"}");
		buff.append(
				", {\"ID\": \"711\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"机械设备经营租赁\",\"PID\": \"71\"}");
		buff.append(
				", {\"ID\": \"7111\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"汽车租赁\",\"PID\": \"711\"}");
		buff.append(
				", {\"ID\": \"7112\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农业机械经营租赁\",\"PID\": \"711\"}");
		buff.append(
				", {\"ID\": \"7113\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"建筑工程机械与设备经营租赁\",\"PID\": \"711\"}");
		buff.append(
				", {\"ID\": \"7114\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"计算机及通讯设备经营租赁\",\"PID\": \"711\"}");
		buff.append(
				", {\"ID\": \"7115\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"医疗设备经营租赁\",\"PID\": \"711\"}");
		buff.append(
				", {\"ID\": \"7119\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他机械与设备经营租赁\",\"PID\": \"711\"}");
		buff.append(
				", {\"ID\": \"712\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文体设备和用品出租\",\"PID\": \"71\"}");
		buff.append(
				", {\"ID\": \"7121\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"休闲娱乐用品设备出租\",\"PID\": \"712\"}");
		buff.append(
				", {\"ID\": \"7122\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育用品设备出租\",\"PID\": \"712\"}");
		buff.append(
				", {\"ID\": \"7123\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文化用品设备出租\",\"PID\": \"712\"}");
		buff.append(
				", {\"ID\": \"7124\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"图书出租\",\"PID\": \"712\"}");
		buff.append(
				", {\"ID\": \"7125\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"音像制品出租\",\"PID\": \"712\"}");
		buff.append(
				", {\"ID\": \"7129\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他文体设备和用品出租\",\"PID\": \"712\"}");
		buff.append(
				", {\"ID\": \"7130\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"日用品出租\",\"PID\": \"71\"}");
		buff.append(
				", {\"ID\": \"72\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"商务服务业\",\"PID\": \"L\"}");
		buff.append(
				", {\"ID\": \"721\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"组织管理服务\",\"PID\": \"72\"}");
		buff.append(
				", {\"ID\": \"7211\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"企业总部管理\",\"PID\": \"721\"}");
		buff.append(
				", {\"ID\": \"7212\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"投资与资产管理\",\"PID\": \"721\"}");
		buff.append(
				", {\"ID\": \"7213\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"资源与产权交易服务\",\"PID\": \"721\"}");
		buff.append(
				", {\"ID\": \"7214\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"单位后勤管理服务\",\"PID\": \"721\"}");
		buff.append(
				", {\"ID\": \"7215\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农村集体经济组织管理\",\"PID\": \"721\"}");
		buff.append(
				", {\"ID\": \"7219\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他组织管理服务\",\"PID\": \"721\"}");
		buff.append(
				", {\"ID\": \"722\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"综合管理服务\",\"PID\": \"72\"}");
		buff.append(
				", {\"ID\": \"7221\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"园区管理服务\",\"PID\": \"722\"}");
		buff.append(
				", {\"ID\": \"7222\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"商业综合体管理服务\",\"PID\": \"722\"}");
		buff.append(
				", {\"ID\": \"7223\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"市场管理服务\",\"PID\": \"722\"}");
		buff.append(
				", {\"ID\": \"7224\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"供应链管理服务\",\"PID\": \"722\"}");
		buff.append(
				", {\"ID\": \"7229\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他综合管理服务\",\"PID\": \"722\"}");
		buff.append(
				", {\"ID\": \"723\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"法律服务\",\"PID\": \"72\"}");
		buff.append(
				", {\"ID\": \"7231\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"律师及相关法律服务\",\"PID\": \"723\"}");
		buff.append(
				", {\"ID\": \"7232\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"公证服务\",\"PID\": \"723\"}");
		buff.append(
				", {\"ID\": \"7239\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他法律服务\",\"PID\": \"723\"}");
		buff.append(
				", {\"ID\": \"724\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"咨询与调查\",\"PID\": \"72\"}");
		buff.append(
				", {\"ID\": \"7241\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"会计、审计及税务服务\",\"PID\": \"724\"}");
		buff.append(
				", {\"ID\": \"7242\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"市场调查\",\"PID\": \"724\"}");
		buff.append(
				", {\"ID\": \"7243\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"社会经济咨询\",\"PID\": \"724\"}");
		buff.append(
				", {\"ID\": \"7244\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"健康咨询\",\"PID\": \"724\"}");
		buff.append(
				", {\"ID\": \"7245\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"环保咨询\",\"PID\": \"724\"}");
		buff.append(
				", {\"ID\": \"7246\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"体育咨询\",\"PID\": \"724\"}");
		buff.append(
				", {\"ID\": \"7249\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他专业咨询与调查\",\"PID\": \"724\"}");
		buff.append(
				", {\"ID\": \"725\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"广告业\",\"PID\": \"72\"}");
		buff.append(
				", {\"ID\": \"7251\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"互联网广告服务\",\"PID\": \"725\"}");
		buff.append(
				", {\"ID\": \"7259\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他广告服务\",\"PID\": \"725\"}");
		buff.append(
				", {\"ID\": \"726\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"人力资源服务\",\"PID\": \"72\"}");
		buff.append(
				", {\"ID\": \"7261\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"公共就业服务\",\"PID\": \"726\"}");
		buff.append(
				", {\"ID\": \"7262\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"职业中介服务\",\"PID\": \"726\"}");
		buff.append(
				", {\"ID\": \"7263\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"劳务派遣服务\",\"PID\": \"726\"}");
		buff.append(
				", {\"ID\": \"7264\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"创业指导服务\",\"PID\": \"726\"}");
		buff.append(
				", {\"ID\": \"7269\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他人力资源服务\",\"PID\": \"726\"}");
		buff.append(
				", {\"ID\": \"727\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"安全保护服务\",\"PID\": \"72\"}");
		buff.append(
				", {\"ID\": \"7271\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"安全服务\",\"PID\": \"727\"}");
		buff.append(
				", {\"ID\": \"7272\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \" 安全系统监控服务\",\"PID\": \"727\"}");
		buff.append(
				", {\"ID\": \"7279\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他安全保护服务\",\"PID\": \"727\"}");
		buff.append(
				", {\"ID\": \"728\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"会议、展览及相关服务\",\"PID\": \"72\"}");
		buff.append(
				", {\"ID\": \"7281\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"科技会展服务\",\"PID\": \"728\"}");
		buff.append(
				", {\"ID\": \"7282\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"旅游会展服务\",\"PID\": \"728\"}");
		buff.append(
				", {\"ID\": \"7283\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"体育会展服务\",\"PID\": \"728\"}");
		buff.append(
				", {\"ID\": \"7284\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"文化会展服务\",\"PID\": \"728\"}");
		buff.append(
				", {\"ID\": \"7289\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他会议、展览及相关服务\",\"PID\": \"728\"}");
		buff.append(
				", {\"ID\": \"729\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他商务服务业\",\"PID\": \"72\"}");
		buff.append(
				", {\"ID\": \"7291\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"旅行社及相关服务\",\"PID\": \"729\"}");
		buff.append(
				", {\"ID\": \"7292\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"包装服务\",\"PID\": \"729\"}");
		buff.append(
				", {\"ID\": \"7293\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"办公服务\",\"PID\": \"729\"}");
		buff.append(
				", {\"ID\": \"7294\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"翻译服务\",\"PID\": \"729\"}");
		buff.append(
				", {\"ID\": \"7295\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"信用服务\",\"PID\": \"729\"}");
		buff.append(
				", {\"ID\": \"7296\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"非融资担保服务\",\"PID\": \"729\"}");
		buff.append(
				", {\"ID\": \"7297\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"商务代理代办服务\",\"PID\": \"729\"}");
		buff.append(
				", {\"ID\": \"7298\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"票务代理服务\",\"PID\": \"729\"}");
		buff.append(
				", {\"ID\": \"7299\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他未列明商务服务业\",\"PID\": \"729\"}");
		buff.append(
				", {\"ID\": \"73\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"研究和试验发展\",\"PID\": \"M\"}");
		buff.append(
				", {\"ID\": \"7310\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"自然科学研究和试验发展\",\"PID\": \"73\"}");
		buff.append(
				", {\"ID\": \"7320\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"工程和技术研究和试验发展\",\"PID\": \"73\"}");
		buff.append(
				", {\"ID\": \"7330\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农业科学研究和试验发展\",\"PID\": \"73\"}");
		buff.append(
				", {\"ID\": \"7340\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"医学研究和试验发展\",\"PID\": \"73\"}");
		buff.append(
				", {\"ID\": \"7350\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"社会人文科学研究\",\"PID\": \"73\"}");
		buff.append(
				", {\"ID\": \"74\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"专业技术服务业\",\"PID\": \"M\"}");
		buff.append(
				", {\"ID\": \"7410\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"气象服务\",\"PID\": \"74\"}");
		buff.append(
				", {\"ID\": \"7420\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"地震服务\",\"PID\": \"74\"}");
		buff.append(
				", {\"ID\": \"743\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"海洋服务\",\"PID\": \"74\"}");
		buff.append(
				", {\"ID\": \"7431\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"海洋气象服务\",\"PID\": \"743\"}");
		buff.append(
				", {\"ID\": \"7432\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"海洋环境服务\",\"PID\": \"743\"}");
		buff.append(
				", {\"ID\": \"7439\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他海洋服务\",\"PID\": \"743\"}");
		buff.append(
				", {\"ID\": \"744\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"测绘地理信息服务\",\"PID\": \"74\"}");
		buff.append(
				", {\"ID\": \"7441\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"遥感测绘服务\",\"PID\": \"744\"}");
		buff.append(
				", {\"ID\": \"7449\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他测绘地理信息服务\",\"PID\": \"744\"}");
		buff.append(
				", {\"ID\": \"745\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"质检技术服务\",\"PID\": \"74\"}");
		buff.append(
				", {\"ID\": \"7451\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"检验检疫服务\",\"PID\": \"745\"}");
		buff.append(
				", {\"ID\": \"7452\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"检测服务\",\"PID\": \"745\"}");
		buff.append(
				", {\"ID\": \"7453\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"计量服务\",\"PID\": \"745\"}");
		buff.append(
				", {\"ID\": \"7454\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"标准化服务\",\"PID\": \"745\"}");
		buff.append(
				", {\"ID\": \"7455\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"认证认可服务\",\"PID\": \"745\"}");
		buff.append(
				", {\"ID\": \"7459\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他质检技术服务\",\"PID\": \"745\"}");
		buff.append(
				", {\"ID\": \"746\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"环境与生态监测\",\"PID\": \"74\"}");
		buff.append(
				", {\"ID\": \"7461\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"环境保护监测\",\"PID\": \"746\"}");
		buff.append(
				", {\"ID\": \"7462\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"生态资源监测\",\"PID\": \"746\"}");
		buff.append(
				", {\"ID\": \"7463\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"野生动物疫源疫病防控监测\",\"PID\": \"746\"}");
		buff.append(
				", {\"ID\": \"747\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"地质勘查 \",\"PID\": \"74\"}");
		buff.append(
				", {\"ID\": \"7471\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"能源矿产地质勘查\",\"PID\": \"747\"}");
		buff.append(
				", {\"ID\": \"7472\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"固体矿产地质勘查\",\"PID\": \"747\"}");
		buff.append(
				", {\"ID\": \"7473\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"水、二氧化碳等矿产地质勘查\",\"PID\": \"747\"}");
		buff.append(
				", {\"ID\": \"7474\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"基础地质勘查\",\"PID\": \"747\"}");
		buff.append(
				", {\"ID\": \"7475\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"地质勘查技术服务\",\"PID\": \"747\"}");
		buff.append(
				", {\"ID\": \"748\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"工程技术与设计服务\",\"PID\": \"74\"}");
		buff.append(
				", {\"ID\": \"7481\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"工程管理服务\",\"PID\": \"748\"}");
		buff.append(
				", {\"ID\": \"7482\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"工程监理服务\",\"PID\": \"748\"}");
		buff.append(
				", {\"ID\": \"7483\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"工程勘察活动\",\"PID\": \"748\"}");
		buff.append(
				", {\"ID\": \"7484\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"工程设计活动\",\"PID\": \"748\"}");
		buff.append(
				", {\"ID\": \"7485\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"规划设计管理\",\"PID\": \"748\"}");
		buff.append(
				", {\"ID\": \"7486\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"土地规划服务\",\"PID\": \"748\"}");
		buff.append(
				", {\"ID\": \"749\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"工业与专业设计及其他专业技术服务\",\"PID\": \"74\"}");
		buff.append(
				", {\"ID\": \"7491\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"工业设计服务\",\"PID\": \"749\"}");
		buff.append(
				", {\"ID\": \"7492\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"专业设计服务\",\"PID\": \"749\"}");
		buff.append(
				", {\"ID\": \"7493\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"兽医服务\",\"PID\": \"749\"}");
		buff.append(
				", {\"ID\": \"7499\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他未列明专业技术服务业\",\"PID\": \"749\"}");
		buff.append(
				", {\"ID\": \"75\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"科技推广和应用服务\",\"PID\": \"M\"}");
		buff.append(
				", {\"ID\": \"751\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"技术推广服务\",\"PID\": \"75\"}");
		buff.append(
				", {\"ID\": \"7511\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"农林牧渔技术推广服务\",\"PID\": \"751\"}");
		buff.append(
				", {\"ID\": \"7512\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"生物技术推广服务\",\"PID\": \"751\"}");
		buff.append(
				", {\"ID\": \"7513\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"新材料技术推广服务\",\"PID\": \"751\"}");
		buff.append(
				", {\"ID\": \"7514\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"节能技术推广服务\",\"PID\": \"751\"}");
		buff.append(
				", {\"ID\": \"7515\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"新能源技术推广服务\",\"PID\": \"751\"}");
		buff.append(
				", {\"ID\": \"7516\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"环保技术推广服务\",\"PID\": \"751\"}");
		buff.append(
				", {\"ID\": \"7517\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"三维（3D)打印技术推广服务\",\"PID\": \"751\"}");
		buff.append(
				", {\"ID\": \"7519\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他技术推广服务\",\"PID\": \"751\"}");
		buff.append(
				", {\"ID\": \"7520\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"知识产权服务\",\"PID\": \"75\"}");
		buff.append(
				", {\"ID\": \"7530\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"科技中介服务\",\"PID\": \"75\"}");
		buff.append(
				", {\"ID\": \"7540\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"创业空间服务\",\"PID\": \"75\"}");
		buff.append(
				", {\"ID\": \"7590\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他科技推广服务业\",\"PID\": \"75\"}");
		buff.append(
				", {\"ID\": \"76\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水利管理业\",\"PID\": \"N\"}");
		buff.append(
				", {\"ID\": \"7610\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"防洪除涝设施管理\",\"PID\": \"76\"}");
		buff.append(
				", {\"ID\": \"7620\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水资源管理\",\"PID\": \"76\"}");
		buff.append(
				", {\"ID\": \"7630\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"天然水收集与分配\",\"PID\": \"76\"}");
		buff.append(
				", {\"ID\": \"7640\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水文服务\",\"PID\": \"76\"}");
		buff.append(
				", {\"ID\": \"7690\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他水利管理业\",\"PID\": \"76\"}");
		buff.append(
				", {\"ID\": \"77\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"生态保护和环境治理业\",\"PID\": \"N\"}");
		buff.append(
				", {\"ID\": \"771\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"生态保护\",\"PID\": \"77\"}");
		buff.append(
				", {\"ID\": \"7711\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"自然生态系统保护管理\",\"PID\": \"771\"}");
		buff.append(
				", {\"ID\": \"7712\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"自然遗迹保护管理\",\"PID\": \"771\"}");
		buff.append(
				", {\"ID\": \"7713\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"野生动物保护\",\"PID\": \"771\"}");
		buff.append(
				", {\"ID\": \"7714\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"野生植物保护\",\"PID\": \"771\"}");
		buff.append(
				", {\"ID\": \"7715\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"动物园、水族馆管理服务\",\"PID\": \"771\"}");
		buff.append(
				", {\"ID\": \"7716\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"植物园管理服务\",\"PID\": \"771\"}");
		buff.append(
				", {\"ID\": \"7719\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他自然保护\",\"PID\": \"771\"}");
		buff.append(
				", {\"ID\": \"772\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"环境治理业\",\"PID\": \"77\"}");
		buff.append(
				", {\"ID\": \"7721\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"水污染治理\",\"PID\": \"772\"}");
		buff.append(
				", {\"ID\": \"7722\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"大气污染治理\",\"PID\": \"772\"}");
		buff.append(
				", {\"ID\": \"7723\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"固体废物治理\",\"PID\": \"772\"}");
		buff.append(
				", {\"ID\": \"7724\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"危险废物治理\",\"PID\": \"772\"}");
		buff.append(
				", {\"ID\": \"7725\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"放射性废物治理\",\"PID\": \"772\"}");
		buff.append(
				", {\"ID\": \"7726\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"土壤污染治理与修复服务\",\"PID\": \"772\"}");
		buff.append(
				", {\"ID\": \"7727\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"噪声与振动控制服务\",\"PID\": \"772\"}");
		buff.append(
				", {\"ID\": \"7729\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他污染治理 \",\"PID\": \"772\"}");
		buff.append(
				", {\"ID\": \"78\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"公共设施管理业\",\"PID\": \"N\"}");
		buff.append(
				", {\"ID\": \"7810\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"市政设施管理\",\"PID\": \"78\"}");
		buff.append(
				", {\"ID\": \"7820\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"环境卫生管理\",\"PID\": \"78\"}");
		buff.append(
				", {\"ID\": \"7830\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"城乡市容管理\",\"PID\": \"78\"}");
		buff.append(
				", {\"ID\": \"7840\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"绿化管理\",\"PID\": \"78\"}");
		buff.append(
				", {\"ID\": \"7850\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"城市公园管理\",\"PID\": \"78\"}");
		buff.append(
				", {\"ID\": \"786\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"游览景区管理\",\"PID\": \"78\"}");
		buff.append(
				", {\"ID\": \"7861\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"名胜风景区管理\",\"PID\": \"786\"}");
		buff.append(
				", {\"ID\": \"7862\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"森林公园管理\",\"PID\": \"786\"}");
		buff.append(
				", {\"ID\": \"7869\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他游览景区管理\",\"PID\": \"786\"}");
		buff.append(
				", {\"ID\": \"79\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"土地管理业\",\"PID\": \"N\"}");
		buff.append(
				", {\"ID\": \"7910\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"土地整治服务\",\"PID\": \"79\"}");
		buff.append(
				", {\"ID\": \"7920\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"土地调查评估服务\",\"PID\": \"79\"}");
		buff.append(
				", {\"ID\": \"7930\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"土地登记服务\",\"PID\": \"79\"}");
		buff.append(
				", {\"ID\": \"7940\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"土地登记代理服务\",\"PID\": \"79\"}");
		buff.append(
				", {\"ID\": \"7990\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他土地管理服务\",\"PID\": \"79\"}");
		buff.append(
				", {\"ID\": \"80\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"居民服务业\",\"PID\": \"O\"}");
		buff.append(
				", {\"ID\": \"8010\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"家庭服务\",\"PID\": \"80\"}");
		buff.append(
				", {\"ID\": \"8020\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"托儿所服务\",\"PID\": \"80\"}");
		buff.append(
				", {\"ID\": \"8030\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"洗染服务\",\"PID\": \"80\"}");
		buff.append(
				", {\"ID\": \"8040\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"理发及美容服务\",\"PID\": \"80\"}");
		buff.append(
				", {\"ID\": \"805\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"洗浴和保健养生服务\",\"PID\": \"80\"}");
		buff.append(
				", {\"ID\": \"8051\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"洗浴服务\",\"PID\": \"805\"}");
		buff.append(
				", {\"ID\": \"8052\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"足浴服务\",\"PID\": \"805\"}");
		buff.append(
				", {\"ID\": \"8053\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"养生保健服务\",\"PID\": \"805\"}");
		buff.append(
				", {\"ID\": \"8060\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"摄影扩印服务\",\"PID\": \"80\"}");
		buff.append(
				", {\"ID\": \"8070\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"婚姻服务\",\"PID\": \"80\"}");
		buff.append(
				", {\"ID\": \"8080\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"殡葬服务\",\"PID\": \"80\"}");
		buff.append(
				", {\"ID\": \"8090\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他居民服务业\",\"PID\": \"80\"}");
		buff.append(
				", {\"ID\": \"81\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"机动车、电子产品和日用产品修理业\",\"PID\": \"O\"}");
		buff.append(
				", {\"ID\": \"811\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"汽车、摩托车等修理与维护\",\"PID\": \"81\"}");
		buff.append(
				", {\"ID\": \"8111\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"汽车修理与维护\",\"PID\": \"811\"}");
		buff.append(
				", {\"ID\": \"8112\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"大型车辆装备修理与维护\",\"PID\": \"811\"}");
		buff.append(
				", {\"ID\": \"8113\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"摩托车修理与维护\",\"PID\": \"811\"}");
		buff.append(
				", {\"ID\": \"8114\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"助动车等修理与维护\",\"PID\": \"811\"}");
		buff.append(
				", {\"ID\": \"812\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"计算机和办公设备维修\",\"PID\": \"81\"}");
		buff.append(
				", {\"ID\": \"8121\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"计算机和辅助设备修理\",\"PID\": \"812\"}");
		buff.append(
				", {\"ID\": \"8122\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"通讯设备修理\",\"PID\": \"812\"}");
		buff.append(
				", {\"ID\": \"8129\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他办公设备维修\",\"PID\": \"812\"}");
		buff.append(
				", {\"ID\": \"813\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"家用电器修理\",\"PID\": \"81\"}");
		buff.append(
				", {\"ID\": \"8131\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"家用电子产品修理\",\"PID\": \"813\"}");
		buff.append(
				", {\"ID\": \"8132\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"日用电器修理\",\"PID\": \"813\"}");
		buff.append(
				", {\"ID\": \"819\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"其他日用产品修理业\",\"PID\": \"81\"}");
		buff.append(
				", {\"ID\": \"8191\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"自行车修理\",\"PID\": \"819\"}");
		buff.append(
				", {\"ID\": \"8192\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"鞋和皮革修理\",\"PID\": \"819\"}");
		buff.append(
				", {\"ID\": \"8193\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"家具和相关物品修理\",\"PID\": \"819\"}");
		buff.append(
				", {\"ID\": \"8199\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"other\",\"NAME\": \"其他未列明日用产品修理业\",\"PID\": \"819\"}");
		buff.append(
				", {\"ID\": \"82\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他服务业\",\"PID\": \"O\"}");
		buff.append(
				", {\"ID\": \"821\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"清洁服务\",\"PID\": \"82\"}");
		buff.append(
				", {\"ID\": \"8211\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"建筑物清洁服务\",\"PID\": \"821\"}");
		buff.append(
				", {\"ID\": \"8219\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他清洁服务\",\"PID\": \"821\"}");
		buff.append(
				", {\"ID\": \"822\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"宠物服务\",\"PID\": \"82\"}");
		buff.append(
				", {\"ID\": \"8221\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"宠物饲养\",\"PID\": \"822\"}");
		buff.append(
				", {\"ID\": \"8222\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"宠物医院服务\",\"PID\": \"822\"}");
		buff.append(
				", {\"ID\": \"8223\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"宠物美容服务\",\"PID\": \"822\"}");
		buff.append(
				", {\"ID\": \"8224\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"宠物寄托收养服务\",\"PID\": \"822\"}");
		buff.append(
				", {\"ID\": \"8229\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他宠物服务\",\"PID\": \"822\"}");
		buff.append(
				", {\"ID\": \"8290\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他未列明服务业\",\"PID\": \"82\"}");
		buff.append(
				", {\"ID\": \"83\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"教育\",\"PID\": \"P\"}");
		buff.append(
				", {\"ID\": \"8310\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"学前教育\",\"PID\": \"83\"}");
		buff.append(
				", {\"ID\": \"832\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"初等教育\",\"PID\": \"83\"}");
		buff.append(
				", {\"ID\": \"8321\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"普通小学教育\",\"PID\": \"832\"}");
		buff.append(
				", {\"ID\": \"8322\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"成人小学教育\",\"PID\": \"832\"}");
		buff.append(
				", {\"ID\": \"833\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"中等教育\",\"PID\": \"83\"}");
		buff.append(
				", {\"ID\": \"8331\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"普通初中教育\",\"PID\": \"833\"}");
		buff.append(
				", {\"ID\": \"8332\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"职业初中教育\",\"PID\": \"833\"}");
		buff.append(
				", {\"ID\": \"8333\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"成人初中教育\",\"PID\": \"833\"}");
		buff.append(
				", {\"ID\": \"8334\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"普通高中教育\",\"PID\": \"833\"}");
		buff.append(
				", {\"ID\": \"8335\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"成人高中教育\",\"PID\": \"833\"}");
		buff.append(
				", {\"ID\": \"8336\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"中等职业学校教育\",\"PID\": \"833\"}");
		buff.append(
				", {\"ID\": \"834\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"高等教育\",\"PID\": \"83\"}");
		buff.append(
				", {\"ID\": \"8341\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"普通高等教育\",\"PID\": \"834\"}");
		buff.append(
				", {\"ID\": \"8342\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"成人高等教育\",\"PID\": \"834\"}");
		buff.append(
				", {\"ID\": \"8350\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"特殊教育\",\"PID\": \"83\"}");
		buff.append(
				", {\"ID\": \"839\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"技能培训、教育辅助及其他教育\",\"PID\": \"83\"}");
		buff.append(
				", {\"ID\": \"8391\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"职业技能培训\",\"PID\": \"839\"}");
		buff.append(
				", {\"ID\": \"8392\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体校及体育培训\",\"PID\": \"839\"}");
		buff.append(
				", {\"ID\": \"8393\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文化艺术培训\",\"PID\": \"839\"}");
		buff.append(
				", {\"ID\": \"8394\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"教育辅助服务\",\"PID\": \"839\"}");
		buff.append(
				", {\"ID\": \"8399\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他未列明教育\",\"PID\": \"839\"}");
		buff.append(
				", {\"ID\": \"84\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"卫生\",\"PID\": \"Q\"}");
		buff.append(
				", {\"ID\": \"841\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"医院\",\"PID\": \"84\"}");
		buff.append(
				", {\"ID\": \"8411\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"综合医院\",\"PID\": \"841\"}");
		buff.append(
				", {\"ID\": \"8412\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"中医医院\",\"PID\": \"841\"}");
		buff.append(
				", {\"ID\": \"8413\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"中西医结合医院\",\"PID\": \"841\"}");
		buff.append(
				", {\"ID\": \"8414\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"民族医院\",\"PID\": \"841\"}");
		buff.append(
				", {\"ID\": \"8415\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"专科医院\",\"PID\": \"841\"}");
		buff.append(
				", {\"ID\": \"8416\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"疗养院\",\"PID\": \"841\"}");
		buff.append(
				", {\"ID\": \"842\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"基层医疗卫生服务\",\"PID\": \"84\"}");
		buff.append(
				", {\"ID\": \"8421\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"社区卫生服务中心（站）\",\"PID\": \"842\"}");
		buff.append(
				", {\"ID\": \"8422\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"街道卫生院\",\"PID\": \"842\"}");
		buff.append(
				", {\"ID\": \"8423\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"乡镇卫生院\",\"PID\": \"842\"}");
		buff.append(
				", {\"ID\": \"8424\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"村卫生室 \",\"PID\": \"842\"}");
		buff.append(
				", {\"ID\": \"8425\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"门诊部（所）\",\"PID\": \"842\"}");
		buff.append(
				", {\"ID\": \"843\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"专业公共卫生服务\",\"PID\": \"84\"}");
		buff.append(
				", {\"ID\": \"8431\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"疾病预防控制中心\",\"PID\": \"843\"}");
		buff.append(
				", {\"ID\": \"8432\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"专科疾病防治院（所、站)\",\"PID\": \"843\"}");
		buff.append(
				", {\"ID\": \"8433\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"妇幼保健院（所、站）\",\"PID\": \"843\"}");
		buff.append(
				", {\"ID\": \"8434\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"急救中心（站）服务\",\"PID\": \"843\"}");
		buff.append(
				", {\"ID\": \"8435\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"采供血机构服务\",\"PID\": \"843\"}");
		buff.append(
				", {\"ID\": \"8436\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"计划生育技术服务活动\",\"PID\": \"843\"}");
		buff.append(
				", {\"ID\": \"849\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他卫生活动\",\"PID\": \"84\"}");
		buff.append(
				", {\"ID\": \"8491\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"健康体检服务\",\"PID\": \"849\"}");
		buff.append(
				", {\"ID\": \"8492\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"临床检验服务\",\"PID\": \"849\"}");
		buff.append(
				", {\"ID\": \"8499\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"serv\",\"NAME\": \"其他未列明卫生服务\",\"PID\": \"849\"}");
		buff.append(
				", {\"ID\": \"85\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"社会工作\",\"PID\": \"Q\"}");
		buff.append(
				", {\"ID\": \"851\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"提供住宿社会工作\",\"PID\": \"85\"}");
		buff.append(
				", {\"ID\": \"8511\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"干部休养所\",\"PID\": \"851\"}");
		buff.append(
				", {\"ID\": \"8512\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"护理机构服务\",\"PID\": \"851\"}");
		buff.append(
				", {\"ID\": \"8513\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"精神康复服务\",\"PID\": \"851\"}");
		buff.append(
				", {\"ID\": \"8514\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"老年人、残疾人养护服务\",\"PID\": \"851\"}");
		buff.append(
				", {\"ID\": \"8515\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"临终关怀服务\",\"PID\": \"851\"}");
		buff.append(
				", {\"ID\": \"8516\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"孤残儿童收养和庇护服务\",\"PID\": \"851\"}");
		buff.append(
				", {\"ID\": \"8519\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他提供住宿社会救助\",\"PID\": \"851\"}");
		buff.append(
				", {\"ID\": \"852\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"不提供住宿社会工作\",\"PID\": \"85\"}");
		buff.append(
				", {\"ID\": \"8521\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"社会看护与帮助服务\",\"PID\": \"852\"}");
		buff.append(
				", {\"ID\": \"8522\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"康复辅具适配服务\",\"PID\": \"852\"}");
		buff.append(
				", {\"ID\": \"8529\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他不提供住宿社会工作\",\"PID\": \"852\"}");
		buff.append(
				", {\"ID\": \"86\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"新闻和出版业\",\"PID\": \"R\"}");
		buff.append(
				", {\"ID\": \"8610\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"新闻业\",\"PID\": \"86\"}");
		buff.append(
				", {\"ID\": \"862\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"出版业\",\"PID\": \"86\"}");
		buff.append(
				", {\"ID\": \"8621\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"图书出版\",\"PID\": \"862\"}");
		buff.append(
				", {\"ID\": \"8622\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"报纸出版\",\"PID\": \"862\"}");
		buff.append(
				", {\"ID\": \"8623\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"期刊出版\",\"PID\": \"862\"}");
		buff.append(
				", {\"ID\": \"8624\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"音像制品出版\",\"PID\": \"862\"}");
		buff.append(
				", {\"ID\": \"8625\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电子出版物出版\",\"PID\": \"862\"}");
		buff.append(
				", {\"ID\": \"8626\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"数字出版\",\"PID\": \"862\"}");
		buff.append(
				", {\"ID\": \"8629\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他出版业\",\"PID\": \"862\"}");
		buff.append(
				", {\"ID\": \"87\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"广播、电视、电影和影视录音制作业\",\"PID\": \"R\"}");
		buff.append(
				", {\"ID\": \"8710\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"广播\",\"PID\": \"87\"}");
		buff.append(
				", {\"ID\": \"8720\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电视\",\"PID\": \"87\"}");
		buff.append(
				", {\"ID\": \"8730\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"影视节目制作\",\"PID\": \"87\"}");
		buff.append(
				", {\"ID\": \"8740\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"广播电视集成播控\",\"PID\": \"87\"}");
		buff.append(
				", {\"ID\": \"8750\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电影和广播电视节目发行\",\"PID\": \"87\"}");
		buff.append(
				", {\"ID\": \"8760\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电影放映\",\"PID\": \"87\"}");
		buff.append(
				", {\"ID\": \"8770\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"录音制作\",\"PID\": \"87\"}");
		buff.append(
				", {\"ID\": \"88\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文化艺术业\",\"PID\": \"R\"}");
		buff.append(
				", {\"ID\": \"8810\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文艺创作与表演\",\"PID\": \"88\"}");
		buff.append(
				", {\"ID\": \"8820\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"艺术表演场馆\",\"PID\": \"88\"}");
		buff.append(
				", {\"ID\": \"883\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"图书馆与档案馆\",\"PID\": \"88\"}");
		buff.append(
				", {\"ID\": \"8831\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"图书馆\",\"PID\": \"883\"}");
		buff.append(
				", {\"ID\": \"8832\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"档案馆\",\"PID\": \"883\"}");
		buff.append(
				", {\"ID\": \"8840\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文物及非物质文化遗产保护\",\"PID\": \"88\"}");
		buff.append(
				", {\"ID\": \"8850\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"博物馆\",\"PID\": \"88\"}");
		buff.append(
				", {\"ID\": \"8860\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"烈士陵园、纪念馆\",\"PID\": \"88\"}");
		buff.append(
				", {\"ID\": \"8870\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"群众文体活动\",\"PID\": \"88\"}");
		buff.append(
				", {\"ID\": \"8890\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他文化艺术业\",\"PID\": \"88\"}");
		buff.append(
				", {\"ID\": \"89\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育\",\"PID\": \"R\"}");
		buff.append(
				", {\"ID\": \"891\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育组织\",\"PID\": \"89\"}");
		buff.append(
				", {\"ID\": \"8911\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育竞赛组织\",\"PID\": \"891\"}");
		buff.append(
				", {\"ID\": \"8912\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育保障组织\",\"PID\": \"891\"}");
		buff.append(
				", {\"ID\": \"8919\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他体育组织\",\"PID\": \"891\"}");
		buff.append(
				", {\"ID\": \"892\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育场地设施管理\",\"PID\": \"89\"}");
		buff.append(
				", {\"ID\": \"8921\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育场馆管理\",\"PID\": \"892\"}");
		buff.append(
				", {\"ID\": \"8929\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他体育场地设施管理 \",\"PID\": \"892\"}");
		buff.append(
				", {\"ID\": \"8930\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"健身休闲活动\",\"PID\": \"89\"}");
		buff.append(
				", {\"ID\": \"899\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他体育\",\"PID\": \"89\"}");
		buff.append(
				", {\"ID\": \"8991\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育中介代理服务\",\"PID\": \"899\"}");
		buff.append(
				", {\"ID\": \"8992\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育健康服务\",\"PID\": \"899\"}");
		buff.append(
				", {\"ID\": \"8999\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他未列明体育\",\"PID\": \"899\"}");
		buff.append(
				", {\"ID\": \"90\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"娱乐业\",\"PID\": \"R\"}");
		buff.append(
				", {\"ID\": \"901\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"室内娱乐活动\",\"PID\": \"90\"}");
		buff.append(
				", {\"ID\": \"9011\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"歌舞厅娱乐活动\",\"PID\": \"901\"}");
		buff.append(
				", {\"ID\": \"9012\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电子游艺厅娱乐活动\",\"PID\": \"901\"}");
		buff.append(
				", {\"ID\": \"9013\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"网吧活动\",\"PID\": \"901\"}");
		buff.append(
				", {\"ID\": \"9019\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他室内娱乐活动\",\"PID\": \"901\"}");
		buff.append(
				", {\"ID\": \"9020\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"游乐园\",\"PID\": \"90\"}");
		buff.append(
				", {\"ID\": \"9030\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"休闲观光活动\",\"PID\": \"90\"}");
		buff.append(
				", {\"ID\": \"904\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"彩票活动\",\"PID\": \"90\"}");
		buff.append(
				", {\"ID\": \"9041\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育彩票服务\",\"PID\": \"904\"}");
		buff.append(
				", {\"ID\": \"9042\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"福利彩票服务\",\"PID\": \"904\"}");
		buff.append(
				", {\"ID\": \"9049\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他彩票服务\",\"PID\": \"904\"}");
		buff.append(
				", {\"ID\": \"905\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文化娱乐体育活动和经纪代理服务\",\"PID\": \"90\"}");
		buff.append(
				", {\"ID\": \"9051\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文化活动服务\",\"PID\": \"905\"}");
		buff.append(
				", {\"ID\": \"9052\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育表演服务\",\"PID\": \"905\"}");
		buff.append(
				", {\"ID\": \"9053\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文化娱乐经纪人\",\"PID\": \"905\"}");
		buff.append(
				", {\"ID\": \"9054\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"体育经纪人\",\"PID\": \"905\"}");
		buff.append(
				", {\"ID\": \"9059\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他文化艺术经纪代理\",\"PID\": \"905\"}");
		buff.append(
				", {\"ID\": \"9090\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他娱乐业\",\"PID\": \"90\"}");
		buff.append(
				", {\"ID\": \"91\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"中国共产党机关\",\"PID\": \"S\"}");
		buff.append(
				", {\"ID\": \"9100\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"中国共产党机关\",\"PID\": \"91\"}");
		buff.append(
				", {\"ID\": \"92\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"国家机构\",\"PID\": \"S\"}");
		buff.append(
				", {\"ID\": \"9210\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"国家权力机构\",\"PID\": \"92\"}");
		buff.append(
				", {\"ID\": \"922\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"国家行政机构\",\"PID\": \"92\"}");
		buff.append(
				", {\"ID\": \"9221\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"综合事务管理机构\",\"PID\": \"922\"}");
		buff.append(
				", {\"ID\": \"9222\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"对外事务管理机构\",\"PID\": \"922\"}");
		buff.append(
				", {\"ID\": \"9223\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"公共安全管理机构\",\"PID\": \"922\"}");
		buff.append(
				", {\"ID\": \"9224\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"社会事务管理机构\",\"PID\": \"922\"}");
		buff.append(
				", {\"ID\": \"9225\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"经济事务管理机构\",\"PID\": \"922\"}");
		buff.append(
				", {\"ID\": \"9226\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"行政监督检查机构\",\"PID\": \"922\"}");
		buff.append(
				", {\"ID\": \"923\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"人民法院和人民检察院\",\"PID\": \"92\"}");
		buff.append(
				", {\"ID\": \"9231\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"人民法院\",\"PID\": \"923\"}");
		buff.append(
				", {\"ID\": \"9232\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"人民检察院\",\"PID\": \"923\"}");
		buff.append(
				", {\"ID\": \"929\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他国家机构\",\"PID\": \"92\"}");
		buff.append(
				", {\"ID\": \"9291\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"消防管理机构\",\"PID\": \"929\"}");
		buff.append(
				", {\"ID\": \"9299\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他未列明国家机构\",\"PID\": \"929\"}");
		buff.append(
				", {\"ID\": \"93\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"人民政协、民主党派\",\"PID\": \"S\"}");
		buff.append(
				", {\"ID\": \"9310\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"人民政协\",\"PID\": \"93\"}");
		buff.append(
				", {\"ID\": \"9320\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"民主党派\",\"PID\": \"93\"}");
		buff.append(
				", {\"ID\": \"94\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"社会保障\",\"PID\": \"S\"}");
		buff.append(
				", {\"ID\": \"941\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"基本保险\",\"PID\": \"94\"}");
		buff.append(
				", {\"ID\": \"9411\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"基本养老保险\",\"PID\": \"941\"}");
		buff.append(
				", {\"ID\": \"9412\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"基本医疗保险\",\"PID\": \"941\"}");
		buff.append(
				", {\"ID\": \"9413\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"失业保险\",\"PID\": \"941\"}");
		buff.append(
				", {\"ID\": \"9414\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"工伤保险\",\"PID\": \"941\"}");
		buff.append(
				", {\"ID\": \"9415\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"生育保险\",\"PID\": \"941\"}");
		buff.append(
				", {\"ID\": \"9419\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他基本保险\",\"PID\": \"941\"}");
		buff.append(
				", {\"ID\": \"9420\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"补充保险\",\"PID\": \"94\"}");
		buff.append(
				", {\"ID\": \"9490\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他社会保障\",\"PID\": \"94\"}");
		buff.append(
				", {\"ID\": \"95\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"群众团体、社会团体和其他成员组织\",\"PID\": \"S\"}");
		buff.append(
				", {\"ID\": \"951\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"群众团体\",\"PID\": \"95\"}");
		buff.append(
				", {\"ID\": \"9511\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"工会\",\"PID\": \"951\"}");
		buff.append(
				", {\"ID\": \"9512\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"妇联\",\"PID\": \"951\"}");
		buff.append(
				", {\"ID\": \"9513\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"共青团\",\"PID\": \"951\"}");
		buff.append(
				", {\"ID\": \"9519\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他群众团体\",\"PID\": \"951\"}");
		buff.append(
				", {\"ID\": \"952\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"社会团体\",\"PID\": \"95\"}");
		buff.append(
				", {\"ID\": \"9521\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"专业性团体\",\"PID\": \"952\"}");
		buff.append(
				", {\"ID\": \"9522\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"行业性团体\",\"PID\": \"952\"}");
		buff.append(
				", {\"ID\": \"9529\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"其他社会团体\",\"PID\": \"952\"}");
		buff.append(
				", {\"ID\": \"9530\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"基金会\",\"PID\": \"95\"}");
		buff.append(
				", {\"ID\": \"954\",\"ENTERPRISELEVEL\": \"3\",\"ASSESSTYPEID\": \"\",\"NAME\": \"宗教组织\",\"PID\": \"95\"}");
		buff.append(
				", {\"ID\": \"9541\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"宗教团体服务\",\"PID\": \"954\"}");
		buff.append(
				", {\"ID\": \"9542\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"宗教活动场所服务\",\"PID\": \"954\"}");
		buff.append(
				", {\"ID\": \"96\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"基层群众自治组织及其他组织\",\"PID\": \"S\"}");
		buff.append(
				", {\"ID\": \"9610\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"社区居民自治组织\",\"PID\": \"96\"}");
		buff.append(
				", {\"ID\": \"9620\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"村民自治组织\",\"PID\": \"96\"}");
		buff.append(
				", {\"ID\": \"97\",\"ENTERPRISELEVEL\": \"2\",\"ASSESSTYPEID\": \"\",\"NAME\": \"国际组织\",\"PID\": \"T\"}");
		buff.append(
				", {\"ID\": \"9700\",\"ENTERPRISELEVEL\": \"4\",\"ASSESSTYPEID\": \"\",\"NAME\": \"国际组织\",\"PID\": \"97\"}");
		buff.append(
				", {\"ID\": \"A\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"农、林、牧、渔业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"B\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"采矿业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"C\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"制造业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"D\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"电力、热力、燃气及水生产和供应业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"E\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"建筑业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"F\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"批发和零售业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"G\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"交通运输、仓储和邮政业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"H\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"住宿和餐饮业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"I\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"信息传输、软件和信息技术服务业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"J\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"金融业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"K\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"房地产业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"L\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"租赁和商务服务业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"M\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"科学研究和技术服务业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"N\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"水利、环境和公共设施管理业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"O\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"居民服务、修理和其他服务业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"P\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"教育\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"Q\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"卫生和社会工作\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"R\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"文化、体育和娱乐业\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"S\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"公共管理、社会保障和社会组织\",\"PID\": \"\"}");
		buff.append(
				", {\"ID\": \"T\",\"ENTERPRISELEVEL\": \"1\",\"ASSESSTYPEID\": \"\",\"NAME\": \"国际组织\",\"PID\": \"\"}]");

		return buff.toString();
	}

}
