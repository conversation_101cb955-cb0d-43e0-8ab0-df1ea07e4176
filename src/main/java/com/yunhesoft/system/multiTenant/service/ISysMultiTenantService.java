package com.yunhesoft.system.multiTenant.service;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.multiTenant.entity.dto.SysEnterpriseDto;
import com.yunhesoft.system.multiTenant.entity.dto.SysMultiTenantDto;
import com.yunhesoft.system.multiTenant.entity.po.SysEnterpriseType;
import com.yunhesoft.system.multiTenant.entity.po.SysMultiTenant;
import org.springframework.dao.DuplicateKeyException;

import java.util.List;

public interface ISysMultiTenantService {
	/**
	 * 注册
	 */
	boolean saveLogin(SysMultiTenantDto bean) throws DuplicateKeyException;

	/**
	 * 校验
	 */
	String verifyData(SysMultiTenantDto bean);

	/**
	 * 租户数据初始化
	 * 
	 * @param tenantId
	 */
	void initData(SysMultiTenantDto bean, String tenantId, String type) throws Exception;

	/**
	 * 获取行业类型
	 * 
	 * @param bean
	 */
	List<SysEnterpriseDto> getEnterprise(String pid);

	/**
	 * 根据租户id获取企业信息
	 * 
	 * @param tenantId
	 * @return
	 */
	SysMultiTenant getSysMultiTenant(String tenantId);

	/**
	 * 根据行业id获取行业信息
	 * 
	 * @param id
	 * @return
	 */
	SysEnterpriseType getSysEnterpriseType(String id);

	/**
	 * 获企业更多信息
	 * 
	 * @return
	 */
	SysMultiTenant getSysMultiTenant();

	/**
	 * 获企业更多信息
	 */
	boolean saveMultiTenantData(SysMultiTenant bean);

	/**
	 * 获取当前登录企业的企业码
	 * 
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	String getCurrentEnterpriseNum();

	/**
	 * 获取邀请信息
	 * 
	 * <AUTHOR>
	 * @return
	 * @params
	 */

	String getEnterpriseInvite();

	/**
	 * 校验邀请信息
	 * 
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	JSONObject enterpriseSecurityCheck(String token);

	/**
	 * 缓存邀请信息
	 * 
	 * <AUTHOR>
	 * @return
	 * @params
	 */

	String cacheInviteInfo(String enterpriseInviteToken);

	/**
	 * 获取缓存的邀请信息
	 * 
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	String getInviteInfoByCache(String id);

	/**
	 * 根据租户id获取其租户管理员 tips: 此方法使用的查询方法均是不使用租户id
	 * 
	 * @param tenantId
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	SysEmployeeInfo getAdminInfoByTenantId(String tenantId);

	/**
	 * 初始化企业类型
	 * 
	 * @return
	 */
	void initEnterpriseType();

}