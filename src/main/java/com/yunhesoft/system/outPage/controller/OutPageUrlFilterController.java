package com.yunhesoft.system.outPage.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.lang.Nullable;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.outPage.entity.OutSecurityDto;
import com.yunhesoft.system.outPage.entity.SaveWhilteDto;
import com.yunhesoft.system.outPage.service.IOutSecurity;
import com.yunhesoft.system.settings.entity.SysOpenInterfaceWhite;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @Description: 外部api接口 此类下的方法将会跳过鉴权
 * @date 2023/2/14
 */
@RestController
@RequestMapping("/system/outPage")
@Api(tags = "外部数据接口")
public class OutPageUrlFilterController extends BaseRestController {
	private String ALLOW_URL_STR = "/system/outPage/tdsOut/**";
	@Autowired
	private IOutSecurity outSecurity;

	/**
	 * 页面路径鉴别
	 *
	 * @return Boolean true 允许 flase 禁止
	 * <AUTHOR>
	 * @params
	 */
	@ApiOperation(value = "页面路径鉴别")
	@RequestMapping(value = "/outTdsSecurity", method = RequestMethod.POST)
	public Res<?> outTdsSecurity(@RequestBody @Nullable OutSecurityDto param) {
		JSONObject result = new JSONObject();
		result.put("allowShow_TDS", false);
		if (StringUtils.isNotEmpty(ALLOW_URL_STR) && StringUtils.isNotEmpty(param.getViewUrl())) {
			List<String> allowUrls = new ArrayList<>();
			if (ALLOW_URL_STR.contains(",")) {
				allowUrls = Arrays.asList(ALLOW_URL_STR.split(","));
			} else {
				allowUrls.add(ALLOW_URL_STR);
			}
			AntPathMatcher antPathMatcher = new AntPathMatcher();
			for (String allowUrl : allowUrls) {
				if (antPathMatcher.match(allowUrl, param.getViewUrl())) {
					result.put("allowShow_TDS", true);
					break;
				}
			}
		}
		return Res.OK(result);
	}

	/**
	 * 查询外部页面权限
	 * 
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	@ApiOperation(value = "查询外部页面权限")
	@RequestMapping(value = "/getWhite", method = RequestMethod.POST)
	public Res<?> outTdsSecurity(@RequestBody @Nullable SysOpenInterfaceWhite param) {
		return Res.OK(outSecurity.getWhite(param));
	}

	/**
	 *
	 * 保存外部页面权限
	 * 
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	@ApiOperation(value = "保存外部页面权限")
	@RequestMapping(value = "/saveWhite", method = RequestMethod.POST)
	public Res<?> saveWhite(@RequestBody SaveWhilteDto param) {
		return Res.OK(outSecurity.saveWhite(param));
	}

}
