package com.yunhesoft.system.outPage.service;


import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.outPage.entity.OutSecurityDto;
import com.yunhesoft.system.outPage.entity.SaveWhilteDto;
import com.yunhesoft.system.settings.entity.SysOpenInterfaceWhite;

/**
 * @Description: 开放页面安全
 * <AUTHOR>
 * @date 2023/3/22
 */
public interface IOutSecurity {

    Res<?> filterTdsRequest(String tdsAlias);

    /**
     * 数据源功能 是否允许访问
     *
     * @param param
     * @return
     * <AUTHOR>
     */
    Boolean allowShowTdsByOut(OutSecurityDto param);

    /**
     * 保存白名单
     * <AUTHOR>
     * @return
     * @params
    */
    Boolean saveWhite(SaveWhilteDto param);
    /**
     * 查询白名单
     * <AUTHOR>
     * @return
     * @params
    */
    List<SysOpenInterfaceWhite> getWhite(SysOpenInterfaceWhite param);


}
