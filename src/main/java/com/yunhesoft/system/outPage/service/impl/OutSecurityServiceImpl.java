package com.yunhesoft.system.outPage.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.entity.BaseEntity;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.outPage.entity.OutSecurityDto;
import com.yunhesoft.system.outPage.entity.SaveWhilteDto;
import com.yunhesoft.system.outPage.service.IOutSecurity;
import com.yunhesoft.system.settings.entity.SysOpenInterfaceWhite;
import com.yunhesoft.system.settings.entity.dto.SysOpenInterFaceWhiteDto;
import com.yunhesoft.system.settings.service.IOpenInterfaceWhiteService;
import com.yunhesoft.system.settings.service.impl.OpenInterfaceWhiteServiceImpl;
import com.yunhesoft.system.tds.entity.po.TdataSource;
import com.yunhesoft.system.tds.service.IDataSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 安全接口实现
 * <AUTHOR>
 * @date 2023/3/22
 */
@Service
public class OutSecurityServiceImpl implements IOutSecurity {


    private static String[] ALLOW_TDS;
    @Autowired
    private IDataSourceService tdsServ;
    @Autowired
    public HttpServletRequest request;
    @Autowired
    private IOpenInterfaceWhiteService whiteService;
    @Autowired
    private EntityService dao;


    /**
     * 是否拦截数据源请求
     * <AUTHOR>
     * @return
     * @params
    */
    @Override
    public Res<?> filterTdsRequest(String tdsAlias){
        String description = SysUserUtil.getCurrentUser().getDescription();
		if(StringUtils.isNotEmpty(description) && description.contains("controller-api-white")) {
            //白名单
            OutSecurityDto securityDto = new OutSecurityDto();
            securityDto.setTdsAlias(tdsAlias);
            Boolean aBoolean = this.allowShowTdsByOut(securityDto);
            if (!aBoolean) {
                //不允许访问
                return Res.FAIL(403, "拒绝访问");
            }
        }
        return null;
    }


    /**
     * 数据源功能 是否允许访问
     *
     * @param param
     * @return
     * <AUTHOR>
     */
    @Override
    public Boolean allowShowTdsByOut(OutSecurityDto param) {
        List<TdataSource> outTds = tdsServ.getOutTds();
        if(StringUtils.isNotEmpty(outTds)) {
            List<String> collect = outTds.stream().map(i -> i.getTdsalias()).collect(Collectors.toList());
            OutSecurityServiceImpl.ALLOW_TDS = collect.toArray(new String[collect.size()]);
        }else {
            return false;
        }
        //数据源别名
        String tdsAlias = param.getTdsAlias();
        return Arrays.asList(ALLOW_TDS).contains(tdsAlias);
    }

    /**
     * 保存白名单
     *
     * @param param
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public Boolean saveWhite(SaveWhilteDto param) {
        if(param!=null && StringUtils.isNotEmpty(param.getData())){
            List<SysOpenInterFaceWhiteDto> list = new ArrayList<>();
            JSONArray jsonArray = JSONArray.parseArray(param.getData());
            if (jsonArray!=null){
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    SysOpenInterFaceWhiteDto bean  =jsonObject.toJavaObject(SysOpenInterFaceWhiteDto.class);
                    list.add(bean);
                }
                if(StringUtils.isNotEmpty(list)){
                    whiteService.save(list);
                }
            }

        }

        return false;
    }

    /**
     * 查询白名单
     *
     * @param param
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public List<SysOpenInterfaceWhite> getWhite(SysOpenInterfaceWhite param) {
    	return whiteService.read(param);
    }

}
