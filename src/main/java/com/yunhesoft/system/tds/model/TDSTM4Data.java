package com.yunhesoft.system.tds.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.PropertyUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.http.HttpsUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 获取TM4数据源数据
 * x。zhong 2025-07-01
 */
@Log4j2
public class TDSTM4Data extends ADataSource {

    private static final long serialVersionUID = 1L;

    //application.properties 配置文件变量
    private static String TM4URLParam = "app.Tm4Url";

    //tm4地址  // @Value("${app.Tm4Url:}")
    private static String TM4URL = null;

    // 输出参数别名
    private List<String> outParaList = new ArrayList<String>();

    public void load() {
        this.load(1, 0);
    }

    public void load(int page, int pageSize) {
        if (StringUtils.isEmpty(getTm4url())) {
            String s = "配置文件未配置TM4地址(" + TM4URLParam + ")";
            this.setErrorInfo(s);
            log.error(s);
            return;
        }
        String tdsAlias = this.getTdsQuerySql();//tm3数据源别名
        if (StringUtils.isEmpty(tdsAlias)) {
            String s = "未设置TM4数据源别名";
            this.setErrorInfo(s);
            log.error(s);
            return;
        }
        this.outParaList.clear();
        this.createData(tdsAlias, page, pageSize);
    }

    /**
     * 生成数据
     */
    private void createData(String alias, int page, int pageSize) {
        JSONObject jsonobj = this.getTds(alias, page, pageSize);//通过web方式获取tm3数据源数据
        if (jsonobj != null) {
            JSONObject props = jsonobj.getJSONObject("props");
            JSONArray cols = props.getJSONArray("cols");
            JSONObject colprop = props.getJSONObject("colprop");//列信息
            JSONArray data = jsonobj.getJSONArray("data");//输出数据
            JSONObject pageInfo = jsonobj.getJSONObject("pageInfo");//分页信息
            if (pageInfo != null) {
                tds.setPage(pageInfo.getInteger("page"));
                tds.setPageSize(pageSize);
                tds.setPageCount(pageInfo.getInteger("pageCount"));
                tds.setRecordCount(pageInfo.getInteger("recordCount"));
            }
            //生成输出参数列表
            List<TOutPara> outList = new ArrayList<>();
            int n = -1;
            for (int i = 0; i < cols.size(); i++) {
                String col = cols.getString(i);
                n++;
                TOutPara outPara = new TOutPara(this);
                JSONObject colObj = colprop.getJSONObject(col);
                outPara.setAlias(col);
                outPara.setAlign(colObj.getString("align"));
                outPara.setAutowidth(false);
                if ("tdsDouble".equalsIgnoreCase(colObj.getString("dataType"))) {
                    outPara.setDataType(DataType.tdsDouble);
                } else if ("tdsLong".equalsIgnoreCase(colObj.getString("dataType"))) {
                    outPara.setDataType(DataType.tdsLong);
                } else if ("tdsInteger".equalsIgnoreCase(colObj.getString("dataType"))) {
                    outPara.setDataType(DataType.tdsInteger);
                } else {
                    outPara.setDataType(DataType.tdsString);
                }
                outPara.setComType(colObj.getString("comType"));
                outPara.setID(n);
                outPara.setName(colObj.getString("header"));
                String width = colObj.getString("width");
                try {
                    outPara.setWidth(Integer.parseInt(width));
                } catch (Exception ex) {
                    outPara.setWidth(100);
                }
                if (colObj.getBoolean("hidden")) {
                    outPara.setVisible(false);
                } else {
                    outPara.setVisible(true);
                }
                outList.add(outPara);
                this.outParaList.add(outPara.getAlias());
            }

            this.setOutPara(outList);
            //生成数据
            if (data != null && data.size() > 0) {
                for (int i = 0; i < data.size(); i++) {
                    JSONObject rowObj = data.getJSONObject(i);
                    int rowid = tds.addRow();
                    for (int j = 0; j < this.outParaList.size(); j++) {
                        String col = this.outParaList.get(j);
                        Object value = rowObj.get(col);
                        tds.set(rowid, j, value);
                    }
                }
            }
        }
    }


    /**
     * 获得TM4地址
     *
     * @return
     */
    private String getTm4url() {
        if (TM4URL == null) {
            TM4URL = PropertyUtils.getProperty(TM4URLParam);
        }
        return TM4URL;
    }

    /**
     * 获取数据源数据
     *
     * @param tdsAlias    数据源别名
     * @param inParaAlias 输入参数
     * @param page
     * @param pageSize
     * @return
     */
    private JSONObject getTds(String tdsAlias, int page, int pageSize) {
        JSONObject obj = null;
        try {
            String url = this.getTm4url() + "/tm4main/tds/getTdsData"; //https://192.168.0.28:30302/tm4main/tds/getTdsData
            JSONObject param = new JSONObject();
            param.put("tdsAlias", tdsAlias);
            param.put("page", page);
            param.put("pageSize", pageSize);
            String inParaAlias = "";
            if (StringUtils.isNotEmpty(this.getInParaList())) {
                JSONArray ary = new JSONArray();
                for (TInPara inPara : this.getInParaList()) {
                    JSONObject inParaObj = new JSONObject();
                    inParaObj.put("name", inPara.getParaAlias());
                    inParaObj.put("value", inPara.getValue());
                    ary.add(inParaObj);
                }
                inParaAlias = ary.toJSONString();
            }
            param.put("inParaAlias", inParaAlias);
            //log.info("获取TM4数据源数据:" + url);
            String json = this.readFromUrl(url, param);
            if (json != null && json.length() > 0) {
                obj = JSONArray.parseObject(json).getJSONArray("result").getJSONObject(0);
            }
        } catch (Exception e) {
            log.error("", e);
            this.setErrorInfo(e.getMessage());
        }
        return obj;
    }

    /**
     * 通过url获得数据
     *
     * @param url
     * @return
     */
    private String readFromUrl(String url, JSONObject params) {
        String jsonData = null;
  //      try {
            String currentToken = SysUserHolder.getCurrentToken();
            JSONObject header = new JSONObject();
            //addHeader("Authorization", "Bearer " + currentToken == null ? "" : currentToken)
            header.put("Authorization", "Bearer " + currentToken == null ? "" : currentToken);
            jsonData = HttpsUtils.doPost(url, "body", params, header);
//
//
//            OkHttpClient client = null;
//            if (url.startsWith("https")) {//忽略证书
//                client = SslUtils.getUnsafeOkHttpClent().newBuilder()
//                        .connectTimeout(10, TimeUnit.SECONDS)  // 连接超时
//                        .readTimeout(60, TimeUnit.SECONDS)     // 读取超时
//                        .writeTimeout(60, TimeUnit.SECONDS)    // 写入超时
//                        .build();
//            } else {
//                client = new OkHttpClient.Builder()
//                        .connectTimeout(10, TimeUnit.SECONDS)  // 连接超时
//                        .readTimeout(60, TimeUnit.SECONDS)     // 读取超时
//                        .writeTimeout(60, TimeUnit.SECONDS)    // 写入超时
//                        .build();
//            }
//            String currentToken = SysUserHolder.getCurrentToken();
//            // 2. 构建请求体 (表单格式)
////            FormBody formBody = new FormBody.Builder()
////                    .add("pid", dto.getPid())
////                    .add("time", dto.getTime())
////                    .build();
//            String json = "{}";
//            if (params != null) {
//                json = params.toJSONString();
//            }
//            // 2. 构建请求体 (body-JSON格式)
//            RequestBody jsonBody = RequestBody.create(
//                    MediaType.parse("application/json; charset=utf-8"),
//                    json
//            );
//            // 3. 创建请求对象
//            log.info("**tm4数据源:" + url + ",参数:" + json);
//            //log.info("**pid:"+dto.getPid());
//            //log.info("**time:"+dto.getTime());
//            Request request = new Request.Builder()
//                    .url(url)
//                    .post(jsonBody) // 设置为 POST 请求
//                    .addHeader("Authorization", "Bearer " + currentToken == null ? "" : currentToken)
//                    .build();
//            //Request request = new Request.Builder().url(url).build();
//            try (Response response = client.newCall(request).execute()) {
//                if (response.isSuccessful() && response.body() != null) {
//                    jsonData = response.body().string().trim();
//                } else {
//                    String s = "[" + url + "],请求失败，状态码: " + response.code();
//                    this.setErrorInfo(s);
//                    log.error(s);
//                }
//            } catch (Exception e) {
//                String s = "[" + url + "]请求失败," + e.getMessage();
//                this.setErrorInfo(s);
//                log.error(url, e);
//            }
//        } catch (Exception ex) {
//            String s = "[" + url + "]请求失败," + ex.getMessage();
//            this.setErrorInfo(s);
//            log.error(url, ex);
//        }
            return jsonData;
        }


        /**
         * 更新数据<br>
         * 该类不需要更新数据
         *
         * @category 更新数据
         */
        public Boolean update () {
            return false;
        }

        /**
         * 初始化数据源必需信息
         *
         * @param initInfoObj
         * @category 初始化数据源必需信息
         */
        public void init (Object initInfoObj){
            setDataSourceId(TMUID.getUID());
            setAutoLoad(false);
        }
    }
