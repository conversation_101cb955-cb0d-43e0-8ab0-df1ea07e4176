package com.yunhesoft.system.tds.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.applyConf.entity.dto.ApplyParams;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormMeter;
import com.yunhesoft.system.applyConf.service.IApplyConfService;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.utils.mongodb.service.MongoDBService;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.tds.entity.dto.LimsDataDto;
import com.yunhesoft.system.tds.entity.dto.TdsAccountDto;
import com.yunhesoft.system.tds.entity.dto.TdsQueryDto;
import com.yunhesoft.system.tds.entity.dto.TdsTagDto;
import com.yunhesoft.system.tds.entity.po.*;
import com.yunhesoft.system.tds.entity.vo.LimsDataVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountCountConfVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountOutparamVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountParamVo;
import com.yunhesoft.system.tds.service.IDataSourceAccountService;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tds.service.IRtdbService;
import com.yunhesoft.system.tds.service.impl.DataAccountTools;
import lombok.extern.log4j.Log4j2;

import java.sql.CallableStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

//@Service
@Log4j2
public class TDSAccount extends ADataSource {

	private int tdsOverTime = 60; // 加载数据库超时时间
	private boolean iso = false; // 是否为oracle数据库

	private EntityService srv = SpringUtils.getBean(EntityService.class);
	private IDataSourceAccountService accountServ = SpringUtils.getBean(IDataSourceAccountService.class);
	private IRtdbService rtdbSrv = SpringUtils.getBean(IRtdbService.class);
	private MongoDBService mongoDBServ = SpringUtils.getBean(MongoDBService.class);
	private IApplyConfService applySrv = SpringUtils.getBean(IApplyConfService.class);
	private RedisUtil redis = SpringUtils.getBean(RedisUtil.class);
	private IShiftService shiftService = SpringUtils.getBean(IShiftService.class);

	private IDataSourceService tdsServ = SpringUtils.getBean(IDataSourceService.class);
	private ISysEmployeeInfoService sysEmployeeInfoService = SpringUtils.getBean(ISysEmployeeInfoService.class);

	// 自定义变量
	private final String accountKey = "TDS:ACCOUUNT:TITLE";//
	private final String timeCol = "timeMarkCol";// 时间列
	private final String infoCol = "_accountInfoCol";// 信息列
	private final String typeCol = "_accountTypeCol";// 类别列
	private final String ulinfoCol = "_accountULInfoCol";// 台账信息列，超限颜色、上下限
	private final String editMark = "_editMark";// 行编辑标识，1可编辑 0不可编辑
	private final String isInitMark = "TDSROW_rowIsChange";// 初始化标识，0 添加、1修改
	private Map<Date, String> isInitIdMap = null; // 是否是初始化的数据
	// unitCode@ZR4M1JSK802OUF47ZP0231;sendUnitCode@ZR4M1JSK802OUF47ZP0231;bc@ZQWV03UIX01BPF706Q0365;sj@2025-04-30
	// 06:00:00,2025-04-30 10:00:00;rq@2025-04-30;accountId@null;formDataId@null
	private String infoContent = "";// 信息列内容 unitCode:xxxxx,xxxxx;sj:yyyy-mm-dd hh:mm:ss,yyyy-mm-dd hh:mm:ss
	private Map<String, String> ulContentMap = null;// 上下限内容 key--"yyyy-MM-dd HH:mm:dd"
													// value字符串--{"ID_COL":{"tip":"下限~上限","up":"上限","low":"下限","up2":"临界上限","low2":"临界下限"},
													// "ID_COL":{"tip":"≥下限","up":"上限","low":"下限","up2":"临界上限","low2":"临界下限"},...}}
	private Map<String, Map<String, String>> colULMap = null;
	private Map<String, String> markMap = null;// 备注信息
	private Map<String, Map<String, String>> colTitleMap = null;// 表头信息
	private final String tagCol = "tagMarkCol";
	private final String sameSuf = "_附加";
	private final String samePre = "sameadd_";
	private final String accountTabName = "accountTab";
	private final String accountNewTab = "ac";
	private static String PK = "ID"; // 主键列字段名称
	private SysUser user;
	// 配置信息
	private TdsAccountConf conf; // 台账配置信息
	private List<TdsAccountMeter> mlist; // 台账仪表配置列表 采集点配置
	private Map<String, String> meterIdMap = null; // 仪表ID映射
	private TdsAccountTime timeConf; // 时间配置信息-台账时间配置表
	private List<TdsAccountOutparamVo> plist; // 输出设置参数列表
	private String confCode = null; // unitcode 核算对象ID 、 台账模型ID、：account_obj_id
	private String sendUnitCode = null; // unitcode 核算对象ID 、 台账模型ID、：account_obj_id
	private String version = null; // 版本、日期 2025-04-16
	private Long versionLong = null; // 版本长整型 日期Long
	private Integer mode = 1;// 仪表查询范围 1核算对象 2机构
	private Map<Date, String> sjmap = null;// 时间列表信息 Date -> 2025-04-16 00:00 （前端传过来的,已经录入的记录）
	// private Map<Date, String> tagmap = null;//仪表列表信息
	private String st = null; // 开始时间 2025-04-16 00:00 上班时间 （前端传过来的,已经录入的记录）
	private String st2 = null; // 开始时间 2025-04-16 00:00 上班时间 （前端传过来的,已经录入的记录）
	private String et = null; // 结束时间 2025-04-16 00:00 下班时间 （前端传过来的,已经录入的记录）
	private String et2 = null; // 结束时间 2025-04-16 00:00 下班时间 （前端传过来的,已经录入的记录）
	private int istep = 60; // 时间间隔 60分钟
	private List<Date> timeList = new ArrayList<Date>(); // 时间列表信息 Date -> 2025-04-16 00:00 （前端传过来的,已经录入的）
	private List<Date> timeList2 = new ArrayList<Date>(); // 时间列表信息 Date -> 2025-04-16 00:00 （前端传过来的,已经录入的）
	private Map<String, Integer> tagFormat = null; // 仪表格式化信息
	private Map<String, List<Double>> ulmap = new HashMap<String, List<Double>>(); // 上下限信息
	// private String searchMode = null;//数据提取模式，tagData实时仪表数据， 默认为保存数据和提取数据，以保存数据为主
	private List<String> saveDataList = null; // String key =
												// data.getApplyTimeStr()台账仪表更新信息:应用时间+"_"+data.getTagId();仪表id
	private String timeBoundDay = ""; // 时间范围 2025-04-16 00:00
	private String shiftCode = null; // 班次编码 1班
	private String timeBoundBc1 = ""; // 班次开始上班时间 （前端传过来的）
	private String timeBoundBc2 = ""; // 班次结束下班时间 （前端传过来的）
	private String noBound = "/"; // 无数据
	private String noConfirm = " "; // 无确认
	private String dataId = null; // 数据ID
	private String accountId = null; // 台账ID
	private Map<String, Map> mongoMap = null; // mongodb数据
	private int confirmStep = 0;// 0是每个时间点确认一次
	private List<Date> noConfirmList = null; // 无确认列表

	private Map<String, TdsAccountMeter> tagIdObjMap = null; // 仪表ID映射-台账仪表配置对象
	private Map<String, String> tagCodeIdMap = null; // 仪表编码映射 PIA7023A - **********************
	private List<TdsAccountCountConfVo> countList = null; // 汇总统计结果
	private Map<String, String> countMap = null;// 汇总统计结果
	private Map<String, List<Map<String, Object>>> tagFaULmap = null; // 仪表对应方案的时间范围和上下限信息
	private Boolean useFa = true;// 是否使用方案上下限
	private String tagType = "pwl";// pwl 平稳率 lims 分析
	private List<String> tagIdList = null; // 仪表ID列表
	private Map<String, Map<String, String>> colInfoMap = null; // 查询明细数据 tagid -> 台账信息
	private Map<Date, String> infoTimeIdMap = null; // 时间、核算对象 unitcode 映射 （前端传过来的,已经录入的）

	private Map<Date, String> jobInputTimeIdMap = null; // 时间ID映射 （前端传过来的,已经录入的）
	private Map<String, Map<Date, String>> jobInputTimeList = null;
	private final String jobInputTimeCol="_jobInputTime";

	private Map<Date, String> infoTimeEditMap = null; // 时间编辑列editmark映射 （前端传过来的,已经录入的）
	private Map<String, String> txtMap = new HashMap<String, String>(); // 台账信息:ZR4NOBT5B08SOE68791025_2025-04-29
																		// 11:00:00 -> null
	private List<Map> newdata = null;// 有无新版数据
	private Boolean haveMongoDbData = false;// 有无mongodb数据
	private List<String> showTimeList = new ArrayList<String>();// 显示时间点列表 （前端传过来的,已经录入的记录）
	private int showTimeNum = 0; // 显示时间点数量
	private List<String> confirmTimeList = new ArrayList<String>(); // 确认时间点列表
	// private Boolean haveFa = false;//是否使用了方案，未使用方案，按原仪表上下限显示；有方案，表头列显示第一个时间点的上下限
	private List<String> fasjlist = null;// 各时间点方案变化信息
	private String reTime = null; // 重新加载时间
	private String inputMode = null; // 输入模式
	private Boolean isOutDataMode = false;// 仪表是否使用外部数据进行存储
	private Boolean mobileSaveStatus = false;// 移动端是否需要保存数据得状态，如果true，数据加载完成后，调用接口保存数据
	private String ledgerModuleId = null;// 台账模型ID
//	private String formDataId = null;// 表格数据ID

	private String initMode = null;// 初始化模式
	private String ledgerFormId = null;// 表单ID

	private String orgcode = null;// 传入的机构编码
	private Map<String, String> defaultMap = new HashMap<>();
	private Map<String, Integer> dotSort;
	private Map<String, String> dotName;
	//用于暂时存储所有采集点的显示属性
	private Map<String, JSONObject> dotShowProperty = new HashMap<>();
	private Boolean isInit = false;// 是否时间初始化

	private Boolean isExand = false; // 是否扩展行
	private String inparamAliasFront = "";
	private Map<String, String> defaultTextValMap = new HashMap<>();// 设备默认值
	private Map<String, String> isWriteInputIdMap = new HashMap<>();//
	private Map<String, String> defaultvalsIdMap = new HashMap<>();//
	private Map<String, String> multiSelectDisplayModeIdMap = new HashMap<>();//
	private Map<String, String> copyAddDefaultModeIdMap = new HashMap<>();// 复制新增默认值模式映射

	// 默认值处理模式：INPUT-录入模式（使用当前登录人信息），QUERY-查询模式（只显示已保存数据）
	private String defaultValueMode = "INPUT";

	/**
	 * 获取有效的机构编码
	 * 优先使用传入的orgcode参数，如果为空则使用当前用户的机构编码
	 * @return 有效的机构编码
	 */
	private String getEffectiveOrgCode() {
		if (StringUtils.isNotEmpty(this.orgcode)) {
			return this.orgcode;
		}
		return user == null ? SysUserHolder.getCurrentUser().getOrgId() : user.getOrgId();
	}

	/**
	 * 设置默认值处理模式
	 * @param mode INPUT-录入模式（使用当前登录人信息），QUERY-查询模式（只显示已保存数据）
	 */
	public void setDefaultValueMode(String mode) {
		this.defaultValueMode = mode;
	}

	/**
	 * 获取当前默认值处理模式
	 * @return 当前的默认值处理模式
	 */
	public String getDefaultValueMode() {
		return this.defaultValueMode;
	}

	/**
	 * 自动检测默认值处理模式
	 * 根据传入参数和用户权限自动判断是录入模式还是查询模式
	 */
	private void autoDetectDefaultValueMode() {
		try {
			// 检查是否有明确的查询模式标识参数
			String queryMode = this.getInParaValue("queryMode");
			if ("true".equals(queryMode) || "1".equals(queryMode)) {
				this.defaultValueMode = "QUERY";
				log.info("TDSAccount: 检测到查询模式参数，设置为QUERY模式");
				return;
			}

			// 默认为录入模式
			this.defaultValueMode = "INPUT";
			log.info("TDSAccount: 默认设置为INPUT模式");

		} catch (Exception e) {
			log.warn("TDSAccount: 自动检测默认值模式失败，使用默认INPUT模式: {}", e.getMessage());
			this.defaultValueMode = "INPUT";
		}
	}

	@Override
	public Boolean update() {
		return true;// false
	}

	@Override
	public void init(Object initInfoObj) {
		setDataSourceId(TMUID.getUID());
	}

	@Override
	public void load() {
		// this.srv = SpringUtils.getBean(EntityService.class);
		// this.accountServ = SpringUtils.getBean(IDataSourceAccountService.class);
		// this.httpSrv = SpringUtils.getBean(HttpClientService.class);
		// this.sysConfigSrv = SpringUtils.getBean(ISysConfigService.class);
		// this.rtdbSrv = SpringUtils.getBean(IRtdbService.class);
		this.load(1, 0);
	}

	@Override
	public void load(int page, int pageSize) {
		long loadStartTime = System.currentTimeMillis();
		log.info("TDSAccount.load: 开始执行load方法，数据源别名: {}", getDSAlias());

		user = SysUserHolder.getCurrentUser();

		// 自动判断默认值处理模式
		autoDetectDefaultValueMode();

		TPageInfo pageInfo = new TPageInfo(); // 分页信息
		pageInfo.setCurrPage(page);
		pageInfo.setPageSize(pageSize);
		this.getRsCols().clear();

		// 1. 配置读取
//		long confStartTime = System.currentTimeMillis();
		getConf();// 配置读取完成后，判断是否保存台账数据，未保存进行保存
//		long confEndTime = System.currentTimeMillis();
//		log.info("TDSAccount.load: getConf()执行耗时: {}ms", (confEndTime - confStartTime));

		// 2. 获取默认值
//		long defaultValueStartTime = System.currentTimeMillis();
		this.getDefaultValue();
//		long defaultValueEndTime = System.currentTimeMillis();
//		log.info("TDSAccount.load: getDefaultValue()执行耗时: {}ms", (defaultValueEndTime - defaultValueStartTime));

		// 3. 删除MongoDB时间数据（如果需要）
		if (StringUtils.isNotEmpty(this.reTime)) {
//			long deleteStartTime = System.currentTimeMillis();
			deleteMongoDbTimeData();
//			long deleteEndTime = System.currentTimeMillis();
//			log.info("TDSAccount.load: deleteMongoDbTimeData()执行耗时: {}ms", (deleteEndTime - deleteStartTime));
		}

		// 4. 获取已保存数据
//		long mongoDataStartTime = System.currentTimeMillis();
		Map<String, Map<Date, String>> dataList = new HashMap<>();
		dataList = getMongoDBData();
//		long mongoDataEndTime = System.currentTimeMillis();
//		log.info("TDSAccount.load: getMongoDBData()执行耗时: {}ms，获取数据条数: {}",
//			(mongoDataEndTime - mongoDataStartTime), dataList.size());

		// 5. 获取外部数据（同步模式）
		if ("synchronize".equals(timeConf.getShowMode())) {
//			long mobileDataStartTime = System.currentTimeMillis();
			Map<String, Map<Date, String>> tlist = new HashMap<String, Map<Date, String>>();
			getMobileData(tlist);
			if (tlist.size() > 0) {
				dataList.putAll(tlist); // 录入的实时数据
			}
//			long mobileDataEndTime = System.currentTimeMillis();
//			log.info("TDSAccount.load: getMobileData()执行耗时: {}ms，获取数据条数: {}",
//				(mobileDataEndTime - mobileDataStartTime), tlist.size());
		}
		// 如果没有数据，默认未初次加载，根据默认配置加载初始化数据（注：行状态需要调整，保证初次保存时，保存所有数据）
		// if((dataList==null || dataList.size() == 0) &&
		// StringUtils.isNotEmpty(initMode)) {
		// //判断默认方式并填充数据（时间，数值）
		// if("".equals(initMode)) {

		// }
		// }

		if("INPUT".equals(this.defaultValueMode)){
// 6. 初始化数据处理
			if (StringUtils.isNotEmpty(initMode) && StringUtils.isNotEmpty(ledgerFormId) && StringUtils.isNotEmpty(ledgerModuleId)) {
				long initStartTime = System.currentTimeMillis();
				log.info("TDSAccount.load: 开始初始化数据处理，初始化模式: {}", initMode);

				Date now = DateTimeUtils.getNowDate();
				if ("time".equals(initMode)) {// 时间初始化模式（默认加载初始化时间记录）
					long timeInitStartTime = System.currentTimeMillis();
					String sql = "select START_TIME_TYPE,START_TIME,END_TIME_TYPE,END_TIME,TIME_INTERVAL,TIME_INTERVAL_TYPE from DIGITAL_LEDGER_TIME where TMUSED=1 "
							+ "and LEDGER_ID in (select LEDGER_MODULE_ID from DIGITAL_LEDGER where TMUSED=1 and INIT_TYPE='"
							+ initMode + "' and COMPONENT_ID='" + ledgerFormId + "' and LEDGER_MODULE_ID='" + ledgerModuleId + "' )";
					List<Map<String, Object>> _list = srv.queryListMap(sql);
					if (StringUtils.isNotEmpty(_list) && _list.size() > 0) {
						isInit = true;
						// 初始化isInitIdMap
						if (this.isInitIdMap == null) {
							this.isInitIdMap = new LinkedHashMap<>();
						}
						// 检查现有数据，只初始化未保存的时间点
						if (StringUtils.isEmpty(dataList)) {
							long ledgerTimeDataStartTime = System.currentTimeMillis();
							dataList = this.initLedgerTimeData(_list.get(0));
							long ledgerTimeDataEndTime = System.currentTimeMillis();
							log.info("TDSAccount.load: initLedgerTimeData()执行耗时: {}ms",
									(ledgerTimeDataEndTime - ledgerTimeDataStartTime));

							// 标记所有初始化的时间点
							if (dataList != null && !dataList.isEmpty()) {
								String firstTagId = dataList.keySet().iterator().next();
								Map<Date, String> tagData = dataList.get(firstTagId);
								if (tagData != null) {
									for (Date timePoint : tagData.keySet()) {
										this.isInitIdMap.put(timePoint, "true");
									}
								}
							}
						} else {
							// 合并现有数据和新初始化的时间点数据
							long mergeStartTime = System.currentTimeMillis();
							Map<String, Map<Date, String>> newTimeData = this.initLedgerTimeData(_list.get(0));
							// 对于每个仪表，检查哪些时间点没有数据
							for (String tagId : newTimeData.keySet()) {
								Map<Date, String> newData = newTimeData.get(tagId);
								Map<Date, String> existingData = dataList.computeIfAbsent(tagId, k -> new HashMap<>());
								Map<Date, String> existingData2 = this.jobInputTimeList.computeIfAbsent(tagId, k -> new LinkedHashMap<>());

								// 对于已存在的仪表，只添加不存在的时间点
								for (Date timePoint : newData.keySet()) {
									if (!existingData.containsKey(timePoint)) {
										existingData.put(timePoint, newData.get(timePoint));
										// 标记为初始化数据
										this.isInitIdMap.put(timePoint, "true");
									}
									if (!existingData2.containsKey(timePoint)) {
										existingData2.put(timePoint, DateTimeUtils.formatDateTime(timePoint));
									}
								}
							}
							long mergeEndTime = System.currentTimeMillis();
							log.info("TDSAccount.load: 时间初始化数据合并耗时: {}ms", (mergeEndTime - mergeStartTime));
						}
					} else {
						isInit = true;
						// 初始化isInitIdMap
						if (this.isInitIdMap == null) {
							this.isInitIdMap = new LinkedHashMap<>();
						}
						if (StringUtils.isEmpty(dataList)) {
							dataList = this.initLedgerTimeData(new HashMap<>());
							// 标记所有初始化的时间点
							if (dataList != null && !dataList.isEmpty()) {
								String firstTagId = dataList.keySet().iterator().next();
								Map<Date, String> tagData = dataList.get(firstTagId);
								if (tagData != null) {
									for (Date timePoint : tagData.keySet()) {
										this.isInitIdMap.put(timePoint, "true");
									}
								}
							}
						} else {
							// 合并现有数据和新初始化的时间点数据
							Map<String, Map<Date, String>> newTimeData = this.initLedgerTimeData(new HashMap<>());
							// 对于每个仪表，检查哪些时间点没有数据
							for (String tagId : newTimeData.keySet()) {
								Map<Date, String> newData = newTimeData.get(tagId);
								Map<Date, String> existingData = dataList.computeIfAbsent(tagId, k -> new HashMap<>());
								Map<Date, String> existingData2 = this.jobInputTimeList.computeIfAbsent(tagId, k -> new LinkedHashMap<>());
								// 对于已存在的仪表，只添加不存在的时间点
								for (Date timePoint : newData.keySet()) {
									if (!existingData.containsKey(timePoint)) {
										existingData.put(timePoint, newData.get(timePoint));
										// 标记为初始化数据
										this.isInitIdMap.put(timePoint, "true");
									}
									if (!existingData2.containsKey(timePoint)) {
										existingData2.put(timePoint, DateTimeUtils.formatDateTime(timePoint));
									}
								}
							}
						}
					}

					// 初始化、已保存、新初始化时间列表
					if (dataList != null && !dataList.isEmpty()) {
						// 初始化时间编辑映射和时间显示映射
						this.infoTimeIdMap = new LinkedHashMap<>();
						this.infoTimeEditMap = new LinkedHashMap<>();
						this.sjmap = new LinkedHashMap<>();

						// 获取第一个tagId的所有时间点
						List<Date> allTimePoints = new ArrayList<>();

						// 只获取第一个tagId的时间点
						String firstTagId = dataList.keySet().iterator().next();
						Map<Date, String> tagData = dataList.get(firstTagId);
						if (tagData != null) {
							allTimePoints.addAll(tagData.keySet());
						}

						// 按时间正序排序
						Collections.sort(allTimePoints);

						// 清空并重新构建各个时间相关Map
						this.timeList.clear();
						this.timeList.addAll(allTimePoints);
//					infoTimeIdMap.clear();
//					infoTimeEditMap.clear();
//					sjmap.clear();
						showTimeList.clear();
//					String unitCode = StringUtils.isNotEmpty(this.confCode) ? this.confCode : this.accountId;
						// 更新其他相关映射
						for (Date timePoint : allTimePoints) {
							infoTimeIdMap.put(timePoint, this.confCode);
							infoTimeEditMap.put(timePoint, "1");
							sjmap.put(timePoint, DateTimeUtils.format(timePoint, this.timeConf.getTimeFormat()));
							// 如果isInitIdMap中没有这个时间点的标识，则设置为非初始化数据
//						if (this.isInitIdMap != null && !this.isInitIdMap.containsKey(timePoint)) {
//							this.isInitIdMap.put(timePoint, "false");
//						}

							// 当前时间点
							if (DateTimeUtils.bjDate(now, timePoint) == 1) {
								showTimeList.add(DateTimeUtils.formatDateTime(timePoint));
							}
						}

						// 设置开始和结束时间
						this.st = this.timeList.isEmpty() ? null : this.timeList.get(0).toString();
						this.et = this.timeList.isEmpty() ? null : this.timeList.get(this.timeList.size() - 1).toString();
					}

					if (jobInputTimeList != null && !jobInputTimeList.isEmpty()) {
						// 初始化时间编辑映射和时间显示映射
						if(this.jobInputTimeIdMap==null){
							this.jobInputTimeIdMap =new LinkedHashMap<>();
						}

						// 获取第一个tagId的所有时间点
						List<Date> allTimePoints2 = new ArrayList<>();

						// 只获取第一个tagId的时间点
						String firstTagId = jobInputTimeList.keySet().iterator().next();
						Map<Date, String> tagData = jobInputTimeList.get(firstTagId);
						if (tagData != null) {
							allTimePoints2.addAll(tagData.keySet());
						}

						// 按时间正序排序
						Collections.sort(allTimePoints2);
						// 清空并重新构建各个时间相关Map
						this.timeList2.clear();
						this.timeList2.addAll(allTimePoints2);

						// 更新其他相关映射
						for (Date timePoint : allTimePoints2) {
							if (tagData != null) {
								jobInputTimeIdMap.put(timePoint, tagData.get(timePoint));
							} else {
								jobInputTimeIdMap.put(timePoint, DateTimeUtils.formatDateTime(timePoint));
							}
						}
						// 设置开始和结束时间
						this.st2 = this.timeList2.isEmpty() ? null : this.timeList2.get(0).toString();
						this.et2 = this.timeList2.isEmpty() ? null : this.timeList2.get(this.timeList2.size() - 1).toString();
					}

				} else if ("expand".equals(initMode)) {// 数据源初始化模式（默认加载）
					long expandInitStartTime = System.currentTimeMillis();
					String sql = "select COLLECTION_POINT_ID,DATA_SOURCE_ALIAS,DATA_SOURCE_FIELD,DATA_SOURCE_PARA_ALIAS,DATA_SOURCE_PARA_NAME from DIGITAL_LEDGER_EXTEND_ROW where TMUSED=1 "
							+ "and LEDGER_ID in (select LEDGER_MODULE_ID from DIGITAL_LEDGER where TMUSED=1 and INIT_TYPE='"
							+ initMode + "' and COMPONENT_ID='" + ledgerFormId + "' and LEDGER_MODULE_ID='" + ledgerModuleId + "' )";
					List<Map<String, Object>> _list = srv.queryListMap(sql);
					if (StringUtils.isNotEmpty(_list)) {
						isExand = true;
						// 初始化isInitIdMap
						if (this.isInitIdMap == null) {
							this.isInitIdMap = new LinkedHashMap<>();
						}
						if (dataList.isEmpty()) {
							long excendRowDataStartTime = System.currentTimeMillis();
							dataList = this.initLedgerExcendRowData(_list);
							long excendRowDataEndTime = System.currentTimeMillis();
							log.info("TDSAccount.load: initLedgerExcendRowData()执行耗时: {}ms",
									(excendRowDataEndTime - excendRowDataStartTime));

							// 标记所有初始化的时间点
							if (dataList != null && !dataList.isEmpty()) {
								String firstTagId = dataList.keySet().iterator().next();
								Map<Date, String> tagData = dataList.get(firstTagId);
								if (tagData != null) {
									for (Date timePoint : tagData.keySet()) {
										this.isInitIdMap.put(timePoint, "true");
									}
								}
							}
						} else {
							// 合并现有数据和新初始化的扩展行数据
							long mergeExpandStartTime = System.currentTimeMillis();
							Map<String, Map<Date, String>> newRowData = this.initLedgerExcendRowData(_list);
							// 对于每个仪表，检查哪些时间点没有数据
							for (String tagId : newRowData.keySet()) {
								Map<Date, String> newData = newRowData.get(tagId);
								Map<Date, String> existingData = dataList.computeIfAbsent(tagId, k -> new HashMap<>());
								Map<Date, String> existingData2 = this.jobInputTimeList.computeIfAbsent(tagId, k -> new LinkedHashMap<>());

								// 对于已存在的仪表，只添加不存在的时间点，且值不为空
								for (Date timePoint : newData.keySet()) {
									String newValue = newData.get(timePoint);
									// 修复：只有当新值不为空且不是空字符串时才添加，避免空行
									if (!existingData.containsKey(timePoint) && StringUtils.isNotEmpty(newValue)) {
										existingData.put(timePoint, newValue);
										// 标记为初始化数据
										this.isInitIdMap.put(timePoint, "true");
									}
									// 只有当有有效数据时才添加时间点到jobInputTimeList
									if (!existingData2.containsKey(timePoint) && StringUtils.isNotEmpty(newValue)) {
										existingData2.put(timePoint, DateTimeUtils.formatDateTime(timePoint));
									}
								}
							}
							long mergeExpandEndTime = System.currentTimeMillis();
							log.info("TDSAccount.load: 扩展行数据合并耗时: {}ms", (mergeExpandEndTime - mergeExpandStartTime));
						}
					}
					long expandInitEndTime = System.currentTimeMillis();
					log.info("TDSAccount.load: 扩展行初始化总耗时: {}ms", (expandInitEndTime - expandInitStartTime));

					// 初始化、已保存、新初始化时间列表
					if (dataList != null && !dataList.isEmpty()) {
						// 初始化时间编辑映射和时间显示映射
						this.infoTimeIdMap = new LinkedHashMap<>();
						this.infoTimeEditMap = new LinkedHashMap<>();
						this.sjmap = new LinkedHashMap<>();

						// 获取第一个tagId的所有时间点
						List<Date> allTimePoints = new ArrayList<>();

						// 只获取第一个tagId的时间点
						String firstTagId = dataList.keySet().iterator().next();
						Map<Date, String> tagData = dataList.get(firstTagId);
						if (tagData != null) {
							allTimePoints.addAll(tagData.keySet());
						}

						// 按时间正序排序
						Collections.sort(allTimePoints);

						// 清空并重新构建各个时间相关Map
						this.timeList.clear();
						this.timeList.addAll(allTimePoints);
//
						showTimeList.clear();
						// 更新其他相关映射
						for (Date timePoint : allTimePoints) {
							infoTimeIdMap.put(timePoint, this.confCode);
							infoTimeEditMap.put(timePoint, "1");
							sjmap.put(timePoint, DateTimeUtils.format(timePoint, this.timeConf.getTimeFormat()));
							// 如果isInitIdMap中没有这个时间点的标识，则设置为非初始化数据
//						if (this.isInitIdMap != null && !this.isInitIdMap.containsKey(timePoint)) {
//							this.isInitIdMap.put(timePoint, "false");
//						}

							// 当前时间点
							if (DateTimeUtils.bjDate(now, timePoint) == 1) {
								showTimeList.add(DateTimeUtils.formatDateTime(timePoint));
							}
						}

						// 设置开始和结束时间
						this.st = this.timeList.isEmpty() ? null : this.timeList.get(0).toString();
						this.et = this.timeList.isEmpty() ? null : this.timeList.get(this.timeList.size() - 1).toString();
					}

					if (jobInputTimeList != null && !jobInputTimeList.isEmpty()) {
						// 初始化时间编辑映射和时间显示映射
						if(this.jobInputTimeIdMap==null){
							this.jobInputTimeIdMap =new LinkedHashMap<>();
						}

						// 获取第一个tagId的所有时间点
						List<Date> allTimePoints2 = new ArrayList<>();

						// 只获取第一个tagId的时间点
						String firstTagId = jobInputTimeList.keySet().iterator().next();
						Map<Date, String> tagData = jobInputTimeList.get(firstTagId);
						if (tagData != null) {
							allTimePoints2.addAll(tagData.keySet());
						}

						// 按时间正序排序
						Collections.sort(allTimePoints2);
						// 清空并重新构建各个时间相关Map
						this.timeList2.clear();
						this.timeList2.addAll(allTimePoints2);

						// 更新其他相关映射
						for (Date timePoint : allTimePoints2) {
							if (tagData != null) {
								jobInputTimeIdMap.put(timePoint, tagData.get(timePoint));
							} else {
								jobInputTimeIdMap.put(timePoint, DateTimeUtils.formatDateTime(timePoint));
							}
						}
						// 设置开始和结束时间
						this.st2 = this.timeList2.isEmpty() ? null : this.timeList2.get(0).toString();
						this.et2 = this.timeList2.isEmpty() ? null : this.timeList2.get(this.timeList2.size() - 1).toString();
					}

					// 处理默认值逻辑：对于每个采集点，如果没有扩展行值，但有默认值，则使用默认值
					if (dataList != null && !dataList.isEmpty() && !defaultMap.isEmpty()) {
						// 遍历所有采集点
						for (String tagId : defaultMap.keySet()) {
							// 获取采集点的默认值数据
							String defaultVal = defaultMap.get(tagId);
							if (StringUtils.isEmpty(defaultVal)) {
								continue; // 如果没有默认值，则跳过
							}

							// 使用统一的默认值处理方法
							defaultVal = processDefaultValue(tagId, defaultVal, this.defaultValueMode);

							// 获取或创建此采集点的时间点数据映射
							Map<Date, String> tagTimeData = dataList.computeIfAbsent(tagId, k -> new HashMap<>());

							// 对于每个时间点，如果没有扩展数据源数据，则使用默认值
							if (!timeList.isEmpty()) {
								for (Date timePoint : timeList) {
									if (!tagTimeData.containsKey(timePoint)
											|| StringUtils.isEmpty(tagTimeData.get(timePoint))) {
										tagTimeData.put(timePoint, defaultVal);
									}
								}
							}
						}
					}

				}
			}

			// long t3 = System.currentTimeMillis();
			// //获取关系数据库数据 分级获取暂时屏蔽 TODO
			// if(!"bound".equals(this.timeConf.getShowMode())) {//不是时间段
			// dataList = getRelationData(dataList);
			// }
			// 7. 获取实时数据（只在非扩展模式下）
			if (!this.isExand && this.isInit) {
				long realTimeDataStartTime = System.currentTimeMillis();
				log.info("TDSAccount.load: 开始获取实时数据处理");

				if (dataList != null && dataList.size() > 0) {
					try {
						long dataSeparationStartTime = System.currentTimeMillis();
						// 分离已保存数据和未保存的初始化数据
						Map<String, Map<Date, String>> savedData = new LinkedHashMap<>();
						Map<String, Map<Date, String>> unsavedData = new LinkedHashMap<>();
						int savedCount = 0;
						int unsavedCount = 0;

						// 检查每个仪表的数据
						for (String tagId : dataList.keySet()) {
							Map<Date, String> timeValues = dataList.get(tagId);
							Map<Date, String> savedTimeValues = new LinkedHashMap<>();
							Map<Date, String> unsavedTimeValues = new LinkedHashMap<>();

							// 检查每个时间点的数据
							for (Date timePoint : timeValues.keySet()) {
								String value = timeValues.get(timePoint);
								// 改进值判断逻辑：null、空字符串和特殊标记（noBound="/"、noConfirm=" "）视为未保存数据
								if (value != null && !value.isEmpty() && !noBound.equals(value)
										&& !noConfirm.equals(value)) {
									// 有值的数据认为是已保存的数据
									savedTimeValues.put(timePoint, value);
									savedCount++;
								} else {
									// 无值的数据认为是未保存的初始化数据
									unsavedTimeValues.put(timePoint, value);
									unsavedCount++;
								}
							}

							// 如果有已保存数据，放入savedData
							if (!savedTimeValues.isEmpty()) {
								savedData.put(tagId, savedTimeValues);
							}

							// 如果有未保存数据，放入unsavedData
							if (!unsavedTimeValues.isEmpty()) {
								unsavedData.put(tagId, unsavedTimeValues);
							}
						}
						long dataSeparationEndTime = System.currentTimeMillis();
						log.info("TDSAccount.load: 数据分离耗时: {}ms，已保存数据点: {}, 未保存数据点: {}",
								(dataSeparationEndTime - dataSeparationStartTime), savedCount, unsavedCount);

						// 只对未保存的初始化数据获取实时数据
						Map<String, Map<Date, String>> realTimeData = null;
						if (!unsavedData.isEmpty()) {
							long getDataStartTime = System.currentTimeMillis();
							log.info("TDSAccount.load: 开始获取未保存数据的实时值，仪表数: {}", unsavedData.size());
							realTimeData = getData(unsavedData);
							long getDataEndTime = System.currentTimeMillis();
							log.info("TDSAccount.load: getData()执行耗时: {}ms", (getDataEndTime - getDataStartTime));
						}

						// 合并已保存数据和获取到的实时数据
						if (realTimeData != null && !realTimeData.isEmpty()) {
							long mergeStartTime = System.currentTimeMillis();
							int mergedCount = 0;
							for (String tagId : realTimeData.keySet()) {
								Map<Date, String> timeValues = realTimeData.get(tagId);
								if (savedData.containsKey(tagId)) {
									// 如果已保存数据中有该仪表，则合并时间点数据
									Map<Date, String> savedTimeValues = savedData.get(tagId);
									for (Date timePoint : timeValues.keySet()) {
										if (!savedTimeValues.containsKey(timePoint)) {
											savedTimeValues.put(timePoint, timeValues.get(timePoint));
											mergedCount++;
										}
									}
								} else {
									// 如果已保存数据中没有该仪表，则直接添加
									savedData.put(tagId, timeValues);
									mergedCount += timeValues.size();
								}
							}
							long mergeEndTime = System.currentTimeMillis();
							log.info("TDSAccount.load: 实时数据合并耗时: {}ms，新增数据点: {}",
									(mergeEndTime - mergeStartTime), mergedCount);
						}

						// 更新dataList为合并后的数据
						dataList = savedData;
					} catch (Exception e) {
						log.error("TDSAccount.load: 处理数据分离与合并时发生错误: {}", e.getMessage(), e);
						// 出错时退回到原始逻辑，确保功能不中断
						long fallbackStartTime = System.currentTimeMillis();
						dataList = getData(dataList);
						long fallbackEndTime = System.currentTimeMillis();
						log.info("TDSAccount.load: 异常回退getData()执行耗时: {}ms", (fallbackEndTime - fallbackStartTime));
					}
				} else {// 提取数据判断，重新提取覆盖原数据
					// 重新提取操作
					long reExtractStartTime = System.currentTimeMillis();
					dataList = getData(null);
					long reExtractEndTime = System.currentTimeMillis();
					log.info("TDSAccount.load: 重新提取getData()执行耗时: {}ms", (reExtractEndTime - reExtractStartTime));
				}
				long realTimeDataEndTime = System.currentTimeMillis();
				log.info("TDSAccount.load: 实时数据处理总耗时: {}ms", (realTimeDataEndTime - realTimeDataStartTime));
			}
		}

		// 8. 封装输出列
//		long encOutParamsStartTime = System.currentTimeMillis();
		encOutParams();
//		long encOutParamsEndTime = System.currentTimeMillis();
//		log.info("TDSAccount.load: encOutParams()执行耗时: {}ms", (encOutParamsEndTime - encOutParamsStartTime));

		// 9. 整理输出内容
//		long putDataStartTime = System.currentTimeMillis();
		putData(dataList, pageInfo);
//		long putDataEndTime = System.currentTimeMillis();
//		log.info("TDSAccount.load: putData()执行耗时: {}ms", (putDataEndTime - putDataStartTime));

		// 10. 总体耗时统计
		long loadEndTime = System.currentTimeMillis();
		long totalTime = loadEndTime - loadStartTime;
		log.info("TDSAccount.load: load方法总执行耗时: {}ms，数据源别名: {}", totalTime, getDSAlias());

		// 性能分析日志
//		if (totalTime > 5000) { // 超过5秒记录警告
//			log.warn("TDSAccount.load: 执行时间过长，总耗时: {}ms，建议优化性能", totalTime);
//		} else if (totalTime > 2000) { // 超过2秒记录提示
//			log.info("TDSAccount.load: 执行时间较长，总耗时: {}ms", totalTime);
//		}

		txtMap = null;
	}

	/**
	 * 统一的默认值处理方法
	 * 根据处理模式决定是否应用默认值：
	 * INPUT模式：录入时使用当前登录人信息作为默认值
	 * QUERY模式：查询时不应用默认值，只显示已保存的数据
	 *
	 * @param tagId      采集点ID
	 * @param defaultVal 原始默认值类型
	 * @param mode       处理模式：INPUT-录入模式，QUERY-查询模式
	 * @return 处理后的实际默认值
	 */
	private String processDefaultValue(String tagId, String defaultVal, String mode) {
		// 查询模式下不应用默认值，直接返回空字符串
		if ("QUERY".equals(mode)) {
			return "";
		}

		// 录入模式下应用默认值
		return getProcessedDefaultValue(tagId, defaultVal);
	}

	/**
	 * 获取处理后的默认值（原有逻辑保持不变，供录入模式使用）
	 *
	 * @param tagId      采集点ID
	 * @param defaultVal 原始默认值类型
	 * @return 处理后的实际默认值
	 */
	private String getProcessedDefaultValue(String tagId, String defaultVal) {
		String defalutVal = "";
		switch (defaultVal) {
			case "1": // 当前用户
				TdsAccountMeter meter = Optional.ofNullable(this.mlist).orElse(new ArrayList<>())
						.stream().filter(item -> Objects.equals(item.getTagid(), tagId))
						.findFirst().orElse(null);
				if (meter != null) {
					String comboStr = getComboStr(meter.getControltype());
					String userId = SysUserHolder.getCurrentUser().getId();
					if ("userfield".equals(comboStr)) {
						defalutVal = userId;
					} else if ("usersfield".equals(comboStr)) {
						defalutVal = userId;
					} else if ("orgfield".equals(comboStr)) {
						//外委人员
						List<EmployeeVo> userPartTimePost = sysEmployeeInfoService.getUserPartTimePost(userId);
						String orgId = SysUserHolder.getCurrentUser().getOrgId();
						if (StringUtils.isNotEmpty(userPartTimePost)) {
							EmployeeVo partTimePost = userPartTimePost.stream().filter(item -> item.getOrgcode().equals(orgId)).findFirst().orElse(null);
							if (partTimePost != null) {
								defalutVal = partTimePost.getOrgcode();
							}else{
								defalutVal = orgId;
							}
						}else{
							defalutVal = orgId;
						}

					} else {
						defalutVal = SysUserHolder.getCurrentUser().getRealName();
					}

					return defalutVal;
				}
				return "";
			case "2": // 当前时间
				return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
			case "3": // 下拉框预设值
				// 获取采集点信息以判断控件类型
				TdsAccountMeter meterForType = Optional.ofNullable(this.mlist).orElse(new ArrayList<>())
						.stream().filter(item -> Objects.equals(item.getTagid(), tagId))
						.findFirst().orElse(null);

				if (meterForType != null && new Integer(15).equals(meterForType.getControltype())) {
					// 控件类型15（多选下拉框），使用DEFAULTVALS字段
					String defaultValsStr = defaultvalsIdMap.get(tagId);
					return StringUtils.isEmpty(defaultValsStr) ? "" : defaultValsStr;
				} else {
					// 其他控件类型，使用DEVICEDEFAULTVAL字段
					String textVal = defaultTextValMap.get(tagId);
					return StringUtils.isEmpty(textVal) ? "" : textVal;
				}
			case "4": // 当前日期
				return new SimpleDateFormat("yyyy-MM-dd").format(new Date());
			case "5": // 当前时间（不含日期）
				return new SimpleDateFormat("HH:mm:ss").format(new Date());
			case "6":
				//外委人员
				String userId = SysUserHolder.getCurrentUser().getId();
				List<EmployeeVo> userPartTimePost = sysEmployeeInfoService.getUserPartTimePost(userId);
				String orgId = SysUserHolder.getCurrentUser().getOrgId();
				if (StringUtils.isNotEmpty(userPartTimePost)) {
					EmployeeVo partTimePost = userPartTimePost.stream().filter(item -> item.getOrgcode().equals(orgId)).findFirst().orElse(null);
					if (partTimePost != null) {
						defalutVal = partTimePost.getOrgcode();
					}else{
						defalutVal = orgId;
					}
				}else{
					defalutVal = orgId;
				}
				return defalutVal;
			default:
				return "";
		}
	}
	private Map<String, Map<Date, String>> initLedgerExcendRowData(List<Map<String, Object>> _list) {
		// 1. 输入参数校验
		if (_list == null || _list.isEmpty() || mlist == null || mlist.isEmpty()) {
			log.warn("初始化扩展行数据失败：数据源关系列表或采集点列表为空");
			return new HashMap<>();
		}

		Map<String, Map<Date, String>> result = new HashMap<>();

		try {
			// 2. 按数据源分组的采集点与参数映射
			Map<String, Map<String, String>> dataSourceParamMap = new HashMap<>();
			Set<String> dataSourceAliasSet = new HashSet<>();

			// 提取数据源别名和参数映射关系
			for (Map<String, Object> map : _list) {
				// 采集点id
				String collectionPointId = getMapString(map, "COLLECTION_POINT_ID");
				// 输出参数别名
				String dataSourceParaAlias = getMapString(map, "DATA_SOURCE_PARA_ALIAS");
				// 数据源别名
				String alias = getMapString(map, "DATA_SOURCE_ALIAS");

				if (StringUtils.isNotEmpty(alias)) {
					dataSourceAliasSet.add(alias);

					// 为每个数据源维护独立的参数映射
					Map<String, String> paramMap = dataSourceParamMap.computeIfAbsent(alias, k -> new HashMap<>());
					if (StringUtils.isNotEmpty(collectionPointId) && StringUtils.isNotEmpty(dataSourceParaAlias)) {
						paramMap.put(collectionPointId, dataSourceParaAlias);
					}
				}
			}

			// 3. 检查数据源别名列表
			List<String> dataAliasList = new ArrayList<>(dataSourceAliasSet);
			if (dataAliasList.isEmpty()) {
				log.warn("未找到有效的数据源别名");
				return result;
			}

			// 4. 查询每个数据源的数据
			Map<String, Map<String, Object>> dsDataMap = new HashMap<>();
			int maxSize = 0;

			for (String tdsAlias : dataAliasList) {
				TdsQueryDto tdsParam = new TdsQueryDto();
				tdsParam.setTdsAlias(tdsAlias);
				tdsParam.setInParaAlias(this.inparamAliasFront);

				JSONArray currentData = tdsServ.getTDSData(tdsParam);
				if (currentData != null && !currentData.isEmpty()) {
					JSONObject firstItem = currentData.getJSONObject(0);
					if (firstItem != null && firstItem.containsKey("data")) {
						// 正确处理JSONArray/JSONObject
						Object dataObj = firstItem.get("data");
						if (dataObj instanceof JSONArray) {
							JSONArray dataArray = (JSONArray) dataObj;
							// 取 dataArray 里 的 每个数据大小，取最大的那个 作为最大长度，作为初始记录
							maxSize = Math.max(maxSize, dataArray.size());

							// 将每个JSONObject转换为Map
							for (int i = 0; i < dataArray.size(); i++) {
								if (dataArray.get(i) instanceof JSONObject) {
									JSONObject dataItem = dataArray.getJSONObject(i);
									dsDataMap.put(tdsAlias + "_" + i, dataItem);
								}
							}
						} else if (dataObj instanceof JSONObject) {
							JSONObject dataJson = (JSONObject) dataObj;
							dsDataMap.put(tdsAlias + "_0", dataJson);
							maxSize = Math.max(maxSize, 1);
						}
					}
				}
			}

			// 5. 生成时间记录
			// 修复：如果没有数据源数据，不生成时间点，避免空行
			if (maxSize == 0) {
				log.warn("所有数据源都没有返回数据，不生成时间点");
				return result; // 直接返回空结果，避免生成空行
			}

			List<Date> dateList = new ArrayList<>();

			if (StringUtils.isEmpty(this.timeBoundBc1)) {
				log.warn("上班时间未设置，使用当前时间");
				// 使用当前时间作为默认值，生成maxSize个时间点
				Date now = new Date();
				for (int i = 0; i < maxSize; i++) {
					// 使用当前时间加上时间间隔
					dateList.add(DateTimeUtils.doSecond(now, i * (this.istep > 0 ? this.istep : 60)));
				}
			} else {
				Date startTime = DateTimeUtils.parseDateTime(this.timeBoundBc1);
				if (startTime != null) {
					for (int i = 0; i < maxSize; i++) {
						dateList.add(DateTimeUtils.doSecond(startTime, i * (this.istep > 0 ? this.istep : 60)));
					}
				} else {
					log.warn("解析上班时间失败: {}, 使用当前时间", this.timeBoundBc1);
					Date now = new Date();
					for (int i = 0; i < maxSize; i++) {
						dateList.add(DateTimeUtils.doSecond(now, i * (this.istep > 0 ? this.istep : 60)));
					}
				}
			}

			// 6. 为每个采集点生成数据记录
			for (TdsAccountMeter meter : mlist) {
				String tagId = meter.getTagid();
				if (StringUtils.isEmpty(tagId)) {
					continue; // 跳过无效的采集点ID
				}

				Map<Date, String> timeDataMap = new LinkedHashMap<>();
				boolean foundValue = false;

				// 遍历数据源找到对应的参数值
				for (String dsAlias : dataAliasList) {
					Map<String, String> paramMap = dataSourceParamMap.get(dsAlias);
					if (paramMap != null && paramMap.containsKey(tagId)) {
						String paramAlias = paramMap.get(tagId);
						if (StringUtils.isEmpty(paramAlias)) {
							continue; // 跳过无效的参数别名
						}

						// 查找所有匹配的数据项
						for (Map.Entry<String, Map<String, Object>> entry : dsDataMap.entrySet()) {
							if (entry.getKey().startsWith(dsAlias + "_")) {
								Map<String, Object> dataItem = entry.getValue();
								if (dataItem != null && dataItem.containsKey(paramAlias)) {
									Object paramValue = dataItem.get(paramAlias);
									String value = paramValue == null ? "" : paramValue.toString();

									// 为该时间点设置值
									try {
										String indexStr = entry.getKey().substring(entry.getKey().lastIndexOf("_") + 1);
										int index = Integer.parseInt(indexStr);
										if (index < dateList.size()) {
											timeDataMap.put(dateList.get(index), value);
											foundValue = true;
										}
									} catch (NumberFormatException e) {
										log.warn("解析索引失败: {}", entry.getKey(), e);
									}
								}
							}
						}

						// 如果找到了值，不用继续查找其他数据源
						if (foundValue) {
							break;
						}
					}
				}

				// 修复：只有找到有效数据的采集点才添加到结果中，避免空行
				if (foundValue && !timeDataMap.isEmpty()) {
					// 只为有数据的时间点填充jobInputTimeList
					for (Date timeDate : timeDataMap.keySet()) {
						if (this.jobInputTimeList.containsKey(tagId)) {
							this.jobInputTimeList.get(tagId).put(timeDate, DateTimeUtils.formatDateTime(timeDate));
						} else {
							Map<Date, String> _map = new LinkedHashMap<Date, String>();
							_map.put(timeDate, DateTimeUtils.formatDateTime(timeDate));
							this.jobInputTimeList.put(tagId, _map);
						}
					}
					result.put(tagId, timeDataMap);
				} else {
					log.debug("采集点 {} 没有找到有效数据，跳过添加", tagId);
				}
			}

//			// 7. 更新时间相关的类属性
//			if (!dateList.isEmpty()) {
//				this.timeList = dateList;
//				this.st = DateTimeUtils.formatDateTime(dateList.get(0));
//				this.et = DateTimeUtils.formatDateTime(dateList.get(dateList.size() - 1));
//
//				// 初始化时间编辑映射和时间显示映射
//				this.infoTimeEditMap = new LinkedHashMap<>();
//				this.sjmap = new LinkedHashMap<>();
//
//				String defaultTimeFormat = "yyyy-MM-dd HH:mm:ss";
//				String timeFormat = (this.timeConf != null && StringUtils.isNotEmpty(this.timeConf.getTimeFormat()))
//						? this.timeConf.getTimeFormat()
//						: defaultTimeFormat;
//
//				for (Date date : dateList) {
//					infoTimeEditMap.put(date, "1");
//					sjmap.put(date, DateTimeUtils.format(date, timeFormat));
//				}
//			}

		} catch (Exception e) {
			log.error("初始化扩展行数据异常: {}", e.getMessage(), e);
		}

		return result;
	}

	public static Map<String, String> parseInParams(String inParams) {
		Map<String, String> paramsMap = new HashMap<>();

		if (StringUtils.isEmpty(inParams)) {
			return paramsMap;
		}

		try {
			// 使用正则表达式分割字符串
			String[] pairs = inParams.split("\\|");

			for (String pair : pairs) {
				String[] keyValue = pair.split("=");
				if (keyValue.length == 2) {
					String key = keyValue[0].trim();
					String value = keyValue[1].trim();
					paramsMap.put(key, value);
				}
			}

			return paramsMap;
		} catch (Exception e) {
			return paramsMap; // 发生异常时返回空Map
		}
	}

	private Map<String, Map<Date, String>> initLedgerTimeData(Map<String, Object> map) {
		Map<String, Map<Date, String>> result = new HashMap<>();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		List<Date> dateList = new ArrayList<>();

		String startTime = map.get("START_TIME") == null ? "" : String.valueOf(map.get("START_TIME"));
		String endTime = map.get("END_TIME") == null ? "" : String.valueOf(map.get("END_TIME"));
		String startTimeType = map.get("START_TIME_TYPE") == null ? "" : String.valueOf(map.get("START_TIME_TYPE"));
		// String endTimeType = map.get("END_TIME_TYPE") == null ? "" :
		// String.valueOf(map.get("END_TIME_TYPE"));
		Integer timeInterval = map.get("TIME_INTERVAL") == null ? 0
				: Integer.parseInt(String.valueOf(map.get("TIME_INTERVAL")));
		String timeIntervalType = map.get("TIME_INTERVAL_TYPE") == null ? ""
				: String.valueOf(map.get("TIME_INTERVAL_TYPE"));

		if (StringUtils.isEmpty(map)) {
			// 生成从startTime到endTime的时间批次，间隔按照startTime到endTime的时间间隔,默认1小时
			dateList = this.generateTimeBatches(this.timeBoundBc1, this.timeBoundBc2, 1, "hour");
		} else if (!map.isEmpty()) {
			// 当班时间类型
			if ("1".equals(startTimeType)) {
				// 生成从startTime到endTime的时间批次，间隔按照时间间隔设置

				if (timeInterval == null || timeInterval <= 0) {
					timeInterval = 1;
				}

				if (StringUtils.isEmpty(timeIntervalType)) {
					timeIntervalType = "hour";
				}
				if (StringUtils.isNotEmpty(this.timeBoundBc1)) {
					startTime = this.timeBoundBc1;
				}
				if (StringUtils.isNotEmpty(this.timeBoundBc2)) {
					endTime = this.timeBoundBc2;
				}

				dateList = this.generateTimeBatches(startTime, endTime, timeInterval,
						timeIntervalType);
			} else {
				// 自定义时间类型，取数据库时间
				try {
					// Date startDate = sdf.parse(startTime);
					// Date endDate = sdf.parse(endTime);

					// if (startDate != null && endDate != null) {
					// String startTimeStr = sdf.format(startDate);
					// String endTimeStr = sdf.format(endDate);

					if (timeInterval == null || timeInterval <= 0) {
						timeInterval = 1;
					}

					if (StringUtils.isEmpty(timeIntervalType)) {
						timeIntervalType = "hour";
					}

					dateList = this.generateTimeBatches(startTime, endTime,
							timeInterval, timeIntervalType);
					// }
				} catch (Exception e) {
					log.error("时间初始化处理自定义时间类型出错: {}", e.getMessage(), e);
					dateList = this.generateTimeBatches(this.timeBoundBc1, this.timeBoundBc2, 1,
							"hour");
				}
			}
		}

		// 2. 遍历digitalLedgerTime，按StartTime查找每个采集点的实时值
		// JSONArray resultArray = new JSONArray();
		Date now = DateTimeUtils.getNowDate();
		String orgId = SysUserHolder.getCurrentUser().getOrgId();
		if (StringUtils.isEmpty(orgId)) {
			log.error("时间批次初始化 initLedgerTimeData 方法 获取当前用户{}机构ID失败", SysUserHolder.getCurrentUser());
		}

		// 当前用户是否当班
		boolean currentShift = false;
		ShiftForeignVo shiftByDateTime = shiftService.getShiftByDateTime(sdf.format(now), orgId);
		if (StringUtils.isNotEmpty(shiftByDateTime.getShiftClassCode())) {
			currentShift = true;
		}
		// 当班情况下，仅筛选dateList中当前时间之前的所有时间点
		List<Date> filteredList = new ArrayList<>();
		if (currentShift) {
			for (Date date : dateList) {
				if (DateTimeUtils.bjDate(now, date) == 1) {
					filteredList.add(date);
				}
			}
		}
		List<Date> dateListShow = StringUtils.isNotEmpty(filteredList) ? filteredList : dateList;

		for (TdsAccountMeter dot : mlist) {
			// 遍历采集点
			for (Date timeDate : dateListShow) {
				String tagId = dot.getTagid();
				String value = null;
				if (result.containsKey(tagId)) {
					result.get(tagId).put(timeDate, value);
				} else {
					Map<Date, String> _map = new LinkedHashMap<Date, String>();
					_map.put(timeDate, value);
					result.put(tagId, _map);
				}
				if (this.jobInputTimeList.containsKey(tagId)) {
					this.jobInputTimeList.get(tagId).put(timeDate, DateTimeUtils.formatDateTime(timeDate));
				} else {
					Map<Date, String> _map = new LinkedHashMap<Date, String>();
					_map.put(timeDate, DateTimeUtils.formatDateTime(timeDate));
					this.jobInputTimeList.put(tagId, _map);
				}
			}
		}

		return result;
	}

	/**
	 * 根据时间间隔类型生成时间批次
	 *
	 * @param startTimeStr     开始时间字符串
	 * @param endTimeStr       结束时间字符串
	 * @param timeInterval     时间间隔
	 * @param timeIntervalType 时间间隔类型
	 * @return 时间批次列表
	 */
	private List<Date> generateTimeBatches(String startTimeStr, String endTimeStr,
			Integer timeInterval, String timeIntervalType) {
		List<Date> result = new ArrayList<>();

		try {
			// 解析开始和结束时间
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date startDate = sdf.parse(startTimeStr);
			Date endDate = sdf.parse(endTimeStr);

			// 设置默认值
			if (timeInterval == null || timeInterval <= 0) {
				timeInterval = 1;
			}

			if (StringUtils.isEmpty(timeIntervalType)) {
				timeIntervalType = "hour";
			}

			Calendar calendar = Calendar.getInstance();
			calendar.setTime(startDate);

			int calendarField;
			switch (timeIntervalType) {
				case "second":
					calendarField = Calendar.SECOND;
					break;
				case "minute":
					calendarField = Calendar.MINUTE;
					break;
				case "hour":
					calendarField = Calendar.HOUR_OF_DAY;
					break;
				case "day":
					calendarField = Calendar.DAY_OF_MONTH;
					break;
				case "month":
					calendarField = Calendar.MONTH;
					break;
				case "year":
					calendarField = Calendar.YEAR;
					break;
				default:
					calendarField = Calendar.HOUR_OF_DAY;
					break;
			}

			int batchCount = 0;
			Calendar tempCal = (Calendar) calendar.clone();
			while (!tempCal.getTime().after(endDate)) {
				batchCount++;
				tempCal.add(calendarField, timeInterval);
				// 避免无限循环
				// if (batchCount > 9) {
				// break;
				// }
			}

			// 如果没有批次，至少创建一个
			if (batchCount == 0) {
				batchCount = 1;
			}

			for (int i = 0; i < batchCount; i++) {
				JSONObject time = new JSONObject();

				// 设置开始时间
				Date currentStartTime = calendar.getTime();
				time.put("startTime", currentStartTime);
				time.put("startTimeType", "1"); // 1表示动态时间

				// 计算结束时间
				Calendar endCal = (Calendar) calendar.clone();
				endCal.add(calendarField, timeInterval);

				// 确保最后一个批次的结束时间不超过指定的结束时间
				if (endCal.getTime().after(endDate)) {
					endCal.setTime(endDate);
				}

				time.put("endTime", endCal.getTime());
				time.put("endTimeType", "1");

				// 设置时间间隔
				time.put("timeInterval", timeInterval);
				time.put("timeIntervalType", timeIntervalType);

				result.add(currentStartTime);

				// 增加间隔时间，准备下一个时间点
				calendar.add(calendarField, timeInterval);

				// 如果下一个开始时间已经超过结束时间，则停止循环
				if (calendar.getTime().after(endDate)) {
					break;
				}
			}
		} catch (Exception e) {
			log.error("生成时间批次出错: {}", e.getMessage(), e);
			return result;
		}

		return result;
	}

	// 重新获取时，删除原数据
	private void deleteMongoDbTimeData() {
		List<Map> data = accountServ.getAccountNewData(accountNewTab + "_" + getDSAlias(), 1, version, confCode,
				shiftCode, accountId);
		if (StringUtils.isNotEmpty(data)) {
			List<JSONObject> delList = new ArrayList<JSONObject>();

			JSONObject tobj = new JSONObject(data.get(0));
			JSONArray arr = tobj.getJSONArray("info");
			for (int i = 0, il = arr.size(); i < il; i++) {
				JSONObject obj = arr.getJSONObject(i);
				String sj = obj.getString("sj");
				if (sj.equals(this.reTime)) {
					delList.add(obj);
				}
			}

			if (StringUtils.isNotEmpty(delList)) {
				arr.removeAll(delList);
				tobj.put("info", arr);
				mongoDBServ.updateById(accountNewTab + "_" + this.getDSAlias(), tobj);
			}
		}
	}

	private Map<String, Map<Date, String>> getRelationData(Map<String, Map<Date, String>> dataList) {
		Date sd = DateTimeUtils.parseDateTime(this.st);
		Date ed = DateTimeUtils.parseDateTime(this.et);

		// 仪表数据
		DataAccountTools dat = new DataAccountTools();
		List<AccountTagData> tagList = dat.getTagData(sd, ed, tagIdList);
		if (StringUtils.isNotEmpty(tagList)) {
			for (AccountTagData data : tagList) {
				if (dataList.containsKey(data.getTagId())) {// 目前只更新值，其他属性暂不处理
					Map<Date, String> sjdata = dataList.get(data.getTagId());
					sjdata.put(data.getApplyTime(), data.getValstr());

					if (new Integer(1).equals(data.getEditMark())) {
						if (saveDataList == null) {
							saveDataList = new ArrayList<String>();
						}
						String key = data.getApplyTimeStr() + "_" + data.getTagId();
						if (!saveDataList.contains(key)) {
							saveDataList.add(key);
						}
					}
				}
			}
		}
		// 确认数据
		List<AccountConfirmData> confirmList = dat.getConfirmData(sd, ed);
		if (StringUtils.isNotEmpty(confirmList)) {
			String col = "rowConfirm";
			for (AccountConfirmData obj : confirmList) {
				Date applyTime = obj.getApplyTime();
				String unitCode = obj.getUnitCode();
				String aid = obj.getCustomFormId();
				Boolean confirmOver = new Integer(1).equals(obj.getConfirmOver());
				String confirmName = obj.getConfirmUserName();

				String valstr = DateTimeUtils.formatDateTime(obj.getConfirmTime());

				if (this.confCode.equals(unitCode)) {// 相同的核算对象
					if (StringUtils.isEmpty(this.accountId)) {// 没用自定义台账
						if (StringUtils.isNotEmpty(this.accountId)) {
							continue;
						}
						valstr = "<div style='color:" + (confirmOver ? "red" : "#1B9A49") + ";'>" + confirmName + "("
								+ valstr + ")</div>";
					} else {// 使用自定义台账
						if (StringUtils.isNotEmpty(aid) && this.accountId.equals(aid)) {
							valstr = "<div style='color:" + (confirmOver ? "red" : "#1B9A49") + ";'>" + confirmName
									+ "(" + valstr + ")</div>";
						} else {
							continue;
						}
					}
				} else {
					continue;
				}

				if (dataList.containsKey(col)) {// 理论上MongoDB不出问题，会有对应数据
					dataList.get(col).put(applyTime, valstr);
				} else {
					Map<Date, String> _map = new HashMap<Date, String>();
					_map.put(applyTime, valstr);
					dataList.put(col, _map);
				}
			}
		}

		return dataList;
	}

	/**
	 * @category 获取已保存数据
	 * @return
	 */
	private Map<String, Map<Date, String>> getMongoDBData() {// 编写独立函数，进行调用 TODO

		Map<String, Map<Date, String>> rmap = new LinkedHashMap<String, Map<Date, String>>();
		// 暂时按仪表为列的模式获取时间范围
		List<Map> mdata = null;
		// String sendrq = timeBoundBc1.length() >= 10 ? timeBoundBc1.substring(0, 10) :
		// this.version;

		if ("bound".equals(this.timeConf.getShowMode())) {// 时间段
			log.info("1.MongoDB获取数据时间段：" + this.timeBoundBc1 + "," + this.accountTabName + "_" + getDSAlias());
			long slong = DateTimeUtils.parseDateTime(this.timeBoundBc1).getTime();
			newdata = accountServ.getAccountNewData(this.accountNewTab + "_" + getDSAlias(), 2, version, confCode,
					shiftCode, accountId);
			if (StringUtils.isNotEmpty(newdata)) {
				List<Map> tmap = new ArrayList<Map>();
				Object info = newdata.get(0).get("info");
				if (info != null) {
					tmap.addAll((List<Map>) info);
				}
				// 确认表数据
				List<Map> cdata = accountServ.getAccountNewData(this.accountNewTab + "_confirm_" + getDSAlias(), 2,
						version, confCode, shiftCode, accountId);
				if (StringUtils.isNotEmpty(cdata)) {
					Object cinfo = cdata.get(0).get("info");
					if (cinfo != null) {
						tmap.addAll((List<Map>) cinfo);
					}
				}
				if (StringUtils.isNotEmpty(tmap)) {
					mdata = tmap;
				}
			}
			// if(StringUtils.isEmpty(mdata)) {
			// mdata = accountServ.getAccountSaveData(this.accountTabName+"_"+getDSAlias(),
			// getDSAlias(), slong, slong, confCode, shiftCode, accountId);
			// }
			if (StringUtils.isEmpty(mdata)) {
				List<String> tagidlist = new ArrayList<String>();
				if (tagIdObjMap != null) {
					for (String tagId : tagIdObjMap.keySet()) {
						tagidlist.add(tagId);
					}
				}
				if (StringUtils.isNotEmpty(tagidlist)) {
					mdata = accountServ.getAccountSaveData(this.accountTabName + "_" + getDSAlias(), getDSAlias(),
							DateTimeUtils.parseDateTime(this.st).getTime(),
							DateTimeUtils.parseDateTime(this.et).getTime(), tagidlist);
				}
			}
		} else {
			log.info("2.MongoDB获取数据时间段：" + this.st + "  ~ " + this.et + "," + this.accountTabName + "_" + getDSAlias());

			long lst = DateTimeUtils.parseDateTime(this.st).getTime();
			long let = DateTimeUtils.parseDateTime(this.et).getTime();

			newdata = accountServ.getAccountNewData(this.accountNewTab + "_" + getDSAlias(), 1, version, confCode,
					shiftCode, accountId);
			if (StringUtils.isNotEmpty(newdata)) {
				List<Map> tmap = new ArrayList<Map>();
				if (!isOutDataMode) {// 外部获取数据模式，不走本地存取方式
					Object info = newdata.get(0).get("info");
					if (info != null) {
						tmap.addAll((List<Map>) info);
					}
				}
				// 确认表数据
				List<Map> cdata = accountServ.getAccountNewData(this.accountNewTab + "_confirm_" + getDSAlias(), 1,
						version, confCode, shiftCode, accountId);
				if (StringUtils.isNotEmpty(cdata)) {
					Object cinfo = cdata.get(0).get("info");
					if (cinfo != null) {
						tmap.addAll((List<Map>) cinfo);
					}
				}
				if (StringUtils.isNotEmpty(tmap)) {
					mdata = tmap;
				}
			}

			// if(StringUtils.isEmpty(mdata)) {
			// mdata = accountServ.getAccountSaveData(this.accountTabName+"_"+getDSAlias(),
			// getDSAlias(), lst, let, confCode, shiftCode, accountId);
			// }

			if (StringUtils.isEmpty(mdata)) {
				List<String> tagidlist = new ArrayList<String>();
				tagidlist.add("rowConfirm");
				if (tagIdObjMap != null) {
					for (String tagId : tagIdObjMap.keySet()) {
						tagidlist.add(tagId);
					}
				}
				mdata = accountServ.getAccountSaveData(this.accountTabName + "_" + getDSAlias(), getDSAlias(), lst, let,
						tagidlist);

			} else if (StringUtils.isEmpty(newdata)) {
				// 补显示更新所在班次确认丢失的问题
				List<Map> cdata = accountServ.getOldAccountConfirmData(this.accountTabName + "_" + getDSAlias(),
						getDSAlias(), lst, let, accountId, confCode);
				if (cdata != null) {
					mdata.addAll(cdata);
				}
			}
		}
		if (mdata != null) {
			haveMongoDbData = true;
			log.info("mogoget:" + mdata.size());
			mdata.sort(Comparator.comparing(o -> String.valueOf(o.get("val"))));

			JSONArray newdataarr = null;
			JSONArray newconfirmarr = null;
			// 如果新结构没数据且已有数据，那么将旧数据转入新数据
			if (StringUtils.isEmpty(newdata)) {
				newdataarr = new JSONArray();
				newconfirmarr = new JSONArray();
			}

			for (Map map : mdata) {
				String col = String.valueOf(map.get("col"));// 仪表相当于tagID
				// String tag = String.valueOf(map.get("tag"));
				String sj = String.valueOf(map.get("sj"));
				long sjlong = 0L;
				try {
					sjlong = (Long) map.get("sjlong");
				} catch (Exception e) {
				}
				String aid = String.valueOf(map.get("accountId"));
				String unitCode = String.valueOf(map.get("unitCode"));
				Date creTime = null;
				try {
					creTime = (Date) map.get("creTime");
				} catch (Exception e) {
				}
				String creUserName = String.valueOf(map.get("creUserName"));
				Date updTime = null;
				try {
					updTime = (Date) map.get("updTime");
				} catch (Exception e) {
				}
				String updUserId = String.valueOf(map.get("updUserId"));
				if (StringUtils.isEmpty(sj)) {// 无时间数据，不进行获取
					continue;
				}
				// String tagId = String.valueOf(map.get("tagId"));
				String valstr = String.valueOf(map.get("valstr"));
				String tvalstr = String.valueOf(map.get("valstr"));

				if (StringUtils.isNotEmpty(newdata)) {
					unitCode = confCode;
					aid = String.valueOf(accountId);
				}

				Boolean isEdit = "true".equals(String.valueOf(map.get("edit")));
				if ("rowConfirm".equals(col)) {
					if (this.confCode.equals(unitCode)) {
						// System.out.println("rowConfirm: col="+col+" sj="+sj+"
						// confCode="+this.confCode+" unitCode="+unitCode+" accountId="+accountId+"
						// aid="+aid);
						if (StringUtils.isEmpty(this.accountId)) {
							if (aid.length() > 0 && !"null".equalsIgnoreCase(aid)) {
								continue;
							}
							if ("true".equals(String.valueOf(map.get("rowConfirmOver")))) {
								valstr = "<div style='color:red;'>" + String.valueOf(map.get("creUserName")) + "("
										+ valstr + ")</div>";
							} else {
								valstr = "<div style='color:#1B9A49;'>" + String.valueOf(map.get("creUserName")) + "("
										+ valstr + ")</div>";
							}
							if (!confirmTimeList.contains(sj)) {
								confirmTimeList.add(sj);
							}
						} else {
							if (StringUtils.isNotEmpty(aid) && this.accountId.equals(aid)) {// 没有保存自定义表单ID
								if ("true".equals(String.valueOf(map.get("rowConfirmOver")))) {
									valstr = "<div style='color:red;'>" + String.valueOf(map.get("creUserName")) + "("
											+ valstr + ")</div>";
								} else {
									valstr = "<div style='color:#1B9A49;'>" + String.valueOf(map.get("creUserName"))
											+ "(" + valstr + ")</div>";
								}
								if (!confirmTimeList.contains(sj)) {
									confirmTimeList.add(sj);
								}
							} else {
								continue;
							}
						}
					} else {
						continue;
					}
				}

				if ("bound".equals(this.timeConf.getShowMode())) {// 时间段
					// if(rmap.containsKey(col)) {
					// Map<Date, String> _map = rmap.get(col);
					// _map.put(null, _map.get(null)+","+valstr);
					// }else {
					Map<Date, String> _map = new HashMap<Date, String>();
					_map.put(null, valstr);
					rmap.put(col, _map);
					// }

					if (StringUtils.isEmpty(newdata)) {
						JSONObject aobj = new JSONObject();
						aobj.put("col", col);// 列名
						aobj.put("sj", sj);
						aobj.put("sjlong", sjlong);
						aobj.put("valstr", tvalstr);
						aobj.put("creTime", creTime);
						aobj.put("creUserName", creUserName);
						aobj.put("updTime", updTime);
						aobj.put("updUserId", updUserId);
						if ("rowConfirm".equals(col)) {
							String rowConfirmOver = String.valueOf(map.get("rowConfirmOver"));
							aobj.put("rowConfirmOver", rowConfirmOver);
							newconfirmarr.add(aobj);
						} else {
							Boolean edit = (Boolean) map.get("edit");
							aobj.put("edit", edit);
							newdataarr.add(aobj);
						}
					}

					if (StringUtils.isNotEmpty(col) && isEdit) {
						// 标识记录已保存的数据，用于背景色显示,单独一行的分析仪表暂无用
						if (saveDataList == null) {
							saveDataList = new ArrayList<String>();
						}
						saveDataList.add(col);
					}

					if (mongoMap == null) {
						mongoMap = new HashMap<String, Map>();// 默认一行数据
					}
					mongoMap.put(col, map);
				} else {

					if (rmap.containsKey(col)) {
						rmap.get(col).put(DateTimeUtils.parseDateTime(sj), valstr);

						// if("rowConfirm".equals(col)) {
						// rmap.get("rowConfirmOver").put(DateTimeUtils.parseDateTime(sj),
						// String.valueOf(map.get("rowConfirmOver")));
						// }
					} else {

						Map<Date, String> _map = new HashMap<Date, String>();
						_map.put(DateTimeUtils.parseDateTime(sj), valstr);
						rmap.put(col, _map);

						// if("rowConfirm".equals(col)) {
						// Map<Date, String> _map2 = new HashMap<Date, String>();
						// _map2.put(DateTimeUtils.parseDateTime(sj),
						// String.valueOf(map.get("rowConfirmOver")));
						// rmap.put("rowConfirmOver", _map2);
						// }
					}

					if (StringUtils.isEmpty(newdata)) {
						JSONObject aobj = new JSONObject();
						aobj.put("col", col);// 列名
						aobj.put("sj", sj);
						aobj.put("sjlong", sjlong);
						aobj.put("valstr", tvalstr);
						aobj.put("creTime", creTime);
						aobj.put("creUserName", creUserName);
						aobj.put("updTime", updTime);
						aobj.put("updUserId", updUserId);
						if ("rowConfirm".equals(col)) {
							String rowConfirmOver = String.valueOf(map.get("rowConfirmOver"));
							aobj.put("rowConfirmOver", rowConfirmOver);
							aobj.put("creUserId", String.valueOf(map.get("creUserId")));
							newconfirmarr.add(aobj);
						} else {
							Boolean edit = (Boolean) map.get("edit");
							aobj.put("edit", edit);
							newdataarr.add(aobj);
						}
					}

					if (StringUtils.isNotEmpty(col) && isEdit) {
						// 标识记录已保存的数据，用于背景色显示
						if (saveDataList == null) {
							saveDataList = new ArrayList<String>();
						}
						saveDataList.add(sj + "_" + col);// tag.replace(" ", "T")
					}
				}

			}

			// 如果新结构没数据且已有数据，那么将旧数据转入新数据
			if (StringUtils.isEmpty(newdata)) {
				JSONObject tobj = createMongoDbData("data", newdataarr);
				// JSONArray alist = new JSONArray();
				// alist.add(tobj);
				mongoDBServ.insert(accountNewTab + "_" + this.getDSAlias(), tobj);

				JSONObject cobj = createMongoDbData("confirm", newconfirmarr);
				// JSONArray clist = new JSONArray();
				// clist.add(cobj);
				mongoDBServ.insert(accountNewTab + "_confirm_" + this.getDSAlias(), cobj);

				Map<String, Object> m = new HashMap<>();
				for (String key : tobj.keySet()) {
					m.put(key, tobj.get(key));
				}
				newdata = new ArrayList<Map>();
				newdata.add(m);
			}

			mdata = null;
		} else {
			log.info("mogoget:" + 0);
		}
		if (StringUtils.isEmpty(mdata) && StringUtils.isEmpty(newdata)) {// 初次无数据情况，初始化newdata
			JSONObject tobj = createMongoDbData("data", new JSONArray());
			// JSONArray alist = new JSONArray();
			// alist.add(tobj);
			mongoDBServ.insert(accountNewTab + "_" + this.getDSAlias(), tobj);

			// 确认表数据
			List<Map> cdata = accountServ.getAccountNewData(this.accountNewTab + "_confirm_" + getDSAlias(), 1,
					version, confCode, shiftCode, accountId);
			if (StringUtils.isEmpty(cdata)) {
				JSONObject cobj = createMongoDbData("confirm", new JSONArray());
				// JSONArray clist = new JSONArray();
				// clist.add(cobj);
				mongoDBServ.insert(accountNewTab + "_confirm_" + this.getDSAlias(), cobj);
			}

			Map<String, Object> m = new HashMap<>();
			for (String key : tobj.keySet()) {
				m.put(key, tobj.get(key));
			}
			newdata = new ArrayList<Map>();
			newdata.add(m);
		}

		return rmap;
	}

	private JSONObject createMongoDbData(String type, JSONArray data) {
		JSONObject tobj = new JSONObject();
		tobj.put("_id", TMUID.getUID());
		tobj.put("type", type);//
		tobj.put("unitCode", confCode);// 核算单元
		tobj.put("bc", shiftCode);// 班次
		tobj.put("accountId", accountId);// accountId
		tobj.put("rq", version);
		tobj.put("rqlong", this.versionLong);
		tobj.put("mode", "bound".equals(this.timeConf.getShowMode()) ? 2 : 1);
		tobj.put("tmused", 1);
		tobj.put("info", data);
		return tobj;
	}
	/**
	 * @category 读取配置信息
	 */
	private void getConf() {

		ApplyParams dto = new ApplyParams();
		dto.setApplyAlias("account_confirm_step");
		String confirmStepStr = applySrv.getApplyConfValue(dto);
		if (Coms.judgeLong(confirmStepStr)) {
			int cs = Integer.parseInt(confirmStepStr);
			if (cs > 0) {
				confirmStep = cs;
			}
		}
		// 应用方案上下限参数
		ApplyParams fadto = new ApplyParams();
		fadto.setApplyAlias("account_usefa_uplowlimit");
		String useFaStr = applySrv.getApplyConfValue(fadto);
		if ("false".equals(useFaStr)) {
			this.useFa = false;
		}

		TdsAccountDto param = new TdsAccountDto();
		param.setTdsAlias(getDSAlias());
		conf = accountServ.getAccountConf(getDSAlias());// 配置信息
		timeConf = accountServ.getTimeConf(getDSAlias());// 时间配置
		plist = accountServ.getOutConf(param);// 输出配置

		isOutDataMode = "mobile".equals(timeConf.getShowMode()) || "synchronize".equals(timeConf.getShowMode());// 仪表数据是否使用外部存储

		this.tagType = "3".equals(conf.getTagType()) ? "lims" : "pwl";// 2平稳率 3化验分析lims

		List<TdsAccountParamVo> bindparamList = accountServ.getAccountBindParam(getDSAlias());
		Map<String, TdsAccountParam> amap = new HashMap<String, TdsAccountParam>();
		for (TdsAccountParam aparam : bindparamList) {
			amap.put(aparam.getOldParamAlias(), aparam);
		}

		// 获取主数据的输入参数及值，确定获取仪表信息范围
		if (StringUtils.isNotEmpty(this.tInParas)) {
			Boolean isunit = false;
			for (TInPara temp : this.tInParas) {
				TdsAccountParam bp = amap.get(temp.getParaAlias());
				if (bp != null && temp.getValue() != null) {
					String val = String.valueOf(temp.getValue());// && String.valueOf(temp.getValue()).length() > 0
					if ("day".equals(bp.getNewParamId())) {
						if (val.length() > 0) {
							Date d = DateTimeUtils.parseDate(val);
							if (d == null) {
								this.version = null;
							} else {
								this.version = val.substring(0, 10);
								this.timeBoundDay = val.substring(0, 10);
							}
						} else {
							this.version = null;
						}
					} else if ("month".equals(bp.getNewParamId())) {
						if (val.length() > 0) {
							Date d = DateTimeUtils.parseDate(val);
							if (d == null) {
								this.version = null;
							} else {
								this.version = val.substring(0, 7);
								this.timeBoundDay = val.substring(0, 7) + "-01";
							}
						} else {
							this.version = null;
						}
					} else if ("unit".equals(bp.getNewParamId())) {
						if (val.length() > 0) {
							mode = 1;
							this.confCode = val;
							sendUnitCode = val;
							isunit = true;
						}
					} else if ("org".equals(bp.getNewParamId())) {
						if (val.length() > 0 && !isunit) {
							mode = 2;
							this.confCode = val;
						}
//						val = String.valueOf(temp.getValue() == null ? "" : temp.getValue());
						log.info("TDSAccount.init - 处理orgcode参数: temp.getValue()={}", val);
						if (StringUtils.isNotEmpty(val)) {
							this.orgcode = val;
							log.info("TDSAccount.init - 设置orgcode成功: {}", this.orgcode);
						} else {
							log.warn("TDSAccount.init - orgcode参数值为空，未设置");
						}
					} else if ("bc".equals(bp.getNewParamId())) {// 班次
						if (StringUtils.isNotEmpty(val) && val.split(",").length > 2) {
							String[] ss = val.split(",");
							shiftCode = ss[0];
							this.timeBoundBc1 = ss.length > 1 ? ss[1] : "";
							this.timeBoundBc2 = ss.length > 2 ? ss[2] : "";
						}
					} else if ("dataid".equals(bp.getNewParamId())) {// 流程数据标识
						if (StringUtils.isNotEmpty(val)) {
							this.dataId = val;
						}
					} else if ("accountId".equals(bp.getNewParamId())) {// 自定义表单标识，可能没有
						if (StringUtils.isNotEmpty(val)) {
							this.accountId = val;
						}
					}
				} else if ("showTimeNum".equals(temp.getParaAlias())) {//
					String val = String.valueOf(temp.getValue());
					if (StringUtils.isNotEmpty(val) && Coms.judgeLong(val)) {
						this.showTimeNum = Integer.parseInt(val);
					}
				} else if ("reTime".equals(temp.getParaAlias())) {//
					String val = String.valueOf(temp.getValue() == null ? "" : temp.getValue());
					if (StringUtils.isNotEmpty(val)) {
						this.reTime = val;
					}
				} else if ("form".equals(temp.getParaAlias())) {//
					String val = String.valueOf(temp.getValue() == null ? "" : temp.getValue());
					if (StringUtils.isNotEmpty(val)) {
						this.reTime = val;
					}
				} else if ("ledgerModuleId".equals(temp.getParaAlias())) {// 外部属性传入配置
					String val = String.valueOf(temp.getValue() == null ? "" : temp.getValue());
					if (StringUtils.isNotEmpty(val)) {
						this.ledgerModuleId = val;
					}
				} else if ("ledgerInitType".equals(temp.getParaAlias())) {// 外部属性传入配置
					String val = String.valueOf(temp.getValue() == null ? "" : temp.getValue());
					if (StringUtils.isNotEmpty(val)) {
						this.initMode = val;
					}
				} else if ("ledgerComponentId".equals(temp.getParaAlias())) {// 外部属性传入配置
					String val = String.valueOf(temp.getValue() == null ? "" : temp.getValue());
					if (StringUtils.isNotEmpty(val)) {
						this.ledgerFormId = val;
					}
				} else if ("orgcode".equals(temp.getParaAlias())) {// 外部属性传入配置
					String val = String.valueOf(temp.getValue() == null ? "" : temp.getValue());
					log.info("TDSAccount.init - 处理orgcode参数: temp.getValue()={}, val={}", temp.getValue(), val);
					if (StringUtils.isNotEmpty(val)) {
						this.orgcode = val;
						log.info("TDSAccount.init - 设置orgcode成功: {}", this.orgcode);
					} else {
						log.warn("TDSAccount.init - orgcode参数值为空，未设置");
					}
				}
				// else if("inputMode".equals(temp.getParaAlias()))
				// {//模式，外部数据模式下，不自动推导时间，如果是采集点模式，获取时间及数据，可添加；如果是台账模式，无添加，通过查询获取时间点
				// String val = String.valueOf(temp.getValue()==null?"":temp.getValue());
				// if(StringUtils.isNotEmpty(val)) {
				// this.inputMode = val;
				// }
				// }
			}

			if (version != null) {
				Date d = DateTimeUtils.parseDate(version);
				if (d != null) {
					this.versionLong = d.getTime();
				}
			}
		}
		// //无核算对象信息传入暂不处理，后期扩展功能使用 TODO
		if (this.confCode == null) {
			// SysUser user = SysUserHolder.getCurrentUser();
			// this.mode = 2;
			// this.confCode = user.getOrgId();//所属机构
			this.confCode = "";
			this.sendUnitCode = "";
		}

		if (StringUtils.isNotEmpty(ledgerModuleId)) {
			String sql = "select * from DIGITAL_LEDGER_MODULE where ID='" + ledgerModuleId + "'";
			List<Map<String, Object>> _list = srv.queryListMap(sql);
			if (StringUtils.isNotEmpty(_list)) {
				Map<String, Object> _map = _list.get(0);
				String tzUnitCode = _map.get("ACCOUNT_OBJ_ID") == null ? ""
						: String.valueOf(_map.get("ACCOUNT_OBJ_ID"));
				if (StringUtils.isNotEmpty(tzUnitCode)) {// 根据配置
					confCode = tzUnitCode;
					sendUnitCode = tzUnitCode;
				}
			}
		}

		// 根据核算对象获取仪表上下限，自定义仪表，在整理输出参数时增加判断，不存在时，根据设置存入上下限，暂不用，用仪表信息的上下限属性
		// if(this.mode == 1) {
		// ulmap.putAll(getTagUplowData(this.confCode, null));
		// }else {
		// List<String> unitList = getOrgUnits();//获取机构对应核算单元列表
		// if(StringUtils.isNotEmpty(unitList)) {
		// //获取对应核算对象对应应用日期
		// Map<String, String> meterVersionMap = getMVMap(unitList);
		// for (String unitId : meterVersionMap.keySet()) {
		// String ver = meterVersionMap.get(unitId);
		// ulmap.putAll(getTagUplowData(this.confCode, ver));
		// }
		// }
		// }
		Map<String, String> confMap = applySrv.getUnitAccountConf(this.sendUnitCode, getDSAlias());
		if ("mobile".equals(this.timeConf.getShowMode())) {// 如果外部模式，判断活动模式，如果巡检模式，自动转换成时间点模式
			// if("tz".equals(this.inputMode)) {
			// timeConf.setShowMode("tz");
			// }
			if ("0".equals(confMap.get("mode"))) {
				timeConf.setShowMode("tz");
				inputMode = "tz";
			}
		}

		// if("synchronize".equals(this.timeConf.getShowMode()))
		// {//外部获取采集点，一定会返回结果（外部未取到，会从采集点获取）
		// ApplyParams p = new ApplyParams();
		// p.setRq(version);
		// p.setUnitCode(confCode);
		// p.setItemCode(transferUnitCode(confCode));
		// p.setBc(shiftCode);
		// p.setSbsj(timeBoundBc1);
		// p.setXbsj(timeBoundBc2);
		// mlist = applySrv.getExtPotList(p);
		//
		// this.infoContent="unitCode@"+p.getItemCode()+";sendUnitCode@"+sendUnitCode;
		// }
		if ("mobile".equals(this.timeConf.getShowMode()) || "tz".equals(this.timeConf.getShowMode())
				|| "synchronize".equals(this.timeConf.getShowMode())) {// 外部获取采集点
			ApplyParams p = new ApplyParams();
			p.setRq(version);
			p.setUnitCode(confCode);
			p.setItemCode(transferUnitCode(confCode));
			p.setBc(shiftCode);
			p.setSbsj(timeBoundBc1);
			p.setXbsj(timeBoundBc2);
			p.setTeamId(getEffectiveOrgCode()); // 优先使用传入的orgcode，如果为空使用用户机构
			mlist = applySrv.getExtPotList(p);

			// if(StringUtils.isEmpty(mlist)) {//暂时测试返回仪表
			// mlist = getDefaultMeter(getDSAlias(), confCode);
			// if(mlist.size() == 3) {
			// mlist.get(0).setControltype(2);
			// mlist.get(1).setControltype(2);
			// mlist.get(2).setControltype(1);
			// }
			// }

			this.infoContent = "unitCode@" + p.getItemCode() + ";sendUnitCode@" + sendUnitCode;
		} else if (this.mode == 1) {// 传入核算对象
			// if(StringUtils.isNotEmpty(version) && StringUtils.isNotEmpty(confCode) &&
			// StringUtils.isNotEmpty(shiftCode)) {
			// accountServ.updateTodoMark(version, confCode, shiftCode, accountId == null ?
			// "" : accountId, getDSAlias());
			// }
			String sendUnitCode = this.confCode + "";

			String newUnitCode = transferUnitCode(this.confCode);// 核算单元转换，核算单元指向多个核算对象
			this.infoContent = "unitCode@" + this.confCode + ";sendUnitCode@" + sendUnitCode;
			List<String> unitlist = Coms.StrToList(newUnitCode, ",");
			if (unitlist.size() > 1) {
				// 缓存读取数据
				String tagType = "3".equals(conf.getTagType()) ? "3" : "2";
				// String bc = shiftCode == null ? "" : shiftCode;
				String aid = accountId == null ? "" : accountId;
				// String sendrq = timeBoundBc1.length() >= 10 ? timeBoundBc1.substring(0, 10) :
				// this.version;
				String verrq = applySrv.getAaccuntVerRq(confCode, version);
				String redisKey = confCode + "_" + verrq + "_" + aid + "_" + tagType;
				List<Map> rlist = redis.getMapValue(accountKey, redisKey);
				List<TdsAccountMeter> redisList = new ArrayList<TdsAccountMeter>();
				if (rlist != null && rlist.size() > 0) {
					for (Map m : rlist) {
						redisList.add(ObjUtils.convertToObject(TdsAccountMeter.class, m));
					}
				}

				if (StringUtils.isNotEmpty(redisList)) {
					mlist = redisList;
				} else {
					List<TdsAccountMeter> tlist = new ArrayList<TdsAccountMeter>();
					for (String ucode : unitlist) {
						List<TdsAccountMeter> _list = null;
						List<String> tagIdList = new ArrayList<String>();
						if (StringUtils.isNotEmpty(this.accountId)) {
							String trq = timeBoundBc1.length() >= 10 ? timeBoundBc1.substring(0, 10) : this.version;
							List<TdsAccountFormMeter> fmlist = accountServ.getFormMeterList(this.accountId, ucode, trq,
									"3".equals(conf.getTagType()) ? "3" : "2");
							if (StringUtils.isNotEmpty(fmlist)) {
								for (TdsAccountFormMeter fmeter : fmlist) {
									tagIdList.add(fmeter.getTagid());
								}
							}
						}
						if (StringUtils.isEmpty(tagIdList)) {// 未设置获取全部仪表
							_list = getDefaultMeter(getDSAlias(), ucode);
						} else {
							_list = getAccountTagCustom(ucode, tagIdList);
						}
						if (StringUtils.isNotEmpty(_list)) {
							tlist.addAll(_list);
						}
					}
					mlist = tlist;

					// JSONArray jarr = new JSONArray();
					//// List<TdsTagDto> ttlist = new ArrayList<TdsTagDto>();
					// for (TdsAccountMeter m : mlist) {
					//// ttlist.add(ObjUtils.copyTo(m, TdsTagDto.class));
					// TdsTagDto obj = ObjUtils.copyTo(m, TdsTagDto.class);
					// JSONObject jsonObject = (JSONObject)
					// JSONObject.parseObject(JSONObject.toJSONString(obj));
					// jarr.add(jsonObject);
					// }
					//
					// redis.setMapValue(accountKey, redisKey, jarr);
				}
			} else {
				// 缓存读取数据
				String tagType = "3".equals(conf.getTagType()) ? "3" : "2";
				// String bc = shiftCode == null ? "" : shiftCode;
				String aid = accountId == null ? "" : accountId;
				// String sendrq = timeBoundBc1.length() >= 10 ? timeBoundBc1.substring(0, 10) :
				// this.version;
				String verrq = applySrv.getAaccuntVerRq(confCode, version);
				String redisKey = confCode + "_" + verrq + "_" + aid + "_" + tagType;

				List<Map> rlist = redis.getMapValue(accountKey, redisKey);
				List<TdsAccountMeter> redisList = new ArrayList<TdsAccountMeter>();
				if (rlist != null && rlist.size() > 0) {
					for (Map m : rlist) {
						redisList.add(ObjUtils.convertToObject(TdsAccountMeter.class, m));
					}
				}

				if (StringUtils.isNotEmpty(redisList)) {
					mlist = redisList;
				} else {
					List<String> tagIdList = new ArrayList<String>();
					if (StringUtils.isNotEmpty(this.accountId)) {
						String trq = timeBoundBc1.length() >= 10 ? timeBoundBc1.substring(0, 10) : this.version;
						List<TdsAccountFormMeter> fmlist = accountServ.getFormMeterList(this.accountId, this.confCode,
								trq, "3".equals(conf.getTagType()) ? "3" : "2");
						if (StringUtils.isNotEmpty(fmlist)) {
							for (TdsAccountFormMeter fmeter : fmlist) {
								tagIdList.add(fmeter.getTagid());
							}

						}
					}
					if (StringUtils.isEmpty(tagIdList)) {// 未设置获取全部仪表
						mlist = getDefaultMeter(getDSAlias(), this.confCode);
						if (mlist == null) {
							mlist = new ArrayList<TdsAccountMeter>();
						}
					} else {
						mlist = getAccountTagCustom(confCode, tagIdList);
					}

					List<TdsTagDto> ttlist = new ArrayList<TdsTagDto>();
					for (TdsAccountMeter m : mlist) {
						ttlist.add(ObjUtils.copyTo(m, TdsTagDto.class));
					}
					redis.setMapValue(accountKey, redisKey, ttlist);
				}
			}
		} else {
			mlist = accountServ.getAccountMeterList(getDSAlias(), this.confCode, null);// 动态仪表配置
			if (StringUtils.isEmpty(mlist)) {// 未设置获取全部仪表
				mlist = getDefaultMeter(getDSAlias(), this.confCode);
				if (mlist == null) {
					mlist = new ArrayList<TdsAccountMeter>();
				}
			}
		}

		List<String> colList = null;
		if (StringUtils.isNotEmpty(mlist)) {
			tagIdObjMap = new HashMap<String, TdsAccountMeter>();
			tagCodeIdMap = new HashMap<String, String>();
			colList = new ArrayList<String>();
			for (TdsAccountMeter obj : mlist) {
				if (StringUtils.isNotEmpty(obj.getDatasource())) {// 仪表位号统一转大写
					obj.setDatasource(obj.getDatasource().toUpperCase());
				}
				tagIdObjMap.put(obj.getTagid(), obj);
				if (StringUtils.isNotEmpty(obj.getDatasource()) && !tagCodeIdMap.containsKey(obj.getDatasource())) {
					tagCodeIdMap.put(obj.getDatasource().toUpperCase(), obj.getTagid());
				}
				colList.add(obj.getTagid());
				// //广东暂无方案，按默认上下限处理
				// Double uplimit = obj.getUpLimit();
				// Double lowlimit = obj.getLowerLimit();
			}
			// 查找相关汇总设置，列出并查询汇总数据，添加行
		}
		// tagIdList = colList;
		// if(StringUtils.isNotEmpty(mlist)) {
		// meterIdMap = new HashMap<String, String>();
		// for (TdsAccountMeter obj : mlist) {
		// String col = obj.getDatasource();
		// if(StringUtils.isEmpty(col)) {
		// col = obj.getTagid();
		// }
		// meterIdMap.put(col, obj.getTagid());
		// }
		// }

		// 获取时间配置对应的时间点数据
		initTimeConfList();
		// 获取累计配置
		initCountInfo();
		// 根据方案获取仪表上下限
		if ("pwl".equals(tagType) && this.useFa) {// lims不取上下限 //!"bound".equals(this.timeConf.getShowMode()) &&
			initTagUpLowLimit(colList);
		}
		// 根据时间和列信息，获取备注信息
		initMarkInfo(colList);

		this.inparamAliasFront = this.getInParaAliasFront();
		Map<String, String> stringStringMap = this.parseInParams(inparamAliasFront);
		if (StringUtils.isNotEmpty(stringStringMap)) {
			if (StringUtils.isEmpty(ledgerFormId)) {
				ledgerFormId = stringStringMap.get("ledgerComponentId");
			}
		}

	}

	private String transferUnitCode(String unitCode) {
		// 查询核算单元是否有指向，如果有，转换核算单元代码，否则，直接返回本核算单元
		try {
			String sql = "select * from COSTUINT where ID='" + unitCode + "'";
			List<Map<String, Object>> list = srv.queryListMap(sql);
			if (StringUtils.isNotEmpty(list)) {
				String unitId = list.get(0).get("DEVICEIDS") == null ? null
						: String.valueOf(list.get(0).get("DEVICEIDS"));
				if (StringUtils.isNotEmpty(unitId)) {
					return unitId;
				}
			}
		} catch (Exception e) {
		}

		return unitCode;
	}

	private void initMarkInfo(List<String> colList) {
		if (StringUtils.isNotEmpty(colList)) {
			List<TdsAccountMarkinfo> mlist = accountServ.getTdsAccountAllMarkData(dsAlias, st, et, colList);
			if (StringUtils.isNotEmpty(mlist)) {
				markMap = new HashMap<String, String>();
				for (TdsAccountMarkinfo obj : mlist) {
					markMap.put(obj.getMarkKey(), obj.getMarkInfo());
				}
			}
		}
	}

	private void initTagUpLowLimit(List<String> colList) {
		// 无方案切换信息，按默认方案显示
		/*
		 * 获取核算对象的方案切换数据
		 * 按序判断当前时段方案
		 * 获取对应方案仪表上下限，整理数据信息 map <tagId, 整体json信息> ，为减少内容，需过滤显示的仪表信息
		 * 按当前台账时间点判断并整理各时间点的数据map ulContentMap
		 */
		if (StringUtils.isNotEmpty(colList)) {
			List<String> unitIdList = Coms.StrToList(this.confCode, ",");
			tagFaULmap = new HashMap<String, List<Map<String, Object>>>();// 仪表对应方案的时间范围和上下限信息
			DataAccountTools dat = new DataAccountTools();
			fasjlist = new ArrayList<String>();
			dat.getProgramInfo(unitIdList, colList, timeList, null, null, tagFaULmap, fasjlist);// 暂时不考虑一个采集点出现在多个核算对象里的情况
			// getProgramInfo(unitIdList, colList);
		}
	}

	private void getProgramInfo(List<String> unitIdList, List<String> colList) {

		/*
		 * 获取所有核算对象列表
		 * 循环核算对象列表，获取相关所有方案切换数据
		 * 根据核算对象、方案切换时间、当前显示时间点，获取使用了哪些方案 -
		 * 循环使用方案，根据当前采集点，获取方案仪表上下限，记录仪表对应方案的时间范围和上下限信息
		 */

		// Map<String, List<Map<String, Object>>> jgmap = new HashMap<String,
		// List<Map<String,Object>>>();//仪表对应方案的时间范围和上下限信息

		StringBuffer sb = new StringBuffer();
		for (String unitId : unitIdList) {
			sb.append(",'");
			sb.append(unitId);
			sb.append("'");
		}
		if (sb.length() > 0) {
			String unitstr = sb.substring(1);
			String sql = "select STARTDATETIME,ENDDATETIME,PROGRAMID,UNITID from PRODUCTSCHEDU_PLAN_START where UNITID in("
					+ unitstr + ") order by UNITID,STARTDATETIME";
			List<Map<String, Object>> plist = getResultList(sql);

			Map<String, List<Map<String, String>>> umap = new HashMap<String, List<Map<String, String>>>();// 核算对象所有方案信息

			Map<String, List<Map<String, String>>> ufamap = new HashMap<String, List<Map<String, String>>>();// 核算对象当前时间点范围内容使用的方案

			// 按核算对象，整理各核算对象的方案切换数据
			for (Map<String, Object> map : plist) {
				String unitId = String.valueOf(map.get("UNITID"));

				if ("null".equalsIgnoreCase(unitId)) {
					continue;
				}

				String programId = String.valueOf(map.get("PROGRAMID"));
				String srq = String.valueOf(map.get("STARTDATETIME"));
				String erq = String.valueOf(map.get("ENDDATETIME"));

				Map<String, String> tmap = new HashMap<String, String>();
				tmap.put("srq", srq);
				tmap.put("erq", erq);
				tmap.put("programId", programId);

				if (umap.containsKey(unitId)) {
					umap.get(unitId).add(tmap);
				} else {
					List<Map<String, String>> tlist = new ArrayList<Map<String, String>>();
					tlist.add(tmap);
					umap.put(unitId, tlist);
				}
			}

			for (String unitId : umap.keySet()) {// 循环核算对象
				List<Map<String, String>> list = umap.get(unitId);
				if (StringUtils.isNotEmpty(list)) {
					for (Map<String, String> map : list) {// 循环核算对象方案
						String srq = map.get("srq");
						String erq = map.get("erq");
						String programId = map.get("programId");

						for (Date d : this.timeList) {// 循环时间点
							String dstr = DateTimeUtils.formatDateTime(d);
							if (dstr.compareTo(srq) >= 0 && dstr.compareTo(erq) < 0) {// 根据时间范围判断是否应用方案

								// 记录所有核算对象对应的方案信息
								if (ufamap.containsKey(unitId)) {
									Map<String, String> famap = new HashMap<String, String>();
									famap.put("srq", srq);
									famap.put("erq", erq);
									famap.put("programId", programId);
									ufamap.get(unitId).add(famap);
								} else {
									List<Map<String, String>> falist = new ArrayList<Map<String, String>>();
									Map<String, String> famap = new HashMap<String, String>();
									famap.put("srq", srq);
									famap.put("erq", erq);
									famap.put("programId", programId);
									falist.add(famap);
									ufamap.put(unitId, falist);
								}

								break;
							}
						}
					}
				}
			}
			// 整理各时间点方案变化信息
			if (ufamap != null && ufamap.size() > 0) {
				fasjlist = new ArrayList<String>();

				List<String> oldlist = new ArrayList<String>();
				for (Date d : this.timeList) {// 循环时间点，判断各时间点，是否有不同方案并记录对应时间点的方案记录，在输出时判断
					String dstr = DateTimeUtils.formatDateTime(d);
					List<String> tlist = new ArrayList<String>();
					for (String unitId : ufamap.keySet()) {
						List<Map<String, String>> falist = ufamap.get(unitId);
						for (Map<String, String> tmap : falist) {
							String srq = tmap.get("srq");
							String erq = tmap.get("erq");
							if (dstr.compareTo(srq) >= 0 && dstr.compareTo(erq) < 0) {// 根据时间范围判断是否应用方案
								tlist.add(unitId + ",,," + tmap.get("programId"));
								break;
							}
						}
					}
					boolean changeFa = false;
					if (StringUtils.isNotEmpty(tlist)) {
						if (oldlist.isEmpty()) {
							changeFa = true;
						} else {
							if (oldlist.size() == tlist.size() && oldlist.containsAll(tlist)) {
							} else {// 有方案变化
								changeFa = true;
							}
						}
					}
					if (changeFa) {
						fasjlist.add(dstr);
						oldlist = tlist;
					}
				}
			}

			// 按核算对象加载方案数据，并过滤除当前有的仪表
			Map<String, Map<String, List<Double>>> hmap = new HashMap<String, Map<String, List<Double>>>();
			for (String unitId : ufamap.keySet()) {// 循环核算对象
				List<Map<String, String>> falist = ufamap.get(unitId);

				for (Map<String, String> fmap : falist) {// 循环方案

					String srq = fmap.get("srq");
					String erq = fmap.get("erq");
					String programId = fmap.get("programId");

					String key = unitId + "_" + programId;

					Map<String, List<Double>> map = null;
					if (hmap.containsKey(key)) {
						map = hmap.get(key);
					} else {
						map = getTagUplowData(unitId, srq, programId, colList);// 获取方案相关仪表上下限
						hmap.put(key, map);
					}
					if (map != null && map.size() > 0) {// 方案所有仪表上下限
						for (String tagId : map.keySet()) {
							List<Double> ullist = map.get(tagId);

							Map<String, Object> taginfo = new HashMap<String, Object>();
							taginfo.put("srq", srq);
							taginfo.put("erq", erq);
							taginfo.put("up", ullist.get(0));
							taginfo.put("low", ullist.get(1));
							taginfo.put("used", ullist.get(4));

							List<Map<String, Object>> tlist = tagFaULmap.get(tagId);// 理论上，每个仪表各方案时间不会重复，按顺序整理每个仪表各个时间段的上下限
							if (StringUtils.isNotEmpty(tlist)) {
								tagFaULmap.get(tagId).add(taginfo);
							} else {
								List<Map<String, Object>> _list = new ArrayList<Map<String, Object>>();
								_list.add(taginfo);
								tagFaULmap.put(tagId, _list);
							}
						}
					}
				}
			}

		}
	}

	// private void getTagULInfo(String unitCode) {
	//
	// }
	/**
	 * @category 初始化数据源行统计信息
	 */
	private void initCountInfo() {
		if (StringUtils.isNotEmpty(this.mlist)) {// 目前先用仪表，后期需要增加自定义输出列的处理 TODO
			List<String> tagIdList = new ArrayList<String>();
			for (String tagId : tagIdObjMap.keySet()) {
				tagIdList.add(tagId);
			}
			// 根据仪表信息判断，是否有统计配置
			countList = accountServ.getCountConfList(dsAlias, tagIdList);
			// 有统计信息，获取对应仪表统计数据 MongoDB
			// 判断有无班次信息shiftCode，查询相关班次对应日期数据；如果无班次，直接查日期统计数据
			// List<String> rowList = new ArrayList<String>();//后期优化，通过MongoDB统一获取，目前先分步获取
			// List<String> tagList = new ArrayList<String>();
			for (TdsAccountCountConfVo conf : countList) {
				// rowList.add(conf.getId());
				List<TdsAccountCountCol> clist = conf.getColList();
				if (StringUtils.isNotEmpty(clist)) {
					List<String> colIdList = new ArrayList<String>();
					for (TdsAccountCountCol cc : clist) {
						colIdList.add(cc.getColMark());
					}
					List<Map> jgmap = accountServ.getAccountCountData(
							this.accountTabName + "_" + getDSAlias() + "_count", getDSAlias(), conf.getId(),
							this.version, this.shiftCode, colIdList);
					if (jgmap != null) {
						if (countMap == null) {
							countMap = new HashMap<String, String>();
						}
						// jgmap.sort(Comparator.comparing(o -> String.valueOf(o.get("val"))));
						for (Map map : jgmap) {
							String col = String.valueOf(map.get("col"));// 仪表相当于COLID
							String valstr = String.valueOf(map.get("valstr"));// 结果
							valstr = valstr == null ? "" : valstr;
							countMap.put(conf.getId() + "_" + col, valstr);
						}
					}
				}
			}
		}
	}

	/**
	 * @category 计算时间配置的时间列表
	 */
	private void initTimeConfList() {
		initTimeConfList(null);
	}

	/**
	 * @category 计算时间配置的时间列表
	 */
	private void initTimeConfList(String conf) {
		// 整理时间信息
		// List<Date> timeList = new ArrayList<Date>();
		if (this.timeConf != null) {
			if ("synchronize".equals(this.timeConf.getShowMode()) && conf == null) {// 根据核算对象判断（定期/不定期)，先从接口获取，再判断，定期，根据配置推；不定期，接口获取
				// 接口获取时间点
				Date nd = DateTimeUtils.getNowDate();
				ApplyParams p = new ApplyParams();
				p.setRq(version);
				p.setUnitCode(confCode);
				p.setItemCode(transferUnitCode(confCode));
				p.setBc(shiftCode);
				p.setSbsj(timeBoundBc1);
				p.setXbsj(timeBoundBc2);
				p.setApplyMode("synchronize");
				p.setTeamId(getEffectiveOrgCode()); // 优先使用传入的orgcode，如果为空使用用户机构
				List<Map<String, Object>> tlist = applySrv.getExtTimeList(p);

				if (StringUtils.isNotEmpty(tlist)) {
					infoTimeIdMap = new HashMap<Date, String>();
					infoTimeEditMap = new HashMap<Date, String>();
					jobInputTimeIdMap = new LinkedHashMap<>();
					for (Map<String, Object> map : tlist) {
						String sj = String.valueOf(map.get("sj"));
						String jobInputTime = String.valueOf(map.get("jobInputTime"));
						String id = String.valueOf(map.get("id"));
						String editMark = String.valueOf(map.get("edit"));
						timeList.add(DateTimeUtils.parseDateTime(sj));
						infoTimeIdMap.put(DateTimeUtils.parseDateTime(sj), id);
						infoTimeEditMap.put(DateTimeUtils.parseDateTime(sj), editMark);
						jobInputTimeIdMap.put(DateTimeUtils.parseDateTime(sj), jobInputTime);
					}
					st = DateTimeUtils.formatDateTime(timeList.get(0));
					et = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));

					if (StringUtils.isNotEmpty(timeList)) {
						sjmap = new LinkedHashMap<Date, String>();
						for (Date date : timeList) {
							sjmap.put(date, DateTimeUtils.format(date, this.timeConf.getTimeFormat()));
							if (DateTimeUtils.bjDate(nd, date) == 1) {
								showTimeList.add(DateTimeUtils.formatDateTime(date));
							}
						}
					}
				} else {
					// 核算对象如果是定期，进行自动推算
					// String newUnitCode = transferUnitCode(this.confCode);
					// List<String> ulist = Coms.StrToList(newUnitCode, ",");
					Map<String, Object> uinfomap = applySrv.getUnitInfoMap(sendUnitCode);
					Boolean dqFlag = new Integer(1).equals(uinfomap.get("isOntime"));// 定期标识
					if (dqFlag) {
						initTimeConfList("exp");
					}
				}

				if ("bc".equals(timeConf.getStartBingDay()) && shiftCode != null) {// 使用班次
					this.infoContent += ";bc@" + this.shiftCode;
				} else {// 不使用班次
					this.shiftCode = null;
				}
				if (StringUtils.isNotEmpty(this.infoContent)) {
					this.infoContent += ";";
				}
				this.infoContent += "sj@" + this.timeBoundBc1 + "," + this.timeBoundBc2;
				this.infoContent += ";rq@" + this.version;
				this.infoContent += ";accountId@" + this.accountId;
				this.infoContent += ";formDataId@" + this.dataId;
			} else if (("mobile".equals(this.timeConf.getShowMode()) || "tz".equals(this.timeConf.getShowMode()))
					&& conf == null) {// 如果外部模式，判断活动模式，如果巡检模式，自动转换成时间点模式

				Date nd = DateTimeUtils.getNowDate();
				ApplyParams p = new ApplyParams();
				p.setRq(version);
				p.setUnitCode(confCode);
				p.setItemCode(transferUnitCode(confCode));
				p.setBc(shiftCode);
				p.setSbsj(timeBoundBc1);
				p.setXbsj(timeBoundBc2);
				p.setApplyMode("mobile");
				p.setTeamId(getEffectiveOrgCode()); // 优先使用传入的orgcode，如果为空使用用户机构
				List<Map<String, Object>> tlist = applySrv.getExtTimeList(p);

				if (StringUtils.isNotEmpty(tlist)) {
					jobInputTimeIdMap = new LinkedHashMap<>();
					infoTimeIdMap = new HashMap<Date, String>();
					infoTimeEditMap = new HashMap<Date, String>();
					for (Map<String, Object> map : tlist) {
						String sj = String.valueOf(map.get("sj"));
						String jobInputTime = String.valueOf(map.get("jobInputTime"));
						String id = String.valueOf(map.get("id"));
						String editMark = String.valueOf(map.get("edit"));
						timeList.add(DateTimeUtils.parseDateTime(sj));
						infoTimeIdMap.put(DateTimeUtils.parseDateTime(sj), id);
						infoTimeEditMap.put(DateTimeUtils.parseDateTime(sj), editMark);
						jobInputTimeIdMap.put(DateTimeUtils.parseDateTime(sj), jobInputTime);
					}
					st = DateTimeUtils.formatDateTime(timeList.get(0));
					et = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
				}

				if (StringUtils.isNotEmpty(timeList)) {
					sjmap = new LinkedHashMap<Date, String>();
					for (Date date : timeList) {
						sjmap.put(date, DateTimeUtils.format(date, this.timeConf.getTimeFormat()));
						if (DateTimeUtils.bjDate(nd, date) == 1) {
							showTimeList.add(DateTimeUtils.formatDateTime(date));
						}
					}
				}

				// if("mobile".equals(this.timeConf.getShowMode())) {//移动端数据外部获取模式
				// //加载外部数据时间点列表
				// try {
				// loadMobileTimeList();
				// if(StringUtils.isNotEmpty(timeList)) {
				// st = DateTimeUtils.formatDateTime(timeList.get(0));
				// et = DateTimeUtils.formatDateTime(timeList.get(timeList.size()-1));
				// if(sjmap == null) {
				// sjmap = new LinkedHashMap<Date, String>();
				// }
				// for (Date date : timeList) {
				// sjmap.put(date, DateTimeUtils.format(date, this.timeConf.getTimeFormat()));
				// if(DateTimeUtils.bjDate(nd, date) == 1) {
				// showTimeList.add(DateTimeUtils.formatDateTime(date));
				// }
				// }
				// }
				// } catch (Exception e) {
				// }
				// }else if("tz".equals(this.timeConf.getShowMode())) {//数据外部获取模式
				// //通过接口获取活动时间点 TODO
				// }

				if ("bc".equals(timeConf.getStartBingDay()) && shiftCode != null) {// 使用班次
					this.infoContent += ";bc@" + this.shiftCode;
				} else {// 不使用班次
					this.shiftCode = null;
				}
				if (StringUtils.isNotEmpty(this.infoContent)) {
					this.infoContent += ";";
				}
				this.infoContent += "sj@" + this.timeBoundBc1 + "," + this.timeBoundBc2;
				this.infoContent += ";rq@" + this.version;
				this.infoContent += ";accountId@" + this.accountId;
				this.infoContent += ";formDataId@" + this.dataId;
			} else {// 自己推时间

				Date startDate = null;
				String startRq = null;
				String startBind = this.timeConf.getStartBingDay(); // 开始绑定时间
				String startFix = this.timeConf.getStartFixed(); // 开始日期浮动
				Boolean haveStart = new Integer(1).equals(this.timeConf.getStartRound()); // 是否包含开始时间
				int istartfix = 0;

				Date endDate = null;
				String endRq = null;
				String endBind = this.timeConf.getEndBingDay();
				String endFix = this.timeConf.getEndFixed(); // 截止日期浮动
				Boolean haveEnd = new Integer(1).equals(this.timeConf.getEndRound());
				int iendfix = 0;
				if (Coms.judgeLong(startFix)) {
					istartfix = Integer.parseInt(startFix);
				}
				if (Coms.judgeLong(endFix)) {
					iendfix = Integer.parseInt(endFix);
				}
				// 开始时间获取
				if (StringUtils.isNotEmpty(startBind)) {
					// //绑定输入参数
					// if (StringUtils.isNotEmpty(this.tInParas)) {
					// for (TInPara temp : this.tInParas) {
					// if(temp.getParaAlias().equals(startBind)) {
					// startRq = temp.getValue()!=null?String.valueOf(temp.getValue()):null;
					// break;
					// }
					// }
					// if(StringUtils.isNotEmpty(startRq)) {
					// Date rq = DateTimeUtils.parseDate(startRq);
					// if(rq == null) {
					// startRq = null;
					// }else {
					// startRq = DateTimeUtils.formatDate(rq, DateTimeUtils.DateFormat_YMD);
					// }
					// }
					// }
					// 绑定台账内部绑定
					if ("day".equals(startBind) || "mon".equals(startBind)) {
						startRq = this.timeBoundDay;
						Object o = startRq;
						if (DateTimeUtils.parseDate(o) == null) {
							startRq = null;
						} else {
							startRq = startRq.substring(0, 10);
						}
					} else if ("bc".equals(startBind)) {
						startRq = this.timeBoundBc1;
						Object o = startRq;
						if (DateTimeUtils.parseDate(o) == null) {
							startRq = null;
						} else {
							startRq = startRq.substring(0, 19);
						}
					}
				}
				if (StringUtils.isEmpty(startRq)) {
					if (istartfix != 0) {
						startRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " "
								+ this.timeConf.getStartBingTime() + ":00";
						startDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(startRq), istartfix);
					} else {
						startRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " "
								+ this.timeConf.getStartBingTime() + ":00";
						startDate = DateTimeUtils.parseDateTime(startRq);
					}
				} else {
					if ("bc".equals(startBind)) {
						startDate = DateTimeUtils.parseDateTime(startRq);
						if (this.conf.getBcDiff() != null) {// 上班时间偏差修正
							int bcdiff = this.conf.getBcDiff();
							startDate = DateTimeUtils.doMinute(startDate, bcdiff);
						}
					} else {
						if (istartfix != 0) {
							startDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(
									startRq + " " + this.timeConf.getStartBingTime() + ":00"), istartfix);
						} else {
							startDate = DateTimeUtils
									.parseDateTime(startRq + " " + this.timeConf.getStartBingTime() + ":00");
						}
					}
				}
				// 截止时间获取
				if (StringUtils.isNotEmpty(endBind)) {
					// //绑定输入参数
					// if (StringUtils.isNotEmpty(this.tInParas)) {
					// for (TInPara temp : this.tInParas) {
					// if(temp.getParaAlias().equals(endBind)) {
					// endRq = temp.getValue()!=null?String.valueOf(temp.getValue()):null;
					// break;
					// }
					// }
					// if(StringUtils.isNotEmpty(endRq)) {
					// Date rq = DateTimeUtils.parseDate(endRq);
					// if(rq == null) {
					// endRq = null;
					// }else {
					// endRq = DateTimeUtils.formatDate(rq, DateTimeUtils.DateFormat_YMD);
					// }
					// }
					// }
					// 绑定台账内部绑定
					if ("day".equals(endBind) || "mon".equals(endBind)) {
						endRq = this.timeBoundDay;
						Object o = endRq;
						if (DateTimeUtils.parseDate(o) == null) {
							endRq = null;
						} else {
							endRq = endRq.substring(0, 10);
						}
					} else if ("bc".equals(endBind)) {
						endRq = this.timeBoundBc2;
						Object o = endRq;
						if (DateTimeUtils.parseDate(o) == null) {
							endRq = null;
						} else {
							endRq = endRq.substring(0, 19);
						}
					}
				}
				if (StringUtils.isEmpty(endRq)) {
					if (iendfix != 0) {
						endRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " " + this.timeConf.getEndBingTime()
								+ ":00";
						endDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(endRq), iendfix);
					} else {
						endRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " " + this.timeConf.getEndBingTime()
								+ ":00";
						endDate = DateTimeUtils.parseDateTime(endRq);
					}
				} else {
					if ("bc".equals(endBind)) {
						endDate = DateTimeUtils.parseDateTime(endRq);
						if (this.conf.getBcDiff() != null) {// 下班时间偏差修正
							int bcdiff = this.conf.getBcDiff();
							endDate = DateTimeUtils.doMinute(endDate, bcdiff);
						}
					} else {
						if (iendfix != 0) {
							endDate = DateTimeUtils.doDate(
									DateTimeUtils.parseDateTime(endRq + " " + this.timeConf.getEndBingTime() + ":00"),
									iendfix);
						} else {
							endDate = DateTimeUtils.parseDateTime(endRq + " " + this.timeConf.getEndBingTime() + ":00");
						}
					}
				}

				if (DateTimeUtils.bjDate(startDate, endDate) == 1) {// 如果时间不对，不获取数据
					// return map;
				} else {

					Date tempDate = startDate;
					String timeStep = this.timeConf.getTimeStep();
					Integer step = 60;
					if (Coms.judgeLong(timeStep)) {
						step = Integer.parseInt(timeStep);
					}
					// 已弃用
					// if("mobile".equals(this.timeConf.getShowMode())) {//移动端数据外部获取模式
					// //加载外部数据时间点列表
					// try {
					// loadMobileTimeList();
					// } catch (Exception e) {
					// }
					// }else if("tz".equals(this.timeConf.getShowMode())) {//数据外部获取模式
					// //通过接口获取活动时间点 TODO
					//
					//
					// } else
					{

						if (haveStart) {
							timeList.add(startDate);
							tempDate = DateTimeUtils.doMinute(tempDate, step);
						}

						for (int i = 0, il = 50; i < il && DateTimeUtils.bjDate(endDate, tempDate) > 0; i++) {// 时间对比，增加循环数，避免死循环
							timeList.add((Date) tempDate.clone());
							tempDate = DateTimeUtils.doMinute(tempDate, step);
						}

						if (DateTimeUtils.bjDate(tempDate, endDate) == 1) {
							if (haveEnd || "bound".equals(this.timeConf.getShowMode())) {
								if (!timeList.contains(endDate)) {
									timeList.add(endDate);
								}
								if ("bound".equals(this.timeConf.getShowMode())) {

								} else if (timeList.size() > 1) {
									// 需要取最小公约数
									long diffMin = Math
											.abs(DateTimeUtils.diffDate(endDate, tempDate, DateTimeUtils.MINITE));
									int imin = Integer.parseInt(String.valueOf(diffMin));
									if (imin == 0) {
										this.istep = step;
									} else {
										this.istep = commonDivisor(60, imin);// 计算最大公约数
									}
									this.st = DateTimeUtils.formatDateTime(timeList.get(0));
									this.et = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
								}
							}
						} else if (DateTimeUtils.bjDate(tempDate, endDate) == 0) {
							// 间隔固定
							if (haveEnd || "bound".equals(this.timeConf.getShowMode())) {
								timeList.add(endDate);
							}
						}
					}
					if (this.st == null && timeList.size() > 0) {
						this.st = DateTimeUtils.formatDateTime(timeList.get(0));
						this.et = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
						this.istep = step;
					}

					if (StringUtils.isNotEmpty(timeList)) {
						Date nd = DateTimeUtils.getNowDate();
						sjmap = new LinkedHashMap<Date, String>();
						for (Date date : timeList) {
							sjmap.put(date, DateTimeUtils.format(date, this.timeConf.getTimeFormat()));
							if (DateTimeUtils.bjDate(nd, date) == 1) {
								showTimeList.add(DateTimeUtils.formatDateTime(date));
							}
						}
					}
				}

				if ("bc".equals(startBind) && "bc".equals(endBind) && shiftCode != null) {// 使用班次
					this.infoContent += ";bc@" + this.shiftCode;
				} else {// 不使用班次
					this.shiftCode = null;
				}

				if (StringUtils.isNotEmpty(this.infoContent)) {
					this.infoContent += ";";
				}
				this.infoContent += "sj@" + this.st + "," + this.et;
				this.infoContent += ";rq@" + this.version;
				this.infoContent += ";accountId@" + this.accountId;

				if ("exp".equals(conf)) {// PC端第一次自动获取数据状态，记录状态，自动获取数据时，通过接口保存数据
					mobileSaveStatus = true;
				}
			}
		}
	}

	private void loadMobileTimeList() {
		// this.timeList = new ArrayList<Date>();
		StringBuffer whereUnit = new StringBuffer();
		String uid = this.confCode;
		if (uid.indexOf(",") == -1) {
			whereUnit.append("='");
			whereUnit.append(this.confCode);
			whereUnit.append("'");
		} else {
			whereUnit.append(" in(");
			List<String> ulist = Coms.StrToList(this.confCode, ",");
			for (String u : ulist) {
				if (u.length() > 3) {
					whereUnit.append(",");
				}
				whereUnit.append("'");
				whereUnit.append(u);
				whereUnit.append("'");
			}
			whereUnit.append(")");
		}

		List<Object> param = new ArrayList<Object>();
		Date sbsjD = StringUtils.isEmpty(this.timeBoundBc1) ? null : DateTimeUtils.parseDateTime(this.timeBoundBc1);
		String sql = "select ID,INPUT_TIME from ACCTOBJ_INPUT where tmused=1 and BA_ID ='" + sendUnitCode
				+ "' and bcdm=? and sbsj=? and team_id = ? order by input_time";
		if (sbsjD != null) {// 班次模式查询
			param.add(this.shiftCode);
			param.add(sbsjD);
			param.add(getEffectiveOrgCode()); // 优先使用传入的orgcode，如果为空使用用户机构
		} else {// 按日期查询
			if (StringUtils.isNotEmpty(timeBoundDay)) {
				param.add(DateTimeUtils.parseDate(timeBoundDay));
				param.add(DateTimeUtils.doSecond(DateTimeUtils.doMonth(DateTimeUtils.parseDate(timeBoundDay), 1), -1));
			} else {
				Date nd = DateTimeUtils.getNowDate();
				param.add(nd);
				param.add(nd);
			}
			param.add(getEffectiveOrgCode()); // 优先使用传入的orgcode，如果为空使用用户机构
			sql = "select ID,INPUT_TIME from ACCTOBJ_INPUT where tmused=1 and BA_ID ='" + sendUnitCode
					+ "' and INPUT_TIME between ? and ? and team_id = ? order by input_time";
		}

		List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
		if (StringUtils.isNotEmpty(list)) {
			infoTimeIdMap = new HashMap<Date, String>();
			for (Map<String, Object> map : list) {
				// Object time = map.get("INPUT_TIME");
				// if(time!=null && time instanceof java.util.Date) {
				// Date d = (Date)time;
				// this.timeList.add(d);
				// }
				Date d = getMapDate(map, "INPUT_TIME");
				if (d != null) {
					String dtstr = DateTimeUtils.formatDateTime(d);
					d = DateTimeUtils.parseDateTime(dtstr);
					this.timeList.add(d);
				}
				String id = getMapString(map, "ID");
				infoTimeIdMap.put(d, id);
			}
		}
	}

	/**
	 * @category 获取默认仪表配置数据
	 * @param dsAlias
	 * @return
	 */
	private List<TdsAccountMeter> getDefaultMeter(String dsAlias, String ccode) {
		List<TdsAccountMeter> rlist = null;
		// Map<String, Object> param = new HashMap<>();
		// param.put("tdsAlias", dsAlias);
		// String costUrl = sysConfigSrv.getSysConfig("TM4CostServiceUrl");
		// if (StringUtils.isEmpty(costUrl)) {
		// costUrl = "http://127.0.0.1:8887/tm4main";
		// }
		// String res = httpSrv.post(costUrl + "/leanCosting/unitConf/getUnitDotList",
		// param);
		//
		// if (StringUtils.isNotEmpty(res)) {
		// JSONArray meters = JSONArray.parseArray(res);
		// if(meters!=null && meters.size() > 0) {
		// for (int j = 0,jl=meters.size(); j < jl; j++) {
		// JSONObject obj = JSONObject.parseObject(meters.getString(j));
		// String tagnumber = obj.getString("tagnumber");
		// String name = obj.getString("name");
		// if(StringUtils.isNotEmpty(tagnumber)) {
		// if(StringUtils.isNotEmpty(tagnumber)) {
		// name = tagnumber;
		// }
		// TdsAccountMeter meter = new TdsAccountMeter();
		// meter.setShowName(name);
		// meter.setTagnumber(tagnumber);
		// meter.setWidth(120);
		// rlist.add(meter);
		// }
		// }
		// }
		// return rlist;
		// }
		// 解耦，通过sql语句查询默认仪表数据，通过输入参数判断获取核算单元或机构对应仪表
		// if(new Integer(2).equals(this.mode)) {//机构
		// rlist = getMeter(this.confCode, this.mode);
		// }else if(new Integer(1).equals(this.mode)) {//核算对象
		// rlist = getMeter(this.confCode, this.mode);
		// }
		rlist = getMeter(ccode, this.mode);

		return rlist;
	}

	// 通过sql获取默认仪表信息，目前已改为从当前日期版本的台账仪表数据库中获取默认仪表信息
	private List<TdsAccountMeter> getMeter(String confCode2, Integer mode2) {
		List<TdsAccountMeter> rlist = null;
		if (new Integer(2).equals(this.mode)) {// 机构
			List<String> unitList = getOrgUnits();// 获取机构对应核算单元列表
			if (StringUtils.isNotEmpty(unitList)) {
				// 获取对应核算对象对应应用日期
				// Map<String, String> meterVersionMap = getMVMap(unitList);
				// Map<String, TdsAccountTagVersion> tagVersionMap = getVerMap(unitList);
				// 查询核算对象对应仪表
				// rlist = getOrgMeter(meterVersionMap);
				rlist = getOrgTag(unitList);
			}
		} else if (new Integer(1).equals(this.mode)) {// 核算对象
			rlist = getAccountTag(confCode2);
		}
		return rlist;
	}

	// 获取核算对象默认仪表列表
	private List<TdsAccountMeter> getAccountTag(String ccode) {
		String unitId = ccode;
		String rq = timeBoundBc1.length() >= 10 ? timeBoundBc1.substring(0, 10) : this.version;
		List<TdsAccountMeter> rlist = null;//
		if (this.conf.getTagType() != null) {
			rlist = accountServ.getDefaultAccountUnitTagList(unitId, rq, String.valueOf(this.conf.getTagType()));// 过滤仪表类型
																													// 平稳率、lims
		} else {
			rlist = accountServ.getDefaultAccountUnitTagList(unitId, rq);
		}
		return rlist;
	}

	// 获取核算对象默认仪表列表
	private List<TdsAccountMeter> getAccountTagCustom(String ccode, List<String> tagIdList) {
		String unitId = ccode;
		String rq = timeBoundBc1.length() >= 10 ? timeBoundBc1.substring(0, 10) : this.version;
		List<TdsAccountMeter> rlist = null;//
		if (this.conf.getTagType() != null) {
			rlist = accountServ.getDefaultAccountUnitTagList(unitId, rq, String.valueOf(this.conf.getTagType()),
					tagIdList);// 过滤仪表类型 平稳率、lims
		} else {
			rlist = accountServ.getDefaultAccountUnitTagList(unitId, rq);
		}
		return rlist;
	}

	/**
	 * @category 获取核算对象各版本信息
	 * @param unitList
	 * @return
	 */
	private Map<String, TdsAccountTagVersion> getVerMap(List<String> unitList) {
		Map<String, TdsAccountTagVersion> rmap = new HashMap<String, TdsAccountTagVersion>();
		List<TdsAccountTagVersion> list = accountServ.getManyAccountTagVersionList(unitList);

		Map<String, List<TdsAccountTagVersion>> vmap = new HashMap<String, List<TdsAccountTagVersion>>();
		String tempUnit = null;
		for (TdsAccountTagVersion obj : list) {

			if (vmap.containsKey(obj.getUnitcode())) {
				vmap.get(obj.getUnitcode()).add(obj);
			} else {
				List<TdsAccountTagVersion> tlist = new ArrayList<TdsAccountTagVersion>();
				tlist.add(obj);
				vmap.put(obj.getUnitcode(), tlist);
			}
		}

		Date sendDt = DateTimeUtils.parseD(DateTimeUtils.getNowDateStr(), DateTimeUtils.DateFormat_YMD);
		if (StringUtils.isNotEmpty(this.version)) {
			sendDt = DateTimeUtils.parseD(this.version, DateTimeUtils.DateFormat_YMD);
		}
		for (String unitCode : unitList) {
			List<TdsAccountTagVersion> clist = vmap.get(unitCode);
			if (StringUtils.isNotEmpty(clist)) {
				TdsAccountTagVersion mv = null;
				// 从小到大日期
				for (TdsAccountTagVersion ver : clist) {
					Date verdt = DateTimeUtils.parseD(ver.getRq(), DateTimeUtils.DateFormat_YMD);
					if (DateTimeUtils.bjDate(verdt, sendDt) >= 0) {
						mv = ver;
						break;
					}
				}
				if (mv == null) {
					mv = clist.get(0);
				}
				rmap.put(unitCode, mv);
			}
		}

		return rmap;
	}

	/**
	 * @category 获取机构核算对象仪表信息 暂无用
	 * @param unitList
	 * @return
	 */
	private List<TdsAccountMeter> getOrgTag(List<String> unitList) {
		List<TdsAccountMeter> rlist = new ArrayList<TdsAccountMeter>();

		for (String unitId : unitList) {
			List<TdsAccountMeter> list = null;
			if (this.conf.getTagType() != null) {
				list = accountServ.getDefaultAccountUnitTagList(unitId, this.version, this.conf.getTagType());// 过滤仪表类型
																												// 平稳率、lims
			} else {
				list = accountServ.getDefaultAccountUnitTagList(unitId, this.version);
			}
			if (StringUtils.isNotEmpty(list)) {
				rlist.addAll(list);
			}
		}
		return rlist;
	}

	/**
	 * @category 获取核算单元信息
	 * @return
	 */
	private Map<String, String> getUnitMap() {
		Map<String, String> rmap = new HashMap<String, String>();
		String sql = "select ID, NAME from COSTUINT";
		List<Map<String, Object>> list = getResultList(sql);
		if (StringUtils.isNotEmpty(list)) {
			for (Map<String, Object> map : list) {
				String id = map.get("ID") == null ? null : String.valueOf(map.get("ID"));
				String name = map.get("NAME") == null ? "" : String.valueOf(map.get("NAME"));
				if (id != null) {
					rmap.put(id, name);
				}
			}
		}
		return rmap;
	}

	/**
	 * @category 获取核算单元仪表信息 暂无用
	 * @return
	 */
	private List<TdsAccountMeter> getUnitMeter() {
		String unitId = this.confCode;
		String sql = "select BEGINTIME from COSTUNITVERSION where UNITID='" + unitId
				+ "' and BEGINTIME is not null order by BEGINTIME";
		List<Map<String, Object>> vlist = getResultList(sql);
		Date sendDt = DateTimeUtils.parseD(DateTimeUtils.getNowDateStr(), DateTimeUtils.DateFormat_YMD);
		if (StringUtils.isNotEmpty(this.version)) {
			sendDt = DateTimeUtils.parseD(this.version, DateTimeUtils.DateFormat_YMD);
		}
		if (StringUtils.isNotEmpty(vlist)) {
			String mv = "";
			// 从大到小日期
			for (Object vtime : vlist) {
				Date ver = DateTimeUtils.parseD(String.valueOf(vtime), DateTimeUtils.DateFormat_YMD);

				if (DateTimeUtils.bjDate(ver, sendDt) <= 0) {
					mv = DateTimeUtils.formatDate(ver, DateTimeUtils.DateFormat_YMD);
					break;
				}
			}
			if ("".equals(mv)) {
				mv = String.valueOf(vlist.get(0).get("BEGINTIME"));
			}
			sql = "select NAME, TAGNUMBER,DATASOURCE,SDUNIT from COSTUNITSAMPLEDOT where TMUSED=1 and UNITID='" + unitId
					+ "' and BEGINTIME='" + mv + "' order by UNITID, TMSORT";
			List<Map<String, Object>> list = getResultList(sql);

			List<TdsAccountMeter> rlist = new ArrayList<TdsAccountMeter>();
			for (Map<String, Object> map : list) {
				String tagnumber = map.get("TAGNUMBER") == null ? null : String.valueOf(map.get("TAGNUMBER"));
				if (StringUtils.isNotEmpty(tagnumber)) {
					String name = map.get("NAME") == null ? tagnumber : String.valueOf(map.get("NAME"));
					String datasource = map.get("DATASOURCE") == null ? "" : String.valueOf(map.get("DATASOURCE"));
					String dw = map.get("SDUNIT") == null ? "" : String.valueOf(map.get("SDUNIT"));
					TdsAccountMeter tm = new TdsAccountMeter();
					tm.setShowName(name);
					tm.setTagnumber(tagnumber);
					tm.setDatasource(datasource);
					tm.setSdunit(dw);
					rlist.add(tm);
				}

			}
			return rlist;
		}

		return null;
	}

	/**
	 * 获取默认数据
	 *
	 * <AUTHOR>
	 * @date 2025/4/30
	 * @params
	 * @return
	 *
	 */
	private void getDefaultValue() {
		String sql = "SELECT \n" +
				"    t2.NAME, t2.TAGNUMBER, t2.DATASOURCE, t2.SDUNIT, t2.defaultval, t2.id, \n" +
				"    t2.tmsort, t2.ISWRITEINPUT, t2.DEVICEDEFAULTVAL, t2.DEFAULTVALS, t2.MULTISELECTDISPLAYMODE, t2.COPYADDDEFAULTMODE, t2.SHOW_WIDTH, t2.ALIGN, t2.AUTO_SWAP_ROW,\n" +
				"    t1.tmsort as class_sort, t1.name as class_name\n" +
				"FROM (\n" +
				"    SELECT id, name, team_log_column_id, tmsort \n" +
				"    FROM COSTUNITSAMPLECLASS\n" +
				"    WHERE unitid='" + this.confCode + "' \n" +
				"    AND tmused=1\n" +
				") t1\n" +
				"LEFT JOIN costunitsampledot t2 ON t2.unitid='" + this.confCode + "' AND t2.pid=t1.id\n" +
				"WHERE t2.tmused=1 AND t2.id IS NOT NULL\n" +
				"ORDER BY \n" +
				"    COALESCE(t1.tmsort, 999999) ASC, \n" +
				"    COALESCE(t2.tmsort, 999999) ASC, \n" +
				"    t2.NAME ASC;";
		// 执行查询
		List<Map<String, Object>> list = getResultList(sql);
		if (StringUtils.isNotEmpty(list)) {
			defaultMap = list.stream()
					.collect(Collectors.toMap(item -> String.valueOf(item.get("id")),
							item -> String.valueOf(item.get("defaultval"))));
			defaultTextValMap = list.stream()
					.collect(Collectors.toMap(item -> String.valueOf(item.get("id")),
							item -> String.valueOf(item.get("devicedefaultval"))));
			isWriteInputIdMap = list.stream()
					.collect(Collectors.toMap(item -> String.valueOf(item.get("id")),
							item -> String.valueOf(item.get("iswriteinput"))));
			defaultvalsIdMap = list.stream()
					.collect(Collectors.toMap(item -> String.valueOf(item.get("id")),
							item -> String.valueOf(item.get("defaultvals"))));
			multiSelectDisplayModeIdMap = list.stream()
					.collect(Collectors.toMap(item -> String.valueOf(item.get("id")),
							item -> String.valueOf(item.get("multiselectdisplaymode"))));
			copyAddDefaultModeIdMap = list.stream()
					.collect(Collectors.toMap(item -> String.valueOf(item.get("id")),
							item -> String.valueOf(item.get("copyadddefaultmode"))));

			// 优化排序字段映射：使用复合排序值确保多分类下的正确排序
			dotSort = list.stream().collect(
					Collectors.toMap(
							item -> String.valueOf(item.get("id")),
							item -> {
								// 获取分类排序和采集点排序
								Integer classSort = item.get("class_sort") == null ? 999999
										: Integer.valueOf(String.valueOf(item.get("class_sort")));
								Integer pointSort = item.get("tmsort") == null ? 999999
										: Integer.valueOf(String.valueOf(item.get("tmsort")));
								// 使用复合排序：分类排序 * 10000 + 采集点排序，确保分类优先
								return classSort * 10000 + pointSort;
							}));

			dotName = list.stream().collect(
					Collectors.toMap(
							item -> String.valueOf(item.get("id")), item -> String.valueOf(item.get("name"))));
			list.stream().forEach(item -> {
				JSONObject showProp = null;
				if(item.get("align")!=null){
					if(showProp==null){
						showProp = new JSONObject();
					}
					showProp.put("align",String.valueOf(item.get("align")));
				}
				if(item.get("show_width")!=null) {
					if(showProp==null){
						showProp = new JSONObject();
					}
					showProp.put("showWidth", Integer.valueOf(String.valueOf(item.get("show_width"))));
				}
				if(item.get("auto_swap_row")!=null) {
					if(showProp==null){
						showProp = new JSONObject();
					}
					showProp.put("autoSwapRow", Integer.valueOf(String.valueOf(item.get("auto_swap_row"))));
				}
				if(showProp!=null){
					dotShowProperty.put(String.valueOf(item.get("id")),showProp);
				}
			});
		}

	}

	/**
	 * @category 获取机构仪表信息 暂无用
	 * @param meterVersionMap
	 * @return
	 */
	private List<TdsAccountMeter> getOrgMeter(Map<String, String> meterVersionMap) {
		List<TdsAccountMeter> rlist = new ArrayList<TdsAccountMeter>();

		boolean many = false;
		StringBuffer sb = new StringBuffer();
		for (String unitId : meterVersionMap.keySet()) {
			if (sb.length() > 0) {
				many = true;
				sb.append(" or ");
			}
			sb.append("(unitid='");
			sb.append(unitId + "' and BEGINTIME='" + meterVersionMap.get(unitId) + "')");
			sb.append("' and BEGINTIME='");
			sb.append(meterVersionMap.get(unitId));
			sb.append("')");
		}
		String condition = " and TMUSED = 0 ";
		if (sb.length() > 0) {
			if (many) {
				condition = " and (" + sb.toString() + ") ";
			} else {
				condition = " and " + sb.toString();
			}
		}

		String sql = "select NAME, TAGNUMBER,DATASOURCE,SDUNIT from COSTUNITSAMPLEDOT where TMUSED=1 and BEGINTIME is not null "
				+ condition + " order by UNITID, TMSORT";
		List<Map<String, Object>> list = getResultList(sql);
		for (Map<String, Object> map : list) {
			String tagnumber = map.get("TAGNUMBER") == null ? null : String.valueOf(map.get("TAGNUMBER"));
			if (StringUtils.isNotEmpty(tagnumber)) {
				String name = map.get("NAME") == null ? tagnumber : String.valueOf(map.get("NAME"));
				String datasource = map.get("DATASOURCE") == null ? "" : String.valueOf(map.get("DATASOURCE"));
				String dw = map.get("SDUNIT") == null ? "" : String.valueOf(map.get("SDUNIT"));
				TdsAccountMeter tm = new TdsAccountMeter();
				tm.setShowName(name);
				tm.setTagnumber(tagnumber);
				tm.setDatasource(datasource);
				tm.setSdunit(dw);

				rlist.add(tm);

			}

		}
		return rlist;
	}

	/**
	 * @category 根据核算对象即日期获取方案及仪表上下限 暂无用
	 * @param unitId
	 * @return Map<String, List<Double>> <仪表, List<上限,下限,临界上,临界下>>
	 */
	private Map<String, List<Double>> getTagUplowData(String unitId, String rq) {

		String ver = rq;
		if (ver == null) {
			ver = this.version;
		}

		Map<String, List<Double>> rmap = new HashMap<String, List<Double>>();
		String sql = "select programid from PRODUCTSCHEDU_PLAN_START where unitid='" + unitId + "' and " +
				"STARTDATETIME=(select max(STARTDATETIME) from PRODUCTSCHEDU_PLAN_START where unitid='" + unitId
				+ "' and " +
				"STARTDATETIME<='" + ver + " 00:00:00')";
		List<Map<String, Object>> vlist = getResultList(sql);
		if (StringUtils.isNotEmpty(vlist)) {
			String faid = String.valueOf(vlist.get(0).get("programid"));
			sql = "select a.DATASOURCE,a.TAGNUMBER,b.KEYLOWLIMIT,b.KEYUPLIMIT,b.OPERATELOWLINIT,b.OPERATEUPLIMIT from "
					+
					"(select id,BEGINTIME,CTYPE,DATASOURCE,NAME,PID,SOURCEYPE,TAGNUMBER,SDUNIT,VALRANGE,TMSORT from COSTUNITSAMPLEDOT "
					+
					"	where UNITID='" + unitId
					+ "' and BEGINTIME=(select max(BEGINTIME) beginTime from COSTUNITVERSION " +
					"		where UNITID='" + unitId + "' and BEGINTIME<='" + ver + "') and TMUSED=1) a inner join " +
					"(select id,iname,KEYUPLIMIT,KEYLOWLIMIT,OPERATEUPLIMIT,OPERATELOWLINIT from PROGRAMINDICATOR where PVID=("
					+
					"select id from PROGRAMVERSION where projectdataid='" + faid + "' and PVERSION=(" +
					"select max(PVERSION) from PROGRAMVERSION where projectdataid='" + faid + "' and PVERSION<='" + ver
					+ "')) and TMUSED=1) b on a.NAME=b.INAME";
			List<Map<String, Object>> ullist = getResultList(sql);
			if (StringUtils.isNotEmpty(ullist)) {
				for (Map<String, Object> map : ullist) {
					String ds = map.get("DATASOURCE") == null ? "" : String.valueOf(map.get("DATASOURCE"));
					if (StringUtils.isNotEmpty(ullist)) {
						List<Double> _list = new ArrayList<Double>(4);
						String up1 = map.get("KEYUPLIMIT") == null ? "" : String.valueOf(map.get("KEYUPLIMIT"));
						String low1 = map.get("KEYLOWLIMIT") == null ? "" : String.valueOf(map.get("KEYLOWLIMIT"));
						String up2 = map.get("OPERATEUPLIMIT") == null ? "" : String.valueOf(map.get("OPERATEUPLIMIT"));
						String low2 = map.get("OPERATELOWLINIT") == null ? ""
								: String.valueOf(map.get("OPERATELOWLINIT"));
						if (Coms.judgeDouble(up1)) {
							_list.add(Double.parseDouble(up1));
						} else {
							_list.add(null);
						}
						if (Coms.judgeDouble(low1)) {
							_list.add(Double.parseDouble(low1));
						} else {
							_list.add(null);
						}
						if (Coms.judgeDouble(up2)) {
							_list.add(Double.parseDouble(up2));
						} else {
							_list.add(null);
						}
						if (Coms.judgeDouble(low2)) {
							_list.add(Double.parseDouble(low2));
						} else {
							_list.add(null);
						}
						rmap.put(ds, _list);
					}
				}
			}
		}
		return rmap;
	}

	/**
	 * @category 根据核算对象ID、日期、方案ID获取方案上下限信息
	 * @param unitId
	 * @param rq
	 * @param programId
	 * @return
	 */
	private Map<String, List<Double>> getTagUplowData(String unitId, String rq, String programId,
			List<String> colList) {
		Map<String, List<Double>> rmap = new HashMap<String, List<Double>>();

		String faid = programId;
		String sql = "select a.id,a.DATASOURCE,a.TAGNUMBER,b.KEYLOWLIMIT,b.KEYUPLIMIT,b.OPERATELOWLINIT,b.OPERATEUPLIMIT from "
				+
				"(select id,BEGINTIME,CTYPE,DATASOURCE,NAME,PID,SOURCEYPE,TAGNUMBER,SDUNIT,VALRANGE,TMSORT from COSTUNITSAMPLEDOT "
				+
				"	where UNITID='" + unitId + "' and BEGINTIME=(select max(BEGINTIME) beginTime from COSTUNITVERSION "
				+
				"		where UNITID='" + unitId + "' and BEGINTIME<='" + rq + "') and TMUSED=1) a inner join " +
				"(select id,iname,KEYUPLIMIT,KEYLOWLIMIT,OPERATEUPLIMIT,OPERATELOWLINIT,TMUSED from PROGRAMINDICATOR where PVID=("
				+
				"select id from PROGRAMVERSION where projectdataid='" + faid + "' and PVERSION=(" +
				"select max(PVERSION) from PROGRAMVERSION where projectdataid='" + faid + "' and PVERSION<='" + rq
				+ "')) and TMUSED=1) b on a.NAME=b.INAME";
		List<Map<String, Object>> ullist = getResultList(sql);
		if (StringUtils.isNotEmpty(ullist)) {
			for (Map<String, Object> map : ullist) {
				String tagId = String.valueOf(map.get("id"));
				if (!colList.contains(tagId)) {
					continue;
				}
				if (StringUtils.isNotEmpty(ullist)) {
					List<Double> _list = new ArrayList<Double>(4);
					String up1 = map.get("KEYUPLIMIT") == null ? "" : String.valueOf(map.get("KEYUPLIMIT"));
					String low1 = map.get("KEYLOWLIMIT") == null ? "" : String.valueOf(map.get("KEYLOWLIMIT"));
					String up2 = map.get("OPERATEUPLIMIT") == null ? "" : String.valueOf(map.get("OPERATEUPLIMIT"));
					String low2 = map.get("OPERATELOWLINIT") == null ? "" : String.valueOf(map.get("OPERATELOWLINIT"));
					Double used = map.get("TMUSED") == null ? 0d
							: ("1".equals(String.valueOf(map.get("TMUSED"))) ? 1d : 0d);// 应用1禁用0
					if (Coms.judgeDouble(up1)) {
						_list.add(Double.parseDouble(up1));
					} else {
						_list.add(null);
					}
					if (Coms.judgeDouble(low1)) {
						_list.add(Double.parseDouble(low1));
					} else {
						_list.add(null);
					}
					if (Coms.judgeDouble(up2)) {
						_list.add(Double.parseDouble(up2));
					} else {
						_list.add(null);
					}
					if (Coms.judgeDouble(low2)) {
						_list.add(Double.parseDouble(low2));
					} else {
						_list.add(null);
					}
					_list.add(used);//

					rmap.put(tagId, _list);
				}
			}
		}

		return rmap;
	}

	// 获取各核算对象对应版本日期信息
	private Map<String, String> getMVMap(List<String> unitList) {

		Map<String, String> rmap = new HashMap<String, String>();

		StringBuffer sb = new StringBuffer();
		for (String unitId : unitList) {
			sb.append(",'");
			sb.append(unitId);
			sb.append("'");
		}
		String sql = "select UNITID, BEGINTIME from COSTUNITVERSION where UNITID in (" + sb.substring(1)
				+ ") order by UNITID, BEGINTIME";
		List<Map<String, Object>> list = getResultList(sql);

		Map<String, List<Date>> vmap = new HashMap<String, List<Date>>();
		String tempUnit = null;
		for (Map<String, Object> map : list) {
			String uid = String.valueOf(map.get("UNITID"));
			Date ver = DateTimeUtils.parseD(String.valueOf(map.get("BEGINTIME")), DateTimeUtils.DateFormat_YMD);

			if (uid.equals(tempUnit)) {
				vmap.get(uid).add(ver);
			} else {
				List<Date> tlist = new ArrayList<Date>();
				tlist.add(ver);
				vmap.put(uid, tlist);
				tempUnit = uid;
			}
		}

		Date sendDt = DateTimeUtils.parseD(DateTimeUtils.getNowDateStr(), DateTimeUtils.DateFormat_YMD);
		if (StringUtils.isNotEmpty(this.version)) {
			sendDt = DateTimeUtils.parseD(this.version, DateTimeUtils.DateFormat_YMD);
		}
		for (String unitCode : unitList) {
			List<Date> clist = vmap.get(unitCode);
			if (StringUtils.isNotEmpty(clist)) {
				String mv = "";
				// 从大到小日期
				for (Date ver : clist) {
					if (DateTimeUtils.bjDate(ver, sendDt) <= 0) {
						mv = DateTimeUtils.formatDate(ver, DateTimeUtils.DateFormat_YMD);
						break;
					}
				}
				if ("".equals(mv)) {
					mv = DateTimeUtils.formatDate(clist.get(0), DateTimeUtils.DateFormat_YMD);
				}
				rmap.put(unitCode, mv);
			}
		}

		return rmap;
	}

	// 获取机构对应核算单元列表
	private List<String> getOrgUnits() {
		List<String> unitList = new ArrayList<String>();
		String orgid = this.confCode;
		String sql = "select id as UNITID from COSTUINT where orgid = '" + orgid + "'"
				+ " union  select UNITID from COSTUNITMANAGER where objid = '" + orgid + "'"
				+ " union select UNITID from COSTUNITOPERATOR where objid = '" + orgid + "'";// 语句
		List<Map<String, Object>> list = getResultList(sql);

		for (Map<String, Object> map : list) {
			String unitId = map.get("UNITID") == null ? "" : String.valueOf(map.get("UNITID"));
			if (unitId.length() > 0) {
				unitList.add(unitId);
			}
		}

		return unitList;
	}

	/**
	 * @category 内部工具类，用于获取语句结果
	 * @param sql
	 * @return
	 */
	private List<Map<String, Object>> getResultList(String sql) {
		List<Map<String, Object>> rlist = new ArrayList<Map<String, Object>>();
		ResultSet rs = null;
		Integer rowCount = 0;
		try {
			rs = getResult(sql);
			if (rs != null) {
				rs.last();
				rowCount = rs.getRow();
				if (rowCount > 0) {// 表中有数据
					rs.beforeFirst();
					List<String> cols = new ArrayList<String>();

					int fieldCount = rs.getMetaData().getColumnCount();
					for (int i = 1; i <= fieldCount; i++) {
						String fieldName = rs.getMetaData().getColumnName(i);
						cols.add(fieldName);
						// int coltype = rs.getMetaData().getColumnType(i);
						// int precision = rs.getMetaData().getPrecision(i);
						// int scale = rs.getMetaData().getScale(i);
					}
					while (rs.next()) {
						Map<String, Object> colMap = new HashMap<String, Object>();
						for (String col : cols) {
							Object obj = rs.getObject(col);
							colMap.put(col, obj);
						}
						rlist.add(colMap);
					}
				}
			}
		} catch (Exception e) {
		} finally {
			try {
				if (rs != null) {
					rs.close();
				}
			} catch (SQLException e) {
				log.error(this.hashCode() + " ", e);
			}
			// 查询结束，关闭连接
			disconnection();
		}

		return rlist;
	}

	/**
	 * @category 内部工具类，用于获取结果集对象
	 * @param sql
	 * @return
	 * @throws SQLException
	 */
	private ResultSet getResult(String sql) throws SQLException {
		ResultSet rs = null;
		try {
			connection();// 连接数据库
		} catch (Exception e) {
			log.error("数据库连接错误", e);
			disconnection();
		}
		if (conn != null) {
			CallableStatement cs = cs = conn.prepareCall(sql, ResultSet.TYPE_SCROLL_INSENSITIVE,
					ResultSet.CONCUR_READ_ONLY);
			Integer ocursorPos = 1;
			ExecutorService executor = Executors.newSingleThreadExecutor();
			String msg = "数据源台账内部查询(别名:" + this.dsAlias + ",sql:[" + sql + "]";
			Future<ResultSet> loadjob = executor.submit(iso ? new LoadJob(cs, ocursorPos, msg) : new LoadJob(cs, msg));// 将任务提交到线程池中
			try {
				rs = loadjob.get(tdsOverTime, TimeUnit.SECONDS);
			} catch (InterruptedException e) {
				loadjob.cancel(true);// 中断执行此任务的线程
				setErrorInfo("数据源台账内部查询(TDSSQ-" + this.dsAlias + ")加载数据库数据时，线程中断错误：" + e.getMessage());
				log.error(this.hashCode() + " 数据源台账内部查询(TDSSQ-" + this.dsAlias + ")加载数据库数据时，线程中断错误：", e);
			} catch (ExecutionException e) {
				loadjob.cancel(true);// 中断执行此任务的线程
				setErrorInfo("数据源台账内部查询(TDSSQ-" + this.dsAlias + ")加载数据库数据时，线程服务错误：" + e.getMessage());
				log.error(this.hashCode() + " 数据源台账内部查询(TDSSQ-" + this.dsAlias + ")加载数据库数据时，线程服务错误：", e);
			} catch (TimeoutException e) {// 超时异常
				loadjob.cancel(true);// 中断执行此任务的线程
				setErrorInfo("数据源台账内部查询(TDSSQ-" + this.dsAlias + ")加载数据库数据时超时(" + tdsOverTime + ")秒错误");
				log.error(
						this.hashCode() + " 数据源台账内部查询(TDSSQ-" + this.dsAlias + ")加载数据库数据时超时(" + tdsOverTime + ")秒错误\r\n"
								+ this.getJson(),
						e);
			} finally {
				executor.shutdown();
			}
		}

		return rs;
	}

	/**
	 * @category 封装输出信息
	 *           因动态与静态设置可能出现重复情况，显示名称和别名都需要判断，名称重复增加后缀，别名重复增加前缀，提取数据时需要做处理
	 *           // * @param dataList
	 *           // * @param pageInfo
	 */
	private void encOutParams() {
		// 整理输出列
		if (conf != null && StringUtils.isNotEmpty(plist)) {
			// 判断是否启用多表头显示，启用后按模型模式输出表头信息，否则只显示名称
			Boolean manyTitleFlag = false;
			TdstableInfo tInfo = this.dsm.getTDSTableInfo(getDSAlias());
			if (tInfo.getIsShowSpanTitle() != null && tInfo.getIsShowSpanTitle() == 1) {
				manyTitleFlag = true;
			}

			List<String> mtmode = Coms.StrToList(conf.getTagTitleShowMode(), ",");
			Map<String, String> unitmap = null;
			if (mtmode.contains("cost")) {
				unitmap = getUnitMap();
			}

			int row = 1;
			List<String> showList = new ArrayList<String>();
			List<String> aliasList = new ArrayList<String>();
			if (new Integer(1).equals(conf.getOutparamMode())) {// 仪表为列----------------------------------------------------------------------
				colTitleMap = new HashMap<String, Map<String, String>>();
				String firstDtstr = StringUtils.isNotEmpty(timeList) ? DateTimeUtils.formatDateTime(timeList.get(0))
						: null;
				if (firstDtstr == null) {
					if (this.timeBoundBc1 != null) {
						firstDtstr = this.timeBoundBc1;
					} else {
						firstDtstr = DateTimeUtils.getNowDateTimeStr();
					}
				}

				for (TdsAccountOutparamVo outparam : plist) {
					String showName = outparam.getShowName();
					if (new Integer(1).equals(outparam.getDynamicMark())) {// 动态标识，动态扩展列
						if (StringUtils.isNotEmpty(mlist)) {
							Integer defaulteditmark = outparam.getEditMark();
							if (dotSort != null) {
								mlist.stream().forEach(meter -> {
									// 设置复合排序值，确保多分类下的正确排序
									Integer sortValue = dotSort.get(meter.getTagid());
									meter.setTmsort(sortValue != null ? sortValue : 999999);

									// 安全获取显示名称，如果为null则保持原有名称
									String displayName = dotName.get(meter.getTagid());
									if (displayName != null) {
										meter.setShowName(displayName);
									}
								});
							}
							// 使用优化的排序比较器：先按复合排序值，再按名称
							mlist = mlist.stream()
									.sorted(Comparator.comparing(TdsAccountMeter::getTmsort,
										Comparator.nullsLast(Comparator.naturalOrder()))
										.thenComparing(TdsAccountMeter::getShowName,
										Comparator.nullsLast(Comparator.naturalOrder())))
									.collect(Collectors.toList());
							for (TdsAccountMeter meter : mlist) {
								Integer bit = getTagDegit(meter.getDecimalDegit(), conf.getMeterBit());
								// 重复处理
								String tagNumber = meter.getTagnumber();
								String tagName = meter.getShowName();
								String yb = meter.getTagid();
								Boolean noTag = false;
								if (StringUtils.isEmpty(meter.getDatasource()) && "pwl".equals(tagType)) {// 未设置仪表位号的仪表且不是范围数据进行过滤!"bound".equals(this.timeConf.getShowMode())
									// continue;//调整为显示，仪表位号使用采集点id为仪表位号
									noTag = true;
								}

								// if("mobile".equals(this.timeConf.getShowMode())) {//移动端数据外部获取模式，暂时不能修改数据
								// noTag = false;
								// }

								if (StringUtils.isEmpty(tagName)) {
									tagName = tagNumber;
								}

								// if(aliasList.contains(yb)) {//不判断重复，提高性能
								// if(new Integer(1).equals(this.conf.getEditMark())) {//编辑器模式下，不能出现重复的仪表，保存冲突
								// continue;
								// }
								// yb = this.samePre+yb;//重复别名增加标识
								// }
								aliasList.add(yb);

								Map<String, String> titleMap = new HashMap<String, String>();
								titleMap.put("unitName", unitmap == null ? "" : unitmap.get(meter.getUnitCode()));
								titleMap.put("zone", meter.getBelongZone());
								titleMap.put("dev", meter.getBelongDev());
								titleMap.put("tname", meter.getBelongTag());
								titleMap.put("name", tagName);
								titleMap.put("tag", tagNumber);
								titleMap.put("unit", meter.getSdunit());

								String uplow = "";
								if (meter.getUpLimit() == null && meter.getLowerLimit() == null) {
								} else if (meter.getUpLimit() != null && meter.getLowerLimit() == null) {// 有上限无下限≤上限
									uplow = "≤";
									uplow += meter.getUpLimit().toString();
								} else if (meter.getUpLimit() == null && meter.getLowerLimit() != null) {// 有下限无上限≥下限
									uplow = "≥";
									uplow += meter.getLowerLimit().toString();
								} else {// 上下限都有
									uplow = meter.getLowerLimit().toString();
									uplow += "~";
									uplow += meter.getUpLimit().toString();
								}
								titleMap.put("uplow", uplow);

								if (bit == null) {
									int degit = this.tagFormat == null || this.tagFormat.get(yb) == null
											? conf.getMeterBit()
											: this.tagFormat.get(yb);
									titleMap.put("degit", String.valueOf(degit));
								} else {
									titleMap.put("degit", String.valueOf(bit));
								}

								colTitleMap.put(meter.getTagid(), titleMap);

								if (manyTitleFlag && mtmode.size() > 1) {// 多层表头
									String tname = "";
									if (mtmode.contains("cost")) {
										String unitcode = meter.getUnitCode() == null ? "" : meter.getUnitCode();
										if (unitcode != null && unitmap != null) {
											tname = unitmap.get(unitcode) == null ? "" : unitmap.get(unitcode);
										} else {
											tname = "无";
										}
									}
									if (mtmode.contains("zone")) {
										tname += tname.length() > 0 ? "_" : "";
										tname += meter.getBelongZone() == null ? ""
												: meter.getBelongZone().replace("_", "＿");
									}
									if (mtmode.contains("dev")) {
										tname += tname.length() > 0 ? "_" : "";
										tname += meter.getBelongDev() == null ? ""
												: meter.getBelongDev().replace("_", "＿");
									}
									if (mtmode.contains("tname")) {
										tname += tname.length() > 0 ? "_" : "";
										tname += meter.getBelongTag() == null ? ""
												: meter.getBelongTag().replace("_", "＿");
									}
									if (mtmode.contains("name")) {
										tname += tname.length() > 0 ? "_" : "";
										tname += tagName == null ? ""
												: tagName.replace("_", "＿").replace("\"", "“").replace("\\", "＼");
									}
									if (mtmode.contains("tag")) {
										tname += tname.length() > 0 ? "_" : "";
										tname += meter.getTagnumber() == null ? ""
												: meter.getTagnumber().replace("_", "＿").replace("\"", "“")
														.replace("\\", "＼");// 表有可能有下划线，做处理
									}
									if (mtmode.contains("unit")) {
										tname += tname.length() > 0 ? "_" : "";
										tname += meter.getSdunit() == null ? ""
												: meter.getSdunit().replace("_", "＿").replace("\"", "“");
									}
									if (mtmode.contains("uplow")) {
										String ul = "";
										// List<Double> limitlist = null;
										// if(ulmap!=null && ulmap.get(yb)!=null) {
										// limitlist = ulmap.get(meter.getDatasource());
										// Double uplimit = limitlist.get(0);
										// Double lowlimit = limitlist.get(1);
										//
										// if(lowlimit!=null) {
										// ul = String.valueOf(lowlimit);
										// }
										// ul+="~";
										// if(uplimit!=null) {
										// ul += String.valueOf(uplimit);
										// }
										// }
										// 判断方案，进行表头上下限处理
										ul = getUpLowLimitTitle(meter, firstDtstr, yb);

										tname += tname.length() > 0 ? "_" : "";
										tname += ul;
									}
									tname = tname == null ? "" : tname;

									tagName = "".equals(tname) ? tagName.replace("_", "＿") : tname;
								} else {
									if (showList.contains(tagName)) {
										tagName = tagName + this.sameSuf;
									}
								}
								showList.add(tagName);

								Integer width = outparam.getWidth();
								if (width == null || new Integer(0).equals(width)) {
									width = meter.getWidth();
								}
								if (width == null || new Integer(0).equals(width)) {
									width = 100;
								}
								//获取采集点显示属性
								JSONObject showProp = dotShowProperty.get(yb);
								TOutPara top = new TOutPara(this);
								top.setRowflag(row);
								top.setName(tagName);
								top.setAlias(yb);
								top.setID(row - 1);
								top.setWidth(width);
								top.setVisible(true);// 动态仪表默认都显示
								top.setAlign(
										StringUtils.isEmpty(meter.getAlign()) ? outparam.getAlign() : meter.getAlign());
								top.setDataType(IDataSource.DataType.get(Types.VARCHAR));
								top.setOvertip(true);
								if(showProp!=null){
									//将显示属性拼接
									if(showProp.getIntValue("showWidth")>0){
										top.setWidth(showProp.getIntValue("showWidth"));
									}
									top.setAlign(showProp.getString("align"));
									top.setAutoSwapRow(showProp.getIntValue("autoSwapRow")==1);
								}
								Boolean editFlag = new Integer(1).equals(outparam.getEditMark());
								Integer em = meter.getEditMark() == null ? defaulteditmark : meter.getEditMark();
								String comboStr = getComboStr(meter.getControltype());
								// if("synchronize".equals(this.timeConf.getShowMode()) && new
								// Integer(1).equals(meter.getIsWriteBackInfluxdb())) {//同步模式，回写influx
								if ("mobile".equals(this.timeConf.getShowMode())
										|| "tz".equals(this.timeConf.getShowMode())
										|| "synchronize".equals(this.timeConf.getShowMode())) {// 外部获取采集点可编辑组件设置
									if (StringUtils.isNotEmpty(meter.getCombinitkey())
											&& StringUtils.isNotEmpty(meter.getCombinitval())) {
										top.setDefaultKeyScript(meter.getCombinitkey());
										top.setDefaultValueScript(meter.getCombinitval());
									}

									if (comboStr != null && editFlag) {
										top.setComType(comboStr);
										if ("combo".equals(comboStr) || "comboboxMulti".equals(comboStr)) {// 组件特定处理方式
											if (StringUtils.isEmpty(top.getDefaultKeyScript())) {
												top.setDefaultKeyScript("[]");
												top.setDefaultValueScript("[]");
											} else if (!top.getDefaultKeyScript().trim().startsWith("[")
													&& top.getDefaultKeyScript().indexOf(",") != -1) {
												top.setDefaultKeyScript("[\""
														+ top.getDefaultKeyScript().replace(",", "\",\"") + "\"]");
												top.setDefaultValueScript("[\""
														+ top.getDefaultValueScript().replace(",", "\",\"") + "\"]");
												// Map<Object, Object> _map = new LinkedHashMap<Object, Object>();
												// List<String> ks = Coms.StrToList(meter.getCombinitkey(), ",");
												// List<String> vs = Coms.StrToList(meter.getCombinitval(), ",");
												// for (int i = 0; i < ks.size(); i++) {
												// _map.put(ks.get(i), vs.get(i));
												// }
												// top.setDefaultValueMap(_map);
											}
										}
									}
								} else if (editFlag || new Integer(1).equals(em) || noTag) {
									top.setComType("textfield");// textfield numberfield
								}

								// 检查是否为手工录入采集点，设置disabled属性
								if (isWriteInputIdMap != null && isWriteInputIdMap.containsKey(yb)) {
									String isWriteInput = isWriteInputIdMap.get(yb);
									// 当值为0或null时，设置为disabled
									if ("0".equals(isWriteInput) || "null".equals(isWriteInput) || StringUtils.isEmpty(isWriteInput)) {
										// 直接设置disabled属性
										top.setDisabled(true);
									}else if("1".equals(isWriteInput) && "not_have".equals(comboStr)){
										// 设置为enabled
										top.setDisabled(true);
									}
								}

								// 设置复制新增默认值模式属性
								if (copyAddDefaultModeIdMap != null && copyAddDefaultModeIdMap.containsKey(yb)) {
									String copyAddDefaultMode = copyAddDefaultModeIdMap.get(yb);
									if (StringUtils.isNotEmpty(copyAddDefaultMode) && !"null".equals(copyAddDefaultMode)) {
										top.setCopyAddDefaultMode(copyAddDefaultMode);
									}else{
										top.setCopyAddDefaultMode("source");
									}
								}
								// 获取多选下拉框显示模式
								if ("comboboxMulti".equals(comboStr) && multiSelectDisplayModeIdMap != null && multiSelectDisplayModeIdMap.containsKey(yb)) {
									String displayMode = multiSelectDisplayModeIdMap.get(yb);
									if (StringUtils.isNotEmpty(displayMode) && !"null".equals(displayMode)) {
										top.setDisplayMode(displayMode);
									}else{
										top.setDisplayMode("dropdown");
									}
								}

								List<Double> limitlist = null;
								if (meter.getUpLimit() != null || meter.getLowerLimit() != null) {
									limitlist = new ArrayList<Double>(4);
									limitlist.add(meter.getUpLimit());
									limitlist.add(meter.getLowerLimit());
									limitlist.add(null);
									limitlist.add(null);
								}

								String rendererFun = getColRender(yb, bit, limitlist);// 格式化显示及颜色等都走显示函数
								top.setRendererFun(rendererFun);

								this.addOutPara(top);
								row++;

								if (colULMap == null) {
									colULMap = new HashMap<String, Map<String, String>>();
								}

								Map<String, String> _ulmap = new HashMap<String, String>();
								_ulmap.put("uplimit",
										meter.getUpLimit() == null ? null
												: Coms.formatNumber(meter.getUpLimit(),
														"##################.###########################"));
								_ulmap.put("lowlimit",
										meter.getLowerLimit() == null ? null
												: Coms.formatNumber(meter.getLowerLimit(),
														"##################.###########################"));
								_ulmap.put("overMark", "0");// 超限
								colULMap.put(meter.getTagid(), _ulmap);
							}
						}
					} else if (new Integer(1).equals(outparam.getJobInputTimeMark())) {
						showName = StringUtils.isNotEmpty(showName) ? showName : "时间";
						if (manyTitleFlag) {
							ApplyParams dto = new ApplyParams();
							dto.setApplyAlias("ledger_title_name");
							String timeManyTitle = applySrv.getApplyConfValue(dto);

							Map<String, String> smap = new HashMap<String, String>();
							List<String> tl = Coms.StrToList(timeManyTitle, ";");
							if (StringUtils.isNotEmpty(tl)) {
								for (String str : tl) {
									String[] s = str.split(",");
									if (s.length > 1) {
										smap.put(s[0], s[1]);
									}
								}
							}
							if (StringUtils.isNotEmpty(timeManyTitle) && smap.size() > 0) {

								String tname = "";
								if (mtmode.contains("cost") && smap.containsKey("cost")) {
									tname = smap.get("cost");
								}
								if (mtmode.contains("zone") && smap.containsKey("zone")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("zone");
								}
								if (mtmode.contains("dev") && smap.containsKey("dev")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("dev");
								}
								if (mtmode.contains("tname") && smap.containsKey("tname")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("tname");
								}
								if (mtmode.contains("name") && smap.containsKey("name")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("name");
								}
								if (mtmode.contains("tag") && smap.containsKey("tag")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("tag");
								}
								if (mtmode.contains("unit") && smap.containsKey("unit")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("unit");
								}
								if (mtmode.contains("uplow") && smap.containsKey("uplow")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("uplow");
								}
								if (StringUtils.isNotEmpty(tname)) {
									showName = tname;
								}
							}
						}

						if (new Integer(1).equals(outparam.getVisible())) {
							TOutPara jobInputTimeOutPara = new TOutPara(this);
							jobInputTimeOutPara.setRowflag(row);
							jobInputTimeOutPara.setName(showName);
							jobInputTimeOutPara.setAlias(this.jobInputTimeCol);
							jobInputTimeOutPara.setID(row - 1);
							jobInputTimeOutPara.setWidth(outparam.getWidth());
							jobInputTimeOutPara.setAlign(outparam.getAlign());
							jobInputTimeOutPara.setVisible(true);
							jobInputTimeOutPara.setDataType(IDataSource.DataType.get(Types.VARCHAR));
							jobInputTimeOutPara.setFixed(outparam.getFixed());
							jobInputTimeOutPara.setOvertip(true);
							// if(new Integer(1).equals(this.conf.getEditMark())) {
							// top.setIsKey(true);
							// }
							this.addOutPara(jobInputTimeOutPara);
							row++;

							this.getRsCols().put(this.jobInputTimeCol, jobInputTimeOutPara);
						}else{
							TOutPara jobInputTimeOutPara = new TOutPara(this);
							jobInputTimeOutPara.setRowflag(row);
							jobInputTimeOutPara.setName(showName);
							jobInputTimeOutPara.setAlias(this.jobInputTimeCol);
							jobInputTimeOutPara.setID(row - 1);
							jobInputTimeOutPara.setWidth(outparam.getWidth());
							jobInputTimeOutPara.setAlign(outparam.getAlign());
							jobInputTimeOutPara.setVisible(false);
							jobInputTimeOutPara.setDataType(IDataSource.DataType.get(Types.VARCHAR));
							jobInputTimeOutPara.setFixed(outparam.getFixed());
							jobInputTimeOutPara.setOvertip(true);
							// if(new Integer(1).equals(this.conf.getEditMark())) {
							// top.setIsKey(true);
							// }
							this.addOutPara(jobInputTimeOutPara);
							row++;

							this.getRsCols().put(this.jobInputTimeCol, jobInputTimeOutPara);
						}

						// if(new Integer(1).equals(this.conf.getEditMark())) {
//						TOutPara mark = new TOutPara(this);
//						mark.setRowflag(row);
//						mark.setName(this.PK);
//						mark.setAlias(this.PK);
//						mark.setID(row - 1);
//						mark.setWidth(100);
//						mark.setVisible(false);
//						mark.setAlign("left");
//						mark.setDataType(IDataSource.DataType.get(Types.VARCHAR));
//						this.addOutPara(mark);
//						row++;
//						this.getRsCols().put(this.PK, mark);
						// }
					} else if (new Integer(1).equals(outparam.getTimeMark())) {// 时间列
						showName = StringUtils.isNotEmpty(showName) ? showName : "时间";
						if (manyTitleFlag) {
							ApplyParams dto = new ApplyParams();
							dto.setApplyAlias("ledger_title_name");
							String timeManyTitle = applySrv.getApplyConfValue(dto);

							Map<String, String> smap = new HashMap<String, String>();
							List<String> tl = Coms.StrToList(timeManyTitle, ";");
							if (StringUtils.isNotEmpty(tl)) {
								for (String str : tl) {
									String[] s = str.split(",");
									if (s.length > 1) {
										smap.put(s[0], s[1]);
									}
								}
							}
							if (StringUtils.isNotEmpty(timeManyTitle) && smap.size() > 0) {

								String tname = "";
								if (mtmode.contains("cost") && smap.containsKey("cost")) {
									tname = smap.get("cost");
								}
								if (mtmode.contains("zone") && smap.containsKey("zone")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("zone");
								}
								if (mtmode.contains("dev") && smap.containsKey("dev")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("dev");
								}
								if (mtmode.contains("tname") && smap.containsKey("tname")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("tname");
								}
								if (mtmode.contains("name") && smap.containsKey("name")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("name");
								}
								if (mtmode.contains("tag") && smap.containsKey("tag")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("tag");
								}
								if (mtmode.contains("unit") && smap.containsKey("unit")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("unit");
								}
								if (mtmode.contains("uplow") && smap.containsKey("uplow")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += smap.get("uplow");
								}
								if (StringUtils.isNotEmpty(tname)) {
									showName = tname;
								}
							}
						}

						if (new Integer(1).equals(outparam.getVisible())) {

							TOutPara top = new TOutPara(this);
							top.setRowflag(row);
							top.setName(showName);
							top.setAlias(this.timeCol);
							top.setID(row - 1);
							top.setWidth(outparam.getWidth());
							top.setAlign(outparam.getAlign());
							top.setVisible(true);
							top.setDataType(IDataSource.DataType.get(Types.VARCHAR));
							top.setFixed(outparam.getFixed());
							top.setOvertip(true);
							// if(new Integer(1).equals(this.conf.getEditMark())) {
							// top.setIsKey(true);
							// }
							this.addOutPara(top);
							row++;

							this.getRsCols().put(this.timeCol, top);
						}

						// if(new Integer(1).equals(this.conf.getEditMark())) {
						TOutPara mark = new TOutPara(this);
						mark.setRowflag(row);
						mark.setName(this.PK);
						mark.setAlias(this.PK);
						mark.setID(row - 1);
						mark.setWidth(100);
						mark.setVisible(false);
						mark.setAlign("left");
						mark.setDataType(IDataSource.DataType.get(Types.VARCHAR));
						this.addOutPara(mark);
						row++;
						this.getRsCols().put(this.PK, mark);
						// }
					} else if (StringUtils.isNotEmpty(outparam.getColType())) {// 自定义列，目前有人员、机构，确认列

						if ("rowConfirm".equals(outparam.getColType())) {// 行确认类型
							TOutPara top = new TOutPara(this);
							top.setRowflag(row);
							top.setName(showName);
							top.setAlias("rowConfirm");
							top.setID(row - 1);
							top.setWidth(outparam.getWidth());
							top.setVisible(new Integer(1).equals(outparam.getVisible()));
							top.setAlign(IDataSource.DataType.getDataAlign(Types.VARCHAR));
							top.setDataType(IDataSource.DataType.get(Types.VARCHAR));
							top.setComType("accountRowConfirm");
							top.setFixed(outparam.getFixed());
							top.setOvertip(true);
							// String rendererFun = getConfirmColRender();//超限颜色显示函数
							// top.setRendererFun(rendererFun);

							this.addOutPara(top);
							row++;
							this.getRsCols().put(top.getAlias(), top);

							// TOutPara c2 = new TOutPara(this);
							// c2.setRowflag(row);
							// c2.setName("超期标识");
							// c2.setAlias("rowConfirmOver");
							// c2.setID(row - 1);
							// c2.setWidth(150);
							// c2.setVisible(false);
							// c2.setAlign(IDataSource.DataType.getDataAlign(Types.VARCHAR));
							// c2.setDataType(IDataSource.DataType.get(Types.BOOLEAN));
							// this.addOutPara(c2);
							// row++;
							// this.getRsCols().put(c2.getAlias(), c2);
						} else {
							TOutPara top = new TOutPara(this);
							top.setRowflag(row);
							top.setName(showName);
							top.setAlias(outparam.getAlias());
							top.setID(row - 1);
							top.setWidth(outparam.getWidth());
							top.setVisible(new Integer(1).equals(outparam.getVisible()));
							top.setAlign(IDataSource.DataType.getDataAlign(Types.VARCHAR));
							top.setDataType(IDataSource.DataType.get(Types.VARCHAR));
							if (outparam.getColType().startsWith("rowHand")) {
								top.setComType("textfield");
							}
							top.setFixed(outparam.getFixed());
							top.setOvertip(true);
							this.addOutPara(top);
							row++;
							this.getRsCols().put(top.getAlias(), top);
						}

					} else {// 指定列
						String yb = outparam.getBindMeter();
						Integer bit = getTagDegit(outparam.getDecimalDegit(), conf.getMeterBit());
						if (StringUtils.isNotEmpty(yb)) {
							showName = StringUtils.isNotEmpty(showName) ? showName : yb;

							if (!this.ulmap.containsKey(yb)) {// 上下限中不包括自定义仪表，取自定义上下限
								List<Double> _list = new ArrayList<Double>(4);
								_list.add(outparam.getUpLimit());
								_list.add(outparam.getLowerLimit());
								_list.add(outparam.getUpCritical());
								_list.add(outparam.getLowerCritical());
								this.ulmap.put(yb, _list);
							}

							if (manyTitleFlag && mtmode.size() > 1) {// 指定列多层表头只显示一层列名
								String tname = "";
								// if(mtmode.contains("zone")) {
								// tname = outparam.getBelongZone()==null?"":outparam.getBelongZone();
								// }
								// if(mtmode.contains("dev")) {
								// tname += tname.length() > 0 ? "_" : "";
								// tname += outparam.getBelongDev()==null?"":outparam.getBelongDev();
								// }
								if (mtmode.contains("name")) {
									tname += tname.length() > 0 ? "_" : "";
									tname += StringUtils.isNotEmpty(showName)
											? showName.replace("_", "＿").replace("\"", "“").replace("\\", "＼")
											: "";
								}
								// if(mtmode.contains("tag")) {
								// tname += tname.length() > 0 ? "_" : "";
								// tname += outparam.getTagnumber() == null ? "" : outparam.getTagnumber();
								// }
								// if(mtmode.contains("unit")) {
								// tname += tname.length() > 0 ? "_" : "";
								// tname += outparam.getSdunit() == null ? "" : outparam.getSdunit();
								// }
								// if(mtmode.contains("uplow")) {
								// String ul = "";
								// List<Double> limitlist = null;
								// if(ulmap!=null && ulmap.get(yb)!=null) {
								// limitlist = ulmap.get(yb);
								// Double uplimit = limitlist.get(0);
								// Double lowlimit = limitlist.get(1);
								//
								// if(lowlimit!=null) {
								// ul = String.valueOf(lowlimit);
								// }
								// ul+="~";
								// if(uplimit!=null) {
								// ul += String.valueOf(uplimit);
								// }
								// }
								// tname += tname.length() > 0 ? "_" : "";
								// tname += ul;
								// }
								showName = tname;
							} else {
								// 重复处理
								if (showList.contains(showName)) {
									showName = showName + this.sameSuf;
								}
							}
							showList.add(showName);
							if (aliasList.contains(yb)) {
								if (new Integer(1).equals(this.conf.getEditMark())) {// 编辑器模式下，不能出现重复的仪表，保存冲突
									continue;
								}
								yb = this.samePre + yb;// 重复别名增加标识
							}
							aliasList.add(yb);

							TOutPara top = new TOutPara(this);
							top.setRowflag(row);
							top.setName(showName);
							top.setAlias(yb);
							top.setID(row - 1);
							top.setWidth(outparam.getWidth());
							top.setVisible(new Integer(1).equals(outparam.getVisible()));
							top.setAlign(IDataSource.DataType.getDataAlign(Types.DECIMAL));
							top.setDataType(IDataSource.DataType.get(Types.VARCHAR));
							top.setFixed(outparam.getFixed());
							if (new Integer(1).equals(outparam.getEditMark())) {
								top.setComType("textfield");// textfield numberfield
							}

							List<Double> limitlist = null;
							if (ulmap != null) {
								limitlist = ulmap.get(yb);
							}

							String rendererFun = getColRender(yb, bit, limitlist);// 格式化显示及颜色等都走显示函数
							top.setRendererFun(rendererFun);
							top.setOvertip(true);
							this.addOutPara(top);
							row++;

							this.getRsCols().put(yb.toLowerCase(), top);
						}
					}
				}

				// 行类别列 count是统计行
				TOutPara rtype = new TOutPara(this);
				rtype.setRowflag(row);
				rtype.setName("行类别");
				rtype.setAlias(this.typeCol);
				rtype.setID(row - 1);
				rtype.setWidth(100);
				rtype.setVisible(false);
				rtype.setAlign("left");
				rtype.setDataType(IDataSource.DataType.get(Types.VARCHAR));
				rtype.setDataStatusFlag(1);
				this.addOutPara(rtype);
				row++;
				this.getRsCols().put(this.timeCol, rtype);

				// 信息列 记录台账录入条件
				TOutPara info = new TOutPara(this);
				info.setRowflag(row);
				info.setName("信息列");
				info.setAlias(this.infoCol);
				info.setID(row - 1);
				info.setWidth(100);
				info.setVisible(false);
				info.setAlign("left");
				info.setDataType(IDataSource.DataType.get(Types.VARCHAR));
				info.setDataStatusFlag(1);
				this.addOutPara(info);
				row++;
				this.getRsCols().put(this.timeCol, info);

				// 仪表上下限列，仪表各个时间点上下限信息，方案切换用
				TOutPara ulinfo = new TOutPara(this);
				ulinfo.setRowflag(row);
				ulinfo.setName("仪表上下限信息");
				ulinfo.setAlias(this.ulinfoCol);
				ulinfo.setID(row - 1);
				ulinfo.setWidth(100);
				ulinfo.setVisible(false);
				ulinfo.setAlign("left");
				ulinfo.setDataType(IDataSource.DataType.get(Types.VARCHAR));
				ulinfo.setDataStatusFlag(0);
				this.addOutPara(ulinfo);
				row++;
				this.getRsCols().put(this.timeCol, ulinfo);

				// //编辑标识列
				TOutPara editMarkCol = new TOutPara(this);
				editMarkCol.setRowflag(row);
				editMarkCol.setName("编辑标识列");
				editMarkCol.setAlias(this.editMark);
				editMarkCol.setID(row - 1);
				editMarkCol.setWidth(100);
				editMarkCol.setVisible(false);
				editMarkCol.setAlign("left");
				editMarkCol.setDataType(IDataSource.DataType.get(Types.INTEGER));
				editMarkCol.setDataStatusFlag(1);
				this.addOutPara(editMarkCol);
				row++;
				this.getRsCols().put(this.editMark, editMarkCol);

				// 初始化标识列
				TOutPara isInitMarkCol = new TOutPara(this);
				isInitMarkCol.setRowflag(row);
				isInitMarkCol.setName("初始化标识列");
				isInitMarkCol.setAlias(this.isInitMark);
				isInitMarkCol.setID(row - 1);
				isInitMarkCol.setWidth(100);
				isInitMarkCol.setVisible(false);
				isInitMarkCol.setAlign("left");
				isInitMarkCol.setDataType(IDataSource.DataType.get(Types.VARCHAR));
				isInitMarkCol.setDataStatusFlag(1);
				this.addOutPara(isInitMarkCol);
				row++;
				this.getRsCols().put(this.isInitMark, isInitMarkCol);

			} else if (new Integer(2).equals(conf.getOutparamMode())) {// 时间为列
				Map<String, String> inmap = null;
				for (TdsAccountOutparamVo outparam : plist) {
					String showName = outparam.getShowName();
					if (new Integer(1).equals(outparam.getDynamicMark())) {// 动态标识，动态扩展列
						// 获取时间配置生成列表，生成动态时间列
						if (StringUtils.isNotEmpty(this.sjmap)) {
							for (Date date : this.sjmap.keySet()) {
								String datestr = this.sjmap.get(date);

								// 重复处理
								String showstr = datestr;
								String alias = DateTimeUtils.formatDateTime(date);// 别名用时间，中间有空格，待调试确认是否可行 TODO
								if (showList.contains(showstr)) {
									showstr = showstr + this.sameSuf;
								}
								showList.add(showstr);
								if (aliasList.contains(alias)) {
									if (new Integer(1).equals(this.conf.getEditMark())) {// 编辑器模式下，不能出现重复的时间，保存冲突
										continue;
									}
									alias = this.samePre + alias;// 重复别名增加标识
								}
								aliasList.add(alias);

								TOutPara top = new TOutPara(this);
								top.setRowflag(row);
								top.setAlias(alias);
								top.setName(showstr);
								top.setID(row - 1);
								top.setWidth(150);
								top.setVisible(true);
								top.setAlign(outparam.getAlign());
								top.setDataType(IDataSource.DataType.get(Types.VARCHAR));
								this.addOutPara(top);
								row++;

								this.getRsCols().put(this.timeCol, top);
							}
						}
					} else if (new Integer(1).equals(outparam.getMeterMark())) {// 仪表列
						showName = StringUtils.isNotEmpty(showName) ? showName : "仪表";
						TOutPara top = new TOutPara(this);
						top.setRowflag(row);
						top.setAlias(this.tagCol);
						top.setName(showName);
						top.setID(row - 1);
						top.setWidth(150);
						top.setVisible(true);
						top.setAlign(outparam.getAlign());
						top.setDataType(IDataSource.DataType.get(Types.VARCHAR));
						this.addOutPara(top);
						row++;

						this.getRsCols().put(this.timeCol, top);
					} else {// 指定列
						String coltime = outparam.getBindTime();// .replace(":", "_");

						if (StringUtils.isNotEmpty(coltime)) {
							// 日期计算
							if (inmap == null) {
								inmap = new HashMap<String, String>();
								for (TInPara temp : this.tInParas) {
									inmap.put(temp.getParaAlias(),
											temp.getValue() != null ? String.valueOf(temp.getValue()) : null);
								}
							}
							Date date = null;
							String bindDay = outparam.getBindDay();// 绑定参数
							if (StringUtils.isNotEmpty(bindDay)) {
								String v = inmap.get(bindDay);
								Date d = DateTimeUtils.parseDate(v);
								if (d == null) {// 内容不符合时间格式，未转化成功，取当前日期
									date = DateTimeUtils.parseDateTime(DateTimeUtils.getNowDateStr().substring(0, 10)
											+ " " + outparam.getBindTime() + ":00");
								} else {
									date = DateTimeUtils.parseDateTime(DateTimeUtils.formatDate(date).substring(0, 10)
											+ " " + outparam.getBindTime() + ":00");
								}
							} else {
								date = DateTimeUtils.parseDateTime(DateTimeUtils.getNowDateStr().substring(0, 10) + " "
										+ outparam.getBindTime() + ":00");
							}

							showName = StringUtils.isNotEmpty(showName) ? showName : coltime;
							String datestr = DateTimeUtils.formatDateTime(date);// 同动态时间，看是否有空格显示问题，待调试 TODO

							// 重复处理
							if (showList.contains(showName)) {
								showName = showName + this.sameSuf;
							}
							showList.add(showName);
							if (aliasList.contains(datestr)) {
								if (new Integer(1).equals(this.conf.getEditMark())) {// 编辑器模式下，不能出现重复的时间，保存冲突
									continue;
								}
								datestr = this.samePre + datestr;// 重复别名增加标识
							}
							aliasList.add(datestr);

							TOutPara top = new TOutPara(this);
							top.setRowflag(row);
							top.setAlias(datestr);
							top.setName(showName);
							top.setID(row - 1);
							top.setWidth(150);
							top.setVisible(new Integer(1).equals(outparam.getVisible()));
							top.setAlign(IDataSource.DataType.getDataAlign(Types.DECIMAL));
							top.setDataType(IDataSource.DataType.get(Types.VARCHAR));
							this.addOutPara(top);
							row++;

							this.getRsCols().put(coltime, top);
						}
					}
				}
			}
		}

		// TOutPara out = new TOutPara(this);
		// this.addOutPara(out);
	}

	// 获取组件类型
	private String getComboStr(Integer confType) {
		// 组件类型[[key:0, value:"文本"], (key: 1 value: "数值"}],(key: 2, value."日期时间控件"],
		// (key:3, value :"复选柜"}, key.4 value"下拉框*)
		/*
		 * textfield 文本框
		 * numberfield 数值
		 * datetimefield 日期时间选择框
		 * checkfield 复选框 (目前只显示一个，选择或者未选择)
		 * combo 下拉框
		 */
		String rv = null;
		if (new Integer(0).equals(confType)) {
			rv = "textfield";
		} else if (new Integer(1).equals(confType)) {
			rv = "numberfield";
		} else if (new Integer(2).equals(confType)) {
			rv = "datetimefield";
		} else if (new Integer(3).equals(confType)) {
			rv = "checkfield";
		} else if (new Integer(4).equals(confType)) {
			rv = "combo";
		} else if (new Integer(8).equals(confType)) {
			rv = "userfield";
		} else if (new Integer(9).equals(confType)) {
			rv = "usersfield";
		} else if (new Integer(10).equals(confType)) {
			rv = "datefield";
		} else if (new Integer(11).equals(confType)) {
			rv = "timefield";
		} else if (new Integer(12).equals(confType)) {
			rv = "uploadImg";
		} else if (new Integer(13).equals(confType)) {
			rv = "orgfield";
		} else if (new Integer(14).equals(confType)) {
			rv = "not_have";
		} else if (new Integer(15).equals(confType)) {
			rv = "comboboxMulti";
		}
		return rv;
	}

	private String getType(String confType) {
		String rv = null;
		if ("textfield".equals(confType)) {
			rv = "0";
		} else if ("numberfield".equals(confType)) {
			rv = "1";
		} else if ("datetimefield".equals(confType)) {
			rv = "2";
		} else if ("checkfield".equals(confType)) {
			rv = "3";
		} else if ("combo".equals(confType)) {
			rv = "4";
		} else if ("userfield".equals(confType)) {
			rv = "8";
		} else if ("usersfield".equals(confType)) {
			rv = "9";
		} else if ("datefield".equals(confType)) {
			rv = "10";
		} else if ("timefield".equals(confType)) {
			rv = "11";
		} else if ("uploadImg".equals(confType)) {
			rv = "12";
		} else if (("orgfield").equals(confType)) {
			rv = "13";
		}else if (("not_have").equals(confType)) {
			rv = "14";
		}else if (("comboboxMulti").equals(confType)) {
			rv = "15";
		}
		return rv;
	}

	private String getUpLowLimitTitle(TdsAccountMeter meter, String dtstr, String tagId) {
		String ul = this.noBound;
		Boolean useOld = true;
		if (StringUtils.isNotEmpty(fasjlist) && fasjlist.contains(dtstr)) {// 有方案且第一个时间点有方案
			List<Map<String, Object>> tfalist = tagFaULmap == null ? null : tagFaULmap.get(tagId);
			if (tfalist != null) {// 没有对应方案信息情况下，使用默认仪表上下限
				for (Map<String, Object> map : tfalist) {
					String srq = String.valueOf(map.get("srq"));
					String erq = String.valueOf(map.get("erq"));
					if (dtstr.compareTo(srq) >= 0 && dtstr.compareTo(erq) < 0) {// 根据时间范围判断是否应用方案
						Double uplimit = (Double) map.get("up");
						Double lowlimit = (Double) map.get("low");

						if (uplimit == null && lowlimit == null) {
							ul = this.noBound;
						} else if (uplimit != null && lowlimit == null) {// 有上限无下限≤上限
							ul = "≤";
							ul += uplimit.toString();
						} else if (uplimit == null && lowlimit != null) {// 有下限无上限≥下限
							ul = "≥";
							ul += lowlimit.toString();
						} else {// 上下限都有
							ul = lowlimit.toString();
							ul += "~";
							ul += uplimit.toString();
						}
						useOld = false;
						break;
					}
				}
			}
		}

		if (useOld && meter != null) {
			if (meter.getUpLimit() == null && meter.getLowerLimit() == null) {// 无上下限，显示/
				ul = this.noBound;
			} else if (meter.getUpLimit() != null && meter.getLowerLimit() == null) {// 有上限无下限≤上限
				ul = "≤";
				ul += meter.getUpLimit().toString();
			} else if (meter.getUpLimit() == null && meter.getLowerLimit() != null) {// 有下限无上限≥下限
				ul = "≥";
				ul += meter.getLowerLimit().toString();
			} else {// 上下限都有
				ul = meter.getLowerLimit().toString();
				ul += "~";
				ul += meter.getUpLimit().toString();
			}
		}

		return ul;
	}

	/**
	 * @category 确认列渲染，--暂无用
	 * @return
	 */
	private String getConfirmColRender() {
		StringBuffer sb = new StringBuffer();
		sb.append("let rv = value;");
		// sb.append("if(rv!=null && rv!='' && row.rowConfirmOver=='true'){rv = \"<div
		// style='color:red'>\"+rv+\"</div>\";}");
		sb.append("rv = \"<div style='color:red;'>\"+rv+\"</div>\";");
		sb.append("return rv;");
		return sb.toString();
	}

	/**
	 * @category 数据源输出列格式化函数，目前包括数值格式及超限颜色脚本
	 * @param yb
	 * @return
	 */
	private String getColRender(String yb, Integer degit, List<Double> limitlist) {
		StringBuffer sb = new StringBuffer();

		if (colInfoMap != null && colInfoMap.get(yb) != null) {
			Map<String, String> infoMap = colInfoMap.get(yb);
			String comType = infoMap.get("comType");
			String comOptions = infoMap.get("comOptions");
			sb.append("let rv = value;let istj = \"1\"==row._typeCol;");
			if ("checkbox".equalsIgnoreCase(comType)) {
				sb.append("if(rv == '1') {rv='是';} else{rv='否';}");
			} else if ("combobox".equalsIgnoreCase(comType)) {
				// comOptions =
				// [{"text":"1号灭火器","value":"1"},{"text":"2号灭火器","value":"2"},{"text":"3号灭火器","value":"3"}]
				if (StringUtils.isNotEmpty(comOptions)) {
					JSONArray arr = JSONArray.parseArray(comOptions);
					if (arr != null && arr.size() > 0) {
						for (int i = 0, il = arr.size(); i < il; i++) {
							JSONObject option = JSONObject.parseObject(arr.getString(i));
							String value = option.getString("value");
							String text = option.getString("text").replace("'", "\'");
							if (i > 0) {
								sb.append("else ");
							}
							sb.append("if(rv=='" + value + "') {rv='" + text + "';}");
						}
					}
				}
			}
			sb.append("return rv;");
			return sb.toString();
		}

		sb.append("let rv = value;let isval = value!=\"\" && !isNaN(value); let istj = \"1\"==row._typeCol;");
		// 数值格式化，判断是否数值，进行小数格式化
		if (degit == null) {
			int bit = this.tagFormat == null || this.tagFormat.get(yb) == null ? conf.getMeterBit()
					: this.tagFormat.get(yb);
			// sb.append("if(value=='' || isNaN(value)){}else{rv =
			// parseFloat(value).toFixed("+bit+");}");
			sb.append("if(isval){rv = parseFloat(value).toFixed(" + bit + ");}");
		} else {
			// sb.append("if(value=='' || isNaN(value)){}else{rv =
			// parseFloat(value).toFixed("+degit+");}");
			sb.append("if(isval){rv = parseFloat(value).toFixed(" + degit + ");}");
		}
		// 颜色渲染，conf中获取超限颜色，超限值从接口获取，依次进行判断并编写渲染脚本
		sb.append("let color = \"\", bg = \"\";");

		if (StringUtils.isNotEmpty(saveDataList) && StringUtils.isNotEmpty(this.conf.getUpdateBgColor())) {
			sb.append("let hdlist = ['" + Coms.listToString(saveDataList, "','") + "'];");
			sb.append("if(isval && hdlist.includes(row.ID+'_'+colName)) {bg = 'background: "
					+ this.conf.getUpdateBgColor() + ";';}");
		}

		sb.append(
				"let ulinfo = row._accountULInfoCol; let uljson = (ulinfo==null || ulinfo==\"\") ? null : eval('('+ulinfo+')');");
		sb.append("if(uljson && isval) {let _color = uljson.overColor; let _colul = uljson.data[colName];");
		sb.append("if(_colul && _color && _color!=\"\") { let uv = _colul.uplimit, lv = _colul.lowlimit;");
		sb.append("if((uv && parseFloat(value) > uv) || (lv && parseFloat(value) < lv)) {");
		sb.append("color = 'color:'+_color+';';");
		sb.append("}}}");

		sb.append("if(color!=\"\" || bg!=\"\"){rv = \"<div style='\"+color+bg+\"'>\"+rv+\"</div>\";}");

		// if(limitlist!=null) {
		// StringBuffer condsb = new StringBuffer();
		// Double uplimit = limitlist.get(0);
		// Double lowlimit = limitlist.get(1);
		// Double uplj = limitlist.get(2);
		// Double lowlj = limitlist.get(3);
		//
		// sb.append("let color = '', bg = '';");
		//
		// String limitColor = this.conf.getOverLimitColor();
		// String warning1Color = this.conf.getWarning1Color();
		// if(StringUtils.isNotEmpty(limitColor)) {//上下限
		// if(uplimit!=null || lowlimit!=null) {
		// condsb.append("if(isval && !istj){if(");
		// if(uplimit!=null) {
		// condsb.append("parseFloat(value)>"+uplimit);
		// }
		// if(lowlimit!=null) {
		// if(uplimit!=null) {
		// condsb.append(" || ");
		// }
		// condsb.append("parseFloat(value)<"+lowlimit);
		// }
		//// condsb.append("){rv = \"<div
		// style='color:"+limitColor+";'>\"+rv+\"</div>\"}}");
		// condsb.append("){color = 'color:"+limitColor+";';}}");
		// }
		// }
		// if(StringUtils.isNotEmpty(warning1Color)) {//临界值
		// if(uplj!=null && lowlj!=null) {
		// condsb.append("if(isval && color=='' && !istj){if(");
		// if(uplimit!=null) {
		// condsb.append("parseFloat(value)>"+uplj);
		// }
		// if(lowlimit!=null) {
		// if(uplimit!=null) {
		// condsb.append(" || ");
		// }
		// condsb.append("parseFloat(value)<"+lowlj);
		// }
		//// condsb.append("){rv = \"<div
		// style='color:"+warning1Color+";'>\"+rv+\"</div>\"}}");
		// condsb.append("){color = 'color:"+warning1Color+";';}}");
		// }
		// }
		// if(condsb.length() > 0) {
		// sb.append(condsb.toString());
		// }
		// }
		//
		// if(StringUtils.isNotEmpty(saveDataList) &&
		// StringUtils.isNotEmpty(this.conf.getUpdateBgColor())) {
		// sb.append("let hdlist = ['"+Coms.listToString(saveDataList, "','")+"'];");
		//// sb.append("if(isval && hdlist.includes(row.ID+'_'+colName)) {rv = \"<div
		// style='background: "+this.conf.getUpdateBgColor()+";'>\"+rv+\"</div>\";}");
		// sb.append("if(isval && hdlist.includes(row.ID+'_'+colName)) {bg =
		// 'background: "+this.conf.getUpdateBgColor()+";';}");
		// }
		// if(limitlist!=null) {
		// sb.append("if(color!='' || bg!=''){rv = \"<div
		// style='\"+color+bg+\"'>\"+rv+\"</div>\";}");
		// }

		sb.append("return rv;");
		return sb.toString();
	}

	/**
	 * @category 整理并插入仪表数据
	 * @param dataList
	 * @param pageInfo
	 */
	private void putData(Map<String, Map<Date, String>> dataList, TPageInfo pageInfo) {

		if (dataList == null) {
			dataList = new HashMap<String, Map<Date, String>>();
		}

		if (new Integer(1).equals(conf.getOutparamMode())) {// 仪表为列

			if ("bound".equals(this.timeConf.getShowMode())) {// 只显示一行数据
				int rowId = 0;
				Map<String, Object> rowRawMp = new HashMap<String, Object>();
				String id = this.st + "," + this.et;

				tds.addRow();
				for (int i = 0, il = this.tOutParas.size(); i < il; i++) {
					TOutPara op = this.tOutParas.get(i);
					String alias = op.getAlias();
					if (alias.startsWith(this.samePre)) {// 别名相同增加前缀，获取数据时处理
						alias = alias.replace(this.samePre, "");
					}
					String val = null;

					if (alias.equals(this.PK)) {
						val = id;
					} else if (alias.equals(this.timeCol)) {
						val = this.st.substring(0, 16) + "~" + this.et.substring(0, 16);
					}else if (alias.equals(this.jobInputTimeCol)) {
						val = this.st2.substring(0, 16) + "~" + this.et2.substring(0, 16);
					} else if (alias.equals(this.infoCol)) {
						val = infoContent;
					} else {
						val = "";
						Map<Date, String> sjval = dataList.get(alias);
						if (sjval != null) {
							for (Date d : sjval.keySet()) {
								if (val.length() > 0) {
									val += ",";
								}
								String v = sjval.get(d) == null ? "" : sjval.get(d);
								val = v;
								// if(!"rowConfirm".equals(alias)) {
								// val+= v+"("+DateTimeUtils.formatTime(d)+")";
								// }else {
								// val = v;
								// }
							}
						}
					}
					tds.set(rowId, i, val);

					String valstr = val == null ? "" : val.replaceAll("<[^>]*>", "");
					if (Coms.judgeDouble(valstr)) {// 导出数据格式化
						int bit = this.tagFormat == null || this.tagFormat.get(alias) == null ? conf.getMeterBit()
								: this.tagFormat.get(alias);
						valstr = Coms.formatNumber(valstr, bit);
					}
					rowRawMp.put(alias, valstr);
				}

				this.addData(rowRawMp);// 行数据加入，为了导出
			} else {

				if (StringUtils.isNotEmpty(this.sjmap)) {// 整理数据成行
					Map<Date, Map<String, String>> dataMap = new HashMap<Date, Map<String, String>>();
					for (String tagId : dataList.keySet()) {
						Integer degit = 3;
						Map<String, String> titleMap = colTitleMap.get(tagId);
						if (titleMap != null) {
							String degitStr = titleMap.get("degit");
							if (Coms.judgeLong(degitStr)) {
								degit = Integer.parseInt(degitStr);
							}
						}

						Map<Date, String> sjval = dataList.get(tagId);
						if (sjval != null) {
							for (Date dt : sjval.keySet()) {
								String val = sjval.get(dt);
								if (val != null && Coms.judgeDouble(val)) {// 格式化
									val = Coms.formatInput(val, degit);
								}

								if (dataMap.containsKey(dt)) {
									dataMap.get(dt).put(tagId, val);
								} else {
									Map<String, String> _map = new HashMap<String, String>();
									_map.put(tagId, val);
									dataMap.put(dt, _map);
								}
							}
						}
					}

					int rowId = 0;
					TDataStore tds = getDataStore();

					Date nd = DateTimeUtils.getNowDate();
					String limitColor = this.conf.getOverLimitColor();
					String updColor = this.conf.getUpdateBgColor();
					String preContent = "{\"overColor\":\"" + limitColor + "\", \"updColor\":\"" + updColor
							+ "\", \"data\":{";

					String firstDtstr = StringUtils.isNotEmpty(timeList) ? DateTimeUtils.formatDateTime(timeList.get(0))
							: "";

					// 获取当前时间最近的时间点
					Date posdate = null;
					for (Date date : sjmap.keySet()) {
						if (DateTimeUtils.bjDate(nd, date) == 1) {
							posdate = date;
						}
					}

					for (Date date : sjmap.keySet()) {// 按时间成行，整理输出行
						String dateStr = sjmap.get(date);
						String timeStr = DateTimeUtils.formatDateTime(date);

						if (showTimeNum != 0) {
							if (!showTimeList.contains(timeStr)) {
								continue;
							}
						}

						Boolean haveDefault = false;

						Map<String, String> vmap = dataMap.get(date);

						Map<String, Object> rowRawMp = new HashMap<String, Object>();

						// 有方案时，判断是否有方案变更，插入上下限信息
						if (!timeStr.equals(firstDtstr) && fasjlist != null && fasjlist.contains(timeStr)) {// 第一个时间点在表头上，不进行处理，从第二个时间点开始判断是否有方案变更，插入上下限信息行
							Map<String, Object> farowRawMp = new HashMap<String, Object>();
							tds.addRow();
							for (int i = 0, il = this.tOutParas.size(); i < il; i++) {
								TOutPara op = this.tOutParas.get(i);
								String alias = op.getAlias();
								if (alias.startsWith(this.samePre)) {// 别名相同增加前缀，获取数据时处理
									alias = alias.replace(this.samePre, "");
								}
								String val = null;

								if (alias.equals(this.PK)) {// 主键
									val = timeStr;
								} else if (alias.equals(this.typeCol)) {// 类型列
									val = "2";
								} else if (alias.equals(this.timeCol)) {// 时间列
									val = "方案变更";
								}else if (alias.equals(this.jobInputTimeCol)) {
									// 从jobInputTimeIdMap获取采集时间
									if (this.jobInputTimeIdMap != null && this.jobInputTimeIdMap.containsKey(date)) {
										val = this.jobInputTimeIdMap.get(date);
									}
								} else if (alias.equals(this.infoCol)) {
									// if(DateTimeUtils.bjDate(nd, date) == 1) {//降低传输数据，未到时间，不输出上下限相关信息
									// val = infoContent+activeId;
									// }
								} else if (alias.equals(this.ulinfoCol)) {// 上下限信息列，列顺序靠后，最后输出，前面需要整理仪表信息//TODO判断时间点，写入不同方案不同上下限
								} else {
									if ("rowConfirm".equals(alias)) {
										val = noConfirm;
									} else {
										// 对应方案时间点的上下限
										TdsAccountMeter meter = tagIdObjMap.get(alias);
										String ul = getUpLowLimitTitle(meter, timeStr, alias);
										val = ul;
									}
								}
								tds.set(rowId, i, val);

								String valstr = val == null ? "" : val.replaceAll("<[^>]*>", "");
								farowRawMp.put(alias, valstr);
							}
							rowId++;
							this.addData(farowRawMp);// 行数据加入，为了导出
						}

						// ulinfocontent.append("{\"overColor\":\""+limitColor+"\", \"data\":{");
						StringBuffer ulinfocontent = new StringBuffer();

						tds.addRow();
						for (int i = 0, il = this.tOutParas.size(); i < il; i++) {
							TOutPara op = this.tOutParas.get(i);
							String alias = op.getAlias();
							if (alias.startsWith(this.samePre)) {// 别名相同增加前缀，获取数据时处理
								alias = alias.replace(this.samePre, "");
							}
							String val = null;

							if (alias.equals(this.PK)) {// 主键
								val = timeStr;
							} else if (alias.equals(this.timeCol)) {// 时间列
								val = dateStr;
							} else if (alias.equals(this.jobInputTimeCol)) {// 时间列
								// 从jobInputTimeIdMap获取采集时间
								if (this.jobInputTimeIdMap != null && this.jobInputTimeIdMap.containsKey(date)) {
									val = this.jobInputTimeIdMap.get(date);
								}
							}else if (alias.equals(this.infoCol)) {
								// if(DateTimeUtils.bjDate(nd, date) == 1) {//降低传输数据，未到时间，不输出上下限相关信息
								String activeId = ";";
								if (this.infoTimeIdMap != null && infoTimeIdMap.containsKey(date)) {
									activeId = ";activeId@" + infoTimeIdMap.get(date);
								}
								val = infoContent + activeId;
								// }
							} else if (alias.equals(this.ulinfoCol)) {// 上下限信息列，列顺序靠后，最后输出，前面需要整理仪表信息//TODO判断时间点，写入不同方案不同上下限
								// String dotULInfo = getTagULInfo(date);
								// val = dotULInfo;
								if (ulinfocontent.length() > 0) {
									String rf = haveDefault ? "true" : "false";
									String ulinfo = "{ \"rf\": " + rf + "," + preContent.substring(1)
											+ ulinfocontent.substring(1) + "}}";
									val = ulinfo;
								}
							} else if (alias.equals(this.editMark)) {
								if (this.infoTimeEditMap != null && infoTimeEditMap.containsKey(date)) {
									val = infoTimeEditMap.get(date);
								} else {
									val = "1";
								}
							} else if (alias.equals(this.isInitMark)) {
								if (this.isInitIdMap != null && isInitIdMap.containsKey(date)) {
									val = isInitIdMap.get(date);
								}
							} else {
								if (vmap != null) {
									val = vmap.get(alias);
									if ("userfield".equals(op.getComType()) || "usersfield".equals(op.getComType())) {
										List<String> userName = new ArrayList<>();
//										ISysEmployeeInfoService employeeInfoService = SpringUtils.getBean(ISysEmployeeInfoService.class);
										if (StringUtils.isNotEmpty(val)) {
											val = val.replaceAll(" ", "");
											String[] ids = val.split(",");
											for (String id : ids) {
												// 人员默认值 通过id 获取人员名称
												SysEmployeeInfo employee = sysEmployeeInfoService.findEmployeeById(id);
												if (employee != null) {
													userName.add(employee.getEmpname());
												}
											}
										}
										if (StringUtils.isNotEmpty(userName)) {
											txtMap.put(alias + "_" + timeStr, StringUtils.join(userName, ","));
										}
									} else if ("orgfield".equals(op.getComType())) {
										List<String> orgName = new ArrayList<>();
										ISysOrgService orgService = SpringUtils.getBean(ISysOrgService.class);
										if (StringUtils.isNotEmpty(val)) {
											val = val.replaceAll(" ", "");
											String[] ids = val.split(",");
											for (String id : ids) {
												// 人员默认值 通过id 获取人员名称
												SysOrg org = orgService.findOrgById(id);
												if (org != null) {
													orgName.add(org.getOrgname());
												}
											}
										}
										if (StringUtils.isNotEmpty(orgName)) {
											txtMap.put(alias + "_" + timeStr, StringUtils.join(orgName, ","));
										}
									}
								} else {
									// 等于空的情况下，根据配置加载默认值，最近时间点且由默认值
									if (date.equals(posdate) && tagIdObjMap != null) {
										TdsAccountMeter m = tagIdObjMap.get(alias);
										if (m != null) {
											String v = m.getDefaultval();// { key:1,value:"当前登录人"}, {key:2
																			// ,value:"当前时间"}
											// 使用统一的默认值处理方法
											String processedVal = processDefaultValue(alias, v, this.defaultValueMode);
											if (StringUtils.isNotEmpty(processedVal)) {
												val = processedVal;
												haveDefault = true;

												// 处理显示文本（仅在录入模式下）
												if ("INPUT".equals(this.defaultValueMode)) {
													if ("1".equals(v)) {
														txtMap.put(alias + "_" + timeStr, user.getRealName());
													}
												}
											}
										}
									}
								}
								if ("rowConfirm".equals(alias) && StringUtils.isEmpty(val)) {
									if (StringUtils.isNotEmpty(noConfirmList) && this.noConfirmList.contains(date)) {
										val = noConfirm;
									} else if (DateTimeUtils.bjDate(date, nd) == 1) {
										val = noConfirm;
									}
								} else {
									// if(DateTimeUtils.bjDate(nd, date) == 1) {//降低传输数据，未到时间，不输出上下限相关信息
									String ulinfo = arrangeAccountInfo(alias, timeStr, val, txtMap, op);
									ulinfocontent.append(ulinfo);
									// }
								}
							}
							tds.set(rowId, i, val);

							String valstr = val == null ? "" : val.replaceAll("<[^>]*>", "");
							if (Coms.judgeDouble(valstr)) {// 导出数据格式化
								int bit = this.tagFormat == null || this.tagFormat.get(alias) == null
										? conf.getMeterBit()
										: this.tagFormat.get(alias);
								valstr = Coms.formatNumber(valstr, bit);
							}
							rowRawMp.put(alias, valstr);
						}
						rowId++;

						this.addData(rowRawMp);// 行数据加入，为了导出
					}

					// 汇总行，目前只有时间点模式下有汇总
					if (StringUtils.isNotEmpty(this.countList)) {
						for (TdsAccountCountConfVo countRow : countList) {
							tds.addRow();
							Map<String, Object> rowRawMp = new HashMap<String, Object>();
							String countId = countRow.getId();
							for (int i = 0, il = this.tOutParas.size(); i < il; i++) {
								TOutPara op = this.tOutParas.get(i);
								String alias = op.getAlias();
								String val = null;
								if (alias.equals(this.timeCol)) {// 时间列
									val = countRow.getCountName();
								} else if (alias.equals(this.typeCol)) {// 类型列
									val = "1";
								} else if (StringUtils.isNotEmpty(this.countMap)) {
									String cval = this.countMap.get(countId + "_" + alias);
									val = cval;
								}
								tds.set(rowId, i, val);

								String valstr = val == null ? "" : val.replaceAll("<[^>]*>", "");
								if (Coms.judgeDouble(valstr)) {// 导出数据格式化
									int bit = this.tagFormat == null || this.tagFormat.get(alias) == null
											? conf.getMeterBit()
											: this.tagFormat.get(alias);
									valstr = Coms.formatNumber(valstr, bit);
								}
								rowRawMp.put(alias, valstr);
							}

							rowId++;
							this.addData(rowRawMp);// 行数据加入，为了导出
						}
					}
				}
			}

		} else if (new Integer(2).equals(conf.getOutparamMode())) {// 时间为列
			// 仪表为行，仪表为动态仪表信息
			if (StringUtils.isNotEmpty(this.mlist)) {
				int rowId = 0;
				TDataStore tds = getDataStore();
				for (TdsAccountMeter meter : this.mlist) {// 循环仪表行
					String tag = meter.getDatasource();// 仪表位号
					Map<Date, String> vmap = dataList.get(tag);
					tds.addRow();
					for (int i = 0, il = this.tOutParas.size(); i < il; i++) {
						TOutPara op = this.tOutParas.get(i);
						String alias = op.getAlias();
						if (alias.startsWith(this.samePre)) {// 别名相同增加前缀，获取数据时处理
							alias = alias.replace(this.samePre, "");
						}

						String val = null;
						if (alias.equals(this.tagCol)) {// 仪表列
							val = StringUtils.isEmpty(meter.getShowName()) ? meter.getTagnumber() : meter.getShowName();
						} else {
							if (vmap != null) {
								Date _date = DateTimeUtils.parseDateTime(alias);
								val = vmap.get(_date);
							}
						}
						tds.set(rowId, i, val);
					}
					rowId++;
				}
			}
		}
	}

	// 整理上下限信息（有方案处理）
	private String arrangeAccountInfo(String alias, String timeStr, String val, Map<String, String> txtMap,
			TOutPara op) {
		String rv = "";

		Boolean haveul = false;

		String markInfo = "";
		if (markMap != null) {
			markInfo = markMap.get(alias + "_" + timeStr);
			markInfo = markInfo == null ? "" : markInfo.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r");
		}

		// 判断是否有方案，如果没有方案，按默认方案（默认上下限）显示
		if (tagFaULmap != null) {
			List<Map<String, Object>> tinfo = tagFaULmap.get(alias);
			if (tinfo != null) {
				for (Map<String, Object> map : tinfo) {
					// taginfo.put("srq", srq);
					// taginfo.put("erq", erq);
					// taginfo.put("up", ullist.get(0));
					// taginfo.put("low", ullist.get(1));
					String srq = String.valueOf(map.get("srq"));
					String erq = String.valueOf(map.get("erq"));
					if (timeStr.compareTo(srq) >= 0 && timeStr.compareTo(erq) < 0) {// 根据时间范围判断是否应用方案
						Double uplimit = (Double) map.get("up");
						Double lowlimit = (Double) map.get("low");
						// Boolean overmark = false;
						// if(Coms.judgeDouble(val)) {//结果是数值
						// Double dv = Double.parseDouble(val);
						// if(uplimit!=null && dv > uplimit) {
						// overmark = true;
						// }
						// if(lowlimit!=null && dv < lowlimit) {
						// overmark = true;
						// }
						// }

						String uplow = "";
						if (uplimit == null && lowlimit == null) {
						} else if (uplimit != null && lowlimit == null) {// 有上限无下限≤上限
							uplow = "≤";
							uplow += uplimit.toString();
						} else if (uplimit == null && lowlimit != null) {// 有下限无上限≥下限
							uplow = "≥";
							uplow += lowlimit.toString();
						} else {// 上下限都有
							uplow = lowlimit.toString();
							uplow += "~";
							uplow += uplimit.toString();
						}

						Map<String, String> titleMap = colTitleMap.get(alias);
						String titleInfo = "";
						if (titleMap != null) {
							String titleName = titleMap.get("name") == null ? ""
									: titleMap.get("name").replace("\"", "").replace("\n", "\\n").replace("\r", "\\r")
											.replace("\\", "＼");
							// String titleUnitName = titleMap.get("unitName") == null? "" :
							// titleMap.get("unitName").replace("\"", "").replace("\n", "\\n").replace("\r",
							// "\\r").replace("\\", "＼");
							// String titleZone = titleMap.get("zone") == null? "" :
							// titleMap.get("zone").replace("\"", "");
							// String titleDev = titleMap.get("dev") == null? "" :
							// titleMap.get("dev").replace("\"", "");
							// String titleTname = titleMap.get("tname") == null? "" :
							// titleMap.get("tname").replace("\"", "").replace("\n", "\\n").replace("\r",
							// "\\r").replace("\\", "＼");
							// String titleTag = titleMap.get("tag") == null? "" :
							// titleMap.get("tag").replace("\"", "").replace("\n", "\\n").replace("\r",
							// "\\r").replace("\\", "＼");
							// String titleUnit = titleMap.get("unit") == null? "" :
							// titleMap.get("unit").replace("\"", "");
							String titleUplow = uplow;
							String degit = titleMap.get("degit") == null ? "3" : titleMap.get("degit");
							// , \"titleZone\": \""+titleZone+"\", \"titleDev\": \""+titleDev+"\",
							// \"titleUnit\": \""+titleUnit+"\"
							// titleInfo = ",\"degit\": "+degit+", \"titleName\": \""+titleName+"\",
							// \"titleUplow\": \""+titleUplow+"\", \"titleTname\": \""+titleTname+"\",
							// \"titleTag\": \""+titleTag+"\", \"titleUnitName\": \""+titleUnitName+"\"";
							titleInfo = ",\"degit\": " + degit + ", \"titleName\": \"" + titleName
									+ "\", \"titleUplow\": \"" + titleUplow + "\"";
						}
						String txt = txtMap == null ? null : (txtMap.get(alias + "_" + timeStr));
						txt = txt == null ? "" : txt.replace("\"", "\\\"");// 组json关键字处理
						String com = getType(op.getComType());
						com = com == null ? "" : com;
						String ks = "";
						String vs = "";
						if (("4".equals(com) || "15".equals(com)) && StringUtils.isNotEmpty(op.getDefaultKeyScript())
								&& StringUtils.isNotEmpty(op.getDefaultValueScript())
								&& !"[]".equals(op.getDefaultKeyScript()) && !"[]".equals(op.getDefaultValueScript())
								&& op.getDefaultKeyScript().startsWith("[")
								&& op.getDefaultValueScript().startsWith("[")
								&& op.getDefaultKeyScript().endsWith("]") && op.getDefaultValueScript().endsWith("]")) {
							try {
								JSONArray keyarr = JSONArray.parseArray(op.getDefaultKeyScript());
								JSONArray valarr = JSONArray.parseArray(op.getDefaultValueScript());
								for (int i = 0; i < keyarr.size(); i++) {
									String k = keyarr.getString(i);
									String v = valarr.getString(i);
									ks += "," + k;
									vs += "," + v;
								}
								if (ks.length() > 0) {
									ks = ks.substring(1);
								}
								if (vs.length() > 0) {
									vs = vs.substring(1);
								}
							} catch (Exception e) {
							}
						}
						// rv = ",\""+alias+"\":{\"uplimit\": "+uplimit+", \"lowlimit\": "+lowlimit+",
						// \"overmark\": "+String.valueOf(overmark)+", \"useFa\": true, \"info\":
						// \""+markInfo+"\""+titleInfo+"}";

						// 获取多选下拉框显示模式
						String displayMode = "";
						if ("15".equals(com) && multiSelectDisplayModeIdMap != null) {
							displayMode = multiSelectDisplayModeIdMap.getOrDefault(alias, "dropdown");
						}
						String displayModeStr = "15".equals(com) ? ",\"displayMode\": \"" + displayMode + "\"" : "";

						rv = ",\"" + alias + "\":{\"uplimit\": " + uplimit + ", \"lowlimit\": " + lowlimit
								+ ", \"useFa\": true, \"txt\": \"" + txt + "\", \"com\": \"" + com + "\",\"ks\": \""
								+ ks + "\",\"vs\": \"" + vs + "\"" + displayModeStr + ", \"info\": \"" + markInfo + "\"" + titleInfo + "}";
						haveul = true;
						break;
					}

				}
			}
		}
		// 没有方案，用采集点设置的上下限数据
		if (this.colULMap != null && colULMap.containsKey(alias) && !haveul) {
			Map<String, String> _map = colULMap.get(alias);
			if (_map != null) {
				Double uplimit = _map.get("uplimit") == null ? null : Double.parseDouble(_map.get("uplimit"));
				Double lowlimit = _map.get("lowlimit") == null ? null : Double.parseDouble(_map.get("lowlimit"));
				// Boolean overmark = false;
				// if(Coms.judgeDouble(val)) {//结果是数值
				// Double dv = Double.parseDouble(val);
				// if(uplimit!=null && dv > uplimit) {
				// overmark = true;
				// }
				// if(lowlimit!=null && dv < lowlimit) {
				// overmark = true;
				// }
				// }
				Map<String, String> titleMap = colTitleMap.get(alias);
				String titleInfo = "";
				if (titleMap != null) {
					String titleName = titleMap.get("name") == null ? ""
							: titleMap.get("name").replace("\"", "").replace("\n", "\\n").replace("\r", "\\r");
					;
					// String titleUnitName = titleMap.get("unitName") == null? "" :
					// titleMap.get("unitName").replace("\"", "").replace("\n", "\\n").replace("\r",
					// "\\r");;
					// String titleZone = titleMap.get("zone") == null? "" :
					// titleMap.get("zone").replace("\"", "").replace("\n", "\\n").replace("\r",
					// "\\r");
					// String titleDev = titleMap.get("dev") == null? "" :
					// titleMap.get("dev").replace("\"", "").replace("\n", "\\n").replace("\r",
					// "\\r");
					// String titleTname = titleMap.get("tname") == null? "" :
					// titleMap.get("tname").replace("\"", "").replace("\n", "\\n").replace("\r",
					// "\\r");
					// String titleTag = titleMap.get("tag") == null? "" :
					// titleMap.get("tag").replace("\"", "").replace("\n", "\\n").replace("\r",
					// "\\r");
					// String titleUnit = titleMap.get("unit") == null? "" :
					// titleMap.get("unit").replace("\"", "").replace("\n", "\\n").replace("\r",
					// "\\r");
					String titleUplow = titleMap.get("uplow") == null ? "" : titleMap.get("uplow");
					String degit = titleMap.get("degit") == null ? "3" : titleMap.get("degit");
					// , \"titleZone\": \""+titleZone+"\", \"titleDev\": \""+titleDev+"\",
					// \"titleUnit\": \""+titleUnit+"\"
					// titleInfo = ",\"degit\": "+degit+", \"titleName\": \""+titleName+"\",
					// \"titleUplow\": \""+titleUplow+"\", \"titleUnitName\": \""+titleUnitName+"\",
					// \"titleTname\": \""+titleTname+"\", \"titleTag\": \""+titleTag+"\"";
					titleInfo = ",\"degit\": " + degit + ", \"titleName\": \"" + titleName + "\", \"titleUplow\": \""
							+ titleUplow + "\"";
				}
				String txt = txtMap == null ? null : (txtMap.get(alias + "_" + timeStr));
				txt = txt == null ? "" : txt.replace("\"", "\\\"");// 组json关键字处理
				String com = getType(op.getComType());
				com = com == null ? "" : com;
				String ks = "";
				String vs = "";
				if (("4".equals(com) || "15".equals(com)) && StringUtils.isNotEmpty(op.getDefaultKeyScript())
						&& StringUtils.isNotEmpty(op.getDefaultValueScript())
						&& !"[]".equals(op.getDefaultKeyScript()) && !"[]".equals(op.getDefaultValueScript())
						&& op.getDefaultKeyScript().startsWith("[") && op.getDefaultValueScript().startsWith("[")
						&& op.getDefaultKeyScript().endsWith("]") && op.getDefaultValueScript().endsWith("]")) {
					try {
						JSONArray keyarr = JSONArray.parseArray(op.getDefaultKeyScript());
						JSONArray valarr = JSONArray.parseArray(op.getDefaultValueScript());
						for (int i = 0; i < keyarr.size(); i++) {
							String k = keyarr.getString(i);
							String v = valarr.getString(i);
							ks += "," + k;
							vs += "," + v;
						}
						if (ks.length() > 0) {
							ks = ks.substring(1);
						}
						if (vs.length() > 0) {
							vs = vs.substring(1);
						}
					} catch (Exception e) {
					}
				}
				// rv = ",\""+alias+"\":{\"uplimit\": "+uplimit+", \"lowlimit\": "+lowlimit+",
				// \"overmark\": "+String.valueOf(overmark)+", \"useFa\": false, \"info\":
				// \""+markInfo+"\""+titleInfo+"}";

				// 获取多选下拉框显示模式
				String displayMode = "";
				if ("15".equals(com) && multiSelectDisplayModeIdMap != null) {
					displayMode = multiSelectDisplayModeIdMap.getOrDefault(alias, "dropdown");
				}
				String displayModeStr = "15".equals(com) ? ",\"displayMode\": \"" + displayMode + "\"" : "";

				rv = ",\"" + alias + "\":{\"uplimit\": " + uplimit + ", \"lowlimit\": " + lowlimit
						+ ", \"useFa\": false, \"txt\": \"" + txt + "\", \"com\": \"" + com + "\",\"ks\": \"" + ks
						+ "\",\"vs\": \"" + vs + "\"" + displayModeStr + ", \"info\": \"" + markInfo + "\"" + titleInfo + "}";
			}
		}

		return rv;
	}

	// 获取台账数据
	private Map<String, Map<Date, String>> getData(Map<String, Map<Date, String>> dataList) {

		// Map<String, List<String>> oldDataMap = null;// 仪表，值数组
		// 显示时间点控制
		// 说明: 控制显示的时间点数量，保留未确认的时间点或最新的几个时间点。
		if (showTimeNum > 0 && showTimeList.size() > showTimeNum) {
			List<String> tsList = new ArrayList<String>();
			for (int i = 0, il = showTimeList.size(); i < il; i++) {
				String showt = showTimeList.get(i);
				if (!confirmTimeList.contains(showt) || i >= il - showTimeNum) {// 未确认或显示数量
					tsList.add(showt);
				}
			}
			showTimeList = tsList;
		}

		// 获取的所有实时数据
		Map<String, Map<Date, String>> map = new HashMap<String, Map<Date, String>>();

		List<String> getTags = new ArrayList<String>();

		// 整理仪表信息
		List<String> tagCodes = new ArrayList<String>();// 仪表信息
		List<String> colList = new ArrayList<String>();// 所有可输出列信息
		// 根据outparamMode判断配置模式：
		// 时间为列(mode=2)：收集所有仪表信息
		// 仪表为列(mode=1)：收集所有列信息，包括动态扩展列、时间列和指定仪表列
		if (new Integer(2).equals(this.conf.getOutparamMode())) {// 时间为列
			if (StringUtils.isNotEmpty(this.mlist)) {
				for (TdsAccountMeter meter : mlist) {
					if (!tagCodes.contains(meter.getDatasource())) {
						tagCodes.add(meter.getDatasource());
					}
				}
			}
		} else {// 仪表为列
			if (StringUtils.isNotEmpty(plist)) {
				this.tagFormat = new HashMap<String, Integer>();
				for (TdsAccountOutparamVo outparam : plist) {
					if (new Integer(1).equals(outparam.getDynamicMark())) {// 动态标识，动态扩展列
						if (StringUtils.isNotEmpty(this.mlist)) {
							for (TdsAccountMeter meter : mlist) {
								colList.add(meter.getTagid());
								if (!tagCodes.contains(meter.getDatasource())
										&& StringUtils.isNotEmpty(meter.getDatasource())) {
									tagCodes.add(meter.getDatasource());
									// colList.add(meter.getDatasource());
									// tagFormat.put(meter.getDatasource(), getTagDegit(meter.getDecimalDegit(),
									// conf.getMeterBit()));
								}
								tagFormat.put(meter.getTagid(),
										getTagDegit(meter.getDecimalDegit(), conf.getMeterBit()));
							}
						}
					} else if (new Integer(1).equals(outparam.getTimeMark())) {// 时间列
					} else if (StringUtils.isNotEmpty(outparam.getColType())
							&& !"rowConfirm".equalsIgnoreCase(outparam.getColType())
							&& new Integer(1).equals(outparam.getVisible())) {// 其他列
						colList.add(outparam.getAlias());
						// if("rowConfirm".equals(outparam.getAlias())) {
						// colList.add("rowConfirmOver");
						// }
					} else {// 指定仪表列
						String tag = outparam.getBindMeter();
						if (!tagCodes.contains(tag) && StringUtils.isNotEmpty(tag)) {
							tagCodes.add(tag);
							colList.add(tag);
							tagFormat.put(outparam.getAlias(),
									getTagDegit(outparam.getDecimalDegit(), conf.getMeterBit()));
						}
					}
				}
			}
		}
		// 无仪表信息直接退出 如果是平稳率类型数据且无仪表信息，直接返回已有数据。
		if (StringUtils.isEmpty(tagCodes) && "pwl".equals(tagType) && !isOutDataMode) {
			return dataList;
		}

		// 整理时间信息：时间为列(mode=2)：处理独立设置的时间，计算时间步长（间隔）
		// 仪表为列(mode=1)：使用配置的时间信息
		if (new Integer(2).equals(this.conf.getOutparamMode())) {// 时间为列，整理独立设置的时间
			List<Date> tempList = new ArrayList<Date>();
			Map<String, String> inmap = null;
			boolean reCalStep = false;
			for (TdsAccountOutparamVo outparam : plist) {
				if (new Integer(1).equals(outparam.getDynamicMark())) {// 动态标识，动态扩展列
					if (StringUtils.isNotEmpty(timeList)) {
						for (Date date : tempList) {
							if (!tempList.contains(date)) {// 去重
								tempList.add(date);
							}
						}
					}
				} else if (new Integer(1).equals(outparam.getMeterMark())) {// 仪表列
				} else {// 指定列
					if (inmap == null) {
						inmap = new HashMap<String, String>();
						for (TInPara temp : this.tInParas) {
							inmap.put(temp.getParaAlias(),
									temp.getValue() != null ? String.valueOf(temp.getValue()) : null);
						}
					}
					Date date = null;
					String bindDay = outparam.getBindDay();// 绑定参数
					if (StringUtils.isNotEmpty(bindDay)) {
						String v = inmap.get(bindDay);
						Date d = DateTimeUtils.parseDate(v);
						if (d == null) {// 内容不符合时间格式，未转化成功，取当前日期
							date = DateTimeUtils.parseDateTime(DateTimeUtils.getNowDateStr().substring(0, 10) + " "
									+ outparam.getBindTime() + ":00");
						} else {
							date = DateTimeUtils.parseDateTime(DateTimeUtils.formatDate(date).substring(0, 10) + " "
									+ outparam.getBindTime() + ":00");
						}
					} else {
						date = DateTimeUtils.parseDateTime(
								DateTimeUtils.getNowDateStr().substring(0, 10) + " " + outparam.getBindTime() + ":00");
					}

					if (!tempList.contains(date)) {// 去重
						tempList.add(date);
						reCalStep = true;
					}
				}
			}
			if (reCalStep) {
				// List<Date> tlist = tempList.stream().sorted(Comparator.comparing).col;
				List<Integer> stepList = new ArrayList<Integer>();
				stepList.add(this.istep);
				tempList.sort((o1, o2) -> o1.compareTo(o2));
				for (int i = 0, il = tempList.size(); i < il; i++) {
					if (i > 0) {
						long lstep = DateTimeUtils.diffDate(tempList.get(i), tempList.get(i - 1), DateTimeUtils.MINITE);
						if (lstep <= 0) {
							continue;
						}
						int _step = Integer.parseInt(String.valueOf(lstep));
						if (!stepList.contains(_step)) {
							stepList.add(_step);
						}
					}
				}
				if (stepList.size() > 1) {
					int is = stepList.get(0);
					for (int i = 1, il = stepList.size(); i < il; i++) {
						is = commonDivisor(is, stepList.get(i));
					}
					this.istep = is;
				}
			}
		} else {
			// 仪表为列，时间为配置信息，如果无时间信息，直接退出
			if (StringUtils.isEmpty(timeList)) {
				return map;
			}
		}

		// 时间信息、仪表信息获取会后，如果有已保存数据，从已存数据中判断哪些时间点已经保存数据，不用再获取了，只根据当前时间获取未保存的数据，如果所有时间点数据都已保存，无需再获取
		// TODO
		List<Date> dtlist = new ArrayList<Date>();
		Date nd = DateTimeUtils.getNowDate();
		List<String> haveDataList = null;
		// 分析已有数据(dataList)
		// 判断哪些时间点、哪些仪表需要获取数据
		// 根据不同模式(mobile/bound/pwl)处理已有数据
		if (dataList != null && dataList.size() > 0) {
			// String st = this.st;
			// String et = this.et;
			haveDataList = new ArrayList<String>();

			if ("mobile".equals(this.timeConf.getShowMode()) || "tz".equals(this.timeConf.getShowMode())) {
				getTags.addAll(tagCodes);
				for (Date dt : this.timeList) {// 循环时间点
					if (DateTimeUtils.bjDate(nd, dt) == 1) {
						dtlist.add(dt);
					}
				}
			} else if (!"pwl".equals(tagType) && !isOutDataMode) {// lims数据获取"bound".equals(this.timeConf.getShowMode())

				for (String tag : dataList.keySet()) {
					Map<Date, String> hmap = dataList.get(tag);// 已存获取数据
					if (hmap != null && hmap.get(null) != null) {
						// String tagCode = tagCodeIdMap.get(tag);//此处暂时取仪表位号，后期独立开发使用limis接口获取数据
						// if(StringUtils.isNotEmpty(tagCode)) {
						// haveDataList.add(tagCode);
						// }
						haveDataList.add(tag);
					}
				}

				// TOOD 后期可能有修正判断，超过多长时间后，数据不再获取
				int overdayget = 0;
				ApplyParams dto = new ApplyParams();
				dto.setApplyAlias("account_limis_overdayget");
				String overdaygetStr = applySrv.getApplyConfValue(dto);
				if (Coms.judgeLong(overdaygetStr)) {
					int overday = Integer.parseInt(overdaygetStr);
					if (overday >= 0) {
						overdayget = overday;
					}
				}
				if (DateTimeUtils.dayDiff(this.timeList.get(this.timeList.size() - 1), nd) > overdayget) {

				} else {
					for (Date dt : this.timeList) {// 循环时间点
						dtlist.add(dt);
					}
				}

			} else {// pwl synchronize
				// oldDataMap = new HashMap<String, List<String>>();
				Map<Date, List<String>> haveMap = new HashMap<Date, List<String>>();
				if ("synchronize".equals(this.timeConf.getShowMode())) {
					// 获取采集点的设置数据
					for (String tagId : dataList.keySet()) {
						Map<Date, String> hmap = dataList.get(tagId);// 已存获取数据+时间批次time初始化数据
						// 获取采集点的默认值数据
						String defalutVal = defaultMap.get(tagId);

						// 使用统一的默认值处理方法（保存时始终使用INPUT模式）
						defalutVal = processDefaultValue(tagId, defalutVal, "INPUT");
						for (Date d : hmap.keySet()) {
							if (StringUtils.isNotEmpty(defalutVal) && StringUtils.isEmpty(hmap.get(d))) {
								hmap.put(d, defalutVal);
							}
							// // 将时间点和仪表id关联起来 05.02
							// if (haveMap.containsKey(d)) {
							// haveMap.get(d).add(tagId);
							// } else {
							// List<String> tlist = new ArrayList<String>();
							// tlist.add(tagId);
							// haveMap.put(d, tlist);
							// }
							// 记录已有数据
							if (haveDataList == null) {
								haveDataList = new ArrayList<String>();
							}
							// // 将时间点和仪表id关联起来 05.02
							haveDataList.add(tagId + "_" + DateTimeUtils.formatDateTime(d));
						}
					}
				} else {
					for (String tagId : dataList.keySet()) {
						Map<Date, String> hmap = dataList.get(tagId);// 已存获取数据
						if (hmap != null && StringUtils.isNotEmpty(tagCodes)) {
							String tagCode = tagIdObjMap == null ? null
									: (tagIdObjMap.get(tagId) == null ? null : tagIdObjMap.get(tagId).getDatasource());// tag相当于id，获取相关仪表
							if (StringUtils.isNotEmpty(tagCode)) {
								for (Date d : hmap.keySet()) {
									if (haveMap.containsKey(d)) {
										haveMap.get(d).add(tagCode);
									} else {
										List<String> tlist = new ArrayList<String>();
										tlist.add(tagCode);
										haveMap.put(d, tlist);
									}

									haveDataList.add(tagId + "_" + DateTimeUtils.formatDateTime(d));

									// //平稳率仪表已保存数据整理，下面判断用
									// if(oldDataMap.containsKey(tagCode)) {
									// oldDataMap.get(tagCode).add(hmap.get(d));
									// }else {
									// List<String> tlist = new ArrayList<String>();
									// tlist.add(hmap.get(d));
									// oldDataMap.put(tagCode, tlist);
									// }

								}
							}
						}
					}
				}

				Date redt = StringUtils.isEmpty(this.reTime) ? null : DateTimeUtils.parseDateTime(this.reTime);
				// for (Date dt : this.timeList) {//循环时间点
				// String sj = DateTimeUtils.formatDateTime(dt);
				// List<String> tlist = haveMap.get(dt);
				// if(StringUtils.isNotEmpty(tlist)) {
				// if(!tlist.containsAll(tagCodes)) {//数据不全时，需要获取
				// List<String> minus = tagCodes.stream().filter(item ->
				// !tlist.contains(item)).collect(Collectors.toList());//取差集
				// getTags.addAll(minus);
				// if(DateTimeUtils.bjDate(nd, dt) == 1 && (dt.equals(redt) ||
				// !confirmTimeList.contains(sj))) {//重提不判断是否确认
				// dtlist.add(dt);
				// }
				// }
				// }else {
				// if(DateTimeUtils.bjDate(nd, dt) == 1 && (dt.equals(redt) ||
				// !confirmTimeList.contains(sj))) {//重提不判断是否确认
				// getTags.addAll(tagCodes);
				// dtlist.add(dt);
				// }
				// }
				// }

				if (redt != null) {// 重提只获取一个时间点的数据
					dtlist.add(redt);
					getTags.addAll(tagCodes);
				} else {
					for (Date dt : this.timeList) {// 循环时间点
						String sj = DateTimeUtils.formatDateTime(dt);
						// List<String> tlist = haveMap.get(dt);
						List<String> tlist = haveMap.getOrDefault(dt, new ArrayList<>());
						if (StringUtils.isNotEmpty(tlist)) {
							// 05.02 修改
							if (!tlist.containsAll(tagCodes)) {// 数据不全时，需要获取
								List<String> minus = tagCodes.stream().filter(item -> !tlist.contains(item))
										.collect(Collectors.toList());// 取差集
								getTags.addAll(minus);

								if (DateTimeUtils.bjDate(nd, dt) == 1 && (confirmTimeList == null || !confirmTimeList.contains(sj))) {//
									// && (dt.equals(redt) || !confirmTimeList.contains(sj))) {//重提不判断是否确认
									dtlist.add(dt);
								}
							}

							// 查找该时间点需要查询的仪表(从tagCodes中排除已有数据的仪表)
							// List<String> needQueryTags = new ArrayList<>();
							// for (String tagCode : tagCodes) {
							// if (!tlist.contains(tagCode)) {
							// needQueryTags.add(tagCode);
							// }
							// }
							// // 如果有需要查询的仪表，添加到getTags
							// if (!needQueryTags.isEmpty()) {
							// getTags.addAll(needQueryTags);

							// // 只添加当前时间之前且未确认的时间点
							// if (DateTimeUtils.bjDate(nd, dt) == 1 && !confirmTimeList.contains(sj)) {
							// dtlist.add(dt);
							// }
							// }
							// 05.02 修改
						} else {
							if (DateTimeUtils.bjDate(nd, dt) == 1 && (confirmTimeList == null || !confirmTimeList.contains(sj))) {// &&
																										// (dt.equals(redt)
																										// ||
																										// !confirmTimeList.contains(sj)))
																										// {//重提不判断是否确认
								getTags.addAll(tagCodes);
								dtlist.add(dt);
							}
						}
					}
				}

			}

		} else {
			getTags.addAll(tagCodes);
			for (Date dt : this.timeList) {// 循环时间点
				if (DateTimeUtils.bjDate(nd, dt) == 1) {
					dtlist.add(dt);
				}
			}
		}

		if (StringUtils.isNotEmpty(getTags)) {
			getTags = getTags.stream().distinct().collect(Collectors.toList());// 去重
		}
		// 有起止时间，有仪表，有需要获取的时间点，进行接口调用，获取数据并保存
		// 根据起止时间、需要获取的时间点和仪表列表获取数据
		// 不同模式使用不同数据源:
		// mobile/tz模式：调用getMobileData从移动端获取
		// lims模式：调用getLimsData从分析系统获取
		// pwl模式：调用queryRtdbTagData从实时数据库获取

		if (this.st != null && this.et != null && dtlist.size() > 0
				&& (StringUtils.isNotEmpty(getTags) || !"pwl".equals(tagType) || isOutDataMode)) {
			if (haveDataList == null) {
				haveDataList = new ArrayList<String>();
			}
			// 重新计算起始时间
			this.st = DateTimeUtils.formatDateTime(dtlist.get(0));
			this.et = DateTimeUtils.formatDateTime(dtlist.get(dtlist.size() - 1));
			// this.istep = //不再重新计算istep
			int getstep = this.istep * 60;// 接口这个间隔是秒
			List<LimsDataVo> limisList = null;

			Boolean synMode = "synchronize".equals(this.timeConf.getShowMode());// 同步模式

			if (synMode && inputMode != null) {// 同步数据，获取移动端保存的数据
				// if(inputMode == null) {//自己推时间模式
				// }else {
				// getMobileData(map);
				// }
				if (dataList.size() == 0) {
					getMobileData(map);
				}
			} else if ("mobile".equals(this.timeConf.getShowMode()) || "tz".equals(this.timeConf.getShowMode())) {// 移动端数据外部获取模式，获取移动端保存的数据
				getMobileData(map);
				// }else if("tz".equals(this.timeConf.getShowMode()))
				// {//外部获取模式，巡检数据，使用原获取及保存方式，无提取数据
				// getTzData(map);
			} else if (!"pwl".equals(tagType) && !synMode) {// limis有特定接口获取数据
															 // "bound".equals(this.timeConf.getShowMode())
				// getstep = 0;//lims数据间隔给0
				limisList = new ArrayList<LimsDataVo>();
				if (StringUtils.isNotEmpty(this.mlist)) {
					for (TdsAccountMeter meter : mlist) {
						LimsDataVo vo = ObjUtils.copyTo(meter, LimsDataVo.class);
						vo.setDotId(meter.getTagid());
						vo.setProcessUnit(meter.getProcessUnitName());
						vo.setProductName(meter.getProductName());
						vo.setSamplingPoint(meter.getSamplingPoint());
						vo.setAnalysisName(meter.getAnalysisName());
						vo.setItemName(meter.getAnalysisSubName());
						limisList.add(vo);
					}
					LimsDataDto dto = new LimsDataDto();
					dto.setKssj(this.st);
					dto.setJzsj(this.et);
					dto.setLimsyb(limisList);
					HashMap<String, List<LimsDataVo>> jgmap = accountServ.getLimsData(dto);
					if (jgmap != null && jgmap.size() > 0) {
						for (String tagId : jgmap.keySet()) {
							List<LimsDataVo> _list = jgmap.get(tagId);
							Map<Date, String> _map = new LinkedHashMap<Date, String>();
							for (LimsDataVo data : _list) {
								Date d = DateTimeUtils.parseDateTime(data.getTime());
								String val = data.getValue();
								_map.put(d, val);
							}
							map.put(tagId, _map);
						}
					}
				}
			} else {// 平稳率时间点数据，接口从R3db中获取 pwl synMode
				// JSONObject jsonObject = null;
				Map<String, List<Tag>> rdbtaglistMap = new LinkedHashMap();

				String tempSt = st;
				String tempEt = et;
				if (tempSt.equals(tempEt)) {
					tempSt = DateTimeUtils
							.formatDateTime(DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(tempSt), -getstep));
					tempEt = DateTimeUtils
							.formatDateTime(DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(tempEt), getstep));
				}
				// 用于第二次录入记录时间和仪表
				Map<Date, List<String>> blmap = new HashMap<>();
				for (Date d : dtlist) {
					List<String> tlist = getTags.stream().collect(Collectors.toList());
					blmap.put(d, tlist);
				}

				long t1 = System.currentTimeMillis();
				try {
					// 使用新的单时间点循环取值逻辑，解决时间匹配问题
					rdbtaglistMap = collectRealTimeDataByTimePoints(getTags, dtlist);

				} catch (Exception e) {
					rdbtaglistMap = null;
					log.error("台账获取仪表数据失败：" + e.getMessage());
				}
				long t2 = System.currentTimeMillis();
				log.info("台账: 数据源别名: {}, 核算对象: {}, 台账id: {}， 获取实时数据接口执行耗时: {}ms",getDSAlias(), this.confCode, this.accountId,	 (t2 - t1));

				String tagstr = null;
				StringBuilder tsb = new StringBuilder();
				if (getTags.size() > 10) {
					for (int i = 0; i < 10; i++) {
						tsb.append(",");
						tsb.append(getTags.get(i));
					}
				} else {
					for (String s : getTags) {
						tsb.append(",");
						tsb.append(s);
					}
				}
				if (tsb.length() > 0) {
					tagstr = tsb.substring(1);
				}

				log.info("台账数据源获取实时数据：" + tempSt + "~" + tempEt + ", 步长：" + getstep + " 仪表数量：" + getTags.size()
						+ " 获取仪表结果：" + rdbtaglistMap.toString() + "， 部分仪表：" + tagstr);

				if (StringUtils.isNotEmpty(rdbtaglistMap)) {
					for (String timeStr : rdbtaglistMap.keySet()) {
						List<Tag> tags = rdbtaglistMap.get(timeStr);
						for (Tag tag : tags) {
							// 05.04 修改
							if (tag == null) {
								log.warn("台账仪表实时数据库接口返回数据错误-无仪表");
								continue;
							}
							String tagCode = tag.getTagCode().toUpperCase();// 转大写

							// 获取或创建该仪表的数据映射，避免覆盖已有数据
							Map<Date, String> _map = map.get(tagCode);
							if (_map == null) {
								_map = new LinkedHashMap<>();
								map.put(tagCode, _map);
							}

							// 数采数据工序判断过滤，如果仪表停用，不获取采集数据
							List<Map<String, Object>> faData = null;
							// if (tagFaULmap != null) {
							if (StringUtils.isNotEmpty(tagFaULmap)) {
								String tagId = tagCodeIdMap.get(tagCode);
								if (StringUtils.isNotEmpty(tagId)) {
									faData = tagFaULmap.get(tagId);
								}
							}

							List<TagData> ilist = tag.getDatas();
							if (StringUtils.isEmpty(ilist)) {
								log.warn("台账仪表实时数据库接口返回数据错误-无仪表值(tagCode:{})", tagCode);
								continue;
							}
							if (StringUtils.isNotEmpty(ilist)) {
//							for (TagData da : ilist) {
								TagData da = ilist.get(ilist.size() - 1);
								if (da == null) {
									log.error("台账仪表接口返回时间错误：" + this.confCode + "|" + tagCode + "|");
									continue;
								}

								// 使用实际的数据时间，而不是timeStr
								String ybdate = da.getDatetime();
								Date dt = null;
								if (ybdate != null && ybdate.length() >= 19) {
									ybdate = ybdate.substring(0, 19);
									try {
										dt = DateTimeUtils.parseDateTime(ybdate);
									} catch (Exception e) {
										log.error("台账仪表接口获取结果时间错误：" + this.confCode + "|" + tagCode + "|" + ybdate + "|"
												+ da.getValue());
									}
								} else {
									log.error("台账仪表接口返回时间错误：" + this.confCode + "|" + tagCode + "|" + ybdate + "|"
											+ da.getValue());
								}

								// 解析请求的时间点（从timeStr获取）
								Date requestedTime = null;
								try {
									requestedTime = DateTimeUtils.parseDateTime(timeStr);
								} catch (Exception e) {
									log.error("台账仪表请求时间解析失败：" + this.confCode + "|" + tagCode + "|" + timeStr);
									continue;
								}

								// 检查请求的时间是否在需要的时间点列表中
								if (requestedTime == null || !dtlist.contains(requestedTime)) {
									continue;
								}

								if (faData == null || faUseTag(faData, requestedTime)) {// 没有方案或者在使用范围内

									String val = da.getValue() == null ? "" : String.valueOf(da.getValue());
									log.debug("台账仪表接口返回数据，核算对象：{}，仪表：{}，实时值{}，请求时间：{}，实际时间：{}",
											this.confCode, tagCode, val, requestedTime, dt);

									if (StringUtils.isNotEmpty(val)) {
										if (Coms.isFind(val, "^-?\\d+\\.?\\d+[E][-]?\\d+$")) {
											try {
												String s = "##################################.#############################";
												DecimalFormat df = new DecimalFormat(s);
												val = df.format(Double.parseDouble(val));
											} catch (Exception e) {
												log.error("台账仪表接口返回数据，科学计数法转换错误：核算对象：{}，仪表：{}，实时值{}，时间：{}", this.confCode, tagCode, val, requestedTime);
											}
										}

										// 将数据存储到请求的时间点，而不是实际返回的时间点
										_map.put(requestedTime, val);
									}

									// 整理未取到数据的仪表时间相关信息
									//List<String> taglist2 = blmap.get(dt);
									//if (StringUtils.isNotEmpty(taglist2) && StringUtils.isNotEmpty(val)) {
									//	taglist2.remove(tagCode);
									//}
								} else {
									log.info("台账仪表根据方案未使用！" + this.confCode + "|" + tagCode + "|" + requestedTime);
								}
								// }
//								break;
							}
						}
					}
					// 输出异常日志,未获取到时间点数据或数据为0
					StringBuilder msg0 = new StringBuilder();
					StringBuffer msg1 = new StringBuffer();

					//log.info("台账数据源获取实时数据：rdbtaglistMap：{}，map：{}， getTags：{}", rdbtaglistMap.toString(), map, getTags.toString());

					for (String tagCode : getTags) {
						Map<Date, String> tmap = map.get(tagCode);
						if (tmap == null || tmap.size() == 0) {
							log.info("台账仪表未获取数据atnotdata：" + tempSt + "~" + tempEt + ", 步长：" + getstep + " 仪表："
									+ tagCode);
						} else {
							for (Date d : dtlist) {
								String val = tmap.get(d);
								if (StringUtils.isEmpty(val)) {
									msg1.append(DateTimeUtils.formatDateTime(d) + "--" + tagCode + "|");
									// log.info("台账仪表时间点未获取数据attnotdata："+DateTimeUtils.formatDateTime(d)+",
									// 仪表："+tagCode);
								} else if (Coms.judgeDouble(val) && Double.parseDouble(val) == 0) { // "0".equals(val)
																									// ||
																									// "0.0".equals(val)
																									// ||
																									// "0.00".equals(val)
																									// ||
																									// "0.000".equals(val))
																									// {
									msg0.append(DateTimeUtils.formatDateTime(d) + "--" + tagCode + "--" + val + "|");
									// log.info("台账仪表时间点获取数据为0，待分析att0："+DateTimeUtils.formatDateTime(d)+",
									// 仪表："+tagCode+" val:"+val);\

									// 仪表数据为0，按异常数据重新获取
									if (blmap.containsKey(d)) {
										List<String> tlist = blmap.get(d);
										if (!tlist.contains(tagCode)) {
											tlist.add(tagCode);
										}
									} else {
										List<String> tlist = new ArrayList<String>();
										tlist.add(tagCode);
										blmap.put(d, tlist);
									}
								}
							}
						}
					}
					if (msg0.length() > 0) {
						log.info("台账仪表时间点获取数据为0，待分析att0：" + msg0.toString());
					}
					if (msg1.length() > 0) {
						log.info("台账仪表时间点未获取数据attnotdata：" + msg1.toString());
					}
					msg0 = null;
					msg1 = null;
				} else {
					// 输出异常日志
					log.info("台账本次未获取到任何数据anotdata：" + tempSt + "~" + tempEt + ", 步长：" + getstep + " 仪表数量："
							+ getTags.size() + " 获取仪表结果：" + rdbtaglistMap.toString() + " 部分仪表："
							+ tagstr);
				}
				// test data
				// Map<Date, String> _map = new LinkedHashMap<Date, String>();
				// Date dt = DateUtil.parseDateTime("2024-04-10 20:10:00");
				// _map.put(dt, "5");
				// map.put("a1", _map);

				//↓↓↓ 从源头已经卡齐采集时间，不会出现时间点错位问题，所以不需要二次采集补偿处理了
				// 未取到数据判断，调用第二次获取接口(包括未取到数据和数据为0的仪表)
				/*if (blmap != null) {

					// 更新方案中无需进行录入的仪表数据
					for (String tagCode : getTags) {
						// 数采数据工序判断过滤，如果仪表停用，不获取采集数据
						List<Map<String, Object>> faData = null;
						// if (tagFaULmap != null) {
						if (StringUtils.isNotEmpty(tagFaULmap)) {
							String tagId = tagCodeIdMap.get(tagCode);
							if (StringUtils.isNotEmpty(tagId)) {
								faData = tagFaULmap.get(tagId);
							}
						}
						for (Date d : dtlist) {
							if (faData == null || faUseTag(faData, d)) {// 没有方案或者在使用范围内
							} else {
								List<String> taglist2 = blmap.get(d);
								if (StringUtils.isNotEmpty(taglist2)) {
									taglist2.remove(tagCode);
								}
							}
						}
					}

					// Date sd = null, ed = null;
					// Set<String> tags = new HashSet<String>();
					for (Date d : blmap.keySet()) {
						// 接口再次获取数据，返回数据补充到map中
						List<String> taglist2 = blmap.get(d);
						if (StringUtils.isNotEmpty(taglist2)) {
							List<Tag> phdtaglist = null;
							try {
								phdtaglist = rtdbSrv.queryLastTagDatas(taglist2, DateTimeUtils.formatDateTime(d));
								log.info("getTag2台账二次获取仪表数据：" + DateTimeUtils.formatDateTime(d) + "tag:"
										+ Coms.listToString(taglist2));
							} catch (Exception e) {
								phdtaglist = null;
								log.error("getTag2台账二次获取仪表数据失败：" + e.getMessage());
							}

							// 赋值
							if (StringUtils.isNotEmpty(phdtaglist)) {
								for (Tag tag : phdtaglist) {
									// 05.04 修改
									if (tag == null) {
										log.info("台账仪表接口返回数据错误-无仪表");
										continue;
									}
									String tagCode = tag.getTagCode().toUpperCase();// 转大写

									List<TagData> ilist = tag.getDatas();
									String val = null;
									if (StringUtils.isNotEmpty(ilist)) {
										val = ilist.get(0).getValue() == null ? ""
												: String.valueOf(ilist.get(0).getValue());
									} else {
										continue;
									}
									if (StringUtils.isEmpty(val)) {
										log.info("getTag2台账仪表接口返回数据错误：" + tag.getTagCode() + "|"
												+ ilist.get(0).getValue());
										continue;
									}
									// 科学计数法转化处理
									if (Coms.isFind(val, "^-?\\d+\\.?\\d+[E][-]?\\d+$")) {
										try {
											String s = "##################################.#############################";
											DecimalFormat df = new DecimalFormat(s);
											val = df.format(Double.parseDouble(val));
										} catch (Exception e) {
										}
									}

									Map<Date, String> tmap = map.get(tagCode);
									if (tmap == null) {
										tmap = new HashMap<Date, String>();
										tmap.put(d, val);
										map.put(tagCode, tmap);
									} else {
										tmap.put(d, val);
									}
								}
							}
							// for (String tag : taglist2) {
							// String val = null;
							// Map<Date, String> tmap = map.get(tag);
							// if(tmap == null) {
							// tmap = new HashMap<Date, String>();
							// tmap.put(d, val);
							// map.put(tag, tmap);
							// }else if(StringUtils.isNotEmpty(val)) {
							// tmap.put(d, val);
							// }
							// }

							// for (String tag : taglist2) {
							// if(!tags.contains(tag)) {
							// tags.add(tag);
							// }
							// }
							//
							// if(sd == null || DateTimeUtils.bjDate(sd, d) == 1) {
							// sd = d;
							// }
							// if(ed == null || DateTimeUtils.bjDate(d, ed) == 1) {
							// ed = d;
							// }
						} else {
							log.info("notgetTag2台账未进行二次获取：" + DateTimeUtils.formatDateTime(d) + this.confCode);
						}
					}

					// if(tags.size() > 0) {
					// //接口再次获取数据，返回数据补充到map中
					//
					// }
				}*/
			}

			// 未保存的数据获取后，如果有值，则保存，不再获取
			// SysUser user = SysUserHolder.getCurrentUser();
			JSONArray insertData = new JSONArray();
			JSONArray updateData = new JSONArray();

			if ("bound".equals(this.timeConf.getShowMode())) {// 时间段
				String id = this.st + "," + this.et;
				if (StringUtils.isNotEmpty(limisList)) {
					for (LimsDataVo data : limisList) {
						// String tagId = tagCodeIdMap.get(tag);//根据仪表位号获取标识
						String tagId = data.getDotId();

						String oldVal = null;
						if (dataList != null && dataList.size() > 0) {
							Map<Date, String> hmap = dataList.get(tagId);// 已存获取数据
							if (hmap != null && hmap.get(null) != null) {
								oldVal = hmap.get(null);
							}
						}

						Map<Date, String> _map = map.get(tagId);
						// if("tg.pic208034.pv".equals(tag)) {
						// _map = new LinkedHashMap<Date, String>();
						// _map.put(DateTimeUtils.parseDateTime("2023-11-14 01:41:59"), "101.456");
						// _map.put(DateTimeUtils.parseDateTime("2023-11-14 03:41:59"), "222.123");
						// }
						if (_map != null && _map.size() > 0) {
							StringBuffer sb = new StringBuffer();
							for (Date d : _map.keySet()) {
								String val = _map.get(d);
								if (StringUtils.isNotEmpty(val)) {
									if (sb.length() > 0) {
										sb.append("<br/>");// 多数据改为换行显示
									}
									// sb.append(val+"("+DateTimeUtils.formatDateTime(d)+")");
									sb.append(val);
								}
							}

							if (oldVal == null) {// 未保存过
								if (sb.length() > 0) {
									long slong = DateTimeUtils.parseDateTime(this.timeBoundBc1).getTime();
									JSONObject aobj = new JSONObject();
									// aobj.put("_id", TMUID.getUID());
									// aobj.put("dsAlias", this.getDSAlias());
									// aobj.put("formId", this.accountId);
									// aobj.put("dataId", this.dataId);
									aobj.put("col", tagId);// 列名
									// aobj.put("tag", tagId);
									// aobj.put("tagId", tagId);
									// aobj.put("dateType", "tag");
									// aobj.put("timetype", "bound");
									// aobj.put("rq", this.version);
									// aobj.put("rqlong", this.versionLong);
									aobj.put("sj", id);
									aobj.put("sjlong", slong);
									// aobj.put("val", sb.toString());
									aobj.put("valstr", sb.toString());
									aobj.put("creTime", nd);
									// aobj.put("creUserId", user.getId());
									// aobj.put("creUserName", user.getRealName());
									aobj.put("updTime", null);
									aobj.put("updUserId", null);
									// aobj.put("updUserName", null);
									// aobj.put("additionVal", null);//附加值
									aobj.put("edit", false);
									// aobj.put("tmused", 1);
									// aobj.put("confirmDiff", 0);//确认差（秒）
									// aobj.put("rowConfirmBound", null);//确认范围（分钟）
									// aobj.put("rowConfirmOver", false);//确认超时

									// aobj.put("unitCode", confCode);//核算单元
									// aobj.put("bc", shiftCode);//班次
									// aobj.put("accountId", accountId);//accountId
									insertData.add(aobj);

									// haveDataList.add(tag);

									if (dataList == null) {
										dataList = new HashMap<String, Map<Date, String>>();
									}
									Map<Date, String> bmap = dataList.get(tagId);
									if (bmap == null) {
										bmap = new HashMap<Date, String>();
										bmap.put(null, sb.toString());//
										dataList.put(tagId, bmap);
									} else {
										bmap.put(null, sb.toString());// 更新数据
									}
								}
							} else {// 保存过，对比两个值，不相同时进行更新
								if (sb.length() > 0 && !sb.toString().equals(oldVal)) {
									Map mmap = mongoMap.get(tagId);
									if (mmap != null) {
										JSONObject aobj = new JSONObject(mmap);
										// aobj.put("val", sb.toString());
										aobj.put("valstr", sb.toString());
										aobj.put("updTime", nd);
										aobj.put("updUserId", user.getId());
										// aobj.put("updUserName", user.getRealName());
										updateData.add(aobj);

										Map<Date, String> bmap = dataList.get(tagId);
										if (bmap == null) {
											bmap = new HashMap<Date, String>();
											bmap.put(null, sb.toString());//
											dataList.put(tagId, bmap);
										} else {
											bmap.put(null, sb.toString());// 更新数据
										}
									}
								}
							}

						}
					}
				}
			} else {
				boolean haveConfirm = false;
				for (TdsAccountOutparamVo op : this.plist) {
					if ("rowConfirm".equals(op.getColType())) {// 行确认类型
						haveConfirm = new Integer(1).equals(op.getVisible());
						break;
					}
				}
				// 判断有确认列，有确认间隔，按间隔处理并判断是有有信息，无需确认数据的时间点无信息时，插入默认信息 /
				if (haveConfirm && this.confirmStep > 0) {
					Map<Date, String> hrmap = dataList == null ? null : dataList.get("rowConfirm");

					Date ed = DateTimeUtils.parseDateTime(this.et);
					Date sd = DateTimeUtils.parseD(this.st.substring(0, 10), DateTimeUtils.YYYY_MM_DD);// 从开始时间日期的0点开始

					Date posd = (Date) sd.clone();
					Date td = (Date) sd.clone();

					for (int i = 0, il = 30; i < il && DateTimeUtils.bjDate(ed, td) >= 0; i++) {// 加数避免死循环
						td = DateTimeUtils.doMinute(td, this.confirmStep);// 下个需确认的时间点
						String posdstr = DateTimeUtils.format(posd, DateTimeUtils.YYYY_MM_DD);
						String tdstr = DateTimeUtils.format(td, DateTimeUtils.YYYY_MM_DD);

						if (posdstr.equals(tdstr)) {// 同一天
							for (Date date : dtlist) {// 时间点
								if (DateTimeUtils.bjDate(posd, date) >= 0) {
									continue;
								}
								if (DateTimeUtils.bjDate(td, date) == 1) {
									// 这个时间点没确认数据，插入 /
									insertNoConfirm(date, nd, insertData, hrmap);
									posd = date;
								} else {
									posd = date;
									break;
								}
							}
						} else {
							td = DateTimeUtils.parseD(tdstr, DateTimeUtils.YYYY_MM_DD);// 下个需确认的时间点0点
							// td = DateTimeUtils.doMinute(td, this.confirmStep);//下个需确认的时间点
							for (Date date : dtlist) {// 时间点
								if (DateTimeUtils.bjDate(posd, date) >= 0) {
									continue;
								}
								if (DateTimeUtils.bjDate(td, date) == 1) {
									// 这个时间点没确认数据，插入 /
									insertNoConfirm(date, nd, insertData, hrmap);
									posd = date;
								} else {
									posd = date;
									break;
								}
							}
						}

						if (posd.equals(dtlist.get(dtlist.size() - 1))) {
							break;
						}
					}
				}

				// String timetype = "point";
				// String tagType = "tag";
				// if("mobile".equals(this.timeConf.getShowMode())) {//移动端数据外部获取模式
				// timetype = "mobile";
				// tagType = "custom";
				// }

				if (synMode) {// 同步模式，调用外部数据存储
					List<Date> sjlist = dtlist;// 再次保存，保存无历史数据，此次获取数据的时间点
					if (mobileSaveStatus) {// 第一次保存，保存所有时间点
						sjlist = timeList;
						String teamId = getEffectiveOrgCode(); // 优先使用传入的orgcode，如果为空使用用户机构
						applySrv.extDataSave(sendUnitCode, shiftCode, dataId, sjlist, infoTimeIdMap, timeBoundBc1,
								timeBoundBc2, colList, tagIdObjMap, map, dataList,jobInputTimeIdMap, teamId);
					}
				} else {
					for (Date date : dtlist) {// 时间点

						for (String tagId : colList) {
							// for (String tagId : map.keySet()) {
							if (haveDataList != null
									&& haveDataList.contains(tagId + "_" + DateTimeUtils.formatDateTime(date))) {// 已有数据不再重复保存
								continue;
							}
							TdsAccountMeter tagObj = tagIdObjMap.get(tagId);
							String tagCode = tagObj == null ? null : tagObj.getDatasource();// tagIdObjMap.get(tagId)==null?null:tagIdObjMap.get(tagId).getDatasource();
							Map<Date, String> _map = map.get(tagCode);// map.get(tagId);
							if (_map != null && _map.containsKey(date)) {
								String val = _map.get(date);
								if (StringUtils.isNotEmpty(val)) {
									JSONObject aobj = new JSONObject();
									// aobj.put("_id", TMUID.getUID());
									// aobj.put("dsAlias", this.getDSAlias());
									// aobj.put("formId", this.accountId);
									// aobj.put("dataId", this.dataId);
									aobj.put("col", tagId);// 列名
									// aobj.put("tag", tagCode);
									// aobj.put("tagId", tagId);
									// aobj.put("dateType", tagType);
									// aobj.put("timetype", timetype);
									// aobj.put("rq", this.version);
									// aobj.put("rqlong", this.versionLong);
									aobj.put("sj", DateTimeUtils.formatDateTime(date));
									aobj.put("sjlong", date.getTime());
									// aobj.put("val", val);
									aobj.put("valstr", val);
									aobj.put("creTime", nd);
									// aobj.put("creUserId", user.getId());
									// aobj.put("creUserName", user.getRealName());
									aobj.put("updTime", null);
									aobj.put("updUserId", null);
									// aobj.put("updUserName", null);
									aobj.put("additionVal", null);// 附加值
									aobj.put("edit", false);
									// aobj.put("tmused", 1);
									// aobj.put("confirmDiff", 0);//确认差（秒）
									// aobj.put("rowConfirmBound", null);//确认范围（分钟）
									// aobj.put("rowConfirmOver", false);//确认超时
									//
									// aobj.put("unitCode", confCode);//核算单元
									// aobj.put("bc", shiftCode);//班次
									// aobj.put("accountId", accountId);//accountId
									insertData.add(aobj);

									haveDataList.add(tagId + "_" + DateTimeUtils.formatDateTime(date));
								}
							}
						}
					}
				}

			}
			// 将新获取的数据构建为JSONObject
			// 根据数据类型(bound/point)不同构建不同的数据结构
			// 保存到MongoDB，根据是否有新版数据结构选择不同的保存方式
			if (insertData.size() > 0 && !isOutDataMode) {// 移动数据不存MongoDB

				if (StringUtils.isNotEmpty(newdata)) {// 有新数据，针对新数据进行操作
					JSONObject tobj = new JSONObject(newdata.get(0));
					JSONArray arr = tobj.getJSONArray("info");
					if (arr == null) {
						arr = new JSONArray();
					}
					arr.addAll(insertData);
					tobj.put("info", arr);
					// JSONArray alist = new JSONArray();
					// alist.add(tobj);
					mongoDBServ.updateById(accountNewTab + "_" + this.getDSAlias(), tobj);
				} else {// 没有新数据，看是否有历史数据
					if (haveMongoDbData) {// 有MongoDB数据
						mongoDBServ.insertBatch(accountTabName + "_" + this.getDSAlias(), insertData);// TODO
																										// 考虑历史数据兼容问题，加个判断，如果这张台账没保存过，从新的开始
					}
					// else {
					// JSONObject tobj = new JSONObject();
					// tobj.put("_id", TMUID.getUID());
					// tobj.put("unitCode", confCode);//核算单元
					// tobj.put("bc", shiftCode);//班次
					// tobj.put("acountId", accountId);//accountId
					// tobj.put("rq", this.version);
					// tobj.put("rqlong", this.versionLong);
					// tobj.put("tmused", 1);
					// tobj.put("mode", 1);
					// tobj.put("info", insertData);
					// JSONArray alist = new JSONArray();
					// alist.add(tobj);
					// mongoDBServ.insertBatch(accountNewTab+"_"+this.getDSAlias(), alist);
					// }
				}
			}
			if (updateData.size() > 0 && !isOutDataMode) {
				if (StringUtils.isNotEmpty(newdata)) {// 有新数据，针对新数据进行操作
					// 整理更新数据
					Map<String, JSONObject> tmap = new HashMap<String, JSONObject>();
					for (int i = 0, il = updateData.size(); i < il; i++) {
						JSONObject o = updateData.getJSONObject(i);
						tmap.put(o.getString("col") + "_" + o.getString("sj"), o);
					}
					// for (Object object : updateData) {
					// JSONObject o = (JSONObject)object;
					// tmap.put(o.getString("col")+"_"+o.getString("sj"), o);
					// }
					// 删除已有的更新的数据
					JSONObject tobj = new JSONObject(newdata.get(0));
					JSONArray arr = tobj.getJSONArray("info");
					arr = arr == null ? new JSONArray() : arr;
					for (Iterator iter = arr.iterator(); iter.hasNext();) {
						JSONObject o = new JSONObject((Map) iter.next());
						String key = o.getString("col") + "_" + o.getString("sj");
						if (tmap.containsKey(key)) {
							iter.remove();
						}
					}
					// for (Iterator iter = arr.iterator(); iter.hasNext();) {
					// JSONObject o = (JSONObject) iter.next();
					// String key = o.getString("col")+"_"+o.getString("sj");
					// if(tmap.containsKey(key)) {
					// iter.remove();
					// }
					// }
					// 添加要更新的数据
					arr.addAll(insertData);
					tobj.put("info", arr);
					// JSONArray alist = new JSONArray();
					// alist.add(tobj);
					mongoDBServ.updateById(accountNewTab + "_" + this.getDSAlias(), tobj);
				} else {// 没有新数据，处理历史数据
					mongoDBServ.updateBatchById(accountTabName + "_" + this.getDSAlias(), updateData);// TODO
																										// 考虑历史数据兼容问题，加个判断，如果这张台账没保存过，从新的开始
				}
			}

		}
		// test调整保存结构
		// List<JSONObject> jsonList = new ArrayList<JSONObject>();
		// for (int i = 0; i < 2; i++) {
		// JSONObject aobj = new JSONObject();
		// aobj.put("col", i);//列名
		// aobj.put("dateType", tagType);
		// aobj.put("sj", "2023-01-01 00:00:00");
		// aobj.put("sjlong", nd.getTime());
		// aobj.put("valstr", i);
		// aobj.put("creTime", nd);
		// aobj.put("updTime", null);
		// aobj.put("updUserId", null);
		// aobj.put("updUserName", null);
		// aobj.put("edit", false);
		//// aobj.put("confirmDiff", 0);//确认差（秒）
		//// aobj.put("rowConfirmOver", false);//确认超时
		// jsonList.add(aobj);
		// }
		// JSONObject tobj = new JSONObject();
		// tobj.put("_id", TMUID.getUID());
		// tobj.put("unitCode", "00000"+confCode);//核算单元
		// tobj.put("bc", "00000"+shiftCode);//班次
		// tobj.put("acountId", accountId);//accountId
		// tobj.put("rq", this.version);
		// tobj.put("info", jsonList);
		// tobj.put("rqlong", this.versionLong);
		// JSONArray alist = new JSONArray();
		// alist.add(tobj);
		// mongoDBServ.insertBatch(accountTabName+"_"+this.getDSAlias(), alist);

		// 根据不同模式(mobile/bound/pwl)合并新获取的数据和历史数据
		// 处理数据覆盖的逻辑，一般是已保存数据覆盖新获取的数据
		if (dataList != null && dataList.size() > 0) {
			if ("mobile".equals(this.timeConf.getShowMode())) {// 移动端数据外部获取模式，获取移动端保存的数据
				Map<String, Map<Date, String>> tmap = new HashMap<String, Map<Date, String>>();
				// 已保存数据dataList为基础，进行交叉处理
				for (String col : colList) {// 循环获取数据
					Map<Date, String> _map = map.get(col) == null ? null : ObjUtils.copyTo(map.get(col), Map.class);// 接口获取数据
					Map<Date, String> hmap = dataList.get(col);// 已存获取数据
					if (_map != null && _map.size() > 0) {
						if (hmap != null) {
							_map.putAll(hmap);// 覆盖已保存数据
						}
						tmap.put(col, _map);
					} else if (hmap != null && hmap.size() > 0) {
						tmap.put(col, hmap);
					}
				}
				for (String tagId : dataList.keySet()) {
					if (!tmap.containsKey(tagId)) {
						tmap.put(tagId, dataList.get(tagId));
					}
				}
				map = tmap;
			} else if ("bound".equals(this.timeConf.getShowMode())) {// 时间段
				// 已保存数据dataList为基础，进行交叉处理
				for (String col : colList) {// 循环获取数据
					// String tagCode =
					// tagIdObjMap.get(col)==null?null:tagIdObjMap.get(col).getDatasource();//根据仪表位号获取标识
					Map<Date, String> _map = map.get(col);// 接口获取数据
					Map<Date, String> hmap = dataList.get(col);// 已存获取数据
					if (_map != null && _map.size() > 0) {
						// for(Date dt : _map.keySet()) {//循环接口获取的仪表时间数据
						// if(hmap!=null && hmap.containsKey(dt)) {
						// String val = hmap.get(dt);
						// if(StringUtils.isNotEmpty(val))
						// {//保存数据有值，覆盖获取的数据//保存数据有值，不替换获取的数据，后期加参数进行判断取数变化
						// _map.put(dt, hmap.get(dt));
						// }
						// }
						// }
						// if("bound".equals(this.timeConf.getShowMode())) {//时间段
						// if(hmap!=null) {
						//
						// }
						// }else {
						if (hmap != null) {
							_map.putAll(hmap);// 覆盖已保存数据
						}
						// }
					} else if (hmap != null && hmap.size() > 0) {
						map.put(col, hmap);
					}
				}
				for (String tagId : dataList.keySet()) {
					if (!map.containsKey(tagId)) {
						map.put(tagId, dataList.get(tagId));
					}
				}
			} else {// pwl仪表数据
				Map<String, Map<Date, String>> tmap = new HashMap<String, Map<Date, String>>();
				// 已保存数据dataList为基础，进行交叉处理
				for (String col : colList) {// 循环获取数据
					String tagCode = tagIdObjMap.get(col) == null ? null : tagIdObjMap.get(col).getDatasource();// 根据仪表位号获取标识
					Map<Date, String> _map = map.get(tagCode) == null ? null
							: ObjUtils.copyTo(map.get(tagCode), Map.class);// 接口获取数据
					Map<Date, String> hmap = dataList.get(col);// 已存获取数据
					if (_map != null && _map.size() > 0) {
						// 如果初始化，则不覆盖已保存数据 05.07修改
						if (hmap != null && hmap.size() > 0 && !isInit) {
							_map.putAll(hmap);// 覆盖已保存数据
						}
						// 如果初始化，则不覆盖已保存数据 05.07修改
						tmap.put(col, _map);
					} else if (hmap != null && hmap.size() > 0) {
						tmap.put(col, hmap);
					}
				}
				for (String tagId : dataList.keySet()) {
					if (!tmap.containsKey(tagId)) {
						tmap.put(tagId, dataList.get(tagId));
					}
				}
				map = tmap;
			}
		} else {
			if ("mobile".equals(this.timeConf.getShowMode())) {// 移动端数据外部获取模式，获取移动端保存的数据
			} else if ("bound".equals(this.timeConf.getShowMode())) {// 时间段
			} else {
				Map<String, Map<Date, String>> tmap = new HashMap<String, Map<Date, String>>();
				// 已保存数据dataList为基础，进行交叉处理
				for (String col : colList) {// 循环获取数据
					String tagCode = tagIdObjMap.get(col) == null ? null : tagIdObjMap.get(col).getDatasource();// 根据仪表位号获取标识
					Map<Date, String> _map = map.get(tagCode) == null ? null
							: ObjUtils.copyTo(map.get(tagCode), Map.class);// 接口获取数据
					if (_map != null && _map.size() > 0) {
						tmap.put(col, _map);
					}
				}
				map = tmap;
			}
		}
		// oldDataMap = null;
		// 在方法结束时清空确认时间点变量，确保下次调用时重新获取
		this.confirmTimeList = null;
		return map;
	}

	private boolean faUseTag(List<Map<String, Object>> faData, Date dt) {// 判断方案在各时间点是否使用
		Boolean flag = true;

		if (StringUtils.isNotEmpty(faData)) {
			String timeStr = DateTimeUtils.formatDateTime(dt);
			for (Map<String, Object> map : faData) {
				String srq = String.valueOf(map.get("srq"));
				String erq = String.valueOf(map.get("erq"));
				if (timeStr.compareTo(srq) >= 0 && timeStr.compareTo(erq) < 0) {// 根据时间范围判断是否应用方案
					String usedstr = String.valueOf(map.get("used"));
					if (Coms.judgeDouble(usedstr) && Double.parseDouble(usedstr) == 0) {
						flag = false;
						break;
					}
				}
			}
		}
		return flag;
	}

	/**
	 * @category 从外部数据获取
	 *           // * @param map
	 */
	private void getMobileData(Map<String, Map<Date, String>> rmap) {
		StringBuffer whereUnit = new StringBuffer();
		String uid = this.confCode;
		if (uid.indexOf(",") == -1) {
			whereUnit.append("='");
			whereUnit.append(this.confCode);
			whereUnit.append("'");
		} else {
			whereUnit.append("in(");
			List<String> ulist = Coms.StrToList(this.confCode, ",");
			for (String u : ulist) {
				if (u.length() > 3) {
					whereUnit.append(",");
				}
				whereUnit.append("'");
				whereUnit.append(u);
				whereUnit.append("'");
			}
			whereUnit.append(")");
		}
		List<Object> param = new ArrayList<Object>();
		Date sbsjD = StringUtils.isEmpty(this.timeBoundBc1) ? null : DateTimeUtils.parseDateTime(this.timeBoundBc1);
		// param.add(this.shiftCode);
		// param.add(sbsjD);

		String sql = "select COLLECT_POINT_ID, IPT_ID, COLLECT_POINT_TEXT, COLLECT_POINT_VAL,INPUT_COMP_TYPE,INPUT_OPTIONS,INPUT_TIME,JOB_INPUT_TIME "
				+
				"from ACCTOBJ_INPUTMX where TMUSED=1 and exists (select ID from ACCTOBJ_INPUT where TMUSED=1 and ACCTOBJ_ID "
				+ whereUnit.toString() + " and bcdm=? and sbsj=? and team_id = ? and ID=ACCTOBJ_INPUTMX.IPT_ID)";
		if (sbsjD != null) {// 班次模式查询
			param.add(this.shiftCode);
			param.add(sbsjD);
			param.add(getEffectiveOrgCode()); // 优先使用传入的orgcode，如果为空使用用户机构
		} else {
			if (StringUtils.isNotEmpty(timeBoundDay)) {
				param.add(DateTimeUtils.parseDate(timeBoundDay));
				param.add(DateTimeUtils.doSecond(DateTimeUtils.doDate(DateTimeUtils.parseDate(timeBoundDay), 1), -1));
			} else {
				Date nd = DateTimeUtils.getNowDate();
				param.add(nd);
				param.add(nd);
			}
			param.add(getEffectiveOrgCode()); // 优先使用传入的orgcode，如果为空使用用户机构
			sql = "select COLLECT_POINT_ID, IPT_ID, COLLECT_POINT_TEXT, COLLECT_POINT_VAL,INPUT_COMP_TYPE,INPUT_OPTIONS,INPUT_TIME,JOB_INPUT_TIME "
					+
					"from ACCTOBJ_INPUTMX where TMUSED=1 and exists (select ID from ACCTOBJ_INPUT where TMUSED=1 and BA_ID ='"
					+ sendUnitCode + "' and INPUT_TIME between ? and ? and team_id = ? and ID=ACCTOBJ_INPUTMX.IPT_ID)";
		}

		List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
		this.jobInputTimeList =new HashMap<>();
		if (StringUtils.isNotEmpty(list)) {
			colInfoMap = new HashMap<String, Map<String, String>>();
			// txtMap = new HashMap<String, String>();
			for (Map<String, Object> map : list) {
				String tagId = getMapString(map, "COLLECT_POINT_ID");
				String mainId = getMapString(map, "IPT_ID");
				String showTxt = getMapString(map, "COLLECT_POINT_TEXT");
				String val = getMapString(map, "COLLECT_POINT_VAL");
				String comType = getMapString(map, "INPUT_COMP_TYPE");
				String comOptions = getMapString(map, "INPUT_OPTIONS");
				Date inputTime = getMapDate(map, "INPUT_TIME");
				Date jobInputTime = getMapDate(map, "JOB_INPUT_TIME");
				String it = DateTimeUtils.formatDateTime(inputTime);
				String jit = DateTimeUtils.formatDateTime(jobInputTime);
				Date bjdate = DateTimeUtils.parseDateTime(it);
//				Date jitDate = DateTimeUtils.parseDateTime(jit);
				String dt = null;
				if (infoTimeIdMap != null && infoTimeIdMap.containsKey(bjdate)) {
					dt = DateTimeUtils.formatDateTime(inputTime);
				}
				if (jobInputTimeIdMap != null && jobInputTimeIdMap.containsKey(bjdate)) {
					// 赋值
					if (bjdate != null && StringUtils.isNotEmpty(jit)) {
						if (this.jobInputTimeList.containsKey(tagId)) {
							this.jobInputTimeList.get(tagId).put(bjdate, jit);
						} else {
							Map<Date, String> _map = new LinkedHashMap<Date, String>();
							_map.put(bjdate, jit);
							this.jobInputTimeList.put(tagId, _map);
						}
					}
				}

				txtMap.put(tagId + "_" + dt, showTxt);

				// 赋值
				if (dt != null) {
					Date d = DateTimeUtils.parseDateTime(dt);
					if (rmap.containsKey(tagId)) {
						rmap.get(tagId).put(d, val);
					} else {
						Map<Date, String> _map = new LinkedHashMap<Date, String>();
						_map.put(d, val);
						rmap.put(tagId, _map);
					}
				}

				// 列相关信息
				if (!colInfoMap.containsKey(tagId)) {
					Map<String, String> tmap = new HashMap<String, String>();
					tmap.put("tagId", tagId);
					tmap.put("mainId", mainId);
					tmap.put("comType", comType);
					tmap.put("comOptions", comOptions);
					tmap.put("showTxt", showTxt);
					tmap.put("val", val);

					colInfoMap.put(tagId, tmap);
				}
			}
		}
	}

	// 插入无需确认和标志
	private void insertNoConfirm(Date dt, Date nd, JSONArray insertData, Map<Date, String> hrmap) {
		if (hrmap == null || hrmap.get(dt) == null) {// 没有存过数据，进行无确认处理
			if (this.noConfirmList == null) {
				this.noConfirmList = new ArrayList<Date>();
			}
			this.noConfirmList.add(dt);
			// SysUser user = SysUserHolder.getCurrentUser();
			// JSONObject aobj = new JSONObject();
			// aobj.put("_id", TMUID.getUID());
			// aobj.put("dsAlias", this.getDSAlias());
			// aobj.put("formId", "");
			// aobj.put("dataId", dataId);
			// aobj.put("col", "rowConfirm");//列名
			// aobj.put("tag", "");
			// aobj.put("dateType", "rowConfirm");
			// aobj.put("sj", DateTimeUtils.formatDateTime(dt));
			// aobj.put("sjlong", dt.getTime());
			// aobj.put("val", noConfirm);
			// aobj.put("valstr", noConfirm);
			// aobj.put("creTime", nd);
			// aobj.put("creUserId", user.getId());
			// aobj.put("creUserName", user.getRealName());
			// aobj.put("updTime", null);
			// aobj.put("updUserId", null);
			// aobj.put("updUserName", null);
			// aobj.put("additionVal", null);//附加值
			// aobj.put("edit", false);
			// aobj.put("confirmDiff", 0);//确认差（秒）
			// aobj.put("rowConfirmBound", 0);//确认范围（分钟）
			// aobj.put("rowConfirmOver", false);//确认超时
			// aobj.put("tmused", 1);
			// insertData.add(aobj);
		}
	}

	// 获取小数位数
	private Integer getTagDegit(Integer decimalDegit, Integer meterBit) {
		Integer rv = 2;
		if (decimalDegit == null || decimalDegit < 0) {
			rv = meterBit == null ? 2 : meterBit;
		} else {
			rv = decimalDegit;
		}
		return rv;
	}

	/**
	 * @category 取最大公约数
	 * @param step 数值1
	 * @param imin 数值2
	 * @return
	 */
	private int commonDivisor(int step, int imin) {
		if (step == imin) {
			return step;
		}
		int min = step < imin ? step : imin;
		int max = step > imin ? step : imin;
		int comDiv = max;
		int jg = 1;

		for (int i = min, il = 0; i > il && comDiv != 0; i--) {
			comDiv = max % min;
			if (comDiv == 0) {
				jg = min;
				break;
			} else {
				max = min;
				min = comDiv;
			}
		}
		// for(int i=min;i>0;i--) {
		// if(max%i==0&&min%i==0) {
		// comDiv=i;
		// break;
		// }
		// }
		return jg;
	}

	/* 备份代码 */
	// 时间配置
	// //整理时间信息
	// List<Date> timeList = new ArrayList<Date>();
	// if(this.timeConf!=null) {
	// Date startDate = null;
	// String startRq = null;
	// String startBind = this.timeConf.getStartBingDay();
	// String startFix = this.timeConf.getStartFixed();
	// Boolean haveStart = new Integer(1).equals(this.timeConf.getStartRound());
	// int istartfix = 0;
	//
	// Date endDate = null;
	// String endRq = null;
	// String endBind = this.timeConf.getStartBingDay();
	// String endFix = this.timeConf.getStartFixed();
	// Boolean haveEnd = new Integer(1).equals(this.timeConf.getEndRound());
	// int iendfix = 0;
	// if(Coms.judgeLong(startFix)) {
	// istartfix = Integer.parseInt(startFix);
	// }
	// if(Coms.judgeLong(endFix)) {
	// iendfix = Integer.parseInt(endFix);
	// }
	// //开始时间获取
	// if(StringUtils.isNotEmpty(startBind)) {
	// if (StringUtils.isNotEmpty(this.tInParas)) {
	// for (TInPara temp : this.tInParas) {
	// if(temp.getParaAlias().equals(startBind)) {
	// startRq = temp.getValue()!=null?String.valueOf(temp.getValue()):null;
	// break;
	// }
	// }
	// if(StringUtils.isNotEmpty(startRq)) {
	// Date rq = DateTimeUtils.parseDate(startRq);
	// if(rq == null) {
	// startRq = null;
	// }else {
	// startRq = DateTimeUtils.formatDate(rq, DateTimeUtils.DateFormat_YMD);
	// }
	// }
	//
	// }
	// }
	// if(StringUtils.isEmpty(startRq)) {
	// if(istartfix != 0) {
	// startRq = DateTimeUtils.getNowDateStr().substring(0,10) + " " +
	// this.timeConf.getStartBingTime()+":00";
	// startDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(startRq),
	// istartfix);
	// }else {
	// startRq = DateTimeUtils.getNowDateStr().substring(0,10) +
	// this.timeConf.getStartBingTime()+":00";
	// startDate = DateTimeUtils.parseDateTime(startRq);
	// }
	// }else {
	// if(istartfix != 0) {
	// startDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(startRq + " " +
	// this.timeConf.getStartBingTime()+":00"), istartfix);
	// }else {
	// startDate = DateTimeUtils.parseDateTime(startRq + " " +
	// this.timeConf.getStartBingTime()+":00");
	// }
	// }
	// //截止时间获取
	// if(StringUtils.isNotEmpty(endBind)) {
	// if (StringUtils.isNotEmpty(this.tInParas)) {
	// for (TInPara temp : this.tInParas) {
	// if(temp.getParaAlias().equals(endBind)) {
	// endRq = temp.getValue()!=null?String.valueOf(temp.getValue()):null;
	// break;
	// }
	// }
	// if(StringUtils.isNotEmpty(endRq)) {
	// Date rq = DateTimeUtils.parseDate(endRq);
	// if(rq == null) {
	// endRq = null;
	// }else {
	// endRq = DateTimeUtils.formatDate(rq, DateTimeUtils.DateFormat_YMD);
	// }
	// }
	//
	// }
	// }
	// if(StringUtils.isEmpty(endRq)) {
	// if(iendfix != 0) {
	// endRq = DateTimeUtils.getNowDateStr().substring(0,10) + " " +
	// this.timeConf.getStartBingTime()+":00";
	// endDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(endRq), iendfix);
	// }else {
	// endRq = DateTimeUtils.getNowDateStr().substring(0,10) +
	// this.timeConf.getStartBingTime()+":00";
	// endDate = DateTimeUtils.parseDateTime(endRq);
	// }
	// }else {
	// if(iendfix != 0) {
	// endDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(endRq + " " +
	// this.timeConf.getStartBingTime()+":00"), iendfix);
	// }else {
	// endDate = DateTimeUtils.parseDateTime(endRq + " " +
	// this.timeConf.getStartBingTime()+":00");
	// }
	// }
	//
	// if(DateTimeUtils.bjDate(startDate, endDate) == 1) {//如果时间不对，不获取数据
	//// return map;
	// }else {
	//
	// Date tempDate = startDate;
	// String timeStep = this.timeConf.getTimeStep();
	// Integer step = 60;
	// if(Coms.judgeLong(timeStep)) {
	// step = Integer.parseInt(timeStep);
	// }
	// if(haveStart) {
	// timeList.add(startDate);
	// tempDate = DateTimeUtils.doMinute(tempDate, step);
	// }
	//
	// for (int i = 0,il=50; i < il && DateTimeUtils.bjDate(endDate, tempDate) > -1;
	// i++) {//时间对比，增加循环数，避免死循环
	// timeList.add((Date)tempDate.clone());
	// tempDate = DateTimeUtils.doMinute(tempDate, step);
	// }
	//
	// if(DateTimeUtils.bjDate(tempDate, endDate) == 1) {
	// if(haveEnd) {
	// if(DateTimeUtils.bjDate(endDate, startDate) == 1) {
	// timeList.add(endDate);
	//
	// if(timeList.size() > 1) {
	// //需要取最小公约数
	// long diffMin = Math.abs(DateTimeUtils.diffDate(endDate, tempDate,
	// DateTimeUtils.MINITE));
	// int imin = Integer.parseInt(String.valueOf(diffMin));
	// interval = commonDivisor(60, imin);//计算最大公约数
	// stime = DateTimeUtils.formatDateTime(timeList.get(0));
	// etime = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
	// }else {
	//// if(timeList.size() > 0) {
	//// stime = DateTimeUtils.formatDateTime(timeList.get(0));
	//// etime = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
	//// interval = step;
	//// }
	// }
	//
	// }else {
	//// if(timeList.size() > 0) {
	//// stime = DateTimeUtils.formatDateTime(timeList.get(0));
	//// etime = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
	//// interval = step;
	//// }
	//
	// }
	// }else {
	// //间隔固定
	//// if(timeList.size() > 0) {
	//// stime = DateTimeUtils.formatDateTime(timeList.get(0));
	//// etime = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
	//// interval = step;
	//// }
	// }
	// }else if(DateTimeUtils.bjDate(tempDate, endDate) == 0) {
	// //间隔固定
	// if(haveEnd) {
	// timeList.add(endDate);
	// }
	//// if(timeList.size() > 0) {
	//// stime = DateTimeUtils.formatDateTime(timeList.get(0));
	//// etime = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
	//// interval = step;
	//// }
	// }else {
	//// if(timeList.size() > 0) {
	//// stime = DateTimeUtils.formatDateTime(timeList.get(0));
	//// etime = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
	//// interval = step;
	//// }
	// }
	//
	// if(stime == null && timeList.size() > 0) {
	// stime = DateTimeUtils.formatDateTime(timeList.get(0));
	// etime = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
	// interval = step;
	// }
	//
	// if(StringUtils.isNotEmpty(timeList)) {
	// sjmap = new LinkedHashMap<Date, String>();
	// for (Date date : timeList) {
	// sjmap.put(date, DateTimeUtils.format(date, this.timeConf.getTimeFormat()));
	// }
	// }
	// }
	// }

	/**
	 * 获取初始化数据标识映射
	 * @return 初始化数据标识映射，key为时间点，value为是否是初始化数据的标识
	 */
	public Map<Date, String> getIsInitIdMap() {
		return isInitIdMap;
	}

	/**
	 * 设置初始化数据标识映射
	 * @param isInitIdMap 初始化数据标识映射，key为时间点，value为是否是初始化数据的标识
	 */
	public void setIsInitIdMap(Map<Date, String> isInitIdMap) {
		this.isInitIdMap = isInitIdMap;
	}

	/**
	 * 获取手工录入采集点ID映射
	 * @return 手工录入采集点ID映射，key为采集点ID，value为是否手工录入标识（1代表手工录入，0或null代表不是）
	 */
	public Map<String, String> getIsWriteInputIdMap() {
		return isWriteInputIdMap;
	}

	/**
	 * 设置手工录入采集点ID映射
	 * @param isWriteInputIdMap 手工录入采集点ID映射，key为采集点ID，value为是否手工录入标识（1代表手工录入，0或null代表不是）
	 */
	public void setIsWriteInputIdMap(Map<String, String> isWriteInputIdMap) {
		this.isWriteInputIdMap = isWriteInputIdMap;
	}

	public Map<String, String> getMultiSelectDisplayModeIdMap() {
		return multiSelectDisplayModeIdMap;
	}

	public void setMultiSelectDisplayModeIdMap(Map<String, String> multiSelectDisplayModeIdMap) {
		this.multiSelectDisplayModeIdMap = multiSelectDisplayModeIdMap;
	}

	public Map<String, String> getCopyAddDefaultModeIdMap() {
		return copyAddDefaultModeIdMap;
	}

	public void setCopyAddDefaultModeIdMap(Map<String, String> copyAddDefaultModeIdMap) {
		this.copyAddDefaultModeIdMap = copyAddDefaultModeIdMap;
	}

	private Integer getMapInt(Map<String, Object> map, String key) {
		Object obj = map.get(key);
		if (obj == null) {
			return null;
		} else if (Coms.judgeLong(obj)) {
			String so = String.valueOf(obj);
			return Integer.parseInt(so);
		}

		return null;
	}

	private String getMapString(Map<String, Object> map, String key) {
		Object obj = map.get(key);
		if (obj == null) {
			return null;
		} else {
			return String.valueOf(obj);
		}
	}

	private Date getMapDate(Map<String, Object> map, String key) {
		Object obj = map.get(key);
		if (obj == null) {
			return null;
		} else if (obj instanceof java.util.Date) {
			return (Date) obj;
		}

		return null;
	}

	/**
	 * 单时间点循环取实时值方法
	 * 为了不改变原有逻辑，将多个时间点的数据合并到统一的rdbtaglist中
	 *
	 * @param getTags 仪表标签列表
	 * @param dtlist 时间点列表
	 * @return 合并后的Tag数据列表
	 */
	private Map<String, List<Tag>> collectRealTimeDataByTimePoints(List<String> getTags, List<Date> dtlist) {
		Map<String, List<Tag>> allRdbtaglistMap = new LinkedHashMap<>();

		if (StringUtils.isEmpty(getTags) || StringUtils.isEmpty(dtlist)) {
			log.error("台账仪表接口参数错误：核算对象：{}，仪表：{}，时间：{}", this.confCode, getTags, dtlist);
			return allRdbtaglistMap;
		}

		for (Date date : dtlist) {
			try {
				String timeStr = DateTimeUtils.formatDateTime(date);

//				log.info("台账单时间点获取实时数据：时间点={}, 仪表数量={}", timeStr, getTags.size());

				// 调用接口获取单个时间点的数据
				// 实时接口如果当前时间没有值，会自动向前一秒时间找值
//				List<Tag> singleTimeTagList = rtdbSrv.queryRtdbTagData(getTags, timeStr, timeStr, 0);
				// 改用新的接口
				List<Tag> singleTimeTagList = rtdbSrv.queryLastTagDatas(getTags, timeStr);

				// 将获取到的数据放入map中，key为请求的时间点
				allRdbtaglistMap.put(timeStr, singleTimeTagList);

				/*if (StringUtils.isNotEmpty(singleTimeTagList)) {
					for (Tag tag : singleTimeTagList) {
						if (tag == null || StringUtils.isEmpty(tag.getTagCode())) {
							continue;
						}

						String tagCode = tag.getTagCode().toUpperCase();

						// 如果tagMap中已存在该tagCode，则合并数据
						if (tagMap.containsKey(tagCode)) {
							Tag existingTag = tagMap.get(tagCode);
							List<TagData> existingDatas = existingTag.getDatas();
							List<TagData> newDatas = tag.getDatas();

							if (StringUtils.isNotEmpty(newDatas)) {
								if (existingDatas == null) {
									existingDatas = new ArrayList<>();
									existingTag.setDatas(existingDatas);
								}
								existingDatas.addAll(newDatas);
							}
						} else {
							// 新的tagCode，直接添加到map中
							tagMap.put(tagCode, tag);
						}
					}
				}*/
			} catch (Exception e) {
				log.error("台账单时间点获取仪表数据失败：时间点={}, 错误信息={}",
						DateTimeUtils.formatDateTime(date), e.getMessage());
			}
		}

		// 将合并后的数据转换为List
		//allRdbtaglist.addAll(tagMap.values());

		//log.info("台账单时间点循环取值完成：总时间点数={}, 总仪表数={}, 合并后TagMap={}",
		//		dtlist.size(), getTags.size(), allRdbtaglistMap.toString());

		return allRdbtaglistMap;
	}
}
