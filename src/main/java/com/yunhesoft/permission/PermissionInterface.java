package com.yunhesoft.permission;


import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 此接口用于实现统一处理系统框架中权限判断
 * <AUTHOR>
 * @date 2024/12/18
 */
public interface PermissionInterface <T> {
    /**
     * 数据是否符合条件逻辑
     * <AUTHOR>
     * @date 2024/12/18
     * @params
     * @return
     *
    */
    Boolean isLicenseed(T source);
    /**
     * 从数据列表中范围符合条件的数据列表
     * <AUTHOR>
     * @date 2024/12/18
     * @params
     * @return
     *
    */
    default List<T> returnLicensedData(List<T> sourceList){
        return Optional.ofNullable(sourceList).orElse(new ArrayList<>()).stream()
                .filter(this::isLicenseed)
                .collect(Collectors.toList());
    }
}
