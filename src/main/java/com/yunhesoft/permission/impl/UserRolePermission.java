package com.yunhesoft.permission.impl;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.permission.PermissionInterface;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.role.entity.po.SysRole;
import com.yunhesoft.system.role.service.ISysRoleService;

import java.util.*;

public class UserRolePermission implements PermissionInterface<SysRole> {

    private static ISysRoleService sysRoleService;

    /**
     * 数据是否符合条件逻辑
     *
     * @param source
     * @return
     * <AUTHOR>
     * @date 2024/12/18
     * @params
     */
    @Override
    public Boolean isLicenseed(SysRole source) {
        if(source.getRoleLevel()==null || SysUserUtil.isAdmin()){
            //角色数据本身没有级别控制 或者 为管理员
            //所有人可见
            return true;
        }
        if(sysRoleService==null){
            sysRoleService = SpringUtils.getBean(ISysRoleService.class);
        }
        List<String> roles = SysUserHolder.getCurrentUser().getRoles();
        List<SysRole> roleByIds = sysRoleService.getRoleByIds(roles);
        //查找本人具有权限级别最高的
        if(StringUtils.isNotEmpty(roleByIds)){
            Optional<SysRole> min = roleByIds.stream()
                    .filter(item -> item.getRoleLevel() != null)
                    .min(Comparator.comparing(SysRole::getRoleLevel));
            if(min.isPresent()){
                SysRole role = min.get();
                if(source.getRoleLevel()>=role.getRoleLevel()){
                    //当前角色级别小于人员具有的最大权限
                    return true;
                }
            }
        }
        return false;
    }

}
