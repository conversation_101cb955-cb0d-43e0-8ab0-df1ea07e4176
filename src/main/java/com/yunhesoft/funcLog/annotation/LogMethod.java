package com.yunhesoft.funcLog.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Description: 此注解用于标志方法是否受到日志监控
 * @date 2024/10/21
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LogMethod {
    /*模块名称*/
    String moudle() default "";
    /*功能名称*/
    String func() default "";
    /*处理器*/
    String handler() default "commonLog";
    /*行为类型  按钮 菜单*/
    String actionType() default "网络资源";
}
