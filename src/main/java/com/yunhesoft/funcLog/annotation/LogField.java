package com.yunhesoft.funcLog.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 此注解用于声明日志中字段意义
 *
 * <AUTHOR>
 * @date 2024/10/21
 * @params
 * @return
 */

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LogField {
    String value() default "";
}
