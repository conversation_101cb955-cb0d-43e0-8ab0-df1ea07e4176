package com.yunhesoft.funcLog.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties("log")
public class LogConfiguration {
    private String logService;

    public String getLogService() {
        return logService;
    }

    public void setLogService(String logService) {
        this.logService = logService;
    }
}
