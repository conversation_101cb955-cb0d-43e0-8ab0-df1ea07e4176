package com.yunhesoft.funcLog.entity;

import com.yunhesoft.core.common.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Data
@Table(name = "COMMON_LOG_DATA")
@Document(collection = "commonLogData")
public class CommonLogData extends BaseEntity {
    //操作时间
    @Column(name = "LOG_TIME", length = 20)
    private String logTime;
    //操作人
    @Column(name = "OPERATION_PERSON", length = 50)
    private String operationPerson;
    //操作人编码
    @Column(name = "OPERATION_PERSON_CODE", length = 50)
    private String operationPersonCode;
    //项目名称
    @Column(name = "PROJECT", length = 50)
    private String project;
    //项目编码
    @Column(name = "PROJECT_CODE", length = 50)
    private String projectCode;
    //模块名称
    @Column(name = "MOUDLE", length = 50)
    private String moudle;
    //功能名称
    @Column(name = "FUNC", length = 50)
    private String func;
    //功能编码默认是方法名
    @Column(name = "FUNC_CODE", length = 50)
    private String funcCode;
    //访问url
    @Column(name = "ACCESS_URL", length = 500)
    private String accessUrl;

    //接口参数
    @Lob
    @Column(name = "ACCESS_PARAM")
    private String accessParam;

    //接口返回参数
    @Lob
    @Column(name = "RETURN_VALUE")
    private String returnValue;

    @Column(name = "CLASS_PATH", length = 500)
    private String classPath;

    @Column(name = "COST_TIME")
    private long costTime;
    @Column(name = "ACTION_TYPE",length = 10)
    private String actionType;
    @Column(name = "ORG_CODE",length = 50)
    private String orgCode;
    @Column(name = "ORG_NAME",length = 100)
    private String orgName;

    @Column(name = "EXPIRE_AT",length = 100)
    private Date expireAt;
}
