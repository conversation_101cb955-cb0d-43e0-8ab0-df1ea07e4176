package com.yunhesoft.funcLog.timeTask;

import com.yunhesoft.funcLog.service.ILogGenerate;
import com.yunhesoft.system.kernel.service.EntityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@EnableScheduling
public class AutoClearCommonLogData{
    @Autowired
    private EntityService dao;

    @Value("${log.log-service:dataBase}")
    private String storeContain;

    @Autowired
    private ApplicationContext applicationContext;

    private static int EXPIRE_DAY = 180; // 日志保存多少天

//    @Scheduled(cron = "0 0 0 * * *")
    @Scheduled(cron = "0 * * * * *")
    public void autoClearLogData(){
        //获取 容器里 所有的日志实现类
        applicationContext.getBeansOfType(ILogGenerate.class).forEach((k,v)->{
            v.autoClearLog(EXPIRE_DAY);
        });
    }
}
