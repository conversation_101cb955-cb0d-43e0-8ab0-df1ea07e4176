package com.yunhesoft.funcLog.service;

import com.yunhesoft.funcLog.entity.CommonLogData;
import com.yunhesoft.funcLog.entity.QueryLogDataDto;
import com.yunhesoft.system.kernel.service.model.Pagination;

import java.util.List;

/**
 * 此接口定义数据存储方法，可以根据此接口实现多种日志保存方式通过实现进行扩展
 *
 * <AUTHOR>
 * @return
 * @params
 */

public interface ILogStoreService {
    /**
     * 保存日志数据
     * <AUTHOR>
     * @date 2024/12/30
     * @params
     * @return
     *
    */
    void saveLog(List<?> data);

    /**
     * 获取保存的日志数据
     *
     * @param queryDto
     * @param page
     * @return
     * <AUTHOR>
     * @date 2024/12/30
     * @params
     */
    List<CommonLogData> getLogInfoList(QueryLogDataDto queryDto, Pagination<?> page);

    /**
     * 自动清除
     * <AUTHOR>
     * @date 2025/1/3
     * @params
     * @return
     *
    */
    default void autoClearLog(int liveDays){
    }
}
