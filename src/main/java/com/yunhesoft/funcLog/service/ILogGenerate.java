package com.yunhesoft.funcLog.service;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.funcLog.config.LogConfiguration;
import com.yunhesoft.funcLog.entity.CommonLogData;
import com.yunhesoft.funcLog.entity.FuncParam;
import com.yunhesoft.funcLog.entity.QueryLogDataDto;
import com.yunhesoft.system.kernel.service.model.Pagination;
import org.springframework.scheduling.annotation.Async;

import java.util.ArrayList;
import java.util.List;

public interface ILogGenerate<T> {
    /**
     * 方法参数数据缓存
     *
     * <AUTHOR>
     * @date 2024/10/21
     * @params
     * @return
     */
    List<CommonLogData> dataCache = new ArrayList<>();

    /**
     * 方法执行前日志处理
     *
     * @return
     * <AUTHOR>
     * @date 2024/10/21
     * @params
     */
    void recordLogFuncBefore(CommonLogData funcParam);

    /**
     * 方法执行后日志处理
     *
     * @return
     * <AUTHOR>
     * @date 2024/10/21
     * @params
     */
    void recordLogFuncAfter(long time, FuncParam funcParam, Object funcResult);

    /**
     * 获取缓存的日志数据
     *
     * @return
     * <AUTHOR>
     * @date 2024/10/21
     * @params
     */

    default List<CommonLogData> getLogData() {
        return dataCache;
    }

    default void addLogData(CommonLogData commonLogData) {
        dataCache.add(commonLogData);
    }

    default ILogStoreService getLogStoreService() {
        //获取配置
        LogConfiguration bean = SpringUtils.getBean(LogConfiguration.class);
        if (bean == null || StringUtils.isEmpty(bean.getLogService())) {
            bean.setLogService("dataBase");
        }
        //根据配置的名称获取日志存储服务
        return SpringUtils.getBean(bean.getLogService());
    }

    @Async
    void saveLog(List<T> data);

    /**
     * 日志查询
     * <AUTHOR>
     * @date 2024/12/30
     * @params
     * @return
     *
    */
    default List<CommonLogData> getLogInfoList(QueryLogDataDto queryDto, Pagination<?> page){
        return null;
    }
    /**
     * 日志自动清除
     * <AUTHOR>
     * @date 2024/12/30
     * @params
     * @return
     *
     */
    default void autoClearLog(int liveDays){
    }

}
