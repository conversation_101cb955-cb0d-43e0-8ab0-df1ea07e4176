package com.yunhesoft.funcLog.service.impl;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.funcLog.entity.CommonLogData;
import com.yunhesoft.funcLog.entity.QueryLogDataDto;
import com.yunhesoft.funcLog.service.ILogStoreService;
import com.yunhesoft.system.kernel.service.model.Pagination;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("console")
@Log4j2
public class LogConsolePrintlnImpl implements ILogStoreService {
    @Override
    public void saveLog(List<?> data) {
        if (StringUtils.isNotEmpty(data)) {
            for (Object datum : data) {
                if (datum instanceof CommonLogData) {
                    CommonLogData commonLogData = (CommonLogData) datum;
                    log.info("接口执行信息：接口路径：{};接口耗时：{}毫秒;是否产生异常：{}", commonLogData.getClassPath(), commonLogData.getCostTime(), commonLogData.getReturnValue().contains("异常"));
                } else {
                    log.info("接口执行信息：{}", datum);
                }
            }
        }
    }

    @Override
    public List<CommonLogData> getLogInfoList(QueryLogDataDto queryDto, Pagination<?> page) {
        //控制台输出模式本身没有存储，不能进行查询操作
        return null;
    }
}
