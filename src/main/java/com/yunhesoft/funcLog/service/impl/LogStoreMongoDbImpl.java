package com.yunhesoft.funcLog.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.funcLog.entity.CommonLogData;
import com.yunhesoft.funcLog.entity.QueryLogDataDto;
import com.yunhesoft.funcLog.service.ILogStoreService;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.utils.mongodb.model.MongoWheres;
import com.yunhesoft.system.kernel.utils.mongodb.service.MongoDBService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service("mongodb")
public class LogStoreMongoDbImpl implements ILogStoreService {
    @Autowired
    private MongoDBService mongoDBService;
    @Autowired(required = false)
    private MongoTemplate mongoTemplate;
    private static int EXPIRE_DAY = 180; // 日志保存多少天

    @PostConstruct
    public void setIndex(){
        if(mongoTemplate==null){
            return;
        }
        //设置索引
        IndexOperations indexOps = mongoTemplate.indexOps("commonLogData");
        List<IndexInfo> indexInfo = indexOps.getIndexInfo();
        if(StringUtils.isNotEmpty(indexInfo)){
            List<IndexInfo> collect = indexInfo.stream().filter(item -> item.getName().contains("expireAt")).collect(Collectors.toList());
            if(StringUtils.isEmpty(collect)){
                //没有索引重建索引
                Index expirationIndex = new Index("expireAt", Sort.Direction.ASC).expire(EXPIRE_DAY, TimeUnit.DAYS);
                indexOps.ensureIndex(expirationIndex);
            }
        }
    }
    @Override
    public void saveLog(List<?> data) {
//        mongoTemplate.save(data);
        mongoDBService.save(data);
    }

    @Override
    public List<CommonLogData> getLogInfoList(QueryLogDataDto queryDto, Pagination<?> page) {
        MongoWheres mongoWheres = MongoWheres.create();
        if (StringUtils.isNotEmpty(queryDto.getStartDate())) {
            mongoWheres.ge("logTime", queryDto.getStartDate());
        }
        if (StringUtils.isNotEmpty(queryDto.getEndDate())) {
            mongoWheres.le("logTime", queryDto.getEndDate());
        }
        if (StringUtils.isNotEmpty(queryDto.getMoudle())) {
            mongoWheres.like("moudle", queryDto.getMoudle());
        }
        if (StringUtils.isNotEmpty(queryDto.getFunc())) {
            mongoWheres.like("func", queryDto.getFunc());
        }
        if (StringUtils.isNotEmpty(queryDto.getOrgName())) {
            mongoWheres.like("orgName", queryDto.getOrgName());
        }
        if (StringUtils.isNotEmpty(queryDto.getOperationPerson())) {
            mongoWheres.like("operationPerson", queryDto.getOperationPerson());
        }
        if (StringUtils.isNotEmpty(queryDto.getApiPath())) {
            mongoWheres.like("accessUrl", queryDto.getApiPath());
        }
        Res<?> commonLogDataStr = mongoDBService.queryData("commonLogData",mongoWheres);
        if(commonLogDataStr==null || commonLogDataStr.getResult()==null){
            return null;
        }
        page.setTotal((int) commonLogDataStr.getTotal());
        JSONArray jsonArray = JSONArray.parseArray(JSONArray.toJSONString(commonLogDataStr.getResult()));
        List<CommonLogData> result = jsonArray.toJavaList(CommonLogData.class);
        //排序
        result = result.stream().sorted(Comparator.comparing(CommonLogData::getLogTime).reversed())
                .collect(Collectors.toList());
        //分页
        if (page!=null) {
            page.setTotal(result.size());
            result = result.stream()
                    .skip((queryDto.getPageNum()-1)*queryDto.getPageSize())
                    .limit(queryDto.getPageSize())
                    .collect(Collectors.toList());
        }
        return result;
    }

}
