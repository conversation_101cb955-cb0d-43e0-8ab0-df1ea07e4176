package com.yunhesoft.funcLog.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.funcLog.entity.CommonLogData;
import com.yunhesoft.funcLog.entity.FuncParam;
import com.yunhesoft.funcLog.entity.QueryLogDataDto;
import com.yunhesoft.funcLog.service.ILogGenerate;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.model.Pagination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Description: 通用日志服务实现
 * @date 2024/10/21
 */
@Service("commonLog")
public class CommonLogServiceImpl implements ILogGenerate<CommonLogData> {
    @Lazy
    @Qualifier("commonLog")
    @Autowired
    private ILogGenerate logGenerate;

    @Override
    public void recordLogFuncBefore(CommonLogData funcParam) {
        funcParam.setId(TMUID.getUID());
        //这个可选如果需要进行缓存多条日志数据
        //addLogData(funcParam);
    }

    @Override
    public void recordLogFuncAfter(long time, FuncParam funcParam, Object funcResult) {
        String returnString = JSONObject.toJSONString(funcResult);
        funcParam.setReturnValue(returnString);
        funcParam.setCostTime(time);
        SysUser currentUser = SysUserHolder.getCurrentUser();
        if(currentUser!=null){
            funcParam.setOrgCode(currentUser.getOrgId());
            funcParam.setOrgName(currentUser.getOrgName());
        }
        List<CommonLogData> list = new ArrayList<>();
        list.add(funcParam);
        logGenerate.saveLog(list);
    }

    @Override
    public void saveLog(List<CommonLogData> data) {

        List<CommonLogData> logData = ObjUtils.copyToList(data, CommonLogData.class);
        if (StringUtils.isEmpty(logData)) {
            return;
        }
        //根据配置调用日志存储服务
        getLogStoreService().saveLog(logData);
    }

    /**
     * 日志查询
     *
     * @return
     * <AUTHOR>
     * @date 2024/12/30
     * @params
     */
    @Override
    public List<CommonLogData> getLogInfoList(QueryLogDataDto queryDto, Pagination<?> page) {
        //获取日志存储服务
        return getLogStoreService().getLogInfoList(queryDto, page);
    }

    /**
     * 日志自动清除
     *
     * @param liveDays
     * @return
     * <AUTHOR>
     * @date 2024/12/30
     * @params
     */
    @Override
    public void autoClearLog(int liveDays) {
        getLogStoreService().autoClearLog(liveDays);
    }
}
