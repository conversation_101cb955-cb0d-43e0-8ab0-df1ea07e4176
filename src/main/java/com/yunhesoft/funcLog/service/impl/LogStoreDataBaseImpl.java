package com.yunhesoft.funcLog.service.impl;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.funcLog.entity.CommonLogData;
import com.yunhesoft.funcLog.entity.QueryLogDataDto;
import com.yunhesoft.funcLog.service.ILogStoreService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service("dataBase")
public class LogStoreDataBaseImpl implements ILogStoreService {

    @Autowired
    private EntityService dao;

    @Override
    public void saveLog(List<?> data) {
        dao.insertBatch(data);
    }

    @Override
    public List<CommonLogData> getLogInfoList(QueryLogDataDto queryDto, Pagination<?> page) {
        Where where = Where.create();
        if (StringUtils.isNotEmpty(queryDto.getStartDate())) {
            where.ge(CommonLogData::getLogTime, queryDto.getStartDate());
        }
        if (StringUtils.isNotEmpty(queryDto.getEndDate())) {
            where.le(CommonLogData::getLogTime, queryDto.getEndDate());
        }
        if (StringUtils.isNotEmpty(queryDto.getMoudle())) {
            where.like(CommonLogData::getMoudle, queryDto.getMoudle());
        }
        if (StringUtils.isNotEmpty(queryDto.getFunc())) {
            where.like(CommonLogData::getFunc, queryDto.getFunc());
        }
        if (StringUtils.isNotEmpty(queryDto.getOrgName())) {
            where.like(CommonLogData::getOrgName, queryDto.getOrgName());
        }
        if (StringUtils.isNotEmpty(queryDto.getOperationPerson())) {
            where.like(CommonLogData::getOperationPerson, queryDto.getOperationPerson());
        }
        if (StringUtils.isNotEmpty(queryDto.getApiPath())) {
            where.like(CommonLogData::getAccessUrl, queryDto.getApiPath());
        }
        Order order = Order.create();
        order.orderByDesc(CommonLogData::getLogTime);
        return dao.queryList(CommonLogData.class, where, order, page);
    }

    /**
     * 自动清除
     *
     * @param liveDays
     * @return
     * <AUTHOR>
     * @date 2025/1/3
     * @params
     */
    @Override
    public void autoClearLog(int liveDays) {
        Date date = DateTimeUtils.doDate(new Date(), -liveDays);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = sdf.format(date);
        Where where = Where.create();
        where.lt(CommonLogData::getLogTime, date);
        dao.rawDeleteByWhere(CommonLogData.class, where);
    }
}
