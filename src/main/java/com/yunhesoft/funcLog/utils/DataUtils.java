package com.yunhesoft.funcLog.utils;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.funcLog.annotation.LogField;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DataUtils {
    /**
     * 数据比对  （文字描述）
     *
     * @return
     * <AUTHOR>
     * @date 2024/10/23
     * orgin是旧数据  target是新数据
     * 传入两个对象，如果origin为空target不为空为新增操作反之删除操作，都不为空将会进行数据比对
     */

    public static List<DataChangeDescirbe> dataDiffTextDescribe(Object oldObj, Object newObj) {
        List<DataChangeDescirbe> diffResult = new ArrayList<>();
        if (oldObj == null && newObj != null) {
            DataChangeDescirbe change = new DataChangeDescirbe();
            change.setChangeType(1);
            change.setMsg("添加一条数据");
            diffResult.add(change);
        } else if (oldObj != null && newObj == null) {
            DataChangeDescirbe change = new DataChangeDescirbe();
            change.setChangeType(-1);
            change.setMsg("删除一条数据");
            diffResult.add(change);
        } else if (oldObj != null && newObj != null) {
            //进行数据比对
            diffResult = compareObjects(oldObj, newObj);
        }
        return diffResult;
    }

    private static List<DataChangeDescirbe> compareObjects(Object oldObj, Object newObj) {
        if (oldObj == null || newObj == null || !oldObj.getClass().equals(newObj.getClass())) {
            throw new IllegalArgumentException("数据比对失败，需要提供两个相同类型的实例并且都不能为空");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<DataChangeDescirbe> diffResult = new ArrayList<>();
        //获取全部字段
        Field[] fields = oldObj.getClass().getDeclaredFields();
        for (Field field : fields) {
            LogField annotation = field.getAnnotation(LogField.class);
            if (annotation == null) {
                //不受到数据监控的字段
                continue;
            }
            // 确保私有字段也可以访问
            field.setAccessible(true);
            try {
                Object value1 = field.get(oldObj);
                String value1Str = String.valueOf(value1);
                if (value1 == null || StringUtils.isEmpty(value1Str)) {
                    value1Str = "空";
                } else if (value1 instanceof Date) {
                    value1Str = sdf.format((Date) value1);
                }
                Object value2 = field.get(newObj);
                String value2Str = String.valueOf(value2);
                if (value2 == null || StringUtils.isEmpty(value2Str)) {
                    value2Str = "空";
                } else if (value2 instanceof Date) {
                    value2Str = sdf.format((Date) value2);
                }
                if (!value2Str.equals(value1Str)) {
                    DataChangeDescirbe change = new DataChangeDescirbe();
                    change.setChangeType(0);
                    change.setMsg(annotation.value());
                    change.setOldValue(value1Str);
                    change.setNewValue(value2Str);
                    diffResult.add(change);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return diffResult;
    }
}
