package com.yunhesoft.funcLog.aspect;


import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.funcLog.annotation.LogMethod;
import com.yunhesoft.funcLog.entity.FuncParam;
import com.yunhesoft.funcLog.service.ILogGenerate;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.menu.service.ISysMenuLibInitService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 此方法是日志系统的切面类
 * @date 2024/10/21
 */
@Component
@Aspect
public class FuncAspect {
    @Autowired
    private ISysMenuLibInitService initService;
    @Autowired
    private HttpServletRequest request;
    @Value("${app.description:}")
    private String description;
    @Value("${app.project:}")
    private String projectCode;

    //根据注解切入通知
    @Around("@annotation(com.yunhesoft.funcLog.annotation.LogMethod)")
    public Object funcLog(ProceedingJoinPoint joinPoint) throws Throwable {
        ILogGenerate logGenerate = null;
        //解析方法进入参数
        FuncParam funcParam = new FuncParam();
        //记录日志
        Object[] args = joinPoint.getArgs();
        Class declaringType = joinPoint.getSignature().getDeclaringType();
        Method[] methods = declaringType.getMethods();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取全部方法
        for (Method method : methods) {
            LogMethod annotation = method.getAnnotation(LogMethod.class);
            if (annotation != null && joinPoint.getSignature().getName().equals(method.getName())) {
                logGenerate = SpringUtils.getBean(annotation.handler());
                //获取到方法注解
                byte[] bytes = description.getBytes("ISO-8859-1");
                description = new String(bytes, "UTF-8");
                funcParam.setProject(description);
                funcParam.setProjectCode(projectCode);
                funcParam.setMoudle(annotation.moudle());
                funcParam.setFunc(annotation.func());
                funcParam.setFuncCode(method.getName());
                funcParam.setActionType(annotation.actionType());
                funcParam.setParamType(method.getTypeParameters());
                funcParam.setOperationPerson(SysUserHolder.getCurrentUser() == null ? "" : SysUserHolder.getCurrentUser().getRealName());
                funcParam.setOperationPersonCode(SysUserHolder.getCurrentUser() == null ? "" : SysUserHolder.getCurrentUser().getId());
                //失效日期
                funcParam.setExpireAt(new Date());
                if (args != null) {
                    String ap = "";
                    try {
                        ap = JSONArray.toJSONString(args);
                    } catch (Exception e) {
                        for (int i = 0; i < args.length; i++) {
                            ap = ap + "_;_" + String.valueOf(args[i]);
                        }
                    }
                    funcParam.setAccessParam(ap);
                    funcParam.setParamValue(args);
                }
                funcParam.setReturnType(method.getReturnType());
                funcParam.setClassPath(joinPoint.getTarget().getClass().getName()+"."+method.getName());
                funcParam.setAccessUrl(request.getRequestURI());
                funcParam.setLogTime(sdf.format(new Date()));
                break;
            }
        }
        logGenerate.recordLogFuncBefore(funcParam);
        //记录方法执行开始时间
        long begin = System.currentTimeMillis();
        Object result = null;
        try {
            result = joinPoint.proceed();
        } catch (Exception e) {
            result = "出现异常："+e.getMessage();
            throw e;
        } finally {
            long end = System.currentTimeMillis();
            long time = end - begin;
            logGenerate.recordLogFuncAfter(time, funcParam, result);
            logGenerate.dataCache.clear();
        }
        return result;
    }

}
