package com.yunhesoft.funcLog.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.funcLog.entity.CommonLogData;
import com.yunhesoft.funcLog.entity.QueryLogDataDto;
import com.yunhesoft.funcLog.service.ILogGenerate;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "系统菜单")
@RestController
@RequestMapping("/system/funcLog")
public class LogDataController extends BaseRestController {

    @Autowired
    @Qualifier("commonLog")
    private ILogGenerate logGenerate;

    @ApiOperation(value = "获取功能日志", notes = "获取功能日志")
    @RequestMapping(value = "/getLogInfoList", method = { RequestMethod.POST })
    public Res<?> getLogInfoList(@RequestBody  QueryLogDataDto queryDto) {
        Res<List<CommonLogData>> res = new Res<>();
        Pagination<?> page=null;
        if (queryDto.getPageSize() != null && queryDto.getPageSize() > 0) {// 创建分页信息
            page = Pagination.create(queryDto.getPageNum() == null ? 1 : queryDto.getPageNum(), queryDto.getPageSize());
        }
        List<CommonLogData> logInfoList = logGenerate.getLogInfoList(queryDto, page);
        res.setResult(logInfoList);
        if(page!=null){
            res.setTotal(page.getTotal());
        }
        return res;
    }
}
