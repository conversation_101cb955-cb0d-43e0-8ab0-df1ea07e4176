package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算——汇总表核算指标")
@Getter
@Setter
@Entity
@Table(name = "COSTSUMMARYPARAMDATA")
public class CostSummaryParamData extends BaseEntity {

	private static final long serialVersionUID = 2865928450132155962L;

	@ApiModelProperty(value = "报表ID")
	@Column(name = "PID", length = 100)
	private String pid;

	@ApiModelProperty(value = "核算单元ID")
	@Column(name = "UNITID", length = 100)
	private String unitId;

	@ApiModelProperty(value = "方案ID")
	@Column(name = "PROGRAMID", length = 100)
	private String programId;

	@ApiModelProperty(value = "班组ID")
	@Column(name = "TEAMID", length = 100)
	private String teamId;

	@ApiModelProperty(value = "指标ID")
	@Column(name = "PARAMID", length = 100)
	private String paramId;

	@ApiModelProperty(value = "标准值")
	@Column(name = "BASEVAL")
	private Double baseVal;

	@ApiModelProperty(value = "计算值")
	@Column(name = "CALCVAL")
	private Double calcVal;

	@ApiModelProperty(value = "工作时长")
	@Column(name = "WORKINGHOUR")
	private Double workingHour;

	@ApiModelProperty(value = "开始时间")
	@Column(name = "BEGINTIME", length = 100)
	private String beginTime;

	@ApiModelProperty(value = "结束时间")
	@Column(name = "ENDTIME", length = 100)
	private String endTime;

	@ApiModelProperty(value = "填写日期")
	@Column(name = "WRITEDAY", length = 10)
	private String writeDay;

	@ApiModelProperty(value = "报表类型")
	@Column(name = "REPORTTYPE", length = 20)
	private String reportType;

	@ApiModelProperty(value = "年计算值")
	@Column(name = "YCALCVAL")
	private Double yCalcVal;

	@ApiModelProperty(value = "年工作时长")
	@Column(name = "YWORKINGHOUR")
	private Double yWorkingHour;

}
