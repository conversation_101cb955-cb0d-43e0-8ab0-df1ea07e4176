package com.yunhesoft.leanCosting.costReport.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.druid.util.StringUtils;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.service.ICostTeamInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class CostTeamInfoServiceImpl implements ICostTeamInfoService {
	
	
	@Autowired
	private EntityService entityService;

	@Override
	public List<CostTeamInfo> getCostTeamInfoDatas(CostReportQueryDto dto) {
		Where where = Where.create();
		if (dto != null) {
			if (!StringUtils.isEmpty(dto.getUnitId())) {
				where.eq(CostTeamInfo::getUnitId, dto.getUnitId());
			}
			if (!StringUtils.isEmpty(dto.getJzrq())) {// 日期
				where.eq(CostTeamInfo::getWriteDay, dto.getJzrq());
			}
			if (!StringUtils.isEmpty(dto.getContentId())) {// 方案ID
				where.eq(CostTeamInfo::getProgramId, dto.getContentId());
			}
			if (!StringUtils.isEmpty(dto.getShiftId())) {// 班组ID
				where.eq(CostTeamInfo::getShiftId, dto.getShiftId());
			}
		}
		Order order = Order.create();
		return entityService.queryList(CostTeamInfo.class, where, order);
	}

}
