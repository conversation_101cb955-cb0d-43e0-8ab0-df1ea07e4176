package com.yunhesoft.leanCosting.productSchedu.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.accountTools.service.IAccountToolsService;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.ProjectType;
import com.yunhesoft.leanCosting.calcLogic.IAutoShiftCalcLogic;
import com.yunhesoft.leanCosting.calcLogic.ICalcTeamProjectLogic;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.productSchedu.entity.dto.CostProgStartTimeDto;
import com.yunhesoft.leanCosting.productSchedu.entity.dto.ZzRunStateDto;
import com.yunhesoft.leanCosting.productSchedu.entity.po.CostProgChangeSize;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduPlanStart;
import com.yunhesoft.leanCosting.productSchedu.entity.po.UnitRunStatus;
import com.yunhesoft.leanCosting.productSchedu.entity.vo.CostInputShiftVo;
import com.yunhesoft.leanCosting.productSchedu.entity.vo.ProductUnitProgShift;
import com.yunhesoft.leanCosting.productSchedu.entity.vo.ZzRunStateStartVo;
import com.yunhesoft.leanCosting.productSchedu.service.IZzRunStateService;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.leanCosting.steadyRate.service.ISteadyRateServise;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostuintQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostuintVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.ICostuintService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.mobile.entity.dto.DeviceStatusSwitchDto;
import com.yunhesoft.mobile.entity.po.AcctobjInputmx;
import com.yunhesoft.mobile.service.IMobLeanCostingService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class ZzRunStateServiceImpl implements IZzRunStateService {

	@Autowired
	private EntityService dao;

	@Autowired
	private ICostService costService;

	@Autowired
	ICalcTeamProjectLogic ctpl;

	@Autowired
	IShiftService shift;

	@Autowired
	IProgramService iProg;

	@Autowired
	private IUnitMethodService unitMeth;
	
	@Autowired
	private ICostuintService costUnit;
	
	@Autowired
	private ISteadyRateServise steadyRateServ;
	
	@Autowired
	IAutoShiftCalcLogic iascl;
	

	@Autowired
	private RedisUtil redis;
	
	@Autowired
	private IAccountToolsService atoolsSrv;
	
	@Autowired
	private IMobLeanCostingService mobLeanCost;

	/**
	 * 获取装置运行状态工作台展示数据
	 * 
	 * @param dto
	 */
	@Override
	public JSONObject getZzRunStateData(ZzRunStateDto dto) {

		JSONObject result = new JSONObject();// 数据

		// 返回两个日期之间所以日期
		List<String> listdt = new ArrayList<String>();
		JSONArray array_table = new JSONArray();
		try {
			listdt = getdates(dto.getStartDt(), dto.getEndDt());
//					jtable.put("unitName", "核算对象");
			// 编制表头
			if (listdt != null && listdt.size() > 0) {
				for (int i = 0; i < listdt.size(); i++) {
					JSONObject jtable = new JSONObject();// 表头
					String dt = listdt.get(i);
					jtable.put("id", "A" + i + 1);
					jtable.put("name", dt);
					array_table.add(jtable);
				}
			}
		} catch (ParseException e) {
			log.error("", e);
		}
		result.put("table", array_table);// 填充表头

		List<Costuint> listUnit = new ArrayList<Costuint>();// 核算对象列表
		List<String> listId = new ArrayList<String>();// 查询子表数据用-核算ID
		List<String> listrq = new ArrayList<String>();// 查询子表数据用-日期区间
		String unitIdStr = "";
		String orgIds = dto.getOrgIds();
		List<Costuint> listCostuint = new ArrayList<Costuint>();
		if(orgIds!=null && orgIds.length()>0) {//根据机构编码获取核算对象列表
			listCostuint = costService.getCostuintListOrgId(orgIds);
			if (listCostuint != null && listCostuint.size() > 0) {
//				listUnit.addAll(listCostuint);
				for (int i = 0; i < listCostuint.size(); i++) {
					Costuint e = listCostuint.get(i);
					int ledgerEntry = e.getLedgerEntry()==null?1:e.getLedgerEntry();
					if(ledgerEntry==1) {
						listUnit.add(e);
						listId.add(e.getId());
						if (unitIdStr.length() > 0) {
							unitIdStr += ",'" + e.getId() + "'";
						} else {
							unitIdStr = "'" + e.getId() + "'";
						}
					}
				}
			}
		}else if(StringUtils.isNotEmpty(dto.getUnitId())) {//根据父核算对象获取子核算对象列表
			CostDto cdto = new CostDto();
			cdto.setIsLedgerEntry(true);
			cdto.setPid(dto.getUnitId());
//			listCostuint = costService.getData(cdto);
			List<CostuintVo> tlist = costService.getCostuintTreeVo(cdto);
			if(StringUtils.isNotEmpty(tlist)) {
				for (CostuintVo vo : tlist) {
					Costuint u = ObjUtils.convertTo(vo, Costuint.class);
					listCostuint.add(u);
				}
			}
			if (listCostuint != null && listCostuint.size() > 0) {
//				listUnit.addAll(listCostuint);
				for (int i = 0; i < listCostuint.size(); i++) {
					Costuint e = listCostuint.get(i);
					int ledgerEntry = e.getLedgerEntry()==null?1:e.getLedgerEntry();
					if(ledgerEntry==1 && !new Integer(1).equals(e.getProductiveType())) {//设备维保不显示
						listUnit.add(e);
						listId.add(e.getId());
						if (unitIdStr.length() > 0) {
							unitIdStr += ",'" + e.getId() + "'";
						} else {
							unitIdStr = "'" + e.getId() + "'";
						}
					}
				}
			}
		}
		
//		SysUser user = SysUserHolder.getCurrentUser();
//		List<Costuint> listCostuint = costService.getCostuintListByOrgId(user.getOrgId(), 2);
//		// 查指定的核算对象数据
//		if (dto.getUnitId() != null && !dto.getUnitId().equals("all")) {
//			String unitId = dto.getUnitId();
//			String unitMc = dto.getUnitName();
//			Costuint e = new Costuint();
//			e.setId(unitId);
//			e.setName(unitMc);
//			if (listCostuint != null && listCostuint.size() > 0) {
//				for (int i = 0; i < listCostuint.size(); i++) {
//					Costuint ec = listCostuint.get(i);
//					if (ec.getId().equals(unitId)) {
//						e.setFormId(ec.getFormId());
//					}
//				}
//			}
//			if (unitIdStr.length() > 0) {
//				unitIdStr += ",'" + unitId + "'";
//			} else {
//				unitIdStr = "'" + unitId + "'";
//			}
//			listUnit.add(e);
//			listId.add(unitId);
//		} else {
//			// 查询全部核算对象数据
//			if (listCostuint != null && listCostuint.size() > 0) {
////				listUnit.addAll(listCostuint);
//				for (int i = 0; i < listCostuint.size(); i++) {
//					Costuint e = listCostuint.get(i);
//					int ledgerEntry = e.getLedgerEntry()==null?1:e.getLedgerEntry();
//					if(ledgerEntry==1) {
//						listUnit.add(e);
//						listId.add(e.getId());
//						if (unitIdStr.length() > 0) {
//							unitIdStr += ",'" + e.getId() + "'";
//						} else {
//							unitIdStr = "'" + e.getId() + "'";
//						}
//					}
//				}
//			}
//		}
		listrq.add(dto.getStartDt());
		listrq.add(dto.getEndDt());

		if (StringUtils.isNotEmpty(listId) && listId.size() > 0) {
			JSONArray array = new JSONArray();

			if (StringUtils.isNotEmpty(listUnit)) {
				// 核算对象运行状态 start
				// *********************************************************************
//				LinkedHashMap<String, UnitRunStatus> mapRun = new LinkedHashMap<String, UnitRunStatus>();
//				Where whereRun = Where.create();
//				whereRun.in(UnitRunStatus::getUnitid, listId.toArray());
//				List<UnitRunStatus> listRun = new ArrayList<UnitRunStatus>();
//				listRun = dao.queryData(UnitRunStatus.class, whereRun, null, null);
//				if (StringUtils.isNotEmpty(listRun)) {
//					for (UnitRunStatus temp : listRun) {
//						mapRun.put(temp.getUnitid(), temp);
//					}
//				}
				List<ProductScheduPlanStart> listStartTemp = new ArrayList<ProductScheduPlanStart>();
				Where whereTemp = Where.create();
				whereTemp.in(ProductScheduPlanStart::getUnitid, listId.toArray());
				whereTemp.le(ProductScheduPlanStart::getStartdatetime, DateTimeUtils.getNowDateTimeStr());
				Order orderTemp = Order.create();
				orderTemp.orderByAsc(ProductScheduPlanStart::getUnitid);
				orderTemp.orderByDesc(ProductScheduPlanStart::getStartdatetime);
				listStartTemp = dao.queryData(ProductScheduPlanStart.class, whereTemp, orderTemp, null);
				LinkedHashMap<String, UnitRunStatus> mapRun = new LinkedHashMap<String, UnitRunStatus>();
				LinkedHashMap<String, String> mapRunUnitTemp = new LinkedHashMap<String, String>();
				if(StringUtils.isNotEmpty(listStartTemp)) {
					for(ProductScheduPlanStart temp : listStartTemp) {
						if(mapRunUnitTemp!=null && mapRunUnitTemp.containsKey(temp.getUnitid())) {
							
						}else {
							mapRunUnitTemp.put(temp.getUnitid(), temp.getProgramid());
							List<String> listProg = new ArrayList<String>();
							listProg.add(temp.getProgramid());
							HashMap<String, ProjectType> mapProgStatus = new HashMap<String, ProjectType>();
							mapProgStatus = iProg.getProgramStateByIdList(listProg);
							if (mapProgStatus != null && mapProgStatus.containsKey(temp.getProgramid())) {
								ProjectType beanType = mapProgStatus.get(temp.getProgramid());
								UnitRunStatus bean = new UnitRunStatus();
								bean.setId(TMUID.getUID());
								bean.setRunstatusid(beanType.getId());
								bean.setRunstatusname(beanType.getDsname());
								bean.setRuntypecolor(beanType.getTypecolor());
								bean.setStartdatetime(temp.getStartdatetime());
								bean.setUnitid(temp.getUnitid());
								mapRun.put(temp.getUnitid(), bean);
							}
						}
					}
				}
				// 核算对象 运行状态 end
				// *********************************************************************

				// 获取开始线数据 start
				// ***********************************************************************************************
				String dtoStartdt = dto.getStartDt() + " 00:00:00";
				String dtoEnddt = dto.getEndDt() + " 23:59:59";
				Where whereStart = Where.create();
				whereStart.in(ProductScheduPlanStart::getUnitid, listId.toArray());
				whereStart.and();
				whereStart.lb();
				whereStart.lb();
				whereStart.le(ProductScheduPlanStart::getStartdatetime, dtoStartdt);
				whereStart.ge(ProductScheduPlanStart::getEnddatetime, dtoStartdt);
				whereStart.rb();
				whereStart.or();
				whereStart.lb();
				whereStart.le(ProductScheduPlanStart::getStartdatetime, dtoStartdt);
				whereStart.ge(ProductScheduPlanStart::getEnddatetime, dtoEnddt);
				whereStart.rb();
				whereStart.or();
				whereStart.lb();
				whereStart.ge(ProductScheduPlanStart::getStartdatetime, dtoStartdt);
				whereStart.le(ProductScheduPlanStart::getEnddatetime, dtoEnddt);
				whereStart.rb();
				whereStart.or();
				whereStart.lb();
				whereStart.ge(ProductScheduPlanStart::getStartdatetime, dtoStartdt);
				whereStart.le(ProductScheduPlanStart::getStartdatetime, dtoEnddt);
				whereStart.ge(ProductScheduPlanStart::getEnddatetime, dtoEnddt);
				whereStart.rb();
				whereStart.rb();
				Order orderStart = Order.create();
				orderStart.orderByAsc(ProductScheduPlanStart::getUnitid);
				orderStart.orderByDesc(ProductScheduPlanStart::getStartdatetime);
				List<ProductScheduPlanStart> listStart = dao.queryData(ProductScheduPlanStart.class, whereStart,
						orderStart, null);
				LinkedHashMap<String, LinkedHashMap<String, List<ZzRunStateStartVo>>> mapStart = new LinkedHashMap<String, LinkedHashMap<String, List<ZzRunStateStartVo>>>();
				if (StringUtils.isNotEmpty(listStart)) {
					for (ProductScheduPlanStart temp : listStart) {
						ZzRunStateStartVo startVo = new ZzRunStateStartVo();
						String ksrq = temp.getStartdatetime().substring(0, 10);
						String runEndDate = temp.getEnddatetime() == null ? dto.getEndDt()
								: temp.getEnddatetime().substring(0, 10);

						for (int j = 0; j < listdt.size(); j++) {
//							// ******** 核算数据 **************************************************************
							int intStartday = 0;
							intStartday = DateTimeUtils.dayDiff(DateTimeUtils.parseDate(ksrq),
									DateTimeUtils.parseDate(listdt.get(j)));
							int intEndday = 0;
							intEndday = DateTimeUtils.dayDiff(DateTimeUtils.parseDate(runEndDate),
									DateTimeUtils.parseDate(listdt.get(j)));
							if (intStartday >= 0 && intEndday <= 0) {// 计划的开始日期小于等于循环的日期 并且循环的日期小于等于计划的结束日期才能有数据
								startVo.setEnddatetime(temp.getEnddatetime());
								startVo.setProgramid(temp.getProgramid());
								startVo.setStartdatetime(temp.getStartdatetime());
								startVo.setUnitid(temp.getUnitid());
								LinkedHashMap<String, List<ZzRunStateStartVo>> mapTemp = new LinkedHashMap<String, List<ZzRunStateStartVo>>();
								List<ZzRunStateStartVo> listTemp = new ArrayList<ZzRunStateStartVo>();
								if (mapStart != null && mapStart.containsKey(temp.getUnitid())) {
									mapTemp = mapStart.get(temp.getUnitid());
									if (mapTemp != null && mapTemp.containsKey(listdt.get(j))) {
										listTemp = mapTemp.get(listdt.get(j));
									}
									listTemp.add(startVo);
									mapTemp.put(listdt.get(j), listTemp);
									mapStart.put(temp.getUnitid(), mapTemp);
								} else {
									listTemp.add(startVo);
									mapTemp.put(listdt.get(j), listTemp);
									mapStart.put(temp.getUnitid(), mapTemp);
								}
							}
						}
					}
				}
				// 获取开始线数据 end
				// ***********************************************************************************************

				// 获取核算是否录入数据 start
				// ***********************************************************************************************
				List<CostTeamInfo> listCost = new ArrayList<CostTeamInfo>();
				Where whereCostInput = Where.create();
				whereCostInput.eq(CostTeamInfo::getProgramId, "0");
				whereCostInput.in(CostTeamInfo::getUnitId, listId.toArray());
				whereCostInput.ge(CostTeamInfo::getWriteDay, dto.getStartDt());
				whereCostInput.le(CostTeamInfo::getWriteDay, dto.getEndDt());
				LinkedHashMap<String, String> mapCostInput = new LinkedHashMap<String, String>();
				listCost = dao.queryData(CostTeamInfo.class, whereCostInput, null, null);
				if (StringUtils.isNotEmpty(listCost)) {
					String costInputKey = "";
					for (CostTeamInfo temp : listCost) {
						// key值 核算对象，日期+班次代码（方案已默认取是0的了）
						costInputKey = temp.getUnitId() + temp.getWriteDay() + temp.getShiftId();
						mapCostInput.put(costInputKey, temp.getId());
					}
				}
				// 获取核算是否录入数据 end
				// ***********************************************************************************************
				List<String> toConfirmList = atoolsSrv.getAccountTodoConfirmList(dto.getStartDt(), dto.getEndDt());//获取干部未确认数据信息
				for (int i = 0; i < listUnit.size(); i++) {

					JSONObject jobjdata = new JSONObject();// 表头
					// 获取核算对象的操作机构 和上班的班次数据，从静态倒班中获取，需要先获取 核算对象的操作机构，然后才能获取当班的数据 start
					// **********************************
					Costuint e = listUnit.get(i);
					MethodQueryDto qdto = new MethodQueryDto();
					qdto.setUnitid(e.getId());// 核算对象ID
					qdto.setObjType("org");
					List<Costunitoperator> listCzjg = unitMeth.getCostunitoperatorList(qdto);
					List<String> listOrg = new ArrayList<String>();
					if (StringUtils.isNotEmpty(listCzjg)) {
						for (Costunitoperator temp : listCzjg) {
							listOrg.add(temp.getObjid());
						}
					}
					LinkedHashMap<String, List<ZzRunStateStartVo>> mapStartData = new LinkedHashMap<String, List<ZzRunStateStartVo>>();
					mapStartData = mapStart.get(e.getId());
					LinkedHashMap<String, List<CostInputShiftVo>> mapShift = new LinkedHashMap<String, List<CostInputShiftVo>>();
					List<ShiftForeignVo> listShift = new ArrayList<ShiftForeignVo>();
					if(listOrg!=null && listOrg.size()>0) {
						listShift = shift.getShiftDataByksrqjzrq(listOrg, dto.getStartDt(), dto.getEndDt());
					}
					if (StringUtils.isNotEmpty(listShift)) {
						for (ShiftForeignVo temp : listShift) {
							CostInputShiftVo shiftVo = new CostInputShiftVo();
							shiftVo = ObjUtils.copyTo(temp, CostInputShiftVo.class);
							List<CostInputShiftVo> listTemp = new ArrayList<CostInputShiftVo>();
							if (mapShift != null && mapShift.containsKey(temp.getTbrq())) {
								listTemp = mapShift.get(temp.getTbrq());
								listTemp.add(shiftVo);
								mapShift.put(temp.getTbrq(), listTemp);
							} else {
								listTemp.add(shiftVo);
								mapShift.put(temp.getTbrq(), listTemp);
							}
						}
					}
					// 获取核算对象的操作机构 和上班的班次数据，从静态倒班中获取，需要先获取 核算对象的操作机构，然后才能获取当班的数据 end
					// **********************************

					// 获取名称 start *****************************
					LinkedHashMap<String, ProgramItem> mapProg = new LinkedHashMap<String, ProgramItem>();
					List<ProgramItem> listProg = iProg.getProgramItemListByUnitid(e.getId());
					if (listProg != null && listProg.size() > 0) {
						for (ProgramItem temp : listProg) {
							mapProg.put(temp.getId(), temp);
						}
					}
					// 获取名称 end *****************************
					String unitId = e.getId();
					String unitName = e.getName();
					String formId = e.getFormId();
					jobjdata.put("unitId", unitId);
					jobjdata.put("unitName", unitName);
					jobjdata.put("formId", formId);
					Object progType = new Object();
					if (mapRun != null && mapRun.containsKey(unitId)) {
						progType = new UnitRunStatus();
						progType = mapRun.get(unitId);
					}
					jobjdata.put("unitProgStatus", progType);// 核算对象当前时间的状态（开、停、运、检）
//					// 遍历日期，给每天赋值
					if (listdt != null && listdt.size() > 0) {
//						LinkedHashMap<String, String> mapPlan = new LinkedHashMap<String, String>();
						for (int j = 0; j < listdt.size(); j++) {
							JSONObject jsonResult = new JSONObject();
//							// ******** 核算数据 **************************************************************
							String dt = listdt.get(j);// 日期
							List<Object> listShiftResult = new ArrayList<Object>();
							if (mapShift.containsKey(dt)) {
								List<CostInputShiftVo> listShiftVo = mapShift.get(dt);
								if (StringUtils.isNotEmpty(listShiftVo)) {
									for (CostInputShiftVo temp : listShiftVo) {
										String costInputKey = e.getId() + dt + temp.getShiftClassCode();
										if (mapCostInput != null && mapCostInput.containsKey(costInputKey)) {
											temp.setIsInput(1);
											temp.setId(mapCostInput.get(costInputKey));
										}
										listShiftResult.add(temp);
										//判断是否有干部确认，增加标识
										if(StringUtils.isNotEmpty(toConfirmList) && toConfirmList.contains(temp.getTbrq()+"_"+unitId+"_"+temp.getShiftClassCode())) {
											temp.setToConfirm(true);
										}
									}
								}
							}
							if (listShiftResult != null && listShiftResult.size() > 0) {
								jsonResult.put("cost", listShiftResult);
							}
							// ******** 核算数据 end
							// *************************************************************

							// ******** 装置运行开始数据 start
							// *************************************************************
							List<Object> listZzRunStart = new ArrayList<Object>();
							if (mapStartData != null && mapStartData.containsKey(dt)) {
								List<ZzRunStateStartVo> listZzRunStartData = mapStartData.get(dt);
								if (StringUtils.isNotEmpty(listZzRunStartData)) {
									for (ZzRunStateStartVo temp : listZzRunStartData) {
										listZzRunStart.add(temp);
										if (temp.getProgramid() != null && mapProg.containsKey(temp.getProgramid())) {// 获取方案名称
											temp.setProgramname(mapProg.get(temp.getProgramid()).getPiName());
										}
									}
								}
							}
							if (listZzRunStart != null && listZzRunStart.size() > 0) {
								jsonResult.put("zzRunStateStart", listZzRunStart);
							}
							// ******** 装置运行开始数据 end
							// *************************************************************
							jobjdata.put("A" + j + 1, jsonResult);
						}
					}
					array.add(jobjdata);
				}
			}
			result.put("data", array);// 填充数据
		}
		return result;
	}

	/**
	 * 给2个日期参数，返回2个日期之间的所有日期
	 * 
	 * @return
	 */
	private List<String> getdates(String d1, String d2) throws ParseException {
		List<String> list = new ArrayList<String>();
		if (d1 == null || d2 == null || d1.equals("") || d2.equals("")) {
			return list;
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		long m1 = sdf.parse(d1).getTime();
		long m2 = sdf.parse(d2).getTime();
		Long oneDay1 = (long) (1000 * 60 * 60 * 24);
		while (m1 <= m2) {
			Date d = new Date(m1);
			list.add(sdf.format(d));
			m1 += oneDay1;
		}
		return list;
	}

	/**
	 * 判断传递过来的开始时间参数是否已存在，如果已存在，不允许保存
	 * 
	 * @param unitId        核算对象编码
	 * @param startDatetime 开始日期+时单
	 * @return
	 */
	@Override
	public boolean isCanSaveStartDateTime(CostProgStartTimeDto dto) {
		String unitId = dto.getUnitId();
		String startDatetime = dto.getStartDatetime();
		String dataId = dto.getDataId();
		boolean result = true;
		Where where = Where.create();
		where.eq(ProductScheduPlanStart::getUnitid, unitId);
		where.eq(ProductScheduPlanStart::getStartdatetime, startDatetime);
		if(dataId!=null && dataId.length()>0) {
			where.ne(ProductScheduPlanStart::getId, dataId);
		}
		List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
		list = dao.queryData(ProductScheduPlanStart.class, where, null, null);
		if (StringUtils.isNotEmpty(list) && list.size() > 0) {
			result = false;
		}
		return result;
	}

//	/**
//	 * 按核算对象保存开始日期+时间数据
//	 * 
//	 * @param unitId        核算对象编码
//	 * @param programid     方案编码
//	 * @param planId        计划编码
//	 * @param startDatetime 开始日期+时间
//	 * @param xbsj          下班时间
//	 * @param type          类型，0：结束日期+时间为下班时间，1：结束日期+时间为下班时间向后推算10年
//	 * @return
//	 */
//	@Override
//	public boolean saveStartDateTime(String unitId, String programid, String planId, String startDatetime) {
//		boolean result = false;
//		List<UnitRunStatus> listRun = new ArrayList<UnitRunStatus>();
//		Where whereRun = Where.create();
//		whereRun.eq(UnitRunStatus::getUnitid, unitId);
//		whereRun.ge(UnitRunStatus::getStartdatetime, startDatetime);
//		listRun = dao.queryData(UnitRunStatus.class, whereRun, null, null);
//		if (listRun != null && listRun.size() > 0) {
//
//		} else {
//			List<String> listProg = new ArrayList<String>();
//			listProg.add(programid);
//			HashMap<String, ProjectType> mapProgStatus = new HashMap<String, ProjectType>();
//			mapProgStatus = iProg.getProgramStateByIdList(listProg);
//			if (mapProgStatus != null && mapProgStatus.containsKey(programid)) {
//				ProjectType beanType = mapProgStatus.get(programid);
//				UnitRunStatus bean = new UnitRunStatus();
//				bean.setId(TMUID.getUID());
//				bean.setRunstatusid(beanType.getId());
//				bean.setRunstatusname(beanType.getDsname());
//				bean.setRuntypecolor(beanType.getTypecolor());
//				bean.setStartdatetime(startDatetime);
//				bean.setUnitid(unitId);
//				dao.insert(bean);
//			}
//		}
//		// 获取小于 开始日期+时间参数最大的 开始日期+时间
//		Where whereOld = Where.create();
//		whereOld.eq(ProductScheduPlanStart::getUnitid, unitId);
//		whereOld.lt(ProductScheduPlanStart::getStartdatetime, startDatetime);
//		String startDatetimeOld = dao.findMaxValue(ProductScheduPlanStart.class,
//				ProductScheduPlanStart::getStartdatetime, String.class, whereOld);
//
//		// 获取 大于开始日期+时间参数最小的 开始日期+时间
//		Where whereNew = Where.create();
//		whereNew.eq(ProductScheduPlanStart::getUnitid, unitId);
//		whereNew.gt(ProductScheduPlanStart::getStartdatetime, startDatetime);
//		String startDatetimeNew = dao.findMinValue(ProductScheduPlanStart.class,
//				ProductScheduPlanStart::getStartdatetime, String.class, whereNew);
//
//		List<ProductScheduPlanStart> listUp = new ArrayList<ProductScheduPlanStart>();
//		if (startDatetimeOld != null && startDatetimeOld.length() > 0) {// 有历史的开始数据
//			Where where = Where.create();
//			where.eq(ProductScheduPlanStart::getUnitid, unitId);
//			where.eq(ProductScheduPlanStart::getStartdatetime, startDatetimeOld);
//			List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
//			list = dao.queryData(ProductScheduPlanStart.class, where, null, null);
//			if (StringUtils.isNotEmpty(list) && list.size() > 0) {// 获取到了历史数据，将历史数据最大的开始日期数据的 结束日期字段 赋值为本次的开始日期+时间
//				ProductScheduPlanStart objOld = new ProductScheduPlanStart();
//				objOld = list.get(0);
//				objOld.setEnddatetime(startDatetime);
//				listUp.add(objOld);
//			}
//		}
//		String endDatetime = "";
//		if (startDatetimeNew != null && startDatetimeNew.length() > 0) {// 有 大于当前传递过来的 开始日期+时间参数的数据
//			Where where = Where.create();
//			where.eq(ProductScheduPlanStart::getUnitid, unitId);
//			where.eq(ProductScheduPlanStart::getStartdatetime, startDatetimeNew);
//			List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
//			list = dao.queryData(ProductScheduPlanStart.class, where, null, null);
//			if (StringUtils.isNotEmpty(list) && list.size() > 0) {// 本次开始的结束日期+时间 为 下一次的开始日期+时间
//				ProductScheduPlanStart objNew = new ProductScheduPlanStart();
//				objNew = list.get(0);
//				endDatetime = objNew.getEnddatetime();
//			} else {
//				endDatetime = DateTimeUtils.formatDateTime(DateTimeUtils.doYear(DateTimeUtils.parseDateTime(startDatetime), 10));
//			}
//		} else {// 没有大于当前传递过来的日期时间节点的数据
//			endDatetime = DateTimeUtils.formatDateTime(DateTimeUtils.doYear(DateTimeUtils.parseDateTime(startDatetime), 10));
//		}
//		List<ProductScheduPlanStart> listAdd = new ArrayList<ProductScheduPlanStart>();
//		ProductScheduPlanStart obj = new ProductScheduPlanStart();
//		obj.setEnddatetime(endDatetime);
//		obj.setId(TMUID.getUID());
//		obj.setPlanid(planId);
//		obj.setProgramid(programid);
//		obj.setStartdatetime(startDatetime);
//		obj.setUnitid(unitId);
//		listAdd.add(obj);
//		boolean blnUp = false;
//		if (StringUtils.isNotEmpty(listUp)) {
//			int row = dao.updateBatch(listUp);
//			if (row > 0) {
//				blnUp = true;
//			}
//		} else {
//			blnUp = true;
//		}
//		boolean blnAdd = false;
//		if (StringUtils.isNotEmpty(listAdd)) {
//			int row = dao.insertBatch(listAdd);
//			if (row > 0) {
//				blnAdd = true;
//			}
//		} else {
//			blnAdd = true;
//		}
//		if (blnAdd && blnUp) {
//			result = true;
//		} else {
//			result = false;
//		}
//		return result;
//	}

	/**
	 * 保存核算交接班录入的主数据
	 * 
	 * @param unitId  核算对象编码
	 * @param tbrq    填写日期
	 * @param sbsj    上班时间
	 * @param xbsj    下班时间
	 * @param orgId   机构代码
	 * @param shiftId 班次代码
	 * @param tjrq    统计日期
	 * @return
	 */
	@Override
	public String saveCostInputMain(String unitId, String tbrq, String sbsj, String xbsj, String orgId, String shiftId,
			String tjrq) {
		String result = "";
		Where where = Where.create();
		where.eq(CostTeamInfo::getUnitId, unitId);
		where.eq(CostTeamInfo::getProgramId, "0");
		where.eq(CostTeamInfo::getWriteDay, tbrq);
		where.eq(CostTeamInfo::getShiftId, shiftId);
		List<CostTeamInfo> list = new ArrayList<CostTeamInfo>();
		list = dao.queryData(CostTeamInfo.class, where, null, null);
		if (list != null && list.size() > 0) {// 已保存过数据
			result = list.get(0).getId();
		} else {
			String tmuid = TMUID.getUID();
			CostTeamInfo bean = new CostTeamInfo();
			bean.setBeginTime(sbsj);
			bean.setEndTime(xbsj);
			bean.setId(tmuid);
			bean.setProgramId("0");
			bean.setShiftId(shiftId);
			bean.setSummaryDay(tjrq);
			bean.setTeamId(orgId);
			bean.setUnitId(unitId);
			bean.setWriteDay(tbrq);
			int row = dao.insert(bean);
			if (row > 0) {
				result = tmuid;
			}
		}
		return result;
	}

	/**
	 * 平稳率超限点判断的请求：传时间，得到所有的核算对象在传入时间点的方案ID，返回一个hashmap<核算对象ID,方案ID>。
	 * 
	 * @param nowDt 日期+时间 不传默认取当前日期+时间
	 * @return
	 */
	@Override
	public LinkedHashMap<String, String> getUnitProgByDatetime(String nowDt) {
		LinkedHashMap<String, String> result = new LinkedHashMap<String, String>();
		if (nowDt == null || nowDt.length() <= 0) {
			nowDt = DateTimeUtils.getNowDateTimeStr();
		}
		List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
		Where where = Where.create();
		where.le(ProductScheduPlanStart::getStartdatetime, nowDt);// 开始时间小于等于参数
		where.gt(ProductScheduPlanStart::getEnddatetime, nowDt);// 结束时间大于参数
		list = dao.queryData(ProductScheduPlanStart.class, where, null, null);
		if (StringUtils.isNotEmpty(list)) {
			for (ProductScheduPlanStart temp : list) {
				result.put(temp.getUnitid(), temp.getProgramid());
			}
		}
		return result;
	}

	/**
	 * 根据id 获取交接班录入的主数据信息
	 * 
	 * @param id
	 * @return
	 */
	@Override
	public CostTeamInfo getCostInputMainCostTeamInfo(String id) {
		CostTeamInfo result = new CostTeamInfo();
		result = dao.queryObjectById(CostTeamInfo.class, id);
		return result;
	}

	/**
	 * 传入一段时间，得到所有核算对象从开始时间到截止时间的方案切换情况，返回LinkedHashMap<核算对象ID,List<Vo>>
	 * 
	 * @param unitId 核算对象编码，如果为空，查询所有的
	 * @param kssj   开始日期+时间
	 * @param jzsj   截止日期+时间
	 * @return map key：核算对象编码 value，一段时间的方案切换数据
	 */
	@Override
	public LinkedHashMap<String, List<ProductScheduPlanStart>> getUnitProgByksrqjzrq(String unitId, String kssj,
			String jzsj) {
		LinkedHashMap<String, List<ProductScheduPlanStart>> result = new LinkedHashMap<String, List<ProductScheduPlanStart>>();
		Where whereKs = Where.create();
		if (unitId != null && unitId.length() > 0) {
			String[] unitArr = unitId.split(",");
			List<String> listUnitId = new ArrayList<String>();
			for(int i=0;i<unitArr.length;i++) {
				listUnitId.add(unitArr[i]);
			}
			whereKs.in(ProductScheduPlanStart::getUnitid, listUnitId.toArray());
		}
		whereKs.le(ProductScheduPlanStart::getStartdatetime, kssj);// 开始时间小于等于参数开始日期+时间
		whereKs.gt(ProductScheduPlanStart::getEnddatetime, kssj);// 结束时间大于参数开始日期+时间
		Order orderKs = Order.create();
		orderKs.orderByAsc(ProductScheduPlanStart::getUnitid);
		orderKs.orderByAsc(ProductScheduPlanStart::getStartdatetime);
		List<ProductScheduPlanStart> listKs = dao.queryData(ProductScheduPlanStart.class, whereKs, orderKs, null);
		LinkedHashMap<String, LinkedHashMap<String, ProductScheduPlanStart>> map = new LinkedHashMap<String, LinkedHashMap<String, ProductScheduPlanStart>>();
		if (StringUtils.isNotEmpty(listKs)) {
			for (ProductScheduPlanStart temp : listKs) {
				int ksDiff = 0;
				ksDiff = DateTimeUtils.bjDate(DateTimeUtils.parseDate(kssj),
						DateTimeUtils.parseDate(temp.getStartdatetime()));
				if (ksDiff > 0) {// 如果开始日期+时间参数大于数据的开始日期+时间，则数据的开始时间为开始日期+时间参数
					temp.setStartdatetime(kssj);
				}
				int jzDiff = 0;
				jzDiff = DateTimeUtils.bjDate(DateTimeUtils.parseDate(jzsj),
						DateTimeUtils.parseDate(temp.getEnddatetime()));
				if (jzDiff < 0) {// 如果结束日期+时间参数小于数据的开始日期+时间，则数据的结束时间为结束日期+时间参数
					temp.setEnddatetime(jzsj);
				}
				LinkedHashMap<String, ProductScheduPlanStart> mapTemp = new LinkedHashMap<String, ProductScheduPlanStart>();
				if (map != null && map.containsKey(temp.getUnitid())) {
					mapTemp = map.get(temp.getUnitid());
					if (mapTemp != null && mapTemp.containsKey(temp.getStartdatetime())) {

					} else {
						mapTemp.put(temp.getStartdatetime(), temp);
					}
				} else {
					mapTemp.put(temp.getStartdatetime(), temp);
				}
				map.put(temp.getUnitid(), mapTemp);
			}
		}
		Where whereJz = Where.create();
		if (unitId != null && unitId.length() > 0) {
			whereJz.eq(ProductScheduPlanStart::getUnitid, unitId);
		}
		whereJz.ge(ProductScheduPlanStart::getStartdatetime, kssj);// 开始时间大于等于参数开始日期+时间
		whereJz.lt(ProductScheduPlanStart::getStartdatetime, jzsj);// 开始时间小于参数结束日期+时间
		Order orderJz = Order.create();
		orderJz.orderByAsc(ProductScheduPlanStart::getUnitid);
		orderJz.orderByAsc(ProductScheduPlanStart::getStartdatetime);
		List<ProductScheduPlanStart> listJz = dao.queryData(ProductScheduPlanStart.class, whereJz, orderJz, null);
		if (StringUtils.isNotEmpty(listJz)) {
			for (ProductScheduPlanStart temp : listJz) {
				long ksDiff = 0;
				ksDiff = DateTimeUtils.diffSecond(DateTimeUtils.parseDate(kssj),
						DateTimeUtils.parseDate(temp.getStartdatetime()));
				if (ksDiff < 0) {// 如果开始日期+时间参数大于数据的开始日期+时间，则数据的开始时间为开始日期+时间参数
					temp.setStartdatetime(kssj);
				}
				int jzDiff = 0;
				jzDiff = DateTimeUtils.bjDate(DateTimeUtils.parseDate(temp.getEnddatetime()), DateTimeUtils.parseDate(jzsj));
//				jzDiff = DateTimeUtils.diffSecond(DateTimeUtils.parseDate(temp.getEnddatetime()),
//						DateTimeUtils.parseDate(jzsj));
				if (jzDiff > 0) {// 如果结束日期+时间参数小于数据的开始日期+时间，则数据的结束时间为结束日期+时间参数
					temp.setEnddatetime(jzsj);
				}
				LinkedHashMap<String, ProductScheduPlanStart> mapTemp = new LinkedHashMap<String, ProductScheduPlanStart>();
				if (map != null && map.containsKey(temp.getUnitid())) {
					mapTemp = map.get(temp.getUnitid());
					if (mapTemp != null && mapTemp.containsKey(temp.getStartdatetime())) {

					} else {
						mapTemp.put(temp.getStartdatetime(), temp);
					}
				} else {
					mapTemp.put(temp.getStartdatetime(), temp);
				}
				map.put(temp.getUnitid(), mapTemp);
			}
		}
		if (StringUtils.isNotEmpty(map)) {
			for (Entry<String, LinkedHashMap<String, ProductScheduPlanStart>> entry : map.entrySet()) {
				List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
				LinkedHashMap<String, ProductScheduPlanStart> mapTemp = entry.getValue();
				for (Entry<String, ProductScheduPlanStart> entryChild : mapTemp.entrySet()) {
					ProductScheduPlanStart obj = entryChild.getValue();
					list.add(obj);
				}
				List<ProductScheduPlanStart> listTemp = new ArrayList<ProductScheduPlanStart>();
				if (result != null && result.containsKey(entry.getKey())) {
					listTemp = result.get(entry.getKey());
				}
				listTemp.addAll(list);
				result.put(entry.getKey(), listTemp);
			}
		}
		return result;
	}
	/**
	 * 根据核算对象获取方案数据
	 * @param unitId 核算对象编码
	 * @return
	 */
	@Override
	public List<ProgramItem> getProgListByUnit(String unitId) {
		List<ProgramItem> result = new ArrayList<ProgramItem>();
		result = iProg.getProgramItemListByUnitid(unitId);
		return result;
	}
	
	/**
	 * 
	 * @param unitId 核算对象编码
	 * @param programid 方案编码
	 * @param startDatetime 开始时间
	 * @param id 类型 有值，但表修改数据，无值，代表新添加数据
	 * @return
	 */
	@Override
	public boolean saveCostStartDateTime(CostProgStartTimeDto dto) {
		String unitId = dto.getUnitId();
		String programid = dto.getProgramid();
		String startDatetime = dto.getStartDatetime();
		String dataId = dto.getDataId();
		String rowFlag = dto.getRowFlag();
		String programname = dto.getProgramname();
		if(dataId==null || dataId.length()<=0) {
			rowFlag = "1";
		}else {
			if(rowFlag==null || rowFlag.length()<=0) {
				rowFlag = "0";
			}
		}
		boolean result = false;
//		List<UnitRunStatus> listRun = new ArrayList<UnitRunStatus>();
//		Where whereRun = Where.create();
//		whereRun.eq(UnitRunStatus::getUnitid, unitId);
//		whereRun.eq(UnitRunStatus::getStartdatetime, startDatetime);
//		listRun = dao.queryData(UnitRunStatus.class, whereRun, null, null);
//		if (listRun != null && listRun.size() > 0) {
//			List<String> listProg = new ArrayList<String>();
//			listProg.add(programid);
//			HashMap<String, ProjectType> mapProgStatus = new HashMap<String, ProjectType>();
//			mapProgStatus = iProg.getProgramStateByIdList(listProg);
//			if (mapProgStatus != null && mapProgStatus.containsKey(programid)) {
//				ProjectType beanType = mapProgStatus.get(programid);
//				UnitRunStatus bean = listRun.get(0);
////				bean.setId(TMUID.getUID());
//				bean.setRunstatusid(beanType.getId());
//				bean.setRunstatusname(beanType.getDsname());
//				bean.setRuntypecolor(beanType.getTypecolor());
////				bean.setStartdatetime(startDatetime);
//				bean.setUnitid(unitId);
//				dao.update(bean);
//			}
//		} else {
//			Where whereRunGe = Where.create();
//			whereRunGe.eq(UnitRunStatus::getUnitid, unitId);
//			whereRunGe.gt(UnitRunStatus::getStartdatetime, startDatetime);
//			listRun = dao.queryData(UnitRunStatus.class, whereRunGe, null, null);
//			if (listRun != null && listRun.size() > 0) {
//				
//			}else {
//				List<String> listProg = new ArrayList<String>();
//				listProg.add(programid);
//				HashMap<String, ProjectType> mapProgStatus = new HashMap<String, ProjectType>();
//				mapProgStatus = iProg.getProgramStateByIdList(listProg);
//				if (mapProgStatus != null && mapProgStatus.containsKey(programid)) {
//					ProjectType beanType = mapProgStatus.get(programid);
//					UnitRunStatus bean = new UnitRunStatus();
//					bean.setId(TMUID.getUID());
//					bean.setRunstatusid(beanType.getId());
//					bean.setRunstatusname(beanType.getDsname());
//					bean.setRuntypecolor(beanType.getTypecolor());
//					bean.setStartdatetime(startDatetime);
//					bean.setUnitid(unitId);
//					dao.insert(bean);
//				}
//			}
//		}
		// 获取小于 开始日期+时间参数最大的 开始日期+时间
		Where whereOld = Where.create();
		whereOld.eq(ProductScheduPlanStart::getUnitid, unitId);
		whereOld.lt(ProductScheduPlanStart::getStartdatetime, startDatetime);
		if("0".equals(rowFlag)) {// 修改数据
			whereOld.ne(ProductScheduPlanStart::getId, dataId);
		}
		String startDatetimeOld = dao.findMaxValue(ProductScheduPlanStart.class,
				ProductScheduPlanStart::getStartdatetime, String.class, whereOld);

		// 获取 大于开始日期+时间参数最小的 开始日期+时间
		Where whereNew = Where.create();
		whereNew.eq(ProductScheduPlanStart::getUnitid, unitId);
		whereNew.gt(ProductScheduPlanStart::getStartdatetime, startDatetime);
		if("0".equals(rowFlag)) {// 修改数据
			whereNew.ne(ProductScheduPlanStart::getId, dataId);
		}
		String startDatetimeNew = dao.findMinValue(ProductScheduPlanStart.class,
				ProductScheduPlanStart::getStartdatetime, String.class, whereNew);

		List<ProductScheduPlanStart> listUp = new ArrayList<ProductScheduPlanStart>();
		List<ProductScheduPlanStart> listUpDel = new ArrayList<ProductScheduPlanStart>();
		List<ProductScheduPlanStart> listDel = new ArrayList<ProductScheduPlanStart>();
		// 上一条方案的数据
		ProductScheduPlanStart previousObj = new ProductScheduPlanStart();
		if (startDatetimeOld != null && startDatetimeOld.length() > 0) {// 有历史的开始数据
			Where where = Where.create();
			where.eq(ProductScheduPlanStart::getUnitid, unitId);
			where.eq(ProductScheduPlanStart::getStartdatetime, startDatetimeOld);
			List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
			list = dao.queryData(ProductScheduPlanStart.class, where, null, null);
			if (StringUtils.isNotEmpty(list) && list.size() > 0) {// 获取到了历史数据，将历史数据最大的开始日期数据的 结束日期字段 赋值为本次的开始日期+时间
				previousObj = list.get(0);
			}
		}else {
			previousObj = null;
		}
		ProductScheduPlanStart nextObj = new ProductScheduPlanStart();
		String endDatetime = "";
		if (startDatetimeNew != null && startDatetimeNew.length() > 0) {// 有 大于当前传递过来的 开始日期+时间参数的数据
			Where where = Where.create();
			where.eq(ProductScheduPlanStart::getUnitid, unitId);
			where.eq(ProductScheduPlanStart::getStartdatetime, startDatetimeNew);
			List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
			list = dao.queryData(ProductScheduPlanStart.class, where, null, null);
			if (StringUtils.isNotEmpty(list) && list.size() > 0) {// 本次开始的结束日期+时间 为 下一次的开始日期+时间
				nextObj = list.get(0);
				endDatetime = nextObj.getEnddatetime();
			} else {
				nextObj = null;
				endDatetime = DateTimeUtils
						.formatDateTime(DateTimeUtils.doYear(DateTimeUtils.parseDateTime(startDatetime), 20));
			}
		} else {// 没有大于当前传递过来的日期时间节点的数据
			nextObj = null;
			endDatetime = DateTimeUtils.formatDateTime(DateTimeUtils.doYear(DateTimeUtils.parseDateTime(startDatetime), 20));
		}
		List<ProductScheduPlanStart> listAdd = new ArrayList<ProductScheduPlanStart>();
		ProductScheduPlanStart sourceObj = new ProductScheduPlanStart();
		
		
		String delKsTime = "";
		String delJzTime = "";
		String oldStartTime = "";
		String oldProgId = "";
		String upStartTime = "";
		String upEndTime = "";
//		int type = 0;//0：未做修改，1:未修改时间，修改了方案，2：方案未发生变化，只是修改了时间，3：方案和时间都被修改
//		int changeType = 0;//0:未做修改，1：向后调整时间，2：向前调整时间
		if("0".equals(rowFlag)) {// 修改数据
			sourceObj = dao.queryObjectById(ProductScheduPlanStart.class, dataId);
			if(sourceObj!=null) {
				oldStartTime = sourceObj.getStartdatetime();
				oldProgId = sourceObj.getProgramid();
				
				long l_startTime = Long.parseLong(startDatetime.replaceAll(" ", "").replaceAll("-", "").replaceAll(":", ""));
				long l_oldStartTime = Long.parseLong(oldStartTime.replaceAll(" ", "").replaceAll("-", "").replaceAll(":", ""));
				long l_bjStart = l_startTime - l_oldStartTime;
				if(l_bjStart==0) {// 未修改开始时间
					if(oldProgId.equals(programid)) {// 方案也未发生变化，不做修改
						
					}else {// 方案发生了变化
						if(previousObj!=null) { // 有上一条记录
							if(previousObj.getProgramid().equals(programid)) {// 上一条的方案和修改的方案一至，删除本条记录
								listUpDel.add(sourceObj);
								if(nextObj!=null) {// 有下一条记录
									if(nextObj.getProgramid().equals(sourceObj.getProgramid())) {// 下一条的方案和修改的方案一至，删除下一条
										listUpDel.add(nextObj);
										previousObj.setEnddatetime(nextObj.getEnddatetime());// 上一条记录的结束时间更新为下一条的结束时间
									}else {// 下一条的方案和修改的方案不一至，上一条的结束时间为下一条的开始时间
										previousObj.setEnddatetime(nextObj.getStartdatetime());
									}
								}else {// 没有下一条记录,， 上一条的结束时间为修改记录的结束时间
									previousObj.setEnddatetime(sourceObj.getEnddatetime());
								}
								listUp.add(previousObj);// 只需要更新上一条就可以了，
							}else {// 上一条的方案和修改的方案不一至
								sourceObj.setProgramid(programid);
								sourceObj.setProgramname(programname);
		//						type = 1;
								upStartTime = startDatetime;
								upEndTime = sourceObj.getEnddatetime();
								if(nextObj!=null) {// 有下一条记录
									if(nextObj.getProgramid().equals(sourceObj.getProgramid())) {// 下一条的方案和修改的方案一样，删除下一条
										listUpDel.add(nextObj);
										sourceObj.setEnddatetime(nextObj.getEnddatetime());// 修改记录的结束时间更新为下一条的结束时间
									}else {// 下一条的方案和修改的方案不一样，修改记录的结束时间为下一条记录的开始时间
										sourceObj.setEnddatetime(nextObj.getStartdatetime());
									}
								}
								listUp.add(sourceObj);
							}
						}else {// 没有上一条记录
							sourceObj.setProgramid(programid);
							sourceObj.setProgramname(programname);
	//						type = 1;
							upStartTime = startDatetime;
							upEndTime = sourceObj.getEnddatetime();
							if(nextObj!=null) {// 有下一条记录
								if(nextObj.getProgramid().equals(sourceObj.getProgramid())) {// 下一条记录的方案和修改的方案一至，删除下一条方案
									listUpDel.add(nextObj);
									sourceObj.setEnddatetime(nextObj.getEnddatetime());// 修改记录的结束时间为下一条的结束时间
								}else {// 下一条记录的方案和修改的方案不一至，修改记录的结束时间为下一条的开始时间
									sourceObj.setEnddatetime(nextObj.getStartdatetime());
								}
							}
							listUp.add(sourceObj);
						}
					}
				}else if(l_bjStart>0) {// 开始时间向后调，删除原开始时间到修改后开始时间内的方案
//					changeType = 1;
					sourceObj.setStartdatetime(startDatetime);
					Where where = Where.create();
					where.gt(ProductScheduPlanStart::getStartdatetime, oldStartTime);
					where.lt(ProductScheduPlanStart::getStartdatetime, startDatetime);
					where.eq(ProductScheduPlanStart::getUnitid, unitId);
//					where.le(ProductScheduPlanStart::getEnddatetime, startDatetime);
					Order order = Order.create();
					order.orderByDesc(ProductScheduPlanStart::getStartdatetime);
					listUpDel = dao.queryData(ProductScheduPlanStart.class, where, null, null);
					if(StringUtils.isNotEmpty(listUpDel)) {// 获取到了需要覆盖的切换方案数据，则将覆盖的数据删除
//						ProductScheduPlanStart upDelObj = listUpDel.get(0);
						
						if(nextObj!=null) {// 如果有下一条记录
							if(nextObj.getProgramid().equals(sourceObj.getProgramid())) {// 下一条记录的方案和本次一至，删除下一条数据，本次的结束时间更新
								listUpDel.add(nextObj);
								sourceObj.setEnddatetime(nextObj.getEnddatetime());
							}else {// 和下一条方案不一至，本次的结束时间为下一条的开妈时间
								sourceObj.setEnddatetime(nextObj.getStartdatetime());
							}
						}else {// 没有下一条数据，结束日期按当前日期向后推20年
							String endDatetimeNew = DateTimeUtils.formatDateTime(DateTimeUtils.doYear(DateTimeUtils.parseDateTime(startDatetime), 20));
							sourceObj.setEnddatetime(endDatetimeNew);
						}
//						sourceObj.setEnddatetime(upDelObj.getEnddatetime());
					}
					listUp.add(sourceObj);
					upStartTime = oldStartTime;
					ProductScheduPlanStart pObj = new ProductScheduPlanStart();
					pObj = getPStartObj(unitId, oldStartTime);
					if(pObj!=null) {// 有上条数据，上一条的结束时间更新 为本次的开始时间
						pObj.setEnddatetime(startDatetime);
						listUp.add(pObj);
					}

					if(oldProgId.equals(programid)) {// 方案未发生变化，只是修改了时间
//						type = 2;
						ProductScheduPlanStart nObjOld = new ProductScheduPlanStart();
						nObjOld = getNStartObj(unitId, oldStartTime);
						if(nObjOld!=null && nextObj==null) {//判断历史的开始日期有没有下一条数据，
							upEndTime = DateTimeUtils.getNowDateTimeStr();
						}else {
							if(nextObj!=null) {// 新修改的开始日期有下一条数据
								if(nObjOld!=null) {// 修改前的开始日期有下一条数据
									if(nObjOld.getProgramid().equals(nextObj.getProgramid())) {
										upEndTime = startDatetime;
									}else {
										upEndTime = nextObj.getStartdatetime();
									}
								}else {
									upEndTime = nextObj.getStartdatetime();
								}
							}else {
								upEndTime = DateTimeUtils.getNowDateTimeStr();
							}
							
						}
						
					}else {
						sourceObj.setProgramid(programid);
						upEndTime = sourceObj.getEnddatetime();
//						type = 3;
					}
				}else {// 开始时间向前调
//					changeType = 2;
					sourceObj.setStartdatetime(startDatetime);
					Where where = Where.create();
					where.gt(ProductScheduPlanStart::getStartdatetime, startDatetime);
					where.lt(ProductScheduPlanStart::getStartdatetime, oldStartTime);
					where.eq(ProductScheduPlanStart::getUnitid, unitId);
//					where.le(ProductScheduPlanStart::getEnddatetime, startDatetime);
					Order order = Order.create();
					order.orderByDesc(ProductScheduPlanStart::getStartdatetime);
					listUpDel = dao.queryData(ProductScheduPlanStart.class, where, null, null);
					// 删除包含的方案切换数据
					if(StringUtils.isNotEmpty(listUpDel)) {
//						ProductScheduPlanStart upDelObj = listUpDel.get(0);
//						sourceObj.setEnddatetime(upDelObj.getEnddatetime());
//						listDel.addAll(listUpDel);
					}
					upStartTime = startDatetime;
					if(oldProgId.equals(programid)) {// 方案未发生变化，只是修改了时间
						upEndTime = oldStartTime;
//						type = 2;
					}else {
						upEndTime = sourceObj.getEnddatetime();
						sourceObj.setProgramid(programid);
//						type = 3;
					}
					
					if(previousObj!=null) {// 有上一条数据
						
						if(previousObj.getProgramid().equals(programid)) {// 如果上一条的方案和修改的方案一至，删除本条数据，上一条的结束时间更新为本次修改的结束时间
							previousObj.setEnddatetime(sourceObj.getEnddatetime());
							listDel.add(sourceObj);
							listUp.add(previousObj);
						}else {// 和上一条方案不一至，上一条数据的结束时间为本次开始时间
							previousObj.setEnddatetime(startDatetime);
							sourceObj.setStartdatetime(startDatetime);
							listUp.add(sourceObj);
							listUp.add(previousObj);
						}
					}else {
						sourceObj.setStartdatetime(startDatetime);
						listUp.add(sourceObj);
					}
				}
			}
		}else if("1".equals(rowFlag)){// 添加数据
			sourceObj.setStartdatetime(startDatetime);
			sourceObj.setEnddatetime(endDatetime);
			sourceObj.setProgramid(programid);
			sourceObj.setProgramname(programname);
			sourceObj.setUnitid(unitId);
			sourceObj.setId(TMUID.getUID());
			String sourceProgId = sourceObj.getProgramid();
			String previousProgId = "";// 上一条记录的方案编码
			String nextProgId = "";// 下一条记录的方案编码
			if(nextObj==null && previousObj==null) {// 没有上一条，也没有下一条
				listAdd.add(sourceObj);
			}else {
				if(previousObj!=null) {//有上一条数据
					previousProgId = previousObj.getProgramid();
					if(previousProgId.equals(programid)) {// 上一条数据的方案和添加的方案一至，不进行添加操作
						
					}else {// 上一条数据的方案和添加的方案不一至
						previousObj.setEnddatetime(startDatetime);//上一条数据的结束日期为添加记录的开始时间
						listUp.add(previousObj);
						if(nextObj!=null) { // 有下一条数据
							nextProgId = nextObj.getProgramid();
							if(nextProgId.equals(programid)) {// 下一条数据的方案和添加记录的方案一至，不进行添加，将下一条数据的开始时间设置为添加的时间
								nextObj.setStartdatetime(startDatetime);
								listUp.add(nextObj);
							}else {// 下一条数据的方案和添加记录的方案不一至
								sourceObj.setEnddatetime(nextObj.getStartdatetime());// 添加记录的方案结束时间更新为下一条记录的开始时间
								listAdd.add(sourceObj);
							}
						}else {// 没有下一条记录
							listAdd.add(sourceObj);
						}
					}
//					
				}else {// 没有上一条记录
					if(nextObj!=null) { // 有下一条数据
						nextProgId = nextObj.getProgramid();
						if(nextProgId.equals(programid)) {// 下一条数据的方案和添加记录的方案一至，不进行添加，将下一条数据的开始时间设置为添加的时间
							nextObj.setStartdatetime(startDatetime);
							listUp.add(nextObj);
						}else {// 下一条数据的方案和添加记录的方案不一至
							sourceObj.setEnddatetime(nextObj.getStartdatetime());// 添加记录的方案结束时间更新为下一条记录的开始时间
							listAdd.add(sourceObj);
						}
					}else {// 没有下一条记录
						listAdd.add(sourceObj);
					}
				}
//				if(previousObj!=null && nextObj!=null) {// 同时拥用上一条和下一条
//					previousProgId = previousObj.getProgramid();
//					nextProgId = nextObj.getProgramid();
//					
//					if(!sourceProgId.equals(previousProgId) && !nextProgId.equals(sourceProgId)) {// 本次的方案和上一次，下一次都不一样
//						previousObj.setEnddatetime(startDatetime);// 将上一条记录的结束日期赋值为本次的结束日期
//						listUp.add(previousObj);
////						nextObj.setStartdatetime(endDatetime);// 将下一条记录的开始日期赋值为本次的开始日期
////						listUp.add(nextObj);
//						sourceObj.setEnddatetime(nextObj.getStartdatetime());
//						listAdd.add(sourceObj);
//					}else {
//						if(sourceProgId.equals(previousProgId)) {// 上一条记录的方案和本次的方案一样，将上一条记录的结束日期赋值为本次的结束日期
////							previousObj.setEnddatetime(endDatetime);// 将上一条记录的结束日期赋值为本次的结束日期
////							listUp.add(previousObj);
////							nextObj.setStartdatetime(endDatetime);// 将下一条记录的开始日期赋值为本次的开始日期
////							listUp.add(nextObj);
//						}else {// 上一条记录的方案和本次的方案 不一样
//							previousObj.setEnddatetime(startDatetime);// 将上一条记录的结束日期赋值为本次的开始日期
//							listUp.add(previousObj);
//							if(nextProgId.equals(sourceProgId)) {// 下一条的方案和本次的方案一样，将下一条记录的开始日期赋值为本次的开始日期
//								nextObj.setStartdatetime(startDatetime);// 将下一条记录的开始日期赋值为本次的开始日期
//								listUp.add(nextObj);
//							}else {
//								sourceObj.setEnddatetime(nextObj.getStartdatetime());
//								listAdd.add(sourceObj);
//							}
//						}
//					}
//				}else {
//					if(previousObj!=null) {// 有上一条数据，没有下一条数据
//						previousProgId = previousObj.getProgramid();
//						if(sourceProgId.equals(previousProgId)) {// 上一条记录的方案和本次的方案一样，不添加，将上一条记录的结束日期赋值为本次的结束日期
//							previousObj.setEnddatetime(endDatetime);// 将上一条记录的结束日期赋值为本次的结束日期
//							listUp.add(previousObj);
//						}else {// 上一条记录的方案和本次的方案 不一样
//							previousObj.setEnddatetime(startDatetime);// 将上一条记录的结束日期赋值为本次的开始日期
//							listUp.add(previousObj);
//							listAdd.add(sourceObj);
//						}
//					}else {// 没有上一条数据，有下一条数据
//						nextProgId = nextObj.getProgramid();
//						if(nextProgId.equals(sourceProgId)) {// 下一条的方案和本次的方案一样，不添加，将下一条记录的开始日期赋值为本次的开始日期
//							nextObj.setStartdatetime(startDatetime);// 将下一条记录的开始日期赋值为本次的开始日期
//							listUp.add(nextObj);
//						}else {
////							nextObj.setStartdatetime(endDatetime);// 将下一条记录的开始日期赋值为本次的开始日期
////							listUp.add(nextObj);
//							sourceObj.setEnddatetime(nextObj.getStartdatetime());
//							listAdd.add(sourceObj);
//						}
//					}
//				}
			}
		}else {// 删除数据
			sourceObj = dao.queryObjectById(ProductScheduPlanStart.class, dataId);
			if(sourceObj!=null) {
				delKsTime = sourceObj.getStartdatetime();
				delJzTime = sourceObj.getEnddatetime();
				listDel.add(sourceObj);// 删除数据
				if(nextObj==null && previousObj==null) {// 没有上一条，也没有下一条
					
				}else {
					if(previousObj!=null) {// 有上条数据
						String previousProgId = previousObj.getProgramid();
						if(nextObj!=null) {// 有下一条数据
							String nextProgId = nextObj.getProgramid();
							if(nextProgId.equals(previousProgId)) {// 上一条数据的方案和下一条数据的方案一至，删除下一条方案
								previousObj.setEnddatetime(nextObj.getEnddatetime());// 上一条方案的结束时间更新为下一条方案的结束时间
								listDel.add(nextObj);
							}else {// 上一条数据的方案和下一条数据的方案不一至
								previousObj.setEnddatetime(nextObj.getStartdatetime());// 上一条方案的结束时间更新为下一条方案的开始时间
							}
						}else {// 没有下一条方案，上一条方案的结束时间更新为删除方案的结束时间
							previousObj.setEnddatetime(sourceObj.getEnddatetime());//
						}
						listUp.add(previousObj);
					}
//					if(previousObj!=null && nextObj!=null) {// 同时拥用上一条和下一条
//						String previousProgId = previousObj.getProgramid();
//						String nextProgId = nextObj.getProgramid();
//						if(previousProgId.equals(nextProgId)) {// 上一条的方案和下一条的方案一样，那么删除下一条的方案，将上一条数据的日期赋值为下一条的结束日期
//							previousObj.setEnddatetime(nextObj.getEnddatetime());
//							listUp.add(previousObj);
//							listDel.add(nextObj);
//						}else {// 上一条的方案和下一条的方案不一样，上一条数据的结束日期赋值为下一条的开始日期
//							previousObj.setEnddatetime(nextObj.getStartdatetime());
//							listUp.add(previousObj);
//						}
//					}else {
//						if(previousObj!=null) {// 有上一条数据，没有下一条数据，上一条数据的结束日期为当前日期向后推算20年
//							String endTime = DateTimeUtils
//									.formatDateTime(DateTimeUtils.doYear(DateTimeUtils.parseDateTime(DateTimeUtils.getNowDateTimeStr()
//											), 20));
//							previousObj.setEnddatetime(endTime);// 上一条数据的结束日期为当前日期向后推算20年
//							listUp.add(previousObj);
//						}else {// 没有上一条数据，有下一条数据， 不处理
//							
//						}
//					}
				}
			}
		}
		boolean blnUp = false;
		if (StringUtils.isNotEmpty(listUp)) {
			int row = dao.updateBatch(listUp);
			if (row > 0) {
				if(StringUtils.isNotEmpty(listUpDel)) {
					dao.deleteByIdBatch(listUpDel);
				}
				blnUp = true;
			}
		} else {
			blnUp = true;
		}
		boolean blnAdd = false;
		if (StringUtils.isNotEmpty(listAdd)) {
			int row = dao.insertBatch(listAdd);
			if (row > 0) {
				blnAdd = true;
			}
		} else {
			blnAdd = true;
		}
		
		boolean blnDel = false;
		if (StringUtils.isNotEmpty(listDel)) {
			int row = dao.deleteByIdBatch(listDel);
			if (row > 0) {
				blnDel = true;
			}
		} else {
			blnDel = true;
		}
		
		List<ProductUnitProgShift> listResult = new ArrayList<ProductUnitProgShift>();
		if (blnAdd && blnUp && blnDel) {
			Where whereMax = Where.create();
			whereMax.eq(ProductScheduPlanStart::getUnitid, unitId);
			Order orderMax = Order.create();
			orderMax.orderByDesc(ProductScheduPlanStart::getStartdatetime);
			List<ProductScheduPlanStart> listMax = dao.queryData(ProductScheduPlanStart.class, whereMax, orderMax, null);
			if(StringUtils.isNotEmpty(listMax)) {
				ProductScheduPlanStart objMax = listMax.get(0);
				String startTime = objMax.getStartdatetime();
				String endTime = DateTimeUtils
						.formatDateTime(DateTimeUtils.doYear(DateTimeUtils.parseDateTime(startTime), 20));
				String oldendtime = objMax.getEnddatetime();
				if(oldendtime.contentEquals(endTime)) {
					
				}else {
					objMax.setEnddatetime(endTime);
					List<ProductScheduPlanStart> listMaxUp = new ArrayList<ProductScheduPlanStart>();
					listMaxUp.add(objMax);
					dao.updateBatch(listMaxUp);
				}
			}
//			List<UnitRunStatus> listRun = new ArrayList<UnitRunStatus>();
			Where whereMaxStart = Where.create();
			whereMaxStart.eq(ProductScheduPlanStart::getUnitid, unitId);
			
			String startMax = dao.findMaxValue(ProductScheduPlanStart.class, ProductScheduPlanStart::getStartdatetime, String.class, whereMaxStart);
			if(startMax!=null && startMax.length()>0) {
				Where whereStart = Where.create();
				whereStart.eq(ProductScheduPlanStart::getUnitid, unitId);
				whereStart.eq(ProductScheduPlanStart::getStartdatetime, startMax);
				List<ProductScheduPlanStart> listStart = dao.queryData(ProductScheduPlanStart.class, whereStart, null, null);
				if(StringUtils.isNotEmpty(listStart)) {
					Where whereRun = Where.create();
					whereRun.eq(UnitRunStatus::getUnitid, unitId);
					List<UnitRunStatus> listRun = new ArrayList<UnitRunStatus>();
					listRun = dao.queryData(UnitRunStatus.class, whereRun, null, null);
					if(listRun!=null && listRun.size()>0) {
						long rows = dao.deleteByIdBatch(listRun);
						if(rows>0) {
							ProductScheduPlanStart objStart = listStart.get(0);
							List<String> listProg = new ArrayList<String>();
							listProg.add(objStart.getProgramid());
							HashMap<String, ProjectType> mapProgStatus = new HashMap<String, ProjectType>();
							mapProgStatus = iProg.getProgramStateByIdList(listProg);
							if (mapProgStatus != null && mapProgStatus.containsKey(objStart.getProgramid())) {
								ProjectType beanType = mapProgStatus.get(objStart.getProgramid());
								UnitRunStatus bean = new UnitRunStatus();
								bean.setId(TMUID.getUID());
								bean.setRunstatusid(beanType.getId());
								bean.setRunstatusname(beanType.getDsname());
								bean.setRuntypecolor(beanType.getTypecolor());
								bean.setStartdatetime(startDatetime);
								bean.setUnitid(unitId);
								dao.insert(bean);
							}
						}
					}
				}else {
					Where whereRun = Where.create();
					whereRun.eq(UnitRunStatus::getUnitid, unitId);
					List<UnitRunStatus> listRun = new ArrayList<UnitRunStatus>();
					listRun = dao.queryData(UnitRunStatus.class, whereRun, null, null);
					dao.deleteByIdBatch(listRun);
				}
//					Where whereRunMax = Where.create();
//					whereRunMax.eq(UnitRunStatus::getUnitid, unitId);
//					String runMax = dao.findMaxValue(UnitRunStatus.class, UnitRunStatus::getStartdatetime, String.class, whereRunMax);
//					if(runMax==null || runMax.length()<=0) {
//						List<String> listProg = new ArrayList<String>();
//						listProg.add(programid);
//						HashMap<String, ProjectType> mapProgStatus = new HashMap<String, ProjectType>();
//						mapProgStatus = iProg.getProgramStateByIdList(listProg);
//						if (mapProgStatus != null && mapProgStatus.containsKey(programid)) {
//							ProjectType beanType = mapProgStatus.get(programid);
//							UnitRunStatus bean = new UnitRunStatus();
//							bean.setId(TMUID.getUID());
//							bean.setRunstatusid(beanType.getId());
//							bean.setRunstatusname(beanType.getDsname());
//							bean.setRuntypecolor(beanType.getTypecolor());
//							bean.setStartdatetime(startDatetime);
//							bean.setUnitid(unitId);
//							dao.insert(bean);
//						}
//					}else {
//						List<UnitRunStatus> listRunMax = new ArrayList<UnitRunStatus>();
//						Where whereRun = Where.create();
//						whereRun.eq(UnitRunStatus::getUnitid, unitId);
//						whereRun.eq(UnitRunStatus::getStartdatetime, runMax);
//						listRunMax = dao.queryData(UnitRunStatus.class, whereRun, null, null);
//						if(StringUtils.isNotEmpty(listRunMax)) {
//							List<String> listProg = new ArrayList<String>();
//							listProg.add(programid);
//							HashMap<String, ProjectType> mapProgStatus = new HashMap<String, ProjectType>();
//							mapProgStatus = iProg.getProgramStateByIdList(listProg);
//							if (mapProgStatus != null && mapProgStatus.containsKey(programid)) {
//								ProjectType beanType = mapProgStatus.get(programid);
//								UnitRunStatus objRun = listRunMax.get(0);
//								if(beanType.getId().equals(objRun.getRunstatusid())) {
//									
//								}else {
//									objRun.setRunstatusid(beanType.getId());
//									objRun.setRunstatusname(beanType.getDsname());
//									objRun.setRuntypecolor(beanType.getTypecolor());
//									objRun.setStartdatetime(startDatetime);
//									dao.update(objRun);
//								}
//							}
//						}else {
//							List<String> listProg = new ArrayList<String>();
//							listProg.add(programid);
//							HashMap<String, ProjectType> mapProgStatus = new HashMap<String, ProjectType>();
//							mapProgStatus = iProg.getProgramStateByIdList(listProg);
//							if (mapProgStatus != null && mapProgStatus.containsKey(programid)) {
//								ProjectType beanType = mapProgStatus.get(programid);
//								UnitRunStatus bean = new UnitRunStatus();
//								bean.setId(TMUID.getUID());
//								bean.setRunstatusid(beanType.getId());
//								bean.setRunstatusname(beanType.getDsname());
//								bean.setRuntypecolor(beanType.getTypecolor());
//								bean.setStartdatetime(startDatetime);
//								bean.setUnitid(unitId);
//								dao.insert(bean);
//							}
//						}
//					}
//					
//				}
			}else {
//				List<String> listProg = new ArrayList<String>();
//				listProg.add(programid);
//				HashMap<String, ProjectType> mapProgStatus = new HashMap<String, ProjectType>();
//				mapProgStatus = iProg.getProgramStateByIdList(listProg);
//				if (mapProgStatus != null && mapProgStatus.containsKey(programid)) {
//					ProjectType beanType = mapProgStatus.get(programid);
//					UnitRunStatus bean = new UnitRunStatus();
//					bean.setId(TMUID.getUID());
//					bean.setRunstatusid(beanType.getId());
//					bean.setRunstatusname(beanType.getDsname());
//					bean.setRuntypecolor(beanType.getTypecolor());
//					bean.setStartdatetime(startDatetime);
//					bean.setUnitid(unitId);
//					dao.insert(bean);
//				}
			}

			String key = "COST:ProgChange";
			Map<String, String> mapRedisChange = new LinkedHashMap<String, String>();
			mapRedisChange = redis.getMap(key);
			if(mapRedisChange!=null) {
				if(mapRedisChange.containsKey(unitId)) {
					
				}else {
					mapRedisChange.put(unitId, "正在进行方案切换操作");
				}
			}else {
				mapRedisChange = new LinkedHashMap<String, String>();
				mapRedisChange.put(unitId, "正在进行方案切换操作");
			}
			redis.setMap(key, mapRedisChange);
			try {
				if("1".equals(rowFlag)) {
					int times = DateTimeUtils.bjDate(DateTimeUtils.parseDate(startDatetime),
							DateTimeUtils.parseDate(DateTimeUtils.getNowDateTimeStr()));
					if (times > 0) {// 方案的切换时间大于当前时间为预设置
					}else {
						listResult =  getProgChangeDataByAdd(unitId, startDatetime);
					}
				}else if("-1".equals(rowFlag)) {
					int ksDiff = 0;
					ksDiff = DateTimeUtils.bjDate(DateTimeUtils.parseDate(delJzTime),
							DateTimeUtils.parseDate(DateTimeUtils.getNowDateTimeStr()));
					if(ksDiff>0) {
						delJzTime = DateTimeUtils.getNowDateTimeStr();
					}
					listResult = getProgChangeDataByDel(unitId, delKsTime, delJzTime);
				}else {
					int ksDiff = 0;
					ksDiff = DateTimeUtils.bjDate(DateTimeUtils.parseDate(upEndTime),
							DateTimeUtils.parseDate(DateTimeUtils.getNowDateTimeStr()));
					if(ksDiff>0) {
						upEndTime = DateTimeUtils.getNowDateTimeStr();
					}
					listResult = getProgChangeDataByEdit(unitId, upStartTime, upEndTime);
				}
				if(StringUtils.isNotEmpty(listResult)) {
					if(dto.getIsCollect()==null || dto.getIsCollect()==1) {// 需要重新采集数据
						steadyRateServ.reCalcShiftSteadyRate(null, listResult);
						this.iascl.reCalcShift(listResult);
					}
				}
			} catch (Exception e) {
				log.error("", e);
			}
			mapRedisChange.clear();
			mapRedisChange = redis.getMap(key);
			if(mapRedisChange!=null && mapRedisChange.containsKey(unitId)) {
				mapRedisChange.remove(unitId);
				redis.delete(key);
				if(mapRedisChange!=null && mapRedisChange.size()>0) {
					redis.setMap(key, mapRedisChange);
				}
			}
			result = true;
		} else {
			result = false;
		}
		return result;
	}
	/**
	 * 按开始和结束时间获取核算对象方案切换数据
	 * @param dto 传入参数，核算对象编码，可以为空，开始日期+时间 ，结束日期+时间 标准格式
	 * @return
	 */
	
	@Override
	public JSONObject getCostProgChangeData(ZzRunStateDto dto) {
		JSONObject result = new JSONObject();// 数据
		
		SysUser user = SysUserHolder.getCurrentUser();
		// 获取人员
		
		LinkedHashMap<String, List<ProductScheduPlanStart>> map = new LinkedHashMap<String, List<ProductScheduPlanStart>>();
		String unitId = dto.getUnitId();
		if("all".equals(unitId)) {
			unitId = "";
		}
		List<String> listUnitIdAcc = new ArrayList<String>();
		List<String> listUnitId = new ArrayList<String>();
		LinkedHashMap<String, Costuint> mapUnit = new LinkedHashMap<String, Costuint>();
		LinkedHashMap<String, Costuint> mapUnitAll = new LinkedHashMap<String, Costuint>();
		List<Costuint> listCostUnit = new ArrayList<Costuint>();
		List<Costuint> listCostUnitTemp = new ArrayList<Costuint>();
		if(unitId!=null && unitId.length()>0) {
//			String[] unitArr = unitId.split(",");
			CostuintQueryDto unitDto = new CostuintQueryDto();
			List<Costuint> listCostUnitAll = costUnit.getDatas(unitDto);
			if(StringUtils.isNotEmpty(listCostUnitAll)) {
				for(Costuint temp : listCostUnitAll) {
					Integer ishd=temp.getProductive();//屏蔽活动
					if (ishd==null) {
						ishd=0;
					}
					if (ishd==1) {
						continue;
					}
					if(temp.getLedgerEntry()==null || temp.getLedgerEntry()==1) {
						mapUnitAll.put(temp.getId(), temp);
						listUnitIdAcc.add(temp.getId()+"_"+temp.getUnittype()+"_2");
						listUnitId.add(temp.getId());
					}
				}
			}
//			for(int i=0;i<unitArr.length;i++) {
//				String unitIdData = unitArr[i];
//				Costuint bean = new Costuint();
//				bean.setId(unitIdData);
//				listCostUnit.add(bean);
//			}
		}else {
			listCostUnitTemp = costService.getCostuintListByOrgId(user.getOrgId(), 2);
			if(StringUtils.isNotEmpty(listCostUnitTemp)) {
				for(Costuint temp : listCostUnitTemp) {
					Integer ishd=temp.getProductive();//屏蔽活动
					if (ishd==null) {
						ishd=0;
					}
					if (ishd==1) {
						continue;
					}
					if(temp.getLedgerEntry()==null || temp.getLedgerEntry()==1) {
						listCostUnit.add(temp);
						mapUnit.put(temp.getId(), temp);
						listUnitIdAcc.add(temp.getId()+"_"+temp.getUnittype()+"_2");
						listUnitId.add(temp.getId());
					}
				}
			}
		}
		// 调用张晋铜设备接口同步到方案切换表中        Start *********************************************************
		
		autoChangeAccInputToStart(listUnitIdAcc, listUnitId, dto.getStartDt(), dto.getEndDt());
				
		// 调用张晋铜设备接口同步到方案切换表中        END *********************************************************
		map = getUnitProgByksrqjzrq(unitId, dto.getStartDt(), dto.getEndDt());
		JSONArray array = new JSONArray();
		if(unitId!=null && unitId.length()>0) {
			String[] unitArr = unitId.split(",");
			for(int i=0;i<unitArr.length;i++) {
				String unitIdData = unitArr[i];
				if(mapUnitAll!=null && mapUnitAll.containsKey(unitIdData)) {
					String unitName = mapUnitAll.get(unitIdData).getName();
					JSONObject jobjdata = new JSONObject();// 表头
					List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
					if(map!=null && map.containsKey(unitIdData)) {
						list = map.get(unitIdData);
						if(StringUtils.isNotEmpty(list)) {
							List<String> listProgId = new ArrayList<String>();
							List<ProgramItem> listProg = iProg.getProgramItemListByUnitid(unitIdData);
							LinkedHashMap<String, ProgramItem> mapProg = new LinkedHashMap<String, ProgramItem>();
							if(StringUtils.isNotEmpty(listProg)) {
								for(ProgramItem tempProg : listProg) {
									listProgId.add(tempProg.getId());
									mapProg.put(tempProg.getId(), tempProg);
								}
							}
							HashMap<String, ProjectType> mapProgStatus = new HashMap<String, ProjectType>();
							if(StringUtils.isNotEmpty(listProgId)) {
								mapProgStatus = iProg.getProgramStateByIdList(listProgId);
							}
							for(ProductScheduPlanStart tempStart : list) {
								if(mapProgStatus!=null && mapProgStatus.containsKey(tempStart.getProgramid())) {
									String runtypecolor = mapProgStatus.get(tempStart.getProgramid()).getTypecolor();
									tempStart.setRuntypecolor(runtypecolor);
								}
								if(mapProg!=null && mapProg.containsKey(tempStart.getProgramid())) {
									String programname = mapProg.get(tempStart.getProgramid()).getPiName();
									tempStart.setProgramname(programname);
								}
							}
						}
						jobjdata.put("costId", unitIdData);
						jobjdata.put("costName", unitName);
						jobjdata.put("costProgData", list);
						array.add(jobjdata);
					}else {
						jobjdata.put("costId", unitIdData);
						jobjdata.put("costName", unitName);
						jobjdata.put("costProgData", list);
						array.add(jobjdata);
					}
				}
			}
		}else {
			if(StringUtils.isNotEmpty(listCostUnit)) {
				for(Costuint temp : listCostUnit) {
					JSONObject jobjdata = new JSONObject();// 表头
					List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
					if(map!=null && map.containsKey(temp.getId())) {
						list = map.get(temp.getId());
						if(StringUtils.isNotEmpty(list)) {
							List<String> listProgId = new ArrayList<String>();
							List<ProgramItem> listProg = iProg.getProgramItemListByUnitid(temp.getId());
							LinkedHashMap<String, ProgramItem> mapProg = new LinkedHashMap<String, ProgramItem>();
							if(StringUtils.isNotEmpty(listProg)) {
								for(ProgramItem tempProg : listProg) {
									listProgId.add(tempProg.getId());
									mapProg.put(tempProg.getId(), tempProg);
								}
							}
							HashMap<String, ProjectType> mapProgStatus = new HashMap<String, ProjectType>();
							if(StringUtils.isNotEmpty(listProgId)) {
								mapProgStatus = iProg.getProgramStateByIdList(listProgId);
							}
							for(ProductScheduPlanStart tempStart : list) {
								if(mapProgStatus!=null && mapProgStatus.containsKey(tempStart.getProgramid())) {
									String runtypecolor = mapProgStatus.get(tempStart.getProgramid()).getTypecolor();
									tempStart.setRuntypecolor(runtypecolor);
								}
								if(mapProg!=null && mapProg.containsKey(tempStart.getProgramid())) {
									String programname = mapProg.get(tempStart.getProgramid()).getPiName();
									tempStart.setProgramname(programname);
								}
							}
						}
						jobjdata.put("costId", temp.getId());
						jobjdata.put("costName", temp.getName());
						jobjdata.put("costProgData", list);
						array.add(jobjdata);
					}else {
						jobjdata.put("costId", temp.getId());
						jobjdata.put("costName", temp.getName());
						jobjdata.put("costProgData", list);
						array.add(jobjdata);
					}
				}
			}
		}
		if(array!=null && array.size()>0) {
			result.put("data", array);// 填充数据
		}
		return result;
	}
	
	/**
	 * 传入一个核算对象ID加一个时间点，接口返回这个时间点的方案
	 * @param unitId 核算对象编码
	 * @param dt 日期+时间
	 * @return key 核算对象编码 value 方案切换的数据
	 */
	@Override
	public LinkedHashMap<String, ProductScheduPlanStart> getCostProgDataByUnitDateTime(String unitId, String dt) {
		LinkedHashMap<String, ProductScheduPlanStart> result = new LinkedHashMap<String, ProductScheduPlanStart>();
		if(dt==null || dt.length()<=0) {// 如果日期时间参数为空，则默认为当前时间
			dt = DateTimeUtils.getNowDateTimeStr();
		}
		Where where = Where.create();
		if(unitId==null || unitId.length()<=0) {// 如果核算对象编码参数有值，按核算对象查询数据
		}else {
			where.eq(ProductScheduPlanStart::getUnitid, unitId);
		}
		where.le(ProductScheduPlanStart::getStartdatetime, dt);
		where.ge(ProductScheduPlanStart::getEnddatetime, dt);
		List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
		list = dao.queryData(ProductScheduPlanStart.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			for(ProductScheduPlanStart temp : list) {
				if(result!=null && result.containsKey(temp.getUnitid())) {// 一个核算对象在一个时间上，只能有一条数据
					
				}else {
					result.put(temp.getUnitid(), temp);
				}
			}
		}
		return result;
	}
	
	/**
	 * 添加方案切换数据
	 * @param unitId
	 * @param dt
	 */
	
	private List<ProductUnitProgShift> getProgChangeDataByAdd(String unitId, String dt) {
		List<ProductUnitProgShift> result = new ArrayList<ProductUnitProgShift>();
		String nowDt = DateTimeUtils.getNowDateTimeStr();
		Map<String, Object> mapOrg = costUnit.getUinToperator(unitId);
		Object objOrg = new ArrayList<SysOrg>();
		if(mapOrg!=null && mapOrg.containsKey("sysOrg")) {
			objOrg = mapOrg.get("sysOrg");
			if(objOrg!=null) {
				
				List<String> listOrg = new ArrayList<String>();
				for(SysOrg temp : (List<SysOrg>) objOrg) {
					listOrg.add(temp.getId());
				}
				String startSbsj = dt;
				ShiftForeignVo shiftSb =  shift.getShiftByOrgListDateTime(listOrg, dt);
				if(shiftSb!=null) {
					startSbsj = shiftSb.getSbsj();
				}
				String endXbsj = "";
				// 下一个方案
				ProductScheduPlanStart nextObj = new ProductScheduPlanStart();
				nextObj = getNStartObj(unitId, dt);
				if(nextObj!=null) {// 有下一个方案，下班时间为下一个方案的开始时间
					endXbsj = nextObj.getStartdatetime();
				}else {
					endXbsj = nowDt;
				}
				ShiftForeignVo shiftXb =  shift.getShiftByOrgListDateTime(listOrg, endXbsj);
				if(shiftXb!=null) {
					endXbsj = shiftXb.getXbsj();
				}
//				List<ShiftForeignVo> listShift = shift.getShiftByDateTime(listOrg, startSbsj, endXbsj);
//				Where whereStart = Where.create();
//				whereStart.eq(ProductScheduPlanStart::getUnitid, unitId);
//				whereStart.le(ProductScheduPlanStart::getStartdatetime, dt);
//				whereStart.ge(ProductScheduPlanStart::getEnddatetime, dt);
//				whereStart.lt(ProductScheduPlanStart::getStartdatetime, endXbsj);
//				Order orderStart = Order.create();
//				orderStart.orderByAsc(ProductScheduPlanStart::getStartdatetime);
//				List<ProductScheduPlanStart> listStart = new ArrayList<ProductScheduPlanStart>();
//				listStart = dao.queryData(ProductScheduPlanStart.class, whereStart, orderStart, null);
				result = createUnitProgStart(unitId, listOrg, startSbsj, endXbsj);
				
			}
		}
		return result;
	}
	
	
	
	/**
	 * 删除方案切换数据
	 * @param unitId
	 * @param ksrq
	 * @param jzrq
	 */
	private List<ProductUnitProgShift> getProgChangeDataByDel(String unitId, String ksrq, String jzrq) {
		List<ProductUnitProgShift> result = new ArrayList<ProductUnitProgShift>();
		String nowDt = DateTimeUtils.getNowDateTimeStr();
		Map<String, Object> mapOrg = costUnit.getUinToperator(unitId);
		Object objOrg = new ArrayList<SysOrg>();
		if(mapOrg!=null && mapOrg.containsKey("sysOrg")) {
			objOrg = mapOrg.get("sysOrg");
			if(objOrg!=null) {
				
				List<String> listOrg = new ArrayList<String>();
				for(SysOrg temp : (List<SysOrg>) objOrg) {
					listOrg.add(temp.getId());
				}
				String startSbsj = ksrq;
				ShiftForeignVo shiftSb =  shift.getShiftByOrgListDateTime(listOrg, ksrq);
				if(shiftSb!=null) {
					startSbsj = shiftSb.getSbsj();
				}
				String endXbsj = "";
				// 下一个方案
				ProductScheduPlanStart nextObj = new ProductScheduPlanStart();
				nextObj = getNStartObj(unitId, ksrq);
				if(nextObj!=null) {// 有下一个方案，下班时间为下一个方案的开始时间
					endXbsj = nextObj.getStartdatetime();
				}else {
					endXbsj = nowDt;
				}
				ShiftForeignVo shiftXb =  shift.getShiftByOrgListDateTime(listOrg, endXbsj);
				if(shiftXb!=null) {
					endXbsj = shiftXb.getXbsj();
				}
//				List<ShiftForeignVo> listShift = shift.getShiftByDateTime(listOrg, startSbsj, endXbsj);
//				Where whereStart = Where.create();
//				whereStart.eq(ProductScheduPlanStart::getUnitid, unitId);
//				whereStart.le(ProductScheduPlanStart::getStartdatetime, ksrq);
//				whereStart.ge(ProductScheduPlanStart::getEnddatetime, ksrq);
//				whereStart.lt(ProductScheduPlanStart::getStartdatetime, endXbsj);
//				Order orderStart = Order.create();
//				orderStart.orderByAsc(ProductScheduPlanStart::getStartdatetime);
//				List<ProductScheduPlanStart> listStart = new ArrayList<ProductScheduPlanStart>();
//				listStart = dao.queryData(ProductScheduPlanStart.class, whereStart, orderStart, null);
//				result = createUnitProgStart(unitId, listShift, listStart);
				result = createUnitProgStart(unitId, listOrg, startSbsj, endXbsj);
				
			}
		}
		return result;
	
		
	}
	
	/**
	 * 修改方案切换数据
	 * @param unitId
	 * @param dt 修改后的时间
	 * @param oldDt 修改前的时间
	 */
	private List<ProductUnitProgShift> getProgChangeDataByEdit(String unitId, String upStartTime, String upEndTime) {
		List<ProductUnitProgShift> result = new ArrayList<ProductUnitProgShift>();
//		String nowDt = DateTimeUtils.getNowDateTimeStr();
		Map<String, Object> mapOrg = costUnit.getUinToperator(unitId);
		Object objOrg = new ArrayList<SysOrg>();
		if(mapOrg!=null && mapOrg.containsKey("sysOrg")) {
			objOrg = mapOrg.get("sysOrg");
			if(objOrg!=null) {
				
				List<String> listOrg = new ArrayList<String>();
				for(SysOrg temp : (List<SysOrg>) objOrg) {
					listOrg.add(temp.getId());
				}
				String startSbsj = upStartTime;
				ShiftForeignVo shiftSb =  shift.getShiftByOrgListDateTime(listOrg, startSbsj);
				if(shiftSb!=null) {
					startSbsj = shiftSb.getSbsj();
				}
				String endXbsj = upEndTime;
				ShiftForeignVo shiftXb =  shift.getShiftByOrgListDateTime(listOrg, upEndTime);
				if(shiftXb!=null) {
					endXbsj = shiftXb.getXbsj();
				}
				
//				result = createUnitProgStart(unitId, listShift, listStart);
				result = createUnitProgStart(unitId, listOrg, startSbsj, endXbsj);
				
//				String startSbsj = dt;
//				ShiftForeignVo shiftSb =  shift.getShiftByOrgListDateTime(listOrg, dt);
//				if(shiftSb!=null) {
//					startSbsj = shiftSb.getSbsj();
//				}
//				String endXbsj = "";
//				// 下一个方案
//				ProductScheduPlanStart nextObj = new ProductScheduPlanStart();
//				nextObj = getNStartObj(unitId, dt);
//				if(nextObj!=null) {// 有下一个方案，下班时间为下一个方案的开始时间
//					endXbsj = nextObj.getStartdatetime();
//				}else {
//					endXbsj = nowDt;
//				}
//				ShiftForeignVo shiftXb =  shift.getShiftByOrgListDateTime(listOrg, endXbsj);
//				if(shiftXb!=null) {
//					endXbsj = shiftXb.getXbsj();
//				}
//				List<ShiftForeignVo> listShift = shift.getShiftByDateTime(listOrg, startSbsj, endXbsj);
//				Where whereStart = Where.create();
//				whereStart.eq(ProductScheduPlanStart::getUnitid, unitId);
//				whereStart.le(ProductScheduPlanStart::getStartdatetime, dt);
//				whereStart.ge(ProductScheduPlanStart::getEnddatetime, dt);
//				whereStart.lt(ProductScheduPlanStart::getStartdatetime, endXbsj);
//				Order orderStart = Order.create();
//				orderStart.orderByAsc(ProductScheduPlanStart::getStartdatetime);
//				List<ProductScheduPlanStart> listStart = new ArrayList<ProductScheduPlanStart>();
//				listStart = dao.queryData(ProductScheduPlanStart.class, whereStart, orderStart, null);
//				List<ProductUnitProgShift> list1 = createUnitProgStart(unitId, listShift, listStart);
//				LinkedHashMap<String, ProductUnitProgShift> map = new LinkedHashMap<String, ProductUnitProgShift>();
//				for(ProductUnitProgShift temp : list1) {
//					String key = temp.getTbrq()+temp.getProgramid()+temp.getSbsj()+temp.getXbsj()+temp.getStartTime()+temp.getEndTime();
//					result.add(temp);
//					map.put(key, temp);
//				}
//				
//				startSbsj = oldDt;
//				ShiftForeignVo shiftSbOld = shift.getShiftByOrgListDateTime(listOrg, oldDt);
//				if(shiftSbOld!=null) {
//					startSbsj = shiftSbOld.getSbsj();
//				}
//				endXbsj = "";
//				// 下一个方案
//				ProductScheduPlanStart nextObjOld = new ProductScheduPlanStart();
//				nextObjOld = getNStartObj(unitId, oldDt);
//				if(nextObjOld!=null) {// 有下一个方案，下班时间为下一个方案的开始时间
//					endXbsj = nextObjOld.getStartdatetime();
//				}else {
//					endXbsj = nowDt;
//				}
//				ShiftForeignVo shiftXbOld =  shift.getShiftByOrgListDateTime(listOrg, endXbsj);
//				if(shiftXbOld!=null) {
//					endXbsj = shiftXbOld.getXbsj();
//				}
//				List<ShiftForeignVo> listShiftOld = shift.getShiftByDateTime(listOrg, startSbsj, endXbsj);
//				Where whereStartOld = Where.create();
//				whereStartOld.eq(ProductScheduPlanStart::getUnitid, unitId);
//				whereStartOld.le(ProductScheduPlanStart::getStartdatetime, oldDt);
//				whereStartOld.ge(ProductScheduPlanStart::getEnddatetime, oldDt);
//				whereStartOld.lt(ProductScheduPlanStart::getStartdatetime, endXbsj);
//				Order orderStartOld = Order.create();
//				orderStartOld.orderByAsc(ProductScheduPlanStart::getStartdatetime);
//				List<ProductScheduPlanStart> listStartOld = new ArrayList<ProductScheduPlanStart>();
//				listStartOld = dao.queryData(ProductScheduPlanStart.class, whereStartOld, orderStartOld, null);
//				List<ProductUnitProgShift> list2 = createUnitProgStart(unitId, listShiftOld, listStartOld);
//				for(ProductUnitProgShift temp : list2) {
//					String key = temp.getTbrq()+temp.getProgramid()+temp.getSbsj()+temp.getXbsj()+temp.getStartTime()+temp.getEndTime();
//					if(map!=null && map.containsKey(key)) {
//						
//					}else {
//						result.add(temp);
//					}
//				}
			}
		}
		return result;
	}
	
	/**
	 * 获取上个方案的数据
	 * @param unitId 核算对象编码
	 * @param startDatetime 时间
	 * @return
	 */
	private ProductScheduPlanStart getPStartObj(String unitId, String startDatetime) {
	
		// 获取小于 开始日期+时间参数最大的 开始日期+时间
		Where whereOld = Where.create();
		whereOld.eq(ProductScheduPlanStart::getUnitid, unitId);
		whereOld.lt(ProductScheduPlanStart::getStartdatetime, startDatetime);
		String startDatetimeOld = dao.findMaxValue(ProductScheduPlanStart.class,
				ProductScheduPlanStart::getStartdatetime, String.class, whereOld);
		// 上一条方案的数据
		ProductScheduPlanStart previousObj = new ProductScheduPlanStart();
		if (startDatetimeOld != null && startDatetimeOld.length() > 0) {// 有历史的开始数据
			Where where = Where.create();
			where.eq(ProductScheduPlanStart::getUnitid, unitId);
			where.eq(ProductScheduPlanStart::getStartdatetime, startDatetimeOld);
			List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
			list = dao.queryData(ProductScheduPlanStart.class, where, null, null);
			if (StringUtils.isNotEmpty(list) && list.size() > 0) {// 获取到了历史数据，将历史数据最大的开始日期数据的 结束日期字段 赋值为本次的开始日期+时间
				previousObj = list.get(0);
			}
		}else {
			previousObj = null;
		}
		return previousObj;
	}
	
	/**
	 * 获取下一个方案的数据
	 * @param unitId 核算对象编码
	 * @param startDatetime 时间
	 * @return
	 */
	private ProductScheduPlanStart getNStartObj(String unitId, String startDatetime) {
		// 获取 大于开始日期+时间参数最小的 开始日期+时间
		Where whereNew = Where.create();
		whereNew.eq(ProductScheduPlanStart::getUnitid, unitId);
		whereNew.gt(ProductScheduPlanStart::getStartdatetime, startDatetime);
		String startDatetimeNew = dao.findMinValue(ProductScheduPlanStart.class,
				ProductScheduPlanStart::getStartdatetime, String.class, whereNew);
		ProductScheduPlanStart nextObj = new ProductScheduPlanStart();
		if (startDatetimeNew != null && startDatetimeNew.length() > 0) {// 有 大于当前传递过来的 开始日期+时间参数的数据
			Where where = Where.create();
			where.eq(ProductScheduPlanStart::getUnitid, unitId);
			where.eq(ProductScheduPlanStart::getStartdatetime, startDatetimeNew);
			List<ProductScheduPlanStart> list = new ArrayList<ProductScheduPlanStart>();
			list = dao.queryData(ProductScheduPlanStart.class, where, null, null);
			if (StringUtils.isNotEmpty(list) && list.size() > 0) {// 本次开始的结束日期+时间 为 下一次的开始日期+时间
				nextObj = list.get(0);
			} else {
				nextObj = null;
			}
		} else {// 没有大于当前传递过来的日期时间节点的数据
			nextObj = null;
		}
		return nextObj;
	}
	
	private List<ProductUnitProgShift> createUnitProgStart(String unitId, List<String> listOrg, String startSbsj, String endXbsj) {
		
//		List<ShiftForeignVo> listShift = new ArrayList<ShiftForeignVo>();
//		List<ProductScheduPlanStart> listStart = new ArrayList<ProductScheduPlanStart>();
		List<ShiftForeignVo> listShift = shift.getShiftByDateTime(listOrg, startSbsj, endXbsj);
		Where whereStart = Where.create();
		whereStart.eq(ProductScheduPlanStart::getUnitid, unitId);
		whereStart.and();
		whereStart.lb();
		whereStart.lb();
		whereStart.le(ProductScheduPlanStart::getStartdatetime, startSbsj);
		whereStart.ge(ProductScheduPlanStart::getEnddatetime, startSbsj);
		whereStart.rb();
		whereStart.or();
		whereStart.lb();
		whereStart.le(ProductScheduPlanStart::getStartdatetime, endXbsj);
		whereStart.ge(ProductScheduPlanStart::getEnddatetime, endXbsj);
		whereStart.rb();
		whereStart.or();
		whereStart.lb();
		whereStart.ge(ProductScheduPlanStart::getStartdatetime, startSbsj);
		whereStart.le(ProductScheduPlanStart::getEnddatetime, endXbsj);
		whereStart.rb();
		whereStart.rb();
		Order orderStart = Order.create();
		orderStart.orderByAsc(ProductScheduPlanStart::getStartdatetime);
		List<ProductScheduPlanStart> listStart = new ArrayList<ProductScheduPlanStart>();
		listStart = dao.queryData(ProductScheduPlanStart.class, whereStart, orderStart, null);
		
		List<ProductUnitProgShift> result = new ArrayList<ProductUnitProgShift>();
		for(ShiftForeignVo tempShift : listShift) {
			String sbsj = tempShift.getSbsj();
			String xbsj = tempShift.getXbsj();
			for(ProductScheduPlanStart tempStart : listStart) {
				String startTime = tempStart.getStartdatetime();
				String endTime = tempStart.getEnddatetime();
				
				long l_startTime = Long.parseLong(startTime.replaceAll(" ", "").replaceAll("-", "").replaceAll(":", ""));
				long l_sbsj = Long.parseLong(sbsj.replaceAll(" ", "").replaceAll("-", "").replaceAll(":", ""));
				long l_xbsj = Long.parseLong(xbsj.replaceAll(" ", "").replaceAll("-", "").replaceAll(":", ""));
				long l_endTime = Long.parseLong(endTime.replaceAll(" ", "").replaceAll("-", "").replaceAll(":", ""));
				
				long bjStartSbsjDays = l_startTime - l_sbsj;
				// 开始日期+时间小于下班时间
				long bjStartXbsjDays = l_startTime - l_xbsj;
//				// 结束日期+时间大于下班时间
				long bjEndXbsjDays = l_endTime - l_xbsj;
				
				long bjEndSbsjDays = l_endTime - l_sbsj;
				
				if(bjStartSbsjDays>=0) {// 开始日期+时间大于上班时间,开始时间为方案的开始时间
					if(bjStartXbsjDays<0) {// 开始时间小于下班时间，大于下班时间没有意义不需要返回
						ProductUnitProgShift obj = new ProductUnitProgShift();
						if(bjEndXbsjDays>0) {//结束时间大于下班时间，则结束日期为下班时间，否则为方案的结束时间
							obj.setEndTime(xbsj);
						}else {
							obj.setEndTime(endTime);
						}
						obj.setOrgCode(tempShift.getOrgCode());
						obj.setProgramid(tempStart.getProgramid());
						obj.setSbsj(sbsj);
						obj.setShiftClassCode(tempShift.getShiftClassCode());
						obj.setShiftClassName(tempShift.getShiftClassName());
						obj.setStartTime(startTime);
						obj.setTbrq(tempShift.getTbrq());
						obj.setTjrq(tempShift.getTjsj());
						obj.setUnitId(unitId);
						obj.setXbsj(xbsj);
						result.add(obj);
					}
				}else {// 开始日期+时间小于上班时间,开始时间为上班时间
					if(bjEndSbsjDays>0) {// 结束时间大于上班时间，如果小于上班时间没有意义不需要返回
						ProductUnitProgShift obj = new ProductUnitProgShift();
						if(bjEndXbsjDays>0) {//结束时间大于下班时间，则结束日期为下班时间，否则为方案的结束时间
							obj.setEndTime(xbsj);
						}else {
							obj.setEndTime(endTime);
						}
						obj.setOrgCode(tempShift.getOrgCode());
						obj.setProgramid(tempStart.getProgramid());
						obj.setSbsj(sbsj);
						obj.setShiftClassCode(tempShift.getShiftClassCode());
						obj.setShiftClassName(tempShift.getShiftClassName());
						obj.setStartTime(sbsj);
						obj.setTbrq(tempShift.getTbrq());
						obj.setTjrq(tempShift.getTjsj());
						obj.setUnitId(unitId);
						obj.setXbsj(xbsj);
						result.add(obj);
					}
				}
			}
		}
		return result;
	}
	
	/**
	 * 获取一个核算对象小于等于时间最大的数据
	 * @param dto 需要传递核算对象编码和日期+时间
	 * @return
	 */
	@Override
	public List<ProductScheduPlanStart> getZzRunState(List<CostProgStartTimeDto> dto) {
		List<ProductScheduPlanStart> result = new ArrayList<ProductScheduPlanStart>();
//		Where where = Where.create();
//		where.eq(ProductScheduPlanStart::getUnitid, dto.getUnitId());
//		where.le(ProductScheduPlanStart::getStartdatetime, dto.getStartDatetime());
//		String startTime = dao.findMaxValue(ProductScheduPlanStart.class, ProductScheduPlanStart::getStartdatetime, String.class, where);
//		if(startTime!=null && startTime.length()>0) {
//			Where whereStart = Where.create();
//			whereStart.eq(ProductScheduPlanStart::getUnitid, dto.getUnitId());
//			whereStart.eq(ProductScheduPlanStart::getStartdatetime, startTime);
//			List<ProductScheduPlanStart> list = dao.queryData(ProductScheduPlanStart.class, whereStart, null, null);
//			if(StringUtils.isNotEmpty(list)) {
//				result = list.get(0);
//			}else {
//				result = null;
//			}
//		}else {
//			result = null;
//		}
		if(StringUtils.isNotEmpty(dto)) {
			String startTime = dto.get(0).getStartDatetime();
			String sql = "select a.* from PRODUCTSCHEDU_PLAN_START a right join " + 
					" (select " + 
					" max(STARTDATETIME) STARTDATETIME,UNITID from PRODUCTSCHEDU_PLAN_START PRODUCTSCHEDU_PLAN_START " + 
					" where STARTDATETIME<='"+startTime+"' group by UNITID " + 
					" ) b on a.UNITID=b.UNITID and a.STARTDATETIME=b.STARTDATETIME ";
//			dao.rawQuery(sql);
			SqlRowSet rowSet = dao.rawQuery(sql);
			LinkedHashMap<String, ProductScheduPlanStart> map = new LinkedHashMap<String, ProductScheduPlanStart>();
			if (rowSet != null) {
				while (rowSet.next()) {
					ProductScheduPlanStart obj = new ProductScheduPlanStart();
					String unitid = rowSet.getString("UNITID");
					obj.setEnddatetime(rowSet.getString("ENDDATETIME"));
					obj.setId(rowSet.getString("ID"));
					obj.setPlanid(rowSet.getString("PLANID"));
					obj.setProgramid(rowSet.getString("PROGRAMID"));
					obj.setProgramname(rowSet.getString("PROGRAMNAME"));
					obj.setStartdatetime(rowSet.getString("STARTDATETIME"));
					obj.setUnitid(unitid);
					map.put(unitid, obj);
				}
				for(CostProgStartTimeDto temp : dto) {
					if(map!=null && map.containsKey(temp.getUnitId())) {
						result.add(map.get(temp.getUnitId()));
					}
				}
			}
		}
		return result;
	}
	/**
	 * 判断是否允许进行方案切换
	 * @return 如果空，允许进行方案切换
	 */
	@Override
	public String getProgChangeRedisSize() {
		String result = "";
		String key = "COST:ProgChange";
		Map<String, String> mapRedisChange = new LinkedHashMap<String, String>();
		mapRedisChange = redis.getMap(key);
		if(mapRedisChange!=null) {
			int maxSize = 10;
			List<CostProgChangeSize> list = dao.queryData(CostProgChangeSize.class, null, null, null);
			if(StringUtils.isNotEmpty(list)) {
				CostProgChangeSize obj = list.get(0);
				maxSize = obj.getChangeSize()==null?10:obj.getChangeSize();
			}
			if(mapRedisChange.size()>maxSize) {
				result = "当前有"+String.valueOf(maxSize)+"个核算对象正在进行方案切换后的数据提取和计算，达到了最大限制，请您稍候再试！";
			}
		}
		return result;
	}
	/**
	 * 获取一个核算对象是否允许进行方案切换
	 * @param dto
	 * @return 如果为空，则允许核算对象进行方案切换操作
	 */
	@Override
	public String getProgChangeRedisUnit(CostProgStartTimeDto dto) {
		String result = "";
		String key = "COST:ProgChange";
		result = getProgChangeRedisSize();
		if(result!=null && result.length()>0) {
			
		}else {
			Map<String, String> mapRedisChange = new LinkedHashMap<String, String>();
			mapRedisChange = redis.getMap(key);
			if(mapRedisChange!=null) {
				if(mapRedisChange.containsKey(dto.getUnitId())) {
					String value = mapRedisChange.get(dto.getUnitId());
					result = "您选择的核算对象正在进行方案切换操作，"+value+"，请等操作完成后再进行！";
				}
			}
		}
		return  result;
	}
	/**
	 * 方案切换提取数据和计算redis赋值操作
	 * @param dto
	 */
	@Override
	public void setProrChangeRedisValue(CostProgStartTimeDto dto) {
		String key = "COST:ProgChange";
		Map<String, String> mapRedisChange = new LinkedHashMap<String, String>();
		mapRedisChange = redis.getMap(key);
		if(mapRedisChange!=null && mapRedisChange.containsKey(dto.getUnitId())) {
			mapRedisChange.remove(dto.getUnitId());
			mapRedisChange.put(dto.getUnitId(), dto.getProgChangeFlagStr());
//			redis.delete(key);
//			redis.
			if(mapRedisChange!=null && mapRedisChange.size()>0) {
				redis.setMap(key, mapRedisChange);
			}
		}
	}
	
	/**
	 * 获取正在计算或提取平稳率数据的提示信息
	 * @param dto
	 * @return
	 */
	@Override
	public String getProgChangeRedisUnitCalValue(CostProgStartTimeDto dto) {
		String result = "";
		String key = "COST:ProgChange";
		Map<String, String> mapRedisChange = new LinkedHashMap<String, String>();
		mapRedisChange = redis.getMap(key);
		if(mapRedisChange!=null && mapRedisChange.containsKey(dto.getUnitId())) {
			result = mapRedisChange.get(dto.getUnitId());
		}
		return result;
	}
	@Override
	public void insertProgChangeUnitSize() {
		List<CostProgChangeSize> list = dao.queryData(CostProgChangeSize.class, null, null, null);
		if(StringUtils.isNotEmpty(list)) {
			
		}else {
			CostProgChangeSize obj = new CostProgChangeSize();
			obj.setId("Z20240509");
			obj.setChangeSize(10);
			obj.setChangeDays(7);
			dao.insert(obj);
		}
	}
	
	/**
	 * 判断核算方案是否可以删除，传入核算对象编码和方案编码
	 * @param dto
	 * @return
	 */
	@Override
	public boolean isCanDelUnitProg(CostProgStartTimeDto dto) {
		boolean result = true;
		Where where = Where.create();
		where.eq(ProductScheduPlanStart::getProgramid, dto.getProgramid());
		Long count = dao.queryCount(ProductScheduPlanStart.class, where);
		if(count!=null && count>0) {
			result = false;
		}
		return result;
	}
	/**
	 * 是否可以进行方案切换操作
	 * @return
	 */
	@Override
	public List<String> getChangeDays(String dt) {
		List<String> result = new ArrayList<String>();
//		int days = DateTimeUtils.dayDiff(DateTimeUtils.parseDate(dt),
//				DateTimeUtils.parseDate(DateTimeUtils.getNowDateTimeStr()));
		Integer maxDays = 7;
		List<CostProgChangeSize> list = dao.queryData(CostProgChangeSize.class, null, null, null);
		if(StringUtils.isNotEmpty(list)) {
			CostProgChangeSize obj = list.get(0);
			maxDays = obj.getChangeDays()==null?7:obj.getChangeDays();
		}
//		days = Math.abs(days);
		String ksrq = DateTimeUtils.format(DateTimeUtils.doDate(DateTimeUtils.getNowDate(), (0 -maxDays)), "yyyy-MM-dd");
		String jzrq = DateTimeUtils.format(DateTimeUtils.doDate(DateTimeUtils.getNowDate(), maxDays), "yyyy-MM-dd");
		result.add(ksrq);
		result.add(jzrq);
//		if(days>maxDays) {
//			
//		}
		return result;
		
	}
	
	/**
	 * 在设备表中获取数据，将数据同步到方案切换表中
	 * @param listUnitIdAcc 核算对象编码 核算对象编码_核算对象类型_2
	 * @param listUnitId 核算对象编码
	 * @param paramStartTime 开始时间
	 * @param paramEndTime 结束时间
	 */
	private void autoChangeAccInputToStart(List<String> listUnitIdAcc, List<String> listUnitId, String paramStartTime, String paramEndTime){
		DeviceStatusSwitchDto paramDto = new DeviceStatusSwitchDto();
		paramDto.setAcctobjIds(listUnitIdAcc);
		paramDto.setEndDateTime(paramEndTime);
		paramDto.setStartDateTime(paramStartTime);
		List<AcctobjInputmx> listAccInput = new ArrayList<AcctobjInputmx>();
		LinkedHashMap<String, LinkedHashMap<String, String>> mapAcc = new LinkedHashMap<String, LinkedHashMap<String, String>>();
//		List<String> listUnitId = paramDto.getAcctobjIds();
		try {
			// 调用张晋铜设备接口同步到方案切换表中        Start *********************************************************
			listAccInput = mobLeanCost.getDeviceStatusSwitchData(paramDto);
			
			// 调用张晋铜设备接口同步到方案切换表中        END *********************************************************
			
			if(StringUtils.isNotEmpty(listAccInput)) {
				for(AcctobjInputmx temp : listAccInput) {
					String pid = temp.getIptId();
					String unitId = temp.getAcctobjId()==null?"":temp.getAcctobjId();
					String key = pid + "@@@@@"+ unitId.split("_")[0];
					LinkedHashMap<String, String> mapTemp = new LinkedHashMap<String, String>();
					if(mapAcc!=null && mapAcc.containsKey(key)) {
						mapTemp = mapAcc.get(key);
						if("ChangeStatusOfEquipment_1".contains(temp.getCollectPointId())) {// 切换时间
							mapTemp.put("ChangeStatusOfEquipment_1", temp.getCollectPointVal()==null?"":temp.getCollectPointVal());
						}else if("ChangeStatusOfEquipment_2".contains(temp.getCollectPointId())){// 方案编码
							mapTemp.put("ChangeStatusOfEquipment_2", temp.getCollectPointVal());
						}
					}else {
						if("ChangeStatusOfEquipment_1".contains(temp.getCollectPointId())) {// 切换时间
							mapTemp.put("ChangeStatusOfEquipment_1", temp.getCollectPointVal()==null?"":temp.getCollectPointVal());
						}else if("ChangeStatusOfEquipment_2".contains(temp.getCollectPointId())){// 方案编码
							mapTemp.put("ChangeStatusOfEquipment_2", temp.getCollectPointVal()==null?"":temp.getCollectPointVal());
						}
					}
					mapAcc.put(key, mapTemp);
				}
				if(mapAcc!=null && mapAcc.size()>0) {
					LinkedHashMap<String, String> mapStartTemp = new LinkedHashMap<String, String>();
					
					// 根据传过来的条件获取方案切换的数据
					Where whereStart = Where.create();
					whereStart.in(ProductScheduPlanStart::getUnitid, listUnitId.toArray());
					whereStart.and();
					whereStart.lb();
					whereStart.lb();
					whereStart.le(ProductScheduPlanStart::getStartdatetime, paramDto.getStartDateTime());
					whereStart.ge(ProductScheduPlanStart::getEnddatetime, paramDto.getStartDateTime());
					whereStart.rb();
					whereStart.or();
					whereStart.lb();
					whereStart.le(ProductScheduPlanStart::getStartdatetime, paramDto.getStartDateTime());
					whereStart.ge(ProductScheduPlanStart::getEnddatetime, paramDto.getEndDateTime());
					whereStart.rb();
					whereStart.or();
					whereStart.lb();
					whereStart.ge(ProductScheduPlanStart::getStartdatetime, paramDto.getStartDateTime());
					whereStart.le(ProductScheduPlanStart::getEnddatetime, paramDto.getEndDateTime());
					whereStart.rb();
					whereStart.or();
					whereStart.lb();
					whereStart.ge(ProductScheduPlanStart::getStartdatetime, paramDto.getStartDateTime());
					whereStart.le(ProductScheduPlanStart::getStartdatetime, paramDto.getEndDateTime());
					whereStart.ge(ProductScheduPlanStart::getEnddatetime, paramDto.getEndDateTime());
					whereStart.rb();
					whereStart.rb();
					Order orderStart = Order.create();
					orderStart.orderByAsc(ProductScheduPlanStart::getUnitid);
					orderStart.orderByDesc(ProductScheduPlanStart::getStartdatetime);
					List<ProductScheduPlanStart> listStart = dao.queryData(ProductScheduPlanStart.class, whereStart,
							orderStart, null);
					if(StringUtils.isNotEmpty(listStart)) {
						for(ProductScheduPlanStart temp : listStart) {
							String unitId = temp.getUnitid()==null?"":temp.getUnitid();
//							String progId = temp.getProgramid()==null?"":temp.getProgramid();
							String startTime = temp.getStartdatetime()==null?"":temp.getStartdatetime();
							String key = unitId + startTime;
							mapStartTemp.put(key, "");
						}
					}
					
					for(Entry<String, LinkedHashMap<String, String>> entry : mapAcc.entrySet()) {
						String unitId = entry.getKey().split("@@@@@")[1];
						LinkedHashMap<String, String> mapTemp = entry.getValue();
						String accStartTime = "";
						String accProgId = "";
						for(Entry<String, String> entryValue : mapTemp.entrySet()) {
							if("ChangeStatusOfEquipment_1".equals(entryValue.getKey())) {// 切换时间
								accStartTime = entryValue.getValue();
							}
							if("ChangeStatusOfEquipment_2".equals(entryValue.getKey())) {// 方案编码
								accProgId = entryValue.getValue();
							}
						}
						String key = unitId + accStartTime;
						if(mapStartTemp!=null && mapStartTemp.containsKey(key)) {// 核算对象在时间点已存在数据，不进行同步
							
						}else {
							CostProgStartTimeDto dtoStart = new CostProgStartTimeDto();
							dtoStart.setProgramid(accProgId);
							dtoStart.setRowFlag("1");
							dtoStart.setStartDatetime(accStartTime);
							dtoStart.setUnitId(unitId);
							saveCostStartDateTime(dtoStart);
							mapStartTemp.put(key, null);
						}
					}
				}
			}
		}catch(Exception e) {
			log.error("", e);
		}
	}
}
