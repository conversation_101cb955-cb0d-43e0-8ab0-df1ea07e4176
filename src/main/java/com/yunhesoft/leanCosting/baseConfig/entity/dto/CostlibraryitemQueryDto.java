package com.yunhesoft.leanCosting.baseConfig.entity.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CostlibraryitemQueryDto {

	private String id;

	private List<String> idList;

	private String pid;

	@ApiModelProperty(value = "项目名称", example = "项目名称1")
	private String ccname;

	@ApiModelProperty(value = "是否只含有ERP编码", example = "是否只含有ERP编码")
	private boolean isNullErpCode;
	
	private List<String> mdmCodeList; //外部数据编码

}
