package com.yunhesoft.leanCosting.workDispatch.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchOnDuty;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.leanCosting.workDispatch.entity.dto.CostBatchDto;
import com.yunhesoft.leanCosting.workDispatch.entity.dto.WorkDispatchDto;
import com.yunhesoft.leanCosting.workDispatch.service.WorkDispatchService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/workDispatch/WorkDispatch")
@Api(tags = "核算车间调度台")
public class WorkDispatchController extends BaseRestController {
	
	@Autowired
	private WorkDispatchService workDispatchService;
	
	@Autowired
	private ICostService costService;
	@Autowired
	private UnitItemInfoService unititeminfoservice;
	
	@Autowired
	private IProgramService iprogramService;
	/**
	  *  获取核算对象列表
	 * @return
	 */
	@RequestMapping(value = "/getCostuintList", method = RequestMethod.POST)
	@ApiOperation("获取核算对象列表")
	public Res<?> getCostuintList() {
		SysUser user = SysUserHolder.getCurrentUser();
		/**
		 * getCostuintListByOrgId 获取核算对象列表（根据绑定机构ID）
		 * @param orgId  绑定机构ID
		 * @param selType  查询类型：1、操作机构；2、管理机构
		 * @return List<Costuint>
		 */
		List<Costuint> listAll=new ArrayList<Costuint>();
		Costuint e=new Costuint();
		e.setId("all");
		e.setName("全部");
		listAll.add(e);
		List<Costuint> list=costService.getCostuintListByOrgId(user.getOrgId(), 2);
		if(list!=null&&list.size()>0) {
			listAll.addAll(list);
		}
		return Res.OK(listAll);
	}
	
	
	
	/**
	  *  获取核算对象列表
	 * @return
	 */
	@RequestMapping(value = "/getCostuintLedgerEntryList", method = RequestMethod.POST)
	@ApiOperation("获取核算对象列表-过滤台账查询")
	public Res<?> getCostuintLedgerEntryList() {
		SysUser user = SysUserHolder.getCurrentUser();
		/**
		 * getCostuintListByOrgId 获取核算对象列表（根据绑定机构ID）
		 * @param orgId  绑定机构ID
		 * @param selType  查询类型：1、操作机构；2、管理机构
		 * @return List<Costuint>
		 */
		List<Costuint> listAll=new ArrayList<Costuint>();
		Costuint e=new Costuint();
		e.setId("all");
		e.setName("全部");
		listAll.add(e);
		List<Costuint> list=costService.getCostuintListByOrgId(user.getOrgId(), 2,true);
		if(list!=null&&list.size()>0) {
			listAll.addAll(list);
		}
		return Res.OK(listAll);
	}
	
	
	
	/**
	  *  获取方案列表列表
	 * @return
	 */
	@RequestMapping(value = "/getProgramItemListByUnitid", method = RequestMethod.POST)
	@ApiOperation("获取方案列表列表")
	public Res<?> getProgramItemListByUnitid(String unitid) {
		List<ProgramItem> list=iprogramService.getProgramItemListByUnitid(unitid);
		if(list==null||list.size()<=0) {
			list=new ArrayList<ProgramItem>();
			ProgramItem e=new ProgramItem();
			e.setId("0");
			e.setPId("");
			e.setMemo("");
			e.setPiName("默认方案");
			e.setTmUsed(1);
			e.setTmSort(0);
			list.add(e);
		}
		return Res.OK(list);
	}
	
	/**
	  *  获取班次列表
	 * @return
	 */
	@RequestMapping(value = "/getShiftList", method = RequestMethod.POST)
	@ApiOperation("获取班次列表")
	public Res<?> getShiftList(String unitcode, String rq) {
		return Res.OK(unititeminfoservice.getShiftList(unitcode,rq));
	}
	
	/**
	 * 获取调度台数据
	 * @param bean
	 * @return
	 */
	@RequestMapping(value = "/getData", method = RequestMethod.POST)
	@ApiOperation("获取调度台数据")
	public Res<?> getData(@RequestBody WorkDispatchDto bean) {
		return Res.OK(workDispatchService.getData(bean));
	}
	/**
	 * 查询交接班明细记录
	 * @param bean
	 * @return
	 */
	@RequestMapping(value = "/getListCostBatchOnDuty", method = RequestMethod.POST)
	@ApiOperation("查询交接班明细记录")
	public Res<?> getListCostBatchOnDuty(@RequestBody CostBatchDto bean) {
		return Res.OK(workDispatchService.getListCostBatchOnDuty(bean));
	}
	
	/**
	 * 交接班明细保存
	 * @param bean
	 * @return
	 */
	@RequestMapping(value = "/saveData", method = RequestMethod.POST)
	@ApiOperation("交接班明细保存")
	public Res<?> saveData(@RequestBody List<CostBatchOnDuty> list) {
		return Res.OK(workDispatchService.saveData(list));
	}
	/**
	 * 交接班明细删除
	 * @param bean
	 * @return
	 */
	@RequestMapping(value = "/deleteData", method = RequestMethod.POST)
	@ApiOperation("交接班明细删除")
	public Res<?> deleteData(@RequestBody List<CostBatchOnDuty> list) {
		return Res.OK(workDispatchService.deleteData(list));
	}
}
