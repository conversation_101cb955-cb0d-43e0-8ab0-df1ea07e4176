package com.yunhesoft.leanCosting.samplePlan.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.itextpdf.io.exceptions.IOException;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.ProductSaveDto;
import com.yunhesoft.leanCosting.baseConfig.service.ICostlibraryitemService;
import com.yunhesoft.leanCosting.order.entity.po.ProductControl;
import com.yunhesoft.leanCosting.samplePlan.entity.dto.QuerySampleDto;
import com.yunhesoft.leanCosting.samplePlan.entity.dto.SaveDto;
import com.yunhesoft.leanCosting.samplePlan.entity.dto.SearchProductDto;
import com.yunhesoft.leanCosting.samplePlan.entity.dto.SearchSamplePlanDto;
import com.yunhesoft.leanCosting.samplePlan.entity.po.SampleControl;
import com.yunhesoft.leanCosting.samplePlan.entity.po.SamplePlanTableSet;
import com.yunhesoft.leanCosting.samplePlan.service.ISamplePlanMangeService;
import com.yunhesoft.leanCosting.samplePlan.service.ISamplePlanTableSet;
import com.yunhesoft.leanCosting.samplePlan.service.ISampleService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 样品管理实现类
 * @date 2021/09/13
 */
@Log4j2
@Service
public class SampleServiceImpl implements ISampleService {

	@Autowired
	private EntityService dao;

	@Autowired
	private ISamplePlanMangeService ISamplePlanMangeService;

	@Autowired
	private ISamplePlanTableSet ots;
	@Autowired
	private HttpServletResponse response;
	@Autowired
	private MapImportHandler mapImport;

	@Autowired
	private ICostlibraryitemService costlibraryitemService;

	/**
	 * 保存样品数据.
	 *
	 * @param
	 * @return
	 */
	@Override
	public Boolean saveSample(SaveDto param) {
		Boolean bln = false;
		if (param != null && StringUtils.isNotEmpty(param.getData())) {
			List<SampleControl> insertList = new ArrayList<>();
			List<SampleControl> upateList = new ArrayList<>();
			List<SampleControl> deleteList = new ArrayList<>();
			JSONArray datas = JSONArray.parseArray(param.getData());
			if (datas != null) {
				for (int i = 0; i < datas.size(); i++) {
					JSONObject row = datas.getJSONObject(i);
					Integer flag = row.getInteger("flag");
					SampleControl product;
					product = JSONObject.toJavaObject(row, SampleControl.class);
					product.setManufactureNumber(product.getOrderNumber());
					if (flag == null || flag == 0) {
						insertList.add(product);
					} else if (flag == 1) {
						if (StringUtils.isNotEmpty(product.getId()))
							deleteList.add(product);
					} else if (flag == 2) {
						if (StringUtils.isNotEmpty(product.getId()))
							upateList.add(product);
					}
				}
			}
			if (StringUtils.isNotEmpty(insertList)) {
				bln = this.addSample(insertList);
			}
			if (StringUtils.isNotEmpty(deleteList)) {
				bln = this.delProduct(deleteList);
			}
			if (StringUtils.isNotEmpty(upateList)) {
				bln = this.updateSample(upateList);
			}
		}
		return bln;
	}

	/**
	 * 更新样品.
	 *
	 * @param
	 * @return
	 */
	@Override
	public Boolean updateSample(List<SampleControl> upateList) {
		int msg = 0;
		if (StringUtils.isNotEmpty(upateList)) {
			for (SampleControl sampleControl : upateList) {
				sampleControl.setUpdatePersonName(SysUserHolder.getCurrentUser().getRealName());
			}
			msg = dao.updateByIdBatch(upateList);
		}
		return msg > 0;
	}

	/**
	 * 删除样品.
	 *
	 * @param
	 * @return
	 */
	private Boolean delProduct(List<SampleControl> deleteList) {
		List<SampleControl> dellist = new ArrayList<>();
		for (SampleControl del : deleteList) {
			del.setUsed(0);
			dellist.add(del);
		}
		int msg = 0;
		if (StringUtils.isNotEmpty(dellist)) {
			msg = dao.updateByIdBatch(dellist);
		}
		return msg > 0;
	}

	/**
	 * 添加样品.
	 *
	 * @param
	 * @return
	 */
	@Override
	public Boolean addSample(List<SampleControl> insertList) {
		// 保存项目库
		ProductSaveDto productSaveDto = new ProductSaveDto();
//		productSaveDto.setName("产品");
		//复制bean改变类型
		List<ProductControl> list_ = ObjUtils.convertToList(ProductControl.class, insertList);
		List<ProductControl> listObj = costlibraryitemService.saveProductVo(productSaveDto, list_);
		insertList = ObjUtils.convertToList(SampleControl.class, listObj);
		//END
		
		List<SampleControl> addlist = new ArrayList<>();
		int max = dao.findMaxId(SampleControl.class, SampleControl::getSort).intValue();
		for (SampleControl prod : insertList) {
			max = max + 1;
			prod.setSort(max);
			if (prod.getId().length() < 22) {
				prod.setId(TMUID.getUID());
				// 如果小于22 是前台手动新增的 需要添加公式word
			}
			prod.setCreatePersonName(SysUserHolder.getCurrentUser().getRealName());
			prod.setUsed(1);
			prod.setIsMakeCard(0);
			addlist.add(prod);
		}
		int msg = 0;
		if (StringUtils.isNotEmpty(addlist)) {
			msg = dao.insertBatch(addlist);
		}
		return msg > 0;
	}

	/**
	 * 根据订单查询样品数据.样品编号正序
	 *
	 * @param
	 * @return
	 */
	@Override
	public List<SampleControl> selectSample(String orderId, Pagination<?> page) {
		Where where = Where.create();
		where.eq(SampleControl::getUsed, 1);
		if (StringUtils.isNotEmpty(orderId)) {
			where.eq(SampleControl::getPid, orderId);
		}
		Order order = Order.create();
		order.orderByAsc(SampleControl::getSort);
		return dao.queryData(SampleControl.class, where, order, page);
	}

	@Override
	public List<SampleControl> selectProduct(String orderId, Pagination<?> page, SearchProductDto params) {
		Where where = Where.create();
		where.eq(ProductControl::getUsed, 1);
		if (StringUtils.isNotEmpty(params.getProductNo())) {
			where.like(ProductControl::getProductNo, params.getProductNo());
		}
		if (StringUtils.isNotEmpty(params.getProduct())) {
			where.like(ProductControl::getProduct, params.getProduct());
		}
		if (StringUtils.isNotEmpty(orderId)) {
			where.eq(ProductControl::getPid, orderId);
		}
		if (StringUtils.isNotEmpty(params.getMaterialsDescribe())) {
			where.like(ProductControl::getMaterialsDescribe, params.getMaterialsDescribe());
		}
		if (StringUtils.isNotEmpty(params.getProductDescribe())) {
			where.like(ProductControl::getProductDescribe, params.getProductDescribe());
		}
		if (StringUtils.isNotEmpty(params.getTechnology())) {
			where.like(ProductControl::getTechnology, params.getTechnology());
		}
		if (StringUtils.isNotEmpty(params.getProductBm())) {
			where.like(ProductControl::getProductBm, params.getProductBm());
		}
		Order order = Order.create();
		order.orderByAsc(SampleControl::getSort);
		return dao.queryData(SampleControl.class, where, order, page);
	}

	@Override
	public List<SampleControl> selectSample(String orderId) {
		return this.selectSample(orderId, null);
	}

	/**
	 * 分页查询样品管理
	 *
	 * @param
	 * @return
	 */
	@Override
	public Res<?> selectSamplePage(QuerySampleDto queryParam) {
		Pagination<?> page = null;
		// where 条件
		Where where = Where.create();
		where.eq(SampleControl::getUsed, 1);
		where.ne(SampleControl::getStatus, 0);
		if (queryParam != null) {
			// if (StringUtils.isNotEmpty(queryParam.getPid())) {
			// where.eq(EmiProductControl::getPid, queryParam.getPid());
			// }

			if (queryParam.getPageSize() != null && queryParam.getPageSize() > 0) {// 创建分页信息
				page = Pagination.create(queryParam.getPageNum() == null ? 1 : queryParam.getPageNum(),
						queryParam.getPageSize());
			}
			if (StringUtils.isNotEmpty(queryParam.getMonth())) {
				where.like(SampleControl::getDeliveryDate, queryParam.getMonth());
			}
			if (queryParam.getStatus() != null) {
				where.eq(SampleControl::getStatus, queryParam.getStatus());
			}
			if (queryParam.getPid() != null) {
				where.eq(SampleControl::getPid, queryParam.getPid());
			}
			if (StringUtils.isNotEmpty(queryParam.getProductName())) {
				where.like(SampleControl::getProduct, queryParam.getProductName());
			}
			if (StringUtils.isNotEmpty(queryParam.getCustomerName())) {
				where.like(SampleControl::getClient, queryParam.getCustomerName());
			}

		}
		// 排序
		Order order = Order.create();
		order.orderByAsc(SampleControl::getProductNo);

		List<SampleControl> sampleControlList = dao.queryData(SampleControl.class, where, order, page);

		Res<?> res = Res.OK(sampleControlList);
		res.setTotal(dao.queryCount(SampleControl.class, where));
		return res;
	}

	/**
	 * 根据订单查询有无样品
	 *
	 * @param
	 * @return
	 */
	@Override
	public int haveSampleByPid(String param) {
		Where where = Where.create();
		where.eq(SampleControl::getUsed, 1);
		where.eq(SampleControl::getPid, param);
		return dao.queryCount(SampleControl.class, where).intValue();
	}

	/**
	 * 根据订单id删除样品
	 *
	 * @param
	 * @return
	 */
	@Override
	public int delSampleByPid(String param) {
		List<SampleControl> dellist = new ArrayList<>();
		Where where = Where.create();
		where.eq(SampleControl::getUsed, 1);
		where.eq(SampleControl::getPid, param);
		List<SampleControl> deleteList = dao.rawQueryListByWhere(SampleControl.class, where);
		for (SampleControl del : deleteList) {
			del.setUsed(0);
			dellist.add(del);
		}
		int msg = 0;
		if (StringUtils.isNotEmpty(dellist)) {
			msg = dao.updateByIdBatch(dellist);
		}
		return msg;
	}

	/**
	 * 获得全部样品
	 *
	 * @param
	 * @return
	 */
	@Override
	public List<SampleControl> getAllPro() {
		Where where = Where.create();
		where.eq(SampleControl::getUsed, 1);

		return dao.rawQueryListByWhere(SampleControl.class, where);
	}

	/**
	 * 更新状态
	 *
	 * @param
	 * @return
	 */
	@Override
	public Boolean updateStatus(String id, int status) {
		SampleControl emiSampleControl = dao.queryObjectById(SampleControl.class, id);
		emiSampleControl.setStatus(status);

		Boolean updateResult = dao.updateById(emiSampleControl) > 0;

		if (status == 4) {
			emiSampleControl = dao.rawQueryById(emiSampleControl);
			List<SampleControl> sampleControlList = new ArrayList<>();
			Where where = Where.create();
			where.eq(SampleControl::getUsed, 1);
			where.eq(SampleControl::getPid, emiSampleControl.getPid());
			sampleControlList = dao.queryData(SampleControl.class, where, null, null);
			for (SampleControl sampleControl : sampleControlList) {
				if (sampleControl.getStatus() != 4) {
					return updateResult;
				}
			}
			ISamplePlanMangeService.switchSamplePlanStatus(emiSampleControl.getPid(), 3);
		}

		return updateResult;
	}

	/**
	 * 样品管理审核
	 *
	 * @param
	 * @return
	 */
	@Override
	public Boolean passSample(SaveDto param) {
		Boolean bln = false;
		if (param != null && StringUtils.isNotEmpty(param.getData())) {
			List<SampleControl> insertList = new ArrayList<>();
			List<SampleControl> upateList = new ArrayList<>();
			List<SampleControl> deleteList = new ArrayList<>();
			JSONArray datas = JSONArray.parseArray(param.getData());
			if (datas != null) {
				for (int i = 0; i < datas.size(); i++) {
					JSONObject row = datas.getJSONObject(i);
					Integer flag = row.getInteger("flag");
					SampleControl product = new SampleControl();
					product = JSONObject.toJavaObject(row, SampleControl.class);
					if (flag == null || flag == 0) {
						insertList.add(product);
					} else if (flag == 1) {
						if (StringUtils.isNotEmpty(product.getId()))
							deleteList.add(product);
					} else if (flag == 2) {
						if (StringUtils.isNotEmpty(product.getId()))
							upateList.add(product);
					}
				}
			}
			if (StringUtils.isNotEmpty(insertList)) {
				bln = this.addSample(insertList);
			}
			if (StringUtils.isNotEmpty(deleteList)) {
				bln = this.delProduct(deleteList);
			}
			if (StringUtils.isNotEmpty(upateList)) {

				bln = this.updateSample(upateList);
			}
		}
		return bln;
	}

	/**
	 * 获取样品信息
	 *
	 * @param
	 * @return
	 */
	@Override
	public Map<String, String> getSampleInfo() {
		Map<String, String> map = new HashMap<>();
		SampleControl product = new SampleControl();
		Field[] fields = product.getClass().getDeclaredFields(); // 将实体的列返回
		for (Field field : fields) {
			String value = field.getName();
			ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
			String key = annotation.value();
			map.put(key, value);
			if ("nps".equals(value)) {
				break;
			}
		}
		return map;
	}

	/**
	 * 根据id查询样品
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	@Override
	public SampleControl selectSampleById(String productId) {
		return dao.queryObjectById(SampleControl.class, productId);
	}

	/**
	 * 查询样品
	 *
	 * @param queryDto
	 * @param page
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public List<SampleControl> querySample(SearchSamplePlanDto queryDto, Pagination<?> page) {
		Where where = Where.create();
		where.eq(SampleControl::getUsed, 1);
		if (StringUtils.isNotEmpty(queryDto.getClient())) {
			where.like(SampleControl::getClient, queryDto.getClient());
		}
		if (StringUtils.isNotEmpty(queryDto.getProductNo())) {
			where.like(SampleControl::getProductNo, queryDto.getProductNo());
		}
		if (StringUtils.isNotEmpty(queryDto.getProduct())) {
			where.like(SampleControl::getProduct, queryDto.getProduct());
		}
		if (ObjUtils.notEmpty(queryDto.getDeliveryDate())) {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String deliverdate1 = sdf.format(queryDto.getDeliveryDate()) + " 00:00:00";
			Date date1 = null;
			try {
				date1 = sdf1.parse(deliverdate1);
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
			String deliverdate2 = sdf.format(queryDto.getDeliveryDate()) + " 23:59:59";
			Date date2 = null;
			try {
				date2 = sdf1.parse(deliverdate2);
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
			where.le(SampleControl::getDeliveryDate, date2);
			where.ge(SampleControl::getDeliveryDate, date1);
		}
		Order order = Order.create();
		order.orderByAsc(SampleControl::getClient);
		order.orderByDesc(SampleControl::getDeliveryDate);
		order.orderByAsc(SampleControl::getProduct);
		if (ObjUtils.isEmpty(page)) {
			return dao.queryList(SampleControl.class, where);
		} else {
			return dao.queryData(SampleControl.class, where, order, page);
		}
	}

	/**
	 * 导出excel
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public void exportExcel(String pid, SearchProductDto sspdto, HttpServletResponse response) {
		try {
			List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
			// 加入一个id列
			ExcelExportEntity idcol = new ExcelExportEntity("ID", "id");
			colList.add(idcol);
			List<SamplePlanTableSet> titleHeadList = ots.getSamplePlanTableSet("product");
			// 循环遍历list，给表头赋值
			for (int i = 0; i < titleHeadList.size(); i++) {
				if (titleHeadList.get(i).getIsShow() == 1) {
					ExcelExportEntity colEntity = new ExcelExportEntity(titleHeadList.get(i).getHeaderName(),
							titleHeadList.get(i).getSourceCode());
					colEntity.setNeedMerge(true);
					// 设定宽度
					if (titleHeadList.get(i).getWidth() != 0) {
						colEntity.setWidth((titleHeadList.get(i).getWidth()) / 6.2);
					} else {
						colEntity.setWidth(0);
					}
					colList.add(colEntity);
				}
			}
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			if (sspdto.getFlag() == 1) {
				// 加入一个变更人列
				ExcelExportEntity changePeopleCol = new ExcelExportEntity("变更人", "updatePersonName");
				colList.add(changePeopleCol);
				changePeopleCol.setWidth(200 / 6.2);
				// 加入一个变更人列
				ExcelExportEntity changeTimeCol = new ExcelExportEntity("变更时间", "updateTime");
				colList.add(changeTimeCol);
				changeTimeCol.setWidth(200 / 6.2);
				// 加入一个创建人列
				ExcelExportEntity createPeopleCol = new ExcelExportEntity("创建人", "createPersonName");
				colList.add(createPeopleCol);
				createPeopleCol.setWidth(200 / 6.2);
				// 加入一个创建时间列
				ExcelExportEntity createTimeCol = new ExcelExportEntity("创建时间", "createTime");
				colList.add(createTimeCol);
				createTimeCol.setWidth(200 / 6.2);
				List<SampleControl> scl = this.selectProduct(pid, null, sspdto);
				for (SampleControl sampleControl : scl) {
					Map<String, Object> map = ObjUtils.convertToMap(sampleControl);
					map.put("id",sampleControl.getId());
					map.put("updateTime", sampleControl.getUpdateTime());
					map.put("createTime", sampleControl.getCreateTime());
					for (Map.Entry<String, Object> oEntry : map.entrySet()) {
						Object value = oEntry.getValue();
						if (value != null && value != "") {
							String type = value.getClass().toString();
							if (type.equals("class java.util.Date")) {
								SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
								oEntry.setValue(sdf.format(value));
							}
						}
					}
					list.add(map);
				}
			}
			Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("样品信息", "数据"), colList, list);
			for (int i = 0; i < titleHeadList.size(); i++) {
				if (titleHeadList.get(i).getHeaderName().equals("是否有工艺卡")) {
					this.selectList(workbook, i, i, new String[] { "0", "1" });
				} else if (titleHeadList.get(i).getHeaderName().equals("是否使用")) {
					this.selectList(workbook, i, i, new String[] { "0", "1" });
				} else if (titleHeadList.get(i).getHeaderName().equals("是否已经制作工艺卡")) {
					this.selectList(workbook, i, i, new String[] { "0", "1" });
				}
			}
			ExcelExport.downLoadExcel("订单数据", response, workbook);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 导入excel
	 *
	 * @return
	 * @throws Exception
	 * @throws java.io.IOException
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public Boolean importExcel(MultipartFile file, String productImprotPid) throws java.io.IOException, Exception {
		mapImport.createTitleHeadList();
		// 获取product_control表中的字段以及对应的数据类型
		Class<?> cls = SampleControl.class;
		Field[] declaredFields = cls.getDeclaredFields();
		// 把所有的字段以及对应的数据类型放到一个map中
		Map<String, String> productMap = new HashMap<String, String>();
		for (Field declaredField : declaredFields) {
			// 获取字段
			String sourceCode = declaredField.getName();
			Class<?> type = declaredField.getType();
			String dataType = type.getName();
			productMap.put(sourceCode, dataType);
		}
		// 是否必填项的列表
		List<SamplePlanTableSet> isMustList = ots.getSamplePlanTableSet("product");
		// 导入参数
		ImportParams params = new ImportParams();
		params.setTitleRows(1);
		params.setHeadRows(1);
		// 此处传入一个对象 这个对象继承于 ExcelDataHandlerDefaultImpl
		// 重写方法 用于映射数据 和 表头对应实体关系
		params.setDataHandler(new MapImportHandler());
		List<Map<String, Object>> list = null;
		// 如果传入的文件连表头都没有的空文件，返回错误提示信息
		try {
			list = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, params);
		} catch (Exception e) {
			// TODO: handle exception
			throw new Exception("整个文件为空！");
		}
		if (list.isEmpty()) {
			return false;
		}
		List<SampleControl> importList = new ArrayList<SampleControl>();
		// 错误消息提示里列表
		List<String> totalerrorList = new ArrayList<String>();
		for (int i = 0; i < list.size(); i++) {
			List<String> errorList = new ArrayList<String>();
			Map<String, Object> oMap = list.get(i);
			for (int j = 0; j < isMustList.size(); j++) {
				if (isMustList.get(j).getIsMust() == 1
						&& ObjUtils.isEmpty(oMap.get(isMustList.get(j).getSourceCode()))) {
					// 添加消息
					errorList.add("第" + (i + 1) + "行 " + isMustList.get(j).getHeaderName() + "不能为空！");
				}
			}
			for (Map.Entry<String, String> productEntry : productMap.entrySet()) {
				// 字段英文名
				String sourceCode = productEntry.getKey();
				// 字段的数据类型
				String dataType = productEntry.getValue();
				// 字段名称
				String headerName = new String();
				for (int k = 0; k < isMustList.size(); k++) {
					if (sourceCode.equals(isMustList.get(k).getSourceCode())) {
						headerName = isMustList.get(k).getHeaderName();
					}
				}
				for (Map.Entry<String, Object> oEntry : oMap.entrySet()) {
					// 传过来数据的值
					Object oMapValue = oEntry.getValue();
					if (sourceCode.equals(oEntry.getKey())) {
						if (dataType.equals("java.lang.Integer") && oMapValue instanceof String) {
							// 数据库中是int,excel传过来的值是String的情况
							String flag = oMapValue.toString();
							if (flag.matches("^[-\\+]?[\\d]*$")) {// 判断字符串是否是整数，是的话将其转换成int类型
								Integer.valueOf(flag);
								oEntry.setValue(flag);
							} else {// 其他类型直接添加报错信息
								errorList.add("第" + (i + 1) + "行" + headerName + "属性值应该为整数，请修改数据");
							}
						} else if (dataType.equals("java.lang.Integer") && oMapValue instanceof Double) {
							// 数据库中是int,excel传过来的值是Double的情况
							errorList.add("第" + (i + 1) + "行" + headerName + "属性值应该为整数，请修改数据");
						} else if (dataType.equals("java.lang.Integer") && oMapValue instanceof Date) {
							// 数据库中是int,excel传过来的值是Date的情况
							errorList.add("第" + (i + 1) + "行" + headerName + "属性值应该为整数，请修改数据");
						} else if (dataType.equals("java.lang.Double") && oMapValue instanceof String) {
							// 数据库中是Double的情况,excel传过来的值是String的情况
							String flag = oMapValue.toString();
							if (flag.matches("[0-9]+[.]{0,1}[0-9]*[dD]{0,1}")) {// 判断字符串是否是double类型，是的话将其转换成double类型
								Double.parseDouble(flag);
								oEntry.setValue(flag);
							} else {// 其他类型直接添加报错信息
								errorList.add("第" + (i + 1) + "行" + headerName + "属性值应该为小数，请修改数据");
							}
						} else if (dataType.equals("java.lang.Double") && oMapValue instanceof Date) {
							// 数据库中是Double的情况,excel传过来的值是Date的情况
							errorList.add("第" + (i + 1) + "行" + headerName + "属性值应该为小数，请修改数据");
						} else if (dataType.equals("java.util.Date") && oMapValue instanceof String) {
							// 数据库中是Date的情况,excel传过来的值是String的情况
							String flag = oMapValue.toString();
							if (flag.matches(
									"^(19|20)\\d{2}[-/\\\\.]([1-9]|0[1-9]|1[012])[-/\\\\.]([1-9]|[12]\\d|3[01])$")) {// 判断字符串是否是日期格式，是的话将其转换成date类型
								SimpleDateFormat sdf = new SimpleDateFormat();
								sdf.applyPattern("yyyy-MM-dd");
								Date date = sdf.parse(flag);
								oEntry.setValue(flag);
							} else {// 其他类型直接添加报错信息
								errorList.add("第" + (i + 1) + "行" + headerName + "属性值是日期，请修改例如2000-10-10");
							}
						} else if (dataType.equals("java.util.Date") && oMapValue instanceof Integer) {
							// 数据库中是Date的情况,excel传过来的值是Integer的情况
							errorList.add("第" + (i + 1) + "行" + headerName + "属性值是日期，请修改例如2000-10-10");
						} else if (dataType.equals("java.util.Date") && oMapValue instanceof Double) {
							// 数据库中是Date的情况,excel传过来的值是Double的情况
							errorList.add("第" + (i + 1) + "行" + headerName + "属性值是日期，请修改例如2000-10-10");
						}
					}
				}
			}
			if (StringUtils.isNotEmpty(errorList)) {
				totalerrorList.addAll(errorList);
				continue;
			}
			SampleControl bean = ObjUtils.convertToObject(SampleControl.class, list.get(i));
			bean.setId(TMUID.getUID());
			bean.setPid(productImprotPid);
			importList.add(bean);

		}
		// 转成string类型，并添加html换行符
		String errorMessage = totalerrorList.stream().collect(Collectors.joining("<br/>"));
		if (StringUtils.isNotEmpty(errorMessage)) {
			throw new Exception(errorMessage);
		}
		// 调用添加数据库的方法，放回一个Boolean类型的值
		return this.addSample(importList);
	}

	/**
	 * 添加下拉列表工具
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */

	public void selectList(Workbook workbook, int firstCol, int lastCol, String[] strings) {

		org.apache.poi.ss.usermodel.Sheet sheet = workbook.getSheetAt(0);
		// 生成下拉列表
		// 支队(x,x)单元有效
		CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(2, 2, firstCol, lastCol);
		// 生成下拉框内容
		DVConstraint dcConstraint = DVConstraint.createExplicitListConstraint(strings);
		HSSFDataValidation dataVaildation = new HSSFDataValidation(cellRangeAddressList, dcConstraint);
		sheet.addValidationData(dataVaildation);

	}

	/**
	 * 获取产品最大序号
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */

	@Override
	public Integer getMaxSort() {
		Long maxId = dao.findMaxId(SampleControl.class, SampleControl::getSort);
		if (maxId == null) {
			maxId = 0L;
		}
		return maxId.intValue();
	}
}
