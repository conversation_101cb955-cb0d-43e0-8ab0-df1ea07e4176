package com.yunhesoft.leanCosting.programConfig.controller;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.programConfig.entity.dto.ProgramLibraryCostQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.dto.ProgramLibraryCostSaveDto;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramVersion;
import com.yunhesoft.leanCosting.programConfig.entity.vo.ComboVo;
import com.yunhesoft.leanCosting.programConfig.entity.vo.ProgramLibraryCostVo;
import com.yunhesoft.leanCosting.programConfig.service.IProgramLibraryCostService;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@RestController
@RequestMapping("/leanCosting/programConfig/programLibraryCost")
@Api(tags = "方案配置")
public class ProgramLibraryCostController extends BaseRestController {

	
	@Autowired
	private IProgramLibraryCostService programLibraryCostService;
	
	
	/**
	 *	获取方案版本数据
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/getProgramVersionList", method = RequestMethod.POST)
	@ApiOperation("获取方案版本数据")
	public Res<?> getProgramVersionList(@RequestBody ProgramLibraryCostQueryDto queryDto) {
		Res<List<ProgramVersion>> res = new Res<List<ProgramVersion>>();
		List<ProgramVersion> list = programLibraryCostService.getProgramVersionList(queryDto);
		res.setResult(list);
		return res;
	}
	
	
	/**
	 *	保存方案版本数据
	 * @param saveDto
	 * @return
	 */
	@RequestMapping(value = "/saveProgramVersionData", method = RequestMethod.POST)
	@ApiOperation("保存方案版本数据")
	public Res<?> saveProgramVersionData(@RequestBody ProgramLibraryCostSaveDto saveDto) {
		return Res.OK(programLibraryCostService.saveProgramVersionData(saveDto));
	}
	
	
	/**
	 *	获取成本项目方案数据
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/getProgramLibraryCostList", method = RequestMethod.POST)
	@ApiOperation("获取成本项目方案数据")
	public Res<?> getProgramLibraryCostList(@RequestBody ProgramLibraryCostQueryDto queryDto) {
		Res<List<ProgramLibraryCostVo>> res = new Res<List<ProgramLibraryCostVo>>();
		List<ProgramLibraryCostVo> list = programLibraryCostService.getProgramLibraryCostList(queryDto);
		res.setResult(list);
		return res;
	}
	
	
	/**
	 *	保存成本核算设置数据
	 * @param saveDto
	 * @return
	 */
	@RequestMapping(value = "/saveProgramLibraryCostData", method = RequestMethod.POST)
	@ApiOperation("保存成本核算设置数据")
	public Res<?> saveProgramLibraryCostData(@RequestBody ProgramLibraryCostSaveDto saveDto) {
		return Res.OK(programLibraryCostService.saveProgramLibraryCostData(saveDto));
	}
	
	
	/**
	 *	获取成本核算方案单耗参考值数据
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/getBaseConsumptionReferenceValueList", method = RequestMethod.POST)
	@ApiOperation("获取成本核算方案单耗参考值数据")
	public Res<?> getBaseConsumptionReferenceValueList(@RequestBody ProgramLibraryCostQueryDto queryDto) {
		Res<List<ComboVo>> res = new Res<List<ComboVo>>();
		List<ComboVo> list = programLibraryCostService.getBaseConsumptionReferenceValueList(queryDto);
		res.setResult(list);
		return res;
	}
	
	
}
