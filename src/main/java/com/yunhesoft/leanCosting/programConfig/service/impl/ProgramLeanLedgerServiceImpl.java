package com.yunhesoft.leanCosting.programConfig.service.impl;


import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.programConfig.entity.dto.LeanLedgerQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.dto.LeanLedgerSaveDto;
import com.yunhesoft.leanCosting.programConfig.entity.po.LeanLedgerForm;
import com.yunhesoft.leanCosting.programConfig.entity.vo.LeanLedgerFormVo;
import com.yunhesoft.leanCosting.programConfig.service.IProgramLeanLedgerService;
import com.yunhesoft.leanCosting.programConfig.service.IProgramLibraryCostService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.tmsf.form.entity.dto.SFFormQueryDto;
import com.yunhesoft.tmsf.form.entity.po.SFForm;
import com.yunhesoft.tmsf.form.service.IFormManageService;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 *	精益台账相关服务接口实现类
 * <AUTHOR>
 * @date 2023-08-17
 */
@Service
public class ProgramLeanLedgerServiceImpl implements IProgramLeanLedgerService {

	@Autowired
	private EntityService entityService;
	
	@Autowired
	private IFormManageService formService; // 表单
	
	@Autowired
	private ISysOrgService orgService; // 机构
	
	@Autowired
	private IProgramLibraryCostService costService; // 核算
	
	
	/**
	 *	获取可绑定表单列表
	 * @return
	 */
	@Override
	public List<SFForm> getLeanLedgerCanBindFormList(LeanLedgerQueryDto queryDto) {
		List<SFForm> result = new ArrayList<SFForm>();
		List<SFForm> formList = formService.queryFormInfoList(null, null);
		if(StringUtils.isNotEmpty(formList)) {
			Map<String, List<LeanLedgerForm>> bindMap = new HashMap<String, List<LeanLedgerForm>>();
			List<LeanLedgerForm> bindList = this.getLeanLedgerFormList(queryDto);
			if(StringUtils.isNotEmpty(bindList)) {
				bindMap = bindList.stream().collect(Collectors.groupingBy(LeanLedgerForm::getFormId,Collectors.toList()));
			}
			for (int i = 0; i < formList.size(); i++) {
				SFForm formObj = formList.get(i);
				String id = formObj.getId();
				if(StringUtils.isEmpty(bindMap)||!bindMap.containsKey(id)) {  //显示未绑定的记录
					result.add(formObj);
				}
			}
    	}
		return result;
	}
	
	
	/**
	 *	获取方案下的精益台账
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<LeanLedgerForm> getLeanLedgerFormList(LeanLedgerQueryDto queryDto) {
		List<LeanLedgerForm> result = new ArrayList<LeanLedgerForm>();
		try {
			String pvId = ""; //方案版本ID
			List<String> pvIdList = null;
			String feedBackOrgId = ""; //反馈机构ID
			if(StringUtils.isNotNull(queryDto)) {
				pvId = queryDto.getPvId();
				pvIdList = queryDto.getPvIdList();
				feedBackOrgId = queryDto.getFeedBackOrgId();
			}
			//检索条件
			Where where = Where.create();
			where.eq(LeanLedgerForm::getTmUsed, 1);
			if(StringUtils.isNotEmpty(pvId)) {
				where.eq(LeanLedgerForm::getPvId, pvId);
			}
			if(StringUtils.isNotEmpty(pvIdList)) {
				where.in(LeanLedgerForm::getPvId, pvIdList.toArray());
			}
			if(StringUtils.isNotEmpty(feedBackOrgId)) {
				where.like(LeanLedgerForm::getFeedBackOrgId, feedBackOrgId);
			}
			//排序
			Order order = Order.create();
			order.orderByAsc(LeanLedgerForm::getTmSort);
			List<LeanLedgerForm> list = entityService.queryData(LeanLedgerForm.class, where, order, null);
			if(StringUtils.isNotEmpty(list)) {
				if(StringUtils.isNotEmpty(feedBackOrgId)) { //因为是逗号分隔保存的数据，所以先模糊查询后，再过滤一下；
					for (int i = 0; i < list.size(); i++) {
						LeanLedgerForm obj = list.get(i);
						String orgId = obj.getFeedBackOrgId();
						if(StringUtils.isNotEmpty(orgId)&&(","+orgId+",").indexOf(","+feedBackOrgId+",")!=-1) {
							result.add(obj);
						}
					}
				}else {
					result = list;
				}
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
	
	/**
	 *	获取精益台账对象
	 * @param id
	 * @return
	 */
	@Override
	public LeanLedgerForm getLeanLedgerFormObjById(String id) {
		LeanLedgerForm result = null;
		if(StringUtils.isNotEmpty(id)) {
			LeanLedgerForm queryObj = entityService.queryObjectById(LeanLedgerForm.class, id);
			if(StringUtils.isNotNull(queryObj)) {
				result = queryObj;
			}
		}
		return result;
	}
	
	
	/**
	 *	获取精益台账数据（vo）
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<LeanLedgerFormVo> getLeanLedgerList(LeanLedgerQueryDto queryDto) {
		List<LeanLedgerFormVo> result = new ArrayList<LeanLedgerFormVo>();
		List<LeanLedgerForm> queryList = this.getLeanLedgerFormList(queryDto);
		if(StringUtils.isNotEmpty(queryList)) {
			Map<String, SFForm> formMap = new HashMap<String, SFForm>();
			SFFormQueryDto formDto = new SFFormQueryDto();
			if(queryDto!=null) {
				formDto.setName(queryDto.getLeanLedgerName());
			}
			List<SFForm> formList = formService.queryFormInfoList(formDto, null);
			if(StringUtils.isNotEmpty(formList)) {
				formMap = formList.stream().collect(Collectors.toMap(SFForm::getId,Function.identity()));
        	}
			for (int i = 0; i < queryList.size(); i++) {
				LeanLedgerForm obj = queryList.get(i);
				String formId = obj.getFormId();
				if(StringUtils.isNotEmpty(formId)&&StringUtils.isNotEmpty(formMap)&&formMap.containsKey(formId)) {
					SFForm formObj = formMap.get(formId);
					String formName = formObj.getName();
					LeanLedgerFormVo vo = new LeanLedgerFormVo();
    				BeanUtils.copyProperties(obj, vo); //赋予返回对象
    				vo.setFormName(formName);
    				vo.setFormObj(formObj);
    				result.add(vo);
				}
			}
		}
		if(StringUtils.isNotEmpty(result)) {
			List<String> orgIdList = this.getOrgIdList(result);
			Map<String, SysOrg> orgNameMap = this.getOrgNameMap(orgIdList);
			this.setOrgName(result, orgNameMap);
		}
		return result;
	}
	
	//获取反馈机构ID列表
	private List<String> getOrgIdList(List<LeanLedgerFormVo> list) {
		List<String> result = new ArrayList<String>();
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				LeanLedgerFormVo vo = list.get(i);
				String feedBackOrgId = vo.getFeedBackOrgId();
				if(StringUtils.isNotEmpty(feedBackOrgId)) {
					String[] feedBackOrgIdArr = feedBackOrgId.split(",");
					for (int j = 0; j < feedBackOrgIdArr.length; j++) {
						String orgId = feedBackOrgIdArr[j];
						if(!result.contains(orgId)) {
							result.add(orgId);
						}
					}
				}
			}
		}
		return result;
	}
	
	//获取机构名称Map
	private Map<String, SysOrg> getOrgNameMap(List<String> orgIdList) {
		Map<String, SysOrg> map = new HashMap<String, SysOrg>();
		if (StringUtils.isNotEmpty(orgIdList)) {
			List<SysOrg> orgList = orgService.getOrgListById(orgIdList);
			if (StringUtils.isNotEmpty(orgList)) {
				map = orgList.stream().collect(Collectors.toMap(SysOrg::getId,Function.identity()));
			}
		}
		return map;
	}
	
	//设置机构名称
	private void setOrgName(List<LeanLedgerFormVo> list, Map<String, SysOrg> map) {
		if (StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				LeanLedgerFormVo vo = list.get(i);
				String feedBackOrgId = vo.getFeedBackOrgId();
				String orgId_str = "";
				String orgName_str = "";
				if(StringUtils.isNotEmpty(feedBackOrgId)) {
					String[] feedBackOrgIdArr = feedBackOrgId.split(",");
					for (int j = 0; j < feedBackOrgIdArr.length; j++) {
						String orgId = feedBackOrgIdArr[j];
						if(StringUtils.isNotEmpty(map)&&map.containsKey(orgId)) {
							String orgName = map.get(orgId).getOrgname();
							orgId_str += ","+orgId;
							orgName_str += ","+orgName;
						}
					}
				}
				if(StringUtils.isNotEmpty(orgId_str)) {
					orgId_str = orgId_str.substring(1);
					orgName_str = orgName_str.substring(1);
				}
				vo.setFeedBackOrgId(orgId_str);
				vo.setFeedBackOrgName(orgName_str);
			}
		}
	}
	
	
	/**
	 *	保存精益台账数据
	 * @param saveDto
	 * @return
	 */
	@Override
	public String saveLeanLedgerData(LeanLedgerSaveDto saveDto) {
		String result = "";
		//String hasOrgIdMess = ""; //已存在机构编码信息
    	String hasOrgNameMess = ""; //已存在机构名称信息
		List<LeanLedgerForm> addList = new ArrayList<LeanLedgerForm>();
       	List<LeanLedgerForm> updList = new ArrayList<LeanLedgerForm>();
		if (saveDto != null) {
			String editType = saveDto.getEditType();
			String pvId = saveDto.getPvId();
			List<LeanLedgerFormVo> saveList = saveDto.getLeanLedgerList();
            if (StringUtils.isNotEmpty(editType)&&StringUtils.isNotEmpty(pvId)&&StringUtils.isNotNull(saveList)) {
            	int maxNum = 0; //最大序号
            	Map<String, List<LeanLedgerForm>> bindMap = new HashMap<String, List<LeanLedgerForm>>();
            	Map<String, LeanLedgerForm> dataMap = new HashMap<String, LeanLedgerForm>();
            	LeanLedgerQueryDto queryDto = new LeanLedgerQueryDto();
            	queryDto.setPvId(pvId);
            	List<LeanLedgerForm> dataList = this.getLeanLedgerFormList(queryDto);
            	if(StringUtils.isNotEmpty(dataList)) {
    				bindMap = dataList.stream().collect(Collectors.groupingBy(LeanLedgerForm::getFormId,Collectors.toList()));
            		dataMap = dataList.stream().collect(Collectors.toMap(LeanLedgerForm::getId,Function.identity()));
            		Integer maxSort = dataList.get(dataList.size()-1).getTmSort();
            		if(maxSort!=null) {
            			maxNum = maxSort;
            		}
            	}
            	//获取需要校验的机构Map
//            	HashMap<String, String> checkOrgMap = new HashMap<String, String>();
//            	if("save".equals(editType)) { //保存
//            		checkOrgMap = this.getCheckOrgMap(dataList, saveList);
//            	}
            	
            	for (int i = 0; i < saveList.size(); i++) {
            		LeanLedgerForm saveObj = saveList.get(i);
            		String id_save = saveObj.getId();
            		if("save".equals(editType)) { //保存
            			//校验机构重复情况
//            			String retMess = this.checkOrgMess(saveObj, checkOrgMap);
//            			if(StringUtils.isNotEmpty(retMess)) {
//            				String[] retMessArr = retMess.split("__");
//            				if(retMessArr.length==2) {
//            					String idStr = retMessArr[0];
//            					String nameStr = retMessArr[1];
//            					String[] idArr = idStr.split(",");
//            					String[] nameArr = nameStr.split("、");
//            					if(idArr.length == nameArr.length) {
//                                    for(int j=0; j<idArr.length; j++) {
//                                    	String orgId = idArr[j];
//                                    	String orgName = nameArr[j];
//	                                    if((hasOrgIdMess+",").indexOf(","+orgId+",")==-1) {
//	                                        hasOrgIdMess += ","+orgId;
//	                                        hasOrgNameMess += "、"+orgName;
//	                                    }
//                                    }
//                                }
//            				}
//            			}
            			if(StringUtils.isNotEmpty(dataMap)&&StringUtils.isNotEmpty(id_save)&&dataMap.containsKey(id_save)) { //修改
            				LeanLedgerForm dataObj = dataMap.get(id_save);
            				BeanUtils.copyProperties(saveObj, dataObj); //赋予返回对象
            				dataObj.setId(id_save);
            				updList.add(dataObj);
                		}else { //新增
                			String formId_save = saveObj.getFormId();
                			if(StringUtils.isEmpty(bindMap)||!bindMap.containsKey(formId_save)) {  //未绑定的记录可以新增
                				maxNum += 1;
                    			LeanLedgerForm dataObj = new LeanLedgerForm();
                				BeanUtils.copyProperties(saveObj, dataObj); //赋予返回对象
                				String id = dataObj.getId();
                				if(StringUtils.isEmpty(id)) {
                					id = TMUID.getUID();
                				}
                				dataObj.setId(id);
                				dataObj.setPvId(pvId);
                				dataObj.setTmUsed(1);
                				dataObj.setTmSort(maxNum);
                				addList.add(dataObj);
            				}
                		}
            		}else if("del".equals(editType)) { //删除
            			if(StringUtils.isNotEmpty(id_save)) {
            				LeanLedgerForm dataObj = this.getLeanLedgerFormObjById(id_save);
                        	if(StringUtils.isNotNull(dataObj)) {
                    			dataObj.setTmUsed(0);
                    			updList.add(dataObj);
                        	}
            			}
            		}
				}
            }
        }
		if(StringUtils.isNotEmpty(hasOrgNameMess)) {
			result = "反馈机构【"+hasOrgNameMess.substring(1)+"】已存在，不能重复设置！";
		}else {
			result = this.saveLeanLedger(addList, updList, null);
		}
		return result;
	}
	
	
	/**
	 *	校验机构信息
	 * @param saveObj
	 * @param checkOrgMap
	 * @return
	 */
	private String checkOrgMess(LeanLedgerForm saveObj, HashMap<String, String> checkOrgMap) {
		String result = "";
		String orgIdMess = "";
		String orgNameMess = "";
		if(StringUtils.isNotNull(saveObj)) {
			String feedBackOrgId = saveObj.getFeedBackOrgId();
			String feedBackOrgName = saveObj.getFeedBackOrgName();
			if(StringUtils.isNotEmpty(feedBackOrgId)&&StringUtils.isNotEmpty(feedBackOrgName)) {
				String[] feedBackOrgIdArr = feedBackOrgId.split(",");
				String[] feedBackOrgNameArr = feedBackOrgName.split(",");
				if(feedBackOrgIdArr.length==feedBackOrgNameArr.length) {
					for (int i = 0; i < feedBackOrgIdArr.length; i++) {
						String orgId = feedBackOrgIdArr[i];
						String orgName = feedBackOrgNameArr[i];
						if(StringUtils.isNotEmpty(checkOrgMap)&&checkOrgMap.containsKey(orgId)) {
							orgIdMess += ","+orgId;
							orgNameMess += "、"+orgName;
						}
					}
				}
			}
		}
		if(StringUtils.isNotEmpty(orgIdMess)&&StringUtils.isNotEmpty(orgNameMess)) {
			result = orgIdMess.substring(1)+"__"+orgNameMess.substring(1);
		}
		return result;
	}
	
	
	/**
	 *	获取校验的机构Map
	 * @param dataList
	 * @param saveList
	 * @return
	 */
	private HashMap<String, String> getCheckOrgMap(List<LeanLedgerForm> dataList,List<LeanLedgerFormVo> saveList) {
		HashMap<String, String> map = new HashMap<String, String>();
		List<String> saveIdList = new ArrayList<String>();
		if(StringUtils.isNotEmpty(saveList)) {
			for (int i = 0; i < saveList.size(); i++) {
				LeanLedgerFormVo saveVo = saveList.get(i);
				String saveId = saveVo.getId();
				if(StringUtils.isNotEmpty(saveId)) {
					saveIdList.add(saveId);
				}
			}
		}
		if(StringUtils.isNotEmpty(dataList)) {
			for (int i = 0; i < dataList.size(); i++) {
				LeanLedgerForm dataObj = dataList.get(i);
				String dataId = dataObj.getId();
				if(StringUtils.isNotEmpty(saveIdList)&&saveIdList.contains(dataId)) {
					continue; //刨除保存数据（保存数据在前台已经验证）和其他数据不能重复
				}
				String feedBackOrgId = dataObj.getFeedBackOrgId();
				String feedBackOrgName = dataObj.getFeedBackOrgName();
				if(StringUtils.isNotEmpty(feedBackOrgId)&&StringUtils.isNotEmpty(feedBackOrgName)) {
					String[] feedBackOrgIdArr = feedBackOrgId.split(",");
					String[] feedBackOrgNameArr = feedBackOrgName.split(",");
					if(feedBackOrgIdArr.length==feedBackOrgNameArr.length) {
						for (int j = 0; j < feedBackOrgIdArr.length; j++) {
							String orgId = feedBackOrgIdArr[j];
							String orgName = feedBackOrgNameArr[j];
							if(!map.containsKey(orgId)) {
								map.put(orgId, orgName);
							}
						}
					}
				}
			}
		}
		return map;
	}
	
	
	/**
	 *	保存精益台账数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	private String saveLeanLedger(List<LeanLedgerForm> addList,List<LeanLedgerForm> updList,List<LeanLedgerForm> delList) {
		String result = "";
		if ("".equals(result)&&StringUtils.isNotEmpty(addList)) {
        	if(entityService.insertBatch(addList)==0) {
        		result = "添加失败！";
        	}
        }
		if ("".equals(result)&&StringUtils.isNotEmpty(updList)) {
        	if(entityService.updateByIdBatch(updList)==0) {
        		result = "更新失败！";
        	}
        }
		if ("".equals(result)&&StringUtils.isNotEmpty(delList)) {
        	if(entityService.deleteByIdBatch(delList)==0) {
        		result = "删除失败！";
        	}
        }
		return result;
	}
	

	/**
	 *	版本改变后，更新精益台账相关数据
	 * @param operType
	 * @param pvId
	 * @param newPvId
	 * @return
	 */
	@Override
	public String renewByVersionLeanLedger(String operType,String pvId,String newPvId) {
		String result = "";
		if(StringUtils.isNotEmpty(operType)) {
			List<LeanLedgerForm> addList = new ArrayList<LeanLedgerForm>();
			List<LeanLedgerForm> updList = new ArrayList<LeanLedgerForm>();
			if("add".equals(operType)) { //新增
				if(StringUtils.isNotEmpty(pvId)&&StringUtils.isNotEmpty(newPvId)) {
					LeanLedgerQueryDto dto = new LeanLedgerQueryDto();
					dto.setPvId(pvId);
	            	List<LeanLedgerForm> dataList = this.getLeanLedgerFormList(dto);
					if(StringUtils.isNotEmpty(dataList)) {
						for (int i = 0; i < dataList.size(); i++) {
							LeanLedgerForm dataObj = dataList.get(i);
							dataObj.setId(TMUID.getUID());
							dataObj.setPvId(newPvId);
		    				addList.add(dataObj);
						}
					}
				}
			}else if("del".equals(operType)) { //删除
				if(StringUtils.isNotEmpty(pvId)) {
            		String[] pvIdArr = pvId.split(",");
            		List<String> pvIdList = Arrays.asList(pvIdArr);
					LeanLedgerQueryDto dto = new LeanLedgerQueryDto();
					dto.setPvIdList(pvIdList);
	            	List<LeanLedgerForm> dataList = this.getLeanLedgerFormList(dto);
					if(StringUtils.isNotEmpty(dataList)) {
						for (int i = 0; i < dataList.size(); i++) {
							LeanLedgerForm dataObj = dataList.get(i);
							dataObj.setTmUsed(0);
							updList.add(dataObj);
						}
					}
				}
			}
			result = this.saveLeanLedger(addList, updList, null);
		}
		return result;		
	}
	
	
	/**
	 *	根据方案（及日期）和机构获取精益台账数据
	 * @param programId
	 * @param pVersion
	 * @param orgId
	 * @return
	 */
	@Override
	public List<LeanLedgerFormVo> getLeanLedgerListByProgramOrg(String programId, String pVersion,String orgId) {
		List<LeanLedgerFormVo> result = new ArrayList<LeanLedgerFormVo>();
		if(StringUtils.isNotEmpty(programId)&&StringUtils.isNotEmpty(pVersion)) {
			//获取最大版本
			String maxVer = costService.getMaxProgramVersionId(programId, pVersion,"");
			if(StringUtils.isNotEmpty(maxVer)) {
				LeanLedgerQueryDto queryDto = new LeanLedgerQueryDto();
				queryDto.setPvId(maxVer);
				queryDto.setFeedBackOrgId(orgId);
				List<LeanLedgerFormVo> queryList = this.getLeanLedgerList(queryDto);
				if(StringUtils.isNotEmpty(queryList)) {
					result = queryList;
				}
			}
		}
		return result;
	}
	
	
}
