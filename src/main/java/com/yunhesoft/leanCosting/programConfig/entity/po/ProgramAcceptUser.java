package com.yunhesoft.leanCosting.programConfig.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 *	方案接收人表（后续方案用）
 */
@Entity
@Setter
@Getter
@Table(name = "PROGRAMACCEPTUSER")
public class ProgramAcceptUser extends BaseEntity {
	
    private static final long serialVersionUID = 1L;
    
    /** 父id（后续方案的数据id） */
    @Column(name="PID", length=100)
    private String pId;
    
    /** 人员id */
    @Column(name="USERID", length=100)
    private String userId;
    
    /** 人员姓名 */
    @Column(name="USERNAME", length=200)
    private String userName;
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmSort;
    
}
