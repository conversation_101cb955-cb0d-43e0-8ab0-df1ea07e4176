package com.yunhesoft.leanCosting.programConfig.entity.vo;


import java.util.List;

import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramAcceptUser;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramNextInfo;

import lombok.Getter;
import lombok.Setter;

/**
 * 	方案后续工序信息
 * <AUTHOR>
 * @date 2023/10/22
 */
@Setter
@Getter
public class ProgramNextInfoVo extends ProgramNextInfo {
	
    
	private static final long serialVersionUID = 1L;

	/** 消息接收人 */
	private List<ProgramAcceptUser> acceptUserList;
	
	/** 工艺卡（后续工序） */
	private List<OperationCardFormVo> craftCardList;
	
	
}
