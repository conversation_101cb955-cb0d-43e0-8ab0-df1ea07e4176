package com.yunhesoft.leanCosting.synLeanCost;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.OutSystemTagNumber;
import com.yunhesoft.leanCosting.unitConf.service.IOutSystemService;
import com.yunhesoft.system.synchronous.utils.SynModel;

/**
 *	lims指标-10
 * <AUTHOR>
 * @date 2023-11-13
 */
@Service
public class SynCostDotLims extends SynModel {
	
	@Autowired
	private IOutSystemService outSystemService;
	
	private String synDelFlagValue = synParam.get("synDelFlagValue"); //外部数据同步删除标识值
	
	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList, HashMap<String,String> synParam) {
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>(); //失败返回的数据
		if(StringUtils.isNotEmpty(dataList)) {
			if(StringUtils.isNotEmpty(synParam)) {
				this.synDelFlagValue = synParam.get("synDelFlagValue"); //外部数据同步删除标识值
			}
			if(StringUtils.isEmpty(this.synDelFlagValue)) {
				this.synDelFlagValue = "T"; //默认标识T为删除
			}
			List<OutSystemTagNumber> addList = new ArrayList<OutSystemTagNumber>();
			List<OutSystemTagNumber> updList = new ArrayList<OutSystemTagNumber>();
			
			//数据库中存在的数据
			int maxPx = 0;
			HashMap<String, OutSystemTagNumber> queryMap = new HashMap<String, OutSystemTagNumber>();
			HashMap<String, Integer> hasDataMap = new HashMap<String, Integer>();
			MethodQueryDto dto = new MethodQueryDto();
			dto.setDataType(1); //lims指标
			List<OutSystemTagNumber> queryList = outSystemService.getOutSystemTagNumberList(dto);
			if(StringUtils.isNotEmpty(queryList)) {
				this.getDataMap(queryList, queryMap);
				Integer tmSort = queryList.get(queryList.size()-1).getTmSort();
				if(tmSort!=null) {
					maxPx = tmSort;
				}
			}
			
			for(HashMap<String, Object> temp : dataList) {
				OutSystemTagNumber synObj = ObjUtils.convertToObject(OutSystemTagNumber.class, temp);
				if(synObj != null) { //数据读取成功
					String tagName = synObj.getTagName()==null?"":synObj.getTagName().trim();
					if(StringUtils.isNotEmpty(tagName)&&!hasDataMap.containsKey(tagName)) {
						hasDataMap.put(tagName, 1); //去掉重复记录
						synObj.setTagName(tagName);
						String activeChk = synObj.getActiveChk()==null?"":synObj.getActiveChk();
						if(synDelFlagValue.equals(activeChk)) { //删除
							activeChk = "F";
						}else {
							activeChk = "X";
						}
						if("X".equals(activeChk)) { //新增或修改
							synObj.setActiveChk(activeChk);
							if(StringUtils.isNotEmpty(queryMap)&&queryMap.containsKey(tagName)) {
								OutSystemTagNumber updObj = queryMap.get(tagName);
								String id = updObj.getId();
								BeanUtils.copyProperties(synObj, updObj); // 赋予返回对象
								updObj.setId(id);
								updList.add(updObj);
							}else {
								String id_syn = synObj.getId();
								if(StringUtils.isEmpty(id_syn)) {
									synObj.setId(TMUID.getUID());
								}
								maxPx += 1;
								synObj.setDataType(1); //lims指标
								synObj.setParentTagName("root");
								synObj.setTmSort(maxPx);
								addList.add(synObj);
							}
						}else { //删除
							if(StringUtils.isNotEmpty(queryMap)&&queryMap.containsKey(tagName)) {
								OutSystemTagNumber delObj = queryMap.get(tagName);
								delObj.setActiveChk("F");
								updList.add(delObj);
							}
						}
					}
				}else {
					result.add(temp);//记录到失败数据中
				}
			}
			if(StringUtils.isNotEmpty(addList)||StringUtils.isNotEmpty(updList)) {
				String ret = outSystemService.saveDataOutSystemTagNumber(addList, updList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
		}
		return result;
	}
	
	/**
	 *	获取数据Map
	 * @param list
	 * @param dataMap
	 */
	private void getDataMap(List<OutSystemTagNumber> list, HashMap<String, OutSystemTagNumber> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, OutSystemTagNumber>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				OutSystemTagNumber obj = list.get(i);
				String tagName = obj.getTagName()==null?"":obj.getTagName().trim();
				if(StringUtils.isNotEmpty(tagName)&&!dataMap.containsKey(tagName)) {
					dataMap.put(tagName, obj);
				}
			}
		}
	}
	
	@Override
	protected List<Object> getPullData(String whereSql, HashMap<String,String> synParam) {
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}

	
}
