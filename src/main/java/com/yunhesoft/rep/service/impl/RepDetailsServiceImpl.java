package com.yunhesoft.rep.service.impl;

import com.aliyuncs.utils.StringUtils;
import com.yunhesoft.core.common.utils.BigArithTools;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.joblist.operCard.entity.po.OpercardInfo;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcessStep;
import com.yunhesoft.rep.entity.dto.RepDetailsDto;
import com.yunhesoft.rep.entity.po.RepData;
import com.yunhesoft.rep.entity.po.RepDetails;
import com.yunhesoft.rep.entity.po.RepInfo;
import com.yunhesoft.rep.entity.vo.RepDetailsVo;
import com.yunhesoft.rep.entity.vo.RepInfoVo;
import com.yunhesoft.rep.service.IRepDetailsService;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.model.IDataSource;
import com.yunhesoft.system.tds.model.TDataSourceManager;
import com.yunhesoft.system.tds.service.IRtdbService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
public class RepDetailsServiceImpl implements IRepDetailsService {

    @Autowired
    private EntityService entityService;
    @Autowired
    private IRtdbService rtdbSrv; //实时数据
    @Autowired
    private IShiftService shiftSrv;
    @Override
    public List<RepDetails> queryList(RepDetailsDto dto) {
        Where where = new Where();
        if (dto != null) {
            //填充条件
            where.eq("repid",dto.getRepId());
        }
        Order order = new Order();
        order.orderByAsc(RepDetails::getTmsort);
        return entityService.queryData(RepDetails.class,where,order,null);
    }

    @Override
    public String saveData(List<RepDetails> data) {
        if (com.yunhesoft.core.utils.StringUtils.isEmpty(data)) {
            return "没有要保存的记录";
        }
        List<RepDetails> insertList = new ArrayList<>();
        List<RepDetails> updateList = new ArrayList<>();
        for (RepDetails step : data) {
            if(step.getSjly()==3){
                step.setScript("");
                step.setHqbc("");
            }
            if (com.yunhesoft.core.utils.StringUtils.isEmpty(step.getId())) {
                //新增
                step.setId(TMUID.getUID());
                insertList.add(step);
            } else {
                //修改
                updateList.add(step);
//                updateStepIdSet.add(step.getId());
            }
        }
        if (com.yunhesoft.core.utils.StringUtils.isNotEmpty(insertList)) {
            entityService.insertBatch(insertList, 1000);
        }
        if (com.yunhesoft.core.utils.StringUtils.isNotEmpty(updateList)) {
            entityService.updateByIdBatch(updateList);
        }
        return "";
    }

    @Override
    public Integer deleteData(List<RepDetails> data) {
        return entityService.deleteByIdBatch(data);
    }

    @Override
    public List<RepInfoVo> queryPreviewList(RepDetailsDto dto) {
//        List<String> tagCodes=new ArrayList<>();
//        List<String> dsCodes=new ArrayList<>();
        List<RepInfoVo> infoVoList=new ArrayList<>();
        Where where = new Where();
        where.eq("tmused",1);
        if (dto != null) {
            if(StringUtils.isNotEmpty(dto.getRepId())){
                //填充条件
                where.eq("id",dto.getRepId());
            }
            if(StringUtils.isNotEmpty(dto.getRepAlias())){
                //填充条件
                where.eq("repalias",dto.getRepAlias());
            }
        }
        //repInfo的数组
        List<RepInfo> list=entityService.queryData(RepInfo.class,where,new Order().orderByAsc(RepInfo::getTmsort),null);

        Map<String,List<RepDetails>> repDetailsMap;//报表详细map
        Order order = new Order();
        order.orderByAsc(RepDetails::getTmsort);
        //根据repid分组将repDetail数据存储到map中
        List<RepDetails> detailLists=entityService.queryData(RepDetails.class,null,order,null);
        if(com.yunhesoft.core.utils.StringUtils.isNotEmpty(detailLists)){
            repDetailsMap =  detailLists.stream().collect(Collectors.groupingBy(obj->obj.getRepId()==null?"":obj.getRepId()));//按分类id分组
        } else {
            repDetailsMap = null;
        }



        list.forEach(item->{
            RepInfoVo infoVo=new RepInfoVo();
            BeanUtils.copyProperties(item, infoVo); //赋予返回对象

            //获取已经保存的值从repData里
            Where whereData = new Where();
            whereData.eq("repid",item.getId());
            whereData.eq("bcdm",dto.getBcdm());
            whereData.eq("bzdm",dto.getBzdm());
            whereData.eq("xbsj",dto.getXbsj());

            List<RepData> dataList=entityService.queryData(RepData.class,whereData,new Order().orderByAsc(RepData::getTmsort),null);


            if(repDetailsMap!=null){
                List<RepDetailsVo> voList=new ArrayList<>();
                List<RepDetails> infoDetails=repDetailsMap.get(item.getId());
                //给详情数据里面的分类1，分类2，分类3没有值的数据赋值上面有值的值
                if(infoDetails!=null) {
                    String fl1 = "";
                    String fl2 = "";
                    String fl3 = "";
                    if(dataList!=null&&!dataList.isEmpty()){
                        for (int i = 0; i < infoDetails.size(); i++) {
                            RepDetails itemDetails = infoDetails.get(i);
                            itemDetails.setFl1(dataList.get(i).getFl1());
                            itemDetails.setFl2(dataList.get(i).getFl2());
                            RepDetailsVo vo = new RepDetailsVo();
                            BeanUtils.copyProperties(itemDetails, vo); //赋予返回对象
                            vo.setValue(dataList.get(i).getValue());
                            //获取数据源，实时数据值
                            voList.add(vo);
                        }

                    }else{
                        for (int i = 0; i < infoDetails.size(); i++) {
                            RepDetails itemDetails = infoDetails.get(i);

//                            if(itemDetails.getSjly()==2){
//                                tagCodes.add(itemDetails.getScript());
//                            }
//                            if(itemDetails.getSjly()==1){
//                                dsCodes.add(itemDetails.getScript());
//                            }
                            if (StringUtils.isNotEmpty(itemDetails.getFl1())) {
                                fl1 = itemDetails.getFl1();
                            } else {
                                itemDetails.setFl1(fl1);
                            }
                            if (StringUtils.isNotEmpty(itemDetails.getFl2())) {
                                fl2 = itemDetails.getFl2();
                            } else {
                                itemDetails.setFl2(fl2);
                            }
                            if (StringUtils.isNotEmpty(itemDetails.getFl3())) {
                                fl3 = itemDetails.getFl3();
                            } else {
                                itemDetails.setFl3(fl3);
                            }
                            RepDetailsVo vo = new RepDetailsVo();
                            BeanUtils.copyProperties(itemDetails, vo); //赋予返回对象
                            //是否带入上次数据（手工输入）
                            if (itemDetails.getIsGetLast() == 1&&itemDetails.getSjly()==3) {
                                Where whereDataScope = new Where();
                                whereDataScope.eq("repid",item.getId());
//                                whereDataScope.eq("bcdm",dto.getBcdm());
//                                whereDataScope.eq("bzdm",dto.getBzdm());
//                                whereDataScope.eq("xbsj",dto.getXbsj());
                                whereDataScope.eq("fl1",itemDetails.getFl1());
                                whereDataScope.eq("fl2",itemDetails.getFl2());
                                whereDataScope.eq("ms",itemDetails.getZbmc());
                                whereDataScope.eq("tmused",1);
                                whereDataScope.eq("tbrq",dto.getPeriod());
                                List<RepData> repDataList=entityService.queryData(RepData.class,whereDataScope,new Order().orderByDesc(RepData::getXbsj),null);
                                if(!com.yunhesoft.core.utils.StringUtils.isEmpty(repDataList)){
                                    vo.setValue(repDataList.get(0).getValue());
                                }else{
                                    vo.setValue("");
                                }
                            }

                            //获取数据源，实时数据值
                            voList.add(vo);
                        }


                        try {
                            List<ShiftForeignVo> shiftForeignVoList=shiftSrv.getShiftByOrgListDateTimeList(dto.getPeriod(), dto.getBzdm());
                            voList.forEach(itemVo->{
                                if(itemVo.getSjly()==2){
                                    List<String> tagCodes=new ArrayList<>();
                                    if(com.yunhesoft.core.utils.StringUtils.isEmpty(itemVo.getHqbc())){
                                        tagCodes.add(itemVo.getScript());
                                        //实时数据
                                        HashMap<String, TagData> tagMap=getTagValues(tagCodes,dto.getXbsj());
                                        itemVo.setValue(tagMap.get(itemVo.getScript())!=null?tagMap.get(itemVo.getScript()).getValue().toString(): "");
                                    }else{
                                        if(itemVo.getHqbc().equals("0")){
                                            tagCodes.add(itemVo.getScript());
                                            //实时数据
                                            HashMap<String, TagData> tagMap=getTagValues(tagCodes,dto.getXbsj());
                                            itemVo.setValue(tagMap.get(itemVo.getScript())!=null?tagMap.get(itemVo.getScript()).getValue().toString(): "");
                                        } else if(itemVo.getHqbc().equals("1")){
                                            if(com.yunhesoft.core.utils.StringUtils.isEmpty(shiftForeignVoList)){
                                                itemVo.setValue("");
                                            }else{
                                                tagCodes.add(itemVo.getScript());
                                                //实时数据
                                                HashMap<String, TagData> tagMap=getTagValues(tagCodes,shiftForeignVoList.get(0).getXbsj());
                                                itemVo.setValue(tagMap.get(itemVo.getScript())!=null?tagMap.get(itemVo.getScript()).getValue().toString(): "");
                                            }
                                        }else if(itemVo.getHqbc().equals("2")){
                                            if(com.yunhesoft.core.utils.StringUtils.isEmpty(shiftForeignVoList)){
                                                itemVo.setValue("");
                                            }else{
                                                tagCodes.add(itemVo.getScript());
                                                //实时数据
                                                HashMap<String, TagData> tagMap=getTagValues(tagCodes,shiftForeignVoList.get(shiftForeignVoList.size()-1).getXbsj());
                                                itemVo.setValue(tagMap.get(itemVo.getScript())!=null?tagMap.get(itemVo.getScript()).getValue().toString(): "");
                                            }

                                        }
                                    }

                                }
                                if(itemVo.getSjly()==1){
                                    List<String> dsCodes=new ArrayList<>();
                                    if(com.yunhesoft.core.utils.StringUtils.isEmpty(itemVo.getHqbc())){
                                        dsCodes.add(itemVo.getScript());
                                        //数据源数据
                                        Map<String, Object> dsDataMap= null;
                                        try {
                                            dsDataMap = getDsData(dsCodes,dto);
                                        } catch (Exception e) {
                                            throw new RuntimeException(e);
                                        }
                                        itemVo.setValue(dsDataMap.get(itemVo.getScript())!=null?dsDataMap.get(itemVo.getScript()).toString():"");
                                    }else{
                                        if(itemVo.getHqbc().equals("0")){
                                            dsCodes.add(itemVo.getScript());
                                            //数据源数据
                                            Map<String, Object> dsDataMap= null;
                                            try {
                                                dsDataMap = getDsData(dsCodes,dto);
                                            } catch (Exception e) {
                                                throw new RuntimeException(e);
                                            }
                                            itemVo.setValue(dsDataMap.get(itemVo.getScript())!=null?dsDataMap.get(itemVo.getScript()).toString():"");
                                        } else if(itemVo.getHqbc().equals("1")){
                                            if(com.yunhesoft.core.utils.StringUtils.isEmpty(shiftForeignVoList)){
                                                itemVo.setValue("");
                                            }else{
                                                dsCodes.add(itemVo.getScript());
                                                //数据源数据
                                                Map<String, Object> dsDataMap= null;
                                                RepDetailsDto dtoTemp = new RepDetailsDto();
                                                BeanUtils.copyProperties(dto, dtoTemp);
                                                dtoTemp.setXbsj(shiftForeignVoList.get(0).getXbsj());
                                                try {
                                                    dsDataMap = getDsData(dsCodes,dtoTemp);
                                                } catch (Exception e) {
                                                    throw new RuntimeException(e);
                                                }
                                                itemVo.setValue(dsDataMap.get(itemVo.getScript())!=null?dsDataMap.get(itemVo.getScript()).toString():"");
                                            }
                                        }else if(itemVo.getHqbc().equals("2")){
                                            if(com.yunhesoft.core.utils.StringUtils.isEmpty(shiftForeignVoList)){
                                                itemVo.setValue("");
                                            }else{
                                                dsCodes.add(itemVo.getScript());
                                                //数据源数据
                                                Map<String, Object> dsDataMap= null;
                                                RepDetailsDto dtoTemp = new RepDetailsDto();
                                                BeanUtils.copyProperties(dto, dtoTemp);
                                                dtoTemp.setXbsj(shiftForeignVoList.get(shiftForeignVoList.size()-1).getXbsj());
                                                try {
                                                    dsDataMap = getDsData(dsCodes,dtoTemp);
                                                } catch (Exception e) {
                                                    throw new RuntimeException(e);
                                                }
                                                itemVo.setValue(dsDataMap.get(itemVo.getScript())!=null?dsDataMap.get(itemVo.getScript()).toString():"");
                                            }

                                        }
                                    }
                                }
                            });
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }

                    }
                }
                infoVo.setDetails(voList);
            }
            infoVoList.add(infoVo);

        });
        return infoVoList;
    }

    @Override
    public List<RepInfoVo> handleExtract(RepDetailsDto dto) {
        List<RepInfoVo> infoVoList=new ArrayList<>();
        Where where = new Where();
        where.eq("tmused",1);
        if (dto != null) {
            if(StringUtils.isNotEmpty(dto.getRepId())){
                //填充条件
                where.eq("id",dto.getRepId());
            }
            if(StringUtils.isNotEmpty(dto.getRepAlias())){
                //填充条件
                where.eq("repalias",dto.getRepAlias());
            }
        }
        //repInfo的数组
        List<RepInfo> list=entityService.queryData(RepInfo.class,where,new Order().orderByAsc(RepInfo::getTmsort),null);

        Map<String,List<RepDetails>> repDetailsMap;//报表详细map
        Order order = new Order();
        order.orderByAsc(RepDetails::getTmsort);
        //根据repid分组将repDetail数据存储到map中
        List<RepDetails> detailLists=entityService.queryData(RepDetails.class,null,order,null);
        if(com.yunhesoft.core.utils.StringUtils.isNotEmpty(detailLists)){
            repDetailsMap =  detailLists.stream().collect(Collectors.groupingBy(obj->obj.getRepId()==null?"":obj.getRepId()));//按分类id分组
        } else {
            repDetailsMap = null;
        }



        list.forEach(item->{
            RepInfoVo infoVo=new RepInfoVo();
            BeanUtils.copyProperties(item, infoVo); //赋予返回对象

            if(repDetailsMap!=null){
                List<RepDetailsVo> voList=new ArrayList<>();
                List<RepDetails> infoDetails=repDetailsMap.get(item.getId());
                //给详情数据里面的分类1，分类2，分类3没有值的数据赋值上面有值的值
                if(infoDetails!=null) {
                    String fl1 = "";
                    String fl2 = "";
                    String fl3 = "";
                    if(false){
//                        for (int i = 0; i < infoDetails.size(); i++) {
//                            RepDetails itemDetails = infoDetails.get(i);
//                            itemDetails.setFl1(dataList.get(i).getFl1());
//                            itemDetails.setFl2(dataList.get(i).getFl2());
//                            RepDetailsVo vo = new RepDetailsVo();
//                            BeanUtils.copyProperties(itemDetails, vo); //赋予返回对象
//                            vo.setValue(dataList.get(i).getValue());
//                            //获取数据源，实时数据值
//                            voList.add(vo);
//                        }

                    }else{
                        for (int i = 0; i < infoDetails.size(); i++) {
                            RepDetails itemDetails = infoDetails.get(i);
                            if (StringUtils.isNotEmpty(itemDetails.getFl1())) {
                                fl1 = itemDetails.getFl1();
                            } else {
                                itemDetails.setFl1(fl1);
                            }
                            if (StringUtils.isNotEmpty(itemDetails.getFl2())) {
                                fl2 = itemDetails.getFl2();
                            } else {
                                itemDetails.setFl2(fl2);
                            }
                            if (StringUtils.isNotEmpty(itemDetails.getFl3())) {
                                fl3 = itemDetails.getFl3();
                            } else {
                                itemDetails.setFl3(fl3);
                            }
                            RepDetailsVo vo = new RepDetailsVo();
                            BeanUtils.copyProperties(itemDetails, vo); //赋予返回对象
                            //是否带入上次数据（手工输入）
                            if (itemDetails.getSjly()==3) {
                                //获取已经保存的值从repData里
                                Where whereData = new Where();
                                whereData.eq("repid",item.getId());
                                whereData.eq("bcdm",dto.getBcdm());
                                whereData.eq("bzdm",dto.getBzdm());
                                whereData.eq("xbsj",dto.getXbsj());

                                List<RepData> dataList=entityService.queryData(RepData.class,whereData,new Order().orderByAsc(RepData::getTmsort),null);
                                if(!com.yunhesoft.core.utils.StringUtils.isEmpty(dataList)){
                                    dataList.forEach(itemData->{
                                        if(itemData.getFl1().equals(vo.getFl1())&&itemData.getFl2().equals(vo.getFl2())&&itemData.getMs().equals(vo.getZbmc())){
                                            vo.setValue(itemData.getValue());
                                        }
                                    });

                                }else{
                                    if (itemDetails.getIsGetLast() == 1){
                                        Where whereDataScope = new Where();
                                        whereDataScope.eq("repid",item.getId());
//                                whereDataScope.eq("bcdm",dto.getBcdm());
//                                whereDataScope.eq("bzdm",dto.getBzdm());
//                                whereDataScope.eq("xbsj",dto.getXbsj());
                                        whereDataScope.eq("fl1",itemDetails.getFl1());
                                        whereDataScope.eq("fl2",itemDetails.getFl2());
                                        whereDataScope.eq("fl3",itemDetails.getFl3());
                                        whereDataScope.eq("ms",itemDetails.getZbmc());
                                        whereDataScope.eq("tmused",1);
                                        whereDataScope.eq("tbrq",dto.getPeriod());
                                        List<RepData> repDataList=entityService.queryData(RepData.class,whereDataScope,new Order().orderByDesc(RepData::getXbsj),null);
                                        if(!com.yunhesoft.core.utils.StringUtils.isEmpty(repDataList)){
                                            vo.setValue(repDataList.get(0).getValue());
                                        }else{
                                            vo.setValue("");
                                        }
                                    }else{
                                        vo.setValue("");
                                    }

                                }
                            }

                            //获取数据源，实时数据值
                            voList.add(vo);
                        }


                        try {
                            List<ShiftForeignVo> shiftForeignVoList=shiftSrv.getShiftByOrgListDateTimeList(dto.getPeriod(), dto.getBzdm());
                            voList.forEach(itemVo->{
                                if(itemVo.getSjly()==2){
                                    List<String> tagCodes=new ArrayList<>();
                                    if(com.yunhesoft.core.utils.StringUtils.isEmpty(itemVo.getHqbc())){
                                        tagCodes.add(itemVo.getScript());
                                        //实时数据
                                        HashMap<String, TagData> tagMap=getTagValues(tagCodes,dto.getXbsj());
                                        itemVo.setValue(tagMap.get(itemVo.getScript())!=null?tagMap.get(itemVo.getScript()).getValue().toString(): "");
                                    }else{
                                        if(itemVo.getHqbc().equals("0")){
                                            tagCodes.add(itemVo.getScript());
                                            //实时数据
                                            HashMap<String, TagData> tagMap=getTagValues(tagCodes,dto.getXbsj());
                                            itemVo.setValue(tagMap.get(itemVo.getScript())!=null?tagMap.get(itemVo.getScript()).getValue().toString(): "");
                                        } else if(itemVo.getHqbc().equals("1")){
                                            if(com.yunhesoft.core.utils.StringUtils.isEmpty(shiftForeignVoList)){
                                                itemVo.setValue("");
                                            }else{
                                                tagCodes.add(itemVo.getScript());
                                                //实时数据
                                                HashMap<String, TagData> tagMap=getTagValues(tagCodes,shiftForeignVoList.get(0).getXbsj());
                                                itemVo.setValue(tagMap.get(itemVo.getScript())!=null?tagMap.get(itemVo.getScript()).getValue().toString(): "");
                                            }
                                        }else if(itemVo.getHqbc().equals("2")){
                                            if(com.yunhesoft.core.utils.StringUtils.isEmpty(shiftForeignVoList)){
                                                itemVo.setValue("");
                                            }else{
                                                tagCodes.add(itemVo.getScript());
                                                //实时数据
                                                HashMap<String, TagData> tagMap=getTagValues(tagCodes,shiftForeignVoList.get(shiftForeignVoList.size()-1).getXbsj());
                                                itemVo.setValue(tagMap.get(itemVo.getScript())!=null?tagMap.get(itemVo.getScript()).getValue().toString(): "");
                                            }

                                        }
                                    }

                                }
                                if(itemVo.getSjly()==1){
                                    List<String> dsCodes=new ArrayList<>();
                                    if(com.yunhesoft.core.utils.StringUtils.isEmpty(itemVo.getHqbc())){
                                        dsCodes.add(itemVo.getScript());
                                        //数据源数据
                                        Map<String, Object> dsDataMap= null;
                                        try {
                                            dsDataMap = getDsData(dsCodes,dto);
                                        } catch (Exception e) {
                                            throw new RuntimeException(e);
                                        }
                                        itemVo.setValue(dsDataMap.get(itemVo.getScript())!=null?dsDataMap.get(itemVo.getScript()).toString():"");
                                    }else{
                                        if(itemVo.getHqbc().equals("0")){
                                            dsCodes.add(itemVo.getScript());
                                            //数据源数据
                                            Map<String, Object> dsDataMap= null;
                                            try {
                                                dsDataMap = getDsData(dsCodes,dto);
                                            } catch (Exception e) {
                                                throw new RuntimeException(e);
                                            }
                                            itemVo.setValue(dsDataMap.get(itemVo.getScript())!=null?dsDataMap.get(itemVo.getScript()).toString():"");
                                        } else if(itemVo.getHqbc().equals("1")){
                                            if(com.yunhesoft.core.utils.StringUtils.isEmpty(shiftForeignVoList)){
                                                itemVo.setValue("");
                                            }else{
                                                dsCodes.add(itemVo.getScript());
                                                //数据源数据
                                                Map<String, Object> dsDataMap= null;
                                                RepDetailsDto dtoTemp = new RepDetailsDto();
                                                BeanUtils.copyProperties(dto, dtoTemp);
                                                dtoTemp.setXbsj(shiftForeignVoList.get(0).getXbsj());
                                                try {
                                                    dsDataMap = getDsData(dsCodes,dtoTemp);
                                                } catch (Exception e) {
                                                    throw new RuntimeException(e);
                                                }
                                                itemVo.setValue(dsDataMap.get(itemVo.getScript())!=null?dsDataMap.get(itemVo.getScript()).toString():"");
                                            }
                                        }else if(itemVo.getHqbc().equals("2")){
                                            if(com.yunhesoft.core.utils.StringUtils.isEmpty(shiftForeignVoList)){
                                                itemVo.setValue("");
                                            }else{
                                                dsCodes.add(itemVo.getScript());
                                                //数据源数据
                                                Map<String, Object> dsDataMap= null;
                                                RepDetailsDto dtoTemp = new RepDetailsDto();
                                                BeanUtils.copyProperties(dto, dtoTemp);
                                                dtoTemp.setXbsj(shiftForeignVoList.get(shiftForeignVoList.size()-1).getXbsj());
                                                try {
                                                    dsDataMap = getDsData(dsCodes,dtoTemp);
                                                } catch (Exception e) {
                                                    throw new RuntimeException(e);
                                                }
                                                itemVo.setValue(dsDataMap.get(itemVo.getScript())!=null?dsDataMap.get(itemVo.getScript()).toString():"");
                                            }

                                        }
                                    }
                                }
                            });
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }

                    }
                }
                infoVo.setDetails(voList);
            }
            infoVoList.add(infoVo);

        });
        return infoVoList;
    }

    /**
     * 批量保存数据源实时数据
     * @param data
     * @return
     */
    @Override
    public Integer insert(List<RepInfoVo> data) {
        List<RepData> dataList=new ArrayList<>();
        data.forEach(item->{
            Where where = new Where();
            where.eq("repid",item.getId());
            entityService.rawDelete("delete from rep_data where repid=? and bcdm=? and bzdm=? and xbsj=?",item.getId(),item.getBcdm(),item.getBzdm(),item.getXbsj());


            if(item.getDetails()!=null&& !item.getDetails().isEmpty()){
                item.getDetails().forEach(itemDetails->{
                    RepData dataItem=new RepData();
                    dataItem.setId(UUID.randomUUID().toString());
                    dataItem.setRepId(item.getId());
                    dataItem.setOrgCode(item.getOrgCode());
                    dataItem.setTbrq(item.getTbrq());
                    dataItem.setBzdm(item.getBzdm());
                    dataItem.setBzmc(item.getBzmc());
                    dataItem.setBcdm(item.getBcdm());
                    dataItem.setBcmc(item.getBcmc());
                    dataItem.setSbsj(item.getSbsj());
                    dataItem.setXbsj(item.getXbsj());
                    dataItem.setFl1(itemDetails.getFl1());
                    dataItem.setFl2(itemDetails.getFl2());
                    dataItem.setMs(itemDetails.getZbmc());
                    dataItem.setValue(itemDetails.getValue());
                    dataItem.setTmused(1);
                    dataItem.setTmsort(itemDetails.getTmsort());
                    dataList.add(dataItem);
                });
            }

        });
        return entityService.insertBatch(dataList,500);
    }

    /**
     * 获取实时数据值
     * @category 获取实时数据值
     * <AUTHOR>
     * @param tagCode 实时数据仪表列表
     * @return
     */
    private HashMap<String, TagData> getTagValues(List<String> tagCodes, String ctime) {
        HashMap<String,TagData> result = new HashMap<String,TagData>();
        if(com.yunhesoft.core.utils.StringUtils.isNotEmpty(tagCodes)) {
//            Date nowDt = DateTimeUtils.getNowDate();
//            Date nowDt_start = DateTimeUtils.doMinute(nowDt, -5);//这里往前取5分钟，防止服务器时间与程序时间不一致
            List<Tag> tagList = null;
            try {
                tagList = rtdbSrv.queryRtdbTagData(tagCodes,ctime, ctime, 60);//1分钟1个点
            }catch(Exception e) {
                log.error("", e);
            }
            if(com.yunhesoft.core.utils.StringUtils.isNotEmpty(tagList)) {
                for(Tag temp:tagList) {
                    if(com.yunhesoft.core.utils.StringUtils.isNotEmpty(temp.getTagCode()) && com.yunhesoft.core.utils.StringUtils.isNotEmpty(temp.getDatas())) {
                        result.put(temp.getTagCode(), temp.getDatas().get(temp.getDatas().size()-1));//全大写仪表位号,取最后一个数据
                    }
                }
            }
        }
        return result;
    }

    /**
     * 获取数据源实时数据值
     * @category 获取数据源实时数据值
     * <AUTHOR>
     * @param tagCode 仪表列表
     * @return
     */
    private Map<String,Object> getDsData(List<String> tdsList, RepDetailsDto info) throws Exception {
        if(com.yunhesoft.core.utils.StringUtils.isNotEmpty(tdsList)){//有公式值
            Map<String,Object> tdsParams =new HashMap<String,Object>();
            Map<String, String> tdsInparam = getInParasValuesMap(info);
            HashMap<String, IDataSource> publicIdsMap = new HashMap<String, IDataSource>();
            TDataSourceManager tdsManager = new TDataSourceManager();
            for(String tdsFormula:tdsList) {
                Map<String, IDataSource> loadMap = tdsManager.loadScriptDs(tdsFormula, tdsInparam, publicIdsMap);
                if(com.yunhesoft.core.utils.StringUtils.isNotEmpty(loadMap)) {
                    Object execVal = tdsManager.execTds(tdsFormula, loadMap);//加载数据源
                    Double dVal = ObjectToDouble(execVal,null);
                    tdsParams.put(tdsFormula, dVal);//记录解析值
                }
            }
            return tdsParams;
        }
        return null;
    }

    private Map<String, String> getInParasValuesMap(RepDetailsDto infoVo) {
        Map<String, String> mapInParasValues = new HashMap<String, String>();// 参数map
        // 通用变量参数 数据源参数
        mapInParasValues.put("sbsj", infoVo.getSbsj());
        mapInParasValues.put("xbsj", infoVo.getXbsj());
        mapInParasValues.put("bcmc", infoVo.getBcmc());
        mapInParasValues.put("bcdm", infoVo.getBcdm());
        mapInParasValues.put("bzmc", infoVo.getBzmc());
        mapInParasValues.put("bzdm", infoVo.getBzdm());
        mapInParasValues.put("zbmc", infoVo.getZbmc());//机构代码
        mapInParasValues.put("orgcode", infoVo.getOrgCode());//机构代码
        mapInParasValues.put("orgid", infoVo.getOrgCode());//机构代码
        mapInParasValues.put("tbrq", infoVo.getPeriod());//机构代码
        mapInParasValues.put("rq", infoVo.getPeriod());//机构代码
        return mapInParasValues;
    }


    private static boolean judgeDouble(Object val) {
//		return Coms.judgeDouble(val);
        if (Coms.judgeDouble(val)) {
            return true;
        } else {
            String so = String.valueOf(val);
            if (Coms.isFind(so, "^-?\\d+\\.?\\d+[E][-]?\\d+$")) {// 是科学计数法
                return true;
            } else {
                return false;
            }
        }
    }

    private Double ObjectToDouble(Object value,Integer scale) {
        Double result = null;
        if(value!=null) {
            if(value instanceof ArrayList){
                if(judgeDouble(((ArrayList<?>) value).get(0))) {//如果是数值，转换整double
                    try{result = Double.parseDouble(String.valueOf(((ArrayList<?>) value).get(0)));}catch(Exception e) {log.error(e);}
                    if(scale!=null) {//需要处理小数位
                        result= BigArithTools.round(result, scale.intValue());//保留小数
                    }
                }
            }else{
                if(judgeDouble(value)) {//如果是数值，转换整double
                    try{result = Double.parseDouble(String.valueOf(value));}catch(Exception e) {log.error(e);}
                    if(scale!=null) {//需要处理小数位
                        result= BigArithTools.round(result, scale.intValue());//保留小数
                    }
                }
            }

        }
        if(result==null) {
            result=0d;//没数就返回0
        }
        return result;
    }
}
