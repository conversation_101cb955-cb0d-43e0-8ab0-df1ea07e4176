package com.yunhesoft.rep.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.joblist.entity.dto.JobFinishDto;
import com.yunhesoft.joblist.entity.dto.JoblistInputDto;
import com.yunhesoft.joblist.entity.vo.JoblistActivityExampleVo;
import com.yunhesoft.joblist.module.IJobQueryInterface;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataQueryDto;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIDataSimpleVo;
import com.yunhesoft.joblist.workInstruction.service.impl.WIDataServiceImpl;
import com.yunhesoft.rep.entity.dto.RepInfoDto;
import com.yunhesoft.rep.entity.po.RepData;
import com.yunhesoft.rep.entity.po.RepInfo;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class RepQueryServiceImpl implements IJobQueryInterface {

    @Autowired
    private RepInfoServiceImpl repInfoService;
    @Autowired
    private EntityService entityService;
    @Override
    public JobFinishDto getFinishCase(JoblistInputDto param) {
        RepInfoDto dto = this.getQueryDtoByParam(param);
        int total = 0;
        int finished = 0;
        int unfinish = 0;
        List<RepInfo> list = SpringUtils.getBean(RepInfoServiceImpl.class).queryList(dto);
        if (StringUtils.isNotEmpty(list)) {
            for(int i=0;i<list.size();i++){
                Where where = new Where();
                where.eq(RepData::getRepId, list.get(i).getId());
                List<RepData> dataList=entityService.queryList(RepData.class,where);
                if (StringUtils.isNotEmpty(dataList)){
                    finished=finished+1;
                }
            }
            total = list.size();
            unfinish = total - finished;
        }
        JobFinishDto result = new JobFinishDto();
        result.setTotal(total);
        result.setUnfinish(unfinish);
        result.setFinished(finished);
        return result;
    }

    private RepInfoDto getQueryDtoByParam(JoblistInputDto param) {
        RepInfoDto dto = new RepInfoDto();
        dto.setOrgCode(param.getOrgId());;
        return dto;
    }

    @Override
    public List<JoblistActivityExampleVo> getJobList(JoblistInputDto param) {
        RepInfoDto dto = this.getQueryDtoByParam(param);
        List<RepInfo> list = SpringUtils.getBean(RepInfoServiceImpl.class).queryList(dto);
        if (StringUtils.isEmpty(list)) {
            return null;
        }
        List<JoblistActivityExampleVo> result = new ArrayList<>();
        for (RepInfo vo : list) {
            JoblistActivityExampleVo bean = new JoblistActivityExampleVo();
            bean.setId(vo.getId());
            bean.setActivityName(vo.getRepName());
            bean.setModuleAlias("rep");
            bean.setRowObject(JSONObject.from(vo));
            //bean.setFinished(1);
            //bean.setTotal(2);
            result.add(bean);
        }
        return result;
    }
}
