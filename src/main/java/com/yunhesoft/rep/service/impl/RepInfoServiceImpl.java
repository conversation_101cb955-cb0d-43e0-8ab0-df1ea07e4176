package com.yunhesoft.rep.service.impl;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.rep.entity.dto.RepInfoDto;
import com.yunhesoft.rep.entity.po.RepDetails;
import com.yunhesoft.rep.entity.po.RepInfo;
import com.yunhesoft.rep.service.IRepInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
public class RepInfoServiceImpl implements IRepInfoService {

    @Autowired
    private EntityService entityService;

    @Override
    public List<RepInfo> queryList(RepInfoDto dto) {
        Where where = new Where();
        if (dto.getOrgCode() != null) {
            //填充条件
            where.eq("orgcode",dto.getOrgCode());
        }
        return entityService.queryList(RepInfo.class,where);
    }

    /**
     * 保存数据
     * @param data
     * @return
     */
    @Override
    public String saveData(List<RepInfo> data) {
        if (com.yunhesoft.core.utils.StringUtils.isEmpty(data)) {
            return "没有要保存的记录";
        }
        List<RepInfo> insertList = new ArrayList<>();
        List<RepInfo> updateList = new ArrayList<>();
        for (RepInfo step : data) {
            if (com.yunhesoft.core.utils.StringUtils.isEmpty(step.getId())) {
                //新增
                step.setId(TMUID.getUID());
                step.setTmused(1);
                insertList.add(step);
            } else {
                //修改
                updateList.add(step);
//                updateStepIdSet.add(step.getId());
            }
        }
        if (com.yunhesoft.core.utils.StringUtils.isNotEmpty(insertList)) {
            entityService.insertBatch(insertList, 1000);
        }
        if (com.yunhesoft.core.utils.StringUtils.isNotEmpty(updateList)) {
            entityService.updateByIdBatch(updateList);
        }
        return "";
    }

    /**
     * 删除数据
     * @param data
     * @return
     */
    @Override
    public Integer deleteData(List<RepInfo> data) {
        return entityService.deleteByIdBatch(data);
    }
}
