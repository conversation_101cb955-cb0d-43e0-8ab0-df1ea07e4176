package com.yunhesoft.rep.service;

import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataDto;
import com.yunhesoft.rep.entity.dto.RepInfoDto;
import com.yunhesoft.rep.entity.po.RepInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IRepInfoService {

    /**
     * 查询报告模板
     * @param dto
     * @return
     */
    List<RepInfo> queryList(RepInfoDto dto);

    /**
     * 添加报告模板
     * @param data
     * @return
     */
    String saveData(List<RepInfo> data);

    /**
     * 删除报告模板
     * @param data
     * @return
     */
    Integer deleteData(List<RepInfo> data);
}
