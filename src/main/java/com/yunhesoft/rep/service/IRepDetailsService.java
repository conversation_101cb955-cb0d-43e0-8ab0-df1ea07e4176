package com.yunhesoft.rep.service;

import com.yunhesoft.rep.entity.dto.RepDetailsDto;
import com.yunhesoft.rep.entity.dto.RepInfoDto;
import com.yunhesoft.rep.entity.po.RepDetails;
import com.yunhesoft.rep.entity.po.RepInfo;
import com.yunhesoft.rep.entity.vo.RepDetailsVo;
import com.yunhesoft.rep.entity.vo.RepInfoVo;

import java.util.List;

public interface IRepDetailsService {
    /**
     * 查询报告详情
     * @param dto
     * @return
     */
    List<RepDetails> queryList(RepDetailsDto dto);

    /**
     * 添加报告详情
     * @param data
     * @return
     */
    String saveData(List<RepDetails> data);

    /**
     * 删除报告详情
     * @param data
     * @return
     */
    Integer deleteData(List<RepDetails> data);

    /**
     * 查询报告详情
     * @param dto
     * @return
     */
    List<RepInfoVo> queryPreviewList(RepDetailsDto dto);

    /**
     * 重新获取
     * @param dto
     * @return
     */
    List<RepInfoVo> handleExtract(RepDetailsDto dto);

    /**
     * 添加报告数据
     * @param data
     * @return
     */
    Integer insert(List<RepInfoVo> data);
}
