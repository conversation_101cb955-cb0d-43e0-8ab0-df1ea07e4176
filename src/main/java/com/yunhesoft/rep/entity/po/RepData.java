package com.yunhesoft.rep.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ApiModel(value = "报表数据表")
@Getter
@Setter
@Entity
@Table(name = "REP_DATA")
public class RepData extends BaseEntity{

    /**
     * 报表id
     */
    @ApiModelProperty(value = "报表id")
    @Column(name = "REPID", length = 50)
    private String repId;
    /**
     * 机构编码 rep_info.orgcode
     */
    @ApiModelProperty(value = "机构编码")
    @Column(name = "ORGCODE", length = 50)
    private String orgCode;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @Column(name = "TBRQ", length = 10)
    private String tbrq;
    /**
     * 班次代码
     */
    @ApiModelProperty(value = "班次代码")
    @Column(name = "BCDM", length = 50)
    private String bcdm;
    /**
     * 班次代码
     */
    @ApiModelProperty(value = "班次名称")
    @Column(name = "BCMC", length = 50)
    private String bcmc;
    /**
     * 班组代码
     */
    @ApiModelProperty(value = "班组代码")
    @Column(name = "BZDM", length = 50)
    private String bzdm;
    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称")
    @Column(name = "BZMC", length = 50)
    private String bzmc;
    /**
     * 分类
     */
    @ApiModelProperty(value = "分类")
    @Column(name = "FL1", length = 50)
    private String fl1;
    /**
     * 项目
     */
    @ApiModelProperty(value = "项目")
    @Column(name = "FL2", length = 50)
    private String fl2;
    /**
     * 项目
     */
    @ApiModelProperty(value = "项目")
    @Column(name = "FL3", length = 50)
    private String fl3;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Column(name = "MS", length = 2000)
    private String ms;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    @Column(name = "VALUE", length = 255)
    private String value;
    /**
     * 上班时间
     */
    @ApiModelProperty(value = "上班时间")
    @Column(name = "SBSJ", length = 50)
    private String sbsj;
    /**
     * 下班时间
     */
    @ApiModelProperty(value = "下班时间")
    @Column(name = "XBSJ", length = 50)
    private String xbsj;

    /**
     * 是否使用 1：使用；0：删除
     */
    @ApiModelProperty(value = "是否使用")
    @Column(name = "TMUSED", length = 20)
    private int tmused;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @Column(name = "TMSORT", length = 20)
    private int tmsort;
}
