package com.yunhesoft.rep.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.rep.entity.dto.RepDetailsDto;
import com.yunhesoft.rep.entity.dto.RepInfoDto;
import com.yunhesoft.rep.entity.po.RepDetails;
import com.yunhesoft.rep.entity.po.RepInfo;
import com.yunhesoft.rep.entity.vo.RepInfoVo;
import com.yunhesoft.rep.service.IRepDetailsService;
import com.yunhesoft.rep.service.IRepInfoService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "报表详细信息相关函数")
@RestController
@RequestMapping("/rep/repDetails")
public class RepDetailsController extends BaseRestController {

    @Autowired
    private IRepDetailsService srv;
    @ApiOperation("查询报表详情模型")
    @RequestMapping(value = "/queryList", method = {RequestMethod.POST})
    public Res<?> queryList(@RequestBody RepDetailsDto param) {
        Pagination<?> page = null;
        if (param.getPageSize() > 0) {
            page = Pagination.create(param.getPageNum(), param.getPageSize());
        }
        Res res = Res.OK(srv.queryList(param));
        if (page != null) {
            res.setTotal(page.getTotal());
        }
        return res;
    }
    @ApiOperation("保存报表详情")
    @RequestMapping(value = "/batchSave", method = {RequestMethod.POST})
    public Res<?> batchSave(@RequestBody List<RepDetails> param) {
        Res res = Res.OK(srv.saveData(param));
        return res;
    }

    @ApiOperation("删除报表详情")
    @RequestMapping(value = "/batchDelete", method = {RequestMethod.POST})
    public Res<?> batchDelete(@RequestBody List<RepDetails> param) {
        Res res = Res.OK(srv.deleteData(param));
        return res;
    }

    @ApiOperation("查询报表预览模型")
    @RequestMapping(value = "/queryPreviewList", method = {RequestMethod.POST})
    public Res<?> queryPreviewList(@RequestBody RepDetailsDto param) {
        Res res = Res.OK(srv.queryPreviewList(param));
        return res;
    }

    @ApiOperation("保存报表数据")
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public Res<?> insert(@RequestBody List<RepInfoVo> param) {
        Res res = Res.OK(srv.insert(param));
        return res;
    }

    @ApiOperation("重新提取")
    @RequestMapping(value = "/handleExtract", method = {RequestMethod.POST})
    public Res<?> handleExtract(@RequestBody RepDetailsDto param) {
        Res res = Res.OK(srv.handleExtract(param));
        return res;
    }
}
