plugins {
    id 'org.springframework.boot' version '2.3.12.RELEASE'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'java'
    id 'idea'
}
group = 'com.yunhesoft.tm4'
version = '0.0.1.beta.23'
sourceCompatibility = '1.8'

ext {
    //set('springCloudVersion', "2020.0.0")
    set('springCloudVersion', "Hoxton.SR12")
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

configurations {
	developmentOnly
	runtimeClasspath {
		extendsFrom developmentOnly
	}
	compileOnly {
		extendsFrom annotationProcessor
	}
}
tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}
//声明url地址
def baseUrl = "http://developer.yunhesoft.net:8081/repository/"
def nexusUsername = 'tm4dev'
def nexusPassword = 'YH2019@bzhs!*6'
repositories {
    //mavenLocal()
    maven{
		url baseUrl+"/tm4-group/"
		credentials {
			username nexusUsername
			password nexusPassword
		}
	}
    maven { url "https://maven.aliyun.com/repository/gradle-plugin"}
}

gradle.projectsEvaluated {
    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation"
    }
}
//全局排除spring boot 自带的日志
configurations {
	compile.exclude group:'org.springframework.boot',module:'spring-boot-starter-logging'
}

//普通依赖包
def implementationItems = [
        'com.oracle.database.jdbc:ojdbc8:********',
        'com.oracle.database.nls:orai18n:********',
        'com.yunhesoft.gmsg:tm4-gmsg-center-client-java:0.0.6',
        'com.yunhesoft.gmsg:tm4-gmsg-center-core:0.0.1',
        //'com.yunhesoft.gmsg:tm4-gmsg-center-client-java:0.0.2',
        //'com.yunhesoft.gmsg:tm4-gmsg-center-core:0.0.1',
        'com.yunhesoft.tm4:tm4-system:0.0.252-saas.422',
        'com.yunhesoft.tm4:tm4-comm-evaluation:0.0.1.beta.18',
       //'com.yunhesoft.tm4:tm4-system:0.0.252-saas.400',
		//log4j
		'org.apache.logging.log4j:log4j-core:2.17.1',
		'org.apache.logging.log4j:log4j-api:2.17.1',
		'org.apache.logging.log4j:log4j-jul:2.17.1',
		'org.apache.logging.log4j:log4j-slf4j-impl:2.17.1',
        //'com.yunhesoft.tm4:tm4-tools:0.0.19',
        //工作流组件
        //'com.yunhesoft.tm4:tm4-comm-flow:0.0.68',
        //'com.yunhesoft.tm4:tm4-comm-tmsf:0.0.53',
//        'com.yunhesoft.tm4:tm4-comm-flow:0.0.76',
        'com.yunhesoft.tm4:tm4-comm-tmsf:0.0.80.gem.test1',
        //倒班
        'com.yunhesoft.tm4:tm4-comm-shift:0.0.32',
        //'com.yunhesoft.tm4:tm4-tools:0.0.14',

        //多主题目标传导
        //'com.yunhesoft.tm4:tm4-comm-mtm:0.0.59.saas.72',
        //PDF
	'com.itextpdf:itextpdf:5.5.11',
	'com.itextpdf:itext-asian:5.2.0',
	'com.itextpdf.tool:xmlworker:5.5.11',
        //竞赛
        'com.yunhesoft.tm4:tm4-comm-race:0.0.12',

        //短信服务（webapi）
        'com.yunhesoft.tm4:tm4-comm-sms:0.0.1.GDSH.003',

        //对外提供待办服务
        'com.yunhesoft.tm4:tm4-comm-openserv:0.0.8.GDSH',
        //单点登录
        'com.yunhesoft.tm4:tm4-comm-sso:0.0.26',
        //广东工作流集成
        'com.yunhesoft.tm4:tm4-comm-extrinsicflow:0.0.21',

        //大屏功能
        //'com.yunhesoft.tm4:tm4-comm-datav:0.0.1.saas.09',

        //R3DB客户端
        'com.yunhesoft.rtdb:tm4-rtdb-client-java:0.0.13',

        //人工智能（人脸、文字识别、语音识别）
        'com.yunhesoft.tm4:tm4-comm-aip:0.0.36',

        //任务督办
        'com.yunhesoft.tm4:tm4-comm-task:0.0.3.saas13.test60',
		//TM4日考核
		//'com.yunhesoft.tm4:tm4-comm-asscenter:*******',
	    //检查审核
	    'com.yunhesoft.tm4:tm4-comm-checkwork:0.0.39',


        'org.apache.tomcat.embed:tomcat-embed-core:9.0.106',
        'org.apache.tomcat.embed:tomcat-embed-el:9.0.106',
        'org.apache.tomcat.embed:tomcat-embed-websocket:9.0.106',

        //actuator
        //'org.springframework.boot:spring-boot-starter-actuator:2.2.9.RELEASE',
        'org.springframework.boot:spring-boot-starter-actuator',
        //admin
        'de.codecentric:spring-boot-admin-starter-client:2.3.1'
]


dependencies {

    //引入普通依赖包
    implementationItems.each { dep ->
        implementation(dep)
    }
//     implementation ('com.yunhesoft.tm4:tm4-comm-asscenter-sdk:0.0.1.beta.03'){
//            exclude group: 'org.springframework.boot', module: 'spring-boot-starter-security'
//            exclude group: 'org.springframework.security', module: 'spring-security-core'
//            exclude group: 'com.yunhesoft.tm4', module: 'tm4-system'
//     }
//     implementation ('com.yunhesoft.tm4:tm4-comm-prize-sdk:0.0.1.beta.03'){
//            exclude group: 'org.springframework.boot', module: 'spring-boot-starter-security'
//            exclude group: 'org.springframework.security', module: 'spring-security-core'
//            exclude group: 'com.yunhesoft.tm4', module: 'tm4-system'
//     }
	//广东石化PaaS平台相关=============================================================================================
	//implementation 'org.springframework.cloud:spring-cloud-dependencies:Hoxton.SR12'
	//implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client:2.2.9.RELEASE'
	implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
	implementation 'org.springframework.cloud:spring-cloud-starter-openfeign:2.2.9.RELEASE'
	//implementation 'org.springframework.boot:spring-boot-starter-actuator'
	//implementation 'com.ctrip.framework.apollo:apollo-client:2.0.0'
	//===============================================================================================

	//implementation 'com.oracle.database.jdbc:ojdbc8:********'
    //implementation 'com.oracle.database.nls:orai18n:********'
    //
	//implementation 'com.yunhesoft.tm4:tm4-system:0.0.252-saas.222'
	////工作流组件
	//implementation 'com.yunhesoft.tm4:tm4-comm-flow:0.0.68'
	//implementation 'com.yunhesoft.tm4:tm4-comm-tmsf:0.0.51'
	////倒班
	//implementation 'com.yunhesoft.tm4:tm4-comm-shift:0.0.27'
	////implementation 'com.yunhesoft.tm4:tm4-system:0.0.112'
	//implementation 'com.yunhesoft.tm4:tm4-tools:0.0.14'
    //
    ////多主题目标传导
    //implementation 'com.yunhesoft.tm4:tm4-comm-mtm:0.0.59.saas.58'
    //
    ////短信服务（webapi）
    //implementation 'com.yunhesoft.tm4:tm4-comm-sms:0.0.1.GDSH.003'
    //
    //
    ////对外提供待办服务
	//implementation 'com.yunhesoft.tm4:tm4-comm-openserv:0.0.8.GDSH'
	////单点登录
    //implementation 'com.yunhesoft.tm4:tm4-comm-sso:0.0.15'
    ////广东工作流集成
    //implementation 'com.yunhesoft.tm4:tm4-comm-extrinsicflow:0.0.21'
    //
    ////大屏功能
    //implementation 'com.yunhesoft.tm4:tm4-comm-datav:0.0.1.saas.09'
    //
    ////R3DB客户端
    //implementation 'com.yunhesoft.rtdb:tm4-rtdb-client-java:0.0.6'
    //
    ////人工智能（人脸、文字识别、语音识别）
    //implementation 'com.yunhesoft.tm4:tm4-comm-aip:0.0.36'
    //
    ////任务督办
    //implementation 'com.yunhesoft.tm4:tm4-comm-task:0.0.3.saas13.test16'

    //implementation ('org.springframework.boot:spring-boot-starter-web:2.3.4.RELEASE'){
    implementation ('org.springframework.boot:spring-boot-starter-web'){
		exclude group: 'org.springframework.boot', module: 'spring-boot-starter-tomcat'
	}
	//implementation ('org.springframework.boot:spring-boot-starter-quartz:2.3.2.RELEASE'){
	implementation ('org.springframework.boot:spring-boot-starter-quartz'){
		exclude group: 'org.springframework.boot', module: 'spring-boot-starter-quartz'
	}
    implementation 'junit:junit:4.13.1'
    implementation 'junit:junit:4.13.1'

    //implementation 'org.apache.tomcat.embed:tomcat-embed-core:9.0.91'
    //implementation 'org.apache.tomcat.embed:tomcat-embed-el:9.0.91'
    //implementation 'org.apache.tomcat.embed:tomcat-embed-websocket:9.0.91'

    compileOnly 'org.projectlombok:lombok:1.18.20'
    annotationProcessor 'org.projectlombok:lombok:1.18.20'

    //testImplementation('org.springframework.boot:spring-boot-starter-test:2.3.4.RELEASE') {
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
}

test {
	useJUnitPlatform()
}

//打包并发布到nexus3服务器
apply plugin:'maven-publish'

//声明非启动模式的jar包
task clientJar(type:Jar) {
	//打包成jar时，必须设置为true
	enabled = true
	//基础名称为项目的名称
	baseName = project.name
    //打包的class文件位置
    from('build/classes/java/main/').include('**/*.class')
    //需要加入的资源那文件
    from('src/main/resources/').include('documents/**/*.md')
	//打包时不包含的class文件
	exclude('com/yunhesoft/*/Tm4CostApplication*.class')
	exclude('com/yunhesoft/*/Tm4Application*.class')
	//打包class文件放到jar包中的哪个目录下
	into('')
}
task clientSourceJar(type: Jar) {
    from sourceSets.main.allJava
    classifier = 'sources'
}
publishing {
    publications {
    	//发布一个jar包
        //mavenBootJar(MavenPublication) {
        //    //指定group/artifact/version信息，可以不填。默认使用项目group/name/version作为groupId/artifactId/version
        //    groupId project.group
        //    artifactId project.name
        //    version project.version
        //    //artifact标识，bootJar是一个生成jar的任务
        //    artifact bootJar
       	//}
        mavenClientJar(MavenPublication) {
            //指定group/artifact/version信息，可以不填。默认使用项目group/name/version作为groupId/artifactId/version
            groupId project.group
            //artifactId project.name+'-client'
            //tm4-core是一个纯的供其它项目使用的依赖包，因此，需要打成不包含其它内容的包
            artifactId project.name
            version project.version
            artifact clientJar
            //包含源文件
            artifact clientSourceJar
            //pom.withXml {
		    //	def dependenciesNode = asNode().appendNode('dependencies')
		    //	def dependencyNode1 = dependenciesNode.appendNode('dependency')
		    //   dependencyNode1.appendNode('groupId', 'com.alibaba')
		    //    dependencyNode1.appendNode('artifactId', 'fastjson')
		    //    dependencyNode1.appendNode('version', '1.2.66')
            //}

            pom.withXml {
                def dependenciesNode = asNode().appendNode('dependencies')
                def dependencyNode

                //将普通依赖包配置整合至发布包的pom.xml中
                implementationItems.each { dep ->
                    def parts = dep.split(':').collect { it.trim() }
                    def groupId = parts[0]
                    def artifactId = parts[1]
                    def version
                    if (parts.size() == 3) {
                        version = parts[2]
                    }

                    dependencyNode = dependenciesNode.appendNode('dependency')
                    dependencyNode.appendNode('groupId', groupId)
                    dependencyNode.appendNode('artifactId', artifactId)
                    if (parts.size() == 3) {
                        dependencyNode.appendNode('version', version)
                    }
                }

                //将项目组件依赖包配置整合至发布包的pom.xml中
                dependencyNode = dependenciesNode.appendNode('dependency')
                dependencyNode.appendNode('groupId', 'org.springframework.boot')
                dependencyNode.appendNode('artifactId', 'spring-boot-starter-web')
                dependencyNode.appendNode('version', '2.3.4.RELEASE')

                dependencyNode = dependenciesNode.appendNode('dependency')
                dependencyNode.appendNode('groupId', 'org.springframework.boot')
                dependencyNode.appendNode('artifactId', 'spring-boot-starter-quartz')
                dependencyNode.appendNode('version', '2.3.2.RELEASE')
            }
       	}
    }
    repositories {
        maven {
            //指定要上传的maven私服仓库
            url = baseUrl+"/tm4-public/"
            //认证用户和密码
            credentials {
				username nexusUsername
				password nexusPassword
            }
        }
    }
}
