
/**
* 此函数用于解决sqlserver 数据库 in 语句过多的问题
* 如果in 参数大于2000时，使用此函数
* 此功能主要针对岗位工作清单
* by x.zhong 2025.5.19
*/

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

create FUNCTION [dbo].[F_SplitString]
(
    @input NVARCHAR(MAX),
    @delimiter CHAR(1)
)
RETURNS @output TABLE (id NVARCHAR(MAX))
AS
BEGIN
    DECLARE @start INT, @end INT;
    SELECT @start = 1, @end = CHARINDEX(@delimiter, @input);
    
    WHILE @start < LEN(@input) + 1
    BEGIN
        IF @end = 0 
            SET @end = LEN(@input) + 1;

        INSERT INTO @output (id)
        VALUES(SUBSTRING(@input, @start, @end - @start));

        SET @start = @end + 1;
        SET @end = CHARINDEX(@delimiter, @input, @start);
    END

    RETURN;
END;
GO
