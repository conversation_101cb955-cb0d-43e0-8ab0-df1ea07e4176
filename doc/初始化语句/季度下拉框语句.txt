/**
*季度选择下拉框
$Sys_quarter.getColValues("q")
$Sys_quarter.getColValues("name")
*/

-- 1.kingbase============================================================

SELECT  '0'|| EXTRACT(QUARTER FROM CURRENT_DATE) AS q ,EXTRACT(QUARTER FROM CURRENT_DATE) AS qnum , 0 as sort ,'第'|| EXTRACT(QUARTER FROM CURRENT_DATE) || '季度' as name 
union 
SELECT * from (
SELECT '01' as q , 1 as qnum, 1 as sort,'第1季度' as name  union  SELECT '02',2,1,'第2季度' union  SELECT '03',3,1,'第3季度' union   SELECT '04',4,1,'第4季度'
) tab  where tab.qnum <>EXTRACT(QUARTER FROM CURRENT_DATE)
order by sort,q 

-- 2.sqlserver============================================================

SELECT '0'+ convert(varchar,DATEPART(QUARTER, GETDATE())) AS q,DATEPART(QUARTER, GETDATE()) as qnum,  0 as sort ,'第'+ convert(varchar,DATEPART(QUARTER, GETDATE())) + '季度' as name 
union 
SELECT * from (
SELECT '01' as q ,1 as qnum,1 as sort,'第1季度' as name   union  SELECT '02',2,1,'第2季度' union  SELECT '03',3,1,'第3季度' union   SELECT '04',4,1,'第4季度'
) tab  where tab.qnum <> DATEPART(QUARTER, GETDATE())
order by sort,q 

-- 3.mysql============================================================

SELECT CONCAT('0', QUARTER(CURRENT_DATE())) AS q,QUARTER(CURRENT_DATE()) as qnum,  0 as sort ,CONCAT('第',QUARTER(CURRENT_DATE()),'季度') as name 
union 
SELECT * from (
SELECT '01' as q ,1 as qnum,1 as sort,'第1季度' as name   union  SELECT '02',2,1,'第2季度' union  SELECT '03',3,1,'第3季度' union   SELECT '04',4,1,'第4季度'
) tab  where tab.qnum <> QUARTER(CURRENT_DATE()) 
order by sort,q 


