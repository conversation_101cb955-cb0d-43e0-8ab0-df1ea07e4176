{"RootName": "DataModels", "CTVER": "43543337", "TableCount": 7, "CreateDate": "2024/9/10 9:38:30", "ModifyDate": "2024/9/10 9:38:30", "Count": 1, "items": [{"ID": 4, "Name": "简化流程", "CreateDate": "2024/5/14 13:25:39", "OrderNo": 1, "CustomAttr1": "DVS:-246.60,-702.40,1.25,0,", "Tables": {"Count": 7, "items": [{"ID": 3, "Name": "CUSTOM_FLOW_MODULE", "Caption": "自定义流程模块表", "Memo": "弃用，减少表", "CreateDate": "2024/5/14 13:36:13", "OrderNo": 1, "GraphDesc": "Left=1357.42\r\nTop=404.70\r\nBLeft=678.71\r\nBTop=202.35", "MetaFields": {"Count": 4, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "MODULE_NAME", "OrderNo": 2, "DisplayName": "模块名称", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "TMSORT", "OrderNo": 3, "DataType": 2}, {"ID": 14, "Name": "TMUSED", "OrderNo": 4, "DataType": 2}]}}, {"ID": 4, "Name": "CUSTOM_FLOW_TPL", "Caption": "自定义流程表", "Memo": "弃用", "CreateDate": "2024/5/14 13:38:49", "OrderNo": 2, "GraphDesc": "Left=1321.00\r\nTop=207.80\r\nBLeft=660.50\r\nBTop=103.90", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "FLOW_NAME", "OrderNo": 2, "DisplayName": "流程名称", "DataType": 1, "DataLength": 100}, {"ID": 5, "Name": "CUSTOM_MODULE_ID", "OrderNo": 3, "DisplayName": "流程模块id", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "CUSTOM_FUN_ID", "OrderNo": 4, "DisplayName": "功能id", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "BUSINESS_CODE", "OrderNo": 5, "DisplayName": "业务编码", "DataType": 1, "DataLength": 50}, {"ID": 8, "Name": "HAVE_STEP_MARK", "OrderNo": 6, "DisplayName": "有步骤标识1有", "DataType": 2}, {"ID": 13, "Name": "TMSORT", "OrderNo": 7, "DataType": 2}, {"ID": 14, "Name": "TMUSED", "OrderNo": 8, "DataType": 2}]}}, {"ID": 5, "Name": "CUSTOM_FLOW_MODULE_FUN", "Caption": "自定义流程模块功能表", "Memo": "弃用", "CreateDate": "2024/5/14 13:41:57", "OrderNo": 3, "GraphDesc": "Left=1257.04\r\nTop=64.43\r\nBLeft=628.52\r\nBTop=32.22", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 7, "Name": "TYPE_CODE", "OrderNo": 2, "DisplayName": "类型编码1模块2功能", "DataType": 2}, {"ID": 15, "Name": "PID", "OrderNo": 3, "DisplayName": "功能所属模块ID", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=252.80,260.00\r\nP2=277.00,260.00\r\nP3=277.00,260.00\r\nP4=301.30,260.00\r\nHookP1=214.20,43.00\r\nHookP2=19.70,42.13\r\nMod_OP1=0\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 12, "Name": "APPLY_NAME", "OrderNo": 4, "DisplayName": "模块、功能名称", "DataType": 1, "DataLength": 100}, {"ID": 13, "Name": "TMSORT", "OrderNo": 5, "DataType": 2}, {"ID": 14, "Name": "TMUSED", "OrderNo": 6, "DataType": 2}]}}, {"ID": 6, "Name": "CUSTOM_FLOW_STEP", "Caption": "自定义流程步骤表", "CreateDate": "2024/5/14 13:44:41", "OrderNo": 4, "GraphDesc": "Left=105.94\r\nTop=174.59\r\nBLeft=52.97\r\nBTop=87.30", "MetaFields": {"Count": 12, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 10, "Name": "FLOW_MODULE_CODE", "OrderNo": 2, "DisplayName": "流程模块id", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "FLOW_FUN_CODE", "OrderNo": 3, "DisplayName": "功能id", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "BUSINESS_CODE", "OrderNo": 4, "DisplayName": "业务编码", "DataType": 1, "DataLength": 200}, {"ID": 15, "Name": "STEP_NO", "OrderNo": 5, "DisplayName": "步骤顺序", "DataType": 2}, {"ID": 16, "Name": "STEP_NAME", "OrderNo": 6, "DisplayName": "步骤名称", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "STEP_TYPE", "OrderNo": 7, "DisplayName": "步骤类型编码", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "ACC_TYPE", "OrderNo": 8, "DisplayName": "接收类型1人2岗位9自定义", "DataType": 2}, {"ID": 9, "Name": "ACC_SHOW", "OrderNo": 9, "DisplayName": "接收信息逗号分割", "DataType": 1, "DataLength": 2000}, {"ID": 13, "Name": "TMSORT", "OrderNo": 10, "DataType": 2}, {"ID": 14, "Name": "TMUSED", "OrderNo": 11, "DataType": 2}, {"ID": 12, "Name": "THEMES_ID", "OrderNo": 12, "DisplayName": "主题ID", "DataType": 1, "DataLength": 50}]}}, {"ID": 7, "Name": "CUSTOM_FLOW_STEP_ACC", "Caption": "自定义流程步骤接收表", "CreateDate": "2024/5/14 13:49:30", "OrderNo": 5, "GraphDesc": "Left=598.91\r\nTop=179.10\r\nBLeft=299.46\r\nBTop=89.55", "MetaFields": {"Count": 13, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 11, "Name": "FLOW_MODULE_CODE", "OrderNo": 2, "DisplayName": "流程模块id", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "FLOW_FUN_CODE", "OrderNo": 3, "DisplayName": "功能id", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "BUSINESS_CODE", "OrderNo": 4, "DisplayName": "业务编码", "DataType": 1, "DataLength": 200}, {"ID": 13, "Name": "STEP_ID", "OrderNo": 5, "DisplayName": "步骤顺序", "DataType": 1, "KeyFieldType": 3, "RelateTable": "CUSTOM_FLOW_STEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=475.94,284.00\r\nP2=537.00,284.00\r\nP3=537.00,284.00\r\nP4=598.91,284.00\r\nHookP1=350.06,109.41\r\nHookP2=20.09,104.90\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 14, "Name": "ACC_TYPE", "OrderNo": 6, "DisplayName": "接收类型1人2岗位9自定义", "DataType": 2}, {"ID": 15, "Name": "ACC_CODE", "OrderNo": 7, "DisplayName": "接收代码", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "ACC_NAME", "OrderNo": 8, "DisplayName": "接收名称", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "ORG_CODE", "OrderNo": 9, "DisplayName": "机构代码", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "JOB_CODE", "OrderNo": 10, "DisplayName": "岗位代码", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "TMSORT", "OrderNo": 11, "DataType": 2}, {"ID": 20, "Name": "TMUSED", "OrderNo": 12, "DataType": 2}, {"ID": 13, "Name": "THEMES_ID", "OrderNo": 13, "DisplayName": "主题ID", "DataType": 1, "DataLength": 50}]}}, {"ID": 8, "Name": "CUSTOM_FLOW_DATA", "Caption": "自定义流程数据表", "CreateDate": "2024/5/14 13:53:56", "OrderNo": 6, "GraphDesc": "Left=14.41\r\nTop=482.43\r\nBLeft=7.21\r\nBTop=241.22", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 13, "Name": "FLOW_MODULE_CODE", "OrderNo": 2, "DisplayName": "流程模块id", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "FLOW_FUN_CODE", "OrderNo": 3, "DisplayName": "功能id", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "BUSINESS_CODE", "OrderNo": 4, "DisplayName": "业务编码", "DataType": 1, "DataLength": 200}, {"ID": 11, "Name": "BUSINESS_DATA_CODE", "Memo": "参考", "OrderNo": 5, "DisplayName": "业务数据编码", "DataType": 1}, {"ID": 21, "Name": "CURR_STEP_ID", "OrderNo": 6, "DisplayName": "当前步骤", "DataType": 1, "DataLength": 50}, {"ID": 22, "Name": "RUN_STATUS", "OrderNo": 7, "DisplayName": "运行状态0开始1进行中2完成-1否决-2销项", "DataType": 2}, {"ID": 23, "Name": "BACK_DESC", "OrderNo": 8, "DisplayName": "否决信息", "DataType": 1, "DataLength": 2000}, {"ID": 18, "Name": "TMSORT", "OrderNo": 9, "DataType": 2}, {"ID": 19, "Name": "TMUSED", "OrderNo": 10, "DataType": 2}, {"ID": 11, "Name": "THEMES_ID", "OrderNo": 11, "DisplayName": "主题ID", "DataType": 1, "DataLength": 50}]}}, {"ID": 9, "Name": "CUSTOM_FLOW_DATA_ACC", "Caption": "自定义流程数据处理表", "CreateDate": "2024/5/14 14:00:20", "OrderNo": 7, "GraphDesc": "Left=622.22\r\nTop=479.54\r\nBLeft=311.11\r\nBTop=239.77", "MetaFields": {"Count": 18, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 16, "Name": "FLOW_MODULE_CODE", "OrderNo": 2, "DisplayName": "流程模块id", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "FLOW_FUN_CODE", "OrderNo": 3, "DisplayName": "功能id", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "BUSINESS_CODE", "OrderNo": 4, "DisplayName": "业务编码", "DataType": 1, "DataLength": 200}, {"ID": 15, "Name": "FLOW_DATA_ID", "OrderNo": 5, "DisplayName": "流程数据ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "CUSTOM_FLOW_DATA", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=591.41,581.00\r\nP2=607.00,581.00\r\nP3=607.00,581.00\r\nP4=622.22,581.00\r\nHookP1=556.59,98.57\r\nHookP2=19.78,101.46\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 13, "Name": "STEP_ID", "OrderNo": 6, "DisplayName": "步骤ID", "DataType": 1, "DataLength": 50}, {"ID": 24, "Name": "STEP_NO", "OrderNo": 7, "DisplayName": "步骤顺序号", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "CURR_MARK", "OrderNo": 8, "DisplayName": "当前应用状态1应用0不用", "DataType": 2}, {"ID": 19, "Name": "DEGREE_MARK", "OrderNo": 9, "DisplayName": "处理标识1处理0未处理", "DataType": 2}, {"ID": 20, "Name": "DEGREE_TIME", "OrderNo": 10, "DisplayName": "处理时间", "DataType": 4}, {"ID": 16, "Name": "ACC_SOURCE_ID", "OrderNo": 11, "DisplayName": "接收来源ID，表CUSTOM_FLOW_TPL_STEP_ACC", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "ACC_TYPE", "OrderNo": 12, "DisplayName": "接收类型1人2岗位9自定义", "DataType": 2}, {"ID": 15, "Name": "ACC_CODE", "OrderNo": 13, "DisplayName": "接收代码", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "ACC_NAME", "OrderNo": 14, "DisplayName": "接收名称", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "ORG_CODE", "OrderNo": 15, "DisplayName": "机构代码", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "JOB_CODE", "OrderNo": 16, "DisplayName": "岗位代码", "DataType": 1, "DataLength": 50}, {"ID": 22, "Name": "TMSORT", "OrderNo": 17, "DataType": 2}, {"ID": 23, "Name": "TMUSED", "OrderNo": 18, "DataType": 2}]}}]}}]}