{"RootName": "DataModels", "CTVER": "43543332", "TableCount": 112, "Count": 16, "items": [{"ID": 1, "Name": "企业用户注册", "CreateDate": "2021/3/29 星期一 14:48:27", "OrderNo": 1, "Tables": {"Count": 2, "items": [{"ID": 1, "Name": "SYS_REG_USER", "Caption": "用户注册临时表", "CreateDate": "2021/3/29 星期一 14:48:59", "OrderNo": 1, "GraphDesc": "Left=39.20\r\nTop=21.00\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 12, "items": [{"ID": 1, "Name": "ID", "Memo": "数据唯一标识", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "MOBILE", "Memo": "通常11位", "OrderNo": 2, "DisplayName": "手机号", "DataType": 1, "DataLength": 20}, {"ID": 3, "Name": "PASSWORD", "Memo": "最长18位", "OrderNo": 3, "DisplayName": "密码", "DataType": 1, "DataLength": 20}, {"ID": 4, "Name": "NAME", "OrderNo": 4, "DisplayName": "真实姓名", "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "IDCARD_NO", "Memo": "通常18位", "OrderNo": 5, "DisplayName": "身份证号码", "DataType": 1, "DataLength": 20}, {"ID": 6, "Name": "ADDR", "OrderNo": 6, "DisplayName": "通讯地址", "DataType": 1, "DataLength": 200}, {"ID": 7, "Name": "EMAIL", "OrderNo": 7, "DisplayName": "个人邮箱", "DataType": 1, "DataLength": 100}, {"ID": 127, "Name": "CREATE_BY", "OrderNo": 8, "DisplayName": "创建人ID", "DataType": 1, "DataLength": 50}, {"ID": 128, "Name": "CREATE_TIME", "OrderNo": 9, "DisplayName": "创建时间", "DataType": 4}, {"ID": 129, "Name": "UPDATE_BY", "OrderNo": 10, "DisplayName": "更新人ID", "DataType": 1, "DataLength": 50}, {"ID": 130, "Name": "UPDATE_TIME", "OrderNo": 11, "DisplayName": "更新时间", "DataType": 4}, {"ID": 148, "Name": "COMP_ID", "OrderNo": 12, "DisplayName": "企业ID", "DataType": 1, "DataLength": 50}]}}, {"ID": 1, "Name": "SYS_REG_COMPANY", "Caption": "企业注册正式表", "CreateDate": "2021/3/29 星期一 14:48:59", "OrderNo": 2, "GraphDesc": "Left=281.41\r\nTop=20.78\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "Memo": "数据唯一标识", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 8, "Name": "COMP_NAME", "OrderNo": 2, "DisplayName": "企业名称", "DataType": 1, "DataLength": 100}, {"ID": 170, "Name": "EMAIL", "OrderNo": 3, "DisplayName": "企业邮箱", "DataType": 1}, {"ID": 9, "Name": "PROVINCE", "OrderNo": 4, "DisplayName": "所属省份", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "CITY", "OrderNo": 5, "DisplayName": "所属城市", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "DOWNTOWN", "OrderNo": 6, "DisplayName": "所属市区", "DataType": 1, "DataLength": 50}, {"ID": 100, "Name": "LICENSE_NO", "OrderNo": 7, "DisplayName": "证照号码", "DataType": 1, "DataLength": 50}, {"ID": 113, "Name": "LICENSE_TYPE", "OrderNo": 8, "DisplayName": "证照类型", "DataType": 1, "DataLength": 100}, {"ID": 127, "Name": "CREATE_BY", "OrderNo": 9, "DisplayName": "创建人ID", "DataType": 1, "DataLength": 50}, {"ID": 128, "Name": "CREATE_TIME", "OrderNo": 10, "DisplayName": "创建时间", "DataType": 4}, {"ID": 129, "Name": "UPDATE_BY", "OrderNo": 11, "DisplayName": "更新人ID", "DataType": 1, "DataLength": 50}, {"ID": 130, "Name": "UPDATE_TIME", "OrderNo": 12, "DisplayName": "更新时间", "DataType": 4}, {"ID": 167, "Name": "AUDIT_STAT", "Memo": "0：待审核，1：审核否决，2：审核通过", "OrderNo": 13, "DisplayName": "审核状态", "DataType": 2}, {"ID": 168, "Name": "AUDIT_BY", "OrderNo": 14, "DisplayName": "审核人ID", "DataType": 1, "DataLength": 50}, {"ID": 169, "Name": "AUDIT_TIME", "OrderNo": 15, "DisplayName": "审核时间", "DataType": 4}, {"ID": 170, "Name": "VETO_REASON", "OrderNo": 16, "DisplayName": "否决原因", "DataType": 1, "DataLength": 200}]}}]}}, {"ID": 1, "Name": "企业管理", "CreateDate": "2021/3/30 星期二 13:48:48", "OrderNo": 2, "Params": "ShowPhyFieldName=2\r\nDatabaseEngine=\r\nExportFileName=C:\\Users\\<USER>\\Desktop\\TM4系统数据表结构-system.doc", "ConfigStr": "DrawerWidth=1500\nDrawerHeight=2560\nWorkAreaColor=16777215\nSelectedColor=-2147483635\nDefaultObjectColor=15921906\nDefaultTitleColor=255\nDefaultPKColor=16711935\nDefaultFKColor=16711680\nDefaultBorderColor=12632256\nDefaultLineColor=16711680\nShowFieldType=1\nShowFieldIcon=1\nShowPhyFieldName=2\nDatabaseEngine=\nGenFKIndexesSQL=0\n", "Tables": {"Count": 5, "items": [{"ID": 1, "Name": "SYS_ENTERPRISE_INFO", "Caption": "企业单位信息表", "CreateDate": "2021/3/30 星期二 13:48:56", "OrderNo": 1, "GraphDesc": "Left=10.00\r\nTop=10.00\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 63, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 281, "Name": "AUDIT_STATUS", "Memo": "-1否决 0默认 2提交 3审核通过", "OrderNo": 2, "DisplayName": "审核状态", "DataType": 2}, {"ID": 282, "Name": "AUDIT_TIME", "OrderNo": 3, "DisplayName": "审核通过时间", "DataType": 4}, {"ID": 13, "Name": "ENTERPRISE_LEVEL", "Memo": "为二级单位预留区分字段", "OrderNo": 4, "DisplayName": "企业等级", "DataType": 2}, {"ID": 2, "Name": "ENTERPRISE_CODE", "OrderNo": 5, "DisplayName": "企业单位编码", "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "ENTERPRISE_NAME", "OrderNo": 6, "DisplayName": "企业单位名称", "DataType": 1, "DataLength": 200}, {"ID": 4, "Name": "LICENSE_TYPE", "OrderNo": 7, "DisplayName": "证照类型", "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "LICENSE_CODE", "OrderNo": 8, "DisplayName": "证照号码", "DataType": 1, "DataLength": 50}, {"ID": 6, "Name": "REGISTRATION_TIME", "OrderNo": 9, "DisplayName": "注册时间", "DataType": 4}, {"ID": 12, "Name": "REGISTERED_FUND", "OrderNo": 10, "DisplayName": "注册资金", "DataType": 3}, {"ID": 7, "Name": "CREDIT_RATING", "OrderNo": 11, "DisplayName": "信用等级", "DataType": 2}, {"ID": 8, "Name": "FOREIGN_PROPORTION", "OrderNo": 12, "DisplayName": "外资比例", "DataType": 3}, {"ID": 10, "Name": "UNIT_NATURE", "OrderNo": 13, "DisplayName": "单位性质", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "UNIT_PROPERTIES", "OrderNo": 14, "DisplayName": "单位属性", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "AREA_COUNTY", "OrderNo": 15, "DisplayName": "所在区县", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "UNIT_ADDRESS", "OrderNo": 16, "DisplayName": "详细地址", "DataType": 1, "DataLength": 500}, {"ID": 16, "Name": "DEPOSIT_BANK", "OrderNo": 17, "DisplayName": "开户银行", "DataType": 1, "DataLength": 200}, {"ID": 17, "Name": "BANK_ACCOUNT", "OrderNo": 18, "DisplayName": "银行账号", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "POSTAL_CODE", "OrderNo": 19, "DisplayName": "邮政编码", "DataType": 1, "DataLength": 10}, {"ID": 292, "Name": "HIGH_ENTERPRISE_TIME", "OrderNo": 20, "DisplayName": "高企认定时间", "DataType": 4}, {"ID": 19, "Name": "HIGH_TECH_ENTERPRISE", "Memo": "1是 0否", "OrderNo": 21, "DisplayName": "是否高新技术企业", "DataType": 2}, {"ID": 20, "Name": "LEGAL_PERSON_NAME", "OrderNo": 22, "DisplayName": "法人姓名", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "LEGAL_PERSON_SEX", "Memo": "1男 2女", "OrderNo": 23, "DisplayName": "法人性别", "DataType": 2}, {"ID": 22, "Name": "LEGAL_PERSON_BIRTHDAY", "OrderNo": 24, "DisplayName": "法人出生年月", "DataType": 4}, {"ID": 23, "Name": "LEGAL_PERSON_PHONE", "OrderNo": 25, "DisplayName": "法人固定电话", "DataType": 1, "DataLength": 30}, {"ID": 24, "Name": "LEGAL_PERSON_MOBILE_PHONE", "OrderNo": 26, "DisplayName": "法人移动电话", "DataType": 1, "DataLength": 30}, {"ID": 25, "Name": "LEGAL_PERSON_FAX", "OrderNo": 27, "DisplayName": "法人传真", "DataType": 1, "DataLength": 20}, {"ID": 26, "Name": "LEGAL_PERSON_EMAIL", "OrderNo": 28, "DisplayName": "法人邮箱", "DataType": 1, "DataLength": 100}, {"ID": 27, "Name": "TECH_DEP_NAME", "OrderNo": 29, "DisplayName": "科技负责人姓名", "DataType": 1, "DataLength": 50}, {"ID": 28, "Name": "TECH_DEP_JOB", "OrderNo": 30, "DisplayName": "科技负责人职务", "DataType": 1, "DataLength": 100}, {"ID": 29, "Name": "TECH_DEP_PHONE", "OrderNo": 31, "DisplayName": "科技负责人固定电话", "DataType": 1, "DataLength": 50}, {"ID": 30, "Name": "TECH_DEP_MOBILE_PHONE", "OrderNo": 32, "DisplayName": "科技负责人移动电话", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "TECH_DEP_QQ", "OrderNo": 33, "DisplayName": "科技负责人QQ", "DataType": 1, "DataLength": 50}, {"ID": 31, "Name": "TECH_DEP_WX", "OrderNo": 34, "DisplayName": "科技负责人微信", "DataType": 1, "DataLength": 100}, {"ID": 32, "Name": "ACCOUNTING_DEP_NAME", "OrderNo": 35, "DisplayName": "财务负责人姓名", "DataType": 1, "DataLength": 50}, {"ID": 34, "Name": "ACCOUNTING_DEP_JOB", "OrderNo": 36, "DisplayName": "财务负责人职务", "DataType": 1, "DataLength": 100}, {"ID": 35, "Name": "ACCOUNTING_DEP_PHONE", "OrderNo": 37, "DisplayName": "财务负责人固定电话", "DataType": 1, "DataLength": 50}, {"ID": 36, "Name": "ACCOUNTING_DEP_MOBILE_PHONE", "OrderNo": 38, "DisplayName": "财务负责人移动电话", "DataType": 1, "DataLength": 50}, {"ID": 37, "Name": "ACCOUNTING_DEP_QQ", "OrderNo": 39, "DisplayName": "财务负责人QQ", "DataType": 1, "DataLength": 50}, {"ID": 33, "Name": "ACCOUNTING_DEP_WX", "OrderNo": 40, "DisplayName": "财务负责人微信", "DataType": 1, "DataLength": 100}, {"ID": 39, "Name": "FIRST_AUDIT_UNIT", "OrderNo": 41, "DisplayName": "初审单位", "DataType": 1, "DataLength": 100}, {"ID": 40, "Name": "FIRST_AUDIT_UNIT_NAME", "OrderNo": 42, "DisplayName": "初审单位名称", "DataType": 1, "DataLength": 100}, {"ID": 41, "Name": "EMPLOYEE_TOTAL", "OrderNo": 43, "DisplayName": "职工总数", "DataType": 2}, {"ID": 42, "Name": "EMPLOYEE_COLLEGE_TOTAL", "OrderNo": 44, "DisplayName": "大专以上人数", "DataType": 2}, {"ID": 43, "Name": "EMPLOYEE_COLLEGE_BACHELOR", "OrderNo": 45, "DisplayName": "大学本科以上人数", "DataType": 2}, {"ID": 38, "Name": "EMPLOYEE_DEV_TOTAL", "OrderNo": 46, "DisplayName": "研究开发人员", "DataType": 2}, {"ID": 88, "Name": "EMPLOYEE_ADMIN_TOTAL", "OrderNo": 47, "DisplayName": "中层以上管理人员总数", "DataType": 2}, {"ID": 89, "Name": "EMPLOYEE_HIGH_TOTAL", "OrderNo": 48, "DisplayName": "高级职称人数", "DataType": 2}, {"ID": 90, "Name": "EMPLOYEE_MIDDLE_TOTAL", "OrderNo": 49, "DisplayName": "中级职称人数", "DataType": 2}, {"ID": 91, "Name": "LASTYEAR_INCOME", "OrderNo": 50, "DisplayName": "上年度企业收入", "DataType": 3}, {"ID": 93, "Name": "LASTYEAR_NET_INCOME", "OrderNo": 51, "DisplayName": "上年度企业净利润", "DataType": 3}, {"ID": 94, "Name": "LASTYEAR_SALE_PROCEEDS", "OrderNo": 52, "DisplayName": "上年度产品销售收入", "DataType": 3}, {"ID": 95, "Name": "LASTYEAR_TAX", "OrderNo": 53, "DisplayName": "上年度企业交税总额", "DataType": 3}, {"ID": 62, "Name": "LASTYEAR_INDUSTRIAL_ADD", "OrderNo": 54, "DisplayName": "上年度工业增加值", "DataType": 3}, {"ID": 63, "Name": "LASTYEAR_EARN", "OrderNo": 55, "DisplayName": "上年度企业创汇总额", "DataType": 3}, {"ID": 96, "Name": "LASTYEAR_ASSET", "OrderNo": 56, "DisplayName": "上年度企业总资产", "DataType": 3}, {"ID": 97, "Name": "LASTYEAR_DEV_COST", "OrderNo": 57, "DisplayName": "上年度企业技术开发经费支出额", "DataType": 3}, {"ID": 92, "Name": "LASTYEAR_DEBT", "OrderNo": 58, "DisplayName": "上年度企业负债总额", "DataType": 3}, {"ID": 87, "Name": "LASTYEAR_DEBT_RATIO", "OrderNo": 59, "DisplayName": "上年度企业负债率", "DataType": 3}, {"ID": 405, "Name": "CREATE_BY", "OrderNo": 60, "DisplayName": "创建人", "DataType": 1, "DataLength": 50}, {"ID": 404, "Name": "CREATE_TIME", "OrderNo": 61, "DisplayName": "创建时间", "DataType": 4}, {"ID": 407, "Name": "UPDATE_BY", "OrderNo": 62, "DisplayName": "更新人", "DataType": 1, "DataLength": 50}, {"ID": 406, "Name": "UPDATE_TIME", "OrderNo": 63, "DisplayName": "更新时间", "DataType": 4}]}}, {"ID": 3, "Name": "SYS_ENTERPRISE_INFO_FILE", "Caption": "企业单位信息上传附件表", "CreateDate": "2021/3/31 星期三 10:26:03", "OrderNo": 2, "GraphDesc": "Left=566.80\r\nTop=481.20\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 13, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "EID", "Memo": "SYS_ENTERPRISE_INFO_ITEM.tmuid", "OrderNo": 2, "DisplayName": "父编号", "DataType": 1, "KeyFieldType": 3, "RelateTable": "SYS_ENTERPRISE_INFO", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=431.00,596.00\r\nP2=499.00,596.00\r\nP3=499.00,596.00\r\nP4=566.80,596.00\r\nHookP1=393.00,586.00\r\nHookP2=20.20,114.80\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 350, "Name": "FILE_TYPE_TMUID", "OrderNo": 3, "DisplayName": "附件类型id", "DataType": 1}, {"ID": 12, "Name": "FILE_TYPE", "OrderNo": 4, "DisplayName": "附件类型", "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "FILE_NAME", "OrderNo": 5, "DisplayName": "文件名", "DataType": 1, "DataLength": 500}, {"ID": 4, "Name": "FILE_ADDRESS", "Memo": "物理路径", "OrderNo": 6, "DisplayName": "文件物理地址", "DataType": 1, "DataLength": 500}, {"ID": 5, "Name": "FILE_URL", "Memo": "相对路径", "OrderNo": 7, "DisplayName": "文件网络访问地址", "DataType": 1, "DataLength": 500}, {"ID": 6, "Name": "FILE_EXT", "Memo": "没有点，只有后缀名", "OrderNo": 8, "DisplayName": "文件后缀", "DataType": 1, "DataLength": 10}, {"ID": 7, "Name": "FILE_SIZE", "Memo": "字节", "OrderNo": 9, "DisplayName": "文件大小", "DataType": 2}, {"ID": 8, "Name": "CREATE_BY", "OrderNo": 10, "DisplayName": "创建人姓名", "DataType": 1, "DataLength": 500}, {"ID": 9, "Name": "CREATE_TIME", "OrderNo": 11, "DisplayName": "创建日期", "DataType": 4}, {"ID": 10, "Name": "UPDATE_BY", "OrderNo": 12, "DisplayName": "数据级别", "DataType": 1, "DataLength": 500}, {"ID": 11, "Name": "UPDATE_TIME", "OrderNo": 13, "DisplayName": "排序号", "DataType": 4}]}}, {"ID": 6, "Name": "SYS_ENTERPRISE_INFO_SHAREHOLDE", "Caption": "企业信息股东信息", "CreateDate": "2021/3/31 星期三 16:05:48", "OrderNo": 3, "GraphDesc": "Left=566.00\r\nTop=14.80\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "逻辑标识", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "EID", "OrderNo": 2, "DisplayName": "企业逻辑标识", "DataType": 1, "KeyFieldType": 3, "RelateTable": "SYS_ENTERPRISE_INFO", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=431.00,114.00\r\nP2=498.00,114.00\r\nP3=498.00,114.00\r\nP4=566.00,114.00\r\nHookP1=377.00,104.00\r\nHookP2=20.00,99.20\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "SHAREHOLDER_NAME", "OrderNo": 3, "DisplayName": "股东名称", "DataType": 1, "DataLength": 100}, {"ID": 4, "Name": "SHAREHOLDER_COUNTRY", "OrderNo": 4, "DisplayName": "股东国别", "DataType": 1, "DataLength": 100}, {"ID": 5, "Name": "SHAREHOLDER_PROPERTY", "OrderNo": 5, "DisplayName": "股东性质", "DataType": 1, "DataLength": 100}, {"ID": 6, "Name": "INVESTMENT_TYPE", "OrderNo": 6, "DisplayName": "投资类型", "DataType": 1, "DataLength": 100}, {"ID": 7, "Name": "INVESTMENT_PROPORTION", "OrderNo": 7, "DisplayName": "投资比例", "DataType": 3}, {"ID": 8, "Name": "CREATE_BY", "OrderNo": 8, "DisplayName": "创建人姓名", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "CREATE_TIME", "OrderNo": 9, "DisplayName": "创建日期", "DataType": 4}, {"ID": 10, "Name": "UPDATE_BY", "OrderNo": 10, "DisplayName": "更新人", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "UPDATE_TIME", "OrderNo": 11, "DisplayName": "更新日期", "DataType": 4}]}}, {"ID": 8, "Name": "SYS_ENTERPRISE_INFO_FUND", "Caption": "企业信息使用政府资金", "Memo": "近三年企业使用政府资金", "CreateDate": "2021/3/31 星期三 16:41:50", "OrderNo": 4, "GraphDesc": "Left=566.80\r\nTop=239.20\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "逻辑标识", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "EID", "OrderNo": 2, "DisplayName": "企业逻辑标识", "DataType": 1, "KeyFieldType": 3, "RelateTable": "SYS_ENTERPRISE_INFO", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=431.00,338.00\r\nP2=499.00,338.00\r\nP3=499.00,338.00\r\nP4=566.80,338.00\r\nHookP1=385.00,328.00\r\nHookP2=20.20,98.80\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "PRO_NAME", "OrderNo": 3, "DisplayName": "项目名称", "DataType": 1, "DataLength": 200}, {"ID": 4, "Name": "FUND_ISSUED_DEP", "OrderNo": 4, "DisplayName": "经费下达部门", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "FUND_ISSUED_TIME", "OrderNo": 5, "DisplayName": "经费下达时间", "DataType": 4}, {"ID": 6, "Name": "COST_SUM", "OrderNo": 6, "DisplayName": "经费总额（万元）", "DataType": 3}, {"ID": 7, "Name": "ITEM_CHECK", "OrderNo": 7, "DisplayName": "项目是否已验收", "DataType": 2}, {"ID": 8, "Name": "CREATE_BY", "OrderNo": 8, "DisplayName": "创建人姓名", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "CREATE_TIME", "OrderNo": 9, "DisplayName": "创建日期", "DataType": 4}, {"ID": 10, "Name": "UPDATE_BY", "OrderNo": 10, "DisplayName": "更新人", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "UPDATE_TIME", "OrderNo": 11, "DisplayName": "更新日期", "DataType": 4}]}}, {"ID": 4, "Name": "SYS_ENTERPRISE_INFO_ACCOUNTING", "Caption": "企业单位年度财务信息", "CreateDate": "2021/3/31 星期三 13:13:50", "OrderNo": 5, "GraphDesc": "Left=565.20\r\nTop=744.80\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 53, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "逻辑标识", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "EID", "OrderNo": 2, "DisplayName": "企业逻辑标识", "DataType": 1, "KeyFieldType": 3, "RelateTable": "SYS_ENTERPRISE_INFO", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=431.00,788.00\r\nP2=498.00,788.00\r\nP3=498.00,788.00\r\nP4=565.20,788.00\r\nHookP1=401.00,778.00\r\nHookP2=19.80,43.20\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 7, "Name": "APPLY_YEAR", "OrderNo": 3, "DisplayName": "应用年度", "DataType": 2}, {"ID": 4, "Name": "PERSON_TOTAL", "OrderNo": 4, "DisplayName": "1.职工人数", "DataType": 2}, {"ID": 5, "Name": "ACCOUNTIONG_TOTAL", "OrderNo": 5, "DisplayName": "2.财务人数", "DataType": 2}, {"ID": 6, "Name": "MIDDLE_ACCOUNTIONG_TOTAL", "OrderNo": 6, "DisplayName": "其中：中级以上财务人员", "DataType": 2}, {"ID": 22, "Name": "REGISTERED_FUND", "OrderNo": 7, "DisplayName": "1.注册资金", "DataType": 3}, {"ID": 170, "Name": "ASSET_TOTAL", "OrderNo": 8, "DisplayName": "2.资产总额", "DataType": 3}, {"ID": 171, "Name": "DEBT_TOTAL", "OrderNo": 9, "DisplayName": "3.负债总额", "DataType": 3}, {"ID": 172, "Name": "SALES_TOTAL", "OrderNo": 10, "DisplayName": "4.销售收入", "DataType": 3}, {"ID": 173, "Name": "INCOME_TOTAL", "OrderNo": 11, "DisplayName": "5.实现利润", "DataType": 3}, {"ID": 204, "Name": "CASH_IN", "OrderNo": 12, "DisplayName": "6.经营活动现金流入", "DataType": 3}, {"ID": 205, "Name": "CASH_OUT", "OrderNo": 13, "DisplayName": "7.经营活动现金流出", "DataType": 3}, {"ID": 206, "Name": "CASH_FLOW", "OrderNo": 14, "DisplayName": "8.经营活动现金净流量", "DataType": 3}, {"ID": 207, "Name": "DEBT_RATIO", "OrderNo": 15, "DisplayName": "9.资产负债率", "DataType": 3}, {"ID": 208, "Name": "ASSET_RATIO", "OrderNo": 16, "DisplayName": "10.资产总额利润率", "DataType": 3}, {"ID": 209, "Name": "FLOW_RATIO", "OrderNo": 17, "DisplayName": "11.流动比率", "DataType": 3}, {"ID": 210, "Name": "QUICK_RATIO", "OrderNo": 18, "DisplayName": "12.速动比率", "DataType": 3}, {"ID": 211, "Name": "INCOME_RATIO", "OrderNo": 19, "DisplayName": "13.营业收入利用率", "DataType": 3}, {"ID": 212, "Name": "RECEIVABLE_TURNOVER_RATIO", "OrderNo": 20, "DisplayName": "14.应收账款周转率", "DataType": 3}, {"ID": 213, "Name": "DEV_COST", "OrderNo": 21, "DisplayName": "15.企业提取技术开发费用", "DataType": 3}, {"ID": 214, "Name": "NEW_TECHNIQUE_COST", "OrderNo": 22, "DisplayName": "其中  （1）购置新技术", "DataType": 3}, {"ID": 215, "Name": "TECHNICAL_PERSON_COST", "OrderNo": 23, "DisplayName": "（2）技术人员人工支出", "DataType": 3}, {"ID": 216, "Name": "RESEARCH_COST", "OrderNo": 24, "DisplayName": "（3）研究开发费用", "DataType": 3}, {"ID": 218, "Name": "DEV_FIXED_ASSETS", "OrderNo": 25, "DisplayName": "（4）研究开发性固定资产购置", "DataType": 3}, {"ID": 217, "Name": "OTHER_DEV_COST", "OrderNo": 26, "DisplayName": "（5）其他科技支出费用", "DataType": 3}, {"ID": 219, "Name": "PRODUCTION_VALUE", "OrderNo": 27, "DisplayName": "16.产值", "DataType": 3}, {"ID": 220, "Name": "SALARY", "OrderNo": 28, "DisplayName": "17.应发职工工资", "DataType": 3}, {"ID": 221, "Name": "PAY", "OrderNo": 29, "DisplayName": "18.实发职工工资", "DataType": 3}, {"ID": 222, "Name": "PAID_IN_CAPITAL", "OrderNo": 30, "DisplayName": "19.实收资本", "DataType": 3}, {"ID": 37, "Name": "NATIONAL_CAPITAL", "OrderNo": 31, "DisplayName": "（1）国家资本", "DataType": 3}, {"ID": 38, "Name": "COLLECTIVE_CAPITAL", "OrderNo": 32, "DisplayName": "（2）集体资本", "DataType": 3}, {"ID": 223, "Name": "CORPORATE_CAPITAL", "OrderNo": 33, "DisplayName": "（3）法人资本", "DataType": 3}, {"ID": 224, "Name": "PERSONAL_CAPITAL", "OrderNo": 34, "DisplayName": "（4）个人资本", "DataType": 3}, {"ID": 39, "Name": "FOREIGN_CAPITAL", "OrderNo": 35, "DisplayName": "（5）外商资本", "DataType": 3}, {"ID": 41, "Name": "CAPITAL_RESERVE", "OrderNo": 36, "DisplayName": "20.资本公积", "DataType": 3}, {"ID": 42, "Name": "SURPLUS_RESERVE", "OrderNo": 37, "DisplayName": "21.盈余公积", "DataType": 3}, {"ID": 43, "Name": "TAX_CASE", "OrderNo": 38, "DisplayName": "22.缴纳各项税金情况", "DataType": 1}, {"ID": 40, "Name": "BEGIN_YEAR_NOT_PAY_TAX", "OrderNo": 39, "DisplayName": "（1）年初未缴各项税金", "DataType": 3}, {"ID": 45, "Name": "THIS_YEAR_PAY_IN_TAX", "OrderNo": 40, "DisplayName": "（2）本年已缴各项税金", "DataType": 3}, {"ID": 44, "Name": "THIS_YEAR_PAY_TAX", "OrderNo": 41, "DisplayName": "（3）本年应缴各项税金", "DataType": 3}, {"ID": 46, "Name": "END_YEAR_UNPAY_TAX", "OrderNo": 42, "DisplayName": "（4）年末未缴各项税金", "DataType": 3}, {"ID": 47, "Name": "DERATE", "OrderNo": 43, "DisplayName": "（5）减免税金", "DataType": 3}, {"ID": 48, "Name": "INSURANCE_CASE", "OrderNo": 44, "DisplayName": "23.缴纳各种保险情况", "DataType": 1}, {"ID": 49, "Name": "BEGIN_YEAR_NOT_PAY_INSURANCE", "OrderNo": 45, "DisplayName": "（1）年初未缴各种社会保险", "DataType": 3}, {"ID": 50, "Name": "THIS_YEAR_PAY_IN_INSURANCE", "OrderNo": 46, "DisplayName": "（2）本年应缴各种社会保险", "DataType": 3}, {"ID": 51, "Name": "THIS_YEAR_PAY_INSURANCE", "OrderNo": 47, "DisplayName": "（3）本年已缴各种社会保险", "DataType": 3}, {"ID": 52, "Name": "END_YEAR_UNPAY_INSURANCE", "OrderNo": 48, "DisplayName": "（4）年末未缴各种社会保险", "DataType": 3}, {"ID": 53, "Name": "DEV_INPUT", "OrderNo": 49, "DisplayName": "24.企业研发投入", "DataType": 3}, {"ID": 8, "Name": "CREATE_BY", "OrderNo": 50, "DisplayName": "创建人", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "CREATE_TIME", "OrderNo": 51, "DisplayName": "创建时间", "DataType": 4}, {"ID": 10, "Name": "UPDATE_BY", "OrderNo": 52, "DisplayName": "更新人", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "UPDATE_TIME", "OrderNo": 53, "DisplayName": "更新时间", "DataType": 4}]}}]}}, {"ID": 16, "Name": "自定义页面", "CreateDate": "2022/1/29 星期六 09:09:56", "OrderNo": 3, "ConfigStr": "DrawerWidth=1500\r\nDrawerHeight=2560\r\nWorkAreaColor=16777215\r\nSelectedColor=16711680\r\nDefaultObjectColor=15921906\r\nDefaultTitleColor=255\r\nDefaultPKColor=16711935\r\nDefaultFKColor=16711680\r\nDefaultBorderColor=12632256\r\nDefaultLineColor=16711680\r\nShowFieldType=1\r\nShowFieldIcon=1\r\nShowPhyFieldName=2\r\nDatabaseEngine=\r\nGenFKIndexesSQL=0\r\nIndependPosForOverviewMode=0\r\n", "Tables": {"Count": 4, "items": [{"ID": 1, "Name": "DIY_PAGE_LIB", "Caption": "自定义页面库配置", "CreateDate": "2022/1/29 星期六 09:10:30", "OrderNo": 1, "GraphDesc": "Left=648.40\r\nTop=12.00\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "RelateTable": "DIY_PAGE_LIB_REL", "RelateField": "Id", "DataLength": 50, "GraphDesc": "P1=961.20,129.00\r\nP2=902.00,129.00\r\nP3=902.00,129.00\r\nP4=843.40,129.00\r\nHookP1=19.80,33.20\r\nHookP2=174.60,117.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 2, "Name": "PAGEID", "OrderNo": 2, "DisplayName": "页面ID", "DataType": 1, "DataLength": 100}, {"ID": 4, "Name": "TITLE", "OrderNo": 3, "DisplayName": "标题", "DataType": 1, "DataLength": 200}, {"ID": 9, "Name": "TMUSED", "OrderNo": 4, "DisplayName": "是否使用", "DataType": 2}, {"ID": 11, "Name": "TMSORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}, {"ID": 6, "Name": "ALIAS", "Memo": "别名", "OrderNo": 6, "DisplayName": "别名", "DataType": 1}, {"ID": 7, "Name": "SHOWTYPE", "Memo": "显示方式", "OrderNo": 7, "DisplayName": "显示方式", "DataType": 2}, {"ID": 8, "Name": "OBJTYPE", "Memo": "对象类型", "OrderNo": 8, "DisplayName": "对象类型", "DataType": 1}]}}, {"ID": 1, "Name": "DIY_PAGE_LIB_INFO", "Caption": "自定义页面库配置", "CreateDate": "2022/1/29 星期六 09:10:30", "OrderNo": 2, "GraphDesc": "Left=40.20\r\nTop=58.20\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "PAGEID", "OrderNo": 2, "DisplayName": "页面ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "DIY_PAGE_LIB", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=648.40,110.00\r\nP2=458.00,110.00\r\nP3=458.00,110.00\r\nP4=268.20,110.00\r\nHookP1=27.60,98.00\r\nHookP2=207.80,51.80\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "TITLE", "OrderNo": 3, "DisplayName": "标题", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "COMMNAME", "Memo": "组件名称", "OrderNo": 4, "DisplayName": "组件名称", "DataType": 1, "DataLength": 255}, {"ID": 7, "Name": "COMMPATH", "Memo": "组件路径", "OrderNo": 5, "DisplayName": "组件路径", "DataType": 1, "DataLength": 255}, {"ID": 8, "Name": "COMMPARAMS", "Memo": "{\"tdsAlias\":\"ACT_BUSS_USER_T_FYXM_HZ\",\"tdsInParaAlias\":\"XMLB=检维修费用\",\"tdsPage\":1,\"tdsPageSize\":100,\"tdsShowQueryBar\":true,\"showAdd\":false,\"showDel\":false,\"showSave\":false,\"saveNullMsg\":false,\"height\":550}", "OrderNo": 6, "DisplayName": "组件参数", "DataType": 1, "DataLength": 2000}, {"ID": 9, "Name": "TMUSED", "OrderNo": 7, "DisplayName": "是否使用", "DataType": 2}, {"ID": 11, "Name": "TMSORT", "OrderNo": 8, "DisplayName": "排序", "DataType": 2}, {"ID": 9, "Name": "TDSALIAS", "OrderNo": 9, "DisplayName": "数据源别名", "DataType": 1}, {"ID": 10, "Name": "TDSNAME", "Memo": "数据源名称", "OrderNo": 10, "DataType": 1}, {"ID": 12, "Name": "GIRD", "OrderNo": 11, "DisplayName": "栅格", "DataType": 1}, {"ID": 11, "Name": "STYLE", "Memo": "0选项卡 1手风琴 2栅格 3自定义", "OrderNo": 12, "DisplayName": "页面样式类型", "DataType": 1}, {"ID": 13, "Name": "CHARTPARAM", "Memo": "分析图参数", "OrderNo": 13, "DisplayName": "分析图参数", "DataType": 1}, {"ID": 14, "Name": "PAGEJSON", "OrderNo": 14, "DisplayName": "布局设置", "DataType": 1}, {"ID": 15, "Name": "MODULECODE", "Memo": "模块编码", "OrderNo": 15, "DisplayName": "模块编码", "DataType": 1}, {"ID": 16, "Name": "DATAID", "Memo": "数据id", "OrderNo": 16, "DisplayName": "数据id", "DataType": 1}]}}, {"Name": "DIY_PAGE_LIB_INPARA", "Caption": "自定义页面输入参数", "OrderNo": 3, "GraphDesc": "Left=53.20\r\nTop=292.20\r\nBLeft=26.60\r\nBTop=146.10\r\n", "MetaFields": {"Count": 17, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "DataTypeName": "<PERSON><PERSON><PERSON>", "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 19, "Name": "PAGEID", "OrderNo": 2, "DisplayName": "页面ID", "DataType": 1, "DataTypeName": "<PERSON><PERSON><PERSON>", "KeyFieldType": 3, "RelateTable": "DIY_PAGE_LIB_INFO", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=268.20,314.00\r\nP2=161.00,314.00\r\nP3=161.00,314.00\r\nP4=53.20,314.00\r\nHookP1=120.80,255.80\r\nHookP2=107.80,21.80\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 7, "Name": "COMPONENTTYPE", "OrderNo": 3, "DisplayName": "组件类型", "DataType": 1, "DataTypeName": "<PERSON><PERSON><PERSON>", "DataLength": 50}, {"ID": 8, "Name": "DATATYPE", "OrderNo": 4, "DisplayName": "数据类型（TM4目前未使用，默认值：tdsString）", "DataType": 1, "DataTypeName": "<PERSON><PERSON><PERSON>", "DataLength": 50}, {"ID": 9, "Name": "DEFAULTKEYSCRIPT", "OrderNo": 5, "DisplayName": "下拉框填充key值脚本", "DataType": 1, "DataTypeName": "<PERSON><PERSON><PERSON>", "DataLength": 1000}, {"ID": 10, "Name": "DEFAULTVALUESCRIPT", "OrderNo": 6, "DisplayName": "下拉框添加显示值脚本", "DataType": 1, "DataTypeName": "<PERSON><PERSON><PERSON>", "DataLength": 1000}, {"ID": 11, "Name": "DISPLAY", "OrderNo": 7, "DisplayName": "是否显示 1：显示；0：隐藏", "DataType": 2, "DataTypeName": "int"}, {"ID": 12, "Name": "INITVALUESCRIPT", "OrderNo": 8, "DisplayName": "初始化默认值脚本（TM4预留，未使用）", "DataType": 1, "DataTypeName": "<PERSON><PERSON><PERSON>", "DataLength": 1000}, {"ID": 13, "Name": "INSERTEDIT", "OrderNo": 9, "DisplayName": "输入是否代入录入项 for 数据源编辑", "DataType": 2, "DataTypeName": "int"}, {"ID": 14, "Name": "ISCANQUERY", "OrderNo": 10, "DisplayName": "下拉框是否支持模糊检索（TM4未使用）", "DataType": 2, "DataTypeName": "int"}, {"ID": 15, "Name": "MEMO", "OrderNo": 11, "DisplayName": "备注", "DataType": 1, "DataTypeName": "<PERSON><PERSON><PERSON>", "DataLength": 255}, {"ID": 16, "Name": "PARAALIAS", "OrderNo": 12, "DisplayName": "输入参数别名", "DataType": 1, "DataTypeName": "<PERSON><PERSON><PERSON>", "DataLength": 50}, {"ID": 17, "Name": "PARAID", "OrderNo": 13, "DisplayName": "显示顺序", "DataType": 2, "DataTypeName": "int"}, {"ID": 18, "Name": "PARANAME", "OrderNo": 14, "DisplayName": "参数名称", "DataType": 1, "DataTypeName": "<PERSON><PERSON><PERSON>", "DataLength": 50}, {"ID": 20, "Name": "WIDTH", "OrderNo": 15, "DisplayName": "控件显示宽度", "DataType": 2, "DataTypeName": "int"}, {"ID": 16, "Name": "USED", "OrderNo": 16, "DisplayName": "删除标识", "DataType": 2}, {"ID": 17, "Name": "IMPORTCHOICE", "Memo": "常规列1，关键列2，不导入3（TM4未使用）", "OrderNo": 17, "DisplayName": "导入选项", "DataType": 1}]}}, {"ID": 5, "Name": "DIY_PAGE_LIB_REL", "Caption": "自定义页面关系表", "Memo": "自定义页面关系表", "CreateDate": "2022/11/28 星期一 09:00:23", "OrderNo": 4, "GraphDesc": "Left=961.20\r\nTop=95.80\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "Id", "OrderNo": 1, "DisplayName": "编号", "DataType": 2, "KeyFieldType": 1}, {"ID": 2, "Name": "OBJID", "Memo": "机构 人员 等外部id", "OrderNo": 2, "DisplayName": "对象id", "DataType": 2}, {"ID": 3, "Name": "PAGEID", "Memo": "对象id对应的页面id  其页面id应来自于lib表", "OrderNo": 3, "DisplayName": "页面id", "DataType": 2, "KeyFieldType": 3}, {"ID": 4, "Name": "TYPE", "Memo": "类型 first机构首页 second岗位首页 third个人首页", "OrderNo": 4, "DisplayName": "页面类型", "DataType": 1, "DataLength": 255}, {"ID": 5, "Name": "ISREFS", "Memo": "页面是否是引用关系", "OrderNo": 5, "DisplayName": "是否引用", "DataType": 1}, {"ID": 6, "Name": "REFSPAGEID", "Memo": "引用页面的id     可以是外部id 也可以是pageid\r\n", "OrderNo": 6, "DisplayName": "引用id", "DataType": 1}, {"ID": 7, "Name": "REFSPAGENAME", "Memo": "引用页面名称  用于显示用", "OrderNo": 7, "DisplayName": "引用id名称", "DataType": 2, "IndexType": 2, "Not_Nullable": true}, {"ID": 8, "Name": "EXTENDSTYPE", "Memo": "继承类型  父类继承 指定继承 和不继承", "OrderNo": 8, "DisplayName": "继承类型", "DataType": 1}]}}]}}, {"ID": 1, "Name": "框架-人员、机构、岗位", "CreateDate": "2020/3/1 星期日 12:00:27", "OrderNo": 4, "Params": "ShowPhyFieldName=1\r\nDatabaseEngine=\r\nExportFileName=E:\\OutDisk\\运和接口程序_TM3程序_文档\\运和智能\\0.VUE培训\\00000.新框架功能设计\\1.人员岗位相关设计材料\\人员岗位数据库设计v8.doc", "ConfigStr": "DrawerWidth=2882\r\nDrawerHeight=2757\r\nWorkAreaColor=16777215\r\nSelectedColor=-16777203\r\nDefaultObjectColor=15921906\r\nDefaultTitleColor=255\r\nDefaultPKColor=16711935\r\nDefaultFKColor=16711680\r\nDefaultBorderColor=12632256\r\nDefaultLineColor=16711680\r\nShowFieldType=1\r\nShowFieldIcon=1\r\nShowPhyFieldName=2\r\nDatabaseEngine=\r\nGenFKIndexesSQL=0\r\n", "Tables": {"Count": 41, "items": [{"ID": 1, "Name": "SYS_POST_LEVEL", "Caption": "岗位级别表【职级】", "CreateDate": "2020/3/1 星期日 12:05:18", "OrderNo": 1, "GraphDesc": "Left=513.00\r\nTop=158.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "Memo": "职级ID", "OrderNo": 1, "DisplayName": "岗位级别", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 4, "Name": "NAME", "Memo": "职级名称", "OrderNo": 2, "DisplayName": "岗位名称", "DataType": 1, "DataLength": 100}, {"ID": 10, "Name": "CLASSID", "Memo": "职级分类ID（职务ID）", "OrderNo": 3, "DisplayName": "级别分类ID（职务ID）", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post_lev_cls", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=880.00,235.00\r\nP2=851.00,235.00\r\nP3=851.00,235.00\r\nP4=822.00,235.00\r\nHookP1=28.00,67.18\r\nHookP2=281.00,76.18\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 63, "Name": "GRADE", "Memo": "职位等级：A,B,C,D,E", "OrderNo": 4, "DisplayName": "等级（职等）", "DataType": 1, "DataLength": 50}, {"ID": 64, "Name": "COEFFICIENT", "OrderNo": 5, "DisplayName": "系数", "DataType": 3}, {"ID": 60, "Name": "MEMO", "OrderNo": 6, "DisplayName": "描述", "DataType": 1, "DataLength": 1000}, {"ID": 11, "Name": "SORT", "OrderNo": 7, "DisplayName": "排序", "DataType": 2}, {"ID": 62, "Name": "USED", "Memo": "0不使用 1使用", "OrderNo": 8, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 4, "Name": "SYS_POST_LEV_CLS", "Caption": "岗位级别分类表【职务】", "CreateDate": "2020/3/1 星期日 12:56:51", "OrderNo": 2, "GraphDesc": "Left=880.00\r\nTop=167.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "分类ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 4, "Name": "NAME", "OrderNo": 2, "DisplayName": "分类名称", "DataType": 1, "DataLength": 100}, {"ID": 414, "Name": "CLASSID", "OrderNo": 3, "DisplayName": "职务分类ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post_lev_cls_cls", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1191.50,226.00\r\nP2=1166.00,226.00\r\nP3=1166.00,226.00\r\nP4=1140.00,226.00\r\nHookP1=20.50,60.18\r\nHookP2=240.00,58.18\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 5, "Name": "MEMO", "OrderNo": 4, "DisplayName": "描述", "DataType": 1, "DataLength": 1000}, {"ID": 10, "Name": "SORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}, {"ID": 11, "Name": "USED", "Memo": "0不使用 1使用", "OrderNo": 6, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 4, "Name": "SYS_POST_LEV_CLS_CLS", "Caption": "职务分类表【职务分类】", "CreateDate": "2020/3/1 星期日 12:56:51", "OrderNo": 3, "GraphDesc": "Left=1191.50\r\nTop=165.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "分类ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 111, "Name": "ALLPATH", "OrderNo": 2, "DisplayName": "树形全路径", "DataType": 1, "DataLength": 2000}, {"ID": 4, "Name": "NAME", "OrderNo": 3, "DisplayName": "分类名称", "DataType": 1, "DataLength": 100}, {"ID": 5, "Name": "MEMO", "OrderNo": 4, "DisplayName": "描述", "DataType": 1, "DataLength": 1000}, {"ID": 10, "Name": "SORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}, {"ID": 11, "Name": "USED", "Memo": "0不使用 1使用", "OrderNo": 6, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 10, "Name": "SYS_POST_LEV_CLS_CLS_RELATI", "Caption": "职务分类树形关系表", "CreateDate": "2020/3/1 星期日 14:50:36", "OrderNo": 4, "GraphDesc": "Left=1195.50\r\nTop=334.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 3, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "CODE", "OrderNo": 2, "DisplayName": "编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post_lev_cls_cls", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1336.00,283.82\r\nP2=1336.00,309.00\r\nP3=1336.00,309.00\r\nP4=1336.00,334.82\r\nHookP1=144.50,90.18\r\nHookP2=140.50,20.18\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 3, "Name": "PCODE", "OrderNo": 3, "DisplayName": "父编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post_lev_cls_cls", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1328.00,290.00\r\nP2=1328.00,316.00\r\nP3=1328.00,316.00\r\nP4=1328.00,341.00\r\nHookP1=134.50,82.00\r\nHookP2=130.50,27.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}]}}, {"ID": 6, "Name": "SYS_POST", "Caption": "岗位表【职位表】", "CreateDate": "2020/3/1 星期日 13:57:49", "OrderNo": 5, "GraphDesc": "Left=101.00\r\nTop=149.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "岗位ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 321, "Name": "ALLPATH", "OrderNo": 2, "DisplayName": "树形全路径", "DataType": 1, "DataLength": 2000}, {"ID": 4, "Name": "NAME", "OrderNo": 3, "DisplayName": "岗位名称", "DataType": 1, "DataLength": 100}, {"ID": 6, "Name": "CLASSID", "OrderNo": 4, "DisplayName": "岗位分类ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post_class", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=512.00,377.82\r\nP2=363.00,377.82\r\nP3=363.00,251.82\r\nP4=305.00,251.82\r\nHookP1=117.00,52.00\r\nHookP2=102.00,102.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 71, "Name": "LEVELID", "OrderNo": 5, "DisplayName": "岗位级别ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post_level", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=513.00,225.00\r\nP2=409.00,225.00\r\nP3=409.00,225.00\r\nP4=305.00,225.00\r\nHookP1=20.00,66.18\r\nHookP2=168.00,75.18\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 72, "Name": "COEFFICIENT", "OrderNo": 6, "DisplayName": "岗位系数", "DataType": 3}, {"ID": 5, "Name": "MEMO", "OrderNo": 7, "DisplayName": "注释", "DataType": 1, "DataLength": 1000}, {"ID": 69, "Name": "SORT", "OrderNo": 8, "DisplayName": "排序", "DataType": 2}, {"ID": 70, "Name": "USED", "OrderNo": 9, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 7, "Name": "SYS_POST_RELATI", "Caption": "岗位表树形关系表【职位树形关系】", "CreateDate": "2020/3/1 星期日 13:58:29", "OrderNo": 6, "GraphDesc": "Left=48.00\r\nTop=37.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 3, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "CODE", "OrderNo": 2, "DisplayName": "编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=204.00,149.82\r\nP2=204.00,129.00\r\nP3=204.00,129.00\r\nP4=204.00,107.82\r\nHookP1=103.00,51.00\r\nHookP2=156.00,35.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 3, "Name": "PCODE", "OrderNo": 3, "DisplayName": "父编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=162.00,591.00\r\nP2=162.00,624.00\r\nP3=162.00,624.00\r\nP4=162.00,657.00\r\nHookP1=110.00,138.00\r\nHookP2=134.00,27.00\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}]}}, {"ID": 11, "Name": "SYS_POST_CLASS", "Caption": "岗位分类表【专业口】", "CreateDate": "2020/3/1 星期日 15:02:54", "OrderNo": 7, "GraphDesc": "Left=512.00\r\nTop=325.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "分类ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "NAME", "OrderNo": 2, "DisplayName": "分类名称", "DataType": 1, "DataLength": 100}, {"ID": 441, "Name": "MEMO", "OrderNo": 3, "DisplayName": "描述", "DataType": 1, "DataLength": 1000}, {"ID": 478, "Name": "SORT", "OrderNo": 4, "DisplayName": "排序", "DataType": 2}, {"ID": 498, "Name": "USED", "OrderNo": 5, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 12, "Name": "SYS_POST_ROLE", "Caption": "岗位角色绑定表", "CreateDate": "2020/3/1 星期日 15:34:02", "OrderNo": 8, "GraphDesc": "Left=512.00\r\nTop=441.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 3, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "POSTID", "OrderNo": 2, "DisplayName": "岗位ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=241.00,315.82\r\nP2=241.00,477.82\r\nP3=241.00,477.82\r\nP4=512.00,477.82\r\nHookP1=140.00,106.00\r\nHookP2=94.00,36.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 554, "Name": "ROLEID", "OrderNo": 3, "DisplayName": "角色ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_role", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1726.00,467.82\r\nP2=1666.00,467.82\r\nP3=1666.00,467.82\r\nP4=700.00,467.82\r\nHookP1=70.00,89.00\r\nHookP2=102.00,26.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}]}}, {"ID": 13, "Name": "SYS_POST_PERMIT", "Caption": "岗位权限绑定表", "CreateDate": "2020/3/1 星期日 15:35:10", "OrderNo": 9, "GraphDesc": "Left=508.00\r\nTop=525.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 4, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "POSTID", "OrderNo": 2, "DisplayName": "岗位ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=227.00,315.82\r\nP2=227.00,560.82\r\nP3=227.00,560.82\r\nP4=508.00,560.82\r\nHookP1=126.00,67.00\r\nHookP2=101.00,35.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 592, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "menuid", "DataLength": 50, "GraphDesc": "P1=2003.00,545.82\r\nP2=1356.00,545.82\r\nP3=1356.00,545.82\r\nP4=710.00,545.82\r\nHookP1=158.00,66.00\r\nHookP2=193.00,20.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 60, "Name": "ACTIONID", "OrderNo": 4, "DisplayName": "按钮ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "actionid", "DataLength": 50, "GraphDesc": "P1=2003.00,476.00\r\nP2=1356.00,476.00\r\nP3=1356.00,476.00\r\nP4=710.00,476.00\r\nHookP1=84.00,66.00\r\nHookP2=85.00,20.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}]}}, {"ID": 14, "Name": "SYS_POST_PERMIT_NOT", "Caption": "岗位权限刨除绑定表", "CreateDate": "2020/3/1 星期日 15:39:15", "OrderNo": 10, "GraphDesc": "Left=507.00\r\nTop=624.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 4, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 604, "Name": "POSTID", "OrderNo": 2, "DisplayName": "岗位ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=213.34,315.82\r\nP2=213.34,651.00\r\nP3=213.34,651.00\r\nP4=507.00,651.00\r\nHookP1=112.34,51.00\r\nHookP2=20.00,26.18\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 605, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "menuid", "DataLength": 50, "GraphDesc": "P1=2003.00,554.82\r\nP2=790.00,554.82\r\nP3=790.00,643.82\r\nP4=761.00,643.82\r\nHookP1=174.00,75.00\r\nHookP2=135.00,19.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 64, "Name": "ACTIONID", "OrderNo": 4, "DisplayName": "按钮ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "actionid", "DataLength": 50, "GraphDesc": "P1=2003.00,485.00\r\nP2=786.00,485.00\r\nP3=786.00,574.00\r\nP4=761.00,574.00\r\nHookP1=94.00,75.00\r\nHookP2=111.00,19.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}]}}, {"ID": 38, "Name": "SYS_POST_DATATYPE", "Caption": "用户拥有数据权限类型", "CreateDate": "2020/3/3 星期二 13:35:05", "OrderNo": 11, "GraphDesc": "Left=508.00\r\nTop=723.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "POSTID", "OrderNo": 2, "DisplayName": "岗位id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=199.00,315.82\r\nP2=199.00,743.82\r\nP3=199.00,743.82\r\nP4=508.00,743.82\r\nHookP1=98.00,139.00\r\nHookP2=20.00,20.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 4, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "menuid", "DataLength": 50, "GraphDesc": "P1=2003.00,565.82\r\nP2=800.00,565.82\r\nP3=800.00,742.82\r\nP4=762.00,742.82\r\nHookP1=174.00,86.00\r\nHookP2=135.00,19.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 5, "Name": "ACTIONID", "OrderNo": 4, "DisplayName": "按钮id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "actionid", "DataLength": 50, "GraphDesc": "P1=2003.00,496.00\r\nP2=800.00,496.00\r\nP3=800.00,673.00\r\nP4=762.00,673.00\r\nHookP1=22.00,86.00\r\nHookP2=111.00,19.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 6, "Name": "DATATYPE", "Memo": "0:仅本人；1：全部；2：本部门；3：本部门及以下；4：自定义", "OrderNo": 5, "DisplayName": "数据类型", "DataType": 2}]}}, {"ID": 39, "Name": "SYS_POST_DATA_ORG", "Caption": "用户拥有数据权限机构", "CreateDate": "2020/3/3 星期二 13:37:11", "OrderNo": 12, "GraphDesc": "Left=508.00\r\nTop=863.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "POSTID", "OrderNo": 2, "DisplayName": "岗位id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post_datatype", "RelateField": "postid", "DataLength": 50, "GraphDesc": "P1=635.00,825.82\r\nP2=635.00,845.00\r\nP3=635.00,845.00\r\nP4=635.00,863.82\r\nHookP1=127.00,75.00\r\nHookP2=127.00,20.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 4, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post_datatype", "RelateField": "menuid", "DataLength": 50, "GraphDesc": "P1=651.00,756.00\r\nP2=651.00,775.00\r\nP3=651.00,775.00\r\nP4=651.00,794.00\r\nHookP1=143.00,19.00\r\nHookP2=143.00,59.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 5, "Name": "ACTIONID", "OrderNo": 4, "DisplayName": "按钮id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post_datatype", "RelateField": "actionid", "DataLength": 50, "GraphDesc": "P1=667.00,756.00\r\nP2=667.00,775.00\r\nP3=667.00,775.00\r\nP4=667.00,794.00\r\nHookP1=159.00,91.00\r\nHookP2=159.00,35.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 6, "Name": "ORGCODE", "OrderNo": 5, "DisplayName": "机构编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=491.00,2079.82\r\nP2=491.00,1549.82\r\nP3=555.00,1549.82\r\nP4=555.00,965.82\r\nHookP1=187.00,147.00\r\nHookP2=47.00,75.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=0"}]}}, {"ID": 16, "Name": "SYS_EMPLOYEE_INFO", "Caption": "人员信息表（当前人员信息）", "CreateDate": "2020/3/1 星期日 16:03:09", "OrderNo": 13, "GraphDesc": "Left=797.00\r\nTop=966.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 32, "items": [{"ID": 628, "Name": "ID", "OrderNo": 1, "DisplayName": "用户ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "EMPNAME", "OrderNo": 2, "DisplayName": "用户姓名", "DataType": 1, "DataLength": 100}, {"ID": 5, "Name": "ENTRY_DATE", "OrderNo": 3, "DisplayName": "入职日期", "DataType": 4}, {"ID": 6, "Name": "SEX", "OrderNo": 4, "DisplayName": "性别", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "STAFF_NO", "OrderNo": 5, "DisplayName": "工号", "DataType": 1, "DataLength": 100}, {"ID": 8, "Name": "MOBILE", "OrderNo": 6, "DisplayName": "手机号", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "CARD_TYPE_CODE", "Memo": "1居民身份证，2军人证，3中国护照，4外国护照，5台湾居民来往大陆通行证，6港澳居民来往内地通行证，7其它", "OrderNo": 7, "DisplayName": "证件类型编码", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "CARD_TYPE_NAME", "OrderNo": 8, "DisplayName": "证件类型名称", "DataType": 1, "DataLength": 100}, {"ID": 10, "Name": "CARDNO", "OrderNo": 9, "DisplayName": "证件号", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "DUTYID", "OrderNo": 10, "DisplayName": "职务ID", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "POSITIONLEVELID", "OrderNo": 11, "DisplayName": "职级ID", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "POLITICS_STATUS", "Memo": "1党员、2预备党员、3团员、4群众、5其它", "OrderNo": 12, "DisplayName": "政治面貌", "DataType": 2, "DataLength": 50}, {"ID": 15, "Name": "MAIL", "OrderNo": 13, "DisplayName": "邮箱", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "QQ", "OrderNo": 14, "DisplayName": "QQ", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "WECHAT", "OrderNo": 15, "DisplayName": "微信", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "NATIONALITY", "OrderNo": 16, "DisplayName": "民族", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "NATIVE_PLACE", "OrderNo": 17, "DisplayName": "籍贯", "DataType": 1, "DataLength": 50}, {"ID": 20, "Name": "EDUCATION", "Memo": "1博士、2硕士、3本科、4大专、5高中、6初中、7小学、8其它", "OrderNo": 18, "DisplayName": "最高学历", "DataType": 2}, {"ID": 21, "Name": "MARITAL", "Memo": "1是0否", "OrderNo": 19, "DisplayName": "婚姻状况", "DataType": 2}, {"ID": 22, "Name": "BIRTHDAY_TYPE", "Memo": "1公立、2农历", "OrderNo": 20, "DisplayName": "生日类型", "DataType": 2}, {"ID": 23, "Name": "BIRTHDAY", "OrderNo": 21, "DisplayName": "出生日期", "DataType": 4}, {"ID": 24, "Name": "OLD_WORK_NUM", "OrderNo": 22, "DisplayName": "历史工龄", "DataType": 3}, {"ID": 25, "Name": "STAFF_TYPE", "Memo": "1全职、2兼职、3实习、4外派、5其它", "OrderNo": 23, "DisplayName": "员工类型", "DataType": 2}, {"ID": 26, "Name": "WORKPLACE", "Memo": "将来可以选择地址信息", "OrderNo": 24, "DisplayName": "工作地点", "DataType": 1, "DataLength": 500}, {"ID": 27, "Name": "ACCOUNT_TYPE", "Memo": "1城镇、2非城镇", "OrderNo": 25, "DisplayName": "户口类型", "DataType": 2}, {"ID": 28, "Name": "LIVEADDRESS", "OrderNo": 26, "DisplayName": "居住地址", "DataType": 1, "DataLength": 500}, {"ID": 29, "Name": "MEMO", "OrderNo": 27, "DisplayName": "描述", "DataType": 1, "DataLength": 2000}, {"ID": 868, "Name": "SORT", "OrderNo": 28, "DisplayName": "排序", "DataType": 2}, {"ID": 869, "Name": "USED", "OrderNo": 29, "DisplayName": "是否使用", "DataType": 2}, {"ID": 4, "Name": "DIMISSION_DATE", "OrderNo": 30, "DisplayName": "离退日期", "DataType": 4}, {"ID": 3, "Name": "STATUS", "Memo": "在职(1)、离职(0)、退休(-1)", "OrderNo": 31, "DisplayName": "人员状态", "DataType": 2}, {"ID": 32, "Name": "INVALID", "Memo": "1：失效，0：有效", "OrderNo": 32, "DisplayName": "失效记录标识", "DataType": 2}]}}, {"ID": 16, "Name": "SYS_EMPLOYEE_INFO_CHANGE", "Caption": "人员信息变动表【历史记录】", "CreateDate": "2020/3/1 星期日 16:03:09", "OrderNo": 14, "GraphDesc": "Left=104.00\r\nTop=1390.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 32, "items": [{"ID": 628, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 468, "Name": "EMPID", "OrderNo": 2, "DisplayName": "用户ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_info", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=797.00,1069.82\r\nP2=533.00,1069.82\r\nP3=533.00,1439.82\r\nP4=442.00,1439.82\r\nHookP1=246.00,103.00\r\nHookP2=156.00,49.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 2, "Name": "EMPNAME", "OrderNo": 3, "DisplayName": "用户姓名", "DataType": 1, "DataLength": 100}, {"ID": 5, "Name": "ENTRY_DATE", "OrderNo": 4, "DisplayName": "入职日期", "DataType": 4}, {"ID": 6, "Name": "SEX", "OrderNo": 5, "DisplayName": "性别", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "STAFF_NO", "OrderNo": 6, "DisplayName": "工号", "DataType": 1, "DataLength": 100}, {"ID": 8, "Name": "MOBILE", "OrderNo": 7, "DisplayName": "手机号", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "CARD_TYPE_CODE", "Memo": "1居民身份证，2军人证，3中国护照，4外国护照，5台湾居民来往大陆通行证，6港澳居民来往内地通行证，7其它", "OrderNo": 8, "DisplayName": "证件类型编码", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "CARD_TYPE_NAME", "OrderNo": 9, "DisplayName": "证件类型名称", "DataType": 1, "DataLength": 100}, {"ID": 10, "Name": "CARDNO", "OrderNo": 10, "DisplayName": "证件号", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "DUTYID", "OrderNo": 11, "DisplayName": "职务ID", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "POSITIONLEVELID", "OrderNo": 12, "DisplayName": "职级ID", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "POLITICS_STATUS", "Memo": "1党员、2预备党员、3团员、4群众、5其它", "OrderNo": 13, "DisplayName": "政治面貌", "DataType": 2, "DataLength": 50}, {"ID": 15, "Name": "MAIL", "OrderNo": 14, "DisplayName": "邮箱", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "QQ", "OrderNo": 15, "DisplayName": "QQ", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "WECHAT", "OrderNo": 16, "DisplayName": "微信", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "NATIONALITY", "OrderNo": 17, "DisplayName": "民族", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "NATIVE_PLACE", "OrderNo": 18, "DisplayName": "籍贯", "DataType": 1, "DataLength": 50}, {"ID": 20, "Name": "EDUCATION", "Memo": "1博士、2硕士、3本科、4大专、5高中、6初中、7小学、8其它", "OrderNo": 19, "DisplayName": "最高学历", "DataType": 2}, {"ID": 21, "Name": "MARITAL", "Memo": "1是0否", "OrderNo": 20, "DisplayName": "婚姻状况", "DataType": 2}, {"ID": 22, "Name": "BIRTHDAY_TYPE", "Memo": "1公立、2农历", "OrderNo": 21, "DisplayName": "生日类型", "DataType": 2}, {"ID": 23, "Name": "BIRTHDAY", "OrderNo": 22, "DisplayName": "出生日期", "DataType": 4}, {"ID": 24, "Name": "OLD_WORK_NUM", "OrderNo": 23, "DisplayName": "历史工龄", "DataType": 3}, {"ID": 25, "Name": "STAFF_TYPE", "Memo": "1全职、2兼职、3实习、4外派、5其它", "OrderNo": 24, "DisplayName": "员工类型", "DataType": 2}, {"ID": 26, "Name": "WORKPLACE", "Memo": "将来可以选择地址信息", "OrderNo": 25, "DisplayName": "工作地点", "DataType": 1, "DataLength": 500}, {"ID": 27, "Name": "ACCOUNT_TYPE", "Memo": "1城镇、2非城镇", "OrderNo": 26, "DisplayName": "户口类型", "DataType": 2}, {"ID": 28, "Name": "LIVEADDRESS", "OrderNo": 27, "DisplayName": "居住地址", "DataType": 1, "DataLength": 500}, {"ID": 29, "Name": "MEMO", "OrderNo": 28, "DisplayName": "描述", "DataType": 1, "DataLength": 2000}, {"ID": 900, "Name": "SORT", "OrderNo": 29, "DisplayName": "排序", "DataType": 2}, {"ID": 901, "Name": "USED", "OrderNo": 30, "DisplayName": "是否使用", "DataType": 2}, {"ID": 4, "Name": "CHANGE_DATE", "OrderNo": 31, "DisplayName": "变动日期", "DataType": 4}, {"ID": 3, "Name": "CHANGE_TYPE", "Memo": "1新增2修改3机构调出4机构调入5岗位调出6岗位调入7离职8退休", "OrderNo": 32, "DisplayName": "变动类型", "DataType": 2}]}}, {"ID": 16, "Name": "SYS_EMPLOYEE_ORG", "Caption": "人员机构表（索引{关系}、兼机构）", "CreateDate": "2020/3/1 星期日 16:03:09", "OrderNo": 15, "GraphDesc": "Left=1238.00\r\nTop=961.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 6, "items": [{"ID": 628, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 649, "Name": "EMPID", "OrderNo": 2, "DisplayName": "用户ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_info", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1089.00,1023.82\r\nP2=1164.00,1023.82\r\nP3=1164.00,1023.82\r\nP4=1238.00,1023.82\r\nHookP1=117.00,57.00\r\nHookP2=20.00,62.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 770, "Name": "ORGCODE", "OrderNo": 3, "DisplayName": "机构编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org", "RelateField": "orgcode", "DataLength": 50, "GraphDesc": "P1=488.00,2079.82\r\nP2=488.00,2060.00\r\nP3=1372.00,2060.00\r\nP4=1372.00,1079.82\r\nHookP1=184.00,91.00\r\nHookP2=134.00,97.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 836, "Name": "STATUS", "Memo": "1主机构2兼机构", "OrderNo": 4, "DisplayName": "是否主机构", "DataType": 2}, {"ID": 838, "Name": "SORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}, {"ID": 837, "Name": "USED", "OrderNo": 6, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 16, "Name": "SYS_EMPLOYEE_ORG_CHANGE", "Caption": "人员机构变动表(历史记录)", "CreateDate": "2020/3/1 星期日 16:03:09", "OrderNo": 16, "GraphDesc": "Left=1242.00\r\nTop=1153.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 8, "items": [{"ID": 628, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 649, "Name": "EMPID", "OrderNo": 2, "DisplayName": "用户ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_org", "RelateField": "empid", "DataLength": 50, "GraphDesc": "P1=1401.00,1079.82\r\nP2=1401.00,1117.00\r\nP3=1401.00,1117.00\r\nP4=1401.00,1153.82\r\nHookP1=163.00,82.18\r\nHookP2=159.00,20.18\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 770, "Name": "ORGCODE", "OrderNo": 3, "DisplayName": "机构编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org", "RelateField": "orgcode", "DataLength": 50, "GraphDesc": "P1=515.00,2154.82\r\nP2=1543.00,2154.82\r\nP3=1543.00,2154.82\r\nP4=1543.00,1303.82\r\nHookP1=203.00,75.00\r\nHookP2=301.00,83.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=0"}, {"ID": 836, "Name": "STATUS", "Memo": "1主机构2兼机构", "OrderNo": 4, "DisplayName": "是否主机构", "DataType": 2}, {"ID": 838, "Name": "SORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}, {"ID": 837, "Name": "USED", "OrderNo": 6, "DisplayName": "是否使用", "DataType": 2}, {"ID": 1054, "Name": "CHANGE_DATE", "OrderNo": 7, "DisplayName": "变动日期", "DataType": 4}, {"ID": 1094, "Name": "CHANGE_TYPE", "Memo": "1新增2修改3机构调出4机构调入5岗位调出6岗位调入7离职8退休", "OrderNo": 8, "DisplayName": "变动类型", "DataType": 2}]}}, {"ID": 16, "Name": "SYS_EMPLOYEE_ORG_POST", "Caption": "人员机构岗位表（索引{关系}、兼岗位、借调）", "CreateDate": "2020/3/1 星期日 16:03:09", "OrderNo": 17, "GraphDesc": "Left=101.00\r\nTop=978.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 7, "items": [{"ID": 628, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 649, "Name": "EMPID", "OrderNo": 2, "DisplayName": "用户ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_info", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=797.00,1053.82\r\nP2=660.00,1053.82\r\nP3=660.00,1053.82\r\nP4=523.00,1053.82\r\nHookP1=28.00,87.00\r\nHookP2=206.00,75.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 770, "Name": "ORGCODE", "OrderNo": 3, "DisplayName": "机构编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org", "RelateField": "orgcode", "DataLength": 50, "GraphDesc": "P1=479.00,2079.82\r\nP2=479.00,1534.00\r\nP3=499.00,1534.00\r\nP4=499.00,1112.82\r\nHookP1=175.00,52.00\r\nHookP2=398.00,75.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=0"}, {"ID": 1132, "Name": "POSTID", "OrderNo": 4, "DisplayName": "岗位ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_post", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=182.00,315.82\r\nP2=182.00,647.00\r\nP3=182.00,647.00\r\nP4=182.00,978.82\r\nHookP1=81.00,35.00\r\nHookP2=81.00,75.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 836, "Name": "STATUS", "Memo": "1主岗位2兼岗位3借调岗位", "OrderNo": 5, "DisplayName": "是否主岗位", "DataType": 2}, {"ID": 838, "Name": "SORT", "OrderNo": 6, "DisplayName": "排序", "DataType": 2}, {"ID": 837, "Name": "USED", "OrderNo": 7, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 16, "Name": "SYS_EMPLOYEE_ORG_POST_CHANGE", "Caption": "人员机构岗位变动表(历史记录)", "CreateDate": "2020/3/1 星期日 16:03:09", "OrderNo": 18, "GraphDesc": "Left=104.00\r\nTop=1163.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 9, "items": [{"ID": 628, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 649, "Name": "EMPID", "OrderNo": 2, "DisplayName": "用户ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_org_post", "RelateField": "empid", "DataLength": 50, "GraphDesc": "P1=280.00,1112.82\r\nP2=280.00,1138.00\r\nP3=292.00,1138.00\r\nP4=292.00,1163.82\r\nHookP1=179.00,98.00\r\nHookP2=188.00,20.18\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 770, "Name": "ORGCODE", "OrderNo": 3, "DisplayName": "机构编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org", "RelateField": "orgcode", "DataLength": 50, "GraphDesc": "P1=468.00,2079.82\r\nP2=468.00,1705.00\r\nP3=468.00,1705.00\r\nP4=468.00,1329.82\r\nHookP1=164.00,131.00\r\nHookP2=364.00,91.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 1132, "Name": "POSTID", "OrderNo": 4, "DisplayName": "岗位ID", "DataType": 1, "DataLength": 50}, {"ID": 836, "Name": "STATUS", "Memo": "1主岗位2兼岗位3借调岗位", "OrderNo": 5, "DisplayName": "是否主岗位", "DataType": 2}, {"ID": 838, "Name": "SORT", "OrderNo": 6, "DisplayName": "排序", "DataType": 2}, {"ID": 837, "Name": "USED", "OrderNo": 7, "DisplayName": "是否使用", "DataType": 2}, {"ID": 1184, "Name": "CHANGE_DATE", "OrderNo": 8, "DisplayName": "变动日期", "DataType": 4}, {"ID": 1185, "Name": "CHANGE_TYPE", "Memo": "1新增2修改3机构调出4机构调入5岗位调出6岗位调入7离职8退休", "OrderNo": 9, "DisplayName": "变动类型", "DataType": 2}]}}, {"ID": 16, "Name": "SYS_EMPLOYEE_CHANGE_TODO", "Caption": "人员变动待办表（预变动-调度）", "CreateDate": "2020/3/1 星期日 16:03:09", "OrderNo": 19, "GraphDesc": "Left=513.00\r\nTop=1705.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 17, "items": [{"ID": 628, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 1196, "Name": "EMPID", "OrderNo": 2, "DisplayName": "用户ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_info", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=958.00,1500.82\r\nP2=958.00,1616.82\r\nP3=758.00,1616.82\r\nP4=758.00,1705.82\r\nHookP1=161.00,211.00\r\nHookP2=245.00,107.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=0"}, {"ID": 411, "Name": "EMPNAME", "OrderNo": 3, "DisplayName": "用户", "DataType": 1, "DataLength": 100}, {"ID": 1197, "Name": "OLD_POSTID", "OrderNo": 4, "DisplayName": "原岗位ID", "DataType": 1, "DataLength": 50}, {"ID": 412, "Name": "OLD_POSTNAME", "OrderNo": 5, "DisplayName": "原岗位", "DataType": 1, "DataLength": 100}, {"ID": 1198, "Name": "NEW_POSTID", "OrderNo": 6, "DisplayName": "新岗位ID", "DataType": 1, "DataLength": 50}, {"ID": 413, "Name": "NEW_POSTNAME", "OrderNo": 7, "DisplayName": "新岗位", "DataType": 1, "DataLength": 100}, {"ID": 1199, "Name": "OLD_ORGCODE", "OrderNo": 8, "DisplayName": "原部门ID", "DataType": 1, "DataLength": 50}, {"ID": 414, "Name": "OLD_ORGNAME", "OrderNo": 9, "DisplayName": "原部门", "DataType": 1, "DataLength": 100}, {"ID": 1200, "Name": "NEW_ORGCODE", "OrderNo": 10, "DisplayName": "新部门ID", "DataType": 1, "DataLength": 50}, {"ID": 415, "Name": "NEW_ORGNAME", "OrderNo": 11, "DisplayName": "新部门", "DataType": 1, "DataLength": 100}, {"ID": 1201, "Name": "STATUS", "Memo": "1主机构岗位2兼机构岗位3借调机构岗位", "OrderNo": 12, "DisplayName": "是否主机构主岗位", "DataType": 2}, {"ID": 1232, "Name": "CHANGE_DATE", "OrderNo": 13, "DisplayName": "变动日期", "DataType": 4}, {"ID": 1233, "Name": "CHANGE_TYPE", "Memo": "1主机构岗位调动2兼机构岗位调动3借调机构岗位调动", "OrderNo": 14, "DisplayName": "变动类型", "DataType": 2}, {"ID": 1262, "Name": "RUN_STATUS", "Memo": "1已运行0未运行", "OrderNo": 15, "DisplayName": "运行状态", "DataType": 2}, {"ID": 1509, "Name": "MEMO", "OrderNo": 16, "DisplayName": "描述", "DataType": 1, "DataLength": 1000}, {"ID": 1510, "Name": "USED", "OrderNo": 17, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 16, "Name": "SYS_EMPLOYEE_CHANGE_INFO", "Caption": "人员变动流水表", "CreateDate": "2020/3/1 星期日 16:03:09", "OrderNo": 20, "GraphDesc": "Left=894.00\r\nTop=1704.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 16, "items": [{"ID": 628, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 1196, "Name": "EMPID", "OrderNo": 2, "DisplayName": "用户ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_info", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=975.00,1500.82\r\nP2=975.00,1614.82\r\nP3=975.00,1614.82\r\nP4=975.00,1704.82\r\nHookP1=178.00,121.00\r\nHookP2=81.00,99.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=0"}, {"ID": 378, "Name": "EMPNAME", "OrderNo": 3, "DisplayName": "用户", "DataType": 1, "DataLength": 100}, {"ID": 1199, "Name": "OLD_ORGCODE", "OrderNo": 4, "DisplayName": "原部门ID", "DataType": 1, "DataLength": 50}, {"ID": 379, "Name": "OLD_ORGNAME", "OrderNo": 5, "DisplayName": "原部门", "DataType": 1, "DataLength": 100}, {"ID": 1197, "Name": "OLD_POSTID", "OrderNo": 6, "DisplayName": "原岗位ID", "DataType": 1, "DataLength": 50}, {"ID": 380, "Name": "OLD_POSTNAME", "OrderNo": 7, "DisplayName": "原岗位", "DataType": 1, "DataLength": 100}, {"ID": 1200, "Name": "NEW_ORGCODE", "OrderNo": 8, "DisplayName": "新部门ID", "DataType": 1, "DataLength": 50}, {"ID": 381, "Name": "NEW_ORGNAME", "OrderNo": 9, "DisplayName": "新部门", "DataType": 1, "DataLength": 100}, {"ID": 1198, "Name": "NEW_POSTID", "OrderNo": 10, "DisplayName": "新岗位ID", "DataType": 1, "DataLength": 50}, {"ID": 382, "Name": "NEW_POSTNAME", "OrderNo": 11, "DisplayName": "新岗位", "DataType": 1, "DataLength": 100}, {"ID": 1201, "Name": "STATUS", "Memo": "1主机构岗位2兼机构岗位3借调机构岗位", "OrderNo": 12, "DisplayName": "是否主机构主岗位", "DataType": 2}, {"ID": 1232, "Name": "CHANGE_DATE", "OrderNo": 13, "DisplayName": "变动日期", "DataType": 4}, {"ID": 1233, "Name": "CHANGE_TYPE", "Memo": "1新增2修改3机构调出4机构调入5岗位调出6岗位调入7离职8退休", "OrderNo": 14, "DisplayName": "变动类型", "DataType": 2}, {"ID": 1386, "Name": "MEMO", "OrderNo": 15, "DisplayName": "描述", "DataType": 1, "DataLength": 1500}, {"ID": 1387, "Name": "USED", "OrderNo": 16, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 16, "Name": "SYS_USER_ROLE", "Caption": "人员角色表", "CreateDate": "2020/3/1 星期日 16:03:09", "OrderNo": 21, "GraphDesc": "Left=1243.00\r\nTop=1364.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 3, "items": [{"ID": 628, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 1659, "Name": "EMPID", "OrderNo": 2, "DisplayName": "用户ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_info", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1089.00,1038.82\r\nP2=1213.00,1038.82\r\nP3=1213.00,1400.00\r\nP4=1243.00,1400.00\r\nHookP1=157.00,72.00\r\nHookP2=20.00,35.18\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 1660, "Name": "ROLEID", "OrderNo": 3, "DisplayName": "角色ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_role", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1726.00,479.82\r\nP2=1677.00,479.82\r\nP3=1677.00,1391.82\r\nP4=1412.00,1391.82\r\nHookP1=126.00,101.00\r\nHookP2=92.00,27.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}]}}, {"ID": 16, "Name": "SYS_EMPLOYEE_PERMIT", "Caption": "人员权限表", "CreateDate": "2020/3/1 星期日 16:03:09", "OrderNo": 22, "GraphDesc": "Left=1244.00\r\nTop=1454.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 4, "items": [{"ID": 628, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 1671, "Name": "EMPID", "OrderNo": 2, "DisplayName": "用户ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_info", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1089.00,1055.82\r\nP2=1196.00,1055.82\r\nP3=1196.00,1475.00\r\nP4=1244.00,1475.00\r\nHookP1=205.00,89.00\r\nHookP2=20.00,20.18\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 1672, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "menuid", "DataLength": 50, "GraphDesc": "P1=2034.00,613.82\r\nP2=2034.00,1483.00\r\nP3=2034.00,1483.00\r\nP4=1446.00,1483.00\r\nHookP1=31.00,107.00\r\nHookP2=174.00,28.18\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 11, "Name": "ACTIONID", "OrderNo": 4, "DisplayName": "按钮ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "actionid", "DataLength": 50, "GraphDesc": "P1=2054.00,544.00\r\nP2=2054.00,1425.00\r\nP3=2054.00,1425.00\r\nP4=1427.00,1425.00\r\nHookP1=51.00,107.00\r\nHookP2=76.00,40.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}]}}, {"ID": 16, "Name": "SYS_EMPLOYEE_PERMIT_NOT", "Caption": "人员权限刨除表", "CreateDate": "2020/3/1 星期日 16:03:09", "OrderNo": 23, "GraphDesc": "Left=1243.00\r\nTop=1556.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 4, "items": [{"ID": 628, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 1680, "Name": "EMPID", "OrderNo": 2, "DisplayName": "用户ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_info", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1089.00,1071.82\r\nP2=1184.00,1071.82\r\nP3=1184.00,1579.82\r\nP4=1243.00,1579.82\r\nHookP1=206.00,105.00\r\nHookP2=114.00,23.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 1681, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "menuid", "DataLength": 50, "GraphDesc": "P1=2044.00,613.82\r\nP2=2044.00,1578.82\r\nP3=2044.00,1578.82\r\nP4=1497.00,1578.82\r\nHookP1=41.00,19.00\r\nHookP2=122.00,22.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 19, "Name": "ACTIONID", "OrderNo": 4, "DisplayName": "按钮ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "actionid", "DataLength": 50, "GraphDesc": "P1=2071.00,544.00\r\nP2=2071.00,1522.00\r\nP3=2071.00,1522.00\r\nP4=1471.00,1522.00\r\nHookP1=68.00,123.00\r\nHookP2=192.00,35.00\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}]}}, {"ID": 38, "Name": "SYS_EMPLOYEE_DATATYPE", "Caption": "用户拥有数据权限类型", "CreateDate": "2020/3/3 星期二 13:35:05", "OrderNo": 24, "GraphDesc": "Left=1242.00\r\nTop=1665.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "EMPID", "OrderNo": 2, "DisplayName": "人员id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_info", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1089.00,1088.82\r\nP2=1169.00,1088.82\r\nP3=1169.00,1690.82\r\nP4=1242.00,1690.82\r\nHookP1=188.00,122.00\r\nHookP2=127.00,25.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "menuid", "DataLength": 50, "GraphDesc": "P1=2055.00,613.82\r\nP2=2055.00,1700.82\r\nP3=2055.00,1700.82\r\nP4=1522.00,1700.82\r\nHookP1=52.00,71.00\r\nHookP2=135.00,35.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 5, "Name": "ACTIONID", "OrderNo": 4, "DisplayName": "按钮id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "actionid", "DataLength": 50, "GraphDesc": "P1=2055.00,544.00\r\nP2=2055.00,1631.00\r\nP3=2055.00,1631.00\r\nP4=1496.00,1631.00\r\nHookP1=52.00,60.00\r\nHookP2=111.00,35.00\r\nMod_OP1=0\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 6, "Name": "DATATYPE", "Memo": "0:仅本人；1：全部；2：本部门；3：本部门及以下；4：自定义", "OrderNo": 5, "DisplayName": "数据类型", "DataType": 2}]}}, {"ID": 39, "Name": "SYS_EMPLOYEE_DATA_ORG", "Caption": "用户拥有数据权限机构", "CreateDate": "2020/3/3 星期二 13:37:11", "OrderNo": 25, "GraphDesc": "Left=1242.00\r\nTop=1809.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "EMPID", "OrderNo": 2, "DisplayName": "人员id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_datatype", "RelateField": "empid", "DataLength": 50, "GraphDesc": "P1=1369.00,1767.82\r\nP2=1369.00,1789.00\r\nP3=1369.00,1789.00\r\nP4=1369.00,1809.82\r\nHookP1=127.00,75.00\r\nHookP2=127.00,20.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 4, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_datatype", "RelateField": "menuid", "DataLength": 50, "GraphDesc": "P1=1385.00,1698.00\r\nP2=1385.00,1719.00\r\nP3=1385.00,1719.00\r\nP4=1385.00,1740.00\r\nHookP1=143.00,19.00\r\nHookP2=143.00,59.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 5, "Name": "ACTIONID", "OrderNo": 4, "DisplayName": "按钮id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_employee_datatype", "RelateField": "actionid", "DataLength": 50, "GraphDesc": "P1=1401.00,1698.00\r\nP2=1401.00,1719.00\r\nP3=1401.00,1719.00\r\nP4=1401.00,1740.00\r\nHookP1=159.00,59.00\r\nHookP2=159.00,35.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 6, "Name": "ORGCODE", "OrderNo": 5, "DisplayName": "机构编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=515.00,2138.82\r\nP2=1385.00,2138.82\r\nP3=1385.00,2138.82\r\nP4=1385.00,1911.82\r\nHookP1=42.00,59.00\r\nHookP2=143.00,59.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=0"}]}}, {"ID": 26, "Name": "SYS_ORG", "Caption": "机构信息", "CreateDate": "2020/3/3 星期二 08:52:14", "OrderNo": 26, "GraphDesc": "Left=304.00\r\nTop=2079.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 10, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 4, "Name": "ORGCODE", "OrderNo": 2, "DisplayName": "机构编号", "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "ORGNAME", "OrderNo": 3, "DisplayName": "机构名称", "DataType": 1, "DataLength": 200}, {"ID": 6, "Name": "ORGLEVEL", "OrderNo": 4, "DisplayName": "级别", "DataType": 2}, {"ID": 8, "Name": "SORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}, {"ID": 9, "Name": "ORGPATH", "OrderNo": 6, "DisplayName": "机构全路径", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "VERSION_DATE", "OrderNo": 7, "DisplayName": "版本日期", "DataType": 1, "DataLength": 20}, {"ID": 11, "Name": "USED", "OrderNo": 8, "DisplayName": "是否可以", "DataType": 2}, {"ID": 220, "Name": "ORG_NUMBER", "OrderNo": 9, "DisplayName": "部门编号", "DataType": 1, "DataLength": 100}, {"ID": 221, "Name": "ORG_HEAD", "OrderNo": 10, "DisplayName": "部门负责人", "DataType": 1, "DataLength": 200}]}}, {"ID": 27, "Name": "SYS_ORG_RELATION", "Caption": "机构关系表", "CreateDate": "2020/3/3 星期二 09:42:10", "OrderNo": 27, "GraphDesc": "Left=12.00\r\nTop=2075.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 3, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 4, "Name": "ORGCODE", "OrderNo": 2, "DisplayName": "机构编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org", "RelateField": "Id", "DataLength": 50, "GraphDesc": "P1=304.00,2114.82\r\nP2=256.00,2114.82\r\nP3=256.00,2114.82\r\nP4=209.00,2114.82\r\nHookP1=20.00,35.00\r\nHookP2=177.00,39.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 5, "Name": "PORGCODE", "OrderNo": 3, "DisplayName": "父机构编码", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=2163.00,932.00\r\nP2=2104.00,932.00\r\nP3=2104.00,932.00\r\nP4=2045.00,932.00\r\nHookP1=28.00,83.00\r\nHookP2=169.00,43.00\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}]}}, {"ID": 28, "Name": "SYS_ORG_VERSIONS", "Caption": "机构版本主表", "CreateDate": "2020/3/3 星期二 09:45:24", "OrderNo": 28, "GraphDesc": "Left=17.00\r\nTop=2312.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 3, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 148, "Name": "VERSION_DATE", "OrderNo": 2, "DisplayName": "版本日期", "DataType": 1, "DataLength": 20}, {"ID": 149, "Name": "USED", "OrderNo": 3, "DisplayName": "是否生效", "DataType": 2}]}}, {"ID": 26, "Name": "SYS_ORG_VERSIONS_DETAIL", "Caption": "机构版本明细信息", "CreateDate": "2020/3/3 星期二 08:52:14", "OrderNo": 29, "GraphDesc": "Left=303.00\r\nTop=2312.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 10, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 4, "Name": "ORGCODE", "OrderNo": 2, "DisplayName": "机构编号", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org", "RelateField": "orgcode", "DataLength": 50, "GraphDesc": "P1=458.00,2261.82\r\nP2=458.00,2287.00\r\nP3=458.00,2287.00\r\nP4=458.00,2312.82\r\nHookP1=154.00,114.18\r\nHookP2=155.00,28.18\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 5, "Name": "ORGNAME", "OrderNo": 3, "DisplayName": "机构名称", "DataType": 1, "DataLength": 200}, {"ID": 6, "Name": "ORGLEVEL", "OrderNo": 4, "DisplayName": "级别", "DataType": 2}, {"ID": 8, "Name": "SORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}, {"ID": 9, "Name": "ORGPATH", "OrderNo": 6, "DisplayName": "机构全路径", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "VERSION_DATE", "OrderNo": 7, "DisplayName": "版本日期", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org_versions", "RelateField": "version_date", "DataLength": 20, "GraphDesc": "P1=228.00,2347.82\r\nP2=266.00,2347.82\r\nP3=266.00,2347.82\r\nP4=303.00,2347.82\r\nHookP1=124.00,35.00\r\nHookP2=36.00,35.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 11, "Name": "USED", "OrderNo": 8, "DisplayName": "是否可以", "DataType": 2}, {"ID": 230, "Name": "ORG_NUMBER", "OrderNo": 9, "DisplayName": "部门编号", "DataType": 1, "DataLength": 100}, {"ID": 231, "Name": "ORG_HEAD", "OrderNo": 10, "DisplayName": "部门负责人", "DataType": 1, "DataLength": 200}]}}, {"ID": 30, "Name": "SYS_ORG_LOG", "Caption": "机构日志", "CreateDate": "2020/3/3 星期二 09:50:02", "OrderNo": 30, "GraphDesc": "Left=303.00\r\nTop=2546.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 10, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 169, "Name": "VERSION_DATE", "OrderNo": 2, "DisplayName": "版本日期", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org_versions_detail", "RelateField": "version_date", "DataLength": 20, "GraphDesc": "P1=420.00,2494.82\r\nP2=420.00,2521.00\r\nP3=420.00,2521.00\r\nP4=420.00,2546.82\r\nHookP1=117.00,91.00\r\nHookP2=117.00,91.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 170, "Name": "FORMER_ORGCODE", "OrderNo": 3, "DisplayName": "原机构编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org_versions_detail", "RelateField": "orgcode", "DataLength": 50, "GraphDesc": "P1=438.00,2368.00\r\nP2=438.00,2410.00\r\nP3=438.00,2410.00\r\nP4=438.00,2452.00\r\nHookP1=152.00,98.00\r\nHookP2=176.00,36.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 171, "Name": "FORMER_ORGNAME", "OrderNo": 4, "DisplayName": "原机构名称", "DataType": 1, "DataLength": 200}, {"ID": 172, "Name": "FORMER_PORGCODE", "OrderNo": 5, "DisplayName": "原上级机构编码", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=2017.00,959.00\r\nP2=2017.00,1423.00\r\nP3=2017.00,1423.00\r\nP4=2134.00,1423.00\r\nHookP1=169.00,43.00\r\nHookP2=28.00,28.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=1"}, {"ID": 173, "Name": "DESTINATION_ORGCODE", "OrderNo": 6, "DisplayName": "目标机构编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org_versions_detail", "RelateField": "orgcode", "DataLength": 50, "GraphDesc": "P1=412.00,2368.00\r\nP2=412.00,2410.00\r\nP3=412.00,2410.00\r\nP4=412.00,2452.00\r\nHookP1=126.00,90.00\r\nHookP2=150.00,44.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 174, "Name": "DESTINATION_ORGNAME", "OrderNo": 7, "DisplayName": "目标机构名称", "DataType": 1, "DataLength": 200}, {"ID": 175, "Name": "DESTINATION_PORGCODE", "OrderNo": 8, "DisplayName": "目标上级机构编码", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=2017.00,959.00\r\nP2=2017.00,1423.00\r\nP3=2017.00,1423.00\r\nP4=2134.00,1423.00\r\nHookP1=169.00,43.00\r\nHookP2=28.00,28.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=1"}, {"ID": 176, "Name": "VERSION_TYPE", "OrderNo": 9, "DisplayName": "版本类型", "DataType": 2}, {"ID": 177, "Name": "DESCRIPTION", "OrderNo": 10, "DisplayName": "描述", "DataType": 1, "DataLength": 4000}]}}, {"ID": 41, "Name": "SYS_MODULE_LIB", "Caption": "模块", "CreateDate": "2020/3/3 星期二 13:54:08", "OrderNo": 31, "GraphDesc": "Left=2303.00\r\nTop=311.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "CODE", "OrderNo": 2, "DisplayName": "编码", "DataType": 1, "DataLength": 255}, {"ID": 3, "Name": "NAME", "OrderNo": 3, "DisplayName": "模块名称", "DataType": 1, "DataLength": 200}, {"ID": 4, "Name": "SORT", "OrderNo": 4, "DisplayName": "排序", "DataType": 2}, {"ID": 5, "Name": "USED", "OrderNo": 5, "DisplayName": "使用", "DataType": 2}, {"ID": 6, "Name": "DESCRIPTION", "OrderNo": 6, "DisplayName": "描述", "DataType": 1, "DataLength": 2000}]}}, {"ID": 40, "Name": "SYS_MENU_LIB", "Caption": "菜单库", "CreateDate": "2020/3/3 星期二 13:42:38", "OrderNo": 32, "GraphDesc": "Left=2298.00\r\nTop=479.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "CODE", "OrderNo": 2, "DisplayName": "编码", "DataType": 1, "DataLength": 255}, {"ID": 9, "Name": "MODULECODE", "OrderNo": 3, "DisplayName": "模块", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_module_lib", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=2402.00,429.82\r\nP2=2402.00,455.00\r\nP3=2402.00,455.00\r\nP4=2402.00,479.82\r\nHookP1=99.00,98.18\r\nHookP2=104.00,36.18\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 3, "Name": "NAME", "OrderNo": 4, "DisplayName": "菜单名称", "DataType": 1, "DataLength": 200}, {"ID": 4, "Name": "SORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}, {"ID": 5, "Name": "USED", "OrderNo": 6, "DisplayName": "使用", "DataType": 2}, {"ID": 6, "Name": "URL", "OrderNo": 7, "DisplayName": "地址", "DataType": 1, "DataLength": 255}, {"ID": 7, "Name": "PARAM", "OrderNo": 8, "DisplayName": "参数", "DataType": 1, "DataLength": 500}, {"ID": 8, "Name": "DESCRIPTION", "OrderNo": 9, "DisplayName": "描述", "DataType": 1, "DataLength": 100}]}}, {"ID": 31, "Name": "SYS_MENU_ACTION_LIB", "Caption": "菜单按钮库", "CreateDate": "2020/3/3 星期二 11:10:54", "OrderNo": 33, "GraphDesc": "Left=2003.00\r\nTop=479.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 2, "KeyFieldType": 1, "DataLength": 50}, {"ID": 250, "Name": "MENUID", "OrderNo": 2, "DisplayName": "菜单ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_lib", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=2298.00,546.82\r\nP2=2252.00,546.82\r\nP3=2252.00,546.82\r\nP4=2207.00,546.82\r\nHookP1=20.00,67.00\r\nHookP2=102.00,67.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 251, "Name": "ACTIONID", "Memo": "query，add，save，del，update", "OrderNo": 3, "DisplayName": "按钮ID", "DataType": 1, "DataLength": 50}, {"ID": 252, "Name": "NAME", "Memo": "查询，添加，保存，删除，修改", "OrderNo": 4, "DisplayName": "按钮名称", "DataType": 1, "DataLength": 200}, {"ID": 253, "Name": "DESCRIPTION", "OrderNo": 5, "DisplayName": "描述", "DataType": 1, "DataLength": 4000}, {"ID": 254, "Name": "USED", "OrderNo": 6, "DisplayName": "是否使用", "DataType": 2}, {"ID": 255, "Name": "SORT", "OrderNo": 7, "DisplayName": "排序字段", "DataType": 2}]}}, {"ID": 35, "Name": "SYS_MENU_FIELD_LIB", "Caption": "菜单字段库", "CreateDate": "2020/3/3 星期二 13:13:30", "OrderNo": 34, "GraphDesc": "Left=2578.00\r\nTop=477.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "MENUID", "OrderNo": 2, "DisplayName": "菜单ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_lib", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=2502.00,537.00\r\nP2=2540.00,537.00\r\nP3=2540.00,537.00\r\nP4=2578.00,537.00\r\nHookP1=176.00,57.18\r\nHookP2=20.00,59.18\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "FIELDID", "OrderNo": 3, "DisplayName": "字段ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "NAME", "OrderNo": 4, "DisplayName": "名称", "DataType": 1, "DataLength": 100}, {"ID": 5, "Name": "DESCRIPTION", "OrderNo": 5, "DisplayName": "注释", "DataType": 1, "DataLength": 500}, {"ID": 6, "Name": "USED", "OrderNo": 6, "DisplayName": "是否可用", "DataType": 2}, {"ID": 7, "Name": "SORT", "OrderNo": 7, "DisplayName": "排序", "DataType": 2}]}}, {"ID": 24, "Name": "SYS_ROLE", "Caption": "角色", "CreateDate": "2020/3/3 星期二 08:34:08", "OrderNo": 35, "GraphDesc": "Left=1726.00\r\nTop=378.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "权限id", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 67, "Name": "NAME", "OrderNo": 2, "DisplayName": "角色名称", "DataType": 1, "DataLength": 200}, {"ID": 3, "Name": "DESCRIPTION", "OrderNo": 3, "DisplayName": "角色描述", "DataType": 1, "DataLength": 500}, {"ID": 4, "Name": "LEVEL", "OrderNo": 4, "DisplayName": "级别", "DataType": 2}, {"ID": 5, "Name": "PID", "OrderNo": 5, "DisplayName": "上级角色id", "DataType": 1, "DataLength": 50}, {"ID": 6, "Name": "USED", "OrderNo": 6, "DisplayName": "是否使用", "DataType": 2}, {"ID": 7, "Name": "SORT", "OrderNo": 7, "DisplayName": "排序", "DataType": 2}]}}, {"ID": 32, "Name": "SYS_ROLE_PERM", "Caption": "角色拥有权限", "CreateDate": "2020/3/3 星期二 11:14:38", "OrderNo": 36, "GraphDesc": "Left=1735.00\r\nTop=712.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 4, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "ROLEID", "OrderNo": 2, "DisplayName": "角色id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_role", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1832.00,512.82\r\nP2=1832.00,613.00\r\nP3=1833.00,613.00\r\nP4=1833.00,712.82\r\nHookP1=106.00,98.00\r\nHookP2=98.00,27.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 3, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "menuid", "DataLength": 50, "GraphDesc": "P1=2012.00,613.82\r\nP2=2012.00,663.00\r\nP3=1854.00,663.00\r\nP4=1854.00,712.82\r\nHookP1=9.00,28.00\r\nHookP2=119.00,51.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 4, "Name": "ACTIONID", "OrderNo": 4, "DisplayName": "按钮id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "actionid", "DataLength": 50, "GraphDesc": "P1=2024.00,544.00\r\nP2=2024.00,604.00\r\nP3=1873.00,604.00\r\nP4=1873.00,643.00\r\nHookP1=21.00,91.00\r\nHookP2=138.00,27.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=0"}]}}, {"ID": 33, "Name": "SYS_ROLE_DATATYPE", "Caption": "角色拥有数据权限类型", "CreateDate": "2020/3/3 星期二 11:29:39", "OrderNo": 37, "GraphDesc": "Left=1731.00\r\nTop=866.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 293, "Name": "ROLEID", "OrderNo": 2, "DisplayName": "角色ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_role", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1726.00,501.82\r\nP2=1696.00,501.82\r\nP3=1696.00,890.82\r\nP4=1731.00,890.82\r\nHookP1=110.00,123.00\r\nHookP2=127.00,24.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 294, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=2024.00,613.82\r\nP2=2024.00,681.82\r\nP3=1965.00,681.82\r\nP4=1965.00,866.82\r\nHookP1=21.00,91.00\r\nHookP2=234.00,28.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=0"}, {"ID": 295, "Name": "ACTIONID", "OrderNo": 4, "DisplayName": "按钮ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_action_lib", "RelateField": "actionid", "DataLength": 50, "GraphDesc": "P1=2070.00,544.00\r\nP2=2070.00,627.00\r\nP3=1949.00,627.00\r\nP4=1949.00,797.00\r\nHookP1=67.00,35.00\r\nHookP2=218.00,36.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=0"}, {"ID": 296, "Name": "DATATYPE", "Memo": "0:仅本人；1：全部；2：本部门；3：本部门及以下；4：自定义", "OrderNo": 5, "DisplayName": "类型编码", "DataType": 2}]}}, {"ID": 34, "Name": "SYS_ROLE_ORG", "Caption": "角色拥有机构（数据权限）", "CreateDate": "2020/3/3 星期二 11:31:33", "OrderNo": 38, "GraphDesc": "Left=2107.00\r\nTop=866.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 173, "Name": "ROLEID", "OrderNo": 2, "DisplayName": "角色ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_role_datatype", "RelateField": "roleid", "DataLength": 50, "GraphDesc": "P1=1985.00,917.82\r\nP2=2078.00,917.82\r\nP3=2078.00,917.82\r\nP4=2107.00,917.82\r\nHookP1=151.00,51.00\r\nHookP2=20.00,51.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 174, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_role_datatype", "RelateField": "menuid", "DataLength": 50, "GraphDesc": "P1=1985.00,867.00\r\nP2=2033.00,867.00\r\nP3=2033.00,867.00\r\nP4=2081.00,867.00\r\nHookP1=202.00,70.00\r\nHookP2=132.00,70.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 175, "Name": "ACTIONID", "OrderNo": 4, "DisplayName": "按钮ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_role_datatype", "RelateField": "actionid", "DataLength": 50, "GraphDesc": "P1=1985.00,828.00\r\nP2=2033.00,828.00\r\nP3=2033.00,828.00\r\nP4=2081.00,828.00\r\nHookP1=167.00,31.00\r\nHookP2=36.00,31.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 176, "Name": "ORGCODE", "OrderNo": 5, "DisplayName": "机构ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_org", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=515.00,2185.82\r\nP2=2066.00,2185.82\r\nP3=2066.00,932.82\r\nP4=2107.00,932.82\r\nHookP1=162.00,106.00\r\nHookP2=132.00,66.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}]}}, {"ID": 36, "Name": "SYS_ROLE_FIELD", "Caption": "角色拥有字段权限", "CreateDate": "2020/3/3 星期二 13:26:35", "OrderNo": 39, "GraphDesc": "Left=1732.00\r\nTop=1028.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 4, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "ROLEID", "OrderNo": 2, "DisplayName": "角色ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_role", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1726.00,490.82\r\nP2=1686.00,490.82\r\nP3=1686.00,1071.82\r\nP4=1732.00,1071.82\r\nHookP1=86.00,112.00\r\nHookP2=104.00,43.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "MENUID", "OrderNo": 3, "DisplayName": "菜单ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_lib", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=2384.00,645.82\r\nP2=2384.00,1067.82\r\nP3=2384.00,1067.82\r\nP4=1940.00,1067.82\r\nHookP1=86.00,67.00\r\nHookP2=178.00,39.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 4, "Name": "FIELDID", "OrderNo": 4, "DisplayName": "字段ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "sys_menu_field_lib", "RelateField": "fieldid", "DataLength": 50, "GraphDesc": "P1=2688.00,611.82\r\nP2=2688.00,1084.82\r\nP3=2688.00,1084.82\r\nP4=1940.00,1084.82\r\nHookP1=110.00,75.00\r\nHookP2=88.00,56.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}]}}, {"ID": 12, "Name": "SYS_EMPLOYEE_CHANGE_ORG", "Caption": "人员机构变动信息表", "OrderNo": 40, "GraphDesc": "Left=10.00\r\nTop=2828.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "EMPLOYEEID", "OrderNo": 2, "DisplayName": "人员ID", "DataType": 1, "RelateTable": "sys_post", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=166.00,315.82\r\nP2=166.00,948.00\r\nP3=54.00,948.00\r\nP4=54.00,2828.82\r\nHookP1=65.00,91.18\r\nHookP2=44.00,20.18\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 554, "Name": "ORGCODE", "OrderNo": 3, "DisplayName": "机构代码", "DataType": 1, "RelateTable": "sys_role", "RelateField": "id", "DataLength": 50, "GraphDesc": "P1=1754.00,512.82\r\nP2=1754.00,2759.00\r\nP3=203.00,2759.00\r\nP4=203.00,2828.82\r\nHookP1=28.00,79.18\r\nHookP2=193.00,27.18\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 4, "Name": "STARTDT", "OrderNo": 4, "DisplayName": "开始日期", "DataType": 4}, {"ID": 5, "Name": "ENDDT", "OrderNo": 5, "DisplayName": "截止日期", "DataType": 4}, {"ID": 7, "Name": "DEPARTSTATUS", "Memo": "0在职 1离职", "OrderNo": 6, "DisplayName": "离职标识", "DataType": 2}]}}, {"ID": 12, "Name": "SYS_EMPLOYEE_CHANGE_POST", "Caption": "人员岗位变动信息表", "OrderNo": 41, "GraphDesc": "Left=10.00\r\nTop=3030.82\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "EMPLOYEEID", "OrderNo": 2, "DisplayName": "人员ID", "DataType": 1, "RelateTable": "sys_post", "RelateField": "id", "DataLength": 50}, {"ID": 7, "Name": "ORGCODE", "OrderNo": 3, "DisplayName": "机构代码", "DataType": 1}, {"ID": 554, "Name": "POSTID", "OrderNo": 4, "DisplayName": "岗位代码", "DataType": 1, "RelateTable": "sys_role", "RelateField": "id", "DataLength": 50}, {"ID": 4, "Name": "STARTDT", "OrderNo": 5, "DisplayName": "开始日期", "DataType": 4}, {"ID": 5, "Name": "ENDDT", "OrderNo": 6, "DisplayName": "截止日期", "DataType": 4}, {"ID": 6, "Name": "DEPARTSTATUS", "Memo": "0离职 1在职", "OrderNo": 7, "DisplayName": "离职标识", "DataType": 2}]}}]}}, {"ID": 1, "Name": "数据源", "CreateDate": "2021/9/27 星期一 08:41:01", "OrderNo": 5, "DefDbEngine": "SQLSERVER", "DbConnectStr": "TCtMetaSqlsvrDb", "ConfigStr": "DrawerWidth=2194\nDrawerHeight=1546\nWorkAreaColor=16777215\nSelectedColor=-16777203\nDefaultObjectColor=15921906\nDefaultTitleColor=255\nDefaultPKColor=16711935\nDefaultFKColor=16711680\nDefaultBorderColor=12632256\nDefaultLineColor=16711680\nShowFieldType=1\nShowFieldIcon=1\nShowPhyFieldName=2\nDatabaseEngine=\nGenFKIndexesSQL=0\nIndependPosForOverviewMode=0\n", "Tables": {"Count": 13, "items": [{"Name": "TDS_DATASOURCE", "Caption": "TM4-数据源信息表", "Memo": "for tm4", "OrderNo": 1, "GraphDesc": "Left=7.00\r\nTop=8.00\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 41, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 3, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "ADVANCEDSEARCH", "Memo": "高级检索功能 ，tm4未启用", "OrderNo": 6, "DisplayName": "高级检索功能", "DataType": 2}, {"ID": 7, "Name": "ALLOWNULL", "Memo": "是否允许为空（TM4未使用）", "OrderNo": 7, "DisplayName": "是否允许为空", "DataType": 2}, {"ID": 8, "Name": "ALLOWTOSAVE", "OrderNo": 8, "DisplayName": "数据源是否可编辑", "DataType": 2}, {"ID": 9, "Name": "AUTOLOAD", "OrderNo": 9, "DisplayName": "是否自动加载", "DataType": 2}, {"ID": 10, "Name": "CATEGORYID", "OrderNo": 10, "DisplayName": "分类ID", "DataType": 1, "DataLength": 255}, {"ID": 11, "Name": "CREATEUID", "OrderNo": 11, "DisplayName": "创建人id", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "CREATEUNAME", "OrderNo": 12, "DisplayName": "创建人", "DataType": 1, "DataLength": 255}, {"ID": 13, "Name": "DBCONNINFOID", "Memo": "数据库连接id（TM3外部数据源使用）", "OrderNo": 13, "DisplayName": "数据库连接id", "DataType": 1, "DataLength": 255}, {"ID": 14, "Name": "DBTABLENAME", "OrderNo": 14, "DisplayName": "数据源修改更新表名", "DataType": 1, "DataLength": 255}, {"ID": 15, "Name": "EXCELAUTOCREATE", "OrderNo": 15, "DisplayName": "TM3Excel数据源使用", "DataType": 2}, {"ID": 16, "Name": "EXCELMULTISHEET", "OrderNo": 16, "DisplayName": "TM3Excel数据源使用", "DataType": 2}, {"ID": 17, "Name": "FINDBYKEY", "OrderNo": 17, "DisplayName": "TM3使用", "DataType": 2}, {"ID": 18, "Name": "IMPORTDATABASE", "OrderNo": 18, "DisplayName": "TM4未使用", "DataType": 2}, {"ID": 19, "Name": "ISMAINDB", "Memo": "是否为系统库，tm3使用", "OrderNo": 19, "DisplayName": "是否为系统库", "DataType": 2}, {"ID": 20, "Name": "ISSYS", "Memo": "是否是系统内置数据源 1:内置（如：SysInfo）", "OrderNo": 20, "DisplayName": "是否是系统内置数据源", "DataType": 2}, {"ID": 21, "Name": "MEMO", "OrderNo": 21, "DisplayName": "备注说明", "DataType": 1, "DataLength": 255}, {"ID": 22, "Name": "MODULECODE", "OrderNo": 22, "DisplayName": "模块编码", "DataType": 1, "DataLength": 50}, {"ID": 23, "Name": "REGTIME", "OrderNo": 23, "DisplayName": "注册时间", "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 24, "Name": "SCRIPT", "Memo": "脚本（TM4未使用）", "OrderNo": 24, "DisplayName": "脚本", "DataType": 1, "DataLength": 255}, {"ID": 25, "Name": "TDSCALSCRIPT", "OrderNo": 25, "DisplayName": "数据源页面计算脚本", "DataType": 1, "DataLength": 2000}, {"ID": 26, "Name": "TDSCANEDITSCRIPT", "OrderNo": 26, "DisplayName": "数据源页面字段是否可以编辑脚本", "DataType": 1, "DataLength": 1000}, {"ID": 27, "Name": "TDSINITSQL", "OrderNo": 27, "DisplayName": "数据源修改默认初始化插入数据语句", "DataType": 1, "DataLength": 2000}, {"ID": 28, "Name": "TDSQUERYSQL", "OrderNo": 28, "DisplayName": "查询语句", "DataType": 1, "DataLength": 4000}, {"ID": 29, "Name": "TDSUPDATESQL", "Memo": "更新语句（未使用）", "OrderNo": 29, "DisplayName": "更新语句", "DataType": 1, "DataLength": 255}, {"ID": 30, "Name": "TDSALIAS", "Memo": "数据源别名（逻辑主键）", "OrderNo": 30, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 100}, {"ID": 31, "Name": "TDSCLASSNAME", "OrderNo": 31, "DisplayName": "执行类名", "DataType": 1, "DataLength": 50}, {"ID": 32, "Name": "TDSNAME", "OrderNo": 32, "DisplayName": "数据源名称", "DataType": 1, "DataLength": 100}, {"ID": 33, "Name": "TDSTYPE", "Memo": "SYS:系统数据源；其他：自定义数据源", "OrderNo": 33, "DisplayName": "SYS", "DataType": 1, "DataLength": 50}, {"ID": 34, "Name": "TMSORT", "OrderNo": 34, "DisplayName": "排序", "DataType": 2}, {"ID": 35, "Name": "UPDATEDATE", "OrderNo": 35, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 36, "Name": "UPDATEUID", "OrderNo": 36, "DisplayName": "更新人ID", "DataType": 1, "DataLength": 50}, {"ID": 37, "Name": "UPDATEUNAME", "OrderNo": 37, "DisplayName": "更新人姓名", "DataType": 1, "DataLength": 255}, {"ID": 38, "Name": "USED", "Memo": "是否使用1：使用；0：不使用", "OrderNo": 38, "DisplayName": "是否使用1", "DataType": 2}, {"ID": 39, "Name": "TDSAFTEREDITSCRIPT", "Memo": "数据源编辑后执行脚本", "OrderNo": 39, "DataType": 1, "DataLength": 2000}, {"ID": 40, "Name": "BINDTDSALIAS", "Memo": "数据源编辑功能初始化数据绑定的数据源别名", "OrderNo": 40, "DataType": 1, "DataLength": 100}, {"ID": 41, "Name": "TDSINITTYPE", "Memo": "数据源编辑功能初始化类型（1：数据源，2或null：自定义初始化语句）", "OrderNo": 41, "DataType": 2}]}}, {"Name": "TDS_INPARA", "Caption": "TM4-输入参数", "Memo": "for tm4", "OrderNo": 2, "GraphDesc": "Left=512.40\r\nTop=7.80\r\nBLeft=256.20\r\nBTop=3.90\r\n", "MetaFields": {"Count": 20, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 3, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "IMPORTCHOICE", "Memo": "导入选项，常规列1，关键列2，不导入3（TM4未使用）", "OrderNo": 6, "DisplayName": "导入选项", "DataType": 2}, {"ID": 7, "Name": "COMPONENTTYPE", "OrderNo": 7, "DataType": 1, "DataLength": 50}, {"ID": 8, "Name": "DATATYPE", "Memo": "数据类型（TM4目前未使用，默认值：tdsString）", "OrderNo": 8, "DisplayName": "数据类型", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "DEFAULTKEYSCRIPT", "OrderNo": 9, "DisplayName": "下拉框填充key值脚本", "DataType": 1, "DataLength": 1000}, {"ID": 10, "Name": "DEFAULTVALUESCRIPT", "OrderNo": 10, "DisplayName": "下拉框添加显示值脚本", "DataType": 1, "DataLength": 1000}, {"ID": 11, "Name": "DISPLAY", "Memo": "是否显示 1：显示；0：隐藏", "OrderNo": 11, "DisplayName": "是否显示", "DataType": 2}, {"ID": 12, "Name": "INITVALUESCRIPT", "Memo": "初始化默认值脚本（TM4预留，未使用）", "OrderNo": 12, "DisplayName": "初始化默认值脚本", "DataType": 1, "DataLength": 1000}, {"ID": 13, "Name": "INSERTEDIT", "Memo": "输入值是否代入录入项 for 数据源编辑", "OrderNo": 13, "DisplayName": "输入值是否代入录入项", "DataType": 2}, {"ID": 14, "Name": "ISCANQUERY", "Memo": "下拉框是否支持模糊检索（TM4未使用）", "OrderNo": 14, "DisplayName": "下拉框是否支持模糊检索", "DataType": 2}, {"ID": 15, "Name": "MEMO", "OrderNo": 15, "DisplayName": "备注说明", "DataType": 1, "DataLength": 255}, {"ID": 16, "Name": "PARAALIAS", "OrderNo": 16, "DisplayName": "输入参数别名", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "PARAID", "OrderNo": 17, "DisplayName": "序号", "DataType": 2}, {"ID": 18, "Name": "PARANAME", "OrderNo": 18, "DisplayName": "输入参数名称", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "TDSALIAS", "OrderNo": 19, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 100}, {"ID": 20, "Name": "WIDTH", "OrderNo": 20, "DisplayName": "组件宽度", "DataType": 2}]}}, {"Name": "TDS_INPARA_EVENT", "Caption": "输入参数联动事件", "Memo": "TM4未使用", "OrderNo": 3, "GraphDesc": "Left=1283.40\r\nTop=15.40\r\nBLeft=641.70\r\nBTop=7.70\r\n", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 3, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "EVENT", "OrderNo": 6, "DataType": 1, "DataLength": 20}, {"ID": 7, "Name": "EVENTBODY", "OrderNo": 7, "DataType": 1, "DataLength": 4000}, {"ID": 8, "Name": "OBJECTALIAS", "OrderNo": 8, "DataType": 1, "DataLength": 20}, {"ID": 9, "Name": "PARAALIAS", "OrderNo": 9, "DataType": 1, "DataLength": 20}, {"ID": 10, "Name": "TDSALIAS", "OrderNo": 10, "DataType": 1, "DataLength": 20}, {"ID": 11, "Name": "USED", "OrderNo": 11, "DataType": 2}]}}, {"Name": "TDS_MEMORY", "Caption": "参数记忆", "Memo": "TM4未使用", "OrderNo": 4, "GraphDesc": "Left=1288.70\r\nTop=239.60\r\nBLeft=644.35\r\nBTop=119.80\r\n", "MetaFields": {"Count": 13, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 3, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "COMTYPE", "OrderNo": 6, "DataType": 1, "DataLength": 20}, {"ID": 7, "Name": "DATA", "OrderNo": 7, "DataType": 1, "DataLength": 4000}, {"ID": 8, "Name": "DATANAME", "OrderNo": 8, "DataType": 1, "DataLength": 1000}, {"ID": 9, "Name": "MCODE", "OrderNo": 9, "DataType": 1, "DataLength": 200}, {"ID": 10, "Name": "MTYPE", "OrderNo": 10, "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "PARAALIAS", "OrderNo": 11, "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 12, "DataType": 1, "DataLength": 200}, {"ID": 13, "Name": "ZYID", "OrderNo": 13, "DataType": 2}]}}, {"Name": "TDS_OUTPARA", "Caption": "TM4-输出参数", "OrderNo": 5, "GraphDesc": "Left=512.90\r\nTop=358.80\r\nBLeft=256.45\r\nBTop=179.40\r\n", "MetaFields": {"Count": 32, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 3, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "ALIGN", "Memo": "对齐方式center,left,right", "OrderNo": 6, "DisplayName": "对齐方式center", "DataType": 1, "DataLength": 10}, {"ID": 7, "Name": "AUTOWIDTH", "OrderNo": 7, "DisplayName": "列宽是否自动扩展", "DataType": 2}, {"ID": 8, "Name": "CALLFUN", "OrderNo": 8, "DisplayName": "tm3使用", "DataType": 1, "DataLength": 1000}, {"ID": 9, "Name": "COMTYPE", "Memo": "组件类型 for 数据源编辑", "OrderNo": 9, "DisplayName": "组件类型", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "DATATYPE", "Memo": "数据类型，默认tdsString", "OrderNo": 10, "DisplayName": "数据类型", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "DEFAULTKEYSCRIPT", "Memo": "下拉框填充key值脚本，数据源修改功能使用", "OrderNo": 11, "DisplayName": "下拉框填充key值脚本", "DataType": 1, "DataLength": 1000}, {"ID": 12, "Name": "DEFAULTVALUESCRIPT", "Memo": "下拉框填充显示值脚本，数据源修改功能使用", "OrderNo": 12, "DisplayName": "下拉框填充显示值脚本", "DataType": 1, "DataLength": 1000}, {"ID": 13, "Name": "FIXED", "Memo": "列是否固定在左侧或者右侧，预留功能", "OrderNo": 13, "DisplayName": "列是否固定在左侧或者右侧", "DataType": 1, "DataLength": 10}, {"ID": 14, "Name": "ISGROUP", "Memo": "是否分组（TM4未使用）", "OrderNo": 14, "DisplayName": "是否分组", "DataType": 2}, {"ID": 15, "Name": "ISREQUIRED", "OrderNo": 15, "DisplayName": "是否为必填项", "DataType": 2}, {"ID": 16, "Name": "ISSPAN", "OrderNo": 16, "DisplayName": "是否合并显示", "DataType": 2}, {"ID": 17, "Name": "ISSUM", "Memo": "是否合计（TM4未使用）", "OrderNo": 17, "DisplayName": "是否合计", "DataType": 2}, {"ID": 18, "Name": "ISKEY", "Memo": "是否为关键列（TM4目前未使用）", "OrderNo": 18, "DisplayName": "是否为关键列", "DataType": 2}, {"ID": 19, "Name": "LX", "OrderNo": 19, "DisplayName": "tm3使用", "DataType": 1, "DataLength": 255}, {"ID": 20, "Name": "MAXLENGTH", "OrderNo": 20, "DisplayName": "输入限制最大长度", "DataType": 2}, {"ID": 21, "Name": "MEMO", "OrderNo": 21, "DisplayName": "备注说明", "DataType": 1, "DataLength": 255}, {"ID": 22, "Name": "OVERTIP", "Memo": "当内容过长被隐藏时显示 tooltip", "OrderNo": 22, "DisplayName": "当内容过长被隐藏时显示", "DataType": 2}, {"ID": 23, "Name": "PARAALIAS", "OrderNo": 23, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 24, "Name": "PARAID", "OrderNo": 24, "DisplayName": "显示顺序序号", "DataType": 2}, {"ID": 25, "Name": "PARANAME", "OrderNo": 25, "DisplayName": "参数名称", "DataType": 1, "DataLength": 50}, {"ID": 26, "Name": "RENDERERFUN", "OrderNo": 26, "DisplayName": "渲染函数", "DataType": 1, "DataLength": 4000}, {"ID": 27, "Name": "REPORTFORMULA", "OrderNo": 27, "DisplayName": "tm3使用", "DataType": 1, "DataLength": 255}, {"ID": 28, "Name": "SPANSCRIPT", "OrderNo": 28, "DisplayName": "合并脚本", "DataType": 1, "DataLength": 1000}, {"ID": 29, "Name": "SPANTYPE", "Memo": "0:相同合并；1：按照条件合并", "OrderNo": 29, "DataType": 2}, {"ID": 30, "Name": "TDSALIAS", "OrderNo": 30, "DisplayName": "输出参数别名", "DataType": 1, "DataLength": 100}, {"ID": 31, "Name": "VISIBLE", "Memo": "是否显示，1：显示，0：隐藏", "OrderNo": 31, "DisplayName": "是否显示", "DataType": 2}, {"ID": 32, "Name": "WIDTH", "OrderNo": 32, "DisplayName": "控件宽度", "DataType": 2}]}}, {"Name": "TDS_TABLE_INFO", "Caption": "TM4-数据源报表模式配置", "Memo": "for tm4", "OrderNo": 6, "GraphDesc": "Left=939.40\r\nTop=14.80\r\nBLeft=469.70\r\nBTop=7.40\r\n", "MetaFields": {"Count": 39, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 3, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "APPADDURL", "OrderNo": 6, "DataType": 1, "DataLength": 100}, {"ID": 7, "Name": "APPDELCONDITION", "OrderNo": 7, "DataType": 1, "DataLength": 100}, {"ID": 8, "Name": "APPDELRIGHT", "OrderNo": 8, "DataType": 1, "DataLength": 100}, {"ID": 9, "Name": "APPDELURL", "OrderNo": 9, "DataType": 1, "DataLength": 100}, {"ID": 10, "Name": "APPEDITCONDITION", "OrderNo": 10, "DataType": 1, "DataLength": 500}, {"ID": 11, "Name": "APPEDITRIGHT", "OrderNo": 11, "DataType": 1, "DataLength": 500}, {"ID": 12, "Name": "APPEDITURL", "OrderNo": 12, "DataType": 1, "DataLength": 500}, {"ID": 13, "Name": "APPISDEL", "OrderNo": 13, "DataType": 2}, {"ID": 14, "Name": "APPISEDIT", "OrderNo": 14, "DataType": 2}, {"ID": 15, "Name": "APPSHOWCOLNAME", "OrderNo": 15, "DataType": 2}, {"ID": 16, "Name": "APPSHOWSTYLE", "OrderNo": 16, "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "FILTERCONDITION", "OrderNo": 17, "DataType": 1, "DataLength": 500}, {"ID": 18, "Name": "FOOTERS", "OrderNo": 18, "DataType": 1, "DataLength": 100}, {"ID": 19, "Name": "GROUPPROJECT1", "OrderNo": 19, "DataType": 1, "DataLength": 500}, {"ID": 20, "Name": "GROUPPROJECT2", "OrderNo": 20, "DataType": 1, "DataLength": 500}, {"ID": 21, "Name": "GSSUMNAME", "OrderNo": 21, "DataType": 1, "DataLength": 50}, {"ID": 22, "Name": "ISGROUP", "OrderNo": 22, "DataType": 2}, {"ID": 23, "Name": "ISGSSUM", "Memo": "是否显示合计行 for TM4", "OrderNo": 23, "DisplayName": "是否显示合计行", "DataType": 2}, {"ID": 24, "Name": "ISSHOWDATANO", "OrderNo": 24, "DataType": 2}, {"ID": 25, "Name": "ISSHOWFOOTER", "OrderNo": 25, "DataType": 2}, {"ID": 26, "Name": "ISSHOWSPANTITLE", "OrderNo": 26, "DataType": 2}, {"ID": 27, "Name": "ISSHOWSUBTITLE", "OrderNo": 27, "DataType": 2}, {"ID": 28, "Name": "ISSHOWSUMMARY", "OrderNo": 28, "DataType": 2}, {"ID": 29, "Name": "ISSHOWSUMS", "OrderNo": 29, "DataType": 2}, {"ID": 30, "Name": "ISSHOWTITLE", "OrderNo": 30, "DataType": 2}, {"ID": 31, "Name": "LISTITEM", "OrderNo": 31, "DataType": 1, "DataLength": 1000}, {"ID": 32, "Name": "LOCKCOLNUM", "OrderNo": 32, "DataType": 2}, {"ID": 33, "Name": "SKINNAME", "OrderNo": 33, "DataType": 1, "DataLength": 50}, {"ID": 34, "Name": "SPANTITLE", "OrderNo": 34, "DataType": 1, "DataLength": 50}, {"ID": 35, "Name": "SUMMARYCOL", "OrderNo": 35, "DataType": 2}, {"ID": 36, "Name": "SUMMARYROW", "OrderNo": 36, "DataType": 2}, {"ID": 37, "Name": "TDSALIAS", "OrderNo": 37, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 100}, {"ID": 38, "Name": "TDSCELLCLICKFUN", "Memo": "单元格点击事件 for TM4", "OrderNo": 38, "DisplayName": "单元格点击事件", "DataType": 1, "DataLength": 2000}, {"ID": 39, "Name": "TITLE", "OrderNo": 39, "DisplayName": "标题", "DataType": 1, "DataLength": 100}]}}, {"Name": "TDS_TABLE_SPANINFO", "Caption": "数据源报表模式合并信息", "Memo": "tm4未应用", "OrderNo": 7, "GraphDesc": "Left=9.50\r\nTop=707.60\r\nBLeft=4.75\r\nBTop=353.80\r\n", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 3, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "GROUPCOL", "OrderNo": 6, "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "SPANCOL", "OrderNo": 7, "DataType": 1, "DataLength": 50}, {"ID": 8, "Name": "TDSALIAS", "OrderNo": 8, "DataType": 1, "DataLength": 100}]}}, {"Name": "TDS_TABLE_SUMMARY", "Caption": "数据源报表模式合计信息", "Memo": "TM4未应用", "OrderNo": 8, "GraphDesc": "Left=1289.00\r\nTop=488.40\r\nBLeft=644.50\r\nBTop=244.20\r\n", "MetaFields": {"Count": 12, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 3, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "GS", "OrderNo": 6, "DataType": 1, "DataLength": 500}, {"ID": 7, "Name": "LX", "OrderNo": 7, "DataType": 2}, {"ID": 8, "Name": "NAME", "OrderNo": 8, "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "TCOL", "OrderNo": 9, "DataType": 2}, {"ID": 10, "Name": "TDSALIAS", "OrderNo": 10, "DataType": 1, "DataLength": 100}, {"ID": 11, "Name": "TROW", "OrderNo": 11, "DataType": 2}, {"ID": 12, "Name": "USED", "OrderNo": 12, "DataType": 2}]}}, {"ID": 9, "Name": "TDS_INPARA_RELATION", "Caption": "输入参数联动关系表", "CreateDate": "2022/5/31 星期二 14:07:41", "OrderNo": 9, "GraphDesc": "Left=10.00\r\nTop=948.00\r\nBLeft=5.00\r\nBTop=474.00\r\n", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 25, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 100}, {"ID": 26, "Name": "ALIAS", "OrderNo": 3, "DisplayName": "输入参数别名", "DataType": 1, "DataLength": 50}, {"ID": 27, "Name": "PALIAS", "OrderNo": 4, "DisplayName": "父输入参数", "DataType": 1, "DataLength": 50}, {"ID": 28, "Name": "TMSORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}]}}, {"ID": 10, "Name": "SYS_EXTDB_CONN", "Caption": "外部数据库连接信息", "CreateDate": "2022/7/26 星期二 10:01:36", "OrderNo": 10, "GraphDesc": "Left=9.20\r\nTop=1080.40\r\nBLeft=4.60\r\nBTop=540.20\r\n", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 15, "Name": "URL", "OrderNo": 2, "DisplayName": "连接字符串", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "USERNAME", "OrderNo": 3, "DisplayName": "用户名", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "PASSWORD", "OrderNo": 4, "DisplayName": "密码", "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "TMUSED", "OrderNo": 5, "DisplayName": "是否使用", "DataType": 2}, {"ID": 7, "Name": "TMSORT", "OrderNo": 6, "DisplayName": "排序", "DataType": 2}, {"ID": 19, "Name": "MEMO", "OrderNo": 7, "DisplayName": "备注", "DataType": 1, "DataLength": 1000}]}}, {"ID": 9, "Name": "TDS_FLOW", "Caption": "数据源-工作流程设置", "CreateDate": "2022/6/24 星期五 11:02:40", "OrderNo": 11, "GraphDesc": "Left=10.00\r\nTop=1428.00\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "Id", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "ISSTARTFLOW", "OrderNo": 3, "DisplayName": "是否启动工作流（1：启动，0：未启动）", "DataType": 2}, {"ID": 14, "Name": "BUSITEMPLATEID", "OrderNo": 4, "DisplayName": "工作流程编码", "DataType": 1, "DataLength": 255}, {"ID": 15, "Name": "ISSHOWBATCHSTARTBTN", "OrderNo": 5, "DisplayName": "是否显示批量发起按钮（1：显示，0：不显示）", "DataType": 2}, {"ID": 16, "Name": "ISSHOWOUTCOLUMN", "OrderNo": 6, "DisplayName": "是否显示输出列（1：显示，0：不显示）", "DataType": 2}, {"ID": 17, "Name": "ALLOWCOMMITSCRIPT", "OrderNo": 7, "DisplayName": "允许发起流程提交脚本", "DataType": 1, "DataLength": 4000}, {"ID": 9, "Name": "BUSINESSKEY", "OrderNo": 8, "DisplayName": "业务主键", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "STARTALIAS", "OrderNo": 9, "DisplayName": "自定义按钮别名", "DataType": 1, "DataLength": 30}, {"ID": 8, "Name": "TOPATH", "OrderNo": 10, "DisplayName": "流程启动后，自动跳转页面地址", "DataType": 1, "DataLength": 500}, {"ID": 11, "Name": "ROOTBUSINESSKEY", "OrderNo": 11, "DisplayName": "根业务主键", "DataType": 1, "DataLength": 50}]}}, {"ID": 14, "Name": "TDS_EDITTDSBINDPARAM", "Caption": "数据源编辑功能输入输出参数绑定", "CreateDate": "2022/10/13 星期四 14:54:45", "OrderNo": 12, "GraphDesc": "Left=10.00\r\nTop=1726.00\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 14, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 3, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 13, "Name": "BINDTDSALIAS", "OrderNo": 6, "DisplayName": "绑定数据源别名", "DataType": 1, "DataLength": 255}, {"ID": 14, "Name": "TARGETTDSALIAS", "OrderNo": 7, "DisplayName": "源数据源别名", "DataType": 1, "DataLength": 255}, {"ID": 18, "Name": "BINDTDSPARAMTYPE", "Memo": "1、输入参数，2：输出参数", "OrderNo": 8, "DisplayName": "绑定数据参数类型", "DataType": 2}, {"ID": 19, "Name": "BINDTDSPARAMALIAS", "OrderNo": 9, "DisplayName": "绑定数据源参数别名", "DataType": 1, "DataLength": 255}, {"ID": 20, "Name": "BINDTDSPARAMNAME", "OrderNo": 10, "DisplayName": "绑定数据源参数名称", "DataType": 1, "DataLength": 255}, {"ID": 21, "Name": "TARGETTDSPARAMALIAS", "OrderNo": 11, "DisplayName": "原数据源参数别名", "DataType": 1, "DataLength": 255}, {"ID": 22, "Name": "TARGETTDSPARAMNAME", "OrderNo": 12, "DisplayName": "原数据源参数名称", "DataType": 1, "DataLength": 255}, {"ID": 23, "Name": "TMSORT", "OrderNo": 13, "DisplayName": "排序字段", "DataType": 2}, {"ID": 14, "Name": "ISSAVEOLD", "OrderNo": 14, "DisplayName": "是否保留原始数据", "DataType": 2}]}}, {"ID": 15, "Name": "TDS_EDITCUSTOMBTN", "Caption": "数据源编辑功能自定义按钮", "CreateDate": "2022/10/13 星期四 15:03:46", "OrderNo": 13, "GraphDesc": "Left=395.00\r\nTop=1726.00\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 3, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 18, "Name": "TDSALIAS", "OrderNo": 6, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "BTNALIAS", "OrderNo": 7, "DisplayName": "按钮别名", "DataType": 1, "DataLength": 50}, {"ID": 20, "Name": "BTNNAME", "OrderNo": 8, "DisplayName": "按钮名称", "DataType": 1, "DataLength": 255}, {"ID": 21, "Name": "BTNCSS", "OrderNo": 9, "DisplayName": "按钮样式", "DataType": 1, "DataLength": 255}, {"ID": 22, "Name": "BTNSCRIPT", "OrderNo": 10, "DisplayName": "执行脚本", "DataType": 1, "DataLength": 2000}, {"ID": 23, "Name": "BTNFUN", "OrderNo": 11, "DisplayName": "执行函数", "DataType": 1, "DataLength": 255}, {"ID": 17, "Name": "BTNTYPE", "Memo": "1、系统按钮，2：自定义按钮", "OrderNo": 12, "DisplayName": "按钮类型", "DataType": 2}, {"ID": 25, "Name": "ISSHOW", "Memo": "1、显示，0：隐藏", "OrderNo": 13, "DisplayName": "是否显示", "DataType": 2}, {"ID": 26, "Name": "SHOWSCRIPT", "OrderNo": 14, "DisplayName": "显示脚本", "DataType": 1, "DataLength": 2000}, {"ID": 27, "Name": "TMSORT", "OrderNo": 15, "DisplayName": "排序字段", "DataType": 2}, {"ID": 28, "Name": "TMUSED", "OrderNo": 16, "DisplayName": "使用标识", "DataType": 2}]}}]}}, {"ID": 4, "Name": "数据权限", "CreateDate": "2022/6/6 星期一 09:23:13", "OrderNo": 6, "Tables": {"Count": 3, "items": [{"ID": 1, "Name": "SYS_DATAPERM_CONFIG", "Caption": "数据权限配置表", "Memo": "哪些功能可以设置数据权限", "CreateDate": "2022/6/6 星期一 09:23:34", "OrderNo": 1, "GraphDesc": "Left=10.00\r\nTop=10.00\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "MODULECODE", "Memo": "sys_module_lib.code", "OrderNo": 2, "DisplayName": "模块编码", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "CODE", "OrderNo": 3, "DisplayName": "数据权限对象编码", "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "NAME", "OrderNo": 4, "DisplayName": "数据权限对象名称", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "TMSORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}, {"ID": 16, "Name": "DATATYPE", "Memo": "0：默认（组织机构）；1自定义", "OrderNo": 6, "DisplayName": "数据权限类型", "DataType": 2}, {"ID": 17, "Name": "TREECLASSNAME", "Memo": "数据权限范围（备选树）", "OrderNo": 7, "DisplayName": "数据备选数据加载类名", "DataType": 1, "DataLength": 255}, {"ID": 19, "Name": "TREEGETDATAFUN", "OrderNo": 8, "DisplayName": "数据备选数据获取函数", "DataType": 1, "DataLength": 255}]}}, {"ID": 2, "Name": "SYS_DATAPERM", "Caption": "数据权限表", "CreateDate": "2022/6/6 星期一 10:50:22", "OrderNo": 2, "GraphDesc": "Left=10.00\r\nTop=228.00\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1}, {"ID": 2, "Name": "DATAPERM_CODE", "Memo": "SYS_DATAPERM_CONFIG.CODE", "OrderNo": 2, "DisplayName": "数据权限编码", "DataType": 1, "DataLength": 100}, {"ID": 25, "Name": "OBJTYPE", "Memo": "0:角色；1：人员；2：其他", "OrderNo": 3, "DisplayName": "拥有数据权限的对象类型", "DataType": 2}, {"ID": 23, "Name": "OBJID", "Memo": "人员id/角色id", "OrderNo": 4, "DisplayName": "拥有数据权限的对象id", "DataType": 1, "DataLength": 100}, {"ID": 24, "Name": "DATACODE", "OrderNo": 5, "DisplayName": "数据权限值", "DataType": 1, "DataLength": 255}]}}, {"ID": 3, "Name": "SYS_DATAPERM_DATATYPE", "Caption": "数据权限类型", "CreateDate": "2022/6/8 星期三 15:33:45", "OrderNo": 3, "GraphDesc": "Left=10.00\r\nTop=430.00\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 23, "Name": "DATAPERM_CODE", "Memo": "SYS_DATAPERM_CONFIG.CODE", "OrderNo": 2, "DisplayName": "数据权限编码", "DataType": 1, "DataLength": 100}, {"ID": 25, "Name": "OBJTYPE", "Memo": "0:角色；1：人员；2：其他", "OrderNo": 3, "DisplayName": "拥有数据权限的对象类型", "DataType": 2}, {"ID": 24, "Name": "OBJID", "Memo": "人员id/角色id", "OrderNo": 4, "DisplayName": "拥有数据权限的对象id", "DataType": 1, "DataLength": 100}, {"ID": 26, "Name": "DATATYPE", "Memo": "1：全部；2:仅本人；30：本部门；40：本部门及以下；-1：自定义", "OrderNo": 5, "DisplayName": "数据范围", "DataType": 2}]}}]}}, {"ID": 1, "Name": "自定义表单", "CreateDate": "2021/3/20 星期六 15:09:17", "OrderNo": 7, "ConfigStr": "DrawerWidth=1500\r\nDrawerHeight=2560\r\nWorkAreaColor=16777215\r\nSelectedColor=-2147483635\r\nDefaultObjectColor=15921906\r\nDefaultTitleColor=255\r\nDefaultPKColor=16711935\r\nDefaultFKColor=16711680\r\nDefaultBorderColor=12632256\r\nDefaultLineColor=16711680\r\nShowFieldType=1\r\nShowFieldIcon=1\r\nShowPhyFieldName=2\r\nDatabaseEngine=\r\nGenFKIndexesSQL=0\r\n", "Tables": {"Count": 5, "items": [{"ID": 8, "Name": "SYS_TEMPLATE", "Caption": "模板", "Memo": "模板", "CreateDate": "2021/3/23 星期二 08:47:01", "OrderNo": 1, "GraphDesc": "Left=461.43\r\nTop=68.62\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "Memo": "主键id", "OrderNo": 1, "DisplayName": "主键id", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 17, "Name": "PID", "OrderNo": 2, "DisplayName": "父id", "DataType": 1, "DataLength": 50}, {"ID": 229, "Name": "TPL_NAME", "Memo": "模板名称", "OrderNo": 3, "DisplayName": "模板名称", "DataType": 1, "DataLength": 500}, {"ID": 8, "Name": "CREATE_BY", "OrderNo": 4, "DisplayName": "创建人", "DataType": 1}, {"ID": 9, "Name": "CREATE_TIME", "Memo": "默认为当前时间", "OrderNo": 5, "DisplayName": "创建时间", "DataType": 4, "DefaultValue": "sysdate"}, {"ID": 254, "Name": "UPDATE_BY", "OrderNo": 6, "DisplayName": "更新人", "DataType": 1}, {"ID": 255, "Name": "UPDATE_TIME", "OrderNo": 7, "DisplayName": "更新时间", "DataType": 1}, {"ID": 59, "Name": "SORT", "OrderNo": 8, "DisplayName": "排序", "DataType": 2}, {"ID": 60, "Name": "USED", "OrderNo": 9, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 7, "Name": "SYS_TEMPLATE_RELATION", "Caption": "模板表单关系表", "CreateDate": "2021/6/10 星期四 13:48:31", "OrderNo": 2, "GraphDesc": "Left=879.80\r\nTop=77.80\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "主键", "DataType": 1, "KeyFieldType": 1}, {"ID": 2, "Name": "TPL_ID", "Memo": "所属模板id", "OrderNo": 2, "DisplayName": "所属模板id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "SYS_TEMPLATE", "RelateField": "ID", "GraphDesc": "P1=665.43,129.00\r\nP2=773.00,129.00\r\nP3=773.00,129.00\r\nP4=879.80,129.00\r\nHookP1=183.57,60.38\r\nHookP2=20.20,51.20\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "RELATED_ID", "Memo": "对应表单的id或者文件的id", "OrderNo": 3, "DisplayName": "关联id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "SYS_FORM", "RelateField": "ID", "GraphDesc": "P1=663.34,316.00\r\nP2=772.00,316.00\r\nP3=772.00,152.00\r\nP4=879.80,152.00\r\nHookP1=167.66,36.06\r\nHookP2=28.20,74.20\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "RELATED_TYPE", "Memo": "类型：1-表单 2-文件", "OrderNo": 4, "DisplayName": "关联类型", "DataType": 2}, {"ID": 11, "Name": "SORT", "Memo": "排序", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}]}}, {"ID": 1, "Name": "SYS_FORM", "Caption": "表单", "Memo": "表单", "CreateDate": "2021/3/20 星期六 15:09:40", "OrderNo": 3, "GraphDesc": "Left=459.34\r\nTop=279.94\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "ID", "Memo": "主键", "OrderNo": 1, "DisplayName": "主键id", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=264.16,174.00\nP2=322.00,174.00\nP3=322.00,174.00\nP4=380.40,174.00\nHookP1=211.84,63.05\nHookP2=19.60,55.20\nMod_OP1=0\nMod_OP2=0\nMod_CP=0\nHorz1=1\nHorz2=1"}, {"ID": 4, "Name": "FORM_NAME", "Memo": "表单名称", "OrderNo": 2, "DisplayName": "表单名称", "DataType": 1, "DataLength": 500}, {"ID": 11, "Name": "FORM_ALIAS", "OrderNo": 3, "DisplayName": "表单别名", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "FORM_DESC", "Memo": "表单描述", "OrderNo": 4, "DisplayName": "表单描述", "DataType": 1, "DataLength": 2000}, {"ID": 12, "Name": "TABLE_NAMES", "Memo": "数据库表名", "OrderNo": 5, "DisplayName": "数据库表名", "DataType": 1, "DataLength": 2000}, {"ID": 11, "Name": "SORT", "Memo": "排序", "OrderNo": 6, "DisplayName": "排序", "DataType": 2}, {"ID": 8, "Name": "CREATE_BY", "OrderNo": 7, "DisplayName": "创建人", "DataType": 1, "DataLength": 500}, {"ID": 9, "Name": "CREATE_TIME", "Memo": "默认为当前时间", "OrderNo": 8, "DisplayName": "创建日期", "DataType": 4, "DefaultValue": "sysdate"}, {"ID": 27, "Name": "UPDATE_BY", "OrderNo": 9, "DisplayName": "更新人", "DataType": 1, "DataLength": 500}, {"ID": 28, "Name": "UPDATE_TIME", "Memo": "默认为当前时间", "OrderNo": 10, "DisplayName": "更新时间", "DataType": 4, "DefaultValue": "sysdate"}, {"ID": 11, "Name": "USED", "Memo": "是否使用", "OrderNo": 11, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 2, "Name": "SYS_FORM_CONTENT", "Caption": "表单内容", "Memo": "表单内容", "CreateDate": "2021/3/20 星期六 15:16:27", "OrderNo": 4, "GraphDesc": "Left=884.06\r\nTop=278.47\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "Memo": "主键id", "OrderNo": 1, "DisplayName": "主键id", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "FORM_ID", "Memo": "表单id", "OrderNo": 2, "DisplayName": "表单id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "SYS_FORM", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=663.34,415.00\r\nP2=774.00,415.00\r\nP3=774.00,340.60\r\nP4=884.06,340.60\r\nHookP1=145.00,135.06\r\nHookP2=33.94,62.13\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "VERSION", "Memo": "版本", "OrderNo": 3, "DisplayName": "版本", "DataType": 1, "DataLength": 100}, {"ID": 4, "Name": "FORM_CONTENT", "Memo": "表单内容", "OrderNo": 4, "DisplayName": "表单内容", "DataType": 1, "DataLength": 4000}, {"ID": 8, "Name": "CREATE_BY", "Memo": "创建人", "OrderNo": 5, "DisplayName": "创建人", "DataType": 1}, {"ID": 9, "Name": "CREATE_TIME", "Memo": "默认为当前时间", "OrderNo": 6, "DisplayName": "创建时间", "DataType": 4, "DefaultValue": "sysdate"}, {"ID": 25, "Name": "UPDATE_BY", "Memo": "更新人", "OrderNo": 7, "DisplayName": "更新人", "DataType": 1}, {"ID": 26, "Name": "UPDATE_TIME", "Memo": "默认为当前时间", "OrderNo": 8, "DisplayName": "更新时间", "DataType": 4, "DefaultValue": "sysdate"}]}}, {"ID": 3, "Name": "SYS_FORM_DATA_INDEX", "Caption": "表单数据索引", "Memo": "表单数据索引", "CreateDate": "2021/3/20 星期六 16:04:08", "OrderNo": 5, "GraphDesc": "Left=660.39\r\nTop=531.05\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "Memo": "主键id", "OrderNo": 1, "DisplayName": "主键id", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 83, "Name": "FORM_ID", "Memo": "表单id", "OrderNo": 2, "DisplayName": "表单id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "SYS_FORM", "RelateField": "ID", "GraphDesc": "P1=555.00,477.94\r\nP2=555.00,504.00\r\nP3=763.00,504.00\r\nP4=763.00,531.05\r\nHookP1=95.66,146.06\r\nHookP2=102.61,10.95\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 7, "Name": "DATA_ID", "Memo": "数据id", "OrderNo": 3, "DisplayName": "数据id", "DataType": 1}, {"ID": 3, "Name": "CONTENT_ID", "Memo": "表单内容id", "OrderNo": 4, "DisplayName": "表单内容id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "SYS_FORM_CONTENT", "RelateField": "ID", "GraphDesc": "P1=1011.06,428.47\r\nP2=1011.06,495.40\r\nP3=781.59,495.40\r\nP4=781.59,531.05\r\nHookP1=127.00,75.00\r\nHookP2=121.20,20.19\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=0"}, {"ID": 8, "Name": "CREATE_BY", "OrderNo": 5, "DisplayName": "创建人", "DataType": 1}, {"ID": 9, "Name": "CREATE_DATE", "Memo": "默认为当前时间", "OrderNo": 6, "DisplayName": "创建日期", "DataType": 4, "DefaultValue": "sysdate"}, {"ID": 51, "Name": "UPDATE_BY", "OrderNo": 7, "DisplayName": "更新人", "DataType": 1}, {"ID": 52, "Name": "UPDATE_TIME", "Memo": "默认为当前时间", "OrderNo": 8, "DisplayName": "更新时间", "DataType": 1, "DefaultValue": "sysdate"}]}}]}}, {"ID": 6, "Name": "消息", "CreateDate": "2021/4/7 星期三 08:24:12", "OrderNo": 8, "DefDbEngine": "SQLSERVER", "DbConnectStr": "TCtMetaSqlsvrDb", "ConfigStr": "DrawerWidth=1500\nDrawerHeight=662\nWorkAreaColor=16777215\nSelectedColor=-2147483635\nDefaultObjectColor=15921906\nDefaultTitleColor=255\nDefaultPKColor=16711935\nDefaultFKColor=16711680\nDefaultBorderColor=12632256\nDefaultLineColor=16711680\nShowFieldType=1\nShowFieldIcon=1\nShowPhyFieldName=2\nDatabaseEngine=\nGenFKIndexesSQL=0\n", "Tables": {"Count": 2, "items": [{"Name": "MSG_INFO_ITEM", "Caption": "消息接收信息表", "OrderNo": 1, "GraphDesc": "Left=429.50\r\nTop=14.80\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 17, "items": [{"ID": 1, "Name": "TMUID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "DataLength": 18}, {"ID": 2, "Name": "PTMUID", "OrderNo": 2, "DisplayName": "父表id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "MSG_INFO", "RelateField": "tmUid", "Not_Nullable": true, "DataLength": 50, "GraphDesc": "P1=326.00,162.00\r\nP2=378.00,162.00\r\nP3=378.00,162.00\r\nP4=429.50,162.00\r\nHookP1=289.00,155.20\r\nHookP2=20.50,147.20\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "ORGDM", "OrderNo": 3, "DisplayName": "接收人所属机构码", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "INCEPT_ZYID", "OrderNo": 4, "DisplayName": "接收人id", "DataType": 2, "Not_Nullable": true}, {"ID": 5, "Name": "INCEPT_ZYXM", "OrderNo": 5, "DisplayName": "接收人姓名", "DataType": 1, "DataLength": 50}, {"ID": 97, "Name": "APPCID", "OrderNo": 6, "DisplayName": "移动端app的client ID", "DataType": 1, "DataLength": 100}, {"ID": 79, "Name": "INCEPT_PC_MARK", "Memo": "0未接收 1接收", "OrderNo": 7, "DisplayName": "pc接收标识", "DataType": 2}, {"ID": 6, "Name": "INCEPT_APP_MARK", "Memo": "0未接收 1接收", "OrderNo": 8, "DisplayName": "app接收标识", "DataType": 2}, {"ID": 7, "Name": "INCEPT_PC_TIME", "Memo": "pc接收时间", "OrderNo": 9, "DisplayName": "pc接收时间", "DataType": 4}, {"ID": 80, "Name": "INCEPT_APP_TIME", "Memo": "app接收时间", "OrderNo": 10, "DisplayName": "app接收时间", "DataType": 4}, {"ID": 8, "Name": "USED", "Memo": "1使用 0不使用", "OrderNo": 11, "Params": "SQLSERVER_DF_CONSTRAINT_NAME=DF__b_msglist__used__6E2152BE", "DisplayName": "是否使用", "DataType": 2, "DefaultValue": "1", "Not_Nullable": true}, {"ID": 9, "Name": "RELATIONID", "Memo": "数据关联ID（用于与微信消息连动）", "OrderNo": 12, "DisplayName": "数据关联ID", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "MODULE_CODE", "Memo": "消息所属模块", "OrderNo": 13, "DisplayName": "消息所属模块", "DataType": 1, "DataLength": 50}, {"ID": 76, "Name": "CREATE_BY", "OrderNo": 14, "DisplayName": "创建人", "DataType": 2}, {"ID": 77, "Name": "CREATE_TIME", "OrderNo": 15, "DisplayName": "创建时间", "DataType": 4}, {"ID": 78, "Name": "UPDATE_BY", "OrderNo": 16, "DisplayName": "更新人", "DataType": 2}, {"ID": 75, "Name": "UPDATE_TIME", "OrderNo": 17, "DisplayName": "更新时间", "DataType": 4}]}}, {"Name": "MSG_INFO", "Caption": "消息基础信息表", "OrderNo": 2, "GraphDesc": "Left=17.00\r\nTop=6.80\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 26, "items": [{"ID": 1, "Name": "TMUID", "OrderNo": 1, "DisplayName": "逻辑标识", "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "MSGTITILE", "OrderNo": 2, "DisplayName": "标题", "DataType": 1, "DataLength": 4000}, {"ID": 3, "Name": "MSGCONTENT", "Memo": "mysql的utf8编码可以存21844", "OrderNo": 3, "DisplayName": "内容", "DataType": 1, "DataLength": 20000}, {"ID": 4, "Name": "SENDZYID", "OrderNo": 4, "DisplayName": "发送人id", "DataType": 2}, {"ID": 5, "Name": "SENDZYXM", "OrderNo": 5, "DisplayName": "发送人姓名", "DataType": 1, "DataLength": 50}, {"ID": 6, "Name": "SENDDT", "OrderNo": 6, "Params": "SQLSERVER_DF_CONSTRAINT_NAME=DF__b_msg__sendDt__6E6F6434", "DisplayName": "发送时间", "DataType": 4, "IndexType": 2, "DefaultValue": "getdate()"}, {"ID": 7, "Name": "REVERTTMUID", "Memo": "用于记录回复的信息", "OrderNo": 7, "DisplayName": "反馈信息父id", "DataType": 1, "DataLength": 50}, {"ID": 63, "Name": "MSG_PC", "Memo": "1是 0否", "OrderNo": 8, "DisplayName": "pc消息", "DataType": 2}, {"ID": 64, "Name": "MSG_APP", "Memo": "1是 0否", "OrderNo": 9, "DisplayName": "app消息", "DataType": 2}, {"ID": 62, "Name": "SEND_PC_MARK", "Memo": "1已发送 0未发送", "OrderNo": 10, "DisplayName": "发送pc标识", "DataType": 2}, {"ID": 61, "Name": "SEND_APP_MARK", "Memo": "1已发送 0未发送", "OrderNo": 11, "DisplayName": "发送app标识", "DataType": 2}, {"ID": 8, "Name": "MSG_TYPE", "OrderNo": 12, "DisplayName": "原消息类型", "DataType": 2}, {"ID": 9, "Name": "MSG_ATTRIBUTE", "OrderNo": 13, "DisplayName": "原消息属性", "DataType": 2}, {"ID": 10, "Name": "USED", "Memo": "1是 0否", "OrderNo": 14, "Params": "SQLSERVER_DF_CONSTRAINT_NAME=DF__b_msg__used__6F63886D", "DisplayName": "是否使用", "DataType": 2, "DefaultValue": "1", "Not_Nullable": true}, {"ID": 11, "Name": "MSGSOURCE", "OrderNo": 15, "Params": "SQLSERVER_DF_CONSTRAINT_NAME=DF__b_msg__msgsource__7057ACA6", "DisplayName": "消息来源", "DataType": 2, "DefaultValue": "0", "Not_Nullable": true}, {"ID": 13, "Name": "MODULEBM", "OrderNo": 16, "DisplayName": "模块编码", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "RELATIONID", "Memo": "数据关联ID（用于与微信消息连动）", "OrderNo": 17, "DisplayName": "数据关联ID", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "MODULECODE", "Memo": "消息所属模块", "OrderNo": 18, "DisplayName": "消息所属模块功能编码", "DataType": 1, "DataLength": 50}, {"ID": 58, "Name": "LOGO", "OrderNo": 19, "DisplayName": "通知栏图标", "DataType": 1, "DataLength": 200}, {"ID": 54, "Name": "LOGOURL", "OrderNo": 20, "DisplayName": "通知栏网络图标", "DataType": 1, "DataLength": 200}, {"ID": 60, "Name": "TRANSMISSION_TYPE", "Memo": "1为强制启动应用，客户端接收到消息后就会立即启动应用；2为等待应用启动", "OrderNo": 21, "DisplayName": "启动方式", "DataType": 2}, {"ID": 59, "Name": "TRANSMISSION_CONTENT", "OrderNo": 22, "DisplayName": "透传内容", "DataType": 1, "DataLength": 4000}, {"ID": 53, "Name": "CREATE_BY", "OrderNo": 23, "DisplayName": "创建人", "DataType": 2}, {"ID": 55, "Name": "CREATE_TIME", "OrderNo": 24, "DisplayName": "创建时间", "DataType": 4}, {"ID": 56, "Name": "UPDATE_BY", "OrderNo": 25, "DisplayName": "更新人", "DataType": 2}, {"ID": 57, "Name": "UPDATE_TIME", "OrderNo": 26, "DisplayName": "更新时间", "DataType": 4}]}}]}}, {"ID": 2, "Name": "待办", "CreateDate": "2021/3/30 星期二 13:48:52", "OrderNo": 9, "DefDbEngine": "SQLSERVER", "DbConnectStr": "TCtMetaSqlsvrDb", "ConfigStr": "DrawerWidth=5338\nDrawerHeight=2072\nWorkAreaColor=16777215\nSelectedColor=-2147483635\nDefaultObjectColor=15921906\nDefaultTitleColor=255\nDefaultPKColor=16711935\nDefaultFKColor=16711680\nDefaultBorderColor=12632256\nDefaultLineColor=16711680\nShowFieldType=1\nShowFieldIcon=1\nShowPhyFieldName=2\nDatabaseEngine=\nGenFKIndexesSQL=0\n", "Tables": {"Count": 9, "items": [{"Name": "弃用，使用缓存a_todo_cached", "OrderNo": 1, "GraphDesc": "Left=30.80\r\nTop=898.80\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "tmuid", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "todocode", "OrderNo": 2, "DataType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 3, "Name": "<PERSON>yid", "OrderNo": 3, "DataType": 2, "Not_Nullable": true, "DataLength": 18}, {"ID": 4, "Name": "todocount", "OrderNo": 4, "DataType": 2}, {"ID": 5, "Name": "lastdt", "OrderNo": 5, "DataType": 4}]}}, {"Name": "A_TODOMODULERIGHT", "Caption": "待办模块权限", "Memo": "待办模块权限", "OrderNo": 2, "GraphDesc": "Left=503.00\r\nTop=16.40\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 4, "items": [{"ID": 1, "Name": "TMUID", "Memo": "记录唯一标识", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "MODULECODE", "Memo": "待办模块编码", "OrderNo": 2, "DisplayName": "待办模块编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "a_todoModule", "RelateField": "moduleCode", "DataLength": 50, "GraphDesc": "P1=224.00,59.00\r\nP2=364.00,59.00\r\nP3=364.00,59.00\r\nP4=503.00,59.00\r\nHookP1=169.00,44.20\r\nHookP2=20.00,42.60\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "RIGHTID", "Memo": "权限id", "OrderNo": 3, "DisplayName": "权限id", "DataType": 2}, {"ID": 4, "Name": "RIGHTNAME", "Memo": "权限名称", "OrderNo": 4, "DisplayName": "权限名称", "DataType": 1, "DataLength": 200}]}}, {"Name": "弃用，使用缓存a_todoCount", "OrderNo": 3, "GraphDesc": "Left=28.00\r\nTop=1042.80\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 17, "items": [{"ID": 1, "Name": "tmuid", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "userId", "OrderNo": 2, "DataType": 2, "DataLength": 19}, {"ID": 3, "Name": "userName", "OrderNo": 3, "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "total", "OrderNo": 4, "DataType": 2}, {"ID": 5, "Name": "taskAudit", "OrderNo": 5, "DataType": 2}, {"ID": 6, "Name": "taskApproval", "OrderNo": 6, "DataType": 2}, {"ID": 7, "Name": "taskAccept", "OrderNo": 7, "DataType": 2}, {"ID": 8, "Name": "taskFeedback", "OrderNo": 8, "DataType": 2}, {"ID": 9, "Name": "taskConfirm", "OrderNo": 9, "DataType": 2}, {"ID": 10, "Name": "taskEvaluate", "OrderNo": 10, "DataType": 2}, {"ID": 11, "Name": "taskExtApproval", "OrderNo": 11, "DataType": 2}, {"ID": 12, "Name": "taskExtConfirm", "OrderNo": 12, "DataType": 2}, {"ID": 13, "Name": "taskExtTransmit", "OrderNo": 13, "DataType": 2}, {"ID": 14, "Name": "taskAppealTransmit", "OrderNo": 14, "DataType": 2}, {"ID": 15, "Name": "taskExtAdvice", "OrderNo": 15, "DataType": 2}, {"ID": 16, "Name": "taskAppealAdvice", "OrderNo": 16, "DataType": 2}, {"ID": 17, "Name": "taskConfirmAdvice", "OrderNo": 17, "DataType": 2}]}}, {"Name": "未知弃用A_TODO_INFO_OPENWINCFG", "OrderNo": 4, "GraphDesc": "Left=361.40\r\nTop=912.40\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "tmuid", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "circle", "OrderNo": 2, "DataType": 2}, {"ID": 3, "Name": "circleEndDay", "OrderNo": 3, "DataType": 1, "DataLength": 20}, {"ID": 4, "Name": "circleStartDay", "OrderNo": 4, "DataType": 1, "DataLength": 20}, {"ID": 5, "Name": "todoCode", "OrderNo": 5, "DataType": 1, "DataLength": 20}, {"ID": 6, "Name": "todoMessage", "OrderNo": 6, "DataType": 1, "DataLength": 4000}]}}, {"Name": "A_TODOINFO", "Caption": "待办项目表", "Memo": "待办-待办信息表", "OrderNo": 5, "GraphDesc": "Left=29.40\r\nTop=263.60\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 19, "items": [{"ID": 1, "Name": "TMUID", "Memo": "唯一标识", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "TODOTYPE", "Memo": "待办类型(1:待办；0：已办)", "OrderNo": 2, "DisplayName": "待办类型", "DataType": 2}, {"ID": 3, "Name": "MODULECODE", "Memo": "模块编码", "OrderNo": 3, "DisplayName": "模块编码", "DataType": 1, "KeyFieldType": 3, "RelateTable": "a_todoModule", "RelateField": "moduleCode", "DataLength": 50, "GraphDesc": "P1=127.00,228.80\r\nP2=127.00,246.00\r\nP3=127.00,246.00\r\nP4=127.00,263.60\r\nHookP1=100.00,186.20\r\nHookP2=97.60,20.40\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 4, "Name": "TODONAME", "Memo": "待办事项", "OrderNo": 4, "DisplayName": "待办事项", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "EXECCONTENT", "Memo": "执行内容(tm3系统中的类或者SQL语句)", "OrderNo": 5, "DisplayName": "执行内容", "DataType": 1, "DataLength": 8000}, {"ID": 6, "Name": "EXECTYPE", "Memo": "执行内容类型(0：SQL语句 ；1：java类)", "OrderNo": 6, "DisplayName": "执行内容类型", "DataType": 2}, {"ID": 7, "Name": "TODOURL", "Memo": "待办跳转页面地址", "OrderNo": 7, "DisplayName": "待办跳转页面地址", "DataType": 1, "DataLength": 500}, {"ID": 8, "Name": "ISFC", "Memo": "是否是分厂库", "OrderNo": 8, "DisplayName": "弃用", "DataType": 2}, {"ID": 9, "Name": "SORT", "Memo": "排序", "OrderNo": 9, "DisplayName": "排序", "DataType": 2}, {"ID": 10, "Name": "USED", "Memo": "是否启用", "OrderNo": 10, "DisplayName": "是否启用", "DataType": 2}, {"ID": 11, "Name": "APPEXECCONTENT", "Memo": "手机端通信接口后台执行代码", "OrderNo": 11, "DisplayName": "手机端通信接口后台执行代码", "DataType": 1, "DataLength": 8000}, {"ID": 12, "Name": "APPURL", "Memo": "手机端前台跳转页面", "OrderNo": 12, "DisplayName": "手机端前台跳转页面", "DataType": 1, "DataLength": 8000}, {"ID": 13, "Name": "TODOCODE", "Memo": "待办项的别名或者已办项的别名", "OrderNo": 13, "DisplayName": "待办项的别名或者已办项的别名", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "USETYPE", "OrderNo": 14, "DisplayName": "？", "DataType": 1, "DataLength": 4000}, {"ID": 15, "Name": "ISMEMCACH", "Memo": "启用缓存", "OrderNo": 15, "DisplayName": "弃用，默认弃用缓存", "DataType": 2}, {"ID": 16, "Name": "ISBATCH", "Memo": "是否批量  是：1  否：0或null ", "OrderNo": 16, "DisplayName": "是否批量", "DataType": 2}, {"ID": 17, "Name": "BATCHAPPURL", "Memo": "app批量跳转地址", "OrderNo": 17, "DisplayName": "app批量跳转地址", "DataType": 1, "DataLength": 400}, {"ID": 18, "Name": "BPMMODULECODE", "Memo": "流程模块编码，bmp_module表中的moduleCode", "OrderNo": 18, "DisplayName": "流程模块编码", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "BPMMODULEID", "Memo": "流程模块唯一id,bmp_module表中的tmuid", "OrderNo": 19, "DisplayName": "流程模块唯一id", "DataType": 1, "DataLength": 50}]}}, {"Name": "A_TODOMODULE", "Caption": "功能模块待办配置", "Memo": "待办-待办模块表", "OrderNo": 6, "GraphDesc": "Left=27.00\r\nTop=14.80\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 12, "items": [{"ID": 1, "Name": "MODULECODE", "Memo": "模块编码", "OrderNo": 1, "DisplayName": "模块编码", "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "NAME", "Memo": "显示名称", "OrderNo": 2, "DisplayName": "显示名称", "DataType": 1, "DataLength": 200}, {"ID": 3, "Name": "SORT", "Memo": "排序", "OrderNo": 3, "DisplayName": "排序", "DataType": 2}, {"ID": 4, "Name": "USED", "Memo": "是否启用", "OrderNo": 4, "DisplayName": "是否启用", "DataType": 2}, {"ID": 5, "Name": "USETYPE", "Memo": "使用类型  0 or null：PC 或 APP  1：pc  2：app", "OrderNo": 5, "DisplayName": "使用类型", "DataType": 1, "DataLength": 4000}, {"ID": 6, "Name": "MENUTYPE", "Memo": "菜单类型 0:系统模块(系统模块名称列，可选) 1：自定义菜单（系统模块名称列，手工输入）", "OrderNo": 6, "DisplayName": "菜单类型", "DataType": 2}, {"ID": 7, "Name": "ICO", "Memo": "图标", "OrderNo": 7, "DisplayName": "图标", "DataType": 1, "DataLength": 500}, {"ID": 8, "Name": "APPURL", "Memo": "跳转地址", "OrderNo": 8, "DisplayName": "跳转地址", "DataType": 1, "DataLength": 2000}, {"ID": 9, "Name": "PTMUID", "OrderNo": 9, "DisplayName": "父记录", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "ISLEVEL", "OrderNo": 10, "DataType": 2}, {"ID": 11, "Name": "FULLPATH", "OrderNo": 11, "DisplayName": "全路径", "DataType": 1, "DataLength": 8000}, {"ID": 12, "Name": "ICOCOLOR", "OrderNo": 12, "DisplayName": "图标颜色", "DataType": 1, "DataLength": 50}]}}, {"Name": "A_TODORIGHT", "Caption": "待办项目权限", "Memo": "待办-待办权限表", "OrderNo": 7, "GraphDesc": "Left=502.40\r\nTop=260.40\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 4, "items": [{"ID": 1, "Name": "TMUID", "Memo": "唯一标识", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "TODOID", "Memo": "待办ID(a_todoInfo.tmuid)", "OrderNo": 2, "DisplayName": "待办ID(a_todoInfo.tmuid)", "DataType": 1, "KeyFieldType": 3, "RelateTable": "a_todoInfo", "RelateField": "tmuid", "DataLength": 50, "GraphDesc": "P1=450.40,305.00\r\nP2=476.00,305.00\r\nP3=476.00,305.00\r\nP4=502.40,305.00\r\nHookP1=392.60,41.40\r\nHookP2=19.60,44.60\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "RIGHTID", "Memo": "权限ID", "OrderNo": 3, "DisplayName": "权限ID", "DataType": 2}, {"ID": 4, "Name": "RIGHTNAME", "Memo": "权限名称", "OrderNo": 4, "DisplayName": "权限名称", "DataType": 1, "DataLength": 100}]}}, {"Name": "未知弃用ATODOINFOOPENWINFONTSTYLECFG", "OrderNo": 8, "GraphDesc": "Left=368.20\r\nTop=768.40\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "tmuid", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "fontColor", "OrderNo": 2, "DataType": 1, "DataLength": 20}, {"ID": 3, "Name": "fontFamily", "OrderNo": 3, "DataType": 1, "DataLength": 20}, {"ID": 4, "Name": "fontSize", "OrderNo": 4, "DataType": 2}, {"ID": 5, "Name": "todoCode", "OrderNo": 5, "DataType": 1, "DataLength": 20}, {"ID": 6, "Name": "warningDays", "OrderNo": 6, "DataType": 2}]}}, {"Name": "弃用，使用缓存a_todo_status", "OrderNo": 9, "GraphDesc": "Left=34.80\r\nTop=764.40\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "tmuid", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "modulecode", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "todocode", "OrderNo": 3, "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "<PERSON>yid", "OrderNo": 4, "DataType": 2, "DataLength": 18}, {"ID": 5, "Name": "lastdt", "OrderNo": 5, "DataType": 4}]}}]}}, {"ID": 12, "Name": "公告（答疑）", "CreateDate": "2021/10/21 星期四 15:33:44", "OrderNo": 10, "ConfigStr": "DrawerWidth=1500\r\nDrawerHeight=2560\r\nWorkAreaColor=16777215\r\nSelectedColor=-2147483635\r\nDefaultObjectColor=15921906\r\nDefaultTitleColor=255\r\nDefaultPKColor=16711935\r\nDefaultFKColor=16711680\r\nDefaultBorderColor=12632256\r\nDefaultLineColor=16711680\r\nShowFieldType=1\r\nShowFieldIcon=1\r\nShowPhyFieldName=2\r\nDatabaseEngine=\r\nGenFKIndexesSQL=0\r\n", "Tables": {"Count": 6, "items": [{"Name": "NOTICE_INFO", "Caption": "系统公告信息表", "OrderNo": 1, "GraphDesc": "Left=38.00\r\nTop=32.00\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 23, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 39, "Name": "TITLE", "OrderNo": 2, "DisplayName": "标题", "DataType": 1, "DataLength": 500}, {"ID": 41, "Name": "TMTYPECODE", "OrderNo": 3, "DisplayName": "公告类型编码", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "TMVISIBLERANGE", "OrderNo": 4, "DisplayName": "可见范围，1：所有用户，2：登录用户，3：指定用户", "DataType": 1, "DataLength": 50}, {"ID": 42, "Name": "TMSTATUS", "OrderNo": 5, "DisplayName": "公告状态，1：正常，2：关闭(结帖)", "DataType": 1, "DataLength": 50}, {"ID": 35, "Name": "TMISMSG", "OrderNo": 6, "DisplayName": "是否提醒，0：不提醒，1：提醒", "DataType": 2}, {"ID": 43, "Name": "TMISMSGTYPE", "OrderNo": 7, "DisplayName": "提醒方式（多个逗号分隔）：1：全部提醒，2：系统，3：微信，4：短信", "DataType": 1, "DataLength": 100}, {"ID": 45, "Name": "TMCONTENT", "OrderNo": 8, "DisplayName": "公告内容", "DataType": 1, "DataTypeName": "TEXT"}, {"ID": 71, "Name": "TMISUSE", "OrderNo": 9, "DisplayName": "是否发布（只有发布了才能看到）", "DataType": 2}, {"ID": 19, "Name": "ISUSEPERSON", "OrderNo": 10, "DisplayName": "发布人id", "DataType": 1, "DataLength": 500}, {"ID": 20, "Name": "ISUSEPERSONNAME", "OrderNo": 11, "DisplayName": "发布人姓名", "DataType": 1, "DataLength": 500}, {"ID": 21, "Name": "ISUSETIME", "OrderNo": 12, "DisplayName": "发布时间", "DataType": 4}, {"ID": 131, "Name": "TMISMODIFY", "OrderNo": 13, "DisplayName": "是否修改（发布之后是否修改） 0：未修改，1：已修改", "DataType": 1}, {"ID": 21, "Name": "MEMO", "OrderNo": 14, "DisplayName": "备注", "DataType": 1, "DataLength": 500}, {"ID": 38, "Name": "USED", "OrderNo": 15, "DisplayName": "是否删除 1：使用；0：删除", "DataType": 2}, {"ID": 44, "Name": "GROUPID", "OrderNo": 16, "DisplayName": "分组ID", "DataType": 1, "DataLength": 100}, {"ID": 22, "Name": "GROUPID1", "OrderNo": 17, "DisplayName": "分组ID1", "DataType": 1, "DataLength": 100}, {"ID": 23, "Name": "GROUPID2", "OrderNo": 18, "DisplayName": "分组ID2", "DataType": 1, "DataLength": 100}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 19, "DataType": 4}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 20, "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "CREATE_BYNAME", "OrderNo": 21, "DisplayName": "创建人姓名", "DataType": 1, "DataLength": 500}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 22, "DataType": 4}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 23, "DataType": 1, "DataLength": 50}]}}, {"Name": "NOTICE_INFO_CACHE", "Caption": "系统公告信息表(缓存表)", "OrderNo": 2, "GraphDesc": "Left=38.84\r\nTop=420.98\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID = NOTICE_INFO.ID", "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 39, "Name": "TITLE", "OrderNo": 2, "DisplayName": "标题", "DataType": 1, "DataLength": 500}, {"ID": 41, "Name": "TMTYPECODE", "OrderNo": 3, "DisplayName": "公告类型编码", "DataType": 1, "DataLength": 50}, {"ID": 50, "Name": "TMVISIBLERANGE", "OrderNo": 4, "DisplayName": "可见范围，1：所有用户，2：登录用户，3：指定用户", "DataType": 1, "DataLength": 50}, {"ID": 42, "Name": "TMSTATUS", "OrderNo": 5, "DisplayName": "公告状态，1：正常，2：关闭(结帖)", "DataType": 1, "DataLength": 50}, {"ID": 35, "Name": "TMISMSG", "OrderNo": 6, "DisplayName": "是否提醒，0：不提醒，1：提醒", "DataType": 2}, {"ID": 43, "Name": "TMISMSGTYPE", "OrderNo": 7, "DisplayName": "提醒方式（多个逗号分隔）：1：全部提醒，2：系统，3：微信，4：短信", "DataType": 1, "DataLength": 100}, {"ID": 45, "Name": "TMCONTENT", "OrderNo": 8, "DisplayName": "公告内容", "DataType": 1, "DataTypeName": "TEXT"}, {"ID": 21, "Name": "MEMO", "OrderNo": 9, "DisplayName": "备注", "DataType": 1, "DataLength": 500}, {"ID": 38, "Name": "USED", "OrderNo": 10, "DisplayName": "是否删除 1：使用；0：删除", "DataType": 2}, {"ID": 44, "Name": "GROUPID", "OrderNo": 11, "DisplayName": "分组ID", "DataType": 1, "DataLength": 100}, {"ID": 3, "Name": "CREATE_TIME", "OrderNo": 12, "DataType": 4}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 13, "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "CREATE_BYNAME", "OrderNo": 14, "DisplayName": "创建人姓名", "DataType": 1, "DataLength": 500}, {"ID": 5, "Name": "UPDATE_TIME", "OrderNo": 15, "DataType": 4}, {"ID": 4, "Name": "UPDATE_BY", "OrderNo": 16, "DataType": 1, "DataLength": 50}]}}, {"Name": "NOTICE_TYPE", "Caption": "系统公告类型", "Memo": "公告、答疑等。", "OrderNo": 3, "GraphDesc": "Left=39.63\r\nTop=742.14\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 12, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 41, "Name": "TMTYPECODE", "OrderNo": 2, "DisplayName": "公告类型编码", "DataType": 1, "DataLength": 50}, {"ID": 37, "Name": "TMTYPENAME", "OrderNo": 3, "DisplayName": "公告类型名称", "DataType": 1, "DataLength": 100}, {"ID": 277, "Name": "TMCOLOR", "OrderNo": 4, "DisplayName": "颜色", "DataType": 1, "DataLength": 50}, {"ID": 186, "Name": "TMISFLOOR", "OrderNo": 5, "DisplayName": "是否允许回复（盖楼）", "DataType": 2}, {"ID": 187, "Name": "TMISMSG", "OrderNo": 6, "DisplayName": "是否允许提醒", "DataType": 2}, {"ID": 188, "Name": "TMISATTENTION", "OrderNo": 7, "DisplayName": "是否允许关注", "DataType": 2}, {"ID": 21, "Name": "MEMO", "OrderNo": 8, "DisplayName": "备注", "DataType": 1, "DataLength": 500}, {"ID": 83, "Name": "USE", "OrderNo": 9, "DisplayName": "是否使用 1：使用；0：不使用", "DataType": 2}, {"ID": 38, "Name": "USED", "OrderNo": 10, "DisplayName": "是否删除 1：使用；0：删除（页面不显示）", "DataType": 2}, {"ID": 39, "Name": "NOTICEID", "OrderNo": 11, "DisplayName": "公告ID：如果为空，所有公告（预留）", "DataType": 1, "DataLength": 50}, {"ID": 44, "Name": "GROUPID", "OrderNo": 12, "DisplayName": "分组ID：如果为空，通用分组", "DataType": 1, "DataLength": 100}]}}, {"Name": "NOTICE_PERSON", "Caption": "系统公告抄送人、关注人等信息", "OrderNo": 4, "GraphDesc": "Left=39.42\r\nTop=1227.72\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 39, "Name": "NOTICEID", "OrderNo": 2, "DisplayName": "公告ID", "DataType": 1, "DataLength": 50}, {"ID": 128, "Name": "PERSONID", "OrderNo": 3, "DisplayName": "人员ID", "DataType": 1, "DataLength": 50}, {"ID": 270, "Name": "PERSONNAME", "OrderNo": 4, "DisplayName": "人员名称", "DataType": 1, "DataLength": 100}, {"ID": 129, "Name": "PERSONTYPE", "OrderNo": 5, "DisplayName": "人员类型。1：抄送人，2：关注人", "DataType": 1, "DataLength": 50}, {"ID": 130, "Name": "PERSONREAD", "OrderNo": 6, "DisplayName": "是否已读", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "READCOUNT", "OrderNo": 7, "DisplayName": "阅读次数", "DataType": 2}, {"ID": 38, "Name": "USED", "OrderNo": 8, "DisplayName": "是否删除 1：使用；0：删除（页面不显示）", "DataType": 2}, {"ID": 10, "Name": "READTIME", "OrderNo": 9, "DisplayName": "阅读时间", "DataType": 4}]}}, {"Name": "NOTICE_MSG_EVENT", "Caption": "系统公告通知事件表", "Memo": "事件编码:发布[CREATE]、回复[REVERT]、抄送[CARBONCOPY]、关注[ATTENTION]、关闭[CLOSE]", "OrderNo": 5, "GraphDesc": "Left=40.37\r\nTop=1001.95\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 128, "Name": "TMEVENTCODE", "OrderNo": 2, "DisplayName": "事件编码:发布[CREATE_END]、回复[REVERT_END]、抄送[CARBONCOPY_END]、关注[ATTENTION_END]、关闭[CLOSE_END]", "DataType": 1, "DataLength": 50}, {"ID": 214, "Name": "TMEVENTNAME", "OrderNo": 3, "DisplayName": "事件名称", "DataType": 1}, {"ID": 207, "Name": "PERSONISSUE", "OrderNo": 4, "DisplayName": "发布人", "DataType": 2}, {"ID": 208, "Name": "PERSONCARBONCOPY", "OrderNo": 5, "DisplayName": "抄送人", "DataType": 2}, {"ID": 209, "Name": "PERSONATTENTION", "OrderNo": 6, "DisplayName": "关注人", "DataType": 2}, {"ID": 210, "Name": "PERSONFLOOR", "OrderNo": 7, "DisplayName": "楼层干系人", "DataType": 2}, {"ID": 189, "Name": "USE", "OrderNo": 8, "DisplayName": "是否使用 1：使用；0：不使用", "DataType": 2}, {"ID": 38, "Name": "USED", "OrderNo": 9, "DisplayName": "是否删除 1：使用；0：删除（页面不显示）", "DataType": 2}, {"ID": 39, "Name": "NOTICEID", "OrderNo": 10, "DisplayName": "公告ID：如果为空，所有公告（预留）", "DataType": 1, "DataLength": 50}, {"ID": 44, "Name": "GROUPID", "OrderNo": 11, "DisplayName": "分组ID：如果为空，通用分组", "DataType": 1, "DataLength": 100}]}}, {"Name": "NOTICE_FLOOR", "Caption": "系统公告楼层信息", "OrderNo": 6, "GraphDesc": "Left=39.29\r\nTop=1424.59\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 39, "Name": "NOTICEID", "OrderNo": 2, "DisplayName": "公告ID", "DataType": 1, "DataLength": 50}, {"ID": 128, "Name": "PERSONID", "OrderNo": 3, "DisplayName": "人员ID", "DataType": 1, "DataLength": 50}, {"ID": 278, "Name": "PERSONNAME", "OrderNo": 4, "DisplayName": "人员名称", "DataType": 1, "DataLength": 100}, {"ID": 130, "Name": "TMCONTENT", "OrderNo": 5, "DisplayName": "回复内容", "DataType": 1}, {"ID": 38, "Name": "USED", "OrderNo": 6, "DisplayName": "是否删除 1：使用；0：删除（页面不显示）", "DataType": 2}, {"ID": 7, "Name": "LEVELNUM", "OrderNo": 7, "DisplayName": "楼层号", "DataType": 2}]}}]}}, {"ID": 1, "Name": "工作流-业务报表（示例）", "CreateDate": "2021/3/29 星期一 09:58:13", "OrderNo": 11, "Params": "ShowPhyFieldName=1\r\nDatabaseEngine=\r\nExportFileName=E:\\OutDisk\\运和接口程序_TM3程序_文档\\运和智能\\0000000.部门管理\\四十五规划\\辽阳科技局项目\\【各功能负责人设计成果物】\\邹浩\\TM4系统数据表结构v0.doc", "DefDbEngine": "MYSQL", "DbConnectStr": "TCtMetaMysqlDb", "ConfigStr": "DrawerWidth=4264\r\nDrawerHeight=3560\r\nWorkAreaColor=16777215\r\nSelectedColor=-2147483635\r\nDefaultObjectColor=15921906\r\nDefaultTitleColor=255\r\nDefaultPKColor=16711935\r\nDefaultFKColor=16711680\r\nDefaultBorderColor=12632256\r\nDefaultLineColor=16711680\r\nShowFieldType=1\r\nShowFieldIcon=1\r\nShowPhyFieldName=2\r\nDatabaseEngine=\r\nGenFKIndexesSQL=0\r\n", "Tables": {"Count": 6, "items": [{"ID": 2, "Name": "ACT_BUSS_PROJ_PLAN", "Caption": "通用工作流项目设置表", "CreateDate": "2021/3/29 星期一 10:00:51", "OrderNo": 1, "GraphDesc": "Left=49.14\r\nTop=48.28\r\nBLeft=24.57\r\nBTop=24.14\r\n\r\n", "MetaFields": {"Count": 21, "items": [{"ID": 9, "Name": "ID", "OrderNo": 1, "DisplayName": "主键", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 10, "Name": "CREATE_BY", "OrderNo": 2, "DisplayName": "创建人", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "CREATE_TIME", "OrderNo": 3, "DisplayName": "创建时间", "DataType": 4}, {"ID": 18, "Name": "UPDATE_BY", "OrderNo": 4, "DisplayName": "更新人", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "UPDATE_TIME", "OrderNo": 5, "DisplayName": "更新时间", "DataType": 4}, {"ID": 17, "Name": "TENANT_UID", "OrderNo": 6, "DisplayName": "租户编号", "DataType": 1, "DataLength": 50}, {"ID": 23, "Name": "NAME", "OrderNo": 7, "DisplayName": "名称", "DataType": 1, "DataLength": 200}, {"ID": 27, "Name": "START_DAY", "OrderNo": 8, "DisplayName": "申报开始日期", "DataType": 1, "DataLength": 20}, {"ID": 20, "Name": "END_DAY", "OrderNo": 9, "DisplayName": "申报截止日期", "DataType": 1, "DataLength": 20}, {"ID": 29, "Name": "YEAR_NUM", "OrderNo": 10, "DisplayName": "申报年度", "DataType": 1, "DataLength": 20}, {"ID": 14, "Name": "MODEL_ID", "Memo": "根节点的id值，必须", "OrderNo": 11, "DisplayName": "根ID", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "PID", "Memo": "如果查询时pid为空，则为系统默认的根:root", "OrderNo": 12, "DisplayName": "父节点", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "LFT", "OrderNo": 13, "DisplayName": "左编号", "DataType": 2, "DataLength": 18}, {"ID": 16, "Name": "RGT", "OrderNo": 14, "DisplayName": "右编号", "DataType": 2, "DataLength": 18}, {"ID": 21, "Name": "LEAF", "Memo": "0：分类节点 1：叶子节点", "OrderNo": 15, "DisplayName": "叶子节点", "DataType": 2}, {"ID": 24, "Name": "ORDER_NO", "OrderNo": 16, "DisplayName": "排序编号", "DataType": 2}, {"ID": 28, "Name": "TREE_PATH", "OrderNo": 17, "DisplayName": "树形全路径", "DataType": 1, "DataLength": 1000}, {"ID": 83, "Name": "PROJECT_INFO", "OrderNo": 18, "DisplayName": "项目信息", "DataType": 1}, {"ID": 84, "Name": "STATUS", "OrderNo": 19, "DisplayName": "发布标识 1:已经发布 0：未发布", "DataType": 1}, {"ID": 85, "Name": "TEMPLATE_ID", "OrderNo": 20, "DisplayName": "表单模板id", "DataType": 1}, {"ID": 86, "Name": "PROCESS_DEF_ID", "OrderNo": 21, "DisplayName": "流程定义id", "DataType": 1}]}}, {"ID": 7, "Name": "ACT_BUSS_PROJ_PLAN_FROM", "Caption": "通用工作流项目设置表-绑定表单", "CreateDate": "2022/3/10 星期四 14:29:29", "OrderNo": 2, "GraphDesc": "Left=1272.23\r\nTop=419.79\r\nBLeft=636.12\r\nBTop=209.90\r\n", "MetaFields": {"Count": 11, "items": [{"ID": 66, "Name": "ID", "OrderNo": 1, "DisplayName": "主键ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 67, "Name": "CREATE_TIME", "OrderNo": 2, "DisplayName": "创建时间", "DataType": 4}, {"ID": 68, "Name": "CREATE_BY", "OrderNo": 3, "DisplayName": "创建人", "DataType": 1, "DataLength": 50}, {"ID": 69, "Name": "UPDATE_TIME", "OrderNo": 4, "DisplayName": "更新时间", "DataType": 4}, {"ID": 70, "Name": "UPDATE_BY", "OrderNo": 5, "DisplayName": "更新人", "DataType": 1, "DataLength": 50}, {"ID": 71, "Name": "TENANT_ID", "OrderNo": 6, "DisplayName": "租户id", "DataType": 1, "DataLength": 50}, {"ID": 72, "Name": "RID", "OrderNo": 7, "DisplayName": "关联id", "DataType": 1, "DataLength": 200}, {"ID": 73, "Name": "TEMPLATE_ID", "OrderNo": 8, "DisplayName": "模板id", "DataType": 1, "DataLength": 200}, {"ID": 74, "Name": "PROCESS_DEF_ID", "OrderNo": 9, "DisplayName": "流程定义id", "DataType": 1, "DataLength": 200}, {"ID": 75, "Name": "DATA_TYPE", "OrderNo": 10, "DisplayName": "数据类型", "DataType": 2}, {"ID": 76, "Name": "FIELD_TYPE", "OrderNo": 11, "DisplayName": "字段类型", "DataType": 2}]}}, {"ID": 28, "Name": "ACT_BUSS_BPM_INFO", "Caption": "通用工作流项目计划设置-绑定模板", "CreateDate": "2021/5/19 星期三 07:34:33", "OrderNo": 3, "GraphDesc": "Left=591.57\r\nTop=50.72\r\nBLeft=295.79\r\nBTop=25.36\r\n\r\n", "MetaFields": {"Count": 34, "items": [{"ID": 37, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "DataLength": 255}, {"ID": 42, "Name": "CREATE_BY", "OrderNo": 2, "DisplayName": "创建人", "DataType": 1, "DataLength": 50}, {"ID": 39, "Name": "CREATE_TIME", "OrderNo": 3, "DisplayName": "创建时间", "DataType": 4, "DataLength": 20}, {"ID": 41, "Name": "UPDATE_BY", "OrderNo": 4, "DisplayName": "更新人", "DataType": 1, "DataLength": 50}, {"ID": 40, "Name": "UPDATE_TIME", "OrderNo": 5, "DisplayName": "更新时间", "DataType": 4, "DataLength": 20}, {"ID": 51, "Name": "DATA_TYPE", "Memo": "用于标识 申报。合同 ，结题", "OrderNo": 6, "DisplayName": "数据类型 用于标识 1:默认表单,2:其它表单", "DataType": 2, "DataLength": 50}, {"ID": 397, "Name": "ENTERPRISE_ID\n\n", "OrderNo": 7, "DisplayName": "企业ID", "DataType": 1, "DataLength": 200}, {"ID": 429, "Name": "FILLED_BY\n\n", "OrderNo": 8, "DisplayName": "填写人", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "FORM_TEMPLATE_ID", "OrderNo": 9, "DisplayName": "表单模板id", "DataType": 1, "DataLength": 255}, {"ID": 47, "Name": "PROCESS_DEFINITION_ID", "OrderNo": 10, "DisplayName": "流程设置id", "DataType": 1, "DataLength": 255}, {"ID": 17, "Name": "PROCESS_INSTANCE_ID", "OrderNo": 11, "DisplayName": "流程实例id", "DataType": 1, "DataLength": 255}, {"ID": 18, "Name": "PROCESS_INSTANCE_STATUS", "OrderNo": 12, "DisplayName": "审核状态 1:结束,0 未开始", "DataType": 2, "DataLength": 50}, {"ID": 50, "Name": "REASON", "OrderNo": 13, "DisplayName": "审核原因", "DataType": 1, "DataLength": 1000}, {"ID": 85, "Name": "USED", "OrderNo": 14, "DisplayName": "是否使用", "DataType": 2}, {"ID": 43, "Name": "YEAR_NUM", "OrderNo": 15, "DisplayName": "申报年度", "DataType": 1, "DataLength": 20}, {"ID": 33, "Name": "PROJ_DATA_ID", "OrderNo": 16, "DisplayName": "数据id", "DataType": 1, "DataLength": 50}, {"ID": 35, "Name": "REPORTER_ID", "OrderNo": 17, "DisplayName": "申报人id", "DataType": 1, "DataLength": 255}, {"ID": 36, "Name": "REPORTER_NAME", "OrderNo": 18, "DisplayName": "申报人姓名", "DataType": 1, "DataLength": 255}, {"ID": 37, "Name": "REPORT_DATETIME", "OrderNo": 19, "DisplayName": "申报时间", "DataType": 1, "DataLength": 50}, {"ID": 38, "Name": "FORM_KEY", "OrderNo": 20, "DisplayName": "表单key", "DataType": 1, "DataLength": 2000}, {"ID": 39, "Name": "TASK_ID", "OrderNo": 21, "DisplayName": "任务id", "DataType": 1, "DataLength": 255}, {"ID": 40, "Name": "START_PROCESS_INSTANCE_ID", "OrderNo": 22, "DisplayName": "开始实例id", "DataType": 1, "DataLength": 50}, {"ID": 41, "Name": "BUSS_PROJECT_NAME", "OrderNo": 23, "DisplayName": "项目名称", "DataType": 1, "DataLength": 500}, {"ID": 42, "Name": "BUSS_PROJECT_INFO", "OrderNo": 24, "DisplayName": "项目信息", "DataType": 1, "DataLength": 1000}, {"ID": 43, "Name": "BAK1", "OrderNo": 25, "DisplayName": "备注1", "DataType": 1, "DataLength": 1000}, {"ID": 44, "Name": "BAK2", "OrderNo": 26, "DisplayName": "备注2", "DataType": 1, "DataLength": 1000}, {"ID": 46, "Name": "BAK3", "OrderNo": 27, "DisplayName": "备注3", "DataType": 1, "DataLength": 1000}, {"ID": 47, "Name": "BAK4", "OrderNo": 28, "DisplayName": "备注4", "DataType": 1, "DataLength": 1000}, {"ID": 48, "Name": "BAK5", "OrderNo": 29, "DisplayName": "备注5", "DataType": 1, "DataLength": 1000}, {"ID": 45, "Name": "BAK6", "OrderNo": 30, "DisplayName": "备注6", "DataType": 1, "DataLength": 500}, {"ID": 50, "Name": "BAK7", "OrderNo": 31, "DisplayName": "备注7", "DataType": 1, "DataLength": 500}, {"ID": 51, "Name": "BAK8", "OrderNo": 32, "DisplayName": "备注8", "DataType": 1, "DataLength": 500}, {"ID": 52, "Name": "BAK9", "OrderNo": 33, "DisplayName": "备注9", "DataType": 1, "DataLength": 500}, {"ID": 49, "Name": "BAK10", "OrderNo": 34, "DisplayName": "备注10", "DataType": 1, "DataLength": 500}]}}, {"ID": 8, "Name": "ACT_BUSS_TITLE", "Caption": "通用工作流项目表头设置", "CreateDate": "2022/3/10 星期四 14:35:23", "OrderNo": 4, "GraphDesc": "Left=1267.91\r\nTop=51.38\r\nBLeft=633.95\r\nBTop=25.69\r\n", "MetaFields": {"Count": 14, "items": [{"ID": 88, "Name": "ID", "OrderNo": 1, "DisplayName": "主键ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 89, "Name": "CREATE_TIME", "OrderNo": 2, "DisplayName": "创建时间", "DataType": 4}, {"ID": 90, "Name": "CREATE_BY", "OrderNo": 3, "DisplayName": "创建人", "DataType": 1, "DataLength": 50}, {"ID": 91, "Name": "UPDATE_TIME", "OrderNo": 4, "DisplayName": "更新时间", "DataType": 4}, {"ID": 92, "Name": "UPDATE_BY", "OrderNo": 5, "DisplayName": "更新人", "DataType": 1, "DataLength": 50}, {"ID": 93, "Name": "TENANT_ID", "OrderNo": 6, "DisplayName": "租户id", "DataType": 1, "DataLength": 50}, {"ID": 94, "Name": "PAGE_TYPE", "OrderNo": 7, "DisplayName": "页面类型 0通用 1填写 2审核 3查询", "DataType": 1, "DataLength": 50}, {"ID": 95, "Name": "FIELD_NAME", "OrderNo": 8, "DisplayName": "字段名称", "DataType": 1, "DataLength": 50}, {"ID": 96, "Name": "ORIGINAL_TEXT", "OrderNo": 9, "DisplayName": "原始名称", "DataType": 1, "DataLength": 200}, {"ID": 97, "Name": "SHOW_TEXT", "OrderNo": 10, "DisplayName": "显示名称", "DataType": 1, "DataLength": 200}, {"ID": 99, "Name": "SEARCH_TYPE", "OrderNo": 11, "DisplayName": "检索类型", "DataType": 1, "DataLength": 200}, {"ID": 98, "Name": "IS_SHOW", "OrderNo": 12, "DisplayName": "是否显示", "DataType": 2}, {"ID": 100, "Name": "TMSORT", "OrderNo": 13, "DisplayName": "排序", "DataType": 2}, {"ID": 14, "Name": "FIELD_FORM", "OrderNo": 14, "DisplayName": "表单中表格名称及字段名称TABLENAME.FIELDNAME", "DataType": 1, "DataLength": 100}]}}, {"ID": 23, "Name": "ACT_BUSS_USER_T_INDEX_LIB", "Caption": "【表格】通用指标库表（指标设置）", "CreateDate": "2021/3/30 星期二 15:25:27", "OrderNo": 5, "GraphDesc": "Left=73.71\r\nTop=881.17\r\nBLeft=36.86\r\nBTop=440.58\r\n", "MetaFields": {"Count": 15, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 330, "Name": "XH", "OrderNo": 2, "DisplayName": "序号", "DataType": 1, "DataLength": 10}, {"ID": 2031, "Name": "ITEMNAME", "OrderNo": 3, "DisplayName": "指标名称", "DataType": 1, "DataLength": 500}, {"ID": 22, "Name": "ITEMTYPE", "Memo": "0:可选；1：必填", "OrderNo": 4, "DisplayName": "指标类型", "DataType": 1, "DataLength": 50}, {"ID": 329, "Name": "TMSORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}, {"ID": 331, "Name": "CALCTYPE", "Memo": "0:录入项；1：计算项", "OrderNo": 6, "DisplayName": "计算类型", "DataType": 2}, {"ID": 8, "Name": "FUNTYPE", "Memo": "1:附件上传功能；2：其它功能使用", "OrderNo": 7, "DisplayName": "功能类型", "DataType": 1, "DataLength": 50}, {"ID": 332, "Name": "CALXH", "OrderNo": 8, "DisplayName": "计算关系", "DataType": 1, "DataLength": 10}, {"ID": 15, "Name": "DUTY_UNIT", "OrderNo": 9, "DisplayName": "责任单位（责任部门）", "DataType": 1, "DataLength": 100}, {"ID": 446, "Name": "FORM_DATA_INDEX_ID", "OrderNo": 10, "DisplayName": "关联标识", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "PARAM1", "OrderNo": 11, "DisplayName": "预留参数1", "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "PARAM2", "OrderNo": 12, "DisplayName": "预留参数2", "DataType": 1, "DataLength": 100}, {"ID": 19, "Name": "PARAM3", "OrderNo": 13, "DisplayName": "预留参数3", "DataType": 1, "DataLength": 100}, {"ID": 20, "Name": "PARAM4", "OrderNo": 14, "DisplayName": "预留参数4", "DataType": 1, "DataLength": 100}, {"ID": 21, "Name": "PARAM5", "OrderNo": 15, "DisplayName": "预留参数5", "DataType": 1, "DataLength": 100}]}}, {"ID": 31, "Name": "ACT_BUSS_USER_T_INDEX_INFO", "Caption": "【表格】通用指标库信息表（指标数据）", "Memo": "表单中配置：\n{\"tdsAlias\":\"ACT_BUSS_USER_T_INDEX_INFO\",\"tdsInParaAlias\":\"FORM_DATA_INDEX_ID=@dataid|FJLX=fj1\",\"tdsPage\":1,\"tdsPageSize\":100,\"tdsShowQueryBar\":false,\"showAdd\":false,\"showDel\":false,\"showSave\":false,\"saveNullMsg\":false,\"height\":200}", "CreateDate": "2021/3/30 星期二 16:16:33", "OrderNo": 6, "GraphDesc": "Left=582.12\r\nTop=886.34\r\nBLeft=291.06\r\nBTop=443.17\r\n", "MetaFields": {"Count": 20, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 39, "Name": "TDSPID", "OrderNo": 2, "DisplayName": "数据源树形PID", "DataType": 1, "DataLength": 100}, {"ID": 9, "Name": "PID", "OrderNo": 3, "DisplayName": "指标配置表ID（ACT_BUSS_USER_T_INDEX_LIB.ID）", "DataType": 1, "DataLength": 50}, {"ID": 2309, "Name": "FJLX", "OrderNo": 4, "DisplayName": "附件类别", "DataType": 1, "DataLength": 50}, {"ID": 2310, "Name": "SCLX", "Memo": "1，必须上传，2可选", "OrderNo": 5, "DisplayName": "上传类型", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "SL", "OrderNo": 6, "DisplayName": "数量", "DataType": 1, "DataLength": 50}, {"ID": 2312, "Name": "FJMC", "OrderNo": 7, "DisplayName": "附件名称", "DataType": 1, "DataLength": 200}, {"ID": 27, "Name": "FJSM", "OrderNo": 8, "DisplayName": "附件说明", "DataType": 1, "DataLength": 1000}, {"ID": 18, "Name": "WZMC", "OrderNo": 9, "DisplayName": "物资名称", "DataType": 1, "DataLength": 200}, {"ID": 19, "Name": "HTJE", "OrderNo": 10, "DisplayName": "合同金额", "DataType": 3}, {"ID": 28, "Name": "FJDATE", "OrderNo": 11, "DisplayName": "附件日期", "DataType": 4}, {"ID": 2313, "Name": "WJMC", "OrderNo": 12, "DisplayName": "文件名称", "DataType": 1, "DataLength": 200}, {"ID": 13, "Name": "WJMC_URL", "Memo": "固定列，文件名称的下载地址", "OrderNo": 13, "DisplayName": "文件名称_URL", "DataType": 1, "DataLength": 500}, {"ID": 2314, "Name": "XZDZ", "OrderNo": 14, "DisplayName": "下载地址", "DataType": 1, "DataLength": 500}, {"ID": 551, "Name": "FORM_DATA_INDEX_ID", "OrderNo": 15, "DisplayName": "关联标识", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "PARAM1", "OrderNo": 16, "DisplayName": "预留参数1", "DataType": 1, "DataLength": 500}, {"ID": 11, "Name": "PARAM2", "OrderNo": 17, "DisplayName": "预留参数2", "DataType": 1, "DataLength": 500}, {"ID": 12, "Name": "PARAM3", "OrderNo": 18, "DisplayName": "预留参数3", "DataType": 1, "DataLength": 500}, {"ID": 45, "Name": "PARAM4", "OrderNo": 19, "DisplayName": "预留参数4", "DataType": 1, "DataLength": 500}, {"ID": 44, "Name": "PARAM5", "OrderNo": 20, "DisplayName": "预留参数5", "DataType": 1, "DataLength": 500}]}}]}}, {"ID": 14, "Name": "项目计划管理", "CreateDate": "2022/4/19 星期二 08:03:17", "OrderNo": 12, "Tables": {"Count": 7, "items": [{"ID": 1, "Name": "PM_MDM_LIB", "Caption": "项目标准库", "CreateDate": "2022/3/17 星期四 15:24:39", "OrderNo": 1, "GraphDesc": "Left=49.90\r\nTop=317.46\r\nBLeft=24.95\r\nBTop=158.73\r\n", "MetaFields": {"Count": 13, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 44, "Name": "ID_ALIAS", "Memo": "目的是，将来可用别名检索项目", "OrderNo": 2, "DisplayName": "项目ID别名", "DataType": 1, "DataLength": 200}, {"ID": 13, "Name": "THEMESID", "Memo": "战略组题ID", "OrderNo": 3, "DisplayName": "战略组题ID", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "FUNSIGNID", "Memo": "数据隔离ID，空值代表通用计划类型", "OrderNo": 4, "DisplayName": "功能标记ID（数据隔离）", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "TMSHOW", "Memo": "控制可见性", "OrderNo": 5, "DisplayName": "是否使用", "DataType": 2}, {"ID": 14, "Name": "TMUSED", "Memo": "控制删除", "OrderNo": 6, "DisplayName": "是否删除", "DataType": 2}, {"ID": 21, "Name": "TMSORT", "Memo": "用于设置显示顺序", "OrderNo": 7, "DisplayName": "排序", "DataType": 2}, {"ID": 12, "Name": "NAME", "Memo": "任务名称", "OrderNo": 8, "DisplayName": "计划名称(可修改)", "DataType": 1, "DataLength": 300}, {"ID": 33, "Name": "BAK1", "OrderNo": 9, "DisplayName": "备用字段1", "DataType": 1, "DataLength": 500}, {"ID": 34, "Name": "BAK2", "OrderNo": 10, "DisplayName": "备用字段2", "DataType": 1, "DataLength": 500}, {"ID": 35, "Name": "BAK3", "OrderNo": 11, "DisplayName": "备用字段3", "DataType": 1, "DataLength": 500}, {"ID": 38, "Name": "BAK4", "OrderNo": 12, "DisplayName": "备用字段4", "DataType": 1, "DataLength": 500}, {"ID": 39, "Name": "BAK5", "OrderNo": 13, "DisplayName": "备用字段5", "DataType": 1, "DataLength": 500}]}}, {"ID": 1, "Name": "PM_MANAGE_TPL_CFG_SRC", "Caption": "计划管理模板配置数据来源表", "CreateDate": "2022/1/29 星期六 09:10:30", "OrderNo": 2, "GraphDesc": "Left=48.80\r\nTop=27.60\r\nBLeft=24.40\r\nBTop=13.80\r\n", "MetaFields": {"Count": 14, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 13, "Name": "THEMESID", "Memo": "战略组题ID", "OrderNo": 2, "DisplayName": "战略组题ID", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "FUNSIGNID", "Memo": "数据隔离ID，空值代表通用计划类型", "OrderNo": 3, "DisplayName": "功能标记ID（数据隔离）", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "NAME", "OrderNo": 4, "DisplayName": "名称", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "COMMNAME", "Memo": "组件名称", "OrderNo": 5, "DisplayName": "组件名称", "DataType": 1, "DataLength": 255}, {"ID": 7, "Name": "COMMPATH", "Memo": "组件路径", "OrderNo": 6, "DisplayName": "组件路径", "DataType": 1, "DataLength": 255}, {"ID": 8, "Name": "COMMPARAMS", "Memo": "{\"tdsAlias\":\"ACT_BUSS_USER_T_FYXM_HZ\",\"tdsInParaAlias\":\"XMLB=检维修费用\",\"tdsPage\":1,\"tdsPageSize\":100,\"tdsShowQueryBar\":true,\"showAdd\":false,\"showDel\":false,\"showSave\":false,\"saveNullMsg\":false,\"height\":550}", "OrderNo": 7, "DisplayName": "组件参数", "DataType": 1, "DataLength": 2000}, {"ID": 32, "Name": "COMM_EMBED_MODE", "Memo": "0,1：超链接模式，2：嵌入模式，3：通过数据递归渲染（需要ID，PID）", "OrderNo": 8, "DisplayName": "组件嵌入模式", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "COMM_COL_ID", "OrderNo": 9, "DisplayName": "数据唯一ID字段名", "DataType": 1}, {"ID": 13, "Name": "COMM_COL_PID", "OrderNo": 10, "DisplayName": "数据父ID字段名", "DataType": 1}, {"ID": 33, "Name": "COMM_COL_START_TIME", "Memo": "返回数据的开始时间字段名称", "OrderNo": 11, "DisplayName": "开始时间字段名称", "DataType": 1, "DataLength": 100}, {"ID": 34, "Name": "COMM_COL_END_TIME", "Memo": "返回数据的结束时间字段名称", "OrderNo": 12, "DisplayName": "结束时间字段名称", "DataType": 1, "DataLength": 100}, {"ID": 9, "Name": "TMUSED", "OrderNo": 13, "DisplayName": "是否使用", "DataType": 2}, {"ID": 11, "Name": "TMSORT", "OrderNo": 14, "DisplayName": "排序", "DataType": 2}]}}, {"ID": 1, "Name": "PM_MANAGE_TPL_CFG", "Caption": "计划管理模板配置", "CreateDate": "2022/3/17 星期四 15:24:39", "OrderNo": 3, "GraphDesc": "Left=438.80\r\nTop=27.60\r\nBLeft=219.40\r\nBTop=13.80\r\n", "MetaFields": {"Count": 35, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "计划ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 13, "Name": "THEMESID", "Memo": "战略组题ID", "OrderNo": 2, "DisplayName": "战略组题ID", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "FUNSIGNID", "Memo": "数据隔离ID，空值代表通用计划类型", "OrderNo": 3, "DisplayName": "功能标记ID（数据隔离）", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "ROOTID", "Memo": "每个项目一棵树（PROJECT_ID）", "OrderNo": 4, "DisplayName": "根节点ID", "DataType": 1, "DataLength": 50}, {"ID": 2, "Name": "PID", "OrderNo": 5, "DisplayName": "父ID", "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "LFT", "OrderNo": 6, "DisplayName": "左编号", "DataType": 2, "DataLength": 18}, {"ID": 18, "Name": "RGT", "OrderNo": 7, "DisplayName": "右编号", "DataType": 2, "DataLength": 18}, {"ID": 4, "Name": "LEAF", "Memo": "0：分类节点 1：叶子节点", "OrderNo": 8, "DisplayName": "叶子节点", "DataType": 2}, {"ID": 5, "Name": "TREE_PATH", "OrderNo": 9, "DisplayName": "树形全路径", "DataType": 1, "DataLength": 1000}, {"ID": 13, "Name": "MEMO", "OrderNo": 10, "DisplayName": "备注信息", "DataType": 1, "DataLength": 1000}, {"ID": 15, "Name": "TMSHOW", "Memo": "控制可见性", "OrderNo": 11, "DisplayName": "是否使用", "DataType": 2}, {"ID": 14, "Name": "TMUSED", "Memo": "控制删除", "OrderNo": 12, "DisplayName": "是否删除", "DataType": 2}, {"ID": 21, "Name": "TMSORT", "Memo": "用于设置显示顺序", "OrderNo": 13, "DisplayName": "排序", "DataType": 2}, {"ID": 18, "Name": "PLAN_RELATION_ID", "Memo": "工作流功能：ACTIVITYID(流程节点ID)", "OrderNo": 14, "DisplayName": "计划与第三方关系ID，例：ACTIVITYID(流程节点ID)", "DataType": 1, "DataLength": 300}, {"ID": 41, "Name": "PLAN_NUM", "Memo": "自动生成", "OrderNo": 15, "DisplayName": "计划序号，例如：1.1.1.2", "DataType": 1, "DataLength": 100}, {"ID": 42, "Name": "PLAN_NUM_SORT", "Memo": "自动生成：补零排序", "OrderNo": 16, "DisplayName": "计划序号排序：00001.00001.00001.00002", "DataType": 1, "DataLength": 300}, {"ID": 20, "Name": "PLAN_LEVEL", "Memo": "计划级别（1~n级计划）", "OrderNo": 17, "DisplayName": "计划级别", "DataType": 1, "DataLength": 50}, {"ID": 40, "Name": "PLAN_INIT_NAME", "OrderNo": 18, "DisplayName": "计划初始名称(从第三方同步时的)", "DataType": 1, "DataLength": 300}, {"ID": 12, "Name": "PLAN_NAME", "Memo": "任务名称", "OrderNo": 19, "DisplayName": "计划名称(可修改)", "DataType": 1, "DataLength": 300}, {"ID": 21, "Name": "PLAN_START_TIME", "Memo": "【用来初始化数据】", "OrderNo": 20, "DisplayName": "计划开始时间", "DataType": 4}, {"ID": 22, "Name": "PLAN_END_TIME", "Memo": "【用来初始化数据】", "OrderNo": 21, "DisplayName": "计划结束时间", "DataType": 4}, {"ID": 23, "Name": "PLAN_COMPLETE_TIME", "Memo": "【用来初始化数据】", "OrderNo": 22, "DisplayName": "实际完成时间", "DataType": 4}, {"ID": 24, "Name": "PLAN_WARN_DAY", "Memo": "【用来初始化数据】", "OrderNo": 23, "DisplayName": "提前提醒天数", "DataType": 2}, {"ID": 25, "Name": "PLAN_PERSON_ID", "Memo": "【用来初始化数据】", "OrderNo": 24, "DisplayName": "负责人ID", "DataType": 1, "DataLength": 50}, {"ID": 26, "Name": "PLAN_PERSON_NAME", "Memo": "【用来初始化数据】", "OrderNo": 25, "DisplayName": "负责人姓名", "DataType": 1, "DataLength": 50}, {"ID": 27, "Name": "PLAN_DELAY_RATIO", "Memo": "【用来初始化数据】", "OrderNo": 26, "DisplayName": "延期考核百分比", "DataType": 2}, {"ID": 36, "Name": "PLAN_WARN_INFO", "Memo": "用户缓存预警信息", "OrderNo": 27, "DisplayName": "计划预警信息", "DataType": 1, "DataLength": 1000}, {"ID": 33, "Name": "BAK1", "OrderNo": 28, "DisplayName": "备用字段1", "DataType": 1, "DataLength": 500}, {"ID": 34, "Name": "BAK2", "OrderNo": 29, "DisplayName": "备用字段2", "DataType": 1, "DataLength": 500}, {"ID": 35, "Name": "BAK3", "OrderNo": 30, "DisplayName": "备用字段3", "DataType": 1, "DataLength": 500}, {"ID": 38, "Name": "BAK4", "OrderNo": 31, "DisplayName": "备用字段4", "DataType": 1, "DataLength": 500}, {"ID": 39, "Name": "BAK5", "OrderNo": 32, "DisplayName": "备用字段5", "DataType": 1, "DataLength": 500}, {"ID": 36, "Name": "PROCESS_DEFINITION_ID", "Memo": "流程实例ID", "OrderNo": 33, "DisplayName": "流程设置ID", "DataType": 1, "DataLength": 255}, {"ID": 35, "Name": "DATA_SOURCE_ID", "Memo": "PLAN_MANAGE_TPL_CFG_SRC.ID", "OrderNo": 34, "DisplayName": "数据来源ID", "DataType": 1, "DataLength": 100}, {"ID": 37, "Name": "DATA_SOURCE_NAME", "Memo": "PLAN_MANAGE_TPL_CFG_SRC.NAME", "OrderNo": 35, "DisplayName": "数据来源名称", "DataType": 1, "DataLength": 100}]}}, {"ID": 1, "Name": "PM_MANAGE", "Caption": "计划管理（甘特图）", "CreateDate": "2022/3/17 星期四 15:24:39", "OrderNo": 4, "GraphDesc": "Left=1135.79\r\nTop=29.89\r\nBLeft=567.90\r\nBTop=14.94\r\n", "MetaFields": {"Count": 36, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "计划ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 13, "Name": "THEMESID", "Memo": "战略组题ID", "OrderNo": 2, "DisplayName": "战略组题ID", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "FUNSIGNID", "Memo": "数据隔离ID，空值代表通用计划类型", "OrderNo": 3, "DisplayName": "功能标记ID（数据隔离）", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "ROOTID", "Memo": "每个项目一棵树（PROJECT_ID）", "OrderNo": 4, "DisplayName": "根节点ID", "DataType": 1, "DataLength": 50}, {"ID": 2, "Name": "PID", "OrderNo": 5, "DisplayName": "父ID", "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "LFT", "OrderNo": 6, "DisplayName": "左编号", "DataType": 2, "DataLength": 18}, {"ID": 18, "Name": "RGT", "OrderNo": 7, "DisplayName": "右编号", "DataType": 2, "DataLength": 18}, {"ID": 4, "Name": "LEAF", "Memo": "0：分类节点 1：叶子节点", "OrderNo": 8, "DisplayName": "叶子节点", "DataType": 2}, {"ID": 5, "Name": "TREE_PATH", "OrderNo": 9, "DisplayName": "树形全路径", "DataType": 1, "DataLength": 1000}, {"ID": 13, "Name": "MEMO", "OrderNo": 10, "DisplayName": "备注信息", "DataType": 1, "DataLength": 1000}, {"ID": 15, "Name": "TMSHOW", "Memo": "控制可见性", "OrderNo": 11, "DisplayName": "是否使用", "DataType": 2}, {"ID": 14, "Name": "TMUSED", "Memo": "控制删除", "OrderNo": 12, "DisplayName": "是否删除", "DataType": 2}, {"ID": 21, "Name": "TMSORT", "Memo": "用于设置显示顺序", "OrderNo": 13, "DisplayName": "排序", "DataType": 2}, {"ID": 16, "Name": "TYPE_ID", "Memo": "ACT_BUSS_PROJ_PLAN.ID", "OrderNo": 14, "DisplayName": "类别ID", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "TYPE_NAME", "Memo": "ACT_BUSS_PROJ_PLAN.NAME", "OrderNo": 15, "DisplayName": "类别名称", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "PROJECT_ID", "Memo": "ACT_BUSS_BPM_INFO.ID 或者 PROJECT_LIB.ID", "OrderNo": 16, "DisplayName": "项目ID", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "PROJECT_NAME", "Memo": "ACT_BUSS_BPM_INFO.BUSS_PROJECT_NAME", "OrderNo": 17, "DisplayName": "项目名称", "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "PLAN_RELATION_ID", "Memo": "工作流功能：ACTIVITYID(流程节点ID)", "OrderNo": 18, "DisplayName": "计划与第三方关系ID，例：ACTIVITYID(流程节点ID)", "DataType": 1, "DataLength": 300}, {"ID": 41, "Name": "PLAN_NUM", "Memo": "自动生成", "OrderNo": 19, "DisplayName": "计划序号，例如：1.1.1.2", "DataType": 1, "DataLength": 100}, {"ID": 42, "Name": "PLAN_NUM_SORT", "Memo": "自动生成：补零排序", "OrderNo": 20, "DisplayName": "计划序号排序：00001.00001.00001.00002", "DataType": 1, "DataLength": 300}, {"ID": 20, "Name": "PLAN_LEVEL", "Memo": "计划级别（1~n级计划）", "OrderNo": 21, "DisplayName": "计划级别", "DataType": 1, "DataLength": 50}, {"ID": 40, "Name": "PLAN_INIT_NAME", "OrderNo": 22, "DisplayName": "计划初始名称(从第三方同步时的)", "DataType": 1, "DataLength": 300}, {"ID": 12, "Name": "PLAN_NAME", "Memo": "任务名称", "OrderNo": 23, "DisplayName": "计划名称(可修改)", "DataType": 1, "DataLength": 300}, {"ID": 21, "Name": "PLAN_START_TIME", "OrderNo": 24, "DisplayName": "计划开始时间", "DataType": 4}, {"ID": 22, "Name": "PLAN_END_TIME", "OrderNo": 25, "DisplayName": "计划结束时间", "DataType": 4}, {"ID": 23, "Name": "PLAN_COMPLETE_TIME", "OrderNo": 26, "DisplayName": "实际完成时间", "DataType": 4}, {"ID": 24, "Name": "PLAN_WARN_DAY", "OrderNo": 27, "DisplayName": "提前提醒天数", "DataType": 2}, {"ID": 25, "Name": "PLAN_PERSON_ID", "OrderNo": 28, "DisplayName": "负责人ID", "DataType": 1, "DataLength": 50}, {"ID": 26, "Name": "PLAN_PERSON_NAME", "OrderNo": 29, "DisplayName": "负责人姓名", "DataType": 1, "DataLength": 50}, {"ID": 27, "Name": "PLAN_DELAY_RATIO", "OrderNo": 30, "DisplayName": "延期考核百分比", "DataType": 2}, {"ID": 36, "Name": "PLAN_WARN_INFO", "Memo": "用户缓存预警信息", "OrderNo": 31, "DisplayName": "计划预警信息", "DataType": 1, "DataLength": 1000}, {"ID": 33, "Name": "BAK1", "OrderNo": 32, "DisplayName": "备用字段1", "DataType": 1, "DataLength": 500}, {"ID": 34, "Name": "BAK2", "OrderNo": 33, "DisplayName": "备用字段2", "DataType": 1, "DataLength": 500}, {"ID": 35, "Name": "BAK3", "OrderNo": 34, "DisplayName": "备用字段3", "DataType": 1, "DataLength": 500}, {"ID": 38, "Name": "BAK4", "OrderNo": 35, "DisplayName": "备用字段4", "DataType": 1, "DataLength": 500}, {"ID": 39, "Name": "BAK5", "OrderNo": 36, "DisplayName": "备用字段5", "DataType": 1, "DataLength": 500}]}}, {"ID": 1, "Name": "PM_MANAGE_FRONT_LIST", "Caption": "计划管理-前置任务列表", "CreateDate": "2022/3/17 星期四 15:24:39", "OrderNo": 5, "GraphDesc": "Left=1136.39\r\nTop=655.42\r\nBLeft=568.20\r\nBTop=327.71\r\n", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "主键", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 13, "Name": "THEMESID", "Memo": "战略组题ID", "OrderNo": 2, "DisplayName": "战略组题ID", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "FUNSIGNID", "Memo": "数据隔离ID，空值代表通用计划类型", "OrderNo": 3, "DisplayName": "功能标记ID（数据隔离）", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "PROJECT_ID", "OrderNo": 4, "DisplayName": "项目ID", "DataType": 1, "DataLength": 50}, {"ID": 2, "Name": "PLAN_ID", "Memo": "PLAN_MANAGE.ID", "OrderNo": 5, "DisplayName": "计划ID", "DataType": 1, "DataLength": 100}, {"ID": 33, "Name": "PLAN_PID", "Memo": "PLAN_MANAGE.ID", "OrderNo": 6, "DisplayName": "计划父ID", "DataType": 1, "DataLength": 100}]}}, {"ID": 1, "Name": "PM_MANAGE_LOG", "Caption": "计划管理修改日志", "CreateDate": "2022/3/17 星期四 15:24:39", "OrderNo": 6, "GraphDesc": "Left=1141.42\r\nTop=807.62\r\nBLeft=570.71\r\nBTop=403.81\r\n", "MetaFields": {"Count": 14, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "主键", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 13, "Name": "THEMESID", "Memo": "战略组题ID", "OrderNo": 2, "DisplayName": "战略组题ID", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "FUNSIGNID", "Memo": "数据隔离ID，空值代表通用计划类型", "OrderNo": 3, "DisplayName": "功能标记ID（数据隔离）", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "PROJECT_ID", "OrderNo": 4, "DisplayName": "项目ID", "DataType": 1, "DataLength": 50}, {"ID": 6, "Name": "TABLE_NAME", "Memo": "如(计划管理)：PLAN_MANAGE", "OrderNo": 5, "DisplayName": "表名称(如计划管理：PLAN_MANAGE)", "DataType": 1, "DataLength": 100}, {"ID": 2, "Name": "TABLE_COL_NAME", "OrderNo": 6, "DisplayName": "列名称", "DataType": 1, "DataLength": 100}, {"ID": 33, "Name": "DATA_ID", "OrderNo": 7, "DisplayName": "数据ID", "DataType": 1, "DataLength": 100}, {"ID": 7, "Name": "DATA_OLD_INFO", "OrderNo": 8, "DisplayName": "旧信息", "DataType": 1, "DataLength": 500}, {"ID": 8, "Name": "DATA_NEW_INFO", "OrderNo": 9, "DisplayName": "新信息", "DataType": 1, "DataLength": 500}, {"ID": 9, "Name": "DATA_TIME", "OrderNo": 10, "DisplayName": "发生时间", "DataType": 4}, {"ID": 10, "Name": "PERSON_ID", "OrderNo": 11, "DisplayName": "操作人ID", "DataType": 1, "DataLength": 100}, {"ID": 11, "Name": "PERSON_NAME", "OrderNo": 12, "DisplayName": "操作人姓名", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "ORG_ID", "OrderNo": 13, "DisplayName": "操作人所在机构ID", "DataType": 1, "DataLength": 100}, {"ID": 13, "Name": "ORG_NAME", "OrderNo": 14, "DisplayName": "操作人所在机构名称", "DataType": 1, "DataLength": 100}]}}, {"ID": 1, "Name": "PM_ASSESS_INFO", "Caption": "考核数据表（不做了，使用广西石化考核功能）", "CreateDate": "2022/3/17 星期四 15:24:39", "OrderNo": 7, "GraphDesc": "Left=1907.60\r\nTop=32.02\r\nBLeft=953.80\r\nBTop=16.01\r\n", "MetaFields": {"Count": 22, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID（考核ID）", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 13, "Name": "THEMESID", "Memo": "战略组题ID", "OrderNo": 2, "DisplayName": "战略组题ID", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "FUNSIGNID", "Memo": "数据隔离ID，空值代表通用计划类型", "OrderNo": 3, "DisplayName": "功能标记ID（数据隔离）", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "PLAN_ID", "OrderNo": 4, "DisplayName": "计划ID", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "NAME", "Memo": "任务名称", "OrderNo": 5, "DisplayName": "计划名称", "DataType": 1, "DataLength": 300}, {"ID": 12, "Name": "LEVEL", "Memo": "计划级别（1~n级计划）", "OrderNo": 6, "DisplayName": "计划级别", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "START_TIME", "OrderNo": 7, "DisplayName": "计划开始时间", "DataType": 4}, {"ID": 22, "Name": "END_TIME", "OrderNo": 8, "DisplayName": "计划结束时间", "DataType": 4}, {"ID": 23, "Name": "COMPLETE_TIME", "OrderNo": 9, "DisplayName": "实际完成时间", "DataType": 4}, {"ID": 15, "Name": "ISASSESS", "OrderNo": 10, "DisplayName": "是否考核", "DataType": 2}, {"ID": 16, "Name": "DELAY_DAY", "OrderNo": 11, "DisplayName": "计划延期天数", "DataType": 2}, {"ID": 17, "Name": "ASSESS_MONEY", "OrderNo": 12, "DisplayName": "建议考核金额", "DataType": 3}, {"ID": 18, "Name": "ASSESS_EXPLAIN", "OrderNo": 13, "DisplayName": "考核原因", "DataType": 1, "DataLength": 2000}, {"ID": 19, "Name": "DUTY_ORG_ID", "OrderNo": 14, "DisplayName": "责任部门ID", "DataType": 1}, {"ID": 20, "Name": "DUTY_ORG_NAME", "OrderNo": 15, "DisplayName": "责任部门名称", "DataType": 1}, {"ID": 14, "Name": "TMUSED", "Memo": "控制删除", "OrderNo": 16, "DisplayName": "是否删除", "DataType": 2}, {"ID": 21, "Name": "TMSORT", "Memo": "用于设置显示顺序", "OrderNo": 17, "DisplayName": "排序", "DataType": 2}, {"ID": 33, "Name": "BAK1", "OrderNo": 18, "DisplayName": "备用字段1", "DataType": 1, "DataLength": 500}, {"ID": 34, "Name": "BAK2", "OrderNo": 19, "DisplayName": "备用字段2", "DataType": 1, "DataLength": 500}, {"ID": 35, "Name": "BAK3", "OrderNo": 20, "DisplayName": "备用字段3", "DataType": 1, "DataLength": 500}, {"ID": 38, "Name": "BAK4", "OrderNo": 21, "DisplayName": "备用字段4", "DataType": 1, "DataLength": 500}, {"ID": 39, "Name": "BAK5", "OrderNo": 22, "DisplayName": "备用字段5", "DataType": 1, "DataLength": 500}]}}]}}, {"ID": 13, "Name": "框架-系统工具栏", "CreateDate": "2022/3/17 星期四 15:12:39", "OrderNo": 13, "Tables": {"Count": 1, "items": [{"ID": 1, "Name": "SYS_TOOLBAR_LINK", "Caption": "工具栏link配置表", "Memo": "工具栏链接配置", "CreateDate": "2022/3/17 星期四 15:24:39", "OrderNo": 1, "GraphDesc": "Left=70.80\r\nTop=45.20\r\nBLeft=35.40\r\nBTop=22.60\r\n", "MetaFields": {"Count": 12, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "主键", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "NAME", "OrderNo": 2, "DisplayName": "链接名称", "DataType": 1, "DataLength": 100}, {"ID": 3, "Name": "IMGURL", "OrderNo": 3, "DisplayName": "图片地址", "DataType": 1, "DataLength": 1000}, {"ID": 18, "Name": "TARGETURLTYPE", "Memo": "0系统组件(默认)，1外部连接", "OrderNo": 4, "DisplayName": "跳转类型", "DataType": 1, "DataLength": 100}, {"ID": 4, "Name": "TARGETURL", "OrderNo": 5, "DisplayName": "跳转地址", "DataType": 1, "DataLength": 1000}, {"ID": 5, "Name": "SHOWTYPE", "Memo": "0弹出窗体（默认），1新页面打开", "OrderNo": 6, "DisplayName": "显示方式", "DataType": 2}, {"ID": 12, "Name": "TIPTEXT", "Memo": "提示内容", "OrderNo": 7, "DisplayName": "悬浮提示内容", "DataType": 1, "DataLength": 500}, {"ID": 13, "Name": "MEMO", "OrderNo": 8, "DisplayName": "备注信息", "DataType": 1, "DataLength": 1000}, {"ID": 15, "Name": "TMSHOW", "Memo": "控制可见性", "OrderNo": 9, "DisplayName": "是否隐藏", "DataType": 2}, {"ID": 14, "Name": "TMUSED", "Memo": "控制删除", "OrderNo": 10, "DisplayName": "是否启用", "DataType": 2}, {"ID": 21, "Name": "TMSORT", "Memo": "用于设置显示顺序", "OrderNo": 11, "DisplayName": "排序", "DataType": 2}, {"ID": 16, "Name": "ORGDM", "Memo": "所属机构id", "OrderNo": 12, "DisplayName": "机构代码", "DataType": 1, "DataLength": 50}]}}]}}, {"ID": 8, "Name": "框架-已读未读", "CreateDate": "2022/5/30 星期一 14:15:44", "OrderNo": 14, "Tables": {"Count": 1, "items": [{"ID": 1, "Name": "SYS_READ_RECORD", "Caption": "系统已读未读表", "CreateDate": "2022/1/29 星期六 09:10:30", "OrderNo": 1, "GraphDesc": "Left=10.00\r\nTop=10.00\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 15, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "唯一ID", "DataType": 1, "KeyFieldType": 1, "RelateTable": "PM_DESIGN_INFO", "RelateField": "DESIGN_TYPE_ID", "DataLength": 50}, {"ID": 13, "Name": "THEMESID", "Memo": "战略组题ID", "OrderNo": 2, "DisplayName": "战略组题ID", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "FUNSIGNID", "Memo": "数据隔离ID，空值代表通用计划类型", "OrderNo": 3, "DisplayName": "功能标记ID（数据隔离）", "DataType": 1, "DataLength": 50}, {"ID": 22, "Name": "MODULECODE", "OrderNo": 4, "DisplayName": "模块编码", "DataType": 1, "DataTypeName": "<PERSON><PERSON><PERSON>", "DataLength": 50}, {"ID": 14, "Name": "OBJID", "OrderNo": 5, "DisplayName": "对象ID（读取的对象）", "DataType": 1, "DataLength": 200}, {"ID": 13, "Name": "TMISREAD", "OrderNo": 6, "DisplayName": "是否已读1：已读，0或null：未读", "DataType": 2}, {"ID": 17, "Name": "READTIME_CREATE", "OrderNo": 7, "DisplayName": "阅读时间（第一次）", "DataType": 4}, {"ID": 10, "Name": "READTIME_UPDATE", "OrderNo": 8, "DisplayName": "阅读时间（最后）", "DataType": 4}, {"ID": 9, "Name": "READ_COUNT", "OrderNo": 9, "DisplayName": "阅读次数", "DataType": 2}, {"ID": 14, "Name": "READ_PERSON_ID", "OrderNo": 10, "DisplayName": "读取人员ID", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "READ_PERSON_NAME", "OrderNo": 11, "DisplayName": "读取人员名称", "DataType": 1, "DataLength": 50}, {"ID": 23, "Name": "MEMO", "OrderNo": 12, "DisplayName": "备注信息", "DataType": 1, "DataLength": 3000}, {"ID": 15, "Name": "TMSHOW", "Memo": "控制可见性", "OrderNo": 13, "DisplayName": "是否使用", "DataType": 2}, {"ID": 9, "Name": "TMUSED", "OrderNo": 14, "DisplayName": "是否删除", "DataType": 2}, {"ID": 11, "Name": "TMSORT", "OrderNo": 15, "DisplayName": "排序", "DataType": 2}]}}]}}, {"ID": 16, "Name": "移动端", "CreateDate": "2022/8/15 星期一 13:56:47", "OrderNo": 15, "Tables": {"Count": 5, "items": [{"ID": 1, "Name": "MOBILE_WX_SEND", "Caption": "发送微信设置", "CreateDate": "2022/8/15 星期一 13:57:05", "OrderNo": 1, "GraphDesc": "Left=14.80\r\nTop=14.80\r\nBLeft=7.40\r\nBTop=7.40\r\n", "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 15, "Name": "NAME", "OrderNo": 2, "DisplayName": "名称", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "MEMO", "OrderNo": 3, "DisplayName": "备注", "DataType": 1, "DataLength": 100}, {"ID": 24, "Name": "MSGTITLE", "OrderNo": 4, "DisplayName": "标题", "DataType": 1, "DataLength": 500}, {"ID": 28, "Name": "MSGCONTENT", "OrderNo": 5, "DisplayName": "消息内容", "DataType": 1, "DataLength": 4000}, {"ID": 27, "Name": "MSGTYPE", "Memo": "消息显示类型：文本类型：text,卡片类型：textcard", "OrderNo": 6, "DisplayName": "消息显示类型", "DataType": 1, "DataLength": 10}, {"ID": 26, "Name": "SENDTYPE", "Memo": "发送消息类型：1：数据源，2：超链接", "OrderNo": 7, "DisplayName": "发送消息类型", "DataType": 2}, {"ID": 29, "Name": "ISLINKCALENDAR", "OrderNo": 8, "DisplayName": "是否关联日历", "DataType": 2}, {"ID": 23, "Name": "TDSALIAS", "Memo": "数据源别名 or 超链接地址", "OrderNo": 9, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 255}, {"ID": 30, "Name": "INPARAALIAS", "OrderNo": 10, "DisplayName": "数据源输入参数", "DataType": 1, "DataLength": 255}, {"ID": 31, "Name": "URLPARAMS", "OrderNo": 11, "DisplayName": "地址栏参数", "DataType": 1, "DataLength": 255}, {"ID": 32, "Name": "TDSZYID", "OrderNo": 12, "DataType": 1, "DataLength": 50}, {"ID": 33, "Name": "RECEPTIONTYPE", "Memo": "发送消息模式：1微信(默认)，2手机短信", "OrderNo": 13, "DisplayName": "发送消息模式", "DataType": 2, "DataLength": 50}, {"ID": 14, "Name": "TIMETEMPLET", "OrderNo": 14, "DisplayName": "发送周期", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "TIMEMEMO", "OrderNo": 15, "DisplayName": "周期描述", "DataType": 1, "DataLength": 100}, {"ID": 25, "Name": "TMUSED", "OrderNo": 16, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 2, "Name": "MOBILE_WX_SEND_OBJ", "Caption": "微信接收对象", "CreateDate": "2022/8/15 星期一 14:12:20", "OrderNo": 2, "GraphDesc": "Left=13.20\r\nTop=310.40\r\nBLeft=6.60\r\nBTop=155.20\r\n", "MetaFields": {"Count": 4, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "PID", "Memo": "MOBILE_WEIXIN_SEND.ID", "OrderNo": 2, "DisplayName": "父编号", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "OBJTYPE", "Memo": "1：分组；0：人员", "OrderNo": 3, "DisplayName": "接收对象类型", "DataType": 2}, {"ID": 13, "Name": "OBJID", "Memo": "分组id or 人员id", "OrderNo": 4, "DisplayName": "接收对象id", "DataType": 1, "DataLength": 50}]}}, {"ID": 3, "Name": "MOBILE_WX_SEND_GROUP", "Caption": "接收对象分组", "CreateDate": "2022/8/15 星期一 14:16:03", "OrderNo": 3, "GraphDesc": "Left=11.60\r\nTop=412.40\r\nBLeft=5.80\r\nBTop=206.20\r\n", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 2, "KeyFieldType": 1}, {"ID": 2, "Name": "NAME", "OrderNo": 2, "DisplayName": "小组名称", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "MEMO", "OrderNo": 3, "DisplayName": "备注", "DataType": 1, "DataLength": 255}, {"ID": 12, "Name": "TMUSED", "OrderNo": 4, "DisplayName": "是否使用", "DataType": 2}, {"ID": 5, "Name": "TMSORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}]}}, {"ID": 4, "Name": "MOBILE_WX_SEND_GROUP_EMP", "Caption": "接收小组明细", "CreateDate": "2022/8/15 星期一 14:19:11", "OrderNo": 4, "GraphDesc": "Left=10.00\r\nTop=529.60\r\nBLeft=5.00\r\nBTop=264.80\r\n", "MetaFields": {"Count": 3, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "主键", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "GROUPID", "OrderNo": 2, "DisplayName": "小组id", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "EMPID", "OrderNo": 3, "DisplayName": "人员id", "DataType": 1, "DataLength": 50}]}}, {"ID": 1, "Name": "MOBILE_MSG_URL", "Caption": "发送消息地址配置", "CreateDate": "2022/8/15 星期一 13:57:05", "OrderNo": 5, "GraphDesc": "Left=327.40\r\nTop=18.40\r\nBLeft=163.70\r\nBTop=9.20\r\n", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 24, "Name": "MODULECODE", "OrderNo": 2, "DisplayName": "模块编码", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "FUNNAME", "OrderNo": 3, "DisplayName": "功能名称", "DataType": 1, "DataLength": 100}, {"ID": 28, "Name": "FUNCODE", "OrderNo": 4, "DisplayName": "功能编码", "DataType": 1, "DataLength": 100}, {"ID": 7, "Name": "APPURL", "OrderNo": 5, "DisplayName": "移动端地址", "DataType": 1, "DataLength": 500}, {"ID": 7, "Name": "APPURL_PARAMS", "OrderNo": 6, "DisplayName": "移动端地址参数", "DataType": 1, "DataLength": 500}, {"ID": 10, "Name": "BATCH_APPURL", "OrderNo": 7, "DisplayName": "批量处理地址", "DataType": 1, "DataLength": 500}, {"ID": 11, "Name": "BATCH_USE", "OrderNo": 8, "DisplayName": "是否使用批量审批", "DataType": 2}, {"ID": 16, "Name": "MEMO", "OrderNo": 9, "DisplayName": "备注", "DataType": 1, "DataLength": 2000}, {"ID": 8, "Name": "TMSORT", "OrderNo": 10, "DisplayName": "排序", "DataType": 2}, {"ID": 25, "Name": "TMUSED", "OrderNo": 11, "DisplayName": "是否使用", "DataType": 2}]}}]}}, {"ID": 18, "Name": "盖章功能", "CreateDate": "2022/10/28 星期五 08:10:30", "OrderNo": 16, "Tables": {"Count": 2, "items": [{"ID": 1, "Name": "SIG_DATA", "Caption": "盖章数据表", "CreateDate": "2022/8/15 星期一 13:57:05", "OrderNo": 1, "GraphDesc": "Left=140.40\r\nTop=76.80\r\nBLeft=70.20\r\nBTop=38.40\r\n", "MetaFields": {"Count": 17, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 14, "Name": "DATA_ID", "Memo": "数据ID，用于业务功能的标识", "OrderNo": 2, "DisplayName": "数据ID", "DataType": 1, "DataLength": 100}, {"ID": 35, "Name": "COM_ID", "OrderNo": 3, "DisplayName": "组件ID", "DataType": 1, "DataLength": 100}, {"ID": 27, "Name": "SIG_OBJECT", "Memo": "签章对象：1：机构，2：人员", "OrderNo": 4, "DisplayName": "签章对象", "DataType": 2}, {"ID": 27, "Name": "SIG_TYPE", "Memo": "签章类型：1：电子印章，2：电子签名，3：手写电子签名", "OrderNo": 5, "DisplayName": "签章类型", "DataType": 2}, {"ID": 16, "Name": "SIG_PERSON_ID", "OrderNo": 6, "DisplayName": "签章人员ID", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "SIG_PERSON_NAME", "OrderNo": 7, "DisplayName": "签章人员姓名", "DataType": 1, "DataLength": 100}, {"ID": 33, "Name": "SIG_ORG_ID", "OrderNo": 8, "DisplayName": "签章机构ID", "DataType": 1, "DataLength": 100}, {"ID": 34, "Name": "SIG_ORG_NAME", "OrderNo": 9, "DisplayName": "签章机构名称", "DataType": 1, "DataLength": 100}, {"ID": 23, "Name": "SIG_IMAGE", "Memo": "签章图片ID", "OrderNo": 10, "DisplayName": "签章图片ID", "DataType": 1, "DataLength": 100}, {"ID": 30, "Name": "SIG_DATE", "Memo": "签章日期", "OrderNo": 11, "DisplayName": "签章日期", "DataType": 4}, {"ID": 16, "Name": "PERSON_ID", "OrderNo": 12, "DisplayName": "登录人员ID", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "PERSON_NAME", "OrderNo": 13, "DisplayName": "登录人员姓名", "DataType": 1, "DataLength": 100}, {"ID": 24, "Name": "PERSON_MY_ORG_NAME", "OrderNo": 14, "DisplayName": "登录人员所在机构名称", "DataType": 1, "DataLength": 100}, {"ID": 28, "Name": "PERSON_MY_ORG_ID", "OrderNo": 15, "DisplayName": "登录人员所在机构ID", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "TMSORT", "OrderNo": 16, "DisplayName": "签章排序", "DataType": 2}, {"ID": 25, "Name": "TMUSED", "OrderNo": 17, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 1, "Name": "SIG_OUT_DATA_SYNC", "Caption": "外部系统盖章数据对照表", "CreateDate": "2022/8/15 星期一 13:57:05", "OrderNo": 2, "GraphDesc": "Left=555.40\r\nTop=87.40\r\nBLeft=0.00\r\nBTop=0.00\r\n", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 14, "Name": "DATA_ID", "Memo": "数据ID，用于业务功能的标识", "OrderNo": 2, "DisplayName": "数据ID", "DataType": 1, "DataLength": 100}, {"ID": 27, "Name": "SIG_OBJECT", "Memo": "签章对象：1：机构，2：人员", "OrderNo": 3, "DisplayName": "签章对象", "DataType": 2}, {"ID": 27, "Name": "SIG_TYPE", "Memo": "签章类型：1：电子印章，2：电子签名，3：手写电子签名", "OrderNo": 4, "DisplayName": "签章类型", "DataType": 2}, {"ID": 16, "Name": "DATA_CODE", "OrderNo": 5, "DisplayName": "对照代码", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "PHOTO_URL", "OrderNo": 6, "DisplayName": "第三方图片路径", "DataType": 1, "DataLength": 100}]}}]}}]}