{"RootName": "DataModels", "CTVER": "********", "TableCount": 27, "CreateDate": "2025/3/19 15:54:26", "ModifyDate": "2025/3/19 15:54:26", "Count": 1, "items": [{"ID": 7, "Name": "台账数据源", "CreateDate": "2023/8/30 15:21:52", "OrderNo": 1, "CustomAttr1": "DVS:-396.20,1743.00,1.25,0,", "Tables": {"Count": 27, "items": [{"ID": 1, "Name": "TDS_ACCOUNT_CONF", "Caption": "台账信息表", "CreateDate": "2023/8/30 15:22:45", "OrderNo": 1, "GraphDesc": "Left=31.20\r\nTop=40.40\r\nBLeft=15.60\r\nBTop=20.20", "MetaFields": {"Count": 20, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "METER_BIT", "OrderNo": 3, "DisplayName": "仪表精度", "DataType": 2}, {"ID": 4, "Name": "TIME_BOUND", "OrderNo": 4, "DisplayName": "时间点获取范围", "DataType": 2}, {"ID": 5, "Name": "UPDATE_BG_COLOR", "OrderNo": 5, "DisplayName": "修改后背景色", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "SHOW_RE_EXTRACT", "OrderNo": 6, "DisplayName": "重新提取", "DataType": 2}, {"ID": 7, "Name": "OUTPARAM_MODE", "OrderNo": 7, "DisplayName": "输出参数模式1仪表2时间", "DataType": 2}, {"ID": 8, "Name": "EDIT_MARK", "OrderNo": 8, "DisplayName": "可编辑", "DataType": 2}, {"ID": 10, "Name": "DYNAMIC_TAG_MARK", "OrderNo": 9, "DisplayName": "是否指定动态仪表范围", "DataType": 2}, {"ID": 9, "Name": "DYNAMIC_TAG_ROUND", "OrderNo": 10, "DisplayName": "指定动态仪表范围", "DataType": 1, "DataLength": 200}, {"ID": 11, "Name": "TAG_TITLE_SHOW_MODE", "OrderNo": 11, "DisplayName": "仪表多表头显示模式，启用符合表头模式时有效", "DataType": 1, "DataLength": 200}, {"ID": 12, "Name": "OVER_LIMIT_COLOR", "OrderNo": 12, "DisplayName": "超限颜色", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "WARNING_1_COLOR", "OrderNo": 13, "DisplayName": "预警颜色", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "WARNING_2_COLOR", "OrderNo": 14, "DisplayName": "二级预警颜色", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "WARNING_3_COLOR", "OrderNo": 15, "DisplayName": "三级预警颜色", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "CONFIRM_ROUND", "OrderNo": 16, "DisplayName": "确认时限范围", "DataType": 2}, {"ID": 17, "Name": "AUTO_REFRESH_SEC", "OrderNo": 17, "DisplayName": "自动刷新时间", "DataType": 2}, {"ID": 18, "Name": "BC_DIFF", "OrderNo": 18, "DisplayName": "班次偏差时间(分钟)", "DataType": 2}, {"ID": 20, "Name": "BC_INPUT_EXT", "OrderNo": 19, "DisplayName": "班次延时可录入时间（分钟）", "DataType": 2}, {"ID": 19, "Name": "TAG_TYPE", "OrderNo": 20, "DisplayName": "仪表显示类型2平稳率3lims0全部", "DataType": 1, "DataLength": 50}]}}, {"ID": 3, "Name": "TDS_ACCOUNT_PARAM", "Caption": "台账参数绑定表", "CreateDate": "2023/8/30 15:31:35", "OrderNo": 2, "GraphDesc": "Left=1141.80\r\nTop=34.60\r\nBLeft=570.90\r\nBTop=17.30", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "OLD_PARAM_ID", "OrderNo": 3, "DisplayName": "原参数标识", "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "OLD_PARAM_ALIAS", "OrderNo": 4, "DisplayName": "原参数别名", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "NEW_PARAM_ID", "OrderNo": 5, "DisplayName": "新参数标识", "DataType": 1, "DataLength": 50}]}}, {"ID": 4, "Name": "TDS_ACCOUNT_METER", "Caption": "台账仪表配置表", "CreateDate": "2023/8/30 15:57:42", "OrderNo": 3, "GraphDesc": "Left=677.20\r\nTop=39.00\r\nBLeft=338.60\r\nBTop=19.50", "MetaFields": {"Count": 25, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 24, "Name": "VERMARK", "OrderNo": 2, "DisplayName": "日期版本标识", "DataType": 1, "DataLength": 50}, {"ID": 23, "Name": "RQ", "OrderNo": 3, "DisplayName": "日期", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 4, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "CONF_TYPE", "OrderNo": 5, "DisplayName": "设置类型1班组2核算单元", "DataType": 2}, {"ID": 14, "Name": "CONF_CODE", "OrderNo": 6, "DisplayName": "单元代码班组代码或核算单元代码", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "UNIT_CODE", "OrderNo": 7, "DisplayName": "核算单元代码", "DataType": 1, "DataLength": 100}, {"ID": 20, "Name": "BELONG_ZONE", "OrderNo": 8, "DisplayName": "所属区域", "DataType": 1, "DataLength": 100}, {"ID": 21, "Name": "BELONG_DEV", "OrderNo": 9, "DisplayName": "所属设备", "DataType": 1, "DataLength": 100}, {"ID": 5, "Name": "SHOW_NAME", "OrderNo": 10, "DisplayName": "显示名称", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "TAGNUMBER", "OrderNo": 11, "DisplayName": "仪表位号", "DataType": 1, "DataLength": 200}, {"ID": 18, "Name": "SDUNIT", "OrderNo": 12, "DisplayName": "单位", "DataType": 1, "DataLength": 100}, {"ID": 19, "Name": "DECIMAL_DEGIT", "OrderNo": 13, "DisplayName": "小数位数", "DataType": 2}, {"ID": 17, "Name": "DATASOURCE", "OrderNo": 14, "DisplayName": "数据来源", "DataType": 1, "DataLength": 1000}, {"ID": 25, "Name": "TAG_TYPE", "OrderNo": 15, "DisplayName": "仪表类型", "DataType": 2}, {"ID": 16, "Name": "TAGID", "OrderNo": 16, "DisplayName": "仪表标识", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "ALIGN", "OrderNo": 17, "DisplayName": "对齐方式", "DataType": 1, "DataLength": 10}, {"ID": 22, "Name": "EDIT_MARK", "OrderNo": 18, "DisplayName": "可编辑标识，默认不可编辑", "DataType": 2}, {"ID": 13, "Name": "WIDTH", "OrderNo": 19, "DisplayName": "宽度", "DataType": 2}, {"ID": 7, "Name": "UP_LIMIT", "OrderNo": 20, "DisplayName": "上限（上下限跟随方案走）", "DataType": 3}, {"ID": 8, "Name": "UP_LIMIT_COLOR", "OrderNo": 21, "DisplayName": "超上限颜色", "DataType": 1, "DataLength": 50}, {"ID": 6, "Name": "LOWER_LIMIT", "OrderNo": 22, "DisplayName": "下限（上下限跟随方案走）", "DataType": 3}, {"ID": 9, "Name": "LOWER_LIMIT_COLOR", "OrderNo": 23, "DisplayName": "超下限颜色", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "TMSORT", "OrderNo": 24, "DataType": 2}, {"ID": 11, "Name": "TMUSED", "OrderNo": 25, "DataType": 2}]}}, {"ID": 5, "Name": "TDS_ACCOUNT_OUTPARAM", "Caption": "台账输出配置表", "CreateDate": "2023/8/30 16:41:40", "OrderNo": 4, "GraphDesc": "Left=4.00\r\nTop=487.40\r\nBLeft=2.00\r\nBTop=243.70", "MetaFields": {"Count": 31, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "MODEMARK", "OrderNo": 3, "DisplayName": "模式", "DataType": 2}, {"ID": 13, "Name": "DYNAMIC_MARK", "OrderNo": 4, "DisplayName": "动态标识", "DataType": 2}, {"ID": 15, "Name": "SHOW_NAME", "OrderNo": 5, "DisplayName": "显示名称", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "ALIAS", "OrderNo": 6, "DisplayName": "别名", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "ALIGN", "OrderNo": 7, "DisplayName": "对齐方式", "DataType": 1, "DataLength": 10}, {"ID": 17, "Name": "WIDTH", "OrderNo": 8, "DisplayName": "宽度", "DataType": 2}, {"ID": 18, "Name": "VISIBLE", "OrderNo": 9, "DisplayName": "可见", "DataType": 2}, {"ID": 19, "Name": "EDIT_MARK", "OrderNo": 10, "DisplayName": "可编辑，默认不可编辑", "DataType": 2}, {"ID": 20, "Name": "BIND_METER", "OrderNo": 11, "DisplayName": "绑定仪表", "DataType": 1, "DataLength": 100}, {"ID": 21, "Name": "BIND_TIME", "OrderNo": 12, "DisplayName": "绑定时间", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "BIND_DAY", "OrderNo": 13, "DisplayName": "绑定日期", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "DAY_FIXED", "OrderNo": 14, "DisplayName": "日期浮动", "DataType": 2}, {"ID": 16, "Name": "EDIT_FIXED", "OrderNo": 15, "DisplayName": "编辑浮动", "DataType": 2}, {"ID": 17, "Name": "METER_MARK", "OrderNo": 16, "DisplayName": "仪表列", "DataType": 2}, {"ID": 18, "Name": "TIME_MARK", "OrderNo": 17, "DisplayName": "时间列", "DataType": 2}, {"ID": 28, "Name": "TAGNUMBER", "OrderNo": 18, "DisplayName": "仪表位号", "DataType": 1, "DataLength": 200}, {"ID": 22, "Name": "SDUNIT", "OrderNo": 19, "DisplayName": "单位", "DataType": 1, "DataLength": 100}, {"ID": 23, "Name": "DECIMAL_DEGIT", "OrderNo": 20, "DisplayName": "小数位数", "DataType": 2}, {"ID": 26, "Name": "UP_LIMIT", "OrderNo": 21, "DisplayName": "上限（上下限跟随方案走）", "DataType": 3}, {"ID": 27, "Name": "UP_LIMIT_COLOR", "OrderNo": 22, "DisplayName": "超上限颜色", "DataType": 1, "DataLength": 50}, {"ID": 28, "Name": "LOWER_LIMIT", "OrderNo": 23, "DisplayName": "下限（上下限跟随方案走）", "DataType": 3}, {"ID": 29, "Name": "LOWER_LIMIT_COLOR", "OrderNo": 24, "DisplayName": "超下限颜色", "DataType": 1, "DataLength": 50}, {"ID": 29, "Name": "UP_CRITICAL", "OrderNo": 25, "DisplayName": "自定义临界上限", "DataType": 3}, {"ID": 30, "Name": "LOWER_CRITICAL", "OrderNo": 26, "DisplayName": "自定义临界下限", "DataType": 3}, {"ID": 20, "Name": "COL_TYPE", "OrderNo": 27, "DisplayName": "列类型，当前人员user，机构org，当前时间time，自定义custom", "DataType": 1, "DataLength": 200}, {"ID": 21, "Name": "COM_TYPE", "OrderNo": 28, "DisplayName": "组件类型", "DataType": 1, "DataLength": 100}, {"ID": 31, "Name": "FIXED", "OrderNo": 29, "DisplayName": "锁定位置", "DataType": 1, "DataLength": 50}, {"ID": 22, "Name": "TMSORT", "OrderNo": 30, "DataType": 2}, {"ID": 23, "Name": "TMUSED", "OrderNo": 31, "DataType": 2}]}}, {"ID": 6, "Name": "TDS_ACCOUNT_TIME", "Caption": "台账时间配置表", "CreateDate": "2023/8/30 17:14:24", "OrderNo": 5, "GraphDesc": "Left=812.60\r\nTop=483.00\r\nBLeft=406.30\r\nBTop=241.50", "MetaFields": {"Count": 17, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "START_BING_DAY", "OrderNo": 3, "DisplayName": "开始日期绑定", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "START_FIXED", "OrderNo": 4, "DisplayName": "开始日期浮动", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "START_BING_TIME", "OrderNo": 5, "DisplayName": "开始绑定时间", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "START_ROUND", "OrderNo": 6, "DisplayName": "包括开始时间点", "DataType": 2}, {"ID": 14, "Name": "END_BING_DAY", "OrderNo": 7, "DisplayName": "截止日期绑定", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "END_FIXED", "OrderNo": 8, "DisplayName": "截止日期浮动", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "END_BING_TIME", "OrderNo": 9, "DisplayName": "截止绑定时间", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "END_ROUND", "OrderNo": 10, "DisplayName": "包括截止时间点", "DataType": 2}, {"ID": 18, "Name": "TIME_POINT", "OrderNo": 11, "DisplayName": "取整点时间", "DataType": 2}, {"ID": 19, "Name": "TIME_FORMAT", "OrderNo": 12, "DisplayName": "时间显示格式", "DataType": 1}, {"ID": 20, "Name": "TIME_STEP", "OrderNo": 13, "DisplayName": "取时间间隔（分钟）", "DataType": 1}, {"ID": 21, "Name": "EDIT_ROUND", "OrderNo": 14, "DisplayName": "可编辑浮动范围", "DataType": 2}, {"ID": 17, "Name": "SHOW_MODE", "OrderNo": 15, "DisplayName": "时间显示模式，默认显示时间点，bound时间范围", "DataType": 1, "DataLength": 50}, {"ID": 22, "Name": "TMSORT", "OrderNo": 16, "DataType": 2}, {"ID": 23, "Name": "TMUSED", "OrderNo": 17, "DataType": 2}]}}, {"ID": 6, "Name": "TDS_ACCOUNT_DATA_BAK", "Caption": "台账数据表(弃用)", "CreateDate": "2023/8/31 8:16:59", "OrderNo": 6, "GraphDesc": "Left=1435.60\r\nTop=939.60\r\nBLeft=717.80\r\nBTop=469.80", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "PARAM_DAY", "OrderNo": 3, "DisplayName": "日期条件", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "PARAM_BZ", "OrderNo": 4, "DisplayName": "班组", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "PARAM_BC", "OrderNo": 5, "DisplayName": "班次", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "FORMID", "OrderNo": 6, "DisplayName": "表单模板ID", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "DATAID", "OrderNo": 7, "DisplayName": "数据ID", "DataType": 1, "DataLength": 100}, {"ID": 22, "Name": "TMSORT", "OrderNo": 8, "DataType": 2}, {"ID": 23, "Name": "TMUSED", "OrderNo": 9, "DataType": 2}]}}, {"ID": 7, "Name": "TDS_ACCOUNT_DATA", "Caption": "台账使用数据表", "CreateDate": "2023/10/22 9:49:55", "OrderNo": 7, "GraphDesc": "Left=860.60\r\nTop=864.40\r\nBLeft=430.30\r\nBTop=432.20", "MetaFields": {"Count": 22, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "RQ", "OrderNo": 3, "DisplayName": "日期", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "WRITEDAY", "OrderNo": 4, "DisplayName": "填写日期", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "ORGCODE", "OrderNo": 5, "DisplayName": "机构", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "ORGNAME", "OrderNo": 6, "DisplayName": "机构名称", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "CLSNO", "OrderNo": 7, "DisplayName": "班次", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "CLSNAME", "OrderNo": 8, "DisplayName": "班次名称", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "UNITCODE", "OrderNo": 9, "DisplayName": "核算对象代码，可能有多个", "DataType": 1, "DataLength": 1000}, {"ID": 18, "Name": "UNITNAME", "OrderNo": 10, "DisplayName": "核算对象名称", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "SBSJ", "OrderNo": 11, "DisplayName": "上班时间", "DataType": 4}, {"ID": 16, "Name": "XBSJ", "OrderNo": 12, "DisplayName": "下班时间", "DataType": 4}, {"ID": 17, "Name": "SBSJSTR", "OrderNo": 13, "DisplayName": "上班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 18, "Name": "XBSJSTR", "OrderNo": 14, "DisplayName": "下班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 19, "Name": "FORMID", "OrderNo": 15, "DisplayName": "表单标识", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "FORMNAME", "OrderNo": 16, "DisplayName": "表单名称", "DataType": 1, "DataLength": 100}, {"ID": 22, "Name": "CUSTOMFORMCODE", "OrderNo": 17, "DisplayName": "自定义表单标识", "DataType": 1, "DataLength": 50}, {"ID": 23, "Name": "CUSTOMFORMNAME", "OrderNo": 18, "DisplayName": "自定义表单名称", "DataType": 1, "DataLength": 50}, {"ID": 20, "Name": "DATAID", "OrderNo": 19, "DisplayName": "数据标识", "DataType": 1, "DataLength": 50}, {"ID": 24, "Name": "EXPORT_PDF_ID", "OrderNo": 20, "DisplayName": "生成的pdf文件ID", "DataType": 1, "DataLength": 50}, {"ID": 39, "Name": "TMSORT", "OrderNo": 21, "DataType": 2}, {"ID": 40, "Name": "TMUSED", "OrderNo": 22, "DataType": 2}]}}, {"ID": 8, "Name": "TDS_ACCOUNT_TAG_VERSION", "Caption": "台账数据源仪表版本信息", "CreateDate": "2023/10/28 9:56:49", "OrderNo": 8, "GraphDesc": "Left=17.80\r\nTop=1091.40\r\nBLeft=8.90\r\nBTop=545.70", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "RQ", "OrderNo": 2, "DisplayName": "日期版本", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "OCCU_TIME", "OrderNo": 3, "DisplayName": "发生时间", "DataType": 4}, {"ID": 3, "Name": "UNITCODE", "OrderNo": 4, "DisplayName": "核算对象", "DataType": 1, "DataLength": 50}, {"ID": 6, "Name": "BAK1", "OrderNo": 5, "DisplayName": "备用字段1", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "BAK2", "OrderNo": 6, "DisplayName": "备用字段2", "DataType": 1, "DataLength": 50}, {"ID": 8, "Name": "BAK3", "OrderNo": 7, "DisplayName": "备用字段3", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "TMSORT", "OrderNo": 8, "DataType": 2}, {"ID": 5, "Name": "TMUSED", "OrderNo": 9, "DataType": 2}]}}, {"ID": 9, "Name": "TDS_ACCOUNT_TAG", "Caption": "台账仪表同步表", "CreateDate": "2023/10/28 10:01:10", "OrderNo": 9, "GraphDesc": "Left=371.40\r\nTop=1091.40\r\nBLeft=185.70\r\nBTop=545.70", "MetaFields": {"Count": 49, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "VERMARK", "OrderNo": 2, "DisplayName": "日期版本标识", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "RQ", "OrderNo": 3, "DisplayName": "日期", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "UNIT_CODE", "OrderNo": 4, "DisplayName": "核算单元代码", "DataType": 1, "DataLength": 100}, {"ID": 22, "Name": "UNIT_NAME", "OrderNo": 5, "DisplayName": "核算单元名称", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "BELONG_ZONE", "OrderNo": 6, "DisplayName": "所属区域", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "BELONG_DEV", "OrderNo": 7, "DisplayName": "所属设备", "DataType": 1, "DataLength": 100}, {"ID": 33, "Name": "BELONG_TAG", "OrderNo": 8, "DisplayName": "所属名称", "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "TAGNAME", "OrderNo": 9, "DisplayName": "显示名称", "DataType": 1, "DataLength": 100}, {"ID": 19, "Name": "TAGNUMBER", "OrderNo": 10, "DisplayName": "仪表位号", "DataType": 1, "DataLength": 200}, {"ID": 20, "Name": "SDUNIT", "OrderNo": 11, "DisplayName": "单位", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "DECIMAL_DEGIT", "OrderNo": 12, "DisplayName": "小数位数", "DataType": 2}, {"ID": 27, "Name": "UP_LIMIT", "OrderNo": 13, "DisplayName": "上限", "DataType": 3}, {"ID": 29, "Name": "LOWER_LIMIT", "OrderNo": 14, "DisplayName": "下限", "DataType": 3}, {"ID": 22, "Name": "DATASOURCE", "OrderNo": 15, "DisplayName": "数据来源", "DataType": 1, "DataLength": 100}, {"ID": 19, "Name": "SOURCEYPE", "OrderNo": 16, "DisplayName": "采集类型", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "CTYPE", "OrderNo": 17, "DisplayName": "仪表类型2控制指标3lims指标", "DataType": 1, "DataLength": 50}, {"ID": 23, "Name": "TAGID", "OrderNo": 18, "DisplayName": "仪表标识", "DataType": 1, "DataLength": 100}, {"ID": 28, "Name": "UP_WARNING", "OrderNo": 19, "DisplayName": "临界上限", "DataType": 3}, {"ID": 30, "Name": "LOWER_WARNING", "OrderNo": 20, "DisplayName": "临界下限", "DataType": 3}, {"ID": 20, "Name": "WIDTH", "OrderNo": 21, "DisplayName": "宽度（根据上下限位数自动计算）", "DataType": 2}, {"ID": 23, "Name": "CLASS1", "OrderNo": 22, "DisplayName": "1级分类", "DataType": 1, "DataLength": 100}, {"ID": 24, "Name": "CLASS2", "OrderNo": 23, "DisplayName": "2级分类", "DataType": 1, "DataLength": 100}, {"ID": 25, "Name": "CLASS3", "OrderNo": 24, "DisplayName": "3级分类", "DataType": 1, "DataLength": 100}, {"ID": 27, "Name": "CLASS5", "OrderNo": 25, "DisplayName": "4级分类", "DataType": 1, "DataLength": 100}, {"ID": 28, "Name": "CLASS6", "OrderNo": 26, "DisplayName": "5级分类", "DataType": 1, "DataLength": 100}, {"ID": 26, "Name": "CLASS4", "OrderNo": 27, "DisplayName": "6级分类", "DataType": 1, "DataLength": 100}, {"ID": 29, "Name": "CLASS7", "OrderNo": 28, "DisplayName": "7级分类", "DataType": 1, "DataLength": 100}, {"ID": 30, "Name": "CLASS8", "OrderNo": 29, "DisplayName": "8级分类", "DataType": 1, "DataLength": 100}, {"ID": 31, "Name": "CLASS9", "OrderNo": 30, "DisplayName": "9级分类", "DataType": 1, "DataLength": 100}, {"ID": 32, "Name": "CLASS10", "OrderNo": 31, "DisplayName": "10级分类ID", "DataType": 1, "DataLength": 100}, {"ID": 39, "Name": "CLASS1ID", "OrderNo": 32, "DisplayName": "1级分类ID", "DataType": 1, "DataLength": 50}, {"ID": 40, "Name": "CLASS2ID", "OrderNo": 33, "DisplayName": "2级分类ID", "DataType": 1, "DataLength": 50}, {"ID": 41, "Name": "CLASS3ID", "OrderNo": 34, "DisplayName": "3级分类ID", "DataType": 1, "DataLength": 50}, {"ID": 42, "Name": "CLASS5ID", "OrderNo": 35, "DisplayName": "4级分类ID", "DataType": 1, "DataLength": 50}, {"ID": 43, "Name": "CLASS6ID", "OrderNo": 36, "DisplayName": "5级分类ID", "DataType": 1, "DataLength": 50}, {"ID": 44, "Name": "CLASS4ID", "OrderNo": 37, "DisplayName": "6级分类ID", "DataType": 1, "DataLength": 50}, {"ID": 45, "Name": "CLASS7ID", "OrderNo": 38, "DisplayName": "7级分类ID", "DataType": 1, "DataLength": 50}, {"ID": 46, "Name": "CLASS8ID", "OrderNo": 39, "DisplayName": "8级分类ID", "DataType": 1, "DataLength": 50}, {"ID": 47, "Name": "CLASS9ID", "OrderNo": 40, "DisplayName": "9级分类ID", "DataType": 1, "DataLength": 50}, {"ID": 48, "Name": "CLASS10ID", "OrderNo": 41, "DisplayName": "10级分类ID", "DataType": 1, "DataLength": 50}, {"ID": 31, "Name": "TMSORT", "OrderNo": 42, "DataType": 2}, {"ID": 32, "Name": "TMUSED", "OrderNo": 43, "DataType": 2}, {"ID": 34, "Name": "PROCESSUNITNAME", "OrderNo": 44, "DisplayName": "装置名称", "DataType": 1, "DataLength": 200}, {"ID": 36, "Name": "PRODUCTNAME", "OrderNo": 45, "DisplayName": "产品名称", "DataType": 1, "DataLength": 200}, {"ID": 37, "Name": "SAMPLINGPOINT", "OrderNo": 46, "DisplayName": "采样点", "DataType": 1, "DataLength": 200}, {"ID": 35, "Name": "ANALYSISNAME", "OrderNo": 47, "DisplayName": "分析名称", "DataType": 1, "DataLength": 200}, {"ID": 38, "Name": "ANALYSISSUBNAME", "OrderNo": 48, "DisplayName": "分析分项名称", "DataType": 1, "DataLength": 200}, {"ID": 49, "Name": "CUSTOMCODE", "OrderNo": 49, "DisplayName": "应用自定义台账编码", "DataType": 1, "DataLength": 50}]}}, {"ID": 11, "Name": "TDS_ACCOUNT_APPLY_CONF", "Caption": "台账应用配置表", "Memo": "参数表", "CreateDate": "2023/11/8 13:19:28", "OrderNo": 10, "GraphDesc": "Left=27.60\r\nTop=1500.00\r\nBLeft=13.80\r\nBTop=750.00", "MetaFields": {"Count": 12, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "APPLY_MODE", "OrderNo": 2, "DisplayName": "应用模型account", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "APPLY_ORG", "OrderNo": 3, "DisplayName": "应用机构默认空", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "APPLY_NAME", "OrderNo": 4, "DisplayName": "参数名称", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "APPLY_ALIAS", "OrderNo": 5, "DisplayName": "参数别名", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "APPLY_VALUE", "OrderNo": 6, "DisplayName": "参数值", "DataType": 1, "DataLength": 200}, {"ID": 17, "Name": "APPLY_DESC", "OrderNo": 7, "DisplayName": "参数描述", "DataType": 1, "DataLength": 2000}, {"ID": 20, "Name": "APPLY_COM", "OrderNo": 8, "DisplayName": "参数设置组件", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "APPLY_KEYS", "OrderNo": 9, "DisplayName": "备选值内容", "DataType": 1, "DataLength": 2000}, {"ID": 22, "Name": "APPLY_VALUES", "OrderNo": 10, "DisplayName": "备选文本内容", "DataType": 1, "DataLength": 2000}, {"ID": 18, "Name": "TMSORT", "OrderNo": 11, "DisplayName": "排序", "DataType": 2}, {"ID": 19, "Name": "TMUSED", "OrderNo": 12, "DisplayName": "使用", "DataType": 2}]}}, {"ID": 11, "Name": "TDS_ACCOUNT_INFO", "Caption": "台账数据源保存数据主表", "CreateDate": "2023/11/16 15:55:05", "OrderNo": 11, "GraphDesc": "Left=900.60\r\nTop=1294.00\r\nBLeft=450.30\r\nBTop=647.00", "MetaFields": {"Count": 18, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "RQ", "OrderNo": 3, "DisplayName": "日期", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "ORGCODE", "OrderNo": 4, "DisplayName": "机构", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "ORGNAME", "OrderNo": 5, "DisplayName": "机构名称", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "CLSNO", "OrderNo": 6, "DisplayName": "班次", "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "CLSNAME", "OrderNo": 7, "DisplayName": "班次名称", "DataType": 1, "DataLength": 100}, {"ID": 19, "Name": "UNITCODE", "OrderNo": 8, "DisplayName": "核算对象代码", "DataType": 1, "DataLength": 100}, {"ID": 20, "Name": "UNITNAME", "OrderNo": 9, "DisplayName": "核算对象名称", "DataType": 1, "DataLength": 100}, {"ID": 23, "Name": "SBSJSTR", "OrderNo": 10, "DisplayName": "上班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 24, "Name": "XBSJSTR", "OrderNo": 11, "DisplayName": "下班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 25, "Name": "ACTSBSJSTR", "OrderNo": 12, "DisplayName": "实际上班时间", "DataType": 1, "DataLength": 30}, {"ID": 26, "Name": "ACTXBSJSTR", "OrderNo": 13, "DisplayName": "实际下班时间", "DataType": 1, "DataLength": 30}, {"ID": 30, "Name": "TIMESTEP", "OrderNo": 14, "DisplayName": "时间间隔（分）", "DataType": 2}, {"ID": 31, "Name": "TIMEBOUND", "OrderNo": 15, "DisplayName": "包含关系10包括开始，01包括结束，11包括两个时间点", "DataType": 1, "DataLength": 10}, {"ID": 27, "Name": "JOBID", "OrderNo": 16, "DisplayName": "岗位标识", "DataType": 1, "DataLength": 50}, {"ID": 28, "Name": "TMSORT", "OrderNo": 17, "DataType": 2}, {"ID": 29, "Name": "TMUSED", "OrderNo": 18, "DataType": 2}]}}, {"ID": 12, "Name": "TDS_ACCOUNT_FORM", "Caption": "台账自定义表单数据主表", "CreateDate": "2023/11/17 16:18:40", "OrderNo": 12, "GraphDesc": "Left=44.00\r\nTop=1930.60\r\nBLeft=22.00\r\nBTop=965.30", "MetaFields": {"Count": 10, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "TYPE_CODE", "OrderNo": 2, "DisplayName": "类别标识0默认 1自定义", "DataType": 2}, {"ID": 14, "Name": "ACCOUNT_NAME", "OrderNo": 3, "DisplayName": "台账名称", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "FORM_CODE", "OrderNo": 4, "DisplayName": "表单代码", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "FORM_NAME", "OrderNo": 5, "DisplayName": "表单名称", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "UNIT_CODE", "OrderNo": 6, "DisplayName": "核算单元代码", "DataType": 1, "DataLength": 500}, {"ID": 19, "Name": "UNIT_NAME", "OrderNo": 7, "DisplayName": "核算对象名称", "DataType": 1, "DataLength": 500}, {"ID": 11, "Name": "MANAGE_ROUND", "OrderNo": 8, "DisplayName": "管理范围名称", "DataType": 1, "DataLength": 2000}, {"ID": 27, "Name": "TMSORT", "OrderNo": 9, "DataType": 2}, {"ID": 28, "Name": "TMUSED", "OrderNo": 10, "DataType": 2}]}}, {"ID": 13, "Name": "TDS_ACCOUNT_FORM_MANAGE", "Caption": "台账表单管理范围表", "CreateDate": "2023/11/17 16:26:03", "OrderNo": 13, "GraphDesc": "Left=419.80\r\nTop=1937.80\r\nBLeft=209.90\r\nBTop=968.90", "MetaFields": {"Count": 13, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 14, "Name": "MODE_TYPE", "OrderNo": 2, "DisplayName": "类型1人2岗3机构4工位", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "ACCOUNTID", "OrderNo": 3, "DisplayName": "台账id", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "MANAGE_CODE", "OrderNo": 4, "DisplayName": "管理编码", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "MANAGE_NAME", "OrderNo": 5, "DisplayName": "管理名", "DataType": 1, "DataLength": 100}, {"ID": 8, "Name": "ORG_CODE", "OrderNo": 6, "DisplayName": "机构代码，用于机构岗位记录", "DataType": 1, "DataLength": 100}, {"ID": 9, "Name": "POST_CODE", "OrderNo": 7, "DisplayName": "岗位代码，用于机构岗位记录", "DataType": 1, "DataLength": 100}, {"ID": 10, "Name": "ADD_MARK", "OrderNo": 8, "DisplayName": "录入标识", "DataType": 2}, {"ID": 11, "Name": "MANAGE_MARK", "OrderNo": 9, "DisplayName": "补录标识", "DataType": 2}, {"ID": 12, "Name": "SEARCH_MARK", "OrderNo": 10, "DisplayName": "查询标识", "DataType": 2}, {"ID": 13, "Name": "MANAGE_BOUND", "OrderNo": 11, "DisplayName": "管理范围1本机构2本车间", "DataType": 1, "DataLength": 20}, {"ID": 18, "Name": "TMSORT", "OrderNo": 12, "DataType": 2}, {"ID": 19, "Name": "TMUSED", "OrderNo": 13, "DataType": 2}]}}, {"ID": 15, "Name": "TDS_ACCOUNT_FORM_METER", "Caption": "台账表单仪表配置表", "CreateDate": "2023/11/17 16:29:52", "OrderNo": 14, "GraphDesc": "Left=910.20\r\nTop=1801.80\r\nBLeft=455.10\r\nBTop=900.90", "MetaFields": {"Count": 43, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 34, "Name": "TYPE_CODE", "OrderNo": 2, "DisplayName": "类型1仪表2设备", "DataType": 2}, {"ID": 35, "Name": "DEV_CODE", "OrderNo": 3, "DisplayName": "设备编码", "DataType": 1, "DataLength": 50}, {"ID": 36, "Name": "DEV_NAME", "OrderNo": 4, "DisplayName": "设备名称", "DataType": 1, "DataLength": 50}, {"ID": 38, "Name": "ACCOUNTID", "OrderNo": 5, "DisplayName": "台账id", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "VERMARK", "OrderNo": 6, "DisplayName": "日期版本标识", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "RQ", "OrderNo": 7, "DisplayName": "日期", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "TDSALIAS", "OrderNo": 8, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "CONF_TYPE", "OrderNo": 9, "DisplayName": "设置类型1班组2核算单元", "DataType": 2}, {"ID": 16, "Name": "CONF_CODE", "OrderNo": 10, "DisplayName": "单元代码班组代码或核算单元代码", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "UNIT_CODE", "OrderNo": 11, "DisplayName": "核算单元代码", "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "BELONG_ZONE", "OrderNo": 12, "DisplayName": "所属区域", "DataType": 1, "DataLength": 100}, {"ID": 19, "Name": "BELONG_DEV", "OrderNo": 13, "DisplayName": "所属设备", "DataType": 1, "DataLength": 100}, {"ID": 43, "Name": "BELONG_TAG", "OrderNo": 14, "DisplayName": "所属名称", "DataType": 1, "DataLength": 100}, {"ID": 20, "Name": "SHOW_NAME", "OrderNo": 15, "DisplayName": "显示名称", "DataType": 1, "DataLength": 100}, {"ID": 21, "Name": "TAGNUMBER", "OrderNo": 16, "DisplayName": "仪表位号", "DataType": 1, "DataLength": 200}, {"ID": 22, "Name": "SDUNIT", "OrderNo": 17, "DisplayName": "单位", "DataType": 1, "DataLength": 100}, {"ID": 23, "Name": "DECIMAL_DEGIT", "OrderNo": 18, "DisplayName": "小数位数", "DataType": 2}, {"ID": 24, "Name": "DATASOURCE", "OrderNo": 19, "DisplayName": "数据来源", "DataType": 1, "DataLength": 1000}, {"ID": 25, "Name": "TAG_TYPE", "OrderNo": 20, "DisplayName": "仪表类型", "DataType": 2}, {"ID": 26, "Name": "TAGID", "OrderNo": 21, "DisplayName": "仪表标识", "DataType": 1, "DataLength": 100}, {"ID": 27, "Name": "ALIGN", "OrderNo": 22, "DisplayName": "对齐方式", "DataType": 1, "DataLength": 10}, {"ID": 28, "Name": "EDIT_MARK", "OrderNo": 23, "DisplayName": "可编辑标识，默认不可编辑", "DataType": 2}, {"ID": 29, "Name": "WIDTH", "OrderNo": 24, "DisplayName": "宽度", "DataType": 2}, {"ID": 30, "Name": "UP_LIMIT", "OrderNo": 25, "DisplayName": "上限", "DataType": 3}, {"ID": 32, "Name": "LOWER_LIMIT", "OrderNo": 26, "DisplayName": "下限", "DataType": 3}, {"ID": 24, "Name": "CLASS1", "OrderNo": 27, "DisplayName": "1级分类", "DataType": 1, "DataLength": 100}, {"ID": 25, "Name": "CLASS2", "OrderNo": 28, "DisplayName": "2级分类", "DataType": 1, "DataLength": 100}, {"ID": 26, "Name": "CLASS3", "OrderNo": 29, "DisplayName": "3级分类", "DataType": 1, "DataLength": 100}, {"ID": 27, "Name": "CLASS5", "OrderNo": 30, "DisplayName": "4级分类", "DataType": 1, "DataLength": 100}, {"ID": 28, "Name": "CLASS6", "OrderNo": 31, "DisplayName": "5级分类", "DataType": 1, "DataLength": 100}, {"ID": 29, "Name": "CLASS4", "OrderNo": 32, "DisplayName": "6级分类", "DataType": 1, "DataLength": 100}, {"ID": 30, "Name": "CLASS7", "OrderNo": 33, "DisplayName": "7级分类", "DataType": 1, "DataLength": 100}, {"ID": 31, "Name": "CLASS8", "OrderNo": 34, "DisplayName": "8级分类", "DataType": 1, "DataLength": 100}, {"ID": 32, "Name": "CLASS9", "OrderNo": 35, "DisplayName": "9级分类", "DataType": 1, "DataLength": 100}, {"ID": 33, "Name": "CLASS10", "OrderNo": 36, "DisplayName": "10级分类", "DataType": 1, "DataLength": 100}, {"ID": 38, "Name": "PROCESSUNITNAME", "OrderNo": 37, "DisplayName": "装置名称", "DataType": 1, "DataLength": 200}, {"ID": 39, "Name": "PRODUCTNAME", "OrderNo": 38, "DisplayName": "产品名称", "DataType": 1, "DataLength": 200}, {"ID": 40, "Name": "SAMPLINGPOINT", "OrderNo": 39, "DisplayName": "采样点", "DataType": 1, "DataLength": 200}, {"ID": 41, "Name": "ANALYSISNAME", "OrderNo": 40, "DisplayName": "分析名称", "DataType": 1, "DataLength": 200}, {"ID": 42, "Name": "ANALYSISSUBNAME", "OrderNo": 41, "DisplayName": "分析分项名称", "DataType": 1, "DataLength": 200}, {"ID": 34, "Name": "TMSORT", "OrderNo": 42, "DataType": 2}, {"ID": 35, "Name": "TMUSED", "OrderNo": 43, "DataType": 2}]}}, {"ID": 16, "Name": "TDS_ACCOUNT_COUNT_CONF", "Caption": "台账汇总设置表", "CreateDate": "2023/12/16 10:39:31", "OrderNo": 15, "GraphDesc": "Left=50.20\r\nTop=2216.40\r\nBLeft=25.10\r\nBTop=1108.20", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 14, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "COUNT_NAME", "OrderNo": 3, "DisplayName": "汇总名称", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "COUNT_NAME_COL", "OrderNo": 4, "DisplayName": "名称显示列，默认时间列", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "COUNT_FORMULA", "OrderNo": 5, "DisplayName": "汇总公式", "DataType": 1, "DataLength": 4000}, {"ID": 8, "Name": "COUNT_RESULT_FORMAT", "OrderNo": 6, "DisplayName": "结果格式", "DataType": 1, "DataLength": 200}, {"ID": 22, "Name": "TMSORT", "OrderNo": 7, "DataType": 2}, {"ID": 23, "Name": "TMUSED", "OrderNo": 8, "DataType": 2}]}}, {"ID": 17, "Name": "TDS_ACCOUNT_COUNT_COL", "Caption": "台账汇总应用列设置表", "CreateDate": "2023/12/16 11:32:13", "OrderNo": 16, "GraphDesc": "Left=438.80\r\nTop=2225.20\r\nBLeft=219.40\r\nBTop=1112.60", "MetaFields": {"Count": 10, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "COUNT_ID", "OrderNo": 3, "DisplayName": "汇总标识", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "COUNT_COL_TYPE", "OrderNo": 4, "DisplayName": "汇总列类型1动态仪表2自定义列", "DataType": 2}, {"ID": 15, "Name": "CUSTOM_COLID", "OrderNo": 5, "DisplayName": "自定义列", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "UNIT_CODE", "OrderNo": 6, "DisplayName": "核算对象", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "TAG_ID", "OrderNo": 7, "DisplayName": "核算对象仪表ID", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "COL_MARK", "OrderNo": 8, "DisplayName": "统计列ID", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "TMSORT", "OrderNo": 9, "DataType": 2}, {"ID": 17, "Name": "TMUSED", "OrderNo": 10, "DataType": 2}]}}, {"ID": 17, "Name": "TDS_ACCOUNT_MARKINFO", "Caption": "台账数据源备注信息表", "CreateDate": "2023/12/25 10:11:34", "OrderNo": 17, "GraphDesc": "Left=48.00\r\nTop=2442.20\r\nBLeft=24.00\r\nBTop=1221.10", "MetaFields": {"Count": 15, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 6, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "RQ", "OrderNo": 3, "DisplayName": "日期", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "UNITCODE", "OrderNo": 4, "DisplayName": "核算对象代码，可能有多个", "DataType": 1, "DataLength": 1000}, {"ID": 11, "Name": "CLSNO", "OrderNo": 5, "DisplayName": "班次", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "SBSJSTR", "OrderNo": 6, "DisplayName": "上班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 18, "Name": "XBSJSTR", "OrderNo": 7, "DisplayName": "下班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 19, "Name": "FORMID", "OrderNo": 8, "DisplayName": "自定义表单标识", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "TIMEPOINT", "OrderNo": 9, "DisplayName": "时间点", "DataType": 1, "DataLength": 50}, {"ID": 22, "Name": "COLALIAS", "OrderNo": 10, "DisplayName": "列标识", "DataType": 1, "DataLength": 50}, {"ID": 23, "Name": "MARK_KEY", "OrderNo": 11, "DisplayName": "单元格标识，时间点和列标识", "DataType": 1, "DataLength": 200}, {"ID": 24, "Name": "MARK_INFO", "OrderNo": 12, "DisplayName": "备注信息", "DataType": 1, "DataLength": 4000}, {"ID": 25, "Name": "VALSTR", "OrderNo": 13, "DisplayName": "备注对应数值", "DataType": 1, "DataLength": 1000}, {"ID": 21, "Name": "TMSORT", "OrderNo": 14, "DataType": 2}, {"ID": 22, "Name": "TMUSED", "OrderNo": 15, "DataType": 2}]}}, {"ID": 18, "Name": "ACCOUNT_ABNORMAL_INFO", "Caption": "台账异常信息表", "CreateDate": "2024/1/5 9:56:29", "OrderNo": 18, "GraphDesc": "Left=51.20\r\nTop=2796.60\r\nBLeft=25.60\r\nBTop=1398.30", "MetaFields": {"Count": 13, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 13, "Name": "RQ", "OrderNo": 2, "DisplayName": "日期", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "UNIT_CODE", "OrderNo": 3, "DisplayName": "核算对象代码", "DataType": 1, "DataLength": 100}, {"ID": 26, "Name": "UNIT_NAME", "OrderNo": 4, "DisplayName": "核算对象名称", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "SHIFT_CODE", "OrderNo": 5, "DisplayName": "班次代码", "DataType": 1, "DataLength": 100}, {"ID": 27, "Name": "SHIFT_NAME", "OrderNo": 6, "DisplayName": "班次名称", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "SBSJSTR", "OrderNo": 7, "DisplayName": "上班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 17, "Name": "XBSJSTR", "OrderNo": 8, "DisplayName": "下班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 28, "Name": "ORG_CODE", "OrderNo": 9, "DisplayName": "机构代码", "DataType": 1}, {"ID": 29, "Name": "ORG_NAME", "OrderNo": 10, "DisplayName": "机构名称", "DataType": 1}, {"ID": 13, "Name": "TOTAL_NUM", "OrderNo": 11, "DisplayName": "总数（暂无用）", "DataType": 2}, {"ID": 24, "Name": "TMSORT", "OrderNo": 12, "DataType": 2}, {"ID": 25, "Name": "TMUSED", "OrderNo": 13, "DataType": 2}]}}, {"ID": 19, "Name": "ACCOUNT_ABNORMAL_FORM", "Caption": "台账异常表单信息表", "CreateDate": "2024/1/5 10:06:58", "OrderNo": 19, "GraphDesc": "Left=482.00\r\nTop=2741.00\r\nBLeft=241.00\r\nBTop=1370.50", "MetaFields": {"Count": 22, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "FID", "OrderNo": 2, "DisplayName": "父标识", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "RQ", "OrderNo": 3, "DisplayName": "日期", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "UNIT_CODE", "OrderNo": 4, "DisplayName": "核算对象代码", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "UNIT_NAME", "OrderNo": 5, "DisplayName": "核算对象名称", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "SHIFT_CODE", "OrderNo": 6, "DisplayName": "班次代码", "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "SHIFT_NAME", "OrderNo": 7, "DisplayName": "班次名称", "DataType": 1, "DataLength": 100}, {"ID": 19, "Name": "SBSJSTR", "OrderNo": 8, "DisplayName": "上班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 20, "Name": "XBSJSTR", "OrderNo": 9, "DisplayName": "下班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 21, "Name": "ORG_CODE", "OrderNo": 10, "DisplayName": "机构代码", "DataType": 1, "DataLength": 50}, {"ID": 22, "Name": "ORG_NAME", "OrderNo": 11, "DisplayName": "机构名称", "DataType": 1, "DataLength": 100}, {"ID": 13, "Name": "FORM_TPL_CODE", "OrderNo": 12, "DisplayName": "表单模板代码", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "CUSTOM_CODE", "OrderNo": 13, "DisplayName": "自定义表单代码", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "CUSTOM_NAME", "OrderNo": 14, "DisplayName": "自定义表单名称", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "FORM_NAME", "OrderNo": 15, "DisplayName": "显示表单名称", "DataType": 1, "DataLength": 100}, {"ID": 23, "Name": "FORM_CODE", "OrderNo": 16, "DisplayName": "显示表单标识", "DataType": 1, "DataLength": 50}, {"ID": 20, "Name": "FORM_DATA_ID", "OrderNo": 17, "DisplayName": "表单数据ID", "DataType": 1, "DataLength": 50}, {"ID": 24, "Name": "LAST_COUNT_TIME", "OrderNo": 18, "DisplayName": "最后统计时间", "DataType": 4}, {"ID": 25, "Name": "END_TIME", "OrderNo": 19, "DisplayName": "统计截止时间（统计上个班次时，为本班次下班时间）", "DataType": 4}, {"ID": 21, "Name": "TOTAL_NUM", "OrderNo": 20, "DisplayName": "总数（暂无用）", "DataType": 2}, {"ID": 22, "Name": "TMSORT", "OrderNo": 21, "DataType": 2}, {"ID": 23, "Name": "TMUSED", "OrderNo": 22, "DataType": 2}]}}, {"ID": 20, "Name": "ACCOUNT_ABNORMAL_DATA", "Caption": "台账异常信息数据表", "CreateDate": "2024/1/5 10:14:54", "OrderNo": 20, "GraphDesc": "Left=28.00\r\nTop=3152.20\r\nBLeft=14.00\r\nBTop=1576.10", "MetaFields": {"Count": 27, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "INFO_ID", "OrderNo": 2, "DisplayName": "主数据标识", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "AB_FORM_ID", "OrderNo": 3, "DisplayName": "异常信息表单表标识ACCOUNT_ABNORMAL_FORM", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "ACCOUNT_NAME", "OrderNo": 4, "DisplayName": "台账名称", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "UNIT_CODE", "OrderNo": 5, "DisplayName": "核算对象代码", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "UNIT_NAME", "OrderNo": 6, "DisplayName": "核算对象名称", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "SHIFT_CODE", "OrderNo": 7, "DisplayName": "班次代码", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "SHIFT_NAME", "OrderNo": 8, "DisplayName": "班次名称", "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "SBSJSTR", "OrderNo": 9, "DisplayName": "上班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 19, "Name": "XBSJSTR", "OrderNo": 10, "DisplayName": "下班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 20, "Name": "ORG_CODE", "OrderNo": 11, "DisplayName": "机构代码", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "ORG_NAME", "OrderNo": 12, "DisplayName": "机构名称", "DataType": 1, "DataLength": 100}, {"ID": 22, "Name": "FORM_TPL_CODE", "OrderNo": 13, "DisplayName": "表单模板代码", "DataType": 1, "DataLength": 100}, {"ID": 23, "Name": "CUSTOM_CODE", "OrderNo": 14, "DisplayName": "自定义表单代码", "DataType": 1, "DataLength": 100}, {"ID": 24, "Name": "CUSTOM_NAME", "OrderNo": 15, "DisplayName": "自定义表单名称", "DataType": 1, "DataLength": 100}, {"ID": 26, "Name": "FORM_DATA_ID", "OrderNo": 16, "DisplayName": "表单数据ID", "DataType": 1, "DataLength": 50}, {"ID": 25, "Name": "FORM_NAME", "OrderNo": 17, "DisplayName": "显示表单名称", "DataType": 1, "DataLength": 100}, {"ID": 27, "Name": "FORM_CODE", "OrderNo": 18, "DisplayName": "显示表单标识", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "AB_ITEM_NAME", "OrderNo": 19, "DisplayName": "异常项目名称", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "AB_ITEM_ID", "OrderNo": 20, "DisplayName": "异常项目标识", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "AB_PROBLEM", "OrderNo": 21, "DisplayName": "异常问题", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "AB_PROBLEM_MARK", "OrderNo": 22, "DisplayName": "异常问题标识（暂无用）", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "AB_POINT", "OrderNo": 23, "DisplayName": "异常点", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "TAG_ID", "OrderNo": 24, "DisplayName": "仪表ID（采集点ID）", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "TAG_NAME", "OrderNo": 25, "DisplayName": "仪表名称", "DataType": 1, "DataLength": 100}, {"ID": 22, "Name": "TMSORT", "OrderNo": 26, "DataType": 2}, {"ID": 23, "Name": "TMUSED", "OrderNo": 27, "DataType": 2}]}}, {"ID": 21, "Name": "ACCOUNT_ABNORMAL_DATA_ITEM", "Caption": "台账异常信息数据子表", "CreateDate": "2024/1/5 10:30:06", "OrderNo": 21, "GraphDesc": "Left=683.60\r\nTop=3155.00\r\nBLeft=341.80\r\nBTop=1577.50", "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "INFO_ID", "OrderNo": 2, "DisplayName": "主数据标识", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "AB_FORM_ID", "OrderNo": 3, "DisplayName": "异常信息表单表标识ACCOUNT_ABNORMAL_FORM", "DataType": 1, "DataLength": 100}, {"ID": 24, "Name": "DATA_ID", "OrderNo": 4, "DisplayName": "异常信息表ID", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "AB_TIME", "OrderNo": 5, "DisplayName": "异常时间", "DataType": 4}, {"ID": 25, "Name": "AB_TIME_STR", "OrderNo": 6, "DisplayName": "异常时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 15, "Name": "AB_VAL", "OrderNo": 7, "DisplayName": "异常值", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "AB_UPLIMIT", "OrderNo": 8, "DisplayName": "异常上限", "DataType": 3}, {"ID": 17, "Name": "AB_LOWLIMIT", "OrderNo": 9, "DisplayName": "异常下限", "DataType": 3}, {"ID": 16, "Name": "AB_LIMITSTR", "OrderNo": 10, "DisplayName": "范围字符串", "DataType": 1, "DataLength": 100}, {"ID": 19, "Name": "AB_POINT", "OrderNo": 11, "DisplayName": "异常点", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "AB_MARKINFO", "OrderNo": 12, "DisplayName": "原因分析", "DataType": 1, "DataLength": 2000}, {"ID": 20, "Name": "TAG_ID", "OrderNo": 13, "DisplayName": "仪表ID（采集点ID）", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "TAG_NAME", "OrderNo": 14, "DisplayName": "仪表名称", "DataType": 1, "DataLength": 100}, {"ID": 22, "Name": "TMSORT", "OrderNo": 15, "DataType": 2}, {"ID": 23, "Name": "TMUSED", "OrderNo": 16, "DataType": 2}]}}, {"ID": 23, "Name": "ACCOUNT_ABNORMAL_CONF", "Caption": "台账异常统计用参数配置表", "CreateDate": "2024/1/5 16:30:49", "OrderNo": 22, "GraphDesc": "Left=703.40\r\nTop=3476.60\r\nBLeft=351.70\r\nBTop=1738.30", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 6, "Name": "CONF_TYPE", "OrderNo": 2, "DisplayName": "配置类型1应用表单2应用台账数据源", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "FORM_NAME", "OrderNo": 3, "DisplayName": "显示表单名称", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "FORM_CODE", "OrderNo": 4, "DisplayName": "显示表单标识", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "TDS_ALIAS", "OrderNo": 5, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 100}, {"ID": 22, "Name": "TMSORT", "OrderNo": 6, "DataType": 2}, {"ID": 23, "Name": "TMUSED", "OrderNo": 7, "DataType": 2}]}}, {"ID": 24, "Name": "ACCOUNT_AUTO_SAVE_TODO", "Caption": "台账自动取数待办表", "CreateDate": "2024/4/22 16:24:49", "OrderNo": 23, "GraphDesc": "Left=34.00\r\nTop=3648.80\r\nBLeft=17.00\r\nBTop=1824.40", "MetaFields": {"Count": 14, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "RQ", "OrderNo": 2, "DisplayName": "日期", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "UNIT_CODE", "OrderNo": 3, "DisplayName": "核算单元代码", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "SHIFT_CODE", "OrderNo": 4, "DisplayName": "班次代码", "DataType": 1, "DataLength": 50}, {"ID": 8, "Name": "CUSTOM_CODE", "OrderNo": 5, "DisplayName": "自定义表单代码", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "TDS_ALIAS", "OrderNo": 6, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "TODO_MARK", "OrderNo": 7, "DisplayName": "待办标识1有0无", "DataType": 2}, {"ID": 15, "Name": "FORM_CODE", "OrderNo": 8, "DisplayName": "表单代码，备用", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "TODO_NUM", "OrderNo": 9, "DisplayName": "待办数，备用", "DataType": 2}, {"ID": 10, "Name": "SBSJ", "OrderNo": 10, "DisplayName": "上班时间，备用", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "XBSJ", "OrderNo": 11, "DisplayName": "下班时间，备用", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "LAST_COUNT_TIME", "OrderNo": 12, "DisplayName": "最后统计时间", "DataType": 4}, {"ID": 16, "Name": "TMSORT", "OrderNo": 13, "DataType": 2}, {"ID": 17, "Name": "TMUSED", "OrderNo": 14, "DataType": 2}]}}, {"ID": 24, "Name": "TDS_ACCOUNT_FORM_SF_MANAGE", "Caption": "台账表单默认台账管理范围表", "Memo": "目前", "CreateDate": "2024/7/10 8:29:35", "OrderNo": 24, "GraphDesc": "Left=1386.00\r\nTop=1922.60\r\nBLeft=693.00\r\nBTop=961.30", "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "MODE_TYPE", "OrderNo": 2, "DisplayName": "类型1人2岗3机构4工位，目前只有工位", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "ACCOUNTID", "OrderNo": 3, "DisplayName": "台账id", "DataType": 1, "DataLength": 50}, {"ID": 24, "Name": "FORM_CODE", "OrderNo": 4, "DisplayName": "表单id", "DataType": 1, "DataLength": 50}, {"ID": 25, "Name": "UNIT_CODE", "OrderNo": 5, "DisplayName": "核算对象id", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "ORG_MARK", "OrderNo": 6, "DisplayName": "机构代码过滤判断", "DataType": 1}, {"ID": 14, "Name": "MANAGE_CODE", "OrderNo": 7, "DisplayName": "管理编码", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "MANAGE_NAME", "OrderNo": 8, "DisplayName": "管理名", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "ORG_CODE", "OrderNo": 9, "DisplayName": "机构代码，用于机构岗位记录", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "POST_CODE", "OrderNo": 10, "DisplayName": "岗位代码，用于机构岗位记录", "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "ADD_MARK", "OrderNo": 11, "DisplayName": "录入标识", "DataType": 2}, {"ID": 19, "Name": "MANAGE_MARK", "OrderNo": 12, "DisplayName": "补录标识", "DataType": 2}, {"ID": 20, "Name": "SEARCH_MARK", "OrderNo": 13, "DisplayName": "查询标识", "DataType": 2}, {"ID": 21, "Name": "MANAGE_BOUND", "OrderNo": 14, "DisplayName": "管理范围1本机构2本车间", "DataType": 1, "DataLength": 20}, {"ID": 22, "Name": "TMSORT", "OrderNo": 15, "DataType": 2}, {"ID": 23, "Name": "TMUSED", "OrderNo": 16, "DataType": 2}]}}, {"ID": 25, "Name": "TDS_ACCOUNT_FORM_ST", "Caption": "台账自定义表单启停数据", "CreateDate": "2024/7/22 15:44:19", "OrderNo": 25, "GraphDesc": "Left=521.20\r\nTop=2458.00\r\nBLeft=260.60\r\nBTop=1229.00", "MetaFields": {"Count": 13, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "ACCOUNTID", "OrderNo": 2, "DisplayName": "自定义台账ID", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "STOP_TIME", "OrderNo": 3, "DisplayName": "停止时间", "DataType": 4}, {"ID": 14, "Name": "START_TIME", "OrderNo": 4, "DisplayName": "启动时间", "DataType": 4}, {"ID": 15, "Name": "STOP_TIME_STR", "OrderNo": 5, "DisplayName": "停止时间字符串", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "START_TIME_STR", "OrderNo": 6, "DisplayName": "启动时间字符串", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "STOP_USER_ID", "OrderNo": 7, "DisplayName": "停止人员id", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "STOP_USER_NAME", "OrderNo": 8, "DisplayName": "停止人员名称", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "STOP_OP_TIME", "OrderNo": 9, "DisplayName": "停止操作时间", "DataType": 4}, {"ID": 22, "Name": "START_USER_ID", "OrderNo": 10, "DisplayName": "启动人员id", "DataType": 1, "DataLength": 50}, {"ID": 23, "Name": "START_USER_NAME", "OrderNo": 11, "DisplayName": "启动人员名称", "DataType": 1, "DataLength": 50}, {"ID": 24, "Name": "START_OP_TIME", "OrderNo": 12, "DisplayName": "启动操作时间", "DataType": 4}, {"ID": 20, "Name": "TMUSED", "OrderNo": 13, "DataType": 2}]}}, {"ID": 26, "Name": "TDS_ACCOUNT_LOG", "Caption": "台账修改日志表", "CreateDate": "2025/3/19 15:55:21", "OrderNo": 26, "GraphDesc": "Left=386.80\r\nTop=3653.80", "MetaFields": {"Count": 17, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "UNIT_CODE", "OrderNo": 3, "DisplayName": "核算对象代码", "DataType": 1, "DataLength": 100}, {"ID": 4, "Name": "UNIT_NAME", "OrderNo": 4, "DisplayName": "核算对象名称", "DataType": 1, "DataLength": 100}, {"ID": 5, "Name": "SHIFT_CODE", "OrderNo": 5, "DisplayName": "班次代码", "DataType": 1, "DataLength": 100}, {"ID": 6, "Name": "SHIFT_NAME", "OrderNo": 6, "DisplayName": "班次名称", "DataType": 1, "DataLength": 100}, {"ID": 7, "Name": "SBSJSTR", "OrderNo": 7, "DisplayName": "上班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 8, "Name": "XBSJSTR", "OrderNo": 8, "DisplayName": "下班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 9, "Name": "ORG_CODE", "OrderNo": 9, "DisplayName": "机构代码", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "ORG_NAME", "OrderNo": 10, "DisplayName": "机构名称", "DataType": 1, "DataLength": 100}, {"ID": 11, "Name": "TAG_ID", "OrderNo": 11, "DisplayName": "仪表ID（采集点ID）", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "TAG_NAME", "OrderNo": 12, "DisplayName": "仪表名称", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "SJSTR", "OrderNo": 13, "DisplayName": "时间字符串", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "MEMO", "OrderNo": 14, "DisplayName": "修改描述", "DataType": 1, "DataLength": 2000}, {"ID": 16, "Name": "UPD_USER_NAME", "OrderNo": 15, "DisplayName": "修改人", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "TMSORT", "OrderNo": 16, "DataType": 2}, {"ID": 15, "Name": "TMUSED", "OrderNo": 17, "DataType": 2}]}}, {"ID": 27, "Name": "TDS_ACCOUNT_OVERINFO", "Caption": "台账录入超限信息表", "CreateDate": "2025/3/19 16:03:13", "OrderNo": 27, "GraphDesc": "Left=748.00\r\nTop=3660.00", "MetaFields": {"Count": 19, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "TDSALIAS", "OrderNo": 2, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "UNIT_CODE", "OrderNo": 3, "DisplayName": "核算对象代码", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "UNIT_NAME", "OrderNo": 4, "DisplayName": "核算对象名称", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "SHIFT_CODE", "OrderNo": 5, "DisplayName": "班次代码", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "SHIFT_NAME", "OrderNo": 6, "DisplayName": "班次名称", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "SBSJSTR", "OrderNo": 7, "DisplayName": "上班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 18, "Name": "XBSJSTR", "OrderNo": 8, "DisplayName": "下班时间字符串", "DataType": 1, "DataLength": 30}, {"ID": 19, "Name": "ORG_CODE", "OrderNo": 9, "DisplayName": "机构代码", "DataType": 1, "DataLength": 50}, {"ID": 20, "Name": "ORG_NAME", "OrderNo": 10, "DisplayName": "机构名称", "DataType": 1, "DataLength": 100}, {"ID": 21, "Name": "TAG_ID", "OrderNo": 11, "DisplayName": "仪表ID（采集点ID）", "DataType": 1, "DataLength": 50}, {"ID": 22, "Name": "TAG_NAME", "OrderNo": 12, "DisplayName": "仪表名称", "DataType": 1, "DataLength": 100}, {"ID": 23, "Name": "SJSTR", "OrderNo": 13, "DisplayName": "时间字符串", "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "VAL", "OrderNo": 14, "DisplayName": "值", "DataType": 1, "DataLength": 50}, {"ID": 27, "Name": "UP_LIMIT", "OrderNo": 15, "DisplayName": "上限", "DataType": 3}, {"ID": 28, "Name": "LOW_LIMIT", "OrderNo": 16, "DisplayName": "下限", "DataType": 3}, {"ID": 24, "Name": "UPD_USER_NAME", "OrderNo": 17, "DisplayName": "修改人", "DataType": 1, "DataLength": 50}, {"ID": 25, "Name": "TMSORT", "OrderNo": 18, "DataType": 2}, {"ID": 26, "Name": "TMUSED", "OrderNo": 19, "DataType": 2}]}}]}}]}