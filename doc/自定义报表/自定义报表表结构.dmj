{"RootName": "DataModels", "CTVER": "43543334", "TableCount": 3, "Count": 1, "items": [{"ID": 1, "Name": "报表模块", "CreateDate": "2022/10/15 14:06:50", "OrderNo": 1, "DefDbEngine": "SQLSERVER", "DbConnectStr": "TCtMetaSqlsvrDb", "ConfigStr": "DrawerWidth=1500\r\nDrawerHeight=2560\r\nWorkAreaColor=16777215\r\nSelectedColor=16711680\r\nDefaultObjectColor=15921906\r\nDefaultTitleColor=255\r\nDefaultPKColor=16711935\r\nDefaultFKColor=16711680\r\nDefaultBorderColor=12632256\r\nDefaultLineColor=16711680\r\nShowFieldType=1\r\nShowFieldIcon=1\r\nShowPhyFieldName=2\r\nDatabaseEngine=\r\nGenFKIndexesSQL=0\r\nIndependPosForOverviewMode=0\r\n", "Tables": {"Count": 3, "items": [{"ID": 5, "Name": "REPORT_MANAGE", "Caption": "报表管理", "CreateDate": "2022/10/15 14:47:23", "OrderNo": 1, "GraphDesc": "Left=54.80\r\nTop=78.46", "MetaFields": {"Count": 15, "items": [{"ID": 1, "Name": "ID", "Memo": "报表ID/报表分类ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 15, "Name": "TABLESIZE", "OrderNo": 2, "DisplayName": "表格尺寸", "DataType": 1, "DataLength": 50}, {"ID": 2, "Name": "PID", "Memo": "报表分类ID", "OrderNo": 3, "DisplayName": "父编号", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "NAME", "Memo": "报表名称/报表分类名称", "OrderNo": 4, "DisplayName": "名称", "DataType": 1, "DataLength": 200}, {"ID": 22, "Name": "NODE_TYPE", "Memo": "1=分类节点 2=报表节点", "OrderNo": 5, "DisplayName": "节点类型", "DataType": 2}, {"ID": 6, "Name": "REPORT_TYPE", "Memo": "1=可编辑报表 2=查询报表", "OrderNo": 6, "DisplayName": "报表类型", "DataType": 2}, {"ID": 14, "Name": "FUNCTION_TYPE", "Memo": "1=数据源 2=功能页面", "OrderNo": 7, "DisplayName": "功能类型", "DataType": 2}, {"ID": 15, "Name": "TDS_NAME", "OrderNo": 8, "DisplayName": "数据源名称", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "TDS_ALIAS", "OrderNo": 9, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 200}, {"ID": 18, "Name": "PAGE_NAME", "OrderNo": 10, "DisplayName": "页面名称", "DataType": 1, "DataLength": 200}, {"ID": 17, "Name": "PAGE_PATH", "OrderNo": 11, "DisplayName": "页面地址", "DataType": 1, "DataLength": 2000}, {"ID": 19, "Name": "CACHE_RESULT", "Memo": "1=缓存至Redis 0=不缓存（每次检索最新数据）", "OrderNo": 12, "DisplayName": "缓存生成结果", "DataType": 2}, {"ID": 14, "Name": "CACHE_TIME", "Memo": "单位：小时", "OrderNo": 13, "DisplayName": "缓存生效时长", "DataType": 3, "DataLength": 18, "DataScale": 2}, {"ID": 11, "Name": "TMSORT", "OrderNo": 14, "DisplayName": "排序号", "DataType": 2}, {"ID": 13, "Name": "TMUSED", "Memo": "medium=正常 small=较小 mini=小 super-mini=最小", "OrderNo": 15, "DisplayName": "可用标识", "DataType": 2}]}}, {"ID": 9, "Name": "REPORT_QUERY", "Caption": "报表查询条件", "CreateDate": "2022/10/15 15:31:24", "OrderNo": 2, "GraphDesc": "Left=437.51\r\nTop=44.00", "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "REPORT_ID", "OrderNo": 2, "DisplayName": "报表ID", "DataType": 1, "RelateTable": "REPORT_MANAGE", "RelateField": "{Link:OppDirect}", "IndexType": 2, "DataLength": 50, "GraphDesc": "P1=283.80,107.80\r\nP2=313.00,107.80\r\nP3=313.00,89.13\r\nP4=437.51,89.13\r\nHookP1=114.00,29.34\r\nHookP2=20.00,45.13\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "NAME", "OrderNo": 3, "DisplayName": "条件名称", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "ALIAS", "OrderNo": 4, "DisplayName": "条件别名", "DataType": 1, "DataLength": 200}, {"ID": 6, "Name": "OPERATOR", "Memo": "等于 不等于 包含 不包含 大于 大于等于 小于 小于等于 为空 不为空", "OrderNo": 5, "DisplayName": "关系运算符", "DataType": 1, "DataLength": 10}, {"ID": 14, "Name": "DEF_VAL", "Memo": "数据源公式", "OrderNo": 6, "DisplayName": "默认值", "DataType": 1, "DataLength": 200}, {"ID": 15, "Name": "COMPONENT_TYPE", "Memo": "textfield=文本框 datefield=日期选择框 monthfield=月份选择框 datetimefield=日期时间选择器 yearfield=年份选择框 combo=下拉框", "OrderNo": 7, "DisplayName": "控件类型", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "COMPONENT_WIDTH", "OrderNo": 8, "DisplayName": "控件宽度", "DataType": 2}, {"ID": 16, "Name": "OPTION_VAL", "Memo": "Json对象数组", "OrderNo": 9, "DisplayName": "备选值", "DataType": 1, "DataLength": 2000}, {"ID": 16, "Name": "BIND_TYPE", "Memo": "1=数据源入参（透传） 2=数据源出参（过滤）", "OrderNo": 10, "DisplayName": "绑定类型", "DataType": 2}, {"ID": 17, "Name": "BIND_PARAM", "OrderNo": 11, "DisplayName": "绑定参数", "DataType": 1, "DataLength": 200}, {"ID": 18, "Name": "BIND_PARAM_ALIAS", "OrderNo": 12, "DisplayName": "绑定参数别名", "DataType": 1, "DataLength": 200}, {"ID": 19, "Name": "SHOWED", "Memo": "1=显示 0=不显示", "OrderNo": 13, "DisplayName": "是否显示参数", "DataType": 2}, {"ID": 13, "Name": "DATATYPE", "Memo": "tdsString=字符型 tdsDouble=数值型", "OrderNo": 14, "DisplayName": "数据类型", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "TMSORT", "OrderNo": 15, "DisplayName": "排序号", "DataType": 2}, {"ID": 13, "Name": "TMUSED", "Memo": "1=有效 0=无效", "OrderNo": 16, "DisplayName": "可用标识", "DataType": 2}]}}, {"ID": 10, "Name": "REPORT_REGION", "Caption": "报表区域设置", "CreateDate": "2022/10/15 20:00:19", "OrderNo": 3, "GraphDesc": "Left=438.48\r\nTop=400.89", "MetaFields": {"Count": 18, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "REPORT_ID", "OrderNo": 2, "DisplayName": "报表ID", "DataType": 1, "RelateTable": "REPORT_MANAGE", "RelateField": "{Link:OppDirect}", "IndexType": 2, "DataLength": 50, "GraphDesc": "P1=283.80,105.82\r\nP2=361.00,105.82\r\nP3=361.00,446.02\r\nP4=438.48,446.02\r\nHookP1=122.00,27.36\r\nHookP2=20.00,45.13\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 14, "Name": "REGION", "Memo": "row=行区 col=列区 measure=度量", "OrderNo": 3, "DisplayName": "分布区域", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "BIND_PARAM", "OrderNo": 4, "DisplayName": "绑定参数", "DataType": 1, "DataLength": 200}, {"ID": 18, "Name": "BIND_PARAM_ALIAS", "OrderNo": 5, "DisplayName": "绑定参数别名", "DataType": 1, "DataLength": 200}, {"ID": 10, "Name": "PARAM_TYPE", "Memo": "1=数据源输出参数 2=Σ度量组", "OrderNo": 6, "DisplayName": "参数类型", "DataType": 2}, {"ID": 12, "Name": "ORDER_BY_WAY", "Memo": "asc=升序 desc=降序 field_asc=指定字段升序 field_desc=指定字段降序", "OrderNo": 7, "DisplayName": "排序方式", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "ORDER_BY_FIELD", "OrderNo": 8, "DisplayName": "排序字段", "DataType": 1, "DataLength": 200}, {"ID": 12, "Name": "ORDER_BY_FIELD_ALIAS", "OrderNo": 9, "DisplayName": "排序字段别名", "DataType": 1, "DataLength": 200}, {"ID": 14, "Name": "ORDER_BY_DATATYPE", "Memo": "tdsString=字符型 tdsDouble=数值型", "OrderNo": 10, "DisplayName": "排序字段数据类型", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "STATISTIC_WAY", "Memo": "sum=合计值 max=最大值 min=最小值 avg=平均值 count=计数值", "OrderNo": 11, "DisplayName": "统计方式", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "DATATYPE", "Memo": "tdsString=字符型 tdsDouble=数值型", "OrderNo": 12, "DisplayName": "数据类型", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "TMSORT", "OrderNo": 13, "DisplayName": "排序号", "DataType": 2}, {"ID": 13, "Name": "TMUSED", "Memo": "1=有效 0=无效", "OrderNo": 14, "DisplayName": "可用标识", "DataType": 2}, {"ID": 15, "Name": "COL_WIDTH", "Memo": "数据源输出参数渲染到前端页面时的列宽度", "OrderNo": 15, "DisplayName": "列宽", "DataType": 2}, {"ID": 16, "Name": "COL_ALIGN", "Memo": "left=居左 right=居右 center=居中", "OrderNo": 16, "DisplayName": "列对齐方式", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "COL_SCALE", "Memo": "正整数，最小为0", "OrderNo": 17, "DisplayName": "列值保留小数位数", "DataType": 2}, {"ID": 19, "Name": "COL_FIXED", "Memo": "只针对行区列表头有效，1=锁定 0=不锁定", "OrderNo": 18, "DisplayName": "是否锁定列", "DataType": 2}]}}]}}]}